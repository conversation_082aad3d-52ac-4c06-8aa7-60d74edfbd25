import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authApi } from '@/api/auth'
import { removeToken, setToken, getToken } from '@/utils/auth'
import { ElMessage } from 'element-plus'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref(null)
  const token = ref('')
  const refreshToken = ref('')
  const permissions = ref([])
  const roles = ref([])
  
  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => roles.value.includes('admin'))
  const userName = computed(() => user.value?.full_name || user.value?.username || '')
  const userAvatar = computed(() => user.value?.avatar || '')
  
  // 方法
  const login = async (loginForm) => {
    try {
      const response = await authApi.login(loginForm)
      const data = response.data

      // 保存令牌
      token.value = data.access_token
      refreshToken.value = data.refresh_token
      setToken(data.access_token)

      // 保存用户信息
      user.value = data.user
      roles.value = data.user.roles || []
      permissions.value = data.user.permissions || []

      ElMessage.success('登录成功')
      return Promise.resolve(response)
    } catch (error) {
      console.error('登录失败:', error)
      const message = error.response?.data?.detail || error.message || '登录失败'
      ElMessage.error(message)
      return Promise.reject(error)
    }
  }
  
  const register = async (registerForm) => {
    try {
      const response = await authApi.register(registerForm)
      ElMessage.success('注册成功，请登录')
      return Promise.resolve(response)
    } catch (error) {
      ElMessage.error(error.message || '注册失败')
      return Promise.reject(error)
    }
  }
  
  const logout = async () => {
    try {
      await authApi.logout()
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除本地数据
      user.value = null
      token.value = ''
      refreshToken.value = ''
      permissions.value = []
      roles.value = []
      removeToken()
      
      ElMessage.success('已退出登录')
    }
  }
  
  const checkAuth = async () => {
    const savedToken = getToken()
    if (!savedToken) {
      return false
    }

    try {
      token.value = savedToken
      const response = await authApi.getCurrentUser()
      const userInfo = response.data

      user.value = userInfo
      roles.value = userInfo.roles || []
      permissions.value = userInfo.permissions || []

      return true
    } catch (error) {
      console.error('检查认证状态失败:', error)
      // 清除无效令牌
      removeToken()
      token.value = ''
      user.value = null
      roles.value = []
      permissions.value = []
      return false
    }
  }
  
  const refreshAccessToken = async () => {
    try {
      const response = await authApi.refreshToken({ refresh_token: refreshToken.value })
      const { access_token } = response.data
      
      token.value = access_token
      setToken(access_token)
      
      return access_token
    } catch (error) {
      console.error('刷新令牌失败:', error)
      // 刷新失败，需要重新登录
      await logout()
      throw error
    }
  }
  
  const updateProfile = async (profileData) => {
    try {
      // 这里应该调用更新用户信息的API
      // const response = await userApi.updateProfile(profileData)
      
      // 更新本地用户信息
      user.value = { ...user.value, ...profileData }
      
      ElMessage.success('个人信息更新成功')
      return Promise.resolve()
    } catch (error) {
      ElMessage.error(error.message || '更新个人信息失败')
      return Promise.reject(error)
    }
  }
  
  const hasPermission = (permission) => {
    if (isAdmin.value) return true
    return permissions.value.includes(permission)
  }
  
  const hasRole = (role) => {
    return roles.value.includes(role)
  }
  
  const hasAnyRole = (roleList) => {
    return roleList.some(role => roles.value.includes(role))
  }
  
  const hasAllRoles = (roleList) => {
    return roleList.every(role => roles.value.includes(role))
  }
  
  return {
    // 状态
    user,
    token,
    refreshToken,
    permissions,
    roles,
    
    // 计算属性
    isLoggedIn,
    isAdmin,
    userName,
    userAvatar,
    
    // 方法
    login,
    register,
    logout,
    checkAuth,
    refreshAccessToken,
    updateProfile,
    hasPermission,
    hasRole,
    hasAnyRole,
    hasAllRoles
  }
})
