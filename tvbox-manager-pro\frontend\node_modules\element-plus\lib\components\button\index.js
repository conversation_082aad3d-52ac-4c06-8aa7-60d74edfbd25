'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var button$1 = require('./src/button2.js');
var buttonGroup = require('./src/button-group2.js');
var button = require('./src/button.js');
var constants = require('./src/constants.js');
var install = require('../../utils/vue/install.js');

const ElButton = install.withInstall(button$1["default"], {
  ButtonGroup: buttonGroup["default"]
});
const ElButtonGroup = install.withNoopInstall(buttonGroup["default"]);

exports.buttonEmits = button.buttonEmits;
exports.buttonNativeTypes = button.buttonNativeTypes;
exports.buttonProps = button.buttonProps;
exports.buttonTypes = button.buttonTypes;
exports.buttonGroupContextKey = constants.buttonGroupContextKey;
exports.ElButton = ElButton;
exports.ElButtonGroup = ElButtonGroup;
exports["default"] = ElButton;
//# sourceMappingURL=index.js.map
