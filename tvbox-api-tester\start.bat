@echo off
chcp 65001 >nul
title TVBox API Web 测试工具

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    TVBox API Web 测试工具                    ║
echo ║                     独立Web测试界面                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🐍 使用Python启动Web测试工具...
echo.

echo 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Python，请先安装Python
    echo 下载地址: https://www.python.org/downloads/
    pause
    goto :end
)

echo ✅ Python环境正常
echo.

echo 🔧 检查后端服务状态...
netstat -ano | findstr :8001 >nul
if %errorlevel% equ 0 (
    echo ✅ 后端服务正在运行 (端口8001)
) else (
    echo ⚠️  后端服务未启动，请先启动TVBox Manager Pro后端
    echo.
    echo 启动命令:
    echo   cd tvbox-manager-pro\backend
    echo   python app/main.py
    echo.
    set /p continue="是否继续启动测试工具? (y/n): "
    if /i not "%continue%"=="y" goto :end
)

echo.
echo 🚀 启动Python HTTP服务器...
echo.
echo 📍 测试工具地址: http://localhost:8080
echo 📍 后端API地址: http://localhost:8001
echo.
echo 💡 使用说明:
echo   1. 在浏览器中打开 http://localhost:8080
echo   2. 配置后端API地址为 http://localhost:8001
echo   3. 使用账号密码登录: <EMAIL> / admin123
echo   4. 开始测试API接口
echo.
echo 按 Ctrl+C 停止服务器
echo.

goto :python_server

echo ❌ 无效选择，请重新运行
pause
goto :end

:python_server
echo.
echo 🐍 启动Python HTTP服务器...
echo.
echo 服务器地址: http://localhost:8080
echo 按 Ctrl+C 停止服务器
echo.
python -m http.server 8080
pause
goto :end

:node_server
echo.
echo 🚀 启动Node.js服务器...
echo.
echo 检查是否安装了serve...
where npx >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ 未找到npx，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    goto :end
)

echo 启动服务器...
npx serve . -p 8080
pause
goto :end

:open_html
echo.
echo 📁 直接打开HTML文件...
echo.
start index.html
echo ✅ 已在默认浏览器中打开测试工具
pause
goto :end

:check_backend
echo.
echo 🔧 检查后端服务状态...
echo.

echo 检查端口8001是否被占用...
netstat -ano | findstr :8001 >nul
if %errorlevel% equ 0 (
    echo ✅ 端口8001有服务运行
    echo.
    echo 尝试访问API文档...
    start http://localhost:8001/docs
) else (
    echo ❌ 端口8001没有服务运行
    echo.
    echo 请先启动TVBox Manager Pro后端服务:
    echo 1. 进入后端目录: cd tvbox-manager-pro\backend
    echo 2. 运行启动脚本: python run.py
    echo 或者: python app/main.py
)

echo.
pause
goto :end

:end
echo.
echo 👋 再见！
