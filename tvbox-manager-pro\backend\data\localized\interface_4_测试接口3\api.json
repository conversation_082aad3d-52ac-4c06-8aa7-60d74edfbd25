{"spider": "/localized/interface_4_测试接口3/spider/Yoursmile.jar", "wallpaper": "https://tuapi.eees.cc/api.php?category=fengjing&type=302", "sites": [{"key": "csp_<PERSON><PERSON><PERSON>", "name": "🔍聚玩盒子-本接口免费-🈲贩卖", "type": 3, "api": "csp_<PERSON><PERSON><PERSON>", "searchable": 0, "quickSearch": 0, "filterable": 1}, {"key": "csp_Czsapp", "name": "🏭厂长", "type": 3, "api": "csp_Czsapp", "searchable": 1, "quickSearch": 1, "filterable": 0, "ext": "https://www.czzy55.com/"}, {"key": "csp_<PERSON><PERSON><PERSON><PERSON>", "name": "🌎一起看", "type": 3, "api": "csp_<PERSON><PERSON><PERSON><PERSON>", "searchable": 1, "quickSearch": 1, "filterable": 1}, {"key": "csp_<PERSON>", "name": "🎃南瓜", "type": 3, "api": "csp_<PERSON>", "searchable": 1, "quickSearch": 1, "filterable": 1, "changeable": 1}, {"key": "酷看", "name": "💡酷看", "type": 3, "api": "csp_<PERSON><PERSON>s", "searchable": 1, "quickSearch": 1, "filterable": 1, "changeable": 1}, {"key": "率率", "name": "🌸率率", "type": 3, "api": "csp_AppLd", "playerType": 1, "searchable": 1, "quickSearch": 1, "filterable": 1, "changeable": 1, "ext": "FbjSZ1FapovNr0QF4QvEpcoxb5gWXdQJE2MXqLG6ykBcSsOR7TnDygTd5bXnCjGuLD/PknUhkn0yQ2NzCIxgZAkPAofMgEqVYlhBvnhU8N1VerP8K2NTNgP+NxESztO6"}, {"key": "萌米", "name": "👀萌米", "type": 3, "api": "csp_AppMao", "searchable": 1, "quickSearch": 1, "filterable": 1, "changeable": 0, "ext": "FbjDcUxPqpfNr0QF4QvEpcoxMMdAA5wQTClJt+Hxgw4VXJLfrTSRjQjA6r70U2ONX3iG4lQWk1cgXQMSUZpbUzIYJ6iQs3+gcUVDp1lz6rcrGqDtCW4hfAf9JSR025m0+TGWMsZkVg=="}, {"key": "星奇", "name": "💥星奇", "type": 3, "api": "csp_AppMao", "playerType": 2, "searchable": 1, "quickSearch": 1, "filterable": 1, "changeable": 1, "ext": "FbjDcUxPqpfNr0QF4QvE6sExbd4UXJxJXzdL462ywU1XScGa5G6Hj0/c+Ou1GW6rdX6N2XIhnD46QzIsRoZ8bk4fG4OYi0iCaWwRj2ddkacwFqHtLjQhalHqIy0+kpiTv2eOfJYxTshgrxcJ+g3lEHx7ZLC9kB1TCfZUSHwqHB3tt6V/1OhRENIOZNRFfXVBEFd7jQg+J06kjCAF7z1Bt8hRvMjC2VcbQXsEz8MCZDgu06C9/wirWcTa/wWJsfT+Z7fXaDs+dIWMYiwf6td5CWTx6LfL6eTuiMhs2KU5Byw9F+4Y/AHBg4r+LEe+Hm4EM2d8X3ca5zHSj+kjmpXOiGDo4TalFqgTq67eggmthCbV2d131SKc03V+5gruudQQk/Tb9e4lg6SQhA9/5kWOOM/LRmkqIxSt6d0wpu7yKKh65enwKbgasc93/HnGr6W4LuZjf6eYnS2GgRzWRE0fT5lYNmxnWeRnYuLhoZbDqslAfJzg"}, {"key": "csp_<PERSON><PERSON>", "name": "🧲荐片", "type": 3, "api": "csp_<PERSON><PERSON>", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "http://api2.rinhome.com"}, {"key": "csp_DiDuan", "name": "📺低端", "type": 3, "api": "csp_Ddrk", "searchable": 1, "quickSearch": 1, "filterable": 0}, {"key": "csp_Auete", "name": "🐼奥特", "type": 3, "api": "csp_Auete", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "https://auete.pro/"}, {"key": "csp_MLYS", "name": "🐴毛驴", "type": 3, "api": "csp_MLYS", "searchable": 1, "quickSearch": 1, "filterable": 0, "changeable": 1, "ext": "https://www.maolvys.com/"}, {"key": "csp_<PERSON><PERSON>j", "name": "✨新视觉", "type": 3, "api": "csp_<PERSON><PERSON>j", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "https://www.hdmyy.com/"}, {"key": "csp_<PERSON><PERSON>woo", "name": "✌比特", "type": 3, "api": "csp_<PERSON><PERSON>woo", "searchable": 1, "quickSearch": 1, "filterable": 1}, {"key": "csp_Lib", "name": "🔷LibVio", "type": 3, "api": "csp_<PERSON><PERSON><PERSON>", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "https://www.libvio.pw/"}, {"key": "csp_<PERSON>", "name": "👀快看", "type": 3, "api": "csp_<PERSON>", "searchable": 1, "quickSearch": 1, "filterable": 1}, {"key": "csp_Kunyu77", "name": "👒酷云七七", "type": 3, "api": "csp_Kunyu77", "searchable": 1, "quickSearch": 1, "filterable": 1}, {"key": "360", "name": "💘360", "type": 3, "api": "csp_SP33", "searchable": 1, "quickSearch": 1, "filterable": 1, "changeable": 1}, {"key": "csp_SixV", "name": "🧲新6V", "type": 3, "api": "csp_SixV", "searchable": 1, "quickSearch": 1, "filterable": 0, "ext": "http://www.xb6v.com/"}, {"key": "csp_Anime1", "name": "🌸动漫", "type": 3, "api": "csp_Anime1", "searchable": 1, "quickSearch": 1, "filterable": 1}, {"key": "csp_Dm84", "name": "🚌动漫巴士", "type": 3, "api": "csp_Dm84", "searchable": 1, "quickSearch": 1, "filterable": 1}, {"key": "<PERSON>", "name": "🌸樱花动漫", "type": 3, "api": "csp_<PERSON>", "searchable": 1, "quickSearch": 1, "filterable": 1}, {"key": "csp_Ysj", "name": "📮异世界", "type": 3, "api": "csp_Ysj", "searchable": 1, "quickSearch": 1, "filterable": 1}, {"key": "csp_<PERSON>", "name": "🎭短剧", "type": 3, "api": "csp_<PERSON>", "searchable": 1, "quickSearch": 1, "filterable": 1, "changeable": 1, "ext": "http://aiduanju.vip/"}, {"key": "csp_Yj1211", "name": "📽️在线直播", "type": 3, "api": "csp_Yj1211", "searchable": 1, "quickSearch": 1, "filterable": 1}, {"key": "csp_<PERSON><PERSON><PERSON><PERSON>", "name": "⚽88看球", "type": 3, "api": "csp_<PERSON><PERSON><PERSON><PERSON>", "searchable": 1, "quickSearch": 1, "filterable": 1, "changeable": 0}, {"key": "Aid", "name": "🚑急救教学", "type": 3, "api": "csp_FirstAid", "searchable": 0, "quickSearch": 0, "filterable": 0, "changeable": 0}, {"key": "csp_WoGG", "name": "👽玩偶哥哥", "type": 3, "api": "csp_WoGG", "searchable": 1, "quickSearch": 1, "filterable": 1, "changeable": 0, "ext": "http://127.0.0.1:9978/file/tvfan/token.txt+4k|auto|fhd$$$https://www.wogg.xyz/$$$弹"}, {"key": "YiSo", "name": "🆎易搜", "type": 3, "api": "csp_<PERSON>o", "searchable": 1, "quickSearch": 1, "filterable": 0, "changeable": 0, "ext": "http://127.0.0.1:9978/file/tvfan/token.txt+4k|auto|fhd"}, {"key": "PanSou", "name": "📀盘搜", "type": 3, "api": "csp_Pan<PERSON>ou", "searchable": 1, "quickSearch": 1, "filterable": 0, "changeable": 0, "ext": "http://127.0.0.1:9978/file/tvfan/token.txt+4k|auto|fhd"}, {"key": "UpYun", "name": "☁️UP云搜", "type": 3, "api": "csp_UpYun", "searchable": 1, "quickSearch": 1, "filterable": 0, "changeable": 0, "ext": "http://127.0.0.1:9978/file/tvfan/token.txt+4k|auto|fhd"}, {"key": "PanSearch", "name": "🙀盘搜索", "type": 3, "api": "csp_PanSearch", "searchable": 1, "quickSearch": 1, "filterable": 0, "changeable": 0, "ext": "http://127.0.0.1:9978/file/tvfan/token.txt+4k|auto|fhd"}, {"key": "Dovx", "name": "😾七夜", "type": 3, "api": "csp_Dovx", "searchable": 1, "quickSearch": 1, "filterable": 0, "changeable": 0, "ext": "http://127.0.0.1:9978/file/tvfan/token.txt+4k|auto|fhd"}, {"key": "<PERSON><PERSON>", "name": "🦊找资源", "type": 3, "api": "csp_<PERSON><PERSON>", "searchable": 1, "quickSearch": 1, "filterable": 0, "changeable": 0, "ext": "http://127.0.0.1:9978/file/tvfan/token.txt+4k|auto|fhd$$$<EMAIL>$$$Yoursmile"}, {"key": "push_agent", "name": "🍭推送", "type": 3, "api": "csp_<PERSON>ush", "searchable": 0, "quickSearch": 0, "filterable": 0, "ext": "http://127.0.0.1:9978/file/tvfan/token.txt+4k|auto|fhd"}, {"key": "csp_<PERSON>bys", "name": "👑泥巴(墙外)", "type": 3, "api": "csp_Ni<PERSON>i", "searchable": 1, "quickSearch": 1, "filterable": 1}, {"key": "csp_trj", "name": "🛫唐人街(墙外)", "type": 3, "api": "csp_<PERSON>", "searchable": 1, "quickSearch": 1, "filterable": 1}, {"key": "农民", "name": "🥝农民", "type": 3, "api": "csp_XBPQ", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "/localized/interface_4_测试接口3/json/农民影视.json"}, {"key": "骚火", "name": "💢骚火", "type": 3, "api": "csp_XBPQ", "playerType": "2", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": {"嗅探词": ".m3u8#.mp4#.m3u8?#freeok.mp4", "分类url": "https://shdy2.com/list/{cateId}-{catePg}.html;;vr1au0", "分类": "电影$1#电视剧$2#韩剧$22#美剧$23#动漫$4", "数组二次截取": "class=\"v_list\">&&</ul>", "数组": "\"v_img\"&&/div>", "标题": "title=\"&&\"", "副标题": "v_note\"&&</div", "跳转播放链接": "<iframe*src=\"&&\"", "二次跳转播放链接": "https://hhjx.hhplayer.com/api.php;post;url=+var url*\"&&\"+&t=+var t*\"&&\"+&key=+var key*\"&&\"+&act=0+&play=1", "三次跳转播放链接": "\"url\"*\"&&\""}}, {"key": "奇优", "name": "️💢奇优", "type": 3, "api": "csp_XBPQ", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "/localized/interface_4_测试接口3/json/奇优.json"}, {"key": "云播", "name": "💢云播", "type": 3, "api": "csp_XBPQ", "searchable": 1, "quickSearch": 1, "filterable": 1, "changeable": 1, "ext": {"影片类型": "分类：&&主演", "导演": "导演：</span>&&</p>", "主演": "主演：</span>&&</p>", "简介": "class=\"sketch content\">&&</span>", "数组": "myui-vodlist__thumb&&</a>", "标题": "title=\"&&\"", "副标题": "pic-tag pic-tag-top\">&&</span>", "图片": "data-original=\"&&\"", "链接": "href=\"&&\"", "搜索模式": "1", "搜索url": "http://www.hktvyun.com/index.php/ajax/suggest?mid=1&wd={wd}&limit=500", "搜索二次截取": "\"list\":[&&]", "搜索数组": "{&&}", "搜索标题": "\"name\":\"&&\"", "搜索图片": "\"pic\":\"&&\"", "搜索链接": "http://www.hktvyun.com/vod/detail/id/+\"id\":&&,+html", "嗅探词": ".m3u8#mp4", "线路数组": "</a><h3&&/h3>", "线路标题": "title\">&&<", "播放数组": "<ul class=\"myui-content__list scrollbar&&</ul>", "播放标题": ">&&<", "播放列表": "<a&&/a>", "分类url": "http://www.hktvyun.com/vod/show/area/{area}/by/{by}/class/{class}/id/{cateId}/lang/{lang}/page/{catePg}/year/{year}.html;;a", "分类": "电影$1#电视剧$2#综艺$3#动漫$4"}}, {"key": "drpy_js_虎牙直播", "name": "🐯虎牙直播", "type": 3, "api": "/localized/interface_4_测试接口3/js/drpy2.min.js", "searchable": 0, "quickSearch": 0, "filterable": 0, "ext": "/localized/interface_4_测试接口3/js/虎牙直播.js"}, {"key": "drpy_js_斗鱼直播", "name": "🐠斗鱼直播", "type": 3, "api": "/localized/interface_4_测试接口3/js/drpy2.min.js", "searchable": 0, "quickSearch": 0, "filterable": 0, "ext": "/localized/interface_4_测试接口3/js/斗鱼直播.js"}, {"key": "drpy_js_有声小说吧", "name": "有声小说吧", "type": 3, "api": "/localized/interface_4_测试接口3/js/drpy2.min.js", "playerType": "2", "searchable": 0, "quickSearch": 0, "filterable": 0, "ext": "/localized/interface_4_测试接口3/js/有声小说吧.js"}, {"key": "drpy_js_JRKAN直播", "name": "JRKAN直播", "type": 3, "api": "/localized/interface_4_测试接口3/js/drpy.min.js", "searchable": 0, "quickSearch": 0, "filterable": 0, "ext": "/localized/interface_4_测试接口3/js/JRKAN直播.js"}, {"key": "短视频", "name": "☘️短视频", "type": 3, "api": "csp_XBPQ", "searchable": 0, "quickSearch": 0, "filterable": 1, "ext": {"主页url": "http://www.sharenice.net", "直接播放": "1", "嗅探词": ".m3u8#m3u8?#.mp4#mp4?#ixigua#.czspp#.flv#.mp3#.m4a#?item/video#is_play_url=1#play/?#qianpailive", "分类": "抖音&快手&微视&火山&场库&美拍&秒拍&全民&梨&好兔&开眼&美女&搞笑&社会&音乐&娱乐&影视&正能量&生活&动漫&萌系&美食&体育&游戏&明星&旅游&时尚", "分类值": "douyin&kua<PERSON><PERSON>&we<PERSON>&huoshan&changku&meipai&miaopai&quanmin&lishipin&haotu&kaiyan&t-576O5aWz&t-5pCe56yR&t-56S+5Lya&t-6Z+z5LmQ&t-5aix5LmQ&t-5b2x6KeG&t-5q2j6IO96YeP&t-55Sf5rS7&t-5Yqo5ryr&t-6JCM57O7&t-576O6aOf&t-5L2T6IKy&t-5ri45oiP&t-5piO5pif&t-5peF5ri4&t-5pe25bCa", "分类url": "http://www.sharenice.net/{cateId}?page={catePg}", "二次截取": "class=\"item-box\">&&</ul>", "数组": "<li&&</li>", "图片": "data-original=\"&&\"", "标题": "title=\"&&\"", "链接": "href=\"&&\""}}, {"key": "酷奇MV", "name": "🎸酷奇MV", "type": 3, "api": "csp_XBPQ", "searchable": 0, "quickSearch": 0, "filterable": 1, "ext": {"主页url": "https://www.kuqimv.com", "直接播放": "1", "短视频": "1", "分类": "华语高清$1#日韩精选$2#欧美MV$3#高清现场$4#影视MV$5#夜店视频$6#车模视频$7#热舞视频$8#美女写真$9#美女打碟$10", "分类url": "https://www.kuqimv.com/play/{cateId}_{catePg}.html;;z", "数组": "<li>&&</li>", "标题": "title=\"&&\"", "副标题": "target=\"play\">&&<", "搜索模式": "1", "搜索url": "https://www.kuqimv.com/search.php?key={wd}"}}, {"key": "csp_<PERSON><PERSON>", "name": "🅱️哔哩哔哩", "type": 3, "api": "csp_<PERSON><PERSON>", "searchable": 0, "quickSearch": 0, "filterable": 1, "ext": "/localized/interface_4_测试接口3/json/Bili.json"}, {"key": "相声小品", "name": "🅱️相声小品", "type": 3, "api": "csp_<PERSON><PERSON>", "searchable": 0, "quickSearch": 0, "filterable": 1, "ext": "/localized/interface_4_测试接口3/json/Blixs.json"}, {"key": "戏曲", "name": "🅱️戏曲", "type": 3, "api": "csp_<PERSON><PERSON>", "searchable": 0, "quickSearch": 0, "filterable": 1, "ext": "/localized/interface_4_测试接口3/json/Blixq.json"}, {"key": "少儿", "name": "🅱️少儿", "type": 3, "api": "csp_<PERSON><PERSON>", "searchable": 0, "quickSearch": 0, "filterable": 1, "ext": "/localized/interface_4_测试接口3/json/少儿教育.json"}, {"key": "小学", "name": "🅱️小学", "type": 3, "api": "csp_<PERSON><PERSON>", "searchable": 0, "quickSearch": 0, "filterable": 1, "ext": "/localized/interface_4_测试接口3/json/小学课堂.json"}, {"key": "初中", "name": "🅱️初中", "type": 3, "api": "csp_<PERSON><PERSON>", "searchable": 0, "quickSearch": 0, "filterable": 1, "ext": "/localized/interface_4_测试接口3/json/初中课堂.json"}, {"key": "高中", "name": "🅱️高中", "type": 3, "api": "csp_<PERSON><PERSON>", "searchable": 0, "quickSearch": 0, "filterable": 1, "ext": "/localized/interface_4_测试接口3/json/高中课堂.json"}], "parses": [{"name": "Web聚合", "type": 3, "url": "Web"}, {"name": "Json聚合", "type": 3, "url": "Demo"}, {"name": "777", "type": 0, "url": "https://jx.777jiexi.com/player/?url=", "ext": {"flag": ["qq", "腾讯", "qiyi", "爱奇艺", "奇艺", "youku", "优酷", "mgtv", "芒果", "letv", "乐视", "pptv", "PPTV", "sohu", "bilibili", "哔哩哔哩", "哔哩"], "header": {"User-Agent": "okhttp/4.1.0"}}}, {"name": "jsonplayer", "type": 0, "url": "https://jx.jsonplayer.com/player/?url=", "ext": {"flag": ["qq", "腾讯", "qiyi", "爱奇艺", "奇艺", "youku", "优酷", "mgtv", "芒果", "letv", "乐视", "pptv", "PPTV", "sohu", "bilibili", "哔哩哔哩", "哔哩"], "header": {"User-Agent": "okhttp/4.1.0"}}}, {"name": "xmflv", "type": 0, "url": "https://jx.xmflv.com/?url=", "ext": {"flag": ["qq", "腾讯", "qiyi", "爱奇艺", "奇艺", "youku", "优酷", "mgtv", "芒果", "letv", "乐视", "pptv", "PPTV", "sohu", "bilibili", "哔哩哔哩", "哔哩"]}}, {"name": "剖云", "type": 0, "url": "https://www.pouyun.com/?url=", "ext": {"flag": ["qq", "腾讯", "qiyi", "爱奇艺", "奇艺", "youku", "优酷", "mgtv", "芒果", "letv", "乐视", "pptv", "PPTV", "sohu", "bilibili", "哔哩哔哩", "哔哩"]}}], "lives": [{"name": "live", "type": 0, "url": "/localized/interface_4_测试接口3/live/live.txt", "playerType": 1, "ua": "okhttp/3.15", "epg": "http://diyp.112114.xyz/?ch={name}&date={date}", "logo": "https://epg.112114.xyz/logo/{name}.png"}], "doh": [{"name": "Google", "url": "https://dns.google/dns-query", "ips": ["*******", "*******"]}, {"name": "Cloudflare", "url": "https://cloudflare-dns.com/dns-query", "ips": ["*******", "*******", "2606:4700:4700::1111", "2606:4700:4700::1001"]}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://dns.adguard.com/dns-query", "ips": ["*************", "*************"]}, {"name": "DNSWatch", "url": "https://resolver2.dns.watch/dns-query", "ips": ["************", "************"]}, {"name": "Quad9", "url": "https://dns.quad9.net/dns-quer", "ips": ["*******", "***************"]}], "rules": [{"name": "wp", "hosts": ["vip.123pan.cn", "rescdn.wuxivlog.cn"], "regex": ["#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:20.840000,[\\s\\S]*?#EXT-X-DISCONTINUITY", "#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:10.120000,[\\s\\S]*?#EXT-X-DISCONTINUITY", "#EXTINF.*?\\s+.*?1o.*?\\.ts\\s+"]}, {"name": "索尼", "hosts": ["suonizy"], "regex": ["#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:15.1666,[\\s\\S]*?#EXT-X-DISCONTINUITY", "#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:15.2666,[\\s\\S]*?#EXT-X-DISCONTINUITY", "#EXTINF.*?\\s+.*?1o.*?\\.ts\\s+"]}, {"name": "海外看", "hosts": ["haiwaikan"], "regex": ["8.1748", "10.0099", "10.3333"]}, {"name": "星星", "hosts": ["aws.ulivetv.net"], "regex": ["#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:8,[\\s\\S]*?#EXT-X-DISCONTINUITY", "#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:9,[\\s\\S]*?#EXT-X-DISCONTINUITY"]}, {"name": "农民", "hosts": ["toutiaovod.com"], "regex": ["video/tos/cn"]}, {"name": "hwk", "hosts": ["haiwaikan"], "regex": ["10.0099", "10.3333", "16.0599", "8.1748", "10.85"]}, {"name": "yqk", "hosts": ["yqk88"], "regex": ["18.4", "15.1666"]}, {"name": "sn", "hosts": ["suonizy"], "regex": ["15.1666", "15.2666"]}, {"name": "xx", "hosts": ["aws.ulivetv.net"], "regex": ["#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:8,[\\s\\S]*?#EXT-X-DISCONTINUITY"]}, {"name": "量子", "hosts": ["vip.lz", "v.cdnlz", "hd.lz"], "regex": ["#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:6.433333,[\\s\\S]*?#EXT-X-DISCONTINUITY", "#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:18.5333,[\\s\\S]*?#EXT-X-DISCONTINUITY", "#EXTINF.*?\\s+.*?1o.*?\\.ts\\s+"]}, {"name": "非凡", "hosts": ["vip.ffzy", "hd.ffzy"], "regex": ["#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:6.666667,[\\s\\S]*?#EXT-X-DISCONTINUITY", "#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:25.0666,[\\s\\S]*?#EXT-X-DISCONTINUITY", "#EXTINF.*?\\s+.*?1o.*?\\.ts\\s+"]}, {"name": "暴风", "hosts": ["bfzy", "s5.bfzycdn"], "regex": ["#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:3,[\\s\\S]*?#EXT-X-DISCONTINUITY", "#EXTINF.*?\\s+.*?1o.*?\\.ts\\s+"]}, {"name": "火山", "hosts": ["huoshan.com"], "regex": ["item_id="]}, {"name": "抖音", "hosts": ["douyin.com"], "regex": ["is_play_url="]}, {"name": "cl", "hosts": ["magnet"], "regex": ["最 新", "直 播", "更 新"]}], "flags": ["youku", "qq", "QQ", "<PERSON><PERSON><PERSON>", "qiyi", "letv", "sohu", "tudou", "pptv", "PPTV", "mgtv", "ltnb", "rx", "SLYS4k", "BYGA", "BYGB", "luanzi", "dxzy", "QEYSS", "<PERSON><PERSON><PERSON>", "AliS", "122", "chuang<PERSON>", "CL4K", "x<PERSON><PERSON>", "wuduzy", "wasu", "bilibili", "ren<PERSON><PERSON>", "xmm", "xigua", "m1905", "funshion", "优酷", "芒果", "腾讯", "爱奇艺", "奇艺", "哔哩哔哩", "哔哩", "西瓜视频", "腾讯视频", "奇艺视频", "优酷视频", "芒果视频", "乐视视频", "FY", "fy", "<PERSON><PERSON><PERSON><PERSON>", "fei<PERSON>", "飞云"], "ijk": [{"group": "软解码", "options": [{"category": 4, "name": "opensles", "value": "0"}, {"category": 4, "name": "overlay-format", "value": "842225234"}, {"category": 4, "name": "framedrop", "value": "1"}, {"category": 4, "name": "soundtouch", "value": "1"}, {"category": 4, "name": "start-on-prepared", "value": "1"}, {"category": 1, "name": "http-detect-rangeupport", "value": "0"}, {"category": 1, "name": "fflags", "value": "fastseek"}, {"category": 2, "name": "skip_loop_filter", "value": "48"}, {"category": 4, "name": "reconnect", "value": "1"}, {"category": 4, "name": "enable-accurateeek", "value": "0"}, {"category": 4, "name": "mediacodec", "value": "0"}, {"category": 4, "name": "mediacodec-auto-rotate", "value": "0"}, {"category": 4, "name": "mediacodec-handle-resolution-change", "value": "0"}, {"category": 4, "name": "mediacodec-hevc", "value": "0"}, {"category": 1, "name": "dns_cache_timeout", "value": "600000000"}]}, {"group": "硬解码", "options": [{"category": 4, "name": "opensles", "value": "0"}, {"category": 4, "name": "overlay-format", "value": "842225234"}, {"category": 4, "name": "framedrop", "value": "1"}, {"category": 4, "name": "soundtouch", "value": "1"}, {"category": 4, "name": "start-on-prepared", "value": "1"}, {"category": 1, "name": "http-detect-rangeupport", "value": "0"}, {"category": 1, "name": "fflags", "value": "fastseek"}, {"category": 2, "name": "skip_loop_filter", "value": "48"}, {"category": 4, "name": "reconnect", "value": "1"}, {"category": 4, "name": "enable-accurateeek", "value": "0"}, {"category": 4, "name": "mediacodec", "value": "1"}, {"category": 4, "name": "mediacodec-auto-rotate", "value": "1"}, {"category": 4, "name": "mediacodec-handle-resolution-change", "value": "1"}, {"category": 4, "name": "mediacodec-hevc", "value": "1"}, {"category": 1, "name": "dns_cache_timeout", "value": "600000000"}]}], "ads": ["iqiyi.hbuioo.com", "vip.ffzyad.com", "wan.51img1.com", "https://lf1-cdn-tos.bytegoofy.com/obj/tos-cn-i-dy/455ccf9e8ae744378118e4bd289288dd", "mimg.0c1q0l.cn", "www.googletagmanager.com", "www.google-analytics.com", "mc.usihnbcq.cn", "mg.g1mm3d.cn", "mscs.svaeuzh.cn", "cnzz.hhttm.top", "tp.vinuxhome.com", "cnzz.mmstat.com", "www.baihuillq.com", "s23.cnzz.com", "z3.cnzz.com", "c.cnzz.com", "stj.v1vo.top", "z12.cnzz.com", "img.mosflower.cn", "tips.gamevvip.com", "ehwe.yhdtns.com", "xdn.cqqc3.com", "www.jixunkyy.cn", "sp.chemacid.cn", "hm.baidu.com", "s9.cnzz.com", "z6.cnzz.com", "um.cavuc.com", "mav.mavuz.com", "wofwk.aoidf3.com", "z5.cnzz.com", "xc.hubeijieshikj.cn", "tj.tianwenhu.com", "xg.gars57.cn", "k.jinxiuzhilv.com", "cdn.bootcss.com", "ppl.xunzhuo123.com", "xomk.jiangjunmh.top", "img.xunzhuo123.com", "z1.cnzz.com", "s13.cnzz.com", "xg.huataisangao.cn", "z7.cnzz.com", "xg.huataisangao.cn", "z2.cnzz.com", "s96.cnzz.com", "q11.cnzz.com", "thy.dacedsfa.cn", "xg.whsbpw.cn", "s19.cnzz.com", "z8.cnzz.com", "s4.cnzz.com", "f5w.as12df.top", "ae01.alicdn.com", "www.92424.cn", "k.wudejia.com", "vivovip.mmszxc.top", "qiu.xixiqiu.com", "cdnjs.hnfenxun.com", "cms.qdwght.com"]}