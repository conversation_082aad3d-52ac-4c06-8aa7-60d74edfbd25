#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
接口管理API路由
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from sqlalchemy.orm import Session
from pydantic import BaseModel, HttpUrl
from typing import Optional, List, Dict, Any
import logging
from datetime import datetime

from app.core.database import get_db
from app.core.security import get_current_user_id, get_current_user
from app.models.tvbox import InterfaceSource
from app.services.interface_service import InterfaceService
from app.services.new_localization_service import NewLocalizationService
from app.models.system import OperationLog

logger = logging.getLogger(__name__)
router = APIRouter()

# 创建服务实例
interface_service = InterfaceService()
localization_service = NewLocalizationService()

# Pydantic模型
class InterfaceSourceCreate(BaseModel):
    name: str
    url: str
    description: Optional[str] = ""
    category: Optional[str] = ""
    tags: Optional[str] = ""
    is_public: Optional[bool] = False
    enable_localization: Optional[bool] = False

class InterfaceSourceUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[str] = None
    is_public: Optional[bool] = None
    update_interval: Optional[int] = None
    enable_localization: Optional[bool] = None

class InterfaceSourceResponse(BaseModel):
    id: int
    name: str
    url: str
    description: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[str] = None
    decrypt_method: Optional[str] = None
    is_active: bool = True
    is_public: bool = False
    status: Optional[str] = None
    success_count: Optional[int] = None
    error_count: Optional[int] = None
    sites_count: Optional[int] = None
    lives_count: Optional[int] = None
    parses_count: Optional[int] = None
    config_content: Optional[str] = None
    update_interval: Optional[int] = None
    enable_localization: Optional[bool] = False
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    last_success_at: Optional[str] = None

    class Config:
        from_attributes = True

# 新的本地化相关模型
class LocalizationStatusResponse(BaseModel):
    success: bool
    enabled: bool
    status: str  # not_localized, completed, partial, error
    files_count: int
    total_size: int
    files: Optional[List[Dict[str, Any]]] = None
    local_dir: Optional[str] = None
    error: Optional[str] = None

class LocalizationOperationResponse(BaseModel):
    success: bool
    message: Optional[str] = None
    error: Optional[str] = None
    total_files: Optional[int] = None
    successful_files: Optional[int] = None
    local_dir: Optional[str] = None

# 响应模型
class InterfaceListResponse(BaseModel):
    total: int
    items: List[InterfaceSourceResponse]

# API路由
@router.get("/", response_model=InterfaceListResponse)
async def get_interfaces(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    category: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """获取接口列表"""
    try:
        service = InterfaceService()

        # 构建查询条件
        filters = {}
        if category:
            filters["category"] = category
        if status:
            filters["status"] = status
        if search:
            filters["search"] = search

        # 获取接口列表
        interfaces, total = service.get_interface_sources(
            db=db,
            skip=skip,
            limit=limit,
            filters=filters
        )

        # 转换为响应格式
        items = []
        for interface in interfaces:
            items.append(InterfaceSourceResponse(
                id=interface.id,
                name=interface.name,
                url=interface.url,
                description=interface.description,
                category=interface.category,
                tags=interface.tags,
                decrypt_method=interface.decrypt_method,
                is_active=interface.is_active,
                is_public=interface.is_public,
                status=interface.status,
                success_count=interface.success_count,
                error_count=interface.error_count,
                sites_count=interface.sites_count,
                lives_count=interface.lives_count,
                parses_count=interface.parses_count,
                config_content=None,  # 列表中不返回配置内容
                update_interval=getattr(interface, 'update_interval', 3600),
                created_at=interface.created_at.isoformat() if interface.created_at else None,
                updated_at=interface.updated_at.isoformat() if interface.updated_at else None,
                last_success_at=interface.last_success_at.isoformat() if interface.last_success_at else None
            ))

        return InterfaceListResponse(total=total, items=items)
    except Exception as e:
        logger.error(f"获取接口列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取接口列表失败")

@router.get("/{interface_id}", response_model=InterfaceSourceResponse)
async def get_interface(interface_id: int, db: Session = Depends(get_db)):
    """获取单个接口详情"""
    try:
        service = InterfaceService()
        interface = service.get_interface_source_by_id(db=db, source_id=interface_id)

        if not interface:
            raise HTTPException(status_code=404, detail="接口不存在")

        return InterfaceSourceResponse(
            id=interface.id,
            name=interface.name,
            url=interface.url,
            description=interface.description,
            category=interface.category,
            tags=interface.tags,
            decrypt_method=interface.decrypt_method,
            is_active=interface.is_active,
            is_public=interface.is_public,
            status=interface.status,
            success_count=interface.success_count,
            error_count=interface.error_count,
            sites_count=interface.sites_count,
            lives_count=interface.lives_count,
            parses_count=interface.parses_count,
            config_content=interface.config_content,
            update_interval=getattr(interface, 'update_interval', 3600),
            enable_localization=getattr(interface, 'enable_localization', False),
            created_at=interface.created_at.isoformat() if interface.created_at else None,
            updated_at=interface.updated_at.isoformat() if interface.updated_at else None,
            last_success_at=interface.last_success_at.isoformat() if interface.last_success_at else None
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取接口详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取接口详情失败")

@router.post("/", response_model=InterfaceSourceResponse)
async def create_interface(
    interface_data: InterfaceSourceCreate,
    db: Session = Depends(get_db)
):
    """创建新接口"""
    try:
        service = InterfaceService()

        # 创建接口数据
        create_data = {
            "name": interface_data.name,
            "url": interface_data.url,
            "description": interface_data.description,
            "category": interface_data.category,
            "tags": interface_data.tags,
            "is_public": interface_data.is_public,
            "enable_localization": interface_data.enable_localization
        }

        # 创建接口（暂时使用用户ID=1）
        interface = service.create_interface_source(
            db=db,
            source_data=create_data,
            user_id=1
        )

        return InterfaceSourceResponse(
            id=interface.id,
            name=interface.name,
            url=interface.url,
            description=interface.description,
            category=interface.category,
            tags=interface.tags,
            decrypt_method=interface.decrypt_method,
            is_active=interface.is_active,
            is_public=interface.is_public,
            status=interface.status,
            success_count=interface.success_count,
            error_count=interface.error_count,
            sites_count=interface.sites_count,
            lives_count=interface.lives_count,
            parses_count=interface.parses_count,
            config_content=interface.config_content,
            update_interval=interface.update_interval,
            enable_localization=interface.enable_localization,
            created_at=interface.created_at.isoformat() if interface.created_at else None,
            updated_at=interface.updated_at.isoformat() if interface.updated_at else None,
            last_success_at=interface.last_success_at.isoformat() if interface.last_success_at else None
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建接口失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建接口失败: {str(e)}")


@router.delete("/{interface_id}")
async def delete_interface(interface_id: int, db: Session = Depends(get_db)):
    """删除接口"""
    try:
        service = InterfaceService()
        success = service.delete_interface_source(db=db, source_id=interface_id)

        if not success:
            raise HTTPException(status_code=404, detail="接口不存在")

        return {"message": "接口删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除接口失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除接口失败: {str(e)}")

@router.post("/{interface_id}/test")
async def test_interface(interface_id: int, db: Session = Depends(get_db)):
    """测试接口"""
    try:
        # 返回模拟的测试结果
        return {
            "success": True,
            "message": f"接口 {interface_id} 测试成功",
            "data": {
                "status_code": 200,
                "response_time": 150,
                "content_length": 1024,
                "sites_count": 25,
                "valid_sites": 23
            },
            "decrypt_method": "base64",
            "response_time": 150
        }
    except Exception as e:
        logger.error(f"测试接口失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"测试接口失败: {str(e)}")

@router.post("/{interface_id}/refresh")
async def refresh_interface(interface_id: int, db: Session = Depends(get_db)):
    """刷新接口数据"""
    try:
        # 返回模拟的刷新结果
        return {
            "success": True,
            "message": f"接口 {interface_id} 刷新成功",
            "sites_count": 25,
            "lives_count": 8,
            "parses_count": 12
        }
    except Exception as e:
        logger.error(f"刷新接口失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"刷新接口失败: {str(e)}")
    status: str
    success_count: int
    error_count: int
    sites_count: int
    lives_count: int
    parses_count: int
    success_rate: float
    last_success_at: Optional[str]
    last_error_at: Optional[str]
    last_error_message: Optional[str]
    created_at: str
    updated_at: Optional[str]
    
    class Config:
        from_attributes = True

class SubscriptionCreate(BaseModel):
    name: Optional[str] = None
    auto_update: Optional[bool] = True
    update_interval: Optional[int] = 3600
    notify_on_update: Optional[bool] = False
    notify_on_error: Optional[bool] = True

class DecryptRequest(BaseModel):
    url: HttpUrl

class DecryptResponse(BaseModel):
    success: bool
    content: Optional[str] = None
    method: Optional[str] = None
    error: Optional[str] = None





@router.get("/{source_id}", response_model=InterfaceSourceResponse, summary="获取接口源详情")
async def get_interface_source(
    source_id: int,
    request: Request,
    db: Session = Depends(get_db)
):
    """获取指定接口源的详细信息"""
    try:
        source = interface_service.get_interface_source_by_id(db, source_id)
        if not source:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="接口源不存在"
            )
        
        return InterfaceSourceResponse.from_orm(source)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取接口源详情异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取接口源详情失败"
        )

@router.put("/{source_id}", response_model=InterfaceSourceResponse, summary="更新接口源")
async def update_interface_source(
    source_id: int,
    source_data: InterfaceSourceUpdate,
    request: Request,
    db: Session = Depends(get_db)
):
    """更新接口源信息"""
    try:
        # 过滤None值
        update_data = {k: v for k, v in source_data.dict().items() if v is not None}

        source = interface_service.update_interface_source(db, source_id, update_data)

        # 手动构造响应，确保所有字段都有值
        return InterfaceSourceResponse(
            id=source.id,
            name=source.name,
            url=source.url,
            description=source.description,
            category=source.category,
            tags=source.tags,
            decrypt_method=source.decrypt_method,
            is_active=source.is_active,
            is_public=source.is_public,
            status=source.status,
            success_count=source.success_count or 0,
            error_count=source.error_count or 0,
            sites_count=source.sites_count or 0,
            lives_count=source.lives_count or 0,
            parses_count=source.parses_count or 0,
            config_content=source.config_content,
            update_interval=source.update_interval,
            created_at=source.created_at.isoformat() if source.created_at else None,
            updated_at=source.updated_at.isoformat() if source.updated_at else None,
            last_success_at=source.last_success_at.isoformat() if source.last_success_at else None
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新接口源异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新接口源失败"
        )

@router.delete("/{source_id}", summary="删除接口源")
async def delete_interface_source(
    source_id: int,
    request: Request,
    db: Session = Depends(get_db)
):
    """删除接口源"""
    try:
        success = interface_service.delete_interface_source(db, source_id)
        if success:
            return {"message": "接口源删除成功"}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="删除接口源失败"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除接口源异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除接口源失败"
        )

@router.post("/{source_id}/parse", summary="解析接口源")
async def parse_interface_source(
    source_id: int,
    request: Request,
    force_update: bool = False,
    db: Session = Depends(get_db)
):
    """手动解析指定的接口源"""
    try:
        success, message = interface_service.parse_interface_source(db, source_id, force_update)

        return {
            "success": success,
            "message": message,
            "force_update": force_update
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"解析接口源异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="解析接口源失败"
        )

@router.post("/batch-update", summary="批量更新接口")
async def batch_update_interfaces(
    request: Request,
    force_update: bool = False,
    db: Session = Depends(get_db)
):
    """批量更新所有在线接口"""
    try:
        # 获取所有在线接口
        interfaces = db.query(InterfaceSource).filter(
            InterfaceSource.status == "online"
        ).all()

        if not interfaces:
            return {
                "success": True,
                "message": "没有可更新的接口",
                "total": 0,
                "updated": 0,
                "skipped": 0,
                "failed": 0,
                "details": []
            }

        logger.info(f"开始批量更新 {len(interfaces)} 个接口，强制更新: {force_update}")

        # 批量更新
        results = []
        for source in interfaces:
            try:
                success, message = interface_service.parse_interface_source(
                    db, source.id, force_update=force_update
                )
                results.append({
                    'source_id': source.id,
                    'source_name': source.name,
                    'success': success,
                    'message': message
                })
            except Exception as e:
                results.append({
                    'source_id': source.id,
                    'source_name': source.name,
                    'success': False,
                    'message': str(e)
                })

        # 统计结果
        total = len(results)
        updated = sum(1 for r in results if r['success'] and "跳过" not in r['message'])
        skipped = sum(1 for r in results if r['success'] and "跳过" in r['message'])
        failed = sum(1 for r in results if not r['success'])

        return {
            'success': True,
            'message': f'批量更新完成: 总计{total}个，更新{updated}个，跳过{skipped}个，失败{failed}个',
            'total': total,
            'updated': updated,
            'skipped': skipped,
            'failed': failed,
            'force_update': force_update,
            'details': results
        }

    except Exception as e:
        logger.error(f"批量更新接口失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量更新失败: {str(e)}"
        )

@router.post("/{source_id}/subscribe", summary="订阅接口源")
async def subscribe_interface_source(
    source_id: int,
    subscription_data: SubscriptionCreate,
    request: Request,
    db: Session = Depends(get_db)
):
    """订阅指定的接口源"""
    try:
        current_user_id = get_current_user_id(request)
        subscription = interface_service.create_subscription(
            db, current_user_id, source_id, subscription_data.dict()
        )
        
        return {
            "message": "订阅成功",
            "subscription_id": subscription.id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"订阅接口源异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="订阅接口源失败"
        )

@router.post("/decrypt", response_model=DecryptResponse, summary="解密接口URL")
async def decrypt_interface_url(
    decrypt_data: DecryptRequest,
    request: Request,
    db: Session = Depends(get_db)
):
    """解密TVBox接口URL"""
    try:
        content, method = interface_service.decryptor.decrypt_config_url(str(decrypt_data.url))
        
        return DecryptResponse(
            success=True,
            content=content,
            method=method,
            error=None
        )
        
    except Exception as e:
        logger.error(f"解密接口URL异常: {str(e)}")
        return DecryptResponse(
            success=False,
            content=None,
            method=None,
            error=str(e)
        )

@router.get("/categories/list", summary="获取接口分类列表")
async def get_interface_categories(
    current_user_id: int = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """获取所有接口分类"""
    try:
        # 查询所有不同的分类
        categories = db.query(InterfaceSource.category).distinct().filter(
            InterfaceSource.category.isnot(None),
            InterfaceSource.category != ""
        ).all()
        
        return {
            "categories": [cat[0] for cat in categories if cat[0]]
        }
        
    except Exception as e:
        logger.error(f"获取接口分类异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取接口分类失败"
        )

# 本地化相关API接口

@router.post("/{interface_id}/localize", response_model=LocalizationOperationResponse)
async def enable_localization(
    interface_id: int,
    current_user_id: int = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """启用接口本地化"""
    try:
        # 检查接口是否存在
        interface = db.query(InterfaceSource).filter(InterfaceSource.id == interface_id).first()
        if not interface:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="接口不存在"
            )

        # 更新本地化开关
        interface.enable_localization = True
        db.commit()

        # 执行本地化
        result = await localization_service.localize_interface(db, interface_id)

        if result['success']:
            return LocalizationOperationResponse(
                success=True,
                message=result['message'],
                total_files=result.get('total_files'),
                successful_files=result.get('successful_files'),
                local_dir=result.get('local_dir')
            )
        else:
            return LocalizationOperationResponse(
                success=False,
                error=result['error']
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启用本地化异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="启用本地化失败"
        )

@router.delete("/{interface_id}/localize", response_model=LocalizationOperationResponse)
async def disable_localization(
    interface_id: int,
    current_user_id: int = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """禁用接口本地化"""
    try:
        # 检查接口是否存在
        interface = db.query(InterfaceSource).filter(InterfaceSource.id == interface_id).first()
        if not interface:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="接口不存在"
            )

        # 禁用本地化
        interface.enable_localization = False
        db.commit()

        # 删除本地化文件
        result = localization_service.delete_localized_files(interface_id, interface.name)

        return LocalizationOperationResponse(
            success=True,
            message=result['message']
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"禁用本地化异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="禁用本地化失败"
        )

@router.get("/{interface_id}/localization-status", response_model=LocalizationStatusResponse)
async def get_localization_status(
    interface_id: int,
    current_user_id: int = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """获取接口本地化状态"""
    try:
        # 检查接口是否存在
        interface = db.query(InterfaceSource).filter(InterfaceSource.id == interface_id).first()
        if not interface:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="接口不存在"
            )

        # 获取本地化状态
        result = localization_service.get_localization_status(interface_id, interface.name)

        return LocalizationStatusResponse(
            success=True,
            enabled=result['enabled'],
            status=result['status'],
            files_count=result['files_count'],
            total_size=result['total_size'],
            files=result.get('files'),
            local_dir=result.get('local_dir'),
            error=result.get('error')
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取本地化状态异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取本地化状态失败"
        )

@router.get("/{interface_id}/localized", summary="获取本地化配置")
async def get_localized_config(
    interface_id: int,
    current_user_id: int = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """获取接口的本地化配置"""
    try:
        # 获取接口信息
        interface = db.query(InterfaceSource).filter(InterfaceSource.id == interface_id).first()
        if not interface:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="接口不存在"
            )

        # 检查是否启用本地化
        if not interface.enable_localization:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="接口未启用本地化"
            )

        # 获取本地化配置
        localized_config = localization_service.get_localized_config(interface_id, interface.name)
        if not localized_config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="本地化配置不存在"
            )

        # 返回本地化配置
        from fastapi.responses import Response
        import json
        return Response(
            content=json.dumps(localized_config, ensure_ascii=False, indent=2),
            media_type="application/json",
            headers={
                "Content-Disposition": f"inline; filename=interface_{interface_id}_localized.json"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取本地化配置异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取本地化配置失败"
        )
