#!/usr/bin/env python3
"""
检查所有接口的配置
"""
import sqlite3
import os
import json

def check_all_configs():
    """检查所有接口的配置"""
    db_path = "data/tvbox.db"
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有接口
        cursor.execute("""
            SELECT id, name, url, 
                   LENGTH(config_content) as config_len,
                   LENGTH(localized_config) as localized_len
            FROM interface_sources 
            ORDER BY id
        """)
        
        results = cursor.fetchall()
        print(f"找到 {len(results)} 个接口:")
        
        for result in results:
            interface_id, name, url, config_len, localized_len = result
            print(f"\n{'='*60}")
            print(f"接口ID: {interface_id}")
            print(f"接口名称: {name}")
            print(f"接口URL: {url}")
            print(f"原始配置长度: {config_len}")
            print(f"本地化配置长度: {localized_len}")
            
            # 获取完整配置内容
            cursor.execute("SELECT config_content FROM interface_sources WHERE id = ?", (interface_id,))
            config_result = cursor.fetchone()
            
            if config_result and config_result[0]:
                try:
                    config_data = json.loads(config_result[0])
                    spider = config_data.get('spider', 'N/A')
                    sites = config_data.get('sites', [])
                    lives = config_data.get('lives', [])
                    parses = config_data.get('parses', [])
                    
                    print(f"Spider: {spider}")
                    print(f"Sites数量: {len(sites)}")
                    print(f"Lives数量: {len(lives)}")
                    print(f"Parses数量: {len(parses)}")
                    
                    # 检查是否看起来像真实配置
                    real_indicators = []
                    
                    # 检查spider
                    if spider and isinstance(spider, str):
                        if spider.startswith('http') and 'example.com' not in spider:
                            real_indicators.append("真实Spider URL")
                        elif spider.startswith('./'):
                            real_indicators.append("本地化Spider")
                        elif 'example.com' in spider:
                            real_indicators.append("测试Spider")
                    
                    # 检查sites
                    if len(sites) > 10:
                        real_indicators.append(f"大量站点({len(sites)}个)")
                    
                    # 检查API类型多样性
                    api_types = set()
                    http_apis = 0
                    for site in sites:
                        api = site.get('api', '')
                        if api.startswith('csp_'):
                            api_types.add('csp')
                        elif api.startswith('http'):
                            api_types.add('http')
                            if 'example.com' not in api:
                                http_apis += 1
                        else:
                            api_types.add('other')
                    
                    if len(api_types) > 1:
                        real_indicators.append(f"多种API类型: {api_types}")
                    
                    if http_apis > 0:
                        real_indicators.append(f"{http_apis}个真实HTTP API")
                    
                    # 显示前几个sites
                    if len(sites) > 0:
                        print(f"前3个Sites:")
                        for i, site in enumerate(sites[:3]):
                            name = site.get('name', 'N/A')
                            api = site.get('api', 'N/A')
                            print(f"  {i+1}. {name} - {api}")
                    
                    # 评估配置真实性
                    if len(real_indicators) >= 2:
                        print(f"🎯 这看起来是真实配置!")
                        print(f"   指标: {', '.join(real_indicators)}")
                    else:
                        print(f"❓ 可能不是完整的真实配置")
                        if real_indicators:
                            print(f"   指标: {', '.join(real_indicators)}")
                        
                except Exception as e:
                    print(f"配置解析失败: {e}")
            else:
                print("无配置内容")
                
    except Exception as e:
        print(f"检查失败: {str(e)}")
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    check_all_configs()
