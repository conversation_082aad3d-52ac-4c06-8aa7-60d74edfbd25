#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def debug_cainisi():
    """调试菜妮丝接口的解密问题"""
    
    from app.services.interface_service import InterfaceService
    import json
    
    service = InterfaceService()
    url = "https://tv.菜妮丝.top"
    
    print(f"调试菜妮丝接口: {url}")
    
    try:
        # 1. 获取原始内容
        normalized_url = service.decryptor._normalize_url(url)
        print(f"标准化URL: {normalized_url}")
        
        content, final_url = service.decryptor._get_json(normalized_url)
        print(f"原始内容长度: {len(content)}")
        print(f"原始内容前200字符: {repr(content[:200])}")
        
        # 2. 检查是否包含Base64标记
        has_base64_marker = "**" in content
        print(f"是否包含Base64标记(**): {has_base64_marker}")
        
        # 3. 检查是否以2423开头（AES/CBC）
        starts_with_2423 = content.startswith("2423")
        print(f"是否以2423开头(AES/CBC): {starts_with_2423}")
        
        # 4. 检查是否被识别为直接配置文件
        is_direct = service.decryptor._is_direct_config_file(normalized_url, content)
        print(f"是否识别为直接配置文件: {is_direct}")
        
        # 5. 验证内容处理
        print(f"\n=== 验证内容处理 ===")
        verified_content = service.decryptor._verify_content(final_url, content)
        print(f"验证后内容长度: {len(verified_content)}")
        print(f"验证后内容前200字符: {repr(verified_content[:200])}")
        
        # 6. 检查验证前后是否相同
        content_changed = content != verified_content
        print(f"验证前后内容是否改变: {content_changed}")
        
        # 7. 手动调用extract_json_from_mixed_content
        print(f"\n=== 手动调用extract_json_from_mixed_content ===")
        extracted_content = service.decryptor.extract_json_from_mixed_content(verified_content)
        print(f"提取后内容长度: {len(extracted_content)}")
        print(f"提取后内容前200字符: {repr(extracted_content[:200])}")
        
        # 8. 检查是否是有效JSON
        try:
            json_data = json.loads(extracted_content)
            print(f"✅ 提取的内容是有效JSON")
            if isinstance(json_data, dict):
                sites_count = len(json_data.get('sites', []))
                lives_count = len(json_data.get('lives', []))
                parses_count = len(json_data.get('parses', []))
                print(f"配置统计: {sites_count}个站点, {lives_count}个直播源, {parses_count}个解析器")
                
                if sites_count == 0:
                    print("⚠️ 警告：没有解析出任何站点！这说明解密失败了")
                    print(f"配置包含的字段: {list(json_data.keys())}")
        except json.JSONDecodeError:
            print(f"❌ 提取的内容不是有效JSON")
        
        # 9. 完整解密流程
        print(f"\n=== 完整解密流程 ===")
        full_content, full_method = service.decryptor.decrypt_config_url(url)
        print(f"完整解密方法: {full_method}")
        print(f"完整解密内容长度: {len(full_content)}")
        
        # 10. 对比两个接口的差异
        print(f"\n=== 对比饭太硬接口（正常工作）===")
        working_url = "http://www.饭太硬.com/tv"
        working_content, working_final_url = service.decryptor._get_json(working_url)
        print(f"饭太硬原始内容长度: {len(working_content)}")
        print(f"饭太硬是否包含Base64标记: {'**' in working_content}")
        print(f"饭太硬是否以2423开头: {working_content.startswith('2423')}")
        print(f"饭太硬原始内容前200字符: {repr(working_content[:200])}")
        
    except Exception as e:
        print(f"调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_cainisi()
