#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全新的本地化服务
完全重新设计，不影响原始配置，提供直观的文件管理
"""

import os
import json
import asyncio
import aiohttp
import hashlib
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from urllib.parse import urlparse, urljoin
import re
from datetime import datetime

from sqlalchemy.orm import Session
from app.models.tvbox import InterfaceSource


class NewLocalizationService:
    """全新的本地化服务"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.base_dir = Path("data/localized")
        self.base_dir.mkdir(parents=True, exist_ok=True)
        
        # 文件类型分类
        self.file_categories = {
            'spider': ['jar'],
            'js': ['js'],
            'json': ['json'],
            'jar': ['jar'],
            'live': ['m3u', 'm3u8', 'txt']
        }
        
        # 支持的文件扩展名
        self.supported_extensions = {
            '.jar', '.js', '.json', '.m3u', '.m3u8', '.txt'
        }
    
    def get_interface_dir(self, interface_id: int, interface_name: str) -> Path:
        """获取接口的本地化目录"""
        safe_name = re.sub(r'[^\w\-_\.]', '_', interface_name)
        return self.base_dir / f"interface_{interface_id}_{safe_name}"
    
    def get_category_dir(self, interface_dir: Path, category: str) -> Path:
        """获取分类目录"""
        return interface_dir / category
    
    def detect_file_type(self, url: str, field_name: str = "", field_path: str = "") -> str:
        """检测文件类型"""
        # 清理URL，移除MD5等参数
        clean_url = url.split(';')[0].split('?')[0]

        # 获取文件扩展名
        parsed = urlparse(clean_url)
        path = parsed.path.lower()

        # 根据文件扩展名判断
        if path.endswith('.jar'):
            return 'jar'
        elif path.endswith('.js'):
            return 'js'
        elif path.endswith('.json'):
            return 'json'
        elif path.endswith(('.m3u', '.m3u8')):
            return 'live'
        elif path.endswith('.txt'):
            return 'live'
        elif path.endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp')):
            # 对于图片文件，如果在spider字段中，通常是jar文件的伪装
            return 'spider'
        else:
            # 无扩展名文件，根据字段名称和URL特征推断
            return self._infer_file_type_by_context(url, field_name, field_path)

    def _infer_file_type_by_context(self, url: str, field_name: str, field_path: str) -> str:
        """根据上下文推断文件类型"""
        # 特殊处理：如果是ext.json字段，强制识别为json类型
        if 'ext' in field_path and 'json' in field_path:
            return 'json'

        # 根据字段名称推断
        if field_name == 'spider' or 'spider' in field_path:
            return 'spider'
        elif field_name == 'api' or 'api' in field_path:
            return 'js'
        elif field_name == 'ext' or 'ext' in field_path:
            return 'js'
        elif field_name == 'json' or 'json' in field_path:
            return 'json'
        elif field_name == 'jar' or 'jar' in field_path:
            return 'jar'
        elif field_name == 'url' and ('live' in field_path or 'lives' in field_path):
            return 'live'

        # 根据URL特征推断
        if 'drpy' in url.lower() or '.js' in url.lower():
            return 'js'
        elif 'json' in url.lower() or 'file.html' in url.lower():
            return 'json'
        elif 'm3u' in url.lower() or 'live' in url.lower():
            return 'live'

        # 默认返回unknown
        return 'unknown'
    
    def extract_urls_from_config(self, config: dict, base_url: str) -> List[Dict]:
        """从配置中提取需要下载的URL"""
        urls = []

        # 1. 处理spider字段
        if 'spider' in config and config['spider']:
            spider_value = config['spider']
            self.logger.info(f"检查spider字段: {spider_value}")

            # 处理包含MD5值的spider URL
            if ';md5;' in spider_value:
                # 分割URL和MD5值
                spider_url = spider_value.split(';md5;')[0]
                md5_value = spider_value.split(';md5;')[1] if len(spider_value.split(';md5;')) > 1 else None
                self.logger.info(f"Spider字段解析: URL={spider_url}, MD5={md5_value}")
            else:
                spider_url = spider_value
                md5_value = None

            if self._is_remote_url(spider_url):
                file_type = self.detect_file_type(spider_url, 'spider', 'spider')
                self.logger.info(f"Spider URL文件类型: {file_type}")
                urls.append({
                    'url': spider_url,
                    'original_value': spider_value,  # 保存原始值用于替换
                    'md5': md5_value,
                    'type': 'spider',
                    'field_path': 'spider',
                    'category': 'spider',
                    'force_extension': '.jar'  # 强制改为jar后缀
                })
                self.logger.info(f"添加spider URL到下载列表: {spider_url}")
            else:
                self.logger.info(f"Spider URL不是远程URL，跳过: {spider_url}")

        # 2. 处理sites中的api、ext、jar、json字段
        if 'sites' in config:
            for i, site in enumerate(config['sites']):
                # api字段
                if 'api' in site and self._is_remote_url(site['api']):
                    field_path = f'sites[{i}].api'
                    file_type = self.detect_file_type(site['api'], 'api', field_path)
                    if file_type != 'unknown':
                        urls.append({
                            'url': site['api'],
                            'type': file_type,
                            'field_path': field_path,
                            'category': file_type
                        })

                # ext字段中的URL
                if 'ext' in site:
                    if isinstance(site['ext'], str) and self._is_remote_url(site['ext']):
                        field_path = f'sites[{i}].ext'
                        file_type = self.detect_file_type(site['ext'], 'ext', field_path)
                        if file_type != 'unknown':
                            urls.append({
                                'url': site['ext'],
                                'type': file_type,
                                'field_path': field_path,
                                'category': file_type
                            })
                    elif isinstance(site['ext'], dict):
                        for key, value in site['ext'].items():
                            if isinstance(value, str) and self._is_remote_url(value):
                                field_path = f'sites[{i}].ext.{key}'
                                file_type = self.detect_file_type(value, key, field_path)
                                if file_type != 'unknown':
                                    url_info = {
                                        'url': value,
                                        'type': file_type,
                                        'field_path': field_path,
                                        'category': file_type
                                    }

                                    # 为json类型的文件设置强制扩展名
                                    if file_type == 'json':
                                        url_info['force_extension'] = '.json'

                                    urls.append(url_info)

                # jar字段
                if 'jar' in site and self._is_remote_url(site['jar']):
                    # 处理jar字段，可能包含MD5验证信息
                    jar_full_value = site['jar']  # 完整的jar字段值
                    jar_url = jar_full_value.split(';')[0]  # 移除MD5部分，获取纯URL
                    field_path = f'sites[{i}].jar'
                    file_type = self.detect_file_type(jar_url, 'jar', field_path)
                    if file_type != 'unknown':
                        urls.append({
                            'url': jar_url,  # 用于下载的纯URL
                            'original_value': jar_full_value,  # 完整的原始值（包含MD5）
                            'type': 'jar',
                            'field_path': field_path,
                            'category': 'jar',
                            'force_extension': '.jar'  # 强制jar扩展名
                        })
                        self.logger.info(f"添加jar URL到下载列表: {jar_url} (原始值: {jar_full_value})")

        # 3. 处理lives中的url字段
        if 'lives' in config:
            for i, live in enumerate(config['lives']):
                if 'url' in live and self._is_remote_url(live['url']):
                    urls.append({
                        'url': live['url'],
                        'type': 'live',
                        'field_path': f'lives[{i}].url',
                        'category': 'live'
                    })
        
        return urls
    
    def _is_remote_url(self, url: str) -> bool:
        """判断是否为远程URL"""
        if not isinstance(url, str):
            return False
        
        # 排除相对路径
        if url.startswith('./') or url.startswith('../'):
            return False
        
        # 排除纯文件名
        if '/' not in url and '\\' not in url:
            return False
        
        # 检查是否为HTTP/HTTPS URL
        return url.startswith(('http://', 'https://'))
    
    async def is_webpage(self, url: str) -> bool:
        """检测URL是否返回HTML网页"""
        try:
            # 检查URL模式 - 明显的网站首页
            parsed = urlparse(url)
            if parsed.path in ['', '/'] or parsed.path.endswith('/'):
                self.logger.info(f"URL模式检测为网站首页，跳过下载: {url}")
                return True

            async with aiohttp.ClientSession() as session:
                # 先发送HEAD请求检查Content-Type
                try:
                    async with session.head(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                        content_type = response.headers.get('content-type', '').lower()
                        if 'text/html' in content_type:
                            self.logger.info(f"Content-Type检测为HTML，跳过下载: {url} (Content-Type: {content_type})")
                            return True
                except:
                    # HEAD请求失败，继续用GET请求检查
                    pass

                # 如果HEAD请求不支持，用GET请求下载少量内容检查
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    if response.status == 200:
                        content_type = response.headers.get('content-type', '').lower()
                        if 'text/html' in content_type:
                            self.logger.info(f"Content-Type检测为HTML，跳过下载: {url} (Content-Type: {content_type})")
                            return True

                        # 读取前1024字节检查内容
                        sample_content = await response.content.read(1024)
                        try:
                            sample_text = sample_content.decode('utf-8', errors='ignore').lower()
                            # 检查HTML标签
                            html_indicators = ['<!doctype html', '<html', '<head>', '<body>', '<title>']
                            if any(indicator in sample_text for indicator in html_indicators):
                                self.logger.info(f"内容检测为HTML网页，跳过下载: {url}")
                                return True
                        except:
                            pass

            return False
        except Exception as e:
            self.logger.warning(f"网页检测失败，继续下载: {url} - {str(e)}")
            return False

    async def download_file(self, url: str, local_path: Path, force_extension: str = None) -> Dict:
        """下载文件"""
        try:
            self.logger.info(f"开始下载文件: {url} -> {local_path}")

            # 检查是否为网页
            if await self.is_webpage(url):
                return {
                    'success': False,
                    'error': 'Skipped: Detected as HTML webpage',
                    'skipped': True
                }

            # 确保目录存在
            local_path.parent.mkdir(parents=True, exist_ok=True)

            # 如果指定了强制扩展名，修改文件名
            if force_extension:
                local_path = local_path.with_suffix(force_extension)

            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=30)) as response:
                    if response.status == 200:
                        content = await response.read()

                        # 写入文件
                        with open(local_path, 'wb') as f:
                            f.write(content)

                        # 计算文件哈希
                        file_hash = hashlib.md5(content).hexdigest()

                        return {
                            'success': True,
                            'local_path': str(local_path),
                            'file_size': len(content),
                            'file_hash': file_hash
                        }
                    else:
                        return {
                            'success': False,
                            'error': f'HTTP {response.status}'
                        }

        except Exception as e:
            self.logger.error(f"下载文件失败 {url}: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def generate_local_filename(self, url: str, file_type: str) -> str:
        """生成本地文件名"""
        # 清理URL
        clean_url = url.split(';')[0].split('?')[0]
        parsed = urlparse(clean_url)
        
        # 获取原始文件名
        filename = os.path.basename(parsed.path)
        
        if not filename or '.' not in filename:
            # 如果没有文件名，生成一个
            url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
            if file_type == 'spider':
                filename = f"spider_{url_hash}.jar"
            elif file_type == 'js':
                filename = f"script_{url_hash}.js"
            elif file_type == 'json':
                filename = f"config_{url_hash}.json"
            elif file_type == 'live':
                filename = f"live_{url_hash}.m3u"
            else:
                filename = f"file_{url_hash}.{file_type}"
        
        return filename
    
    async def localize_interface(self, db: Session, interface_id: int) -> Dict:
        """本地化接口"""
        try:
            # 获取接口信息
            interface = db.query(InterfaceSource).filter(InterfaceSource.id == interface_id).first()
            if not interface:
                return {'success': False, 'error': '接口不存在'}
            
            if not interface.config_content:
                return {'success': False, 'error': '接口配置为空'}
            
            # 解析原始配置
            try:
                config = json.loads(interface.config_content)
            except json.JSONDecodeError as e:
                return {'success': False, 'error': f'配置解析失败: {str(e)}'}
            
            # 创建本地化目录
            interface_dir = self.get_interface_dir(interface_id, interface.name)
            interface_dir.mkdir(parents=True, exist_ok=True)
            
            # 提取需要下载的URL
            urls_to_download = self.extract_urls_from_config(config, interface.url)
            
            if not urls_to_download:
                return {'success': True, 'message': '没有需要本地化的文件', 'files': []}
            
            # 下载文件并记录映射关系
            url_mappings = {}
            downloaded_files = []
            
            for url_info in urls_to_download:
                url = url_info['url']
                category = url_info['category']
                file_type = url_info['type']
                original_value = url_info.get('original_value', url)  # 获取原始值

                # 创建分类目录
                category_dir = self.get_category_dir(interface_dir, category)

                # 生成本地文件名
                filename = self.generate_local_filename(url, file_type)
                local_path = category_dir / filename

                # 下载文件
                force_ext = url_info.get('force_extension')
                result = await self.download_file(url, local_path, force_ext)

                if result['success']:
                    # 获取实际下载的文件路径（可能包含强制扩展名）
                    actual_local_path = Path(result['local_path'])
                    # 记录URL映射 - 使用实际下载URL作为key，使用实际文件名
                    relative_path = f"./{category}/{actual_local_path.name}"
                    url_mappings[url] = relative_path  # 使用实际下载的URL作为key

                    # 对于jar文件，如果original_value包含MD5，也要添加完整值的映射
                    if original_value != url and ';md5;' in original_value:
                        url_mappings[original_value] = relative_path
                        self.logger.info(f"🐾 添加jar完整值映射: {original_value} -> {relative_path}")

                    downloaded_files.append({
                        'original_url': url,
                        'local_path': result['local_path'],
                        'relative_path': relative_path,
                        'file_type': file_type,
                        'category': category,
                        'file_size': result['file_size'],
                        'file_hash': result['file_hash']
                    })
                elif result.get('skipped'):
                    # 跳过的网页文件，记录但不添加到映射中
                    self.logger.info(f"跳过网页文件: {url} - {result['error']}")
                    downloaded_files.append({
                        'original_url': url,
                        'local_path': None,
                        'relative_path': None,
                        'file_type': file_type,
                        'category': category,
                        'status': 'skipped',
                        'reason': result['error']
                    })
                else:
                    self.logger.warning(f"下载失败: {url} - {result['error']}")
            
            # 生成本地化配置
            localized_config = self._replace_urls_in_config(config, url_mappings, interface_id, interface.name)

            # 保存本地化配置
            config_path = interface_dir / "api.json"
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(localized_config, f, ensure_ascii=False, indent=2)
            
            return {
                'success': True,
                'message': f'本地化完成，共处理 {len(downloaded_files)} 个文件',
                'total_files': len(urls_to_download),
                'successful_files': len(downloaded_files),
                'local_dir': str(interface_dir),
                'files': downloaded_files,
                'config_path': str(config_path)
            }
            
        except Exception as e:
            self.logger.error(f"本地化失败: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def _replace_urls_in_config(self, config: dict, url_mappings: dict, interface_id: int, interface_name: str) -> dict:
        """在配置中替换URL为本地路径"""
        # 深拷贝配置
        import copy
        localized_config = copy.deepcopy(config)

        # 生成接口目录名（与前端保持一致）
        safe_name = re.sub(r'[^\w\-_\.]', '_', interface_name)
        interface_dir_name = f"interface_{interface_id}_{safe_name}"

        # 替换spider
        if 'spider' in localized_config and localized_config['spider'] in url_mappings:
            relative_path = url_mappings[localized_config['spider']]
            # 转换为从/localized/开始的绝对路径
            full_path = f"/localized/{interface_dir_name}/{relative_path.lstrip('./')}"
            localized_config['spider'] = full_path
            self.logger.info(f"🐾 Spider路径转换: {relative_path} -> {full_path}")

        # 替换sites中的URL
        if 'sites' in localized_config:
            for site in localized_config['sites']:
                # api字段
                if 'api' in site and site['api'] in url_mappings:
                    relative_path = url_mappings[site['api']]
                    full_path = f"/localized/{interface_dir_name}/{relative_path.lstrip('./')}"
                    site['api'] = full_path
                    self.logger.info(f"🐾 Site API路径转换: {relative_path} -> {full_path}")

                # ext字段
                if 'ext' in site:
                    if isinstance(site['ext'], str) and site['ext'] in url_mappings:
                        relative_path = url_mappings[site['ext']]
                        full_path = f"/localized/{interface_dir_name}/{relative_path.lstrip('./')}"
                        site['ext'] = full_path
                        self.logger.info(f"🐾 Site Ext路径转换: {relative_path} -> {full_path}")
                    elif isinstance(site['ext'], dict):
                        for key, value in site['ext'].items():
                            if isinstance(value, str) and value in url_mappings:
                                relative_path = url_mappings[value]
                                full_path = f"/localized/{interface_dir_name}/{relative_path.lstrip('./')}"
                                site['ext'][key] = full_path
                                self.logger.info(f"🐾 Site Ext[{key}]路径转换: {relative_path} -> {full_path}")

                # jar字段 - 需要处理包含MD5的情况
                if 'jar' in site:
                    jar_full_value = site['jar']
                    # 优先查找完整值的映射（包含MD5）
                    if jar_full_value in url_mappings:
                        relative_path = url_mappings[jar_full_value]
                        full_path = f"/localized/{interface_dir_name}/{relative_path.lstrip('./')}"
                        site['jar'] = full_path  # 直接替换为本地路径，不保留MD5
                        self.logger.info(f"🐾 Site Jar路径转换(完整值): {jar_full_value} -> {full_path}")
                    # 如果完整值没找到，尝试查找纯URL（不含MD5）
                    elif ';md5;' in jar_full_value:
                        jar_url = jar_full_value.split(';')[0]  # 获取URL部分
                        if jar_url in url_mappings:
                            relative_path = url_mappings[jar_url]
                            full_path = f"/localized/{interface_dir_name}/{relative_path.lstrip('./')}"
                            site['jar'] = full_path  # 直接替换为本地路径，不保留MD5
                            self.logger.info(f"🐾 Site Jar路径转换(纯URL): {jar_url} -> {full_path}")

        # 替换lives中的URL
        if 'lives' in localized_config:
            for live in localized_config['lives']:
                if 'url' in live and live['url'] in url_mappings:
                    relative_path = url_mappings[live['url']]
                    full_path = f"/localized/{interface_dir_name}/{relative_path.lstrip('./')}"
                    live['url'] = full_path
                    self.logger.info(f"🐾 Live URL路径转换: {relative_path} -> {full_path}")

        return localized_config

    def get_localized_config(self, interface_id: int, interface_name: str) -> Optional[dict]:
        """获取本地化配置"""
        try:
            interface_dir = self.get_interface_dir(interface_id, interface_name)
            config_path = interface_dir / "api.json"

            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return None
        except Exception as e:
            self.logger.error(f"读取本地化配置失败: {str(e)}")
            return None

    def get_localized_files(self, interface_id: int, interface_name: str) -> List[Dict]:
        """获取本地化文件列表"""
        try:
            interface_dir = self.get_interface_dir(interface_id, interface_name)
            if not interface_dir.exists():
                return []

            files = []
            for category in ['spider', 'js', 'json', 'jar', 'live']:
                category_dir = interface_dir / category
                if category_dir.exists():
                    for file_path in category_dir.iterdir():
                        if file_path.is_file():
                            stat = file_path.stat()
                            files.append({
                                'file_type': category,  # 前端期望的字段名
                                'original_filename': file_path.name,  # 前端期望的字段名
                                'download_status': 'completed',  # 前端期望的字段名，本地文件都是已完成状态
                                'file_size': stat.st_size,  # 前端期望的字段名
                                'local_path': str(file_path),  # 本地路径
                                'relative_path': f"./{category}/{file_path.name}",  # 相对路径
                                'modified_time': datetime.fromtimestamp(stat.st_mtime).isoformat()  # 修改时间
                            })

            return files
        except Exception as e:
            self.logger.error(f"获取本地化文件列表失败: {str(e)}")
            return []

    def delete_localized_files(self, interface_id: int, interface_name: str) -> Dict:
        """删除本地化文件"""
        try:
            interface_dir = self.get_interface_dir(interface_id, interface_name)
            if interface_dir.exists():
                import shutil
                shutil.rmtree(interface_dir)
                return {'success': True, 'message': '本地化文件已删除'}
            else:
                return {'success': True, 'message': '没有本地化文件需要删除'}
        except Exception as e:
            self.logger.error(f"删除本地化文件失败: {str(e)}")
            return {'success': False, 'error': str(e)}

    def get_localization_status(self, interface_id: int, interface_name: str) -> Dict:
        """获取本地化状态"""
        try:
            interface_dir = self.get_interface_dir(interface_id, interface_name)
            config_path = interface_dir / "api.json"

            if not interface_dir.exists():
                return {
                    'enabled': False,
                    'status': 'not_localized',
                    'files_count': 0,
                    'total_size': 0
                }

            files = self.get_localized_files(interface_id, interface_name)
            total_size = sum(f['file_size'] for f in files)

            return {
                'enabled': True,
                'status': 'completed' if config_path.exists() else 'partial',
                'files_count': len(files),
                'total_size': total_size,
                'files': files,
                'local_dir': str(interface_dir)
            }
        except Exception as e:
            self.logger.error(f"获取本地化状态失败: {str(e)}")
            return {
                'enabled': False,
                'status': 'error',
                'error': str(e)
            }
