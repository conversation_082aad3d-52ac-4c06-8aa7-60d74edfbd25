{"version": 3, "file": "position.js", "sources": ["../../../../../packages/utils/dom/position.ts"], "sourcesContent": ["import { isClient } from '../browser'\n\nexport const isInContainer = (\n  el?: Element,\n  container?: Element | Window\n): boolean => {\n  if (!isClient || !el || !container) return false\n\n  const elRect = el.getBoundingClientRect()\n\n  let containerRect: Pick<DOMRect, 'top' | 'bottom' | 'left' | 'right'>\n  if (container instanceof Element) {\n    containerRect = container.getBoundingClientRect()\n  } else {\n    containerRect = {\n      top: 0,\n      right: window.innerWidth,\n      bottom: window.innerHeight,\n      left: 0,\n    }\n  }\n  return (\n    elRect.top < containerRect.bottom &&\n    elRect.bottom > containerRect.top &&\n    elRect.right > containerRect.left &&\n    elRect.left < containerRect.right\n  )\n}\n\nexport const getOffsetTop = (el: HTMLElement) => {\n  let offset = 0\n  let parent = el\n\n  while (parent) {\n    offset += parent.offsetTop\n    parent = parent.offsetParent as HTMLElement\n  }\n\n  return offset\n}\n\nexport const getOffsetTopDistance = (\n  el: HTMLElement,\n  containerEl: HTMLElement\n) => {\n  return Math.abs(getOffsetTop(el) - getOffsetTop(containerEl))\n}\n\nexport const getClientXY = (event: MouseEvent | TouchEvent) => {\n  let clientX: number\n  let clientY: number\n  if (event.type === 'touchend') {\n    clientY = (event as TouchEvent).changedTouches[0].clientY\n    clientX = (event as TouchEvent).changedTouches[0].clientX\n  } else if (event.type.startsWith('touch')) {\n    clientY = (event as TouchEvent).touches[0].clientY\n    clientX = (event as TouchEvent).touches[0].clientX\n  } else {\n    clientY = (event as MouseEvent).clientY\n    clientX = (event as MouseEvent).clientX\n  }\n  return {\n    clientX,\n    clientY,\n  }\n}\n"], "names": ["isClient"], "mappings": ";;;;;;AACY,MAAC,aAAa,GAAG,CAAC,EAAE,EAAE,SAAS,KAAK;AAChD,EAAE,IAAI,CAACA,aAAQ,IAAI,CAAC,EAAE,IAAI,CAAC,SAAS;AACpC,IAAI,OAAO,KAAK,CAAC;AACjB,EAAE,MAAM,MAAM,GAAG,EAAE,CAAC,qBAAqB,EAAE,CAAC;AAC5C,EAAE,IAAI,aAAa,CAAC;AACpB,EAAE,IAAI,SAAS,YAAY,OAAO,EAAE;AACpC,IAAI,aAAa,GAAG,SAAS,CAAC,qBAAqB,EAAE,CAAC;AACtD,GAAG,MAAM;AACT,IAAI,aAAa,GAAG;AACpB,MAAM,GAAG,EAAE,CAAC;AACZ,MAAM,KAAK,EAAE,MAAM,CAAC,UAAU;AAC9B,MAAM,MAAM,EAAE,MAAM,CAAC,WAAW;AAChC,MAAM,IAAI,EAAE,CAAC;AACb,KAAK,CAAC;AACN,GAAG;AACH,EAAE,OAAO,MAAM,CAAC,GAAG,GAAG,aAAa,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,aAAa,CAAC,GAAG,IAAI,MAAM,CAAC,KAAK,GAAG,aAAa,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,GAAG,aAAa,CAAC,KAAK,CAAC;AAC1J,EAAE;AACU,MAAC,YAAY,GAAG,CAAC,EAAE,KAAK;AACpC,EAAE,IAAI,MAAM,GAAG,CAAC,CAAC;AACjB,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC;AAClB,EAAE,OAAO,MAAM,EAAE;AACjB,IAAI,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC;AAC/B,IAAI,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC;AACjC,GAAG;AACH,EAAE,OAAO,MAAM,CAAC;AAChB,EAAE;AACU,MAAC,oBAAoB,GAAG,CAAC,EAAE,EAAE,WAAW,KAAK;AACzD,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC;AAChE,EAAE;AACU,MAAC,WAAW,GAAG,CAAC,KAAK,KAAK;AACtC,EAAE,IAAI,OAAO,CAAC;AACd,EAAE,IAAI,OAAO,CAAC;AACd,EAAE,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE;AACjC,IAAI,OAAO,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;AAC9C,IAAI,OAAO,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;AAC9C,GAAG,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;AAC7C,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;AACvC,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;AACvC,GAAG,MAAM;AACT,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;AAC5B,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;AAC5B,GAAG;AACH,EAAE,OAAO;AACT,IAAI,OAAO;AACX,IAAI,OAAO;AACX,GAAG,CAAC;AACJ;;;;;;;"}