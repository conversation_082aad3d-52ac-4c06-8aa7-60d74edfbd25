{"version": 3, "file": "km.mjs", "sources": ["../../../../../packages/locale/lang/km.ts"], "sourcesContent": ["export default {\n  name: 'km',\n  el: {\n    breadcrumb: {\n      label: 'ទីតាំងនៃទំព័របច្ចុប្បន្ន',\n    },\n    colorpicker: {\n      confirm: 'យល់ព្រម',\n      clear: 'លុបចោល',\n      defaultLabel: 'ឧបករណ៍ជ្រើសរើសពណ៌',\n      description:\n        '{color} ជាពណ៌បច្ចុប្បន្ន។ សូមចុច Enter ដើម្បីជ្រើសរើសពណ៌ថ្មី',\n      alphaLabel: 'ជ្រើសរើសភាពស្រអាប់',\n    },\n    datepicker: {\n      now: 'ឥឡូវនេះ',\n      today: 'ថ្ងៃនេះ',\n      cancel: 'បោះបង់',\n      clear: 'លុបចោល',\n      confirm: 'យល់ព្រម',\n      dateTablePrompt:\n        'ប្រើគ្រាប់ចុចដើម្បីរំកិល និងចុច Enter ដើម្បីជ្រើសរើសកាលបរិច្ឆេទ',\n      monthTablePrompt:\n        'ប្រើគ្រាប់ចុចដើម្បីរំកិល និងចុច Enter ដើម្បីជ្រើសរើសខែ',\n      yearTablePrompt:\n        'ប្រើគ្រាប់ចុចដើម្បីរំកិល និងចុច Enter ដើម្បីជ្រើសរើសឆ្នាំ',\n      selectedDate: 'កាលបរិច្ឆេទដែលបានជ្រើសរើស',\n      selectDate: 'ជ្រើសរើសកាលបរិច្ឆេទ',\n      selectTime: 'ជ្រើសរើសម៉ោង',\n      startDate: 'កាលបរិច្ឆេទចាប់ផ្តើម',\n      startTime: 'ម៉ោងចាប់ផ្តើម',\n      endDate: 'កាលបរិច្ឆេទបញ្ចប់',\n      endTime: 'ម៉ោងបញ្ចប់',\n      prevYear: 'ឆ្នាំមុន',\n      nextYear: 'ឆ្នាំក្រោយ',\n      prevMonth: 'ខែមុន',\n      nextMonth: 'ខែក្រោយ',\n      year: 'ឆ្នាំ',\n      month1: 'ខែមករា',\n      month2: 'ខែកុម្ភៈ',\n      month3: 'ខែមីនា',\n      month4: 'ខែមេសា',\n      month5: 'ខែឧសភា',\n      month6: 'ខែមិថុនា',\n      month7: 'ខែកក្កដា',\n      month8: 'ខែសីហា',\n      month9: 'ខែកញ្ញា',\n      month10: 'ខែតុលា',\n      month11: 'ខែវិច្ឆិកា',\n      month12: 'ខែធ្នូ',\n      // week: 'សប្តាហ៍',\n      weeks: {\n        sun: 'អាទិត្យ',\n        mon: 'ច័ន្ទ',\n        tue: 'អង្គារ',\n        wed: 'ពុធ',\n        thu: 'ព្រហស្បតិ៍',\n        fri: 'សុក្រ',\n        sat: 'សៅរ៍',\n      },\n      weeksFull: {\n        sun: 'ថ្ងៃអាទិត្យ',\n        mon: 'ថ្ងៃច័ន្ទ',\n        tue: 'ថ្ងៃអង្គារ',\n        wed: 'ថ្ងៃពុធ',\n        thu: 'ថ្ងៃព្រហស្បតិ៍',\n        fri: 'ថ្ងៃសុក្រ',\n        sat: 'ថ្ងៃសៅរ៍',\n      },\n      months: {\n        jan: 'មករា',\n        feb: 'កុម្ភៈ',\n        mar: 'មីនា',\n        apr: 'មេសា',\n        may: 'ឧសភា',\n        jun: 'មិថុនា',\n        jul: 'កក្កដា',\n        aug: 'សីហា',\n        sep: 'កញ្ញា',\n        oct: 'តុលា',\n        nov: 'វិច្ឆិកា',\n        dec: 'ធ្នូ',\n      },\n    },\n    inputNumber: {\n      decrease: 'បន្ថយតម្លៃ',\n      increase: 'បង្កើនតម្លៃ',\n    },\n    select: {\n      loading: 'កំពុងដំណើរការ',\n      noMatch: 'គ្មានទិន្នន័យដែលត្រូវគ្នា',\n      noData: 'គ្មានទិន្នន័យ',\n      placeholder: 'សូមជ្រើសរើស',\n    },\n    dropdown: {\n      toggleDropdown: 'បើកបិទផ្ទាំងជម្រើស',\n    },\n    mention: {\n      loading: 'កំពុងដំណើរការ',\n    },\n    cascader: {\n      noMatch: 'គ្មានទិន្នន័យដែលត្រូវគ្នា',\n      loading: 'កំពុងដំណើរការ',\n      placeholder: 'សូមជ្រើសរើស',\n      noData: 'គ្មានទិន្នន័យ',\n    },\n    pagination: {\n      goto: 'ទៅកាន់',\n      pagesize: '/ទំព័រ',\n      total: 'សរុប {total}',\n      pageClassifier: 'ទំព័រ',\n      page: 'ទំព័រ',\n      prev: 'មុន',\n      next: 'បន្ទាប់',\n      currentPage: 'ទំព័រទី {pager}',\n      prevPages: 'ទៅមុខ {pager} ទំព័រ',\n      nextPages: 'ថយក្រោយ {pager} ទំព័រ',\n      deprecationWarning:\n        'អ្នកបានប្រើប្រាស់របស់ដែលបានផ្អាកឈប់ប្រើ សូមចូលទៅកាន់ឯកសារផ្លូវការរបស់ el-pagination សម្រាប់ព័ត៌មានបន្ថែម',\n    },\n    dialog: {\n      close: 'បិទ',\n    },\n    drawer: {\n      close: 'បិទ',\n    },\n    messagebox: {\n      title: 'ជំនួយ',\n      confirm: 'យល់ព្រម',\n      cancel: 'បោះបង់',\n      error: 'បញ្ចូលទិន្នន័យមិនត្រឹមត្រូវ!',\n      close: 'បិទប្រអប់សារ',\n    },\n    upload: {\n      deleteTip: 'ចុច Delete ដើម្បីលុបចេញ',\n      delete: 'លុប',\n      preview: 'មើលជាមុនសិន',\n      continue: 'បន្តបញ្ជូន',\n    },\n    slider: {\n      defaultLabel: 'ស្លាយចាប់ពី {min} ដល់ {max}',\n      defaultRangeStartLabel: 'ជ្រើសរើសតម្លៃចាប់ផ្តើម',\n      defaultRangeEndLabel: 'ជ្រើសរើសតម្លៃបញ្ចប់',\n    },\n    table: {\n      emptyText: 'គ្មានទិន្នន័យ',\n      confirmFilter: 'យល់ព្រម',\n      resetFilter: 'កំណត់ឡើងវិញ',\n      clearFilter: 'ទាំងអស់',\n      sumText: 'សរុប',\n    },\n    tour: {\n      next: 'បន្ទាប់',\n      previous: 'ថយក្រោយ',\n      finish: 'បញ្ចប់ការណែនាំ',\n    },\n    tree: {\n      emptyText: 'គ្មានទិន្នន័យ',\n    },\n    transfer: {\n      noMatch: 'គ្មានទិន្នន័យដែលត្រូវគ្នា',\n      noData: 'គ្មានទិន្នន័យ',\n      titles: ['បញ្ជីទី១', 'បញ្ជីទី២'],\n      filterPlaceholder: 'សូមបញ្ចូលពាក្យគន្លឹះដើម្បីស្វែងរក',\n      noCheckedFormat: 'ចំនួនសរុប {total}',\n      hasCheckedFormat: 'ធាតុដែលបានជ្រើស {checked}/{total}',\n    },\n    image: {\n      error: 'ការទាញយកបរាជ័យ',\n    },\n    pageHeader: {\n      title: 'ត្រឡប់ក្រោយ',\n    },\n    popconfirm: {\n      confirmButtonText: 'យល់ព្រម',\n      cancelButtonText: 'បោះបង់',\n    },\n    carousel: {\n      leftArrow: 'ស្លាយមុន',\n      rightArrow: 'ស្លាយបន្ទាប់',\n      indicator: 'ប្តូរទៅស្លាយទី {index}',\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,kJAAkJ;AAC/J,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,KAAK,EAAE,sCAAsC;AACnD,MAAM,YAAY,EAAE,wGAAwG;AAC5H,MAAM,WAAW,EAAE,0RAA0R;AAC7S,MAAM,UAAU,EAAE,8GAA8G;AAChI,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,4CAA4C;AACvD,MAAM,KAAK,EAAE,4CAA4C;AACzD,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,KAAK,EAAE,sCAAsC;AACnD,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,eAAe,EAAE,oVAAoV;AAC3W,MAAM,gBAAgB,EAAE,8RAA8R;AACtT,MAAM,eAAe,EAAE,gTAAgT;AACvU,MAAM,YAAY,EAAE,wJAAwJ;AAC5K,MAAM,UAAU,EAAE,oHAAoH;AACtI,MAAM,UAAU,EAAE,0EAA0E;AAC5F,MAAM,SAAS,EAAE,0HAA0H;AAC3I,MAAM,SAAS,EAAE,gFAAgF;AACjG,MAAM,OAAO,EAAE,wGAAwG;AACvH,MAAM,OAAO,EAAE,8DAA8D;AAC7E,MAAM,QAAQ,EAAE,kDAAkD;AAClE,MAAM,QAAQ,EAAE,8DAA8D;AAC9E,MAAM,SAAS,EAAE,gCAAgC;AACjD,MAAM,SAAS,EAAE,4CAA4C;AAC7D,MAAM,IAAI,EAAE,gCAAgC;AAC5C,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,kDAAkD;AAChE,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,kDAAkD;AAChE,MAAM,MAAM,EAAE,kDAAkD;AAChE,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,OAAO,EAAE,sCAAsC;AACrD,MAAM,OAAO,EAAE,8DAA8D;AAC7E,MAAM,OAAO,EAAE,sCAAsC;AACrD,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,4CAA4C;AACzD,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,8DAA8D;AAC3E,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,0BAA0B;AACvC,OAAO;AACP,MAAM,SAAS,EAAE;AACjB,QAAQ,GAAG,EAAE,oEAAoE;AACjF,QAAQ,GAAG,EAAE,wDAAwD;AACrE,QAAQ,GAAG,EAAE,8DAA8D;AAC3E,QAAQ,GAAG,EAAE,4CAA4C;AACzD,QAAQ,GAAG,EAAE,sFAAsF;AACnG,QAAQ,GAAG,EAAE,wDAAwD;AACrE,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,0BAA0B;AACvC,OAAO;AACP,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,QAAQ,EAAE,8DAA8D;AAC9E,MAAM,QAAQ,EAAE,oEAAoE;AACpF,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,gFAAgF;AAC/F,MAAM,OAAO,EAAE,wJAAwJ;AACvK,MAAM,MAAM,EAAE,gFAAgF;AAC9F,MAAM,WAAW,EAAE,oEAAoE;AACvF,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,cAAc,EAAE,8GAA8G;AACpI,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,gFAAgF;AAC/F,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,wJAAwJ;AACvK,MAAM,OAAO,EAAE,gFAAgF;AAC/F,MAAM,WAAW,EAAE,oEAAoE;AACvF,MAAM,MAAM,EAAE,gFAAgF;AAC9F,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,sCAAsC;AAClD,MAAM,QAAQ,EAAE,iCAAiC;AACjD,MAAM,KAAK,EAAE,kCAAkC;AAC/C,MAAM,cAAc,EAAE,gCAAgC;AACtD,MAAM,IAAI,EAAE,gCAAgC;AAC5C,MAAM,IAAI,EAAE,oBAAoB;AAChC,MAAM,IAAI,EAAE,4CAA4C;AACxD,MAAM,WAAW,EAAE,oDAAoD;AACvE,MAAM,SAAS,EAAE,uEAAuE;AACxF,MAAM,SAAS,EAAE,mFAAmF;AACpG,MAAM,kBAAkB,EAAE,kiBAAkiB;AAC5jB,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,oBAAoB;AACjC,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,oBAAoB;AACjC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,gCAAgC;AAC7C,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,KAAK,EAAE,qKAAqK;AAClL,MAAM,KAAK,EAAE,0EAA0E;AACvF,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,oGAAoG;AACrH,MAAM,MAAM,EAAE,oBAAoB;AAClC,MAAM,OAAO,EAAE,oEAAoE;AACnF,MAAM,QAAQ,EAAE,8DAA8D;AAC9E,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,YAAY,EAAE,mGAAmG;AACvH,MAAM,sBAAsB,EAAE,sIAAsI;AACpK,MAAM,oBAAoB,EAAE,oHAAoH;AAChJ,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,gFAAgF;AACjG,MAAM,aAAa,EAAE,4CAA4C;AACjE,MAAM,WAAW,EAAE,oEAAoE;AACvF,MAAM,WAAW,EAAE,4CAA4C;AAC/D,MAAM,OAAO,EAAE,0BAA0B;AACzC,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,4CAA4C;AACxD,MAAM,QAAQ,EAAE,4CAA4C;AAC5D,MAAM,MAAM,EAAE,sFAAsF;AACpG,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,gFAAgF;AACjG,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,wJAAwJ;AACvK,MAAM,MAAM,EAAE,gFAAgF;AAC9F,MAAM,MAAM,EAAE,CAAC,kDAAkD,EAAE,kDAAkD,CAAC;AACtH,MAAM,iBAAiB,EAAE,wMAAwM;AACjO,MAAM,eAAe,EAAE,gEAAgE;AACvF,MAAM,gBAAgB,EAAE,8GAA8G;AACtI,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,sFAAsF;AACnG,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,oEAAoE;AACjF,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,4CAA4C;AACrE,MAAM,gBAAgB,EAAE,sCAAsC;AAC9D,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,kDAAkD;AACnE,MAAM,UAAU,EAAE,0EAA0E;AAC5F,MAAM,SAAS,EAAE,8FAA8F;AAC/G,KAAK;AACL,GAAG;AACH,CAAC;;;;"}