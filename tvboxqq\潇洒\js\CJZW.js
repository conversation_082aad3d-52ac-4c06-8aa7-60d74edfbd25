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<PERSON><PERSON><PERSON>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