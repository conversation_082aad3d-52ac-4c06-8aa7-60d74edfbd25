{"version": 3, "file": "tr.min.mjs", "sources": ["../../../../packages/locale/lang/tr.ts"], "sourcesContent": ["export default {\n  name: 'tr',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: '<PERSON><PERSON><PERSON>',\n      clear: '<PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: '<PERSON><PERSON><PERSON>',\n      today: '<PERSON><PERSON><PERSON><PERSON>',\n      cancel: '<PERSON>pta<PERSON>',\n      clear: 'Temizle',\n      confirm: '<PERSON><PERSON><PERSON>',\n      selectDate: '<PERSON><PERSON><PERSON> seç',\n      selectTime: 'Saat seç',\n      startDate: 'Başlangıç Tarihi',\n      startTime: '<PERSON>şlangıç Saati',\n      endDate: 'Bitiş Tarihi',\n      endTime: '<PERSON><PERSON><PERSON> Saati',\n      prevYear: 'Önceki Yıl',\n      nextYear: '<PERSON><PERSON><PERSON> Yıl',\n      prevMonth: 'Önceki Ay',\n      nextMonth: '<PERSON>rak<PERSON> Ay',\n      year: '',\n      month1: 'Ocak',\n      month2: 'Şubat',\n      month3: 'Mart',\n      month4: 'Nisan',\n      month5: 'Mayıs',\n      month6: 'Haziran',\n      month7: 'Temmuz',\n      month8: 'Ağust<PERSON>',\n      month9: '<PERSON><PERSON><PERSON><PERSON>',\n      month10: '<PERSON><PERSON>',\n      month11: 'Kasım',\n      month12: 'Ara<PERSON><PERSON><PERSON>',\n      // week: 'week',\n      weeks: {\n        sun: 'Paz',\n        mon: 'Pzt',\n        tue: 'Sal',\n        wed: 'Çar',\n        thu: 'Per',\n        fri: 'Cum',\n        sat: 'Cmt',\n      },\n      months: {\n        jan: 'Oca',\n        feb: 'Şub',\n        mar: 'Mar',\n        apr: 'Nis',\n        may: 'May',\n        jun: 'Haz',\n        jul: 'Tem',\n        aug: 'Ağu',\n        sep: 'Eyl',\n        oct: 'Eki',\n        nov: 'Kas',\n        dec: 'Ara',\n      },\n    },\n    select: {\n      loading: 'Yükleniyor',\n      noMatch: 'Eşleşen veri bulunamadı',\n      noData: 'Veri yok',\n      placeholder: 'Seç',\n    },\n    mention: {\n      loading: 'Yükleniyor',\n    },\n    cascader: {\n      noMatch: 'Eşleşen veri bulunamadı',\n      loading: 'Yükleniyor',\n      placeholder: 'Seç',\n      noData: 'Veri yok',\n    },\n    pagination: {\n      goto: 'Git',\n      pagesize: '/sayfa',\n      total: 'Toplam {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Mesaj',\n      confirm: 'Onayla',\n      cancel: 'İptal',\n      error: 'İllegal giriş',\n    },\n    upload: {\n      deleteTip: 'kaldırmak için delete tuşuna bas',\n      delete: 'Sil',\n      preview: 'Görüntüle',\n      continue: 'Devam',\n    },\n    table: {\n      emptyText: 'Veri yok',\n      confirmFilter: 'Onayla',\n      resetFilter: 'Sıfırla',\n      clearFilter: 'Hepsi',\n      sumText: 'Sum',\n    },\n    tree: {\n      emptyText: 'Veri yok',\n    },\n    transfer: {\n      noMatch: 'Eşleşen veri bulunamadı',\n      noData: 'Veri yok',\n      titles: ['Liste 1', 'Liste 2'],\n      filterPlaceholder: 'Anahtar kelimeleri gir',\n      noCheckedFormat: '{total} adet',\n      hasCheckedFormat: '{checked}/{total} seçildi',\n    },\n    image: {\n      error: 'BAŞARISIZ OLDU',\n    },\n    pageHeader: {\n      title: 'Geri',\n    },\n    popconfirm: {\n      confirmButtonText: 'Evet',\n      cancelButtonText: 'Hayır',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;AAAA,SAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,aAAa,CAAC,SAAS,CAAC,+BAA+B,CAAC,SAAS,CAAC,8BAA8B,CAAC,OAAO,CAAC,mBAAmB,CAAC,OAAO,CAAC,kBAAkB,CAAC,QAAQ,CAAC,oBAAoB,CAAC,QAAQ,CAAC,kBAAkB,CAAC,SAAS,CAAC,cAAc,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,wCAAwC,CAAC,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,wCAAwC,CAAC,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,+CAA+C,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,oBAAoB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAC,WAAW,CAAC,mBAAmB,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,wCAAwC,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,iBAAiB,CAAC,wBAAwB,CAAC,eAAe,CAAC,cAAc,CAAC,gBAAgB,CAAC,8BAA8B,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;"}