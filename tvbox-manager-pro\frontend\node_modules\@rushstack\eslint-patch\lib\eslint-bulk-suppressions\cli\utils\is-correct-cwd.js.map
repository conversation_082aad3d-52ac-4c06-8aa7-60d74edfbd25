{"version": 3, "file": "is-correct-cwd.js", "sourceRoot": "", "sources": ["../../../../src/eslint-bulk-suppressions/cli/utils/is-correct-cwd.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;AAI3D,oCAQC;AAVD,4CAAoB;AAEpB,SAAgB,YAAY,CAAC,GAAW;IACtC,OAAO,CACL,YAAE,CAAC,UAAU,CAAC,GAAG,GAAG,mBAAmB,CAAC;QACxC,YAAE,CAAC,UAAU,CAAC,GAAG,GAAG,oBAAoB,CAAC;QACzC,YAAE,CAAC,UAAU,CAAC,GAAG,GAAG,oBAAoB,CAAC;QACzC,YAAE,CAAC,UAAU,CAAC,GAAG,GAAG,eAAe,CAAC;QACpC,YAAE,CAAC,UAAU,CAAC,GAAG,GAAG,gBAAgB,CAAC,CACtC,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nimport fs from 'fs';\n\nexport function isCorrectCwd(cwd: string): boolean {\n  return (\n    fs.existsSync(`${cwd}/eslint.config.js`) ||\n    fs.existsSync(`${cwd}/eslint.config.cjs`) ||\n    fs.existsSync(`${cwd}/eslint.config.mjs`) ||\n    fs.existsSync(`${cwd}/.eslintrc.js`) ||\n    fs.existsSync(`${cwd}/.eslintrc.cjs`)\n  );\n}\n"]}