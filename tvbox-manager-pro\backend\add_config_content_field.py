#!/usr/bin/env python3
"""
添加 config_content 字段到 interface_sources 表
"""
import sqlite3
import os

def add_config_content_field():
    db_path = './data/tvbox.db'
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 检查字段是否已存在
        cursor.execute("PRAGMA table_info(interface_sources)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        if 'config_content' in column_names:
            print("config_content 字段已存在")
            return
        
        # 添加字段
        cursor.execute("ALTER TABLE interface_sources ADD COLUMN config_content TEXT")
        conn.commit()
        print("成功添加 config_content 字段")
        
        # 验证字段已添加
        cursor.execute("PRAGMA table_info(interface_sources)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        print("当前字段:", column_names)
        
    except Exception as e:
        print(f"添加字段失败: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    add_config_content_field()
