#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查数据库结构
"""

import sqlite3
import os

def get_db_path():
    """获取数据库路径"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    db_path = os.path.join(current_dir, "data", "tvbox.db")
    return db_path

def check_database_structure():
    """检查数据库结构"""
    db_path = get_db_path()
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print("数据库中的表:")
        for table in tables:
            table_name = table[0]
            print(f"  - {table_name}")
            
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            print(f"    列信息:")
            for col in columns:
                print(f"      {col[1]} ({col[2]})")
            print()
        
    except Exception as e:
        print(f"检查数据库失败: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    check_database_structure()
