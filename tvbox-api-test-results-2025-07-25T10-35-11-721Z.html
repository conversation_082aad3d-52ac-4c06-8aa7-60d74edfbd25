
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TVBox API 测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .stats { display: flex; justify-content: space-around; margin-bottom: 30px; }
        .stat-card { text-align: center; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .stat-number { font-size: 2em; font-weight: bold; }
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .success { color: green; }
        .failed { color: red; }
    </style>
</head>
<body>
    <div class="header">
        <h1>TVBox API 测试报告</h1>
        <p>生成时间: 2025/7/25 18:35:11</p>
    </div>
    
    <div class="stats">
        <div class="stat-card">
            <div class="stat-number">28</div>
            <div>总测试数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number success">8</div>
            <div>成功</div>
        </div>
        <div class="stat-card">
            <div class="stat-number failed">20</div>
            <div>失败</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">29%</div>
            <div>成功率</div>
        </div>
    </div>
    
    <table>
        <thead>
            <tr>
                <th>时间</th>
                <th>分类</th>
                <th>API名称</th>
                <th>方法</th>
                <th>状态</th>
                <th>响应时间</th>
            </tr>
        </thead>
        <tbody>
        
                <tr>
                    <td>Invalid Date</td>
                    <td>认证API</td>
                    <td>用户登录</td>
                    <td>POST</td>
                    <td class="success">成功</td>
                    <td>594ms</td>
                </tr>
            
                <tr>
                    <td>Invalid Date</td>
                    <td>认证API</td>
                    <td>用户注册</td>
                    <td>POST</td>
                    <td class="failed">失败</td>
                    <td>8ms</td>
                </tr>
            
                <tr>
                    <td>Invalid Date</td>
                    <td>认证API</td>
                    <td>获取当前用户</td>
                    <td>GET</td>
                    <td class="failed">失败</td>
                    <td>6ms</td>
                </tr>
            
                <tr>
                    <td>Invalid Date</td>
                    <td>认证API</td>
                    <td>刷新令牌</td>
                    <td>POST</td>
                    <td class="failed">失败</td>
                    <td>7ms</td>
                </tr>
            
                <tr>
                    <td>Invalid Date</td>
                    <td>接口管理</td>
                    <td>获取接口列表</td>
                    <td>GET</td>
                    <td class="success">成功</td>
                    <td>9ms</td>
                </tr>
            
                <tr>
                    <td>Invalid Date</td>
                    <td>接口管理</td>
                    <td>创建接口</td>
                    <td>POST</td>
                    <td class="failed">失败</td>
                    <td>9ms</td>
                </tr>
            
                <tr>
                    <td>Invalid Date</td>
                    <td>接口管理</td>
                    <td>获取接口详情</td>
                    <td>GET</td>
                    <td class="success">成功</td>
                    <td>10ms</td>
                </tr>
            
                <tr>
                    <td>Invalid Date</td>
                    <td>接口管理</td>
                    <td>更新接口</td>
                    <td>PUT</td>
                    <td class="failed">失败</td>
                    <td>13ms</td>
                </tr>
            
                <tr>
                    <td>Invalid Date</td>
                    <td>接口管理</td>
                    <td>删除接口</td>
                    <td>DELETE</td>
                    <td class="success">成功</td>
                    <td>19ms</td>
                </tr>
            
                <tr>
                    <td>Invalid Date</td>
                    <td>接口管理</td>
                    <td>测试接口</td>
                    <td>POST</td>
                    <td class="failed">失败</td>
                    <td>10ms</td>
                </tr>
            
                <tr>
                    <td>Invalid Date</td>
                    <td>接口管理</td>
                    <td>刷新接口</td>
                    <td>POST</td>
                    <td class="failed">失败</td>
                    <td>10ms</td>
                </tr>
            
                <tr>
                    <td>Invalid Date</td>
                    <td>订阅管理</td>
                    <td>获取订阅列表</td>
                    <td>GET</td>
                    <td class="failed">失败</td>
                    <td>7ms</td>
                </tr>
            
                <tr>
                    <td>Invalid Date</td>
                    <td>订阅管理</td>
                    <td>创建订阅</td>
                    <td>POST</td>
                    <td class="failed">失败</td>
                    <td>6ms</td>
                </tr>
            
                <tr>
                    <td>Invalid Date</td>
                    <td>订阅管理</td>
                    <td>获取订阅详情</td>
                    <td>GET</td>
                    <td class="failed">失败</td>
                    <td>10ms</td>
                </tr>
            
                <tr>
                    <td>Invalid Date</td>
                    <td>订阅管理</td>
                    <td>获取订阅配置</td>
                    <td>GET</td>
                    <td class="failed">失败</td>
                    <td>4ms</td>
                </tr>
            
                <tr>
                    <td>Invalid Date</td>
                    <td>系统管理</td>
                    <td>系统统计</td>
                    <td>GET</td>
                    <td class="failed">失败</td>
                    <td>12ms</td>
                </tr>
            
                <tr>
                    <td>Invalid Date</td>
                    <td>系统管理</td>
                    <td>系统设置</td>
                    <td>GET</td>
                    <td class="failed">失败</td>
                    <td>9ms</td>
                </tr>
            
                <tr>
                    <td>Invalid Date</td>
                    <td>解密API</td>
                    <td>URL解密</td>
                    <td>POST</td>
                    <td class="failed">失败</td>
                    <td>8ms</td>
                </tr>
            
                <tr>
                    <td>Invalid Date</td>
                    <td>解密API</td>
                    <td>内容解密</td>
                    <td>POST</td>
                    <td class="failed">失败</td>
                    <td>7ms</td>
                </tr>
            
                <tr>
                    <td>Invalid Date</td>
                    <td>解密API</td>
                    <td>解密方法</td>
                    <td>GET</td>
                    <td class="failed">失败</td>
                    <td>4ms</td>
                </tr>
            
                <tr>
                    <td>2025/7/25 18:34:08</td>
                    <td>手动测试</td>
                    <td>手动测试</td>
                    <td>undefined</td>
                    <td class="failed">失败</td>
                    <td>0ms</td>
                </tr>
            
                <tr>
                    <td>2025/7/25 18:34:05</td>
                    <td>手动测试</td>
                    <td>手动测试</td>
                    <td>undefined</td>
                    <td class="failed">失败</td>
                    <td>0ms</td>
                </tr>
            
                <tr>
                    <td>2025/7/25 18:33:56</td>
                    <td>手动测试</td>
                    <td>手动测试</td>
                    <td>GET</td>
                    <td class="success">成功</td>
                    <td>330ms</td>
                </tr>
            
                <tr>
                    <td>2025/7/25 18:33:40</td>
                    <td>手动测试</td>
                    <td>手动测试</td>
                    <td>POST</td>
                    <td class="success">成功</td>
                    <td>4004ms</td>
                </tr>
            
                <tr>
                    <td>2025/7/25 18:33:22</td>
                    <td>手动测试</td>
                    <td>手动测试</td>
                    <td>undefined</td>
                    <td class="failed">失败</td>
                    <td>0ms</td>
                </tr>
            
                <tr>
                    <td>2025/7/25 18:33:15</td>
                    <td>手动测试</td>
                    <td>手动测试</td>
                    <td>undefined</td>
                    <td class="failed">失败</td>
                    <td>0ms</td>
                </tr>
            
                <tr>
                    <td>2025/7/25 18:33:03</td>
                    <td>手动测试</td>
                    <td>手动测试</td>
                    <td>POST</td>
                    <td class="success">成功</td>
                    <td>636ms</td>
                </tr>
            
                <tr>
                    <td>2025/7/25 18:32:49</td>
                    <td>手动测试</td>
                    <td>手动测试</td>
                    <td>POST</td>
                    <td class="success">成功</td>
                    <td>332ms</td>
                </tr>
            
        </tbody>
    </table>
</body>
</html>
        