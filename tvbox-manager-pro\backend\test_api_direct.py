#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def test_api_direct():
    """直接测试API接口"""
    
    import requests
    import json
    
    url = "http://localhost:8000/api/v1/interfaces/decrypt"
    data = {"url": "http://xhztv.top/4k.json"}
    
    print(f"测试API: {url}")
    print(f"请求数据: {data}")
    
    try:
        response = requests.post(url, json=data)
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ API调用成功")
            print(f"成功: {result.get('success', '未知')}")
            print(f"解密方法: {result.get('method', '未知')}")
            print(f"错误: {result.get('error', '无')}")

            content = result.get('content', '')
            print(f"内容长度: {len(content)}")

            if content:
                try:
                    import json
                    config = json.loads(content)
                    sites_count = len(config.get('sites', []))
                    lives_count = len(config.get('lives', []))
                    parses_count = len(config.get('parses', []))

                    print(f"配置统计: {sites_count}个站点, {lives_count}个直播源, {parses_count}个解析器")

                    if sites_count > 0:
                        print("✅ API返回了正确的站点数据")
                    else:
                        print("❌ API没有返回站点数据")
                except json.JSONDecodeError:
                    print("❌ 返回的内容不是有效JSON")
            else:
                print("❌ API没有返回内容")
        else:
            print(f"❌ API调用失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_api_direct()
