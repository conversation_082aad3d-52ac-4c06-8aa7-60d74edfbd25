{"cookie": "SESSDATA=7624af93%2C1696008331%2C862c8%2A42; bili_jct=141a474ef3ce8cf2fedf384e68f6625d; DedeUserID=3493271303096985; DedeUserID__ckMd5=212a836c164605b7", "classes": [{"type_name": "幼儿英语", "type_id": "幼儿英语"}, {"type_name": "思维课程", "type_id": "少儿思维"}, {"type_name": "少儿口才", "type_id": "少儿口才"}, {"type_name": "十万个为什么", "type_id": "十万个为什么"}, {"type_name": "DK百科", "type_id": "DK百科"}, {"type_name": "小灯塔学堂", "type_id": "小灯塔"}, {"type_name": "成语故事", "type_id": "成语故事"}, {"type_name": "安全教育", "type_id": "安全教育"}, {"type_name": "少儿编程", "type_id": "少儿编程"}, {"type_name": "古诗", "type_id": "古诗"}, {"type_name": "声律启蒙", "type_id": "声律启蒙"}, {"type_name": "笠翁对韵", "type_id": "笠翁对韵"}, {"type_name": "三字经", "type_id": "三字经"}, {"type_name": "弟子规", "type_id": "弟子规"}, {"type_name": "百家姓", "type_id": "百家姓"}, {"type_name": "儿童性教育", "type_id": "给孩子的第一堂性教育课"}], "filter": {"幼儿英语": [{"key": "tid", "name": "分类", "value": [{"n": "幼儿英语", "v": "幼儿英语"}, {"n": "英语启蒙", "v": "英语启蒙"}, {"n": "英文绘本", "v": "英文绘本"}, {"n": "Tumble Leaf飘零叶", "v": "Tumble Leaf飘零叶"}, {"n": "清华附小动画英语", "v": "清华附小动画英语"}, {"n": "牛津树", "v": "牛津树"}, {"n": "6岁英文启蒙动画", "v": "6岁英文启蒙动画"}, {"n": "动物王国大冒险", "v": "动物王国大冒险"}, {"n": "世界经典童话合集", "v": "世界经典童话合集"}, {"n": "火火兔学前英语", "v": "火火兔学前英语"}, {"n": "亲子英语", "v": "亲子英语"}, {"n": "国外幼儿园英语", "v": "国外幼儿园英语"}]}, {"key": "duration", "name": "时长", "value": [{"n": "全部", "v": "0"}, {"n": "60分钟以上", "v": "4"}, {"n": "30~60分钟", "v": "3"}, {"n": "10~30分钟", "v": "2"}, {"n": "10分钟以下", "v": "1"}]}], "少儿思维": [{"key": "tid", "name": "分类", "value": [{"n": "全部", "v": "少儿思维"}, {"n": "幼小衔接数学思维课", "v": "幼小衔接数学思维课"}, {"n": "数学思维", "v": "数学思维启蒙"}, {"n": "摩比爱数学", "v": "摩比爱数学"}, {"n": "数理思维", "v": "数理思维"}, {"n": "火花思维", "v": "火花思维"}, {"n": "幼儿思维", "v": "幼儿思维训练"}, {"n": "数学思维培养", "v": "数学思维培养"}, {"n": "儿童思维", "v": "儿童思维训练"}, {"n": "幼儿思维推理训练", "v": "幼儿思维推理训练"}]}, {"key": "duration", "name": "时长", "value": [{"n": "全部", "v": "0"}, {"n": "60分钟以上", "v": "4"}, {"n": "30~60分钟", "v": "3"}, {"n": "10~30分钟", "v": "2"}, {"n": "10分钟以下", "v": "1"}]}], "少儿口才": [{"key": "tid", "name": "分类", "value": [{"n": "全部", "v": "少儿口才"}, {"n": "孩子必学口才表达", "v": "孩子必学口才表达"}, {"n": "儿童表达力", "v": "儿童表达力"}]}, {"key": "duration", "name": "时长", "value": [{"n": "全部", "v": "0"}, {"n": "60分钟以上", "v": "4"}, {"n": "30~60分钟", "v": "3"}, {"n": "10~30分钟", "v": "2"}, {"n": "10分钟以下", "v": "1"}]}], "十万个为什么": [{"key": "tid", "name": "分类", "value": [{"n": "全部", "v": "十万个为什么"}, {"n": "科普启蒙", "v": "科普启蒙"}, {"n": "儿童科普", "v": "儿童科普"}]}, {"key": "duration", "name": "时长", "value": [{"n": "全部", "v": "0"}, {"n": "60分钟以上", "v": "4"}, {"n": "30~60分钟", "v": "3"}, {"n": "10~30分钟", "v": "2"}, {"n": "10分钟以下", "v": "1"}]}], "DK百科": [{"key": "tid", "name": "分类", "value": [{"n": "全部", "v": "DK百科"}, {"n": "幼儿百科", "v": "幼儿百科"}, {"n": "儿童百科", "v": "儿童百科"}]}, {"key": "duration", "name": "时长", "value": [{"n": "全部", "v": "0"}, {"n": "60分钟以上", "v": "4"}, {"n": "30~60分钟", "v": "3"}, {"n": "10~30分钟", "v": "2"}, {"n": "10分钟以下", "v": "1"}]}], "小灯塔": [{"key": "tid", "name": "分类", "value": [{"n": "全部", "v": "小灯塔"}, {"n": "昆虫世界总动员", "v": "昆虫世界总动员"}, {"n": "玩转奇妙动物世界", "v": "玩转奇妙动物世界"}, {"n": "探秘地球南北极", "v": "探秘地球南北极"}, {"n": "给孩子的第一堂海洋课", "v": "给孩子的第一堂海洋课"}, {"n": "stem奇妙科学课", "v": "stem奇妙科学课"}, {"n": "剑桥数学启蒙课", "v": "剑桥数学启蒙课"}, {"n": "给孩子的财商启蒙教育", "v": "给孩子的财商启蒙教育"}, {"n": "古诗里的二十四节气", "v": "古诗里的二十四节气"}, {"n": "成语动画", "v": "成语动画"}, {"n": "十二生肖", "v": "十二生肖"}, {"n": "天才发明家", "v": "天才发明家"}, {"n": "神奇的汉字", "v": "神奇的汉字"}]}, {"key": "duration", "name": "时长", "value": [{"n": "全部", "v": "0"}, {"n": "60分钟以上", "v": "4"}, {"n": "30~60分钟", "v": "3"}, {"n": "10~30分钟", "v": "2"}, {"n": "10分钟以下", "v": "1"}]}], "成语故事": [{"key": "tid", "name": "分类", "value": [{"n": "成语故事", "v": "成语故事"}]}, {"key": "duration", "name": "时长", "value": [{"n": "全部", "v": "0"}, {"n": "60分钟以上", "v": "4"}, {"n": "30~60分钟", "v": "3"}, {"n": "10~30分钟", "v": "2"}, {"n": "10分钟以下", "v": "1"}]}], "安全教育": [{"key": "duration", "name": "时长", "value": [{"n": "全部", "v": "0"}, {"n": "60分钟以上", "v": "4"}, {"n": "30~60分钟", "v": "3"}, {"n": "10~30分钟", "v": "2"}, {"n": "10分钟以下", "v": "1"}]}], "少儿编程": [{"key": "tid", "name": "分类", "value": [{"n": "全部", "v": "少儿编程"}, {"n": "<PERSON><PERSON>", "v": "<PERSON><PERSON>"}, {"n": "图形编程", "v": "图形编程"}, {"n": "趣味编程", "v": "趣味编程"}]}, {"key": "duration", "name": "时长", "value": [{"n": "全部", "v": "0"}, {"n": "60分钟以上", "v": "4"}, {"n": "30~60分钟", "v": "3"}, {"n": "10~30分钟", "v": "2"}, {"n": "10分钟以下", "v": "1"}]}], "古诗": [{"key": "tid", "name": "分类", "value": [{"n": "全部", "v": "古诗"}, {"n": "唐诗三百首", "v": "唐诗三百首"}, {"n": "小学古诗", "v": "小学古诗"}, {"n": "幼儿古诗", "v": "幼儿古诗"}]}, {"key": "duration", "name": "时长", "value": [{"n": "全部", "v": "0"}, {"n": "60分钟以上", "v": "4"}, {"n": "30~60分钟", "v": "3"}, {"n": "10~30分钟", "v": "2"}, {"n": "10分钟以下", "v": "1"}]}], "声律启蒙": [{"key": "tid", "name": "分类", "value": [{"n": "全部", "v": "声律启蒙"}, {"n": "声律启蒙", "v": "声律启蒙"}, {"n": "笠翁对韵", "v": "笠翁对韵"}]}, {"key": "duration", "name": "时长", "value": [{"n": "全部", "v": "0"}, {"n": "60分钟以上", "v": "4"}, {"n": "30~60分钟", "v": "3"}, {"n": "10~30分钟", "v": "2"}, {"n": "10分钟以下", "v": "1"}]}], "三字经": [{"key": "duration", "name": "时长", "value": [{"n": "全部", "v": "0"}, {"n": "60分钟以上", "v": "4"}, {"n": "30~60分钟", "v": "3"}, {"n": "10~30分钟", "v": "2"}, {"n": "10分钟以下", "v": "1"}]}], "弟子规": [{"key": "duration", "name": "时长", "value": [{"n": "全部", "v": "0"}, {"n": "60分钟以上", "v": "4"}, {"n": "30~60分钟", "v": "3"}, {"n": "10~30分钟", "v": "2"}, {"n": "10分钟以下", "v": "1"}]}], "百家姓": [{"key": "duration", "name": "时长", "value": [{"n": "全部", "v": "0"}, {"n": "60分钟以上", "v": "4"}, {"n": "30~60分钟", "v": "3"}, {"n": "10~30分钟", "v": "2"}, {"n": "10分钟以下", "v": "1"}]}]}}