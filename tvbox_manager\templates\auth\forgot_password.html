<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>忘记密码 - TVBox Manager</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        /* 重置样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body, html {
            height: 100%;
            font-family: 'Nunito', system-ui, -apple-system, sans-serif;
            background-color: #f3f4f6;
        }
        
        /* 登录页面主容器 */
        .login-container {
            height: 100vh;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #06b6d4 0%, #0369a1 100%);
            position: relative;
            overflow: hidden;
        }
        
        /* 背景装饰元素 */
        .bg-circles {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }
        
        .bg-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.05);
        }
        
        .bg-circle:nth-child(1) {
            width: 300px;
            height: 300px;
            top: -100px;
            right: -100px;
        }
        
        .bg-circle:nth-child(2) {
            width: 500px;
            height: 500px;
            bottom: -200px;
            left: -200px;
        }
        
        .bg-circle:nth-child(3) {
            width: 150px;
            height: 150px;
            top: 50%;
            left: 10%;
        }
        
        /* 登录卡片容器 - 减小最大宽度 */
        .login-card-container {
            width: 100%;
            max-width: 400px;
            padding: 0 15px;
            z-index: 2;
        }
        
        /* 登录卡片 */
        .login-card {
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 15px 30px -10px rgba(0, 0, 0, 0.25);
            backdrop-filter: blur(10px);
            transition: transform 0.3s ease;
        }
        
        .login-card:hover {
            transform: translateY(-3px);
        }
        
        /* 卡片头部 - 减小高度和内边距 */
        .login-header {
            padding: 20px 20px;
            text-align: center;
            background: linear-gradient(to right, #0ea5e9, #0369a1);
            color: white;
            position: relative;
        }
        
        /* 缩小logo尺寸 */
        .login-logo {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
        }
        
        .login-logo i {
            font-size: 24px;
            color: white;
        }
        
        /* 减小标题和副标题的字体大小 */
        .login-title {
            font-size: 22px;
            font-weight: 700;
            margin-bottom: 3px;
        }
        
        .login-subtitle {
            font-size: 14px;
            opacity: 0.85;
        }
        
        /* 卡片内容 - 减小内边距 */
        .login-body {
            padding: 20px 20px;
        }
        
        /* 表单样式 - 减小间距 */
        .login-form .form-group {
            margin-bottom: 16px;
        }
        
        .login-form label {
            display: block;
            font-size: 13px;
            font-weight: 600;
            color: #4b5563;
            margin-bottom: 5px;
        }
        
        .input-group {
            position: relative;
        }
        
        .input-group i {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #0284c7;
        }
        
        /* 减小输入框的高度和内边距 */
        .login-form input[type="email"],
        .login-form input[type="password"],
        .login-form input[type="text"] {
            width: 100%;
            padding: 10px 15px 10px 35px;
            font-size: 14px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background-color: #f9fafb;
            transition: all 0.3s;
        }
        
        .login-form input:focus {
            outline: none;
            border-color: #0ea5e9;
            box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.15);
            background-color: #fff;
        }
        
        /* 登录按钮 - 减小高度和内边距 */
        .login-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            padding: 10px 15px;
            font-size: 15px;
            font-weight: 600;
            color: white;
            background: linear-gradient(to right, #0ea5e9, #0284c7);
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .login-btn:hover {
            background: linear-gradient(to right, #0284c7, #0369a1);
            transform: translateY(-2px);
            box-shadow: 0 6px 10px -3px rgba(0, 0, 0, 0.1);
        }
        
        .login-btn:active {
            transform: translateY(0);
        }
        
        .login-btn i {
            margin-right: 8px;
        }
        
        /* 注册链接 - 减小间距 */
        .register-link {
            text-align: center;
            margin-top: 18px;
            padding-top: 15px;
            border-top: 1px solid #e5e7eb;
            font-size: 13px;
            color: #6b7280;
        }
        
        .register-link a {
            color: #0ea5e9;
            font-weight: 600;
            text-decoration: none;
            transition: color 0.2s;
        }
        
        .register-link a:hover {
            color: #0369a1;
            text-decoration: underline;
        }
        
        /* 页脚 - 减小内边距 */
        .login-footer {
            text-align: center;
            padding: 12px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
        }
        
        .login-footer a {
            color: white;
            font-weight: 500;
            text-decoration: none;
        }
        
        .login-footer a:hover {
            text-decoration: underline;
        }
        
        /* 错误信息样式 */
        .form-error {
            color: #ef4444;
            font-size: 12px;
            margin-top: 5px;
            display: flex;
            align-items: center;
        }
        
        .form-error i {
            margin-right: 4px;
        }
        
        /* 帮助文本 */
        .help-text {
            font-size: 13px;
            color: #6b7280;
            margin-bottom: 16px;
            text-align: center;
            line-height: 1.5;
        }
        
        /* 媒体查询 */
        @media (max-width: 640px) {
            .login-card {
                border-radius: 10px;
            }
            
            .login-header {
                padding: 15px 15px;
            }
            
            .login-body {
                padding: 20px 15px;
            }
            
            .login-logo {
                width: 40px;
                height: 40px;
            }
            
            .login-title {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- 背景装饰 -->
        <div class="bg-circles">
            <div class="bg-circle"></div>
            <div class="bg-circle"></div>
            <div class="bg-circle"></div>
        </div>
        
        <div class="login-card-container">
            <!-- 登录卡片 -->
            <div class="login-card">
                <!-- 卡片头部 -->
                <div class="login-header">
                    <div class="login-logo">
                        <i class="fas fa-key"></i>
                    </div>
                    <h1 class="login-title">忘记密码</h1>
                    <p class="login-subtitle">重置您的账户密码</p>
                </div>
                
                <!-- 卡片内容 -->
                <div class="login-body">
                    <p class="help-text">请输入您注册时使用的电子邮箱，我们将向您发送重置密码的链接。</p>
                    
                    <form action="{{ url_for('security.forgot_password') }}" method="POST" class="login-form" name="forgot_password_form">
                        {{ forgot_password_form.hidden_tag() }}
                        
                        <div class="form-group">
                            <label for="email">电子邮箱</label>
                            <div class="input-group">
                                <i class="fas fa-envelope"></i>
                                {{ forgot_password_form.email(class="", placeholder="请输入您的电子邮箱") }}
                            </div>
                            {% if forgot_password_form.email.errors %}
                                {% for error in forgot_password_form.email.errors %}
                                <div class="form-error">
                                    <i class="fas fa-exclamation-circle"></i> {{ error }}
                                </div>
                                {% endfor %}
                            {% endif %}
                        </div>
                        
                        <button type="submit" class="login-btn">
                            <i class="fas fa-paper-plane"></i> 发送重置链接
                        </button>
                        
                        <div class="register-link">
                            <a href="{{ url_for('security.login') }}">返回登录</a>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- 页脚 -->
            <div class="login-footer">
                <p>&copy; {{ now.year if now else 2023 }} TVBox Manager. All rights reserved.</p>
            </div>
        </div>
    </div>
    
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 移除Flask-Security默认内容
        const allContainers = document.querySelectorAll('.container, .navbar, .page-header, h1.flask-security');
        allContainers.forEach(element => {
            if (element.closest('.login-card') === null) {
                element.style.display = 'none';
            }
        });
        
        // 添加输入框聚焦效果
        const inputs = document.querySelectorAll('.login-form input');
        inputs.forEach(input => {
            const iconEl = input.parentElement.querySelector('i');
            
            input.addEventListener('focus', () => {
                if (iconEl) iconEl.style.color = '#0ea5e9';
            });
            
            input.addEventListener('blur', () => {
                if (iconEl) iconEl.style.color = '#0284c7';
            });
        });
        
        // 错误信息本地化处理
        const errorMessages = document.querySelectorAll('.form-error');
        errorMessages.forEach(errorMsg => {
            let text = errorMsg.textContent.trim();
            
            // 将常见的英文错误信息转换为中文
            if (text.includes('Email not provided')) {
                errorMsg.textContent = '请提供电子邮箱';
            } else if (text.includes('Invalid email address')) {
                errorMsg.textContent = '无效的电子邮箱地址';
            } else if (text.includes('Specified user does not exist')) {
                errorMsg.textContent = '该邮箱未注册';
            }
        });
    });
    </script>
</body>
</html> 