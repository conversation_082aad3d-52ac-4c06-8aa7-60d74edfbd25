/**
 * TVBox Manager
 * 主JavaScript文件
 * 版本: 1.0.0
 */

document.addEventListener('DOMContentLoaded', () => {
  // 初始化工具提示
  initTooltips();
  
  // 初始化表格功能
  initTableFeatures();
  
  // 初始化表单验证
  initFormValidation();
  
  // 初始化确认框
  initConfirmDialogs();
});

/**
 * 初始化工具提示
 */
function initTooltips() {
  // 查找所有带有title属性的元素
  const tooltipElements = document.querySelectorAll('[title]');
  
  tooltipElements.forEach(element => {
    const title = element.getAttribute('title');
    if (!title) return;
    
    // 创建工具提示元素
    element.addEventListener('mouseenter', function(e) {
      // 移除旧的提示
      const oldTooltip = document.querySelector('.tooltip');
      if (oldTooltip) {
        document.body.removeChild(oldTooltip);
      }
      
      // 创建新的提示
      const tooltip = document.createElement('div');
      tooltip.className = 'tooltip';
      tooltip.textContent = title;
      document.body.appendChild(tooltip);
      
      // 定位提示
      const rect = element.getBoundingClientRect();
      tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
      tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
      
      // 显示提示
      setTimeout(() => {
        tooltip.classList.add('show');
      }, 10);
    });
    
    // 移除工具提示
    element.addEventListener('mouseleave', function() {
      const tooltip = document.querySelector('.tooltip');
      if (tooltip) {
        tooltip.classList.remove('show');
        setTimeout(() => {
          if (tooltip.parentNode) {
            document.body.removeChild(tooltip);
          }
        }, 300);
      }
    });
    
    // 防止默认的title提示
    element.setAttribute('data-original-title', title);
    element.removeAttribute('title');
  });
}

/**
 * 初始化表格功能
 */
function initTableFeatures() {
  // 检查表格是否需要水平滚动
  const tables = document.querySelectorAll('.table');
  tables.forEach(table => {
    const tableContainer = table.parentElement;
    if (table.scrollWidth > tableContainer.clientWidth) {
      tableContainer.classList.add('table-scrollable');
    }
  });
  
  // 表格行悬停效果
  const tableRows = document.querySelectorAll('.table tbody tr');
  tableRows.forEach(row => {
    row.addEventListener('mouseenter', function() {
      this.classList.add('hover');
    });
    
    row.addEventListener('mouseleave', function() {
      this.classList.remove('hover');
    });
    });
}

/**
 * 初始化表单验证
 */
function initFormValidation() {
  const forms = document.querySelectorAll('form[data-validate="true"]');
  forms.forEach(form => {
    form.addEventListener('submit', function(event) {
      let isValid = true;
      
      // 必填字段验证
      const requiredFields = form.querySelectorAll('[required]');
      requiredFields.forEach(field => {
        if (!field.value.trim()) {
          isValid = false;
          field.classList.add('form-control-error');
          
          // 创建或更新错误消息
          let errorMessage = field.parentElement.querySelector('.form-error');
          if (!errorMessage) {
            errorMessage = document.createElement('div');
            errorMessage.className = 'form-error';
            field.parentElement.appendChild(errorMessage);
          }
          errorMessage.textContent = '此字段是必填项';
        } else {
          field.classList.remove('form-control-error');
          const errorMessage = field.parentElement.querySelector('.form-error');
          if (errorMessage) {
            errorMessage.remove();
          }
        }
      });
      
      // URL字段验证
      const urlFields = form.querySelectorAll('[type="url"]');
      urlFields.forEach(field => {
        if (field.value.trim() && !isValidURL(field.value.trim())) {
          isValid = false;
          field.classList.add('form-control-error');
          
          // 创建或更新错误消息
          let errorMessage = field.parentElement.querySelector('.form-error');
          if (!errorMessage) {
            errorMessage = document.createElement('div');
            errorMessage.className = 'form-error';
            field.parentElement.appendChild(errorMessage);
          }
          errorMessage.textContent = '请输入有效的URL';
        }
      });
      
      if (!isValid) {
        event.preventDefault();
      }
    });
    
    // 实时验证
    const formFields = form.querySelectorAll('input, select, textarea');
    formFields.forEach(field => {
      field.addEventListener('blur', function() {
        // 必填验证
        if (field.hasAttribute('required') && !field.value.trim()) {
          field.classList.add('form-control-error');
          
          // 创建或更新错误消息
          let errorMessage = field.parentElement.querySelector('.form-error');
          if (!errorMessage) {
            errorMessage = document.createElement('div');
            errorMessage.className = 'form-error';
            field.parentElement.appendChild(errorMessage);
          }
          errorMessage.textContent = '此字段是必填项';
        } else {
          field.classList.remove('form-control-error');
          const errorMessage = field.parentElement.querySelector('.form-error');
          if (errorMessage) {
            errorMessage.remove();
          }
        }
        
        // URL验证
        if (field.type === 'url' && field.value.trim() && !isValidURL(field.value.trim())) {
          field.classList.add('form-control-error');
          
          // 创建或更新错误消息
          let errorMessage = field.parentElement.querySelector('.form-error');
          if (!errorMessage) {
            errorMessage = document.createElement('div');
            errorMessage.className = 'form-error';
            field.parentElement.appendChild(errorMessage);
          }
          errorMessage.textContent = '请输入有效的URL';
        }
      });
    });
  });
}

/**
 * 验证URL是否有效
 * @param {string} url - 要验证的URL
 * @returns {boolean} - 是否有效
 */
function isValidURL(url) {
  try {
    new URL(url);
    return true;
  } catch (e) {
    return false;
  }
}

/**
 * 初始化确认对话框
 */
function initConfirmDialogs() {
  const confirmLinks = document.querySelectorAll('a[data-confirm]');
  confirmLinks.forEach(link => {
    link.addEventListener('click', function(event) {
      const message = this.getAttribute('data-confirm') || '确认执行此操作?';
      if (!confirm(message)) {
        event.preventDefault();
      }
    });
  });
}

/**
 * 显示加载中状态
 * @param {HTMLElement} element - 要显示加载状态的元素
 * @param {string} message - 加载消息
 */
function showLoading(element, message = '加载中...') {
  element.setAttribute('data-original-html', element.innerHTML);
  element.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${message}`;
  element.disabled = true;
  if (element.tagName === 'A') {
    element.style.pointerEvents = 'none';
  }
}

/**
 * 隐藏加载状态
 * @param {HTMLElement} element - 要隐藏加载状态的元素
 */
function hideLoading(element) {
  if (element.hasAttribute('data-original-html')) {
    element.innerHTML = element.getAttribute('data-original-html');
    element.removeAttribute('data-original-html');
    element.disabled = false;
    if (element.tagName === 'A') {
      element.style.pointerEvents = '';
    }
  }
}

/**
 * 显示通知
 * @param {string} message - 通知消息
 * @param {string} type - 通知类型 (success, warning, danger, info)
 * @param {number} duration - 显示时长(毫秒)
 */
function showNotification(message, type = 'info', duration = 5000) {
  // 创建通知容器
  let container = document.querySelector('.notification-container');
  if (!container) {
    container = document.createElement('div');
    container.className = 'notification-container';
    document.body.appendChild(container);
  }
  
  // 创建通知
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  
  // 图标
  let icon = '';
  switch (type) {
    case 'success':
      icon = '<i class="fas fa-check-circle"></i>';
      break;
    case 'warning':
      icon = '<i class="fas fa-exclamation-triangle"></i>';
      break;
    case 'danger':
      icon = '<i class="fas fa-exclamation-circle"></i>';
      break;
    default:
      icon = '<i class="fas fa-info-circle"></i>';
  }
  
  // 设置内容
  notification.innerHTML = `
    <div class="notification-icon">${icon}</div>
    <div class="notification-content">${message}</div>
    <button class="notification-close" type="button">
      <i class="fas fa-times"></i>
    </button>
  `;
  
  // 添加到容器
  container.appendChild(notification);
  
  // 显示通知
  setTimeout(() => {
    notification.classList.add('show');
  }, 10);
  
  // 关闭按钮
  const closeButton = notification.querySelector('.notification-close');
  closeButton.addEventListener('click', () => {
    closeNotification(notification);
  });
  
  // 自动关闭
  if (duration > 0) {
    setTimeout(() => {
      closeNotification(notification);
    }, duration);
  }
}

/**
 * 关闭通知
 * @param {HTMLElement} notification - 通知元素
 */
function closeNotification(notification) {
  notification.classList.remove('show');
  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
      
      // 如果没有更多通知，移除容器
      const container = document.querySelector('.notification-container');
      if (container && !container.hasChildNodes()) {
        document.body.removeChild(container);
      }
    }
  }, 300);
} 