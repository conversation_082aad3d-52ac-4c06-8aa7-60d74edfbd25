<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/text"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_weight="1"
        android:background="@drawable/selector_text"
        android:ellipsize="middle"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:gravity="center"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="18sp"
        tools:text="https://fongmi.github.io/cat.json" />

    <ImageView
        android:id="@+id/delete"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginStart="12dp"
        android:background="@drawable/selector_text"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:padding="8dp"
        android:scaleType="fitCenter"
        android:src="@drawable/ic_setting_delete" />

</LinearLayout>