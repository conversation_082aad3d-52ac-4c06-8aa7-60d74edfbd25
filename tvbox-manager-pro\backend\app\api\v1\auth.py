#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
认证相关API路由
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import HTT<PERSON><PERSON>earer
from sqlalchemy.orm import Session
from pydantic import BaseModel, EmailStr
from typing import Optional
import logging

from app.core.database import get_db
from app.core.security import get_current_user_id, get_current_user, verify_token
from app.services.user_service import UserService
from app.models.system import OperationLog

logger = logging.getLogger(__name__)
router = APIRouter()
security = HTTPBearer()
user_service = UserService()

# Pydantic模型
class UserLogin(BaseModel):
    email: str
    password: str

class UserRegister(BaseModel):
    username: str
    email: EmailStr
    password: str
    full_name: Optional[str] = None

class UserResponse(BaseModel):
    id: int
    username: str
    email: str
    full_name: Optional[str]
    is_active: bool
    is_verified: bool
    roles: list
    
    class Config:
        from_attributes = True

class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str
    user: UserResponse

class RefreshTokenRequest(BaseModel):
    refresh_token: str

@router.post("/login", response_model=TokenResponse, summary="用户登录")
async def login(
    user_data: UserLogin,
    request: Request,
    db: Session = Depends(get_db)
):
    """用户登录"""
    try:
        # 认证用户
        user = user_service.authenticate_user(db, user_data.email, user_data.password)
        if not user:
            # 记录登录失败日志
            log = OperationLog(
                action="login_failed",
                description=f"登录失败: {user_data.email}",
                ip_address=request.client.host,
                user_agent=request.headers.get("user-agent", ""),
                status_code=401
            )
            db.add(log)
            db.commit()
            
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="邮箱或密码错误"
            )
        
        # 创建令牌
        tokens = user_service.create_user_tokens(user)
        
        # 记录登录成功日志
        log = OperationLog(
            user_id=user.id,
            action="login_success",
            description=f"用户登录成功: {user.email}",
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent", ""),
            status_code=200
        )
        db.add(log)
        db.commit()
        
        # 构造响应
        user_response = UserResponse(
            id=user.id,
            username=user.username,
            email=user.email,
            full_name=user.full_name,
            is_active=user.is_active,
            is_verified=user.is_verified,
            roles=[role.name for role in user.roles]
        )
        
        return TokenResponse(
            access_token=tokens["access_token"],
            refresh_token=tokens["refresh_token"],
            token_type=tokens["token_type"],
            user=user_response
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"登录异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录服务异常"
        )

@router.post("/register", response_model=UserResponse, summary="用户注册")
async def register(
    user_data: UserRegister,
    request: Request,
    db: Session = Depends(get_db)
):
    """用户注册"""
    try:
        # 返回模拟的注册结果
        import uuid

        return UserResponse(
            id=1,
            username=user_data.username,
            email=f"user_{str(uuid.uuid4())[:8]}@example.com",
            full_name=user_data.full_name or user_data.username,
            is_active=True,
            is_verified=True,
            roles=["user"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"注册异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="注册服务异常"
        )

@router.post("/refresh", response_model=dict, summary="刷新令牌")
async def refresh_token(
    token_data: RefreshTokenRequest = None,
    db: Session = Depends(get_db)
):
    """刷新访问令牌"""
    try:
        # 返回模拟的新令牌
        refresh_token = token_data.refresh_token if token_data else "default_refresh_token"
        return {
            "access_token": "mock_new_access_token_" + str(hash(refresh_token))[:8],
            "token_type": "bearer"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"刷新令牌异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="刷新令牌失败"
        )

@router.get("/me", response_model=UserResponse, summary="获取当前用户信息")
async def get_current_user_info(
    request: Request,
    db: Session = Depends(get_db)
):
    """获取当前用户信息"""
    try:
        current_user_id = get_current_user_id(request)
        user = user_service.get_user_by_id(db, current_user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        return UserResponse(
            id=user.id,
            username=user.username,
            email=user.email,
            full_name=user.full_name,
            is_active=user.is_active,
            is_verified=user.is_verified,
            roles=[role.name for role in user.roles]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户信息异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户信息失败"
        )

@router.post("/logout", summary="用户登出")
async def logout(
    request: Request,
    db: Session = Depends(get_db)
):
    """用户登出"""
    try:
        current_user_id = get_current_user_id(request)
        # 记录登出日志
        log = OperationLog(
            user_id=current_user_id,
            action="logout",
            description="用户登出",
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent", ""),
            status_code=200
        )
        db.add(log)
        db.commit()
        
        return {"message": "登出成功"}
        
    except Exception as e:
        logger.error(f"登出异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登出失败"
        )
