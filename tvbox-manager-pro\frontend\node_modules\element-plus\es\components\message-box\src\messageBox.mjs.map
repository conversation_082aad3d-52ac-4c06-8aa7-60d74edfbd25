{"version": 3, "file": "messageBox.mjs", "sources": ["../../../../../../packages/components/message-box/src/messageBox.ts"], "sourcesContent": ["import { createVNode, isVNode, markRaw, render } from 'vue'\nimport {\n  debugWarn,\n  hasOwn,\n  isClient,\n  isElement,\n  isFunction,\n  isObject,\n  isString,\n  isUndefined,\n} from '@element-plus/utils'\nimport MessageBoxConstructor from './index.vue'\n\nimport type { AppContext, ComponentPublicInstance, VNode } from 'vue'\nimport type {\n  Action,\n  Callback,\n  ElMessageBoxOptions,\n  ElMessageBoxShortcutMethod,\n  IElMessageBox,\n  MessageBoxData,\n  MessageBoxState,\n} from './message-box.type'\n\n// component default merge props & data\n\nconst messageInstance = new Map<\n  ComponentPublicInstance<{ doClose: () => void }>, // marking doClose as function\n  {\n    options: any\n    callback: Callback | undefined\n    resolve: (res: any) => void\n    reject: (reason?: any) => void\n  }\n>()\n\nconst getAppendToElement = (props: any): HTMLElement => {\n  let appendTo: HTMLElement | null = document.body\n  if (props.appendTo) {\n    if (isString(props.appendTo)) {\n      appendTo = document.querySelector<HTMLElement>(props.appendTo)\n    }\n    if (isElement(props.appendTo)) {\n      appendTo = props.appendTo\n    }\n\n    // should fallback to default value with a warning\n    if (!isElement(appendTo)) {\n      debugWarn(\n        'ElMessageBox',\n        'the appendTo option is not an HTMLElement. Falling back to document.body.'\n      )\n      appendTo = document.body\n    }\n  }\n  return appendTo\n}\n\nconst initInstance = (\n  props: any,\n  container: HTMLElement,\n  appContext: AppContext | null = null\n) => {\n  const vnode = createVNode(\n    MessageBoxConstructor,\n    props,\n    isFunction(props.message) || isVNode(props.message)\n      ? {\n          default: isFunction(props.message)\n            ? props.message\n            : () => props.message,\n        }\n      : null\n  )\n  vnode.appContext = appContext\n  render(vnode, container)\n  getAppendToElement(props).appendChild(container.firstElementChild!)\n  return vnode.component\n}\n\nconst genContainer = () => {\n  return document.createElement('div')\n}\n\nconst showMessage = (options: any, appContext?: AppContext | null) => {\n  const container = genContainer()\n  // Adding destruct method.\n  // when transition leaves emitting `vanish` evt. so that we can do the clean job.\n  options.onVanish = () => {\n    // not sure if this causes mem leak, need proof to verify that.\n    // maybe calling out like 1000 msg-box then close them all.\n    render(null, container)\n    messageInstance.delete(vm) // Remove vm to avoid mem leak.\n    // here we were suppose to call document.body.removeChild(container.firstElementChild)\n    // but render(null, container) did that job for us. so that we do not call that directly\n  }\n\n  options.onAction = (action: Action) => {\n    const currentMsg = messageInstance.get(vm)!\n    let resolve: Action | { value: string; action: Action }\n    if (options.showInput) {\n      resolve = { value: vm.inputValue, action }\n    } else {\n      resolve = action\n    }\n    if (options.callback) {\n      options.callback(resolve, instance.proxy)\n    } else {\n      if (action === 'cancel' || action === 'close') {\n        if (options.distinguishCancelAndClose && action !== 'cancel') {\n          currentMsg.reject('close')\n        } else {\n          currentMsg.reject('cancel')\n        }\n      } else {\n        currentMsg.resolve(resolve)\n      }\n    }\n  }\n\n  const instance = initInstance(options, container, appContext)!\n\n  // This is how we use message box programmatically.\n  // Maybe consider releasing a template version?\n  // get component instance like v2.\n  const vm = instance.proxy as ComponentPublicInstance<\n    {\n      visible: boolean\n      doClose: () => void\n    } & MessageBoxState\n  >\n\n  for (const prop in options) {\n    if (hasOwn(options, prop) && !hasOwn(vm.$props, prop)) {\n      if (prop === 'closeIcon' && isObject(options[prop])) {\n        vm[prop as keyof ComponentPublicInstance] = markRaw(options[prop])\n      } else {\n        vm[prop as keyof ComponentPublicInstance] = options[prop]\n      }\n    }\n  }\n\n  // change visibility after everything is settled\n  vm.visible = true\n  return vm\n}\n\nasync function MessageBox(\n  options: ElMessageBoxOptions,\n  appContext?: AppContext | null\n): Promise<MessageBoxData>\nfunction MessageBox(\n  options: ElMessageBoxOptions | string | VNode,\n  appContext: AppContext | null = null\n): Promise<{ value: string; action: Action } | Action> {\n  if (!isClient) return Promise.reject()\n  let callback: Callback | undefined\n  if (isString(options) || isVNode(options)) {\n    options = {\n      message: options,\n    }\n  } else {\n    callback = options.callback\n  }\n\n  return new Promise((resolve, reject) => {\n    const vm = showMessage(\n      options,\n      appContext ?? (MessageBox as IElMessageBox)._context\n    )\n    // collect this vm in order to handle upcoming events.\n    messageInstance.set(vm, {\n      options,\n      callback,\n      resolve,\n      reject,\n    })\n  })\n}\n\nconst MESSAGE_BOX_VARIANTS = ['alert', 'confirm', 'prompt'] as const\nconst MESSAGE_BOX_DEFAULT_OPTS: Record<\n  typeof MESSAGE_BOX_VARIANTS[number],\n  Partial<ElMessageBoxOptions>\n> = {\n  alert: { closeOnPressEscape: false, closeOnClickModal: false },\n  confirm: { showCancelButton: true },\n  prompt: { showCancelButton: true, showInput: true },\n}\n\nMESSAGE_BOX_VARIANTS.forEach((boxType) => {\n  ;(MessageBox as IElMessageBox)[boxType] = messageBoxFactory(\n    boxType\n  ) as ElMessageBoxShortcutMethod\n})\n\nfunction messageBoxFactory(boxType: typeof MESSAGE_BOX_VARIANTS[number]) {\n  return (\n    message: string | VNode,\n    title: string | ElMessageBoxOptions,\n    options?: ElMessageBoxOptions,\n    appContext?: AppContext | null\n  ) => {\n    let titleOrOpts = ''\n    if (isObject(title)) {\n      options = title as ElMessageBoxOptions\n      titleOrOpts = ''\n    } else if (isUndefined(title)) {\n      titleOrOpts = ''\n    } else {\n      titleOrOpts = title as string\n    }\n\n    return MessageBox(\n      Object.assign(\n        {\n          title: titleOrOpts,\n          message,\n          type: '',\n          ...MESSAGE_BOX_DEFAULT_OPTS[boxType],\n        },\n        options,\n        {\n          boxType,\n        }\n      ),\n      appContext\n    )\n  }\n}\n\nMessageBox.close = () => {\n  // instance.setupInstall.doClose()\n  // instance.setupInstall.state.visible = false\n\n  messageInstance.forEach((_, vm) => {\n    vm.doClose()\n  })\n\n  messageInstance.clear()\n}\n;(MessageBox as IElMessageBox)._context = null\n\nexport default MessageBox as IElMessageBox\n"], "names": [], "mappings": ";;;;;;;AAYA,MAAM,eAAe,mBAAmB,IAAI,GAAG,EAAE,CAAC;AAClD,MAAM,kBAAkB,GAAG,CAAC,KAAK,KAAK;AACtC,EAAE,IAAI,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC;AAC/B,EAAE,IAAI,KAAK,CAAC,QAAQ,EAAE;AACtB,IAAI,IAAI,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;AAClC,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AACxD,KAAK;AACL,IAAI,IAAI,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;AACnC,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;AAChC,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE;AAC9B,MAAM,SAAS,CAAC,cAAc,EAAE,2EAA2E,CAAC,CAAC;AAC7G,MAAM,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC;AAC/B,KAAK;AACL,GAAG;AACH,EAAE,OAAO,QAAQ,CAAC;AAClB,CAAC,CAAC;AACF,MAAM,YAAY,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,UAAU,GAAG,IAAI,KAAK;AAC9D,EAAE,MAAM,KAAK,GAAG,WAAW,CAAC,qBAAqB,EAAE,KAAK,EAAE,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG;AAChH,IAAI,OAAO,EAAE,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC,OAAO,GAAG,MAAM,KAAK,CAAC,OAAO;AAC5E,GAAG,GAAG,IAAI,CAAC,CAAC;AACZ,EAAE,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC;AAChC,EAAE,MAAM,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AAC3B,EAAE,kBAAkB,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;AACrE,EAAE,OAAO,KAAK,CAAC,SAAS,CAAC;AACzB,CAAC,CAAC;AACF,MAAM,YAAY,GAAG,MAAM;AAC3B,EAAE,OAAO,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACvC,CAAC,CAAC;AACF,MAAM,WAAW,GAAG,CAAC,OAAO,EAAE,UAAU,KAAK;AAC7C,EAAE,MAAM,SAAS,GAAG,YAAY,EAAE,CAAC;AACnC,EAAE,OAAO,CAAC,QAAQ,GAAG,MAAM;AAC3B,IAAI,MAAM,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AAC5B,IAAI,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AAC/B,GAAG,CAAC;AACJ,EAAE,OAAO,CAAC,QAAQ,GAAG,CAAC,MAAM,KAAK;AACjC,IAAI,MAAM,UAAU,GAAG,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAC/C,IAAI,IAAI,OAAO,CAAC;AAChB,IAAI,IAAI,OAAO,CAAC,SAAS,EAAE;AAC3B,MAAM,OAAO,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC;AACjD,KAAK,MAAM;AACX,MAAM,OAAO,GAAG,MAAM,CAAC;AACvB,KAAK;AACL,IAAI,IAAI,OAAO,CAAC,QAAQ,EAAE;AAC1B,MAAM,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;AAChD,KAAK,MAAM;AACX,MAAM,IAAI,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,OAAO,EAAE;AACrD,QAAQ,IAAI,OAAO,CAAC,yBAAyB,IAAI,MAAM,KAAK,QAAQ,EAAE;AACtE,UAAU,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACrC,SAAS,MAAM;AACf,UAAU,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AACtC,SAAS;AACT,OAAO,MAAM;AACb,QAAQ,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACpC,OAAO;AACP,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,QAAQ,GAAG,YAAY,CAAC,OAAO,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;AAChE,EAAE,MAAM,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC;AAC5B,EAAE,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE;AAC9B,IAAI,IAAI,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;AAC3D,MAAM,IAAI,IAAI,KAAK,WAAW,IAAI,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE;AAC3D,QAAQ,EAAE,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;AAC1C,OAAO,MAAM;AACb,QAAQ,EAAE,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AACjC,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,EAAE,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,EAAE,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC;AACF,SAAS,UAAU,CAAC,OAAO,EAAE,UAAU,GAAG,IAAI,EAAE;AAChD,EAAE,IAAI,CAAC,QAAQ;AACf,IAAI,OAAO,OAAO,CAAC,MAAM,EAAE,CAAC;AAC5B,EAAE,IAAI,QAAQ,CAAC;AACf,EAAE,IAAI,QAAQ,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;AAC7C,IAAI,OAAO,GAAG;AACd,MAAM,OAAO,EAAE,OAAO;AACtB,KAAK,CAAC;AACN,GAAG,MAAM;AACT,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;AAChC,GAAG;AACH,EAAE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAK;AAC1C,IAAI,MAAM,EAAE,GAAG,WAAW,CAAC,OAAO,EAAE,UAAU,IAAI,IAAI,GAAG,UAAU,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC;AAC3F,IAAI,eAAe,CAAC,GAAG,CAAC,EAAE,EAAE;AAC5B,MAAM,OAAO;AACb,MAAM,QAAQ;AACd,MAAM,OAAO;AACb,MAAM,MAAM;AACZ,KAAK,CAAC,CAAC;AACP,GAAG,CAAC,CAAC;AACL,CAAC;AACD,MAAM,oBAAoB,GAAG,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;AAC5D,MAAM,wBAAwB,GAAG;AACjC,EAAE,KAAK,EAAE,EAAE,kBAAkB,EAAE,KAAK,EAAE,iBAAiB,EAAE,KAAK,EAAE;AAChE,EAAE,OAAO,EAAE,EAAE,gBAAgB,EAAE,IAAI,EAAE;AACrC,EAAE,MAAM,EAAE,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE;AACrD,CAAC,CAAC;AACF,oBAAoB,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK;AAE1C,EAAE,UAAU,CAAC,OAAO,CAAC,GAAG,iBAAiB,CAAC,OAAO,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC;AACH,SAAS,iBAAiB,CAAC,OAAO,EAAE;AACpC,EAAE,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,KAAK;AAClD,IAAI,IAAI,WAAW,GAAG,EAAE,CAAC;AACzB,IAAI,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AACzB,MAAM,OAAO,GAAG,KAAK,CAAC;AACtB,MAAM,WAAW,GAAG,EAAE,CAAC;AACvB,KAAK,MAAM,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;AACnC,MAAM,WAAW,GAAG,EAAE,CAAC;AACvB,KAAK,MAAM;AACX,MAAM,WAAW,GAAG,KAAK,CAAC;AAC1B,KAAK;AACL,IAAI,OAAO,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC;AACpC,MAAM,KAAK,EAAE,WAAW;AACxB,MAAM,OAAO;AACb,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,GAAG,wBAAwB,CAAC,OAAO,CAAC;AAC1C,KAAK,EAAE,OAAO,EAAE;AAChB,MAAM,OAAO;AACb,KAAK,CAAC,EAAE,UAAU,CAAC,CAAC;AACpB,GAAG,CAAC;AACJ,CAAC;AACD,UAAU,CAAC,KAAK,GAAG,MAAM;AACzB,EAAE,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK;AACrC,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;AACjB,GAAG,CAAC,CAAC;AACL,EAAE,eAAe,CAAC,KAAK,EAAE,CAAC;AAC1B,CAAC,CAAC;AACF,UAAU,CAAC,QAAQ,GAAG,IAAI;;;;"}