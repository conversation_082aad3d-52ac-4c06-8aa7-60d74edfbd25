import requests
import json

def test_direct_config_url():
    """测试直接指向配置文件的URL"""
    url = 'http://xhztv.top/4k.json'
    headers = {
        'User-Agent': 'okhttp/3.12.11',
        'Accept': '*/*'
    }

    try:
        response = requests.get(url, headers=headers, timeout=10)
        print(f'状态码: {response.status_code}')
        print(f'Content-Type: {response.headers.get("Content-Type", "未知")}')
        print(f'内容长度: {len(response.text)}')
        print(f'前200个字符: {response.text[:200]}')
        
        # 尝试解析JSON
        try:
            data = json.loads(response.text)
            print(f'JSON解析成功!')
            print(f'站点数: {len(data.get("sites", []))}')
            print(f'直播源数: {len(data.get("lives", []))}')
            print(f'解析器数: {len(data.get("parses", []))}')
            
            # 显示前几个站点
            sites = data.get("sites", [])
            if sites:
                print(f'前3个站点:')
                for i, site in enumerate(sites[:3]):
                    name = site.get('name', '未知')
                    api = site.get('api', '未知')
                    print(f'  {i+1}. {name} - {api}')
            
            return response.text
            
        except json.JSONDecodeError as e:
            print(f'JSON解析失败: {e}')
            return None
            
    except Exception as e:
        print(f'请求失败: {e}')
        return None

def test_with_decryptor():
    """使用系统解密器测试"""
    from app.services.tvbox_decryptor import TVBoxDecryptor

    decryptor = TVBoxDecryptor()
    try:
        content, method = decryptor.decrypt_config_url('http://xhztv.top/4k.json')
        print(f'解密方法: {method}')
        print(f'内容长度: {len(content) if content else 0}')

        if content:
            print(f'前200个字符: {content[:200]}')

            # 尝试解析配置
            try:
                config_info = decryptor.parse_tvbox_config_content(content)
                print(f'解析成功!')
                print(f'站点数: {len(config_info.get("sites", []))}')
                print(f'直播源数: {len(config_info.get("lives", []))}')
                print(f'解析器数: {len(config_info.get("parses", []))}')

                # 显示前几个站点
                sites = config_info.get("sites", [])
                if sites:
                    print(f'前3个站点:')
                    for i, site in enumerate(sites[:3]):
                        name = site.get('name', '未知')
                        api = site.get('api', '未知')
                        print(f'  {i+1}. {name} - {api}')

                return len(sites) > 0
            except Exception as parse_e:
                print(f'解析配置失败: {parse_e}')

                # 尝试手动修复JSON
                try:
                    print("尝试手动修复JSON...")
                    fixed_content = decryptor._fix_json(content)
                    print(f'修复后长度: {len(fixed_content)}')

                    # 再次尝试解析
                    config_info = decryptor.parse_tvbox_config_content(fixed_content)
                    print(f'修复后解析成功!')
                    print(f'站点数: {len(config_info.get("sites", []))}')
                    return len(config_info.get("sites", [])) > 0
                except Exception as fix_e:
                    print(f'修复也失败: {fix_e}')
                    return False
        else:
            print('解密失败')
            return False
    except Exception as e:
        print(f'异常: {e}')
        return False

if __name__ == "__main__":
    print("=== 直接请求测试 ===")
    content = test_direct_config_url()

    print("\n=== 系统解密器测试 ===")
    success = test_with_decryptor()

    if success:
        print(f'\n✅ 系统解密器测试成功')
    else:
        print(f'\n❌ 系统解密器测试失败')
