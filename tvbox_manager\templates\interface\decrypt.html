{% extends "layouts/admin.html" %}
{% set active_nav = 'decrypt' %}

{% block title %}接口解密 - TVBox Manager{% endblock %}

{% block content %}
<!-- 页面标题 -->
<div class="page-header">
    <h1 class="page-title">接口解密</h1>
</div>

<!-- 解密表单卡片 -->
<div class="card">
    <div class="card-header">
        <h2 class="card-title">解密工具</h2>
    </div>
    
    <div class="card-body">
        <form action="{{ url_for('interface.decrypt') }}" method="post">
            {{ form.csrf_token }}
            
            <div class="form-group">
                <label class="form-label" for="type">解密类型</label>
                <select class="form-control" id="type" name="type">
                    <option value="url">URL解密</option>
                    <option value="text">文本解密</option>
                    <option value="file">文件解密</option>
                </select>
                <div class="form-text">选择需要解密的内容类型</div>
            </div>
            
            <div class="form-group" id="url-group">
                <label class="form-label" for="url">接口URL</label>
                <input type="text" class="form-control" id="url" name="url" placeholder="输入需要解密的接口URL">
                <div class="form-text">例如: https://example.com/encrypted.json</div>
            </div>
            
            <div class="form-group" id="text-group" style="display: none;">
                <label class="form-label" for="text">加密文本</label>
                <textarea class="form-control" id="text" name="text" rows="5" placeholder="输入需要解密的文本内容"></textarea>
            </div>
            
            <div class="form-group" id="file-group" style="display: none;">
                <label class="form-label" for="file">加密文件</label>
                <input type="file" class="form-control" id="file" name="file">
                <div class="form-text">选择需要解密的文件</div>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="method">解密方法</label>
                <select class="form-control" id="method" name="method">
                    <option value="auto">自动检测</option>
                    <option value="base64">Base64</option>
                    <option value="json">JSON</option>
                    <option value="aes">AES</option>
                    <option value="rc4">RC4</option>
                </select>
                <div class="form-text">如果不确定解密方法，请选择"自动检测"</div>
            </div>
            
            <div class="form-group" id="key-group" style="display: none;">
                <label class="form-label" for="key">密钥(Key)</label>
                <input type="text" class="form-control" id="key" name="key" placeholder="输入解密密钥">
                <div class="form-text">对于AES或RC4等加密方法，需要提供密钥</div>
            </div>
            
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-unlock-alt btn-icon"></i> 解密
            </button>
        </form>
    </div>
</div>

<!-- 解密结果卡片 -->
{% if result %}
<div class="card">
    <div class="card-header">
        <h2 class="card-title">解密结果</h2>
    </div>
    
    <div class="card-body">
        {% if success %}
        <div class="alert alert-success">
            <div class="alert-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="alert-content">
                <p>解密成功！解密方法: {{ method }}</p>
            </div>
        </div>
        
        <div style="margin-top: 16px;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                <h3 style="font-size: 16px; font-weight: 600; margin: 0;">解密内容</h3>
                <button id="copy-btn" class="btn btn-sm btn-secondary" onclick="copyResult()">
                    <i class="fas fa-copy btn-icon"></i> 复制
                </button>
            </div>
            
            <div style="position: relative;">
                <pre id="result-content" style="background-color: var(--bg-color); padding: 16px; border-radius: 8px; overflow-x: auto; max-height: 400px;">{{ result }}</pre>
            </div>
        </div>
        
        <div style="margin-top: 24px;">
            <a href="{{ url_for('interface.add') }}?url={{ url }}&method={{ method }}" class="btn btn-primary">
                <i class="fas fa-plus btn-icon"></i> 添加到接口
            </a>
            
            <button id="save-btn" class="btn btn-secondary" onclick="saveAsFile()">
                <i class="fas fa-download btn-icon"></i> 保存为文件
            </button>
        </div>
        {% else %}
        <div class="alert alert-danger">
            <div class="alert-icon">
                <i class="fas fa-exclamation-circle"></i>
            </div>
            <div class="alert-content">
                <p>解密失败: {{ result }}</p>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endif %}

<!-- 解密帮助卡片 -->
<div class="card">
    <div class="card-header">
        <h2 class="card-title">解密帮助</h2>
    </div>
    
    <div class="card-body">
        <div class="alert alert-info">
            <div class="alert-icon">
                <i class="fas fa-info-circle"></i>
            </div>
            <div class="alert-content">
                <p>接口解密工具支持多种加密方式，常见的TVBox接口加密方式包括Base64、AES、RC4等。</p>
            </div>
        </div>
        
        <h3 style="margin-top: 16px; font-size: 16px; font-weight: 600;">常见问题</h3>
        
        <div style="margin-top: 12px;">
            <h4 style="font-weight: 500; margin-bottom: 6px;">1. 自动检测失败怎么办？</h4>
            <p>尝试手动选择解密方法，并提供正确的密钥（如需要）。</p>
        </div>
        
        <div style="margin-top: 12px;">
            <h4 style="font-weight: 500; margin-bottom: 6px;">2. 解密后格式错乱？</h4>
            <p>可能是选择了错误的解密方法，或者源文件使用了多重加密。</p>
        </div>
        
        <div style="margin-top: 12px;">
            <h4 style="font-weight: 500; margin-bottom: 6px;">3. 无法获取远程接口？</h4>
            <p>检查URL是否正确，或者服务器是否有访问限制。</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 切换解密类型
    document.getElementById('type').addEventListener('change', function() {
        const type = this.value;
        
        // 隐藏所有输入组
        document.getElementById('url-group').style.display = 'none';
        document.getElementById('text-group').style.display = 'none';
        document.getElementById('file-group').style.display = 'none';
        
        // 显示选中的输入组
        if (type === 'url') {
            document.getElementById('url-group').style.display = 'block';
        } else if (type === 'text') {
            document.getElementById('text-group').style.display = 'block';
        } else if (type === 'file') {
            document.getElementById('file-group').style.display = 'block';
        }
    });
    
    // 切换解密方法，显示/隐藏密钥输入框
    document.getElementById('method').addEventListener('change', function() {
        const method = this.value;
        const keyGroup = document.getElementById('key-group');
        
        if (method === 'aes' || method === 'rc4') {
            keyGroup.style.display = 'block';
        } else {
            keyGroup.style.display = 'none';
        }
    });
    
    // 复制解密结果
    function copyResult() {
        const result = document.getElementById('result-content').innerText;
        navigator.clipboard.writeText(result)
            .then(() => {
                const copyBtn = document.getElementById('copy-btn');
                const originalText = copyBtn.innerHTML;
                copyBtn.innerHTML = '<i class="fas fa-check"></i> 已复制';
                
                setTimeout(() => {
                    copyBtn.innerHTML = originalText;
                }, 2000);
            })
            .catch(err => {
                console.error('复制失败:', err);
            });
    }
    
    // 保存为文件
    function saveAsFile() {
        const result = document.getElementById('result-content').innerText;
        const blob = new Blob([result], {type: 'application/json'});
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        
        a.href = url;
        a.download = 'decrypted_' + new Date().getTime() + '.json';
        a.click();
        
        URL.revokeObjectURL(url);
    }
</script>
{% endblock %} 