{"version": 3, "file": "constants.js", "sourceRoot": "", "sources": ["../../src/eslint-bulk-suppressions/constants.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;AAE9C,QAAA,mCAAmC,GAC9C,kCAAkC,CAAC;AACxB,QAAA,iCAAiC,GAC5C,gCAAgC,CAAC;AACtB,QAAA,+BAA+B,GAAyB,oBAAoB,CAAC;AAC7E,QAAA,8BAA8B,GAAwB,mBAAmB,CAAC;AAC1E,QAAA,+BAA+B,GAC1C,+BAA+B,CAAC;AACrB,QAAA,+CAA+C,GAC1D,8CAA8C,CAAC;AACpC,QAAA,uBAAuB,GAAiB,YAAY,CAAC;AAErD,QAAA,gCAAgC,GAC3C,gCAAgC,CAAC;AAEtB,QAAA,yCAAyC,GACpD,MAAA,OAAO,CAAC,GAAG,CAAC,wCAAgC,CAAC,mCAAI,QAAQ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See <PERSON><PERSON><PERSON><PERSON> in the project root for license information.\n\nexport const ESLINT_BULK_PATCH_PATH_ENV_VAR_NAME: 'RUSHSTACK_ESLINT_BULK_PATCH_PATH' =\n  'RUSHSTACK_ESLINT_BULK_PATCH_PATH';\nexport const ESLINT_BULK_SUPPRESS_ENV_VAR_NAME: 'RUSHSTACK_ESLINT_BULK_SUPPRESS' =\n  'RUSHSTACK_ESLINT_BULK_SUPPRESS';\nexport const ESLINT_BULK_ENABLE_ENV_VAR_NAME: 'ESLINT_BULK_ENABLE' = 'ESLINT_BULK_ENABLE';\nexport const ESLINT_BULK_PRUNE_ENV_VAR_NAME: 'ESLINT_BULK_PRUNE' = 'ESLINT_BULK_PRUNE';\nexport const ESLINT_BULK_DETECT_ENV_VAR_NAME: '_RUSHSTACK_ESLINT_BULK_DETECT' =\n  '_RUSHSTACK_ESLINT_BULK_DETECT';\nexport const ESLINT_BULK_FORCE_REGENERATE_PATCH_ENV_VAR_NAME: 'RUSHSTACK_ESLINT_BULK_FORCE_REGENERATE_PATCH' =\n  'RUSHSTACK_ESLINT_BULK_FORCE_REGENERATE_PATCH';\nexport const VSCODE_PID_ENV_VAR_NAME: 'VSCODE_PID' = 'VSCODE_PID';\n\nexport const ESLINT_PACKAGE_NAME_ENV_VAR_NAME: '_RUSHSTACK_ESLINT_PACKAGE_NAME' =\n  '_RUSHSTACK_ESLINT_PACKAGE_NAME';\n\nexport const BULK_SUPPRESSIONS_CLI_ESLINT_PACKAGE_NAME: string =\n  process.env[ESLINT_PACKAGE_NAME_ENV_VAR_NAME] ?? 'eslint';\n"]}