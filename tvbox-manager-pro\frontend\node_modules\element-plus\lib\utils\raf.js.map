{"version": 3, "file": "raf.js", "sources": ["../../../../packages/utils/raf.ts"], "sourcesContent": ["import { isClient } from './browser'\n\nexport const rAF = (fn: () => void) =>\n  isClient\n    ? window.requestAnimationFrame(fn)\n    : (setTimeout(fn, 16) as unknown as number)\n\nexport const cAF = (handle: number) =>\n  isClient ? window.cancelAnimationFrame(handle) : clearTimeout(handle)\n"], "names": ["isClient"], "mappings": ";;;;;;AACY,MAAC,GAAG,GAAG,CAAC,EAAE,KAAKA,aAAQ,GAAG,MAAM,CAAC,qBAAqB,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,EAAE,EAAE;AAChF,MAAC,GAAG,GAAG,CAAC,MAAM,KAAKA,aAAQ,GAAG,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,GAAG,YAAY,CAAC,MAAM;;;;;"}