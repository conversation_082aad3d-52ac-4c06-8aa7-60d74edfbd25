{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/teleport/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Teleport from './src/teleport.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElTeleport: SFCWithInstall<typeof Teleport> = withInstall(Teleport)\n\nexport default ElTeleport\n\nexport * from './src/teleport'\n"], "names": [], "mappings": ";;;;AAEY,MAAC,UAAU,GAAG,WAAW,CAAC,QAAQ;;;;"}