{"name": "vue", "version": "3.5.18", "description": "The progressive JavaScript framework for building modern web UI.", "main": "index.js", "module": "dist/vue.runtime.esm-bundler.js", "types": "dist/vue.d.ts", "unpkg": "dist/vue.global.js", "jsdelivr": "dist/vue.global.js", "files": ["index.js", "index.mjs", "dist", "compiler-sfc", "server-renderer", "jsx-runtime", "jsx.d.ts"], "exports": {".": {"import": {"types": "./dist/vue.d.mts", "node": "./index.mjs", "default": "./dist/vue.runtime.esm-bundler.js"}, "require": {"types": "./dist/vue.d.ts", "node": {"production": "./dist/vue.cjs.prod.js", "development": "./dist/vue.cjs.js", "default": "./index.js"}, "default": "./index.js"}}, "./server-renderer": {"import": {"types": "./server-renderer/index.d.mts", "default": "./server-renderer/index.mjs"}, "require": {"types": "./server-renderer/index.d.ts", "default": "./server-renderer/index.js"}}, "./compiler-sfc": {"import": {"types": "./compiler-sfc/index.d.mts", "browser": "./compiler-sfc/index.browser.mjs", "default": "./compiler-sfc/index.mjs"}, "require": {"types": "./compiler-sfc/index.d.ts", "browser": "./compiler-sfc/index.browser.js", "default": "./compiler-sfc/index.js"}}, "./jsx-runtime": {"types": "./jsx-runtime/index.d.ts", "import": "./jsx-runtime/index.mjs", "require": "./jsx-runtime/index.js"}, "./jsx-dev-runtime": {"types": "./jsx-runtime/index.d.ts", "import": "./jsx-runtime/index.mjs", "require": "./jsx-runtime/index.js"}, "./jsx": "./jsx.d.ts", "./dist/*": "./dist/*", "./package.json": "./package.json"}, "buildOptions": {"name": "<PERSON><PERSON>", "formats": ["esm-bundler", "esm-bundler-runtime", "cjs", "global", "global-runtime", "esm-browser", "esm-browser-runtime"]}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git"}, "keywords": ["vue"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/core/issues"}, "homepage": "https://github.com/vuejs/core/tree/main/packages/vue#readme", "dependencies": {"@vue/shared": "3.5.18", "@vue/compiler-dom": "3.5.18", "@vue/compiler-sfc": "3.5.18", "@vue/server-renderer": "3.5.18", "@vue/runtime-dom": "3.5.18"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}