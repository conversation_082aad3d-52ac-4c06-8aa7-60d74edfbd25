{"规则名": "兔小贝", "规则作者": "香雅情", "请求头参数": "手机", "网页编码格式": "UTF-8", "图片是否需要代理": "0", "分类起始页码": "1", "分类链接": "https://www.tuxiaobei.com/subject/{cateId}", "分类名称": "兔小贝原创儿歌&睡前儿歌&Everybody动起来&爸爸妈妈小时候的儿歌&宝宝吃饭香&乖宝宝不哭闹&好宝宝爱上学&欢快儿歌&经典儿歌&快乐的节日&快乐的童年&流行儿歌&美丽四季&男孩最爱&女孩最爱&我爱爸爸妈妈&我的动物朋友&我的祖国和我&舞蹈音乐&英文儿歌&粤语儿歌&早教好习惯&放暑假啦&我能照顾好自己&画一个梦&哆啦A梦的神奇口袋&你是我的小天使&最动听的笑声&许个愿吧&阳光下的我们&智慧成语故事&名人成语故事&励志成语故事&神话成语故事&古诗启蒙&三字经&弟子规&千字文&十万个为什么&神奇的大自然&英语宝典&数学课堂&动物奇缘&安全教育", "分类名称替换词": "17&16&1&2&3&4&5&6&9&10&11&12&13&14&15&19&20&21&22&23&24&25&56&57&58&59&60&61&62&63&89&90&88&87&39&43&46&48&54&55&50&51&53&84", "筛选数据": {}, "筛选子分类名称": "", "筛选子分类替换词": "", "分类截取模式": "1", "分类列表数组规则": ".list-con&&.items", "分类片单是否Jsoup写法": "1", "分类片单标题": ".title&&Text", "分类片单链接": "a&&href", "分类片单图片": "mip-img&&src", "分类片单副标题": ".time&&Text", "分类片单链接加前缀": "https://www.tuxiaobei.com", "分类片单链接加后缀": "", "搜索请求头参数": "User-Agent$手机", "搜索链接": "https://www.tuxiaobei.com/search/index?key={wd}", "POST请求数据": "", "搜索截取模式": "1", "搜索列表数组规则": ".list-con&&.items", "搜索片单是否Jsoup写法": "1", "搜索片单图片": "mip-img&&src", "搜索片单标题": ".title&&Text", "搜索片单链接": "a&&href", "搜索片单副标题": ".time&&Text", "搜索片单链接加前缀": "https://www.tuxiaobei.com", "搜索片单链接加后缀": "", "链接是否直接播放": "2", "直接播放链接加前缀": "", "直接播放链接加后缀": "#isVideo=true#", "直接播放直链视频请求头": "", "线路列表数组规则": "", "线路标题": "", "播放列表数组规则": "body", "选集列表数组规则": ".video", "选集标题链接是否Jsoup写法": "1", "选集标题": "'立即播放'", "选集链接": "mip-search-video&&video-src", "是否反转选集序列": "0", "选集链接加前缀": "", "选集链接加后缀": "", "是否开启手动嗅探": "0", "手动嗅探视频链接关键词": ".mp4", "手动嗅探视频链接过滤词": ".html#=http"}