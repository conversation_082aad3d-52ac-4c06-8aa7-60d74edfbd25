package com.github.catvod.utils;

import java.util.HashMap;
import java.util.Map;

public class ZhuToPin {

    private static final Map<String, String> map = new HashMap<>();

    static {
        map.put("ㄅㄧㄝ", "bie");
        map.put("ㄅㄧㄠ", "biao");
        map.put("ㄅㄧㄢ", "bian");
        map.put("ㄅㄧㄣ", "bin");
        map.put("ㄅㄧㄥ", "bing");
        map.put("ㄅㄨ", "bu");
        map.put("ㄅㄚ", "ba");
        map.put("ㄅㄛ", "bo");
        map.put("ㄅㄞ", "bai");
        map.put("ㄅㄟ", "bei");
        map.put("ㄅㄠ", "bao");
        map.put("ㄅㄢ", "ban");
        map.put("ㄅㄣ", "ben");
        map.put("ㄅㄤ", "bang");
        map.put("ㄅㄥ", "beng");
        map.put("ㄅㄧ", "bi");
        map.put("ㄆㄧㄝ", "pie");
        map.put("ㄆㄧㄠ", "piao");
        map.put("ㄆㄧㄢ", "pian");
        map.put("ㄆㄧㄣ", "pin");
        map.put("ㄆㄧㄥ", "ping");
        map.put("ㄆㄨ", "pu");
        map.put("ㄆㄚ", "pa");
        map.put("ㄆㄛ", "po");
        map.put("ㄆㄞ", "pai");
        map.put("ㄆㄟ", "pei");
        map.put("ㄆㄠ", "pao");
        map.put("ㄆㄡ", "pou");
        map.put("ㄆㄢ", "pan");
        map.put("ㄆㄣ", "pen");
        map.put("ㄆㄤ", "pang");
        map.put("ㄆㄥ", "peng");
        map.put("ㄆㄧ", "pi");
        map.put("ㄇㄧㄝ", "mie");
        map.put("ㄇㄧㄠ", "miao");
        map.put("ㄇㄧㄡ", "miu");
        map.put("ㄇㄧㄢ", "mian");
        map.put("ㄇㄧㄣ", "min");
        map.put("ㄇㄨ", "mu");
        map.put("ㄇㄚ", "ma");
        map.put("ㄇㄛ", "mo");
        map.put("ㄇㄜ", "me");
        map.put("ㄇㄞ", "mai");
        map.put("ㄇㄟ", "mei");
        map.put("ㄇㄠ", "mao");
        map.put("ㄇㄡ", "mou");
        map.put("ㄇㄢ", "man");
        map.put("ㄇㄣ", "men");
        map.put("ㄇㄤ", "mang");
        map.put("ㄇㄥ", "meng");
        map.put("ㄇㄧ", "mi");
        map.put("ㄈㄚ", "fa");
        map.put("ㄈㄛ", "fo");
        map.put("ㄈㄜ", "fe");
        map.put("ㄈㄟ", "fei");
        map.put("ㄈㄡ", "fou");
        map.put("ㄈㄢ", "fan");
        map.put("ㄈㄣ", "fen");
        map.put("ㄈㄤ", "fang");
        map.put("ㄈㄥ", "feng");
        map.put("ㄈㄨ", "fu");
        map.put("ㄉㄧㄝ", "die");
        map.put("ㄉㄧㄠ", "diao");
        map.put("ㄉㄧㄡ", "diu");
        map.put("ㄉㄧㄢ", "dian");
        map.put("ㄉㄧㄥ", "ding");
        map.put("ㄉㄨㄛ", "duo");
        map.put("ㄉㄨㄟ", "dui");
        map.put("ㄉㄨㄢ", "duan");
        map.put("ㄉㄨㄣ", "dun");
        map.put("ㄉㄚ", "da");
        map.put("ㄉㄜ", "de");
        map.put("ㄉㄞ", "dai");
        map.put("ㄉㄟ", "dei");
        map.put("ㄉㄠ", "dao");
        map.put("ㄉㄡ", "dou");
        map.put("ㄉㄢ", "dan");
        map.put("ㄉㄣ", "den");
        map.put("ㄉㄤ", "dang");
        map.put("ㄉㄥ", "deng");
        map.put("ㄉㄧ", "di");
        map.put("ㄉㄨ", "du");
        map.put("ㄊㄧㄝ", "tie");
        map.put("ㄊㄧㄠ", "tiao");
        map.put("ㄊㄧㄢ", "tian");
        map.put("ㄊㄧㄥ", "ting");
        map.put("ㄊㄨㄛ", "tuo");
        map.put("ㄊㄨㄟ", "tui");
        map.put("ㄊㄨㄢ", "tuan");
        map.put("ㄊㄨㄣ", "tun");
        map.put("ㄊㄚ", "ta");
        map.put("ㄊㄜ", "te");
        map.put("ㄊㄞ", "tai");
        map.put("ㄊㄠ", "tao");
        map.put("ㄊㄡ", "tou");
        map.put("ㄊㄢ", "tan");
        map.put("ㄊㄤ", "tang");
        map.put("ㄊㄥ", "teng");
        map.put("ㄊㄧ", "ti");
        map.put("ㄊㄨ", "tu");
        map.put("ㄋㄧㄝ", "nie");
        map.put("ㄋㄧㄠ", "niao");
        map.put("ㄋㄧㄡ", "niu");
        map.put("ㄋㄧㄢ", "nian");
        map.put("ㄋㄧㄣ", "nin");
        map.put("ㄋㄧㄤ", "niang");
        map.put("ㄋㄧㄥ", "ning");
        map.put("ㄋㄨㄛ", "nuo");
        map.put("ㄋㄨㄢ", "nuan");
        map.put("ㄋㄨㄣ", "nun");
        map.put("ㄋㄨㄥ", "nung");
        map.put("ㄋㄚ", "na");
        map.put("ㄋㄜ", "ne");
        map.put("ㄋㄞ", "nai");
        map.put("ㄋㄟ", "nei");
        map.put("ㄋㄠ", "nao");
        map.put("ㄋㄡ", "nou");
        map.put("ㄋㄢ", "nan");
        map.put("ㄋㄣ", "nen");
        map.put("ㄋㄤ", "nang");
        map.put("ㄋㄥ", "neng");
        map.put("ㄋㄧ", "ni");
        map.put("ㄋㄨ", "nu");
        map.put("ㄌㄧㄚ", "lia");
        map.put("ㄌㄧㄝ", "lie");
        map.put("ㄌㄧㄠ", "liao");
        map.put("ㄌㄧㄡ", "liu");
        map.put("ㄌㄧㄢ", "lian");
        map.put("ㄌㄧㄣ", "lin");
        map.put("ㄌㄧㄤ", "liang");
        map.put("ㄌㄧㄥ", "ling");
        map.put("ㄌㄨㄛ", "luo");
        map.put("ㄌㄨㄢ", "luan");
        map.put("ㄌㄨㄣ", "lun");
        map.put("ㄌㄨㄥ", "lung");
        map.put("ㄌㄩ", "lv");
        map.put("ㄌㄩㄝ", "lve");
        map.put("ㄌㄚ", "la");
        map.put("ㄌㄛ", "lo");
        map.put("ㄌㄜ", "le");
        map.put("ㄌㄞ", "lai");
        map.put("ㄌㄟ", "lei");
        map.put("ㄌㄠ", "lao");
        map.put("ㄌㄡ", "lou");
        map.put("ㄌㄢ", "lan");
        map.put("ㄌㄣ", "len");
        map.put("ㄌㄤ", "lang");
        map.put("ㄌㄥ", "leng");
        map.put("ㄌㄧ", "li");
        map.put("ㄌㄨ", "lu");
        map.put("ㄍㄨㄚ", "gua");
        map.put("ㄍㄨㄛ", "guo");
        map.put("ㄍㄨㄞ", "guai");
        map.put("ㄍㄨㄟ", "gui");
        map.put("ㄍㄨㄢ", "guan");
        map.put("ㄍㄨㄣ", "gun");
        map.put("ㄍㄨㄤ", "guang");
        map.put("ㄍㄨㄥ", "gong");
        map.put("ㄍㄚ", "ga");
        map.put("ㄍㄜ", "ge");
        map.put("ㄍㄞ", "gai");
        map.put("ㄍㄟ", "gei");
        map.put("ㄍㄠ", "gao");
        map.put("ㄍㄡ", "gou");
        map.put("ㄍㄢ", "gan");
        map.put("ㄍㄣ", "gen");
        map.put("ㄍㄤ", "gang");
        map.put("ㄍㄥ", "geng");
        map.put("ㄍㄨ", "gu");
        map.put("ㄎㄨㄚ", "kua");
        map.put("ㄎㄨㄛ", "kuo");
        map.put("ㄎㄨㄞ", "kuai");
        map.put("ㄎㄨㄟ", "kui");
        map.put("ㄎㄨㄢ", "kuan");
        map.put("ㄎㄨㄣ", "kun");
        map.put("ㄎㄨㄤ", "kuang");
        map.put("ㄎㄨㄥ", "kong");
        map.put("ㄎㄚ", "ka");
        map.put("ㄎㄜ", "ke");
        map.put("ㄎㄞ", "kai");
        map.put("ㄎㄠ", "kao");
        map.put("ㄎㄡ", "kou");
        map.put("ㄎㄢ", "kan");
        map.put("ㄎㄣ", "ken");
        map.put("ㄎㄤ", "kang");
        map.put("ㄎㄥ", "keng");
        map.put("ㄎㄨ", "ku");
        map.put("ㄏㄨㄚ", "hua");
        map.put("ㄏㄨㄛ", "huo");
        map.put("ㄏㄨㄞ", "huai");
        map.put("ㄏㄨㄟ", "huai");
        map.put("ㄏㄨㄢ", "huan");
        map.put("ㄏㄨㄣ", "hun");
        map.put("ㄏㄨㄤ", "huang");
        map.put("ㄏㄨㄥ", "hong");
        map.put("ㄏㄚ", "ha");
        map.put("ㄏㄜ", "he");
        map.put("ㄏㄞ", "hai");
        map.put("ㄏㄟ", "hei");
        map.put("ㄏㄠ", "hao");
        map.put("ㄏㄡ", "hou");
        map.put("ㄏㄢ", "han");
        map.put("ㄏㄣ", "hen");
        map.put("ㄏㄤ", "hang");
        map.put("ㄏㄥ", "heng");
        map.put("ㄏㄨ", "hu");
        map.put("ㄐㄧㄚ", "jia");
        map.put("ㄐㄧㄝ", "jie");
        map.put("ㄐㄧㄠ", "jiao");
        map.put("ㄐㄧㄡ", "jiu");
        map.put("ㄐㄧㄢ", "jian");
        map.put("ㄐㄧㄣ", "jin");
        map.put("ㄐㄧㄤ", "jiang");
        map.put("ㄐㄧㄥ", "jing");
        map.put("ㄐㄩㄝ", "jue");
        map.put("ㄐㄩㄢ", "juan");
        map.put("ㄐㄩㄣ", "jun");
        map.put("ㄐㄩㄥ", "jiong");
        map.put("ㄐㄧ", "ji");
        map.put("ㄐㄩ", "ju");
        map.put("ㄑㄧㄚ", "qia");
        map.put("ㄑㄧㄝ", "qie");
        map.put("ㄑㄧㄠ", "qiao");
        map.put("ㄑㄧㄡ", "qiu");
        map.put("ㄑㄧㄢ", "qian");
        map.put("ㄑㄧㄣ", "qin");
        map.put("ㄑㄧㄤ", "qiang");
        map.put("ㄑㄧㄥ", "qing");
        map.put("ㄑㄩㄝ", "que");
        map.put("ㄑㄩㄢ", "quan");
        map.put("ㄑㄩㄣ", "qun");
        map.put("ㄑㄩㄥ", "qun");
        map.put("ㄑㄧ", "qi");
        map.put("ㄑㄩ", "qu");
        map.put("ㄒㄧㄚ", "xia");
        map.put("ㄒㄧㄝ", "xie");
        map.put("ㄒㄧㄠ", "xiao");
        map.put("ㄒㄧㄡ", "xiu");
        map.put("ㄒㄧㄢ", "xian");
        map.put("ㄒㄧㄣ", "xin");
        map.put("ㄒㄧㄤ", "xiang");
        map.put("ㄒㄧㄥ", "xing");
        map.put("ㄒㄩㄝ", "xue");
        map.put("ㄒㄩㄢ", "xuan");
        map.put("ㄒㄩㄣ", "xun");
        map.put("ㄒㄩㄥ", "xiong");
        map.put("ㄒㄧ", "xi");
        map.put("ㄒㄩ", "xu");
        map.put("ㄓㄨㄚ", "zhua");
        map.put("ㄓㄨㄛ", "zhuo");
        map.put("ㄓㄨㄞ", "zhuai");
        map.put("ㄓㄨㄟ", "zhuo");
        map.put("ㄓㄨㄢ", "zhuan");
        map.put("ㄓㄨㄣ", "zhuan");
        map.put("ㄓㄨㄤ", "zhuan");
        map.put("ㄓㄨㄥ", "zhong");
        map.put("ㄓㄚ", "zha");
        map.put("ㄓㄜ", "zhe");
        map.put("ㄓㄞ", "zhai");
        map.put("ㄓㄟ", "zhei");
        map.put("ㄓㄠ", "zhao");
        map.put("ㄓㄡ", "zhou");
        map.put("ㄓㄢ", "zhan");
        map.put("ㄓㄣ", "zhen");
        map.put("ㄓㄤ", "zhang");
        map.put("ㄓㄥ", "zheng");
        map.put("ㄓㄨ", "zhu");
        map.put("ㄔㄨㄚ", "chua");
        map.put("ㄔㄨㄛ", "chuo");
        map.put("ㄔㄨㄞ", "chuai");
        map.put("ㄔㄨㄟ", "chui");
        map.put("ㄔㄨㄢ", "chuan");
        map.put("ㄔㄨㄣ", "chun");
        map.put("ㄔㄨㄤ", "chuang");
        map.put("ㄔㄨㄥ", "chong");
        map.put("ㄔㄚ", "cha");
        map.put("ㄔㄜ", "che");
        map.put("ㄔㄞ", "chai");
        map.put("ㄔㄠ", "chao");
        map.put("ㄔㄡ", "chou");
        map.put("ㄔㄢ", "chan");
        map.put("ㄔㄣ", "chen");
        map.put("ㄔㄤ", "chang");
        map.put("ㄔㄥ", "cheng");
        map.put("ㄔㄨ", "chu");
        map.put("ㄕㄨㄚ", "shua");
        map.put("ㄕㄨㄛ", "shuo");
        map.put("ㄕㄨㄞ", "shuai");
        map.put("ㄕㄨㄟ", "shui");
        map.put("ㄕㄨㄢ", "shuan");
        map.put("ㄕㄨㄣ", "shun");
        map.put("ㄕㄨㄤ", "shuang");
        map.put("ㄕㄚ", "sha");
        map.put("ㄕㄜ", "she");
        map.put("ㄕㄞ", "shai");
        map.put("ㄕㄟ", "shei");
        map.put("ㄕㄠ", "shao");
        map.put("ㄕㄡ", "shou");
        map.put("ㄕㄢ", "shan");
        map.put("ㄕㄣ", "shen");
        map.put("ㄕㄤ", "shang");
        map.put("ㄕㄥ", "sheng");
        map.put("ㄕㄨ", "shu");
        map.put("ㄖㄨㄛ", "ruo");
        map.put("ㄖㄨㄟ", "rui");
        map.put("ㄖㄨㄢ", "ruan");
        map.put("ㄖㄨㄣ", "run");
        map.put("ㄖㄨㄥ", "rung");
        map.put("ㄖㄜ", "re");
        map.put("ㄖㄠ", "rao");
        map.put("ㄖㄡ", "rou");
        map.put("ㄖㄢ", "ran");
        map.put("ㄖㄣ", "ren");
        map.put("ㄖㄤ", "rang");
        map.put("ㄖㄥ", "reng");
        map.put("ㄖㄨ", "ru");
        map.put("ㄗㄨㄛ", "zuo");
        map.put("ㄗㄨㄟ", "zui");
        map.put("ㄗㄨㄢ", "zuan");
        map.put("ㄗㄨㄣ", "zun");
        map.put("ㄗㄨㄥ", "zong");
        map.put("ㄗㄚ", "za");
        map.put("ㄗㄜ", "ze");
        map.put("ㄗㄞ", "zai");
        map.put("ㄗㄟ", "zei");
        map.put("ㄗㄠ", "zao");
        map.put("ㄗㄡ", "zou");
        map.put("ㄗㄢ", "zan");
        map.put("ㄗㄣ", "zen");
        map.put("ㄗㄤ", "zang");
        map.put("ㄗㄥ", "zeng");
        map.put("ㄗㄨ", "zu");
        map.put("ㄘㄨㄛ", "cuo");
        map.put("ㄘㄨㄟ", "cui");
        map.put("ㄘㄨㄢ", "cuan");
        map.put("ㄘㄨㄣ", "cun");
        map.put("ㄘㄨㄥ", "cong");
        map.put("ㄘㄚ", "ca");
        map.put("ㄘㄜ", "ce");
        map.put("ㄘㄞ", "cai");
        map.put("ㄘㄠ", "cao");
        map.put("ㄘㄡ", "cou");
        map.put("ㄘㄢ", "can");
        map.put("ㄘㄣ", "cen");
        map.put("ㄘㄤ", "cang");
        map.put("ㄘㄥ", "ceng");
        map.put("ㄘㄨ", "cu");
        map.put("ㄙㄨㄛ", "suo");
        map.put("ㄙㄨㄟ", "sui");
        map.put("ㄙㄨㄢ", "suan");
        map.put("ㄙㄨㄣ", "sun");
        map.put("ㄙㄨㄥ", "song");
        map.put("ㄙㄚ", "sa");
        map.put("ㄙㄜ", "se");
        map.put("ㄙㄞ", "sai");
        map.put("ㄙㄟ", "sei");
        map.put("ㄙㄠ", "sao");
        map.put("ㄙㄡ", "sou");
        map.put("ㄙㄢ", "san");
        map.put("ㄙㄣ", "sen");
        map.put("ㄙㄤ", "sang");
        map.put("ㄙㄥ", "seng");
        map.put("ㄙㄨ", "su");
        map.put("ㄧㄚ", "ya");
        map.put("ㄧㄛ", "yo");
        map.put("ㄧㄝ", "ye");
        map.put("ㄧㄞ", "yai");
        map.put("ㄧㄠ", "yao");
        map.put("ㄧㄡ", "you");
        map.put("ㄧㄢ", "yan");
        map.put("ㄧㄣ", "yin");
        map.put("ㄧㄤ", "yang");
        map.put("ㄧㄥ", "ying");
        map.put("ㄨㄚ", "wa");
        map.put("ㄨㄛ", "wo");
        map.put("ㄨㄞ", "wai");
        map.put("ㄨㄟ", "wei");
        map.put("ㄨㄢ", "wan");
        map.put("ㄨㄣ", "wen");
        map.put("ㄨㄤ", "wang");
        map.put("ㄨㄥ", "weng");
        map.put("ㄩㄝ", "yue");
        map.put("ㄩㄢ", "yuan");
        map.put("ㄩㄣ", "yun");
        map.put("ㄩㄥ", "yong");
        map.put("ㄅ", "b");
        map.put("ㄆ", "p");
        map.put("ㄇ", "m");
        map.put("ㄈ", "f");
        map.put("ㄉ", "d");
        map.put("ㄊ", "t");
        map.put("ㄋ", "n");
        map.put("ㄌ", "l");
        map.put("ㄍ", "g");
        map.put("ㄎ", "k");
        map.put("ㄏ", "h");
        map.put("ㄐ", "j");
        map.put("ㄑ", "q");
        map.put("ㄒ", "x");
        map.put("ㄓ", "zh");
        map.put("ㄔ", "ch");
        map.put("ㄕ", "sh");
        map.put("ㄖ", "r");
        map.put("ㄗ", "z");
        map.put("ㄘ", "c");
        map.put("ㄙ", "s");
        map.put("ㄧ", "yi");
        map.put("ㄨ", "wu");
        map.put("ㄩ", "yu");
        map.put("ㄚ", "a");
        map.put("ㄛ", "o");
        map.put("ㄜ", "e");
        map.put("ㄝ", "eh");
        map.put("ㄞ", "ai");
        map.put("ㄟ", "ei");
        map.put("ㄠ", "ao");
        map.put("ㄡ", "ou");
        map.put("ㄢ", "an");
        map.put("ㄣ", "en");
        map.put("ㄤ", "ang");
        map.put("ㄥ", "eng");
        map.put("ㄦ", "er");
    }

    public static String get(String text) {
        StringBuilder sb = new StringBuilder();
        String[] splits = text.split("↩");
        if (splits.length > 1) findSplit(sb, splits);
        else findSingle(sb, text);
        return sb.toString();
    }

    private static void findSplit(StringBuilder sb, String[] splits) {
        for (String split : splits) {
            String pinyin = map.get(split);
            sb.append(pinyin != null ? pinyin : split);
        }
    }

    private static void findSingle(StringBuilder sb, String text) {
        for (char chars : text.toCharArray()) {
            String pinyin = map.get(String.valueOf(chars));
            sb.append(pinyin != null ? pinyin : chars);
        }
    }
}
