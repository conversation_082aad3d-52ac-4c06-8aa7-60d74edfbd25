{"version": 3, "file": "header.js", "sources": ["../../../../../../packages/components/table-v2/src/header.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { columns } from './common'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\n\nconst requiredNumberType = {\n  type: Number,\n  required: true,\n} as const\n\nexport const tableV2HeaderProps = buildProps({\n  class: String,\n  columns,\n  fixedHeaderData: {\n    type: definePropType<any[]>(Array),\n  },\n  headerData: {\n    type: definePropType<any[]>(Array),\n    required: true,\n  },\n  headerHeight: {\n    type: definePropType<number | number[]>([Number, Array]),\n    default: 50,\n  },\n  rowWidth: requiredNumberType,\n  rowHeight: {\n    type: Number,\n    default: 50,\n  },\n  height: requiredNumberType,\n  width: requiredNumberType,\n} as const)\n\nexport type TableV2HeaderProps = ExtractPropTypes<typeof tableV2HeaderProps>\nexport type TableV2HeaderPropsPublic = __ExtractPublicPropTypes<\n  typeof tableV2HeaderProps\n>\n"], "names": ["buildProps", "columns", "definePropType"], "mappings": ";;;;;;;AAEA,MAAM,kBAAkB,GAAG;AAC3B,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,QAAQ,EAAE,IAAI;AAChB,CAAC,CAAC;AACU,MAAC,kBAAkB,GAAGA,kBAAU,CAAC;AAC7C,EAAE,KAAK,EAAE,MAAM;AACf,WAAEC,cAAO;AACT,EAAE,eAAe,EAAE;AACnB,IAAI,IAAI,EAAEC,sBAAc,CAAC,KAAK,CAAC;AAC/B,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAEA,sBAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAEA,sBAAc,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACzC,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,QAAQ,EAAE,kBAAkB;AAC9B,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,MAAM,EAAE,kBAAkB;AAC5B,EAAE,KAAK,EAAE,kBAAkB;AAC3B,CAAC;;;;"}