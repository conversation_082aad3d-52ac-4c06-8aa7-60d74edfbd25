{% extends 'base.html' %}

{% block title %}TVBox助手 - 资源管理{% endblock %}

{% block content %}
<div class="container py-4">
    <h1 class="mb-4">
        <i class="fas fa-download me-2"></i> 资源管理
    </h1>
    
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">下载资源管理</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> 此页面用于管理从配置文件中下载的资源。
                    </div>
                    
                    <div id="resourcesContent">
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status"></div>
                            <div class="mt-2">扫描下载资源...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // 模拟加载资源列表
    setTimeout(function() {
        // 生产环境中应该通过API获取资源列表
        const downloadPath = './downloads';
        let html = '';
        
        // 如果没有下载资源
        html = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-circle me-2"></i> 暂无下载的资源。请先在<a href="${window.location.origin}/configs" class="alert-link">配置管理</a>页面处理配置文件。
            </div>
        `;
        
        // 资源类型列表（实际应从后端API获取）
        const resourceTypes = [
            { type: 'spider', name: '爬虫规则', icon: 'fa-spider' },
            { type: 'lives', name: '直播源', icon: 'fa-tv' },
            { type: 'sites', name: '站点配置', icon: 'fa-globe' },
            { type: 'parses', name: '解析器', icon: 'fa-code' },
            { type: 'wallpaper', name: '壁纸', icon: 'fa-image' },
            { type: 'ads', name: '广告过滤', icon: 'fa-ban' },
            { type: 'rules', name: '规则文件', icon: 'fa-scroll' }
        ];
        
        // 创建资源类型标签页
        html += '<ul class="nav nav-tabs" id="resourceTabs" role="tablist">';
        resourceTypes.forEach((type, index) => {
            html += `
                <li class="nav-item" role="presentation">
                    <button class="nav-link ${index === 0 ? 'active' : ''}" id="${type.type}-tab" 
                        data-bs-toggle="tab" data-bs-target="#${type.type}-tab-pane" type="button" 
                        role="tab" aria-controls="${type.type}-tab-pane" aria-selected="${index === 0 ? 'true' : 'false'}">
                        <i class="fas ${type.icon} me-1"></i> ${type.name}
                    </button>
                </li>
            `;
        });
        html += '</ul>';
        
        // 创建标签页内容
        html += '<div class="tab-content py-3" id="resourceTabsContent">';
        resourceTypes.forEach((type, index) => {
            html += `
                <div class="tab-pane fade ${index === 0 ? 'show active' : ''}" id="${type.type}-tab-pane" role="tabpanel" 
                    aria-labelledby="${type.type}-tab" tabindex="0">
                    <div class="alert alert-info">
                        暂无${type.name}资源或正在加载中...
                    </div>
                </div>
            `;
        });
        html += '</div>';
        
        $('#resourcesContent').html(html);
    }, 1000);
});
</script>
{% endblock %} 