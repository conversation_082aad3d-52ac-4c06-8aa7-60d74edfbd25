{"version": 3, "file": "vnode.mjs", "sources": ["../../../../../packages/utils/vue/vnode.ts"], "sourcesContent": ["import {\n  Comment,\n  Fragment,\n  Text,\n  createBlock,\n  createCommentVNode,\n  isVNode,\n  openBlock,\n} from 'vue'\nimport { camelize } from '../strings'\nimport { isArray } from '../types'\nimport { hasOwn } from '../objects'\nimport { debugWarn } from '../error'\n\nimport type {\n  VNode,\n  VNodeArrayChildren,\n  VNodeChild,\n  VNodeNormalizedChildren,\n} from 'vue'\n\nconst SCOPE = 'utils/vue/vnode'\n\nexport enum PatchFlags {\n  TEXT = 1,\n  CLASS = 2,\n  STYLE = 4,\n  PROPS = 8,\n  FULL_PROPS = 16,\n  HYDRATE_EVENTS = 32,\n  STABLE_FRAGMENT = 64,\n  KEYED_FRAGMENT = 128,\n  UNKEYED_FRAGMENT = 256,\n  NEED_PATCH = 512,\n  DYNAMIC_SLOTS = 1024,\n  HOISTED = -1,\n  BAIL = -2,\n}\n\nexport type VNodeChildAtom = Exclude<VNodeChild, Array<any>>\nexport type RawSlots = Exclude<\n  VNodeNormalized<PERSON>hildren,\n  Array<any> | null | string\n>\n\nexport function isFragment(node: VNode): boolean\nexport function isFragment(node: unknown): node is VNode\nexport function isFragment(node: unknown): node is VNode {\n  return isVNode(node) && node.type === Fragment\n}\n\nexport function isText(node: VNode): boolean\nexport function isText(node: unknown): node is VNode\nexport function isText(node: unknown): node is VNode {\n  return isVNode(node) && node.type === Text\n}\n\nexport function isComment(node: VNode): boolean\nexport function isComment(node: unknown): node is VNode\nexport function isComment(node: unknown): node is VNode {\n  return isVNode(node) && node.type === Comment\n}\n\nconst TEMPLATE = 'template'\nexport function isTemplate(node: VNode): boolean\nexport function isTemplate(node: unknown): node is VNode\nexport function isTemplate(node: unknown): node is VNode {\n  return isVNode(node) && node.type === TEMPLATE\n}\n\n/**\n * determine if the element is a valid element type rather than fragments and comment e.g. <template> v-if\n * @param node {VNode} node to be tested\n */\nexport function isValidElementNode(node: VNode): boolean\nexport function isValidElementNode(node: unknown): node is VNode\nexport function isValidElementNode(node: unknown): node is VNode {\n  return isVNode(node) && !isFragment(node) && !isComment(node)\n}\n\n/**\n * get a valid child node (not fragment nor comment)\n * @param node {VNode} node to be searched\n * @param depth {number} depth to be searched\n */\nfunction getChildren(\n  node: VNodeNormalizedChildren | VNodeChild,\n  depth: number\n): VNodeNormalizedChildren | VNodeChild {\n  if (isComment(node)) return\n  if (isFragment(node) || isTemplate(node)) {\n    return depth > 0 ? getFirstValidNode(node.children, depth - 1) : undefined\n  }\n  return node\n}\n\nexport const getFirstValidNode = (\n  nodes: VNodeNormalizedChildren,\n  maxDepth = 3\n) => {\n  if (isArray(nodes)) {\n    return getChildren(nodes[0], maxDepth)\n  } else {\n    return getChildren(nodes, maxDepth)\n  }\n}\n\nexport function renderIf(\n  condition: boolean,\n  ...args: Parameters<typeof createBlock>\n) {\n  return condition ? renderBlock(...args) : createCommentVNode('v-if', true)\n}\n\nexport function renderBlock(...args: Parameters<typeof createBlock>) {\n  return openBlock(), createBlock(...args)\n}\n\nexport const getNormalizedProps = (node: VNode) => {\n  if (!isVNode(node)) {\n    debugWarn(SCOPE, '[getNormalizedProps] must be a VNode')\n    return {}\n  }\n\n  const raw = node.props || {}\n  const type = (isVNode(node.type) ? node.type.props : undefined) || {}\n  const props: Record<string, any> = {}\n\n  Object.keys(type).forEach((key) => {\n    if (hasOwn(type[key], 'default')) {\n      props[key] = type[key].default\n    }\n  })\n\n  Object.keys(raw).forEach((key) => {\n    props[camelize(key)] = raw[key]\n  })\n\n  return props\n}\n\nexport const ensureOnlyChild = (children: VNodeArrayChildren | undefined) => {\n  if (!isArray(children) || children.length > 1) {\n    throw new Error('expect to receive a single Vue element child')\n  }\n  return children[0]\n}\n\nexport type FlattenVNodes = Array<VNodeChildAtom | RawSlots>\n\nexport const flattedChildren = (\n  children: FlattenVNodes | VNode | VNodeNormalizedChildren\n): FlattenVNodes => {\n  const vNodes = isArray(children) ? children : [children]\n  const result: FlattenVNodes = []\n\n  vNodes.forEach((child) => {\n    if (isArray(child)) {\n      result.push(...flattedChildren(child))\n    } else if (isVNode(child) && child.component?.subTree) {\n      result.push(child, ...flattedChildren(child.component.subTree))\n    } else if (isVNode(child) && isArray(child.children)) {\n      result.push(...flattedChildren(child.children))\n    } else if (isVNode(child) && child.shapeFlag === 2) {\n      // @ts-ignore\n      result.push(...flattedChildren(child.type()))\n    } else {\n      result.push(child)\n    }\n  })\n  return result\n}\n"], "names": [], "mappings": ";;;;AAaA,MAAM,KAAK,GAAG,iBAAiB,CAAC;AACtB,IAAC,UAAU,mBAAmB,CAAC,CAAC,WAAW,KAAK;AAC1D,EAAE,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;AAChD,EAAE,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;AAClD,EAAE,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;AAClD,EAAE,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;AAClD,EAAE,WAAW,CAAC,WAAW,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,GAAG,YAAY,CAAC;AAC7D,EAAE,WAAW,CAAC,WAAW,CAAC,gBAAgB,CAAC,GAAG,EAAE,CAAC,GAAG,gBAAgB,CAAC;AACrE,EAAE,WAAW,CAAC,WAAW,CAAC,iBAAiB,CAAC,GAAG,EAAE,CAAC,GAAG,iBAAiB,CAAC;AACvE,EAAE,WAAW,CAAC,WAAW,CAAC,gBAAgB,CAAC,GAAG,GAAG,CAAC,GAAG,gBAAgB,CAAC;AACtE,EAAE,WAAW,CAAC,WAAW,CAAC,kBAAkB,CAAC,GAAG,GAAG,CAAC,GAAG,kBAAkB,CAAC;AAC1E,EAAE,WAAW,CAAC,WAAW,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,GAAG,YAAY,CAAC;AAC9D,EAAE,WAAW,CAAC,WAAW,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC,GAAG,eAAe,CAAC;AACrE,EAAE,WAAW,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;AACvD,EAAE,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;AACjD,EAAE,OAAO,WAAW,CAAC;AACrB,CAAC,EAAE,UAAU,IAAI,EAAE,EAAE;AACd,SAAS,UAAU,CAAC,IAAI,EAAE;AACjC,EAAE,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC;AACjD,CAAC;AACM,SAAS,MAAM,CAAC,IAAI,EAAE;AAC7B,EAAE,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;AAC7C,CAAC;AACM,SAAS,SAAS,CAAC,IAAI,EAAE;AAChC,EAAE,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC;AAChD,CAAC;AACD,MAAM,QAAQ,GAAG,UAAU,CAAC;AACrB,SAAS,UAAU,CAAC,IAAI,EAAE;AACjC,EAAE,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC;AACjD,CAAC;AACM,SAAS,kBAAkB,CAAC,IAAI,EAAE;AACzC,EAAE,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AAChE,CAAC;AACD,SAAS,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE;AAClC,EAAE,IAAI,SAAS,CAAC,IAAI,CAAC;AACrB,IAAI,OAAO;AACX,EAAE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE;AAC5C,IAAI,OAAO,KAAK,GAAG,CAAC,GAAG,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;AAC5E,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd,CAAC;AACW,MAAC,iBAAiB,GAAG,CAAC,KAAK,EAAE,QAAQ,GAAG,CAAC,KAAK;AAC1D,EAAE,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;AACtB,IAAI,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AAC3C,GAAG,MAAM;AACT,IAAI,OAAO,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AACxC,GAAG;AACH,EAAE;AACK,SAAS,QAAQ,CAAC,SAAS,EAAE,GAAG,IAAI,EAAE;AAC7C,EAAE,OAAO,SAAS,GAAG,WAAW,CAAC,GAAG,IAAI,CAAC,GAAG,kBAAkB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAC7E,CAAC;AACM,SAAS,WAAW,CAAC,GAAG,IAAI,EAAE;AACrC,EAAE,OAAO,SAAS,EAAE,EAAE,WAAW,CAAC,GAAG,IAAI,CAAC,CAAC;AAC3C,CAAC;AACW,MAAC,kBAAkB,GAAG,CAAC,IAAI,KAAK;AAC5C,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AACtB,IAAI,SAAS,CAAC,KAAK,EAAE,sCAAsC,CAAC,CAAC;AAC7D,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;AAC/B,EAAE,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;AACrE,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;AACnB,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AACrC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,EAAE;AACtC,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC;AACrC,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AACpC,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AACpC,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,KAAK,CAAC;AACf,EAAE;AACU,MAAC,eAAe,GAAG,CAAC,QAAQ,KAAK;AAC7C,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;AACjD,IAAI,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;AACpE,GAAG;AACH,EAAE,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC;AACrB,EAAE;AACU,MAAC,eAAe,GAAG,CAAC,QAAQ,KAAK;AAC7C,EAAE,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,QAAQ,GAAG,CAAC,QAAQ,CAAC,CAAC;AAC3D,EAAE,MAAM,MAAM,GAAG,EAAE,CAAC;AACpB,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK;AAC5B,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;AACxB,MAAM,MAAM,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;AAC7C,KAAK,MAAM,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,EAAE;AACzF,MAAM,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,eAAe,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;AACtE,KAAK,MAAM,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;AAC1D,MAAM,MAAM,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;AACtD,KAAK,MAAM,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,SAAS,KAAK,CAAC,EAAE;AACxD,MAAM,MAAM,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AACpD,KAAK,MAAM;AACX,MAAM,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACzB,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,MAAM,CAAC;AAChB;;;;"}