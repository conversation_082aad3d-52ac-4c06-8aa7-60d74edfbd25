{"version": 3, "names": ["_index", "require", "isMemberExpressionLike", "node", "isMemberExpression", "isMetaProperty", "matchesPattern", "member", "match", "allowPartial", "parts", "Array", "isArray", "split", "nodes", "_object", "object", "meta", "push", "property", "length", "i", "j", "value", "isIdentifier", "name", "isStringLiteral", "isThisExpression", "is<PERSON><PERSON><PERSON>", "isPrivateName", "id"], "sources": ["../../src/validators/matchesPattern.ts"], "sourcesContent": ["import {\n  isIdentifier,\n  isMetaProperty,\n  isMemberExpression,\n  isPrivateName,\n  isString<PERSON>iteral,\n  isSuper,\n  isThisExpression,\n} from \"./generated/index.ts\";\nimport type * as t from \"../index.ts\";\n\nfunction isMemberExpressionLike(\n  node: t.Node,\n): node is t.MemberExpression | t.MetaProperty {\n  return isMemberExpression(node) || isMetaProperty(node);\n}\n\n/**\n * Determines whether or not the input node `member` matches the\n * input `match`.\n *\n * For example, given the match `React.createClass` it would match the\n * parsed nodes of `React.createClass` and `React[\"createClass\"]`.\n */\nexport default function matchesPattern(\n  member: t.Node | null | undefined,\n  match: string | string[],\n  allowPartial?: boolean,\n): boolean {\n  // not a member expression\n  if (!isMemberExpressionLike(member)) return false;\n\n  const parts = Array.isArray(match) ? match : match.split(\".\");\n  const nodes = [];\n\n  let node;\n  for (\n    node = member;\n    isMemberExpressionLike(node);\n    node = (node as t.MemberExpression).object ?? (node as t.MetaProperty).meta\n  ) {\n    nodes.push(node.property);\n  }\n  nodes.push(node);\n\n  if (nodes.length < parts.length) return false;\n  if (!allowPartial && nodes.length > parts.length) return false;\n\n  for (let i = 0, j = nodes.length - 1; i < parts.length; i++, j--) {\n    const node = nodes[j];\n    let value;\n    if (isIdentifier(node)) {\n      value = node.name;\n    } else if (isStringLiteral(node)) {\n      value = node.value;\n    } else if (isThisExpression(node)) {\n      value = \"this\";\n    } else if (isSuper(node)) {\n      value = \"super\";\n    } else if (isPrivateName(node)) {\n      value = \"#\" + node.id.name;\n    } else {\n      return false;\n    }\n\n    if (parts[i] !== value) return false;\n  }\n\n  return true;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAWA,SAASC,sBAAsBA,CAC7BC,IAAY,EACiC;EAC7C,OAAO,IAAAC,yBAAkB,EAACD,IAAI,CAAC,IAAI,IAAAE,qBAAc,EAACF,IAAI,CAAC;AACzD;AASe,SAASG,cAAcA,CACpCC,MAAiC,EACjCC,KAAwB,EACxBC,YAAsB,EACb;EAET,IAAI,CAACP,sBAAsB,CAACK,MAAM,CAAC,EAAE,OAAO,KAAK;EAEjD,MAAMG,KAAK,GAAGC,KAAK,CAACC,OAAO,CAACJ,KAAK,CAAC,GAAGA,KAAK,GAAGA,KAAK,CAACK,KAAK,CAAC,GAAG,CAAC;EAC7D,MAAMC,KAAK,GAAG,EAAE;EAEhB,IAAIX,IAAI;EACR,KACEA,IAAI,GAAGI,MAAM,EACbL,sBAAsB,CAACC,IAAI,CAAC,EAC5BA,IAAI,IAAAY,OAAA,GAAIZ,IAAI,CAAwBa,MAAM,YAAAD,OAAA,GAAKZ,IAAI,CAAoBc,IAAI,EAC3E;IAAA,IAAAF,OAAA;IACAD,KAAK,CAACI,IAAI,CAACf,IAAI,CAACgB,QAAQ,CAAC;EAC3B;EACAL,KAAK,CAACI,IAAI,CAACf,IAAI,CAAC;EAEhB,IAAIW,KAAK,CAACM,MAAM,GAAGV,KAAK,CAACU,MAAM,EAAE,OAAO,KAAK;EAC7C,IAAI,CAACX,YAAY,IAAIK,KAAK,CAACM,MAAM,GAAGV,KAAK,CAACU,MAAM,EAAE,OAAO,KAAK;EAE9D,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGR,KAAK,CAACM,MAAM,GAAG,CAAC,EAAEC,CAAC,GAAGX,KAAK,CAACU,MAAM,EAAEC,CAAC,EAAE,EAAEC,CAAC,EAAE,EAAE;IAChE,MAAMnB,IAAI,GAAGW,KAAK,CAACQ,CAAC,CAAC;IACrB,IAAIC,KAAK;IACT,IAAI,IAAAC,mBAAY,EAACrB,IAAI,CAAC,EAAE;MACtBoB,KAAK,GAAGpB,IAAI,CAACsB,IAAI;IACnB,CAAC,MAAM,IAAI,IAAAC,sBAAe,EAACvB,IAAI,CAAC,EAAE;MAChCoB,KAAK,GAAGpB,IAAI,CAACoB,KAAK;IACpB,CAAC,MAAM,IAAI,IAAAI,uBAAgB,EAACxB,IAAI,CAAC,EAAE;MACjCoB,KAAK,GAAG,MAAM;IAChB,CAAC,MAAM,IAAI,IAAAK,cAAO,EAACzB,IAAI,CAAC,EAAE;MACxBoB,KAAK,GAAG,OAAO;IACjB,CAAC,MAAM,IAAI,IAAAM,oBAAa,EAAC1B,IAAI,CAAC,EAAE;MAC9BoB,KAAK,GAAG,GAAG,GAAGpB,IAAI,CAAC2B,EAAE,CAACL,IAAI;IAC5B,CAAC,MAAM;MACL,OAAO,KAAK;IACd;IAEA,IAAIf,KAAK,CAACW,CAAC,CAAC,KAAKE,KAAK,EAAE,OAAO,KAAK;EACtC;EAEA,OAAO,IAAI;AACb", "ignoreList": []}