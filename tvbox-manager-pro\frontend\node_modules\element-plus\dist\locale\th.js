/*! Element Plus v2.10.4 */

(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
  typeof define === 'function' && define.amd ? define(factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.ElementPlusLocaleTh = factory());
})(this, (function () { 'use strict';

  var th = {
    name: "th",
    el: {
      breadcrumb: {
        label: "Breadcrumb"
      },
      colorpicker: {
        confirm: "\u0E15\u0E01\u0E25\u0E07",
        clear: "\u0E25\u0E49\u0E32\u0E07\u0E02\u0E49\u0E2D\u0E21\u0E39\u0E25"
      },
      datepicker: {
        now: "\u0E15\u0E2D\u0E19\u0E19\u0E35\u0E49",
        today: "\u0E27\u0E31\u0E19\u0E19\u0E35\u0E49",
        cancel: "\u0E22\u0E01\u0E40\u0E25\u0E34\u0E01",
        clear: "\u0E25\u0E49\u0E32\u0E07\u0E02\u0E49\u0E2D\u0E21\u0E39\u0E25",
        confirm: "\u0E15\u0E01\u0E25\u0E07",
        selectDate: "\u0E40\u0E25\u0E37\u0E2D\u0E01\u0E27\u0E31\u0E19\u0E17\u0E35\u0E48",
        selectTime: "\u0E40\u0E25\u0E37\u0E2D\u0E01\u0E40\u0E27\u0E25\u0E32",
        startDate: "\u0E27\u0E31\u0E19\u0E17\u0E35\u0E48\u0E40\u0E23\u0E34\u0E48\u0E21\u0E15\u0E49\u0E19",
        startTime: "\u0E40\u0E27\u0E25\u0E32\u0E40\u0E23\u0E34\u0E48\u0E21\u0E15\u0E49\u0E19",
        endDate: "\u0E27\u0E31\u0E19\u0E17\u0E35\u0E48\u0E2A\u0E34\u0E49\u0E19\u0E2A\u0E38\u0E14",
        endTime: "\u0E40\u0E27\u0E25\u0E32\u0E2A\u0E34\u0E49\u0E19\u0E2A\u0E38\u0E14",
        prevYear: "\u0E1B\u0E35\u0E01\u0E48\u0E2D\u0E19\u0E2B\u0E19\u0E49\u0E32",
        nextYear: "\u0E1B\u0E35\u0E16\u0E31\u0E14\u0E44\u0E1B",
        prevMonth: "\u0E40\u0E14\u0E37\u0E2D\u0E19\u0E01\u0E48\u0E2D\u0E19\u0E2B\u0E19\u0E49\u0E32",
        nextMonth: "\u0E40\u0E14\u0E37\u0E2D\u0E19\u0E16\u0E31\u0E14\u0E44\u0E1B",
        year: "\u0E1B\u0E35",
        month1: "\u0E21\u0E01\u0E23\u0E32\u0E04\u0E21",
        month2: "\u0E01\u0E38\u0E21\u0E20\u0E32\u0E1E\u0E31\u0E19\u0E18\u0E4C",
        month3: "\u0E21\u0E35\u0E19\u0E32\u0E04\u0E21",
        month4: "\u0E40\u0E21\u0E29\u0E32\u0E22\u0E19",
        month5: "\u0E1E\u0E24\u0E29\u0E20\u0E32\u0E04\u0E21",
        month6: "\u0E21\u0E34\u0E16\u0E38\u0E19\u0E32\u0E22\u0E19",
        month7: "\u0E01\u0E23\u0E01\u0E0E\u0E32\u0E04\u0E21",
        month8: "\u0E2A\u0E34\u0E07\u0E2B\u0E32\u0E04\u0E21",
        month9: "\u0E01\u0E31\u0E19\u0E22\u0E32\u0E22\u0E19",
        month10: "\u0E15\u0E38\u0E25\u0E32\u0E04\u0E21",
        month11: "\u0E1E\u0E24\u0E28\u0E08\u0E34\u0E01\u0E32\u0E22\u0E19",
        month12: "\u0E18\u0E31\u0E19\u0E27\u0E32\u0E04\u0E21",
        weeks: {
          sun: "\u0E2D\u0E32",
          mon: "\u0E08",
          tue: "\u0E2D",
          wed: "\u0E1E",
          thu: "\u0E1E\u0E24",
          fri: "\u0E28",
          sat: "\u0E2A"
        },
        months: {
          jan: "\u0E21.\u0E04.",
          feb: "\u0E01.\u0E1E.",
          mar: "\u0E21\u0E35.\u0E04.",
          apr: "\u0E40\u0E21.\u0E22.",
          may: "\u0E1E.\u0E04.",
          jun: "\u0E21\u0E34.\u0E22.",
          jul: "\u0E01.\u0E04.",
          aug: "\u0E2A.\u0E04.",
          sep: "\u0E01.\u0E22.",
          oct: "\u0E15.\u0E04.",
          nov: "\u0E1E.\u0E22.",
          dec: "\u0E18.\u0E04."
        }
      },
      select: {
        loading: "\u0E01\u0E33\u0E25\u0E31\u0E07\u0E42\u0E2B\u0E25\u0E14",
        noMatch: "\u0E44\u0E21\u0E48\u0E1E\u0E1A\u0E02\u0E49\u0E2D\u0E21\u0E39\u0E25\u0E17\u0E35\u0E48\u0E15\u0E23\u0E07\u0E01\u0E31\u0E19",
        noData: "\u0E44\u0E21\u0E48\u0E1E\u0E1A\u0E02\u0E49\u0E2D\u0E21\u0E39\u0E25",
        placeholder: "\u0E40\u0E25\u0E37\u0E2D\u0E01"
      },
      mention: {
        loading: "\u0E01\u0E33\u0E25\u0E31\u0E07\u0E42\u0E2B\u0E25\u0E14"
      },
      cascader: {
        noMatch: "\u0E44\u0E21\u0E48\u0E1E\u0E1A\u0E02\u0E49\u0E2D\u0E21\u0E39\u0E25\u0E17\u0E35\u0E48\u0E15\u0E23\u0E07\u0E01\u0E31\u0E19",
        loading: "\u0E01\u0E33\u0E25\u0E31\u0E07\u0E42\u0E2B\u0E25\u0E14",
        placeholder: "\u0E40\u0E25\u0E37\u0E2D\u0E01",
        noData: "\u0E44\u0E21\u0E48\u0E1E\u0E1A\u0E02\u0E49\u0E2D\u0E21\u0E39\u0E25"
      },
      pagination: {
        goto: "\u0E44\u0E1B\u0E17\u0E35\u0E48",
        pagesize: "/\u0E2B\u0E19\u0E49\u0E32",
        total: "\u0E17\u0E31\u0E49\u0E07\u0E2B\u0E21\u0E14 {total}",
        pageClassifier: "",
        page: "Page",
        prev: "Go to previous page",
        next: "Go to next page",
        currentPage: "page {pager}",
        prevPages: "Previous {pager} pages",
        nextPages: "Next {pager} pages"
      },
      messagebox: {
        title: "\u0E02\u0E49\u0E2D\u0E04\u0E27\u0E32\u0E21",
        confirm: "\u0E15\u0E01\u0E25\u0E07",
        cancel: "\u0E22\u0E01\u0E40\u0E25\u0E34\u0E01",
        error: "\u0E04\u0E38\u0E13\u0E1B\u0E49\u0E2D\u0E19\u0E02\u0E49\u0E2D\u0E21\u0E39\u0E25\u0E44\u0E21\u0E48\u0E16\u0E39\u0E01\u0E15\u0E49\u0E2D\u0E07"
      },
      upload: {
        deleteTip: '\u0E01\u0E14\u0E1B\u0E38\u0E48\u0E21 "\u0E25\u0E1A" \u0E40\u0E1E\u0E37\u0E48\u0E2D\u0E25\u0E1A\u0E2D\u0E2D\u0E01',
        delete: "\u0E25\u0E1A",
        preview: "\u0E15\u0E31\u0E27\u0E2D\u0E22\u0E48\u0E32\u0E07",
        continue: "\u0E17\u0E33\u0E15\u0E48\u0E2D"
      },
      table: {
        emptyText: "\u0E44\u0E21\u0E48\u0E1E\u0E1A\u0E02\u0E49\u0E2D\u0E21\u0E39\u0E25",
        confirmFilter: "\u0E22\u0E37\u0E19\u0E22\u0E31\u0E19",
        resetFilter: "\u0E23\u0E35\u0E40\u0E0B\u0E47\u0E15",
        clearFilter: "\u0E17\u0E31\u0E49\u0E07\u0E2B\u0E21\u0E14",
        sumText: "\u0E23\u0E27\u0E21"
      },
      tour: {
        next: "\u0E16\u0E31\u0E14\u0E44\u0E1B",
        previous: "\u0E22\u0E49\u0E2D\u0E19\u0E01\u0E25\u0E31\u0E1A",
        finish: "\u0E40\u0E2A\u0E23\u0E47\u0E08\u0E2A\u0E34\u0E49\u0E19"
      },
      tree: {
        emptyText: "\u0E44\u0E21\u0E48\u0E1E\u0E1A\u0E02\u0E49\u0E2D\u0E21\u0E39\u0E25"
      },
      transfer: {
        noMatch: "\u0E44\u0E21\u0E48\u0E1E\u0E1A\u0E02\u0E49\u0E2D\u0E21\u0E39\u0E25\u0E17\u0E35\u0E48\u0E15\u0E23\u0E07\u0E01\u0E31\u0E19",
        noData: "\u0E44\u0E21\u0E48\u0E1E\u0E1A\u0E02\u0E49\u0E2D\u0E21\u0E39\u0E25",
        titles: ["List 1", "List 2"],
        filterPlaceholder: "\u0E01\u0E23\u0E2D\u0E01\u0E04\u0E35\u0E22\u0E4C\u0E40\u0E27\u0E34\u0E23\u0E4C\u0E14",
        noCheckedFormat: "{total} items",
        hasCheckedFormat: "{checked}/{total} checked"
      },
      image: {
        error: "FAILED"
      },
      pageHeader: {
        title: "\u0E22\u0E49\u0E2D\u0E19\u0E01\u0E25\u0E31\u0E1A"
      },
      popconfirm: {
        confirmButtonText: "Yes",
        cancelButtonText: "No"
      },
      carousel: {
        leftArrow: "Carousel arrow left",
        rightArrow: "Carousel arrow right",
        indicator: "Carousel switch to index {index}"
      }
    }
  };

  return th;

}));
