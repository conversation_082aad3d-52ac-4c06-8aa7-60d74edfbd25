{
    "规则名": "戏曲多多",
    "规则作者": "",
    "请求头参数": "User-Agent$okhttp/3.12.6",
    "网页编码格式": "UTF-8",
    "图片是否需要代理": "0",
    "是否开启获取首页数据": "0",
    "首页推荐链接": "",
    "首页列表数组规则": "",
    "首页片单列表数组规则": "",
    "首页片单是否Jsoup写法": "1",
    "首页片单标题": "a&&title",
    "首页片单链接": "a&&href",
    "首页片单图片": "img&&src",
    "首页片单副标题": "",
    "首页片单链接加前缀": "",
    "首页片单链接加后缀": "",
    "分类起始页码": "0",
    "分类链接": "https://main.xiquduoduo.com/bama/service/s.php?type=getcollabellist&collid={cateId}&label=默认&pg={catePg}&ps=30",
    "分类名称": "花鼓戏&黄梅戏&京剧&老电影&小品相声&儿歌多多&越剧&车载歌曲&采茶戏&豫剧&晋剧&经典歌曲&祁剧&桂剧&民间小调&二人转&河南坠子&评剧&歌仔戏&曲剧&湘剧&川剧&秦腔&婺剧&大平调&越调&莲花落&淮剧&赣剧&春晚戏曲&昆曲&锡剧&二人台&上党梆子&河北梆子&眉户戏&扬剧&皮影戏&绍兴莲花落&北路梆子&泗州戏&楚剧&庐剧&蒲剧&潮剧&评书&沪剧&粤剧&莆仙戏&琼剧&藏剧&布袋戏&吕剧&闽剧&黔剧&滇剧&新疆曲子戏&陇剧&漫瀚剧",
    "分类名称替换词": "80000084&80000018&80000013&80000221&80000083&80000069&80000086&80000214&80000103&80000017&80000020&80000219&80000100&80000102&80000224&80000014&80000023&80000091&80000087&80000022&80000096&80000088&80000019&80000216&80000217&80000218&80000136&80000095&80000099&80000015&80000090&80000097&80000021&80000025&80000027&80000110&80000048&80000092&80000137&80000024&80000220&80000098&80000101&80000093&80000026&80000223&80000089&80000085&80000215&80000104&80000112&80000114&80000094&80000105&80000106&80000107&80000108&80000109&80000113",
    "筛选数据": {},
    //"筛选数据": "ext",
    //{cateId}
    "筛选子分类名称": "",
    "筛选子分类替换词": "",
    //{class}
    "筛选类型名称": "",
    "筛选类型替换词": "*",
    //{area}
    "筛选地区名称": "",
    "筛选地区替换词": "*",
    //{year}
    "筛选年份名称": "",
    "筛选年份替换词": "*",
    //{lang}
    "筛选语言名称": "",
    "筛选语言替换词": "*",
    //{by}
    "筛选排序名称": "默认&最新&高清&全本",
    "筛选排序替换词": "*",
    "分类截取模式": "0",
    "分类列表数组规则": "list",
    "分类片单是否Jsoup写法": "1",
    "分类片单标题": "name",
    "分类片单链接": "ddurl",
    "分类片单图片": "pic",
    "分类片单副标题": "",
    "分类片单链接加前缀": "",
    "分类片单链接加后缀": "",
    "搜索请求头参数": "User-Agent$okhttp/3.12.6",
    "搜索起始页码":"0",
    "搜索链接": "https://main.xiquduoduo.com/bama/service/s.php?type=ddsearch&keyword={wd}&pg={SearchPg}&ps=30&album=true&allbz=true&origin=true",
    "POST请求数据": "",
    "搜索截取模式": "0",
    "搜索列表数组规则": "list",
    "搜索片单是否Jsoup写法": "1",
    "搜索片单图片": "pic",
    "搜索片单标题": "name",
    "搜索片单链接": "ddurl",
    "搜索片单副标题": "",
    "搜索片单链接加前缀": "",
    "搜索片单链接加后缀": "",
    "链接是否直接播放": "1",
    "直接播放链接加前缀": "",
    "直接播放链接加后缀": "",
    "直接播放直链视频请求头": "",
    "是否开启手动嗅探": "0",
    "手动嗅探视频链接关键词": ".mp4#.m3u8#.flv#video/tos",
    "手动嗅探视频链接过滤词": ".html#=http"
}