#!/usr/bin/env python3
"""
检查本地化状态的脚本
"""
import sqlite3
import json

def check_localization():
    """检查本地化状态"""
    conn = sqlite3.connect('data/tvbox.db')
    cursor = conn.cursor()
    
    cursor.execute('SELECT localization_status, localized_config FROM interface_sources WHERE id = 1')
    result = cursor.fetchone()
    
    if result:
        status, config_json = result
        print(f'本地化状态: {status}')
        
        if config_json:
            config = json.loads(config_json)
            print(f'本地化配置存在: 是')
            print(f'Spider路径: {config.get("spider", "未找到")}')
            
            # 检查前几个站点的ext路径
            sites = config.get('sites', [])
            print(f'站点数量: {len(sites)}')
            
            for i, site in enumerate(sites[:3]):
                if 'ext' in site:
                    print(f'  sites[{i}].ext: {site["ext"]}')
        else:
            print('本地化配置存在: 否')
    else:
        print('未找到接口记录')
    
    conn.close()

if __name__ == "__main__":
    check_localization()
