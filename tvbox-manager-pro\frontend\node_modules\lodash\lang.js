module.exports = {
  'castArray': require('./castArray'),
  'clone': require('./clone'),
  'cloneDeep': require('./cloneDeep'),
  'cloneDeepWith': require('./cloneDeepWith'),
  'cloneWith': require('./cloneWith'),
  'conformsTo': require('./conformsTo'),
  'eq': require('./eq'),
  'gt': require('./gt'),
  'gte': require('./gte'),
  'isArguments': require('./isArguments'),
  'isArray': require('./isArray'),
  'isArrayBuffer': require('./isArrayBuffer'),
  'isArrayLike': require('./isArrayLike'),
  'isArrayLikeObject': require('./isArrayLikeObject'),
  'isBoolean': require('./isBoolean'),
  'isBuffer': require('./isBuffer'),
  'isDate': require('./isDate'),
  'isElement': require('./isElement'),
  'isEmpty': require('./isEmpty'),
  'isEqual': require('./isEqual'),
  'isEqualWith': require('./isEqualWith'),
  'isError': require('./isError'),
  'isFinite': require('./isFinite'),
  'isFunction': require('./isFunction'),
  'isInteger': require('./isInteger'),
  'isLength': require('./isLength'),
  'isMap': require('./isMap'),
  'isMatch': require('./isMatch'),
  'isMatchWith': require('./isMatchWith'),
  'isNaN': require('./isNaN'),
  'isNative': require('./isNative'),
  'isNil': require('./isNil'),
  'isNull': require('./isNull'),
  'isNumber': require('./isNumber'),
  'isObject': require('./isObject'),
  'isObjectLike': require('./isObjectLike'),
  'isPlainObject': require('./isPlainObject'),
  'isRegExp': require('./isRegExp'),
  'isSafeInteger': require('./isSafeInteger'),
  'isSet': require('./isSet'),
  'isString': require('./isString'),
  'isSymbol': require('./isSymbol'),
  'isTypedArray': require('./isTypedArray'),
  'isUndefined': require('./isUndefined'),
  'isWeakMap': require('./isWeakMap'),
  'isWeakSet': require('./isWeakSet'),
  'lt': require('./lt'),
  'lte': require('./lte'),
  'toArray': require('./toArray'),
  'toFinite': require('./toFinite'),
  'toInteger': require('./toInteger'),
  'toLength': require('./toLength'),
  'toNumber': require('./toNumber'),
  'toPlainObject': require('./toPlainObject'),
  'toSafeInteger': require('./toSafeInteger'),
  'toString': require('./toString')
};
