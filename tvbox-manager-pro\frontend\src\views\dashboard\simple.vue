<template>
  <div class="simple-dashboard">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>TVBox Manager Pro 控制台</span>
          <el-tag type="success">运行正常</el-tag>
        </div>
      </template>
      
      <div class="welcome-content">
        <h2>🎉 欢迎使用 TVBox Manager Pro！</h2>
        <p>系统已成功启动，您可以开始管理TVBox接口了。</p>
        
        <div class="quick-actions">
          <el-button type="primary" icon="Plus" @click="$router.push('/interfaces/create')">
            添加接口
          </el-button>
          <el-button type="success" icon="List" @click="$router.push('/interfaces')">
            接口列表
          </el-button>
          <el-button type="info" icon="Setting" @click="$router.push('/settings')">
            系统设置
          </el-button>
        </div>
        
        <div class="system-info">
          <h3>系统信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="前端版本">1.0.0</el-descriptions-item>
            <el-descriptions-item label="后端版本">1.0.0</el-descriptions-item>
            <el-descriptions-item label="当前用户">{{ currentUser }}</el-descriptions-item>
            <el-descriptions-item label="登录时间">{{ loginTime }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

const currentUser = computed(() => userStore.userName || '未知用户')
const loginTime = computed(() => new Date().toLocaleString())
</script>

<style scoped>
.simple-dashboard {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-content {
  text-align: center;
}

.welcome-content h2 {
  color: var(--el-color-primary);
  margin-bottom: 16px;
}

.welcome-content p {
  color: var(--el-text-color-regular);
  margin-bottom: 32px;
}

.quick-actions {
  margin-bottom: 32px;
}

.quick-actions .el-button {
  margin: 0 8px;
}

.system-info {
  max-width: 600px;
  margin: 0 auto;
}

.system-info h3 {
  margin-bottom: 16px;
  color: var(--el-text-color-primary);
}
</style>
