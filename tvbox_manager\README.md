# TVBox Manager

TVBox接口解析、解密和管理系统

## 功能特点

- 支持各种TVBox接口解密和解析
- 强大的配置管理功能
- 直播源管理
- 用户权限管理系统
- 现代化的Web界面
- API支持

## 技术栈

- 后端: Flask, Flask-Security-Too, SQLAlchemy
- 前端: HTMX, Tailwind CSS
- 数据库: SQLite (可配置其他数据库)

## 安装

### 安装依赖

```bash
pip install -r requirements.txt
```

### 初始化数据库

```bash
python run.py --init-only
```

### 启动服务

```bash
python run.py
```

或指定主机和端口:

```bash
python run.py -H 0.0.0.0 -p 8080
```

### 使用Docker

```bash
# 构建镜像
docker build -t tvbox-manager .

# 运行容器
docker run -d -p 5000:5000 --name tvbox-manager tvbox-manager
```

## 配置

可以通过环境变量或.env文件进行配置:

- `SECRET_KEY`: 密钥
- `DATABASE_URI`: 数据库连接URI
- `ADMIN_EMAIL`: 管理员邮箱
- `ADMIN_PASSWORD`: 管理员密码
- `MAIL_SERVER`, `MAIL_PORT`, `MAIL_USERNAME`, `MAIL_PASSWORD`: 邮件服务器配置

## 模块说明

- `interface`: 接口管理模块，负责解密和订阅管理
- `config`: 配置管理模块，负责TVBox配置的增删改查
- `live`: 直播源管理模块，负责分组和频道管理
- `auth`: 用户认证和授权模块
- `utils`: 工具类，包含解密器等实用工具

## API接口

### 接口解密

```
POST /interface/api/decrypt
Content-Type: application/json

{
    "url": "http://example.com/tvbox"
}
```

### 获取默认配置

```
GET /config/api/default
```

### 获取指定配置

```
GET /config/api/get/<id>
```

### 获取直播源

```
GET /live/api/groups
```

## 系统要求

- Python 3.7+
- 现代Web浏览器支持

## 许可协议

MIT 