//资源全部免费，仅供测试交流，请勿内置传播与转卖；关注微信公众号：奇奇乐分享，发送“本地包”获取教程与更多资源！

{
    "spider": "./spider.jar",
    "wallpaper": "https://tuapi.eees.cc/api.php?category=fengjing&type=302",
    "logo": "http://php.540734621.xyz/logo/logo.php",
    "sites": [
        {
            "key": "本地包更新",
            "name": "💞本地包｜0718",
            "type": 3,
            "api": "csp_Market",
            "jar": "./jars/gx.jar",
            "searchable": 0,
            "changeable": 0,
            "indexs": 0,
            "ext": "http://bd.qiqiv.cn/666.json"
        },
        {
            "key": "戏 曲",
            "name": "🎈国粹戏曲",
            "type": 3,
            "api": "csp_Bili",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/Blixq.json"
        },
        {
            "key": "相声小品",
            "name": "🏮相声小品",
            "type": 3,
            "api": "csp_<PERSON><PERSON>",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/Blixs.json"
        },
        {
            "key": "潮 汕",
            "name": "🥨潮汕",
            "type": 3,
            "api": "csp_Bili",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/Bli潮汕.json"
        },
        {
            "key": "演唱会",
            "name": "🎸演唱会",
            "type": 3,
            "api": "csp_Bili",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/演唱会.json"
        },
        {
            "key": "MTV",
            "name": "🎧明星┃MV",
            "type": 3,
            "api": "csp_Bili",
            "style": {
                "type": "rect",
                "ratio": 1.597
            },
            "searchable": 0,
            "quickSearch": 0,
            "changeable": 0,
            "ext": "./json/MTV.json"
        },
        {
            "key": "酷奇",
            "name": "🎤酷奇┃MV",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/酷奇MV.js",
            "style": {
                "type": "rect",
                "ratio": 1.597
            },
            "searchable": 0,
            "quickSearch": 0,
            "changeable": 0
        },
        {
            "key": "哔哩哔哩",
            "name": "🅱️哔哩大全",
            "type": 3,
            "api": "csp_Bili",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/Bili.json"
        },
        {
            "key": "哔哩直播",
            "name": "🅱️哔哩直播",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "ext": "./js/blzb.js"
        },
        {
            "key": "csp_Bili短剧",
            "name": "🅱️哔哩短剧",
            "type": 3,
            "api": "csp_Bili",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "ext": "./json/Bili短剧.json"
        },
        {
            "key": "影视解说",
            "name": "🎬影视解说",
            "type": 3,
            "api": "csp_Bili",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/影视解说.json"
        },
        {
            "key": "js_310直播",
            "name": "🥎球赛直播",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "searchable": 1,
            "playerType": 1,
            "quickSearch": 1,
            "filterable": 0,
            "ext": "./js/310.js"
        },
        {
            "key": "虎牙直播",
            "name": "🐯虎牙直播",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "ext": "./js/huyazhibo.js"
        },
        {
            "key": "斗鱼直播",
            "name": "🐟斗鱼直播",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "ext": "./js/douyu.js"
        }
    ],
    "parses": [
        {
            "name": "超级并发",
            "type": 3,
            "url": "Demo"
        },
        {
            "name": "上限万",
            "type": 1,
            "url": "https://svip.cygc.xyz/api/?key=cygctest&url="
        }
    ],
    "flags": [
        "youku",
        "qq",
        "iqiyi",
        "qiyi",
        "letv",
        "sohu",
        "tudou",
        "pptv",
        "mgtv",
        "wasu",
        "bilibili",
        "renrenmi"
    ],
    "rules": [
        {
            "host": "www.iesdouyin.com",
            "rule": [
                "playwm/?video_id="
            ]
        },
        {
            "host": "www.ysgc.vip",
            "rule": [
                "getm3u8?url=http"
            ]
        },
        {
            "host": "v.douyin.com",
            "rule": [
                "playwm/?video_id="
            ]
        },
        {
            "host": "dyxs20.com",
            "rule": [
                ".m3u8"
            ]
        },
        {
            "host": "www.agemys.cc",
            "rule": [
                "cdn-tos",
                "obj/tos-cn"
            ]
        },
        {
            "host": "www.sharenice.net",
            "rule": [
                "http.*?/play.{0,3}\\?[^url]{2,8}=.*",
                "qianpailive.com",
                "vid="
            ]
        },
        {
            "name": "暴风",
            "hosts": [
                "bfzy",
                "bfbfvip"
            ],
            "regex": [
                "#EXTINF.*?\\s+.*?adjump.*?\\.ts\\s+"
            ]
        },
        {
            "name": "量子广告",
            "hosts": [
                "vip.lz",
                "hd.lz",
                ".cdnlz",
                ".cdnlz*"
            ],
            "regex": [
                "#EXTINF.*?\\s+[a-z0-9]{18}\\.ts",
                "[a-z0-9]{18}\\.ts\\s+",
                "#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF.*?\\s+[a-z0-9]{18}\\.ts[\\s\\S]*?#EXT-X-DISCONTINUITY"
            ]
        },
        {
            "name": "非凡广告",
            "hosts": [
                "vip.ffzy",
                "hd.ffzy",
                ".ffzy*"
            ],
            "regex": [
                "#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF.*?\\s+[0-9]{11}[1-9]{2}.*?\\.ts[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:5.033333,[\\s\\S]*?#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:3.960000",
                "#EXTINF.*?\\s+.*?1171.*?\\.ts\\s+"
            ]
        },
        {
            "name": "索尼",
            "hosts": [
                "suonizy"
            ],
            "regex": [
                "#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:1\\.000000,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXTINF.*?\\s+.*?p1ayer.*?\\.ts\\s+",
                "#EXTINF.*?\\s+.*?\\/video\\/original.*?\\.ts\\s+"
            ]
        },
        {
            "name": "快看",
            "hosts": [
                "kuaikan"
            ],
            "regex": [
                "#EXT-X-KEY:METHOD=NONE\\r*\\n*#EXTINF:5,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXT-X-KEY:METHOD=NONE\\r*\\n*#EXTINF:2\\.4,[\\s\\S]*?#EXT-X-DISCONTINUITY"
            ]
        },
        {
            "name": "海外看",
            "hosts": [
                "haiwaikan"
            ],
            "regex": [
                "10.0099",
                "10.3333",
                "16.0599",
                "8.1748",
                "10.85"
            ]
        },
        {
            "name": "磁力广告",
            "hosts": [
                "magnet"
            ],
            "regex": [
                "更多",
                "社 區",
                "x u u",
                "最 新",
                "更 新",
                "社 区",
                "有趣",
                "有 趣",
                "英皇体育",
                "全中文AV在线",
                "澳门皇冠赌场",
                "哥哥快来",
                "美女荷官",
                "裸聊",
                "新片首发",
                "UUE29"
            ]
        }
    ],
    "ijk": [
        {
            "group": "软解码",
            "options": [
                {
                    "category": 4,
                    "name": "opensles",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "overlay-format",
                    "value": "842225234"
                },
                {
                    "category": 4,
                    "name": "framedrop",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "soundtouch",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "start-on-prepared",
                    "value": "1"
                },
                {
                    "category": 1,
                    "name": "http-detect-range-support",
                    "value": "0"
                },
                {
                    "category": 1,
                    "name": "fflags",
                    "value": "fastseek"
                },
                {
                    "category": 2,
                    "name": "skip_loop_filter",
                    "value": "48"
                },
                {
                    "category": 4,
                    "name": "reconnect",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "enable-accurate-seek",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "mediacodec",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "mediacodec-auto-rotate",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "mediacodec-handle-resolution-change",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "mediacodec-hevc",
                    "value": "0"
                },
                {
                    "category": 1,
                    "name": "dns_cache_timeout",
                    "value": "600000000"
                }
            ]
        },
        {
            "group": "硬解码",
            "options": [
                {
                    "category": 4,
                    "name": "opensles",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "overlay-format",
                    "value": "842225234"
                },
                {
                    "category": 4,
                    "name": "framedrop",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "soundtouch",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "start-on-prepared",
                    "value": "1"
                },
                {
                    "category": 1,
                    "name": "http-detect-range-support",
                    "value": "0"
                },
                {
                    "category": 1,
                    "name": "fflags",
                    "value": "fastseek"
                },
                {
                    "category": 2,
                    "name": "skip_loop_filter",
                    "value": "48"
                },
                {
                    "category": 4,
                    "name": "reconnect",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "enable-accurate-seek",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "mediacodec",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "mediacodec-auto-rotate",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "mediacodec-handle-resolution-change",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "mediacodec-hevc",
                    "value": "1"
                },
                {
                    "category": 1,
                    "name": "dns_cache_timeout",
                    "value": "600000000"
                }
            ]
        }
    ],
    "ads": [
        "mimg.0c1q0l.cn",
        "www.googletagmanager.com",
        "www.google-analytics.com",
        "mc.usihnbcq.cn",
        "mg.g1mm3d.cn",
        "mscs.svaeuzh.cn",
        "cnzz.hhttm.top",
        "tp.vinuxhome.com",
        "cnzz.mmstat.com",
        "www.baihuillq.com",
        "s23.cnzz.com",
        "z3.cnzz.com",
        "c.cnzz.com",
        "stj.v1vo.top",
        "z12.cnzz.com",
        "img.mosflower.cn",
        "tips.gamevvip.com",
        "ehwe.yhdtns.com",
        "xdn.cqqc3.com",
        "www.jixunkyy.cn",
        "sp.chemacid.cn",
        "hm.baidu.com",
        "s9.cnzz.com",
        "z6.cnzz.com",
        "um.cavuc.com",
        "mav.mavuz.com",
        "wofwk.aoidf3.com",
        "z5.cnzz.com",
        "xc.hubeijieshikj.cn",
        "tj.tianwenhu.com",
        "xg.gars57.cn",
        "k.jinxiuzhilv.com",
        "cdn.bootcss.com",
        "ppl.xunzhuo123.com",
        "xomk.jiangjunmh.top",
        "img.xunzhuo123.com",
        "z1.cnzz.com",
        "s13.cnzz.com",
        "xg.huataisangao.cn",
        "z7.cnzz.com",
        "xg.huataisangao.cn",
        "z2.cnzz.com",
        "s96.cnzz.com",
        "q11.cnzz.com",
        "thy.dacedsfa.cn",
        "xg.whsbpw.cn",
        "s19.cnzz.com",
        "z8.cnzz.com",
        "s4.cnzz.com",
        "f5w.as12df.top",
        "ae01.alicdn.com",
        "www.92424.cn",
        "k.wudejia.com",
        "vivovip.mmszxc.top",
        "qiu.xixiqiu.com",
        "cdnjs.hnfenxun.com",
        "cms.qdwght.com",
        "api.htpan.net"
    ]
}