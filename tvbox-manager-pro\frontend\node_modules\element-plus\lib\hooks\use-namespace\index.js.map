{"version": 3, "file": "index.js", "sources": ["../../../../../packages/hooks/use-namespace/index.ts"], "sourcesContent": ["import { computed, getCurrentInstance, inject, ref, unref } from 'vue'\n\nimport type { InjectionKey, Ref } from 'vue'\n\nexport const defaultNamespace = 'el'\nconst statePrefix = 'is-'\n\nconst _bem = (\n  namespace: string,\n  block: string,\n  blockSuffix: string,\n  element: string,\n  modifier: string\n) => {\n  let cls = `${namespace}-${block}`\n  if (blockSuffix) {\n    cls += `-${blockSuffix}`\n  }\n  if (element) {\n    cls += `__${element}`\n  }\n  if (modifier) {\n    cls += `--${modifier}`\n  }\n  return cls\n}\n\nexport const namespaceContextKey: InjectionKey<Ref<string | undefined>> =\n  Symbol('namespaceContextKey')\n\nexport const useGetDerivedNamespace = (\n  namespaceOverrides?: Ref<string | undefined>\n) => {\n  const derivedNamespace =\n    namespaceOverrides ||\n    (getCurrentInstance()\n      ? inject(namespaceContextKey, ref(defaultNamespace))\n      : ref(defaultNamespace))\n  const namespace = computed(() => {\n    return unref(derivedNamespace) || defaultNamespace\n  })\n  return namespace\n}\n\nexport const useNamespace = (\n  block: string,\n  namespaceOverrides?: Ref<string | undefined>\n) => {\n  const namespace = useGetDerivedNamespace(namespaceOverrides)\n  const b = (blockSuffix = '') =>\n    _bem(namespace.value, block, blockSuffix, '', '')\n  const e = (element?: string) =>\n    element ? _bem(namespace.value, block, '', element, '') : ''\n  const m = (modifier?: string) =>\n    modifier ? _bem(namespace.value, block, '', '', modifier) : ''\n  const be = (blockSuffix?: string, element?: string) =>\n    blockSuffix && element\n      ? _bem(namespace.value, block, blockSuffix, element, '')\n      : ''\n  const em = (element?: string, modifier?: string) =>\n    element && modifier\n      ? _bem(namespace.value, block, '', element, modifier)\n      : ''\n  const bm = (blockSuffix?: string, modifier?: string) =>\n    blockSuffix && modifier\n      ? _bem(namespace.value, block, blockSuffix, '', modifier)\n      : ''\n  const bem = (blockSuffix?: string, element?: string, modifier?: string) =>\n    blockSuffix && element && modifier\n      ? _bem(namespace.value, block, blockSuffix, element, modifier)\n      : ''\n  const is: {\n    (name: string, state: boolean | undefined): string\n    (name: string): string\n  } = (name: string, ...args: [boolean | undefined] | []) => {\n    const state = args.length >= 1 ? args[0]! : true\n    return name && state ? `${statePrefix}${name}` : ''\n  }\n\n  // for css var\n  // --el-xxx: value;\n  const cssVar = (object: Record<string, string>) => {\n    const styles: Record<string, string> = {}\n    for (const key in object) {\n      if (object[key]) {\n        styles[`--${namespace.value}-${key}`] = object[key]\n      }\n    }\n    return styles\n  }\n  // with block\n  const cssVarBlock = (object: Record<string, string>) => {\n    const styles: Record<string, string> = {}\n    for (const key in object) {\n      if (object[key]) {\n        styles[`--${namespace.value}-${block}-${key}`] = object[key]\n      }\n    }\n    return styles\n  }\n\n  const cssVarName = (name: string) => `--${namespace.value}-${name}`\n  const cssVarBlockName = (name: string) =>\n    `--${namespace.value}-${block}-${name}`\n\n  return {\n    namespace,\n    b,\n    e,\n    m,\n    be,\n    em,\n    bm,\n    bem,\n    is,\n    // css\n    cssVar,\n    cssVarName,\n    cssVarBlock,\n    cssVarBlockName,\n  }\n}\n\nexport type UseNamespaceReturn = ReturnType<typeof useNamespace>\n"], "names": ["getCurrentInstance", "inject", "ref", "computed", "unref"], "mappings": ";;;;;;AACY,MAAC,gBAAgB,GAAG,KAAK;AACrC,MAAM,WAAW,GAAG,KAAK,CAAC;AAC1B,MAAM,IAAI,GAAG,CAAC,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,KAAK;AACnE,EAAE,IAAI,GAAG,GAAG,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AACpC,EAAE,IAAI,WAAW,EAAE;AACnB,IAAI,GAAG,IAAI,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC;AAC7B,GAAG;AACH,EAAE,IAAI,OAAO,EAAE;AACf,IAAI,GAAG,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC;AAC1B,GAAG;AACH,EAAE,IAAI,QAAQ,EAAE;AAChB,IAAI,GAAG,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC,CAAC;AAC3B,GAAG;AACH,EAAE,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AACU,MAAC,mBAAmB,GAAG,MAAM,CAAC,qBAAqB,EAAE;AACrD,MAAC,sBAAsB,GAAG,CAAC,kBAAkB,KAAK;AAC9D,EAAE,MAAM,gBAAgB,GAAG,kBAAkB,KAAKA,sBAAkB,EAAE,GAAGC,UAAM,CAAC,mBAAmB,EAAEC,OAAG,CAAC,gBAAgB,CAAC,CAAC,GAAGA,OAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC;AACrJ,EAAE,MAAM,SAAS,GAAGC,YAAQ,CAAC,MAAM;AACnC,IAAI,OAAOC,SAAK,CAAC,gBAAgB,CAAC,IAAI,gBAAgB,CAAC;AACvD,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,SAAS,CAAC;AACnB,EAAE;AACU,MAAC,YAAY,GAAG,CAAC,KAAK,EAAE,kBAAkB,KAAK;AAC3D,EAAE,MAAM,SAAS,GAAG,sBAAsB,CAAC,kBAAkB,CAAC,CAAC;AAC/D,EAAE,MAAM,CAAC,GAAG,CAAC,WAAW,GAAG,EAAE,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACpF,EAAE,MAAM,CAAC,GAAG,CAAC,OAAO,KAAK,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;AACtF,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ,KAAK,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,GAAG,EAAE,CAAC;AACzF,EAAE,MAAM,EAAE,GAAG,CAAC,WAAW,EAAE,OAAO,KAAK,WAAW,IAAI,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;AAC5H,EAAE,MAAM,EAAE,GAAG,CAAC,OAAO,EAAE,QAAQ,KAAK,OAAO,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE,QAAQ,CAAC,GAAG,EAAE,CAAC;AACnH,EAAE,MAAM,EAAE,GAAG,CAAC,WAAW,EAAE,QAAQ,KAAK,WAAW,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,EAAE,EAAE,QAAQ,CAAC,GAAG,EAAE,CAAC;AAC/H,EAAE,MAAM,GAAG,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,QAAQ,KAAK,WAAW,IAAI,OAAO,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,CAAC,GAAG,EAAE,CAAC;AACzJ,EAAE,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,IAAI,KAAK;AAChC,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AACpD,IAAI,OAAO,IAAI,IAAI,KAAK,GAAG,CAAC,EAAE,WAAW,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;AACxD,GAAG,CAAC;AACJ,EAAE,MAAM,MAAM,GAAG,CAAC,MAAM,KAAK;AAC7B,IAAI,MAAM,MAAM,GAAG,EAAE,CAAC;AACtB,IAAI,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE;AAC9B,MAAM,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE;AACvB,QAAQ,MAAM,CAAC,CAAC,EAAE,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;AAC5D,OAAO;AACP,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,CAAC,MAAM,KAAK;AAClC,IAAI,MAAM,MAAM,GAAG,EAAE,CAAC;AACtB,IAAI,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE;AAC9B,MAAM,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE;AACvB,QAAQ,MAAM,CAAC,CAAC,EAAE,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;AACrE,OAAO;AACP,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG,CAAC;AACJ,EAAE,MAAM,UAAU,GAAG,CAAC,IAAI,KAAK,CAAC,EAAE,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AAC9D,EAAE,MAAM,eAAe,GAAG,CAAC,IAAI,KAAK,CAAC,EAAE,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AAC5E,EAAE,OAAO;AACT,IAAI,SAAS;AACb,IAAI,CAAC;AACL,IAAI,CAAC;AACL,IAAI,CAAC;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,IAAI,EAAE;AACN,IAAI,GAAG;AACP,IAAI,EAAE;AACN,IAAI,MAAM;AACV,IAAI,UAAU;AACd,IAAI,WAAW;AACf,IAAI,eAAe;AACnB,GAAG,CAAC;AACJ;;;;;;;"}