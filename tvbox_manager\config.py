#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
应用配置模块
"""

import os
from datetime import timedelta


class Config(object):
    """基础配置类"""
    
    # 应用基本配置
    SECRET_KEY = os.environ.get('SECRET_KEY', 'dev-key-tvbox-manager')
    SECURITY_PASSWORD_SALT = os.environ.get('SECURITY_PASSWORD_SALT', 'tvbox-manager-salt')
    
    # 数据库配置
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URI', 'sqlite:///tvbox_manager.db')
    
    # 安全配置
    SECURITY_REGISTERABLE = True
    SECURITY_CONFIRMABLE = False
    SECURITY_RECOVERABLE = True
    SECURITY_CHANGEABLE = True
    SECURITY_PASSWORD_COMPLEXITY_CHECKER = None
    
    # 会话配置
    PERMANENT_SESSION_LIFETIME = timedelta(days=7)
    SESSION_TYPE = 'filesystem'
    
    # 邮件配置
    MAIL_SERVER = os.environ.get('MAIL_SERVER', 'smtp.example.com')
    MAIL_PORT = int(os.environ.get('MAIL_PORT', 587))
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() == 'true'
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME', '')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD', '')
    MAIL_DEFAULT_SENDER = os.environ.get('MAIL_DEFAULT_SENDER', '<EMAIL>')
    
    # 上传配置
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER', 'uploads')
    
    # 日志配置
    LOG_LEVEL = 'INFO'
    LOG_FILE = None
    
    # 语言和本地化
    BABEL_DEFAULT_LOCALE = 'zh_CN'
    BABEL_DEFAULT_TIMEZONE = 'Asia/Shanghai'


class DevelopmentConfig(Config):
    """开发环境配置"""
    
    # 调试模式
    DEBUG = True
    
    # 日志配置
    LOG_LEVEL = 'DEBUG'
    
    # SQLAlchemy配置
    SQLALCHEMY_ECHO = True


class TestingConfig(Config):
    """测试环境配置"""
    
    # 测试模式
    TESTING = True
    DEBUG = False
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    
    # 禁用CSRF保护，方便测试
    WTF_CSRF_ENABLED = False
    
    # 安全配置
    SECURITY_PASSWORD_HASH = 'plaintext'  # 测试环境不需要安全散列


class ProductionConfig(Config):
    """生产环境配置"""
    
    # 禁用调试模式
    DEBUG = False
    
    # 日志配置
    LOG_FILE = 'app.log'
    
    # 强制HTTPS
    SESSION_COOKIE_SECURE = True
    REMEMBER_COOKIE_SECURE = True
    
    # 安全设置
    SECURITY_PASSWORD_HASH = 'bcrypt'
    SECURITY_PASSWORD_LENGTH_MIN = 8
    
    # 设置更严格的内容安全策略
    CONTENT_SECURITY_POLICY = {
        'default-src': ["'self'"],
        'img-src': ["'self'", 'data:'],
        'style-src': ["'self'", "'unsafe-inline'"],
        'script-src': ["'self'"]
    }


# 配置字典
config_dict = {
    'Development': DevelopmentConfig,
    'Testing': TestingConfig,
    'Production': ProductionConfig,
    'Debug': DevelopmentConfig
}


# 获取当前配置
def get_config():
    """根据环境变量获取当前配置"""
    config_name = os.environ.get('FLASK_CONFIG', 'Development')
    return config_dict.get(config_name, DevelopmentConfig) 