{"version": 3, "file": "plugin-vue_export-helper.mjs", "sources": ["../../../../internal/build/plugin-vue:export-helper"], "sourcesContent": ["\nexport default (sfc, props) => {\n  const target = sfc.__vccOpts || sfc;\n  for (const [key, val] of props) {\n    target[key] = val;\n  }\n  return target;\n}\n"], "names": [], "mappings": "AACA,kBAAe,CAAC,GAAG,EAAE,KAAK,KAAK;AAC/B,EAAE,MAAM,MAAM,GAAG,GAAG,CAAC,SAAS,IAAI,GAAG,CAAC;AACtC,EAAE,KAAK,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,KAAK,EAAE;AAClC,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;AACtB,GAAG;AACH,EAAE,OAAO,MAAM,CAAC;AAChB;;;;"}