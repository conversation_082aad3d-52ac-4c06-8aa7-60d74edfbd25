#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
配置处理器
用于更新TVBox配置文件中的URL，并提取配置信息
"""

import json
import os
import logging

class ConfigProcessor:
    """配置处理器"""
    
    def __init__(self):
        """初始化处理器"""
        self.logger = logging.getLogger('tvbox_processor')
    
    def update_config(self, config, resources, download_map):
        """
        更新配置文件中的URL
        
        Args:
            config: 配置对象
            resources: 资源信息字典 {url: {type, path, field, encrypted}}
            download_map: 下载映射 {url: local_path}
            
        Returns:
            dict: 更新后的配置
        """
        self.logger.info("开始更新配置文件...")
        
        # 深拷贝配置对象，避免修改原始配置
        updated_config = self._deep_copy(config)
        update_count = 0
        
        # 遍历资源，更新配置中的URL
        for url, info in resources.items():
            if url in download_map:
                local_path = download_map[url]
                
                # 如果下载失败，本地路径与URL相同，跳过
                if local_path == url:
                    continue
                
                # 获取字段路径
                path = info['path']
                field = info['field']
                
                # 更新配置
                try:
                    # 逐层定位到目标对象
                    target = updated_config
                    for i, segment in enumerate(path):
                        if i == len(path) - 1:  # 最后一个路径段
                            if field == 'item' and isinstance(target, list) and segment < len(target):
                                # 数组元素是直接的URL
                                if target[segment] == url:
                                    target[segment] = local_path
                                    update_count += 1
                            elif isinstance(target, dict) and segment in target:
                                # 字典字段
                                if target[segment] == url:
                                    target[segment] = local_path
                                    update_count += 1
                        else:
                            # 继续导航到下一级
                            if isinstance(target, list) and isinstance(segment, int) and segment < len(target):
                                target = target[segment]
                            elif isinstance(target, dict) and segment in target:
                                target = target[segment]
                            else:
                                self.logger.warning(f"无效路径: {path} 在 {segment} 处中断")
                                break
                except Exception as e:
                    self.logger.error(f"更新配置失败: {str(e)}, 路径: {path}, 字段: {field}")
        
        self.logger.info(f"配置更新完成，共更新了 {update_count} 个URL")
        return updated_config
    
    def save_config(self, config, output_path):
        """
        保存配置到文件
        
        Args:
            config: 配置对象
            output_path: 输出文件路径
            
        Returns:
            bool: 是否成功
        """
        try:
            # 确保输出目录存在
            os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
            
            # 将配置写入文件
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"配置已保存到: {output_path}")
            return True
        except Exception as e:
            self.logger.error(f"保存配置失败: {str(e)}")
            return False
    
    def extract_config_info(self, config):
        """
        提取配置信息
        
        Args:
            config: 配置对象
            
        Returns:
            dict: 配置信息摘要
        """
        info = {
            'sites': [],
            'lives': [],
            'parses': [],
            'flags': config.get('flags', []),
            'wallpaper': config.get('wallpaper'),
            'spider': config.get('spider'),
            'rules': config.get('rules', []),
            'ads': config.get('ads', []),
            'ijk': len(config.get('ijk', [])),
            'doh': config.get('doh'),
        }
        
        # 处理站点信息
        sites = config.get('sites', [])
        for site in sites:
            if isinstance(site, dict):
                site_info = {
                    'key': site.get('key', ''),
                    'name': site.get('name', ''),
                    'type': site.get('type', 0),
                    'api': site.get('api', ''),
                    'searchable': site.get('searchable', 0),
                    'quickSearch': site.get('quickSearch', 0),
                    'filterable': site.get('filterable', 0)
                }
                info['sites'].append(site_info)
        
        # 处理直播源
        lives = config.get('lives', [])
        for live in lives:
            if isinstance(live, dict):
                live_info = {
                    'group': live.get('group', ''),
                    'channels': len(live.get('channels', [])) if isinstance(live.get('channels'), list) else 0
                }
                info['lives'].append(live_info)
        
        # 处理解析器
        parses = config.get('parses', [])
        for parse in parses:
            if isinstance(parse, dict):
                parse_info = {
                    'name': parse.get('name', ''),
                    'type': parse.get('type', 0),
                    'url': parse.get('url', '')
                }
                info['parses'].append(parse_info)
        
        return info
    
    def _deep_copy(self, obj):
        """
        深拷贝对象
        
        Args:
            obj: 要拷贝的对象
            
        Returns:
            拷贝后的对象
        """
        if isinstance(obj, dict):
            return {k: self._deep_copy(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._deep_copy(item) for item in obj]
        else:
            return obj 