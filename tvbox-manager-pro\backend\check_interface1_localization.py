#!/usr/bin/env python3
"""
检查接口1的本地化状态
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models import InterfaceSource, LocalizedFile
from app.services.localization_service import LocalizationService

def check_interface1_localization():
    """检查接口1的本地化状态"""
    db = SessionLocal()
    localization_service = LocalizationService()
    
    try:
        print("🔍 检查接口1的本地化状态...")
        print("=" * 80)
        
        # 获取接口1
        interface = db.query(InterfaceSource).filter(InterfaceSource.id == 1).first()
        
        if not interface:
            print("❌ 接口1不存在")
            return
        
        print(f"📋 接口信息:")
        print(f"   ID: {interface.id}")
        print(f"   名称: {interface.name}")
        print(f"   URL: {interface.url}")
        print(f"   本地化开关: {interface.enable_localization}")
        print(f"   本地化状态: {interface.localization_status}")
        print(f"   本地化进度: {interface.localization_progress}%")
        print(f"   本地路径: {interface.local_base_path}")
        print(f"   最后本地化时间: {interface.last_localization_at}")
        
        if interface.localization_error_message:
            print(f"   ❌ 错误信息: {interface.localization_error_message}")
        
        # 获取本地化文件
        localized_files = db.query(LocalizedFile).filter(
            LocalizedFile.interface_id == interface.id
        ).all()
        
        if localized_files:
            print(f"\n📄 本地化文件 ({len(localized_files)} 个):")
            for file in localized_files:
                status_icon = "✅" if file.download_status == 'completed' else "❌"
                size_str = f"{file.file_size:,} bytes" if file.file_size else "N/A"
                print(f"   {status_icon} {file.file_type}: {file.original_url}")
                print(f"      本地路径: {file.local_path}")
                print(f"      文件大小: {size_str}")
                print(f"      下载状态: {file.download_status}")
                if file.error_message:
                    print(f"      错误信息: {file.error_message}")
        else:
            print(f"\n📄 本地化文件: 无")
        
        # 使用服务获取详细状态
        print(f"\n🔧 服务状态检查:")
        status = localization_service.get_localization_status(db, interface.id)
        if status['success']:
            print(f"   ✅ 服务状态正常")
            print(f"   📊 总文件数: {status['total_files']}")
            print(f"   ✅ 完成文件数: {status['completed_files']}")
            print(f"   ❌ 错误文件数: {status['error_files']}")
        else:
            print(f"   ❌ 服务状态异常: {status['error']}")
        
        # 检查本地化目录
        if interface.local_base_path:
            local_dir = f"data/localized/interface_{interface.id}"
            print(f"\n📁 本地化目录检查: {local_dir}")
            if os.path.exists(local_dir):
                files = os.listdir(local_dir)
                print(f"   📄 目录中的文件: {len(files)} 个")
                for file in files[:10]:  # 只显示前10个
                    print(f"      - {file}")
                if len(files) > 10:
                    print(f"      ... 还有 {len(files) - 10} 个文件")
            else:
                print(f"   ❌ 目录不存在")
        
        print("\n" + "=" * 80)
        print("✅ 检查完成！")
        
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    check_interface1_localization()
