<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}TVBox助手{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    {% block head_extra %}{% endblock %}
    <style>
        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: #f8f9fa;
            border-right: 1px solid #dee2e6;
        }
        .content {
            padding: 20px;
        }
        .nav-link {
            color: #495057;
        }
        .nav-link.active {
            color: #0d6efd;
            font-weight: bold;
        }
        .flash-messages {
            position: fixed;
            top: 70px;
            right: 20px;
            z-index: 1000;
            width: 300px;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('routes.index') }}">
                <i class="fas fa-tv me-2"></i>TVBox助手
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == '/' %}active{% endif %}" href="{{ url_for('routes.index') }}">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path.startswith('/configs') %}active{% endif %}" href="{{ url_for('routes.configs') }}">
                            <i class="fas fa-cog me-1"></i>配置管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path.startswith('/decrypt') %}active{% endif %}" href="{{ url_for('routes.decrypt') }}">
                            <i class="fas fa-unlock-alt me-1"></i>接口解密
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path.startswith('/resources') %}active{% endif %}" href="{{ url_for('routes.resources') }}">
                            <i class="fas fa-download me-1"></i>资源管理
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- 闪现消息 -->
    <div class="flash-messages">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>
    
    <!-- 主要内容 -->
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            {% block sidebar %}{% endblock %}
            
            <!-- 主内容区域 -->
            <div class="col">
                <div class="content">
                    {% block content %}{% endblock %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html> 