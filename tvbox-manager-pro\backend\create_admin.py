#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建管理员用户脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from sqlalchemy.orm import Session
from app.core.database import engine
from app.models.user import User
from app.core.security import create_password_hash
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_admin_user():
    """创建管理员用户"""
    try:
        with Session(engine) as db:
            # 检查是否已存在admin用户
            existing_admin = db.query(User).filter(User.email == "<EMAIL>").first()
            
            if existing_admin:
                logger.info("更新现有admin用户为管理员角色...")
                existing_admin.role = "admin"
                existing_admin.is_superuser = True
                existing_admin.is_active = True
                existing_admin.is_verified = True
                db.commit()
                logger.info("✅ Admin用户已更新为管理员角色！")
                return True
            
            # 创建新的admin用户
            logger.info("创建新的admin用户...")
            admin_user = User(
                username="admin",
                email="<EMAIL>",
                hashed_password=create_password_hash("admin123"),
                full_name="系统管理员",
                role="admin",
                is_active=True,
                is_verified=True,
                is_superuser=True
            )
            
            db.add(admin_user)
            db.commit()
            db.refresh(admin_user)
            
            logger.info("✅ Admin用户创建成功！")
            logger.info(f"用户名: admin")
            logger.info(f"邮箱: <EMAIL>")
            logger.info(f"密码: admin123")
            logger.info(f"角色: admin")
            
            return True
            
    except Exception as e:
        logger.error(f"创建admin用户失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = create_admin_user()
    if success:
        print("✅ 管理员用户创建/更新成功！")
        sys.exit(0)
    else:
        print("❌ 管理员用户创建/更新失败！")
        sys.exit(1)
