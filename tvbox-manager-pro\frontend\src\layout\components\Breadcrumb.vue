<template>
  <el-breadcrumb class="app-breadcrumb" separator="/">
    <transition-group name="breadcrumb">
      <el-breadcrumb-item
        v-for="(item, index) in breadcrumbs"
        :key="item.path"
      >
        <span
          v-if="item.redirect === 'noRedirect' || index === breadcrumbs.length - 1"
          class="no-redirect"
        >
          {{ item.meta.title }}
        </span>
        <a v-else @click.prevent="handleLink(item)">
          {{ item.meta.title }}
        </a>
      </el-breadcrumb-item>
    </transition-group>
  </el-breadcrumb>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

const breadcrumbs = ref([])

const getBreadcrumb = () => {
  // 过滤掉不需要显示在面包屑中的路由
  let matched = route.matched.filter(item => item.meta && item.meta.title)

  // 如果当前就是控制台页面，不需要添加额外的面包屑
  if (route.path === '/dashboard') {
    breadcrumbs.value = matched.filter(item => {
      return item.meta && item.meta.title && item.meta.breadcrumb !== false
    })
    return
  }

  const first = matched[0]

  // 如果第一个不是首页，则添加首页
  if (!isDashboard(first)) {
    matched = [{ path: '/dashboard', meta: { title: '控制台' } }].concat(matched)
  }

  breadcrumbs.value = matched.filter(item => {
    return item.meta && item.meta.title && item.meta.breadcrumb !== false
  })
}

const isDashboard = (route) => {
  const name = route && route.name
  if (!name) {
    return false
  }
  return name.trim().toLocaleLowerCase() === 'Dashboard'.toLocaleLowerCase()
}

const handleLink = (item) => {
  const { redirect, path } = item
  if (redirect) {
    router.push(redirect)
    return
  }
  router.push(path)
}

// 监听路由变化
watch(route, getBreadcrumb, { immediate: true })
</script>

<style lang="scss" scoped>
.app-breadcrumb {
  display: inline-block;
  font-size: 14px;
  line-height: 50px;
  
  .no-redirect {
    color: var(--el-text-color-regular);
    cursor: text;
  }
  
  a {
    color: var(--el-text-color-primary);
    text-decoration: none;
    cursor: pointer;
    
    &:hover {
      color: var(--el-color-primary);
    }
  }
}

// 面包屑动画
.breadcrumb-enter-active,
.breadcrumb-leave-active {
  transition: all 0.5s;
}

.breadcrumb-enter-from,
.breadcrumb-leave-active {
  opacity: 0;
  transform: translateX(20px);
}

.breadcrumb-leave-active {
  position: absolute;
}
</style>
