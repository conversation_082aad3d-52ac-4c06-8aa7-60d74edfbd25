{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/timeline/index.ts"], "sourcesContent": ["import { withInstall, withNoopInstall } from '@element-plus/utils'\nimport Timeline from './src/timeline'\nimport TimelineItem from './src/timeline-item.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElTimeline: SFCWithInstall<typeof Timeline> & {\n  TimelineItem: typeof TimelineItem\n} = withInstall(Timeline, {\n  TimelineItem,\n})\nexport default ElTimeline\nexport const ElTimelineItem: SFCWithInstall<typeof TimelineItem> =\n  withNoopInstall(TimelineItem)\n\nexport * from './src/timeline'\nexport * from './src/timeline-item'\nexport * from './src/tokens'\n"], "names": [], "mappings": ";;;;;;AAGY,MAAC,UAAU,GAAG,WAAW,CAAC,QAAQ,EAAE;AAChD,EAAE,YAAY;AACd,CAAC,EAAE;AAES,MAAC,cAAc,GAAG,eAAe,CAAC,YAAY;;;;"}