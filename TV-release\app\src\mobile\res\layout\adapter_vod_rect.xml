<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="150dp"
    android:layout_margin="8dp"
    android:foreground="@drawable/shape_vod">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/image"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black_20"
        android:scaleType="center"
        app:shapeAppearanceOverlay="@style/Vod.Grid"
        tools:src="@drawable/ic_img_loading" />

    <TextView
        android:id="@+id/year"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_vod_year"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:visibility="gone"
        tools:text="2022"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/site"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_vod_site"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:visibility="gone"
        tools:text="泥巴"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/remark"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@+id/name"
        android:background="@drawable/shape_vod_remark"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="12sp"
        tools:text="1080p" />

    <TextView
        android:id="@+id/name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@drawable/shape_vod_name"
        android:gravity="center"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="14sp"
        tools:text="蜘蛛人" />

</RelativeLayout>