#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
应用配置管理
"""

import os
from typing import List, Optional

try:
    from pydantic_settings import BaseSettings
    from pydantic import validator
except ImportError:
    try:
        from pydantic import BaseSettings, validator
    except ImportError:
        # 如果都没有，使用基础配置类
        class BaseSettings:
            def __init__(self, **kwargs):
                for key, value in kwargs.items():
                    setattr(self, key, value)

        def validator(*args, **kwargs):
            def decorator(func):
                return func
            return decorator


class Settings(BaseSettings):
    """应用配置类"""
    
    # 基础配置
    APP_NAME: str = "TVBox Manager Pro"
    VERSION: str = "1.0.0"
    DEBUG: bool = True
    HOST: str = "127.0.0.1"
    PORT: int = 8000
    
    # 安全配置
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 7  # 7天
    REFRESH_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 30  # 30天
    ALGORITHM: str = "HS256"
    
    # 数据库配置
    DATABASE_URL: str = "sqlite:///./data/tvbox.db"
    DATABASE_ECHO: bool = False
    
    # Redis配置
    REDIS_URL: Optional[str] = None
    REDIS_EXPIRE: int = 60 * 60 * 24  # 24小时
    
    # CORS配置
    CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:3001",
        "http://localhost:8080",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:3001",
        "http://127.0.0.1:8080",
        "http://localhost:5173",
        "http://127.0.0.1:5173"
    ]
    
    # 可信主机
    ALLOWED_HOSTS: List[str] = ["*"]
    
    # 文件上传配置
    UPLOAD_DIR: str = "./data/uploads"
    MAX_UPLOAD_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS: List[str] = [".json", ".txt", ".xml"]
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: Optional[str] = "./logs/app.log"
    LOG_ROTATION: str = "1 day"
    LOG_RETENTION: str = "30 days"
    
    # TVBox解密配置
    DECRYPT_TIMEOUT: int = 30  # 解密超时时间（秒）
    DECRYPT_RETRY: int = 3  # 重试次数
    DECRYPT_CACHE_EXPIRE: int = 60 * 60  # 缓存过期时间（秒）
    
    # 订阅更新配置
    SUBSCRIPTION_UPDATE_INTERVAL: int = 60 * 60 * 6  # 6小时
    SUBSCRIPTION_TIMEOUT: int = 30  # 订阅请求超时时间
    SUBSCRIPTION_MAX_RETRIES: int = 3  # 最大重试次数
    
    # API限流配置
    RATE_LIMIT_REQUESTS: int = 100  # 每分钟请求数
    RATE_LIMIT_WINDOW: int = 60  # 时间窗口（秒）
    
    # 邮件配置（可选）
    MAIL_SERVER: Optional[str] = None
    MAIL_PORT: int = 587
    MAIL_USERNAME: Optional[str] = None
    MAIL_PASSWORD: Optional[str] = None
    MAIL_USE_TLS: bool = True
    MAIL_FROM: Optional[str] = None
    
    # 管理员配置
    ADMIN_EMAIL: str = "<EMAIL>"
    ADMIN_PASSWORD: str = "admin123"
    ADMIN_USERNAME: str = "admin"
    
    @validator("CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v):
        """处理CORS origins"""
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v
    
    @validator("ALLOWED_HOSTS", pre=True)
    def assemble_allowed_hosts(cls, v):
        """处理允许的主机"""
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v
    
    @validator("DATABASE_URL", pre=True)
    def assemble_database_url(cls, v):
        """处理数据库URL"""
        if v.startswith("sqlite"):
            # 确保SQLite数据库目录存在
            db_path = v.replace("sqlite:///", "")
            db_dir = os.path.dirname(db_path)
            if db_dir and not os.path.exists(db_dir):
                os.makedirs(db_dir, exist_ok=True)
        return v
    
    @property
    def is_sqlite(self) -> bool:
        """判断是否使用SQLite数据库"""
        return self.DATABASE_URL.startswith("sqlite")
    
    @property
    def is_mysql(self) -> bool:
        """判断是否使用MySQL数据库"""
        return self.DATABASE_URL.startswith("mysql")
    
    @property
    def is_postgresql(self) -> bool:
        """判断是否使用PostgreSQL数据库"""
        return self.DATABASE_URL.startswith("postgresql")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# 创建全局配置实例
settings = Settings()

# 确保必要的目录存在
def ensure_directories():
    """确保必要的目录存在"""
    directories = [
        "./data",
        "./logs",
        settings.UPLOAD_DIR,
        "./data/backups",
        "./data/cache"
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)

# 初始化目录
ensure_directories()
