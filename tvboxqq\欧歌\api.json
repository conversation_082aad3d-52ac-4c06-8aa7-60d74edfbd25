//资源全部免费，仅供测试交流，请勿内置传播与转卖；关注微信公众号：奇奇乐分享，发送“本地包”获取教程与更多资源！

{
    "wallpaper": "https://xn--dkw0c.v.nxog.top/m/t/",
    "logo": "https://alicliimg.clewm.net/342/790/68790342/17369906896774bd3d1d8ba1877dea5d967a137aa23d91736990672.gif",
    "spider": "./spider.jar",
    "warningText": "公众号【杰歌软件APP】如有收费,直接找退款！提示:\n📢接口软件永远免费 \n📢长期维护切勿贩卖",
    "lives": [
        {
            "name": "公众号【杰歌软件APP】如有收费,直接找退款！",
            "type": 0,
            "url": "./lives/公众号【杰歌软件APP】如有收费,直接找退款！.txt",
            "epg": "http://cdn.1678520.xyz/epg/?ch={name}&date={date}"
        },
        {
            "name": "AI直播",
            "type": 0,
            "url": "./lives/AI直播.txt",
            "epg": "http://cdn.1678520.xyz/epg/?ch={name}&date={date}"
        }
    ],
    "sites": [
        {
            "key": "豆瓣2",
            "name": "📢公众号【杰歌软件APP】如有收费,直接找退款！",
            "type": 3,
            "api": "csp_Douban",
            "searchable": 0
        },
        {
            "key": "豆瓣1",
            "name": "📢公告❤更新:7/18❤",
            "type": 3,
            "api": "csp_Notice",
            "searchable": 0,
            "changeable": 0,
            "jar": "./jars/豆瓣1.jar",
            "ext": "https://xn--dkw0c.v.nxog.top/m/公告.php?b=公众号【杰歌软件APP】如有收费,直接找退款！"
        },
        {
            "key": "csp_woog",
            "name": "🐲玩欧｜4K弹幕",
            "type": 3,
            "changeable": "0",
            "api": "csp_Duopan",
            "filterable": 1,
            "jar": "./jars/csp_woog.jar",
            "ext": {
                "site_urls": [
                    "https://woog.nxog.eu.org",
                    "https://woog.nxog.fun",
                    "https://woog.xn--dkw.xn--6qq986b3xl",
                    "https://ogwo.xn--dkw.xn--6qq986b3xl"
                ],
                "url_key": "woog",
                "threadinfo": {
                    "chunksize": 206,
                    "threads": 16
                }
            }
        },
        {
            "key": "config",
            "name": "🐲配置｜中心",
            "type": 3,
            "jar": "./jars/config.jar",
            "api": "csp_Config",
            "searchable": 0
        },
        {
            "key": "csp_woogkk",
            "name": "🐲夸快┃非会员4K",
            "type": 3,
            "changeable": "0",
            "api": "csp_woog",
            "filterable": 1,
            "jar": "./jars/csp_woogkk.jar",
            "ext": {
                "site_urls": [
                    "hhttps://ogkk.nxog.eu.org",
                    "https://ogkk.nxog.fun",
                    "https://ogkk.xn--dkw.xn--6qq986b3xl"
                ],
                "url_key": "UC",
                "threadinfo": {
                    "chunksize": 243,
                    "threads": 60
                }
            }
        },
        {
            "key": "csp_欧歌123",
            "name": "🐲夸快┃配置",
            "type": 3,
            "api": "csp_Config",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "jar": "./jars/csp_欧歌123.jar"
        },
        {
            "key": "csp_woog2",
            "name": "❤欧歌｜4K弹幕",
            "type": 3,
            "changeable": "0",
            "api": "csp_Duopan",
            "filterable": 1,
            "jar": "./jars/csp_woog2.jar",
            "ext": {
                "site_urls": [
                    "https://ogkk.nxog.eu.org",
                    "https://ogkk.nxog.fun",
                    "https://ogkk.xn--dkw.xn--6qq986b3xl"
                ],
                "url_key": "woog2",
                "threadinfo": {
                    "chunksize": 209,
                    "threads": 16
                }
            }
        },
        {
            "key": "配置中心",
            "name": "❤配置｜中心",
            "type": 3,
            "api": "csp_Config",
            "searchable": 0,
            "changeable": 0,
            "style": {
                "type": "rect",
                "ratio": 1.597
            }
        },
        {
            "key": "巧技",
            "name": "🐢聚搜┃搜索",
            "type": 3,
            "api": "csp_qiao2",
            "playerType": 2,
            "jar": "./jars/巧技.jar",
            "ext": "7lj763gg402i79425739i7jghj118797l4hj840gi18633331l4708g2h7145403549g44l8ii56i187681hkjj3hhgh1ih3l32j250lk1k786lj20j468hk3hli4l46gig4i3g7g2722328j0136h01i7g5183k22k7gg3i72hk81gl8k9839kl7i0707"
        },
        {
            "key": "采集之王",
            "name": "🐢采集┃搜索",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/采集之王.js?type=url&params=./json/采集静态.json$1$1"
        },
        {
            "key": "荐片",
            "name": "💡荐片｜影视",
            "api": "csp_Jianpian",
            "type": 3,
            "playerType": 1,
            "ext": "https://api.ubj83.com"
        },
        {
            "key": "移动",
            "name": "💡移动｜影视",
            "type": 3,
            "api": "csp_YDjisu",
            "searchable": 1,
            "quickSearch": 1
        },
        {
            "key": "热播影视",
            "name": "💡热播｜影视",
            "type": 3,
            "api": "csp_AppRJ",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 0,
            "ext": {
                "url": "http://v.rbotv.cn"
            }
        },
        {
            "key": "天天影视",
            "name": "💡天天｜影视",
            "type": 3,
            "api": "csp_AppRJ",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 0,
            "ext": {
                "url": "http://tt.ysdqjs.cn"
            }
        },
        {
            "key": "浪酷影视",
            "name": "💡浪酷｜影视",
            "type": 3,
            "api": "csp_AppRJ",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 0,
            "ext": {
                "url": "http://v.lkuys.cn"
            }
        },
        {
            "key": "瓜子影视",
            "name": "💡瓜子｜影视",
            "type": 3,
            "api": "csp_Gz360",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "低端",
            "name": "💡低端｜影视",
            "type": 3,
            "api": "csp_Ddys",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "快看影视",
            "name": "💡快看｜影视",
            "type": 3,
            "api": "csp_Kuaikan",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "来看影视",
            "name": "💡来看｜影视",
            "type": 3,
            "api": "csp_Lkdy",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "苹果影视",
            "name": "💡苹果｜影视",
            "type": 3,
            "api": "csp_LiteApple",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "爱看机器人",
            "name": "💡爱看｜影视",
            "type": 3,
            "api": "csp_Ikanbot",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "全网影视",
            "name": "💡全看｜影视",
            "type": 3,
            "api": "csp_Quanwk",
            "ext": "https://www.91qkw.com"
        },
        {
            "key": "三六零",
            "name": "💡三六｜视频",
            "type": 3,
            "api": "csp_SP360"
        },
        {
            "key": "1905",
            "name": "💡1905｜影视",
            "type": 3,
            "api": "csp_Web1905",
            "searchable": 1,
            "quickSearch": 0,
            "filterable": 0
        },
        {
            "key": "csp_baibai",
            "name": "💡白白｜影视",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_SuBaiBai",
            "ext": "https://www.subaibai.com"
        },
        {
            "key": "好感",
            "name": "💡好感｜影视",
            "type": 3,
            "api": "csp_AppYsV2",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "https://cs.hgyx.vip/api2/api.php/app/"
        },
        {
            "key": "糯米影视",
            "name": "💡糯米┃影视",
            "type": 3,
            "api": "csp_Wwys",
            "ext": "https://www.wwgz.cn"
        },
        {
            "key": "csp_nongmin",
            "name": "💡农民｜影视",
            "type": 3,
            "api": "csp_Wwys",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "https://wwgz.cn/"
        },
        {
            "key": "鸭梨影视",
            "name": "💡鸭梨｜影视",
            "type": 3,
            "api": "csp_KmeiJu"
        },
        {
            "key": "huomaoys",
            "name": "💡火猫｜影视",
            "type": 3,
            "api": "csp_Muou",
            "jar": "./jars/巧技.jar",
            "playerType": 2,
            "ext": "7lj763gg0939790i413gi484k8058896highi4414h68l7g6hk8qiaojig9k2k289l9ik807i213k5j602"
        },
        {
            "key": "爆炸",
            "name": "💡爆炸｜影视",
            "type": 3,
            "api": "csp_Muou",
            "jar": "./jars/巧技.jar",
            "playerType": 2,
            "ext": "7lj763gg402i79425i3l85i6h848i295l5hiji5l828g3l3jjhg6kg7410lhjkqiaojij3ig1lg475178k7h0il4ig3h753h7hi516758699jh2g5h433li30gk11g73l90312h4g7"
        },
        {
            "key": "麻花",
            "name": "👑麻花｜弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "",
                "site": "https://apicoss-alimama-com-1307821617.file.myqcloud.com/Uploadsget",
                "dataKey": "q7gj4f9br3fls6nh",
                "dataIv": "q7gj4f9br3fls6nh",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "豆丁",
            "name": "👑豆丁｜弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "",
                "site": "./txt/xfdd.txt",
                "dataKey": "xasdasdqwertyuio",
                "dataIv": "xasdasdqwertyuio",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "灵虎",
            "name": "👑灵虎｜弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "",
                "site": "./txt/89.txt",
                "dataKey": "#getapp@TMD@2025",
                "dataIv": "#getapp@TMD@2025",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "追忆",
            "name": "👑追忆｜弹幕",
            "type": 3,
            "api": "csp_AppSy",
            "ext": {
                "url": "http://110.42.7.130:1866",
                "key1": "aassddwwxxllsx1x",
                "key2": "2083c87e98b6ce08",
                "key3": "2083c87e98b6ce08"
            }
        },
        {
            "key": "雄鹰",
            "name": "👑雄鹰｜弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet2",
            "ext": {
                "url": "http://122.228.193.2:9988",
                "dataKey": "ca94b06ca359d80e",
                "dataIv": "ca94b06ca359d80e",
                "deviceId": "",
                "version": "120"
            }
        },
        {
            "key": "橘子",
            "name": "👑橘子｜弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "https://api1.bffree.cn",
                "dataKey": "2015692015692015",
                "dataIv": "2015692015692015",
                "deviceId": "",
                "version": "300"
            }
        },
        {
            "key": "1若惜",
            "name": "👑若惜｜弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "http://110.40.59.188:9527",
                "dataKey": "ebad3f1a58b13933",
                "dataIv": "ebad3f1a58b13933",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "世颜",
            "name": "👑世颜｜弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "https://sykjw.xyz",
                "dataKey": "sicnagduxbSfisnz",
                "dataIv": "sicnagduxbSfisnz",
                "deviceId": "2112fc5eac600314ba95c4d65da9286b3",
                "version": "556"
            }
        },
        {
            "key": "可达",
            "name": "👑可达｜弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "https://ledayy.com",
                "dataKey": "hjjp68c2okw12345",
                "dataIv": "hjjp68c2okw12345",
                "deviceId": "24bba8caeff7b3a63a486806bd7130fca",
                "version": "102"
            }
        },
        {
            "key": "咖啡",
            "name": "👑咖啡｜弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet2",
            "ext": {
                "url": "",
                "site": "./txt/2.txt",
                "dataKey": "qwertyuiopqwertt",
                "dataIv": "qwertyuiopqwertt",
                "deviceId": "",
                "version": "109"
            }
        },
        {
            "key": "仓鼠",
            "name": "👑仓鼠｜弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet2",
            "ext": {
                "url": "https://qjappcms.cs4k.top",
                "dataKey": "Z98KXaLtO2wC1Pte",
                "dataIv": "Z98KXaLtO2wC1Pte",
                "deviceId": "",
                "version": "120"
            }
        },
        {
            "key": "趣看",
            "name": "👑趣看｜弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppMuou",
            "ext": {
                "url": "https://xjuzi.top",
                "dataKey": "6a482a70b80eefc9",
                "dataIv": "c995826a3e86fedd",
                "jxurl": "https://www.ququkan.cc"
            }
        },
        {
            "key": "驿站",
            "name": "👑驿站｜弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppMuou",
            "ext": {
                "url": "http://110.42.110.230:16189",
                "dataKey": "d7378557d1d9c924",
                "dataIv": "fb4594f622f34fea",
                "jxurl": "https://qqiqiyiyoukumgtvapiappdyyztvjsoni8n2o0z1ws2n7b6v3x8.68.gy:56789"
            }
        },
        {
            "key": "樱桃",
            "name": "👑樱桃｜弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppMuou",
            "ext": {
                "url": "http://sp.dxgsp.cc",
                "dataKey": "25f9e794323b4538",
                "dataIv": "25f9e794323b4538",
                "jxurl": "https://ap.dxgsp.cc"
            }
        },
        {
            "key": "蓝鹰",
            "name": "👑蓝鹰｜弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet2",
            "ext": {
                "url": "http://172.247.192.138:18520",
                "dataKey": "SuNlEkOLAoWJj1Oe",
                "dataIv": "SuNlEkOLAoWJj1Oe",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "瓜萌",
            "name": "👑瓜萌｜弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "https://www.guahd.com",
                "dataKey": "f2A7D4B9E8C16531",
                "dataIv": "f2A7D4B9E8C16531",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "星河",
            "name": "👑星河｜弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "http://121.62.22.204:9876",
                "dataKey": "f5e2tx53ykp6s2c9",
                "dataIv": "f5e2tx53ykp6s2c9",
                "deviceId": "",
                "version": "361"
            }
        },
        {
            "key": "萝卜",
            "name": "👑萝卜｜弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "https://apiapplbys.lbys.app:5678",
                "dataKey": "apiapplbyskey168",
                "dataIv": "apiapplbyskey168",
                "deviceId": "",
                "version": "107"
            }
        },
        {
            "key": "小红",
            "name": "👑小红｜弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "https://www.xiaohys.com",
                "dataKey": "ENonBHeVBoYZhVUV",
                "dataIv": "ENonBHeVBoYZhVUV",
                "deviceId": "298e5fe29c74b35aabb9836ee2f6f449f",
                "version": "166"
            }
        },
        {
            "key": "csp_XYQHiker_农民影视",
            "name": "🧿农民｜影视",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/nmys.json"
        },
        {
            "key": "八号影视",
            "name": "🧿八号｜影视",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/八号影视.json"
        },
        {
            "key": "雪糕影视",
            "name": "🧿雪糕｜影视",
            "type": 3,
            "api": "csp_HBPQ",
            "ext": "./json/雪糕影视.json"
        },
        {
            "key": "永乐影视",
            "name": "🧿永乐｜影视",
            "type": 3,
            "api": "csp_HBPQ",
            "ext": "./json/永乐影视.json"
        },
        {
            "key": "可可影视",
            "name": "🧿可可｜影视",
            "type": 3,
            "api": "csp_HBPQ",
            "ext": "./json/KKYS.json"
        },
        {
            "key": "面包影视",
            "name": "🧿面包｜影视",
            "type": 3,
            "api": "csp_HBPQ",
            "ext": "./json/MBYS.json"
        },
        {
            "key": "猎手影视",
            "name": "🧡猎手｜PY影视",
            "type": 3,
            "api": "./api/猎手影视.py",
            "searchable": 1,
            "changeable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "playerType": 2
        },
        {
            "key": "若惜",
            "name": "🧡若惜｜PY影视",
            "type": 3,
            "api": "./api/若惜追剧APP.py",
            "searchable": 1,
            "changeable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "playerType": 2
        },
        {
            "key": "金牌影视PY",
            "name": "🧡金牌｜PY影视",
            "type": 3,
            "api": "./api/金牌.py",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "playerType": 2,
            "ext": {
                "site": "https://www.hkybqufgh.com,https://www.sizhengxt.com,https://0996zp.com,https://9zhoukj.com/,https://www.sizhengxt.com,https://www.tjrongze.com,https://www.jiabaide.cn,https://cqzuoer.com"
            }
        },
        {
            "key": "New6v",
            "name": "🎃New6V┃磁力",
            "type": 3,
            "api": "csp_New6v",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "https://www.xb6v.com"
        },
        {
            "key": "剧迷",
            "name": "🎃剧迷┃磁力",
            "type": 3,
            "api": "csp_MeijuMi",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "电影",
            "name": "🎃Mp4┃磁力",
            "type": 3,
            "api": "csp_Mp4Mov",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "天堂",
            "name": "🎃美剧┃磁力",
            "type": 3,
            "api": "csp_MeijuTT",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "学堂",
            "name": "🅰️学堂┃教育",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "order_num": 142,
            "ext": "./js/我的哔哩.js?type=url&params=./json/学堂教育.json"
        },
        {
            "key": "少儿",
            "name": "🅰️少儿┃合集",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "order_num": 142,
            "ext": "./js/我的哔哩.js?type=url&params=./json/少儿合集.json"
        },
        {
            "key": "小学课堂",
            "name": "🅰️小学┃课堂",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "order_num": 142,
            "ext": "./js/我的哔哩.js?type=url&params=./json/小学课堂.json"
        },
        {
            "key": "初中课堂",
            "name": "🅰️初中┃课堂",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "order_num": 142,
            "ext": "./js/我的哔哩.js?type=url&params=./json/初中课堂.json"
        },
        {
            "key": "高中课堂",
            "name": "🅰️高中┃课堂",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "order_num": 142,
            "ext": "./js/我的哔哩.js?type=url&params=./json/高中课堂.json"
        },
        {
            "key": "儿童口才",
            "name": " 🧒口才┃儿童",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "order_num": 142,
            "ext": "./js/我的哔哩.js?type=url&params=./json/儿童口才.json"
        },
        {
            "key": "儿童拼音",
            "name": " 🧒拼音┃儿童",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "order_num": 142,
            "ext": "./js/我的哔哩.js?type=url&params=./json/儿童拼音.json"
        },
        {
            "key": "儿童识字",
            "name": " 🧒识字┃儿童",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "order_num": 142,
            "ext": "./js/我的哔哩.js?type=url&params=./json/儿童识字.json"
        },
        {
            "key": "儿童思维",
            "name": " 🧒思维┃儿童",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "order_num": 142,
            "ext": "./js/我的哔哩.js?type=url&params=./json/儿童思维.json"
        },
        {
            "key": "儿童英语",
            "name": " 🧒英语┃儿童",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "order_num": 142,
            "ext": "./js/我的哔哩.js?type=url&params=./json/儿童英语.json"
        },
        {
            "key": "儿童硬笔",
            "name": " 🧒硬笔┃儿童",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "order_num": 142,
            "ext": "./js/我的哔哩.js?type=url&params=./json/儿童硬笔.json"
        },
        {
            "key": "儿童编程",
            "name": " 🧒编程┃儿童",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "order_num": 142,
            "ext": "./js/我的哔哩.js?type=url&params=./json/儿童编程.json"
        },
        {
            "key": "儿童武术",
            "name": " 🧒武术┃儿童",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "order_num": 142,
            "ext": "./js/我的哔哩.js?type=url&params=./json/儿童武术.json"
        },
        {
            "key": "csp_BBB",
            "name": "🅱️套餐┃哔哩",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "order_num": 142,
            "ext": "./js/我的哔哩.js?type=url&params=./json/哔哩套餐1.json"
        },
        {
            "key": "Bili",
            "name": "🅱️套餐2┃哔哩",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "order_num": 142,
            "ext": "./js/我的哔哩.js?type=url&params=./json/哔哩套餐2.json"
        },
        {
            "key": "软件",
            "name": "🅰️软件┃教程",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "order_num": 142,
            "ext": "./js/我的哔哩.js?type=url&params=./json/软件教程.json"
        },
        {
            "key": "演唱会",
            "name": "🅰️演唱会┃哔哩",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "order_num": 142,
            "ext": "./js/我的哔哩.js?type=url&params=./json/演唱会.json"
        },
        {
            "key": "MV",
            "name": "🌈歌曲┃哔哩",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "order_num": 142,
            "ext": "./js/我的哔哩.js?type=url&params=./json/歌曲MV.json"
        },
        {
            "key": "美食",
            "name": "🌈美食┃哔哩",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "order_num": 142,
            "ext": "./js/我的哔哩.js?type=url&params=./json/美食.json"
        },
        {
            "key": "球类",
            "name": "⚽️球类┃合集",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "order_num": 142,
            "ext": "./js/我的哔哩.js?type=url&params=./json/球类合集.json"
        },
        {
            "key": "球星",
            "name": "⚽️球星┃合集",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "order_num": 142,
            "ext": "./js/我的哔哩.js?type=url&params=./json/球星合集.json"
        },
        {
            "key": "戏曲",
            "name": "🅰️戏曲┃合集",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "order_num": 142,
            "ext": "./js/我的哔哩.js?type=url&params=./json/戏曲合集.json"
        },
        {
            "key": "急救教学",
            "name": "🚑急救｜教学",
            "type": 3,
            "api": "csp_FirstAid",
            "searchable": 0,
            "quickSearch": 0,
            "changeable": 0,
            "style": {
                "type": "rect",
                "ratio": 3.8
            },
            "gridview": "0-0-4.1"
        },
        {
            "key": "本地",
            "name": "🌸本地｜视频",
            "type": 3,
            "api": "csp_LocalFile"
        },
        {
            "key": "预告",
            "name": "🌸新片｜预告",
            "type": 3,
            "api": "csp_DouBan",
            "searchable": 0
        },
        {
            "key": "合集",
            "name": "💋哔哩┃合集",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "searchable": 1,
            "quickSearch": 0,
            "filterable": 1,
            "order_num": 142,
            "ext": "./js/我的哔哩.js?type=url&params=./json/合集.json"
        },
        {
            "key": "88看球",
            "name": "💋88｜看球",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "style": {
                "type": "list"
            },
            "ext": "./js/88看球.js"
        },
        {
            "key": "曼波动漫",
            "name": "🤣曼波｜动漫",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "https://app.omofun1.top",
                "dataKey": "66dc309cbeeca454",
                "dataIv": "66dc309cbeeca454",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "咕咕动漫",
            "name": "🤣咕咕｜动漫",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "https://www.gugu3.com",
                "dataKey": "nKfZ8KX6JTNWRzTD",
                "dataIv": "nKfZ8KX6JTNWRzTD",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "米饭动漫",
            "name": "🤣米饭｜动漫",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "http://45.43.29.111:9527",
                "dataKey": "GETMIFUNGEIMIFUN",
                "dataIv": "GETMIFUNGEIMIFUN",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "魔都资源",
            "name": "🤣魔都┃动漫",
            "type": 1,
            "api": "https://caiji.moduapi.cc/api.php/provide/vod/?ac=list",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "木偶",
            "name": "❤木偶｜4K弹幕",
            "type": 3,
            "api": "csp_PanWebShare",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/mogg.json?"
        },
        {
            "key": "蜡笔",
            "name": "❤蜡笔｜4K弹幕",
            "type": 3,
            "api": "csp_PanWebShare",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "changeable": 1,
            "ext": "./json/lb.json?"
        },
        {
            "key": "小米",
            "name": "❤小米｜4K弹幕",
            "type": 3,
            "api": "csp_PanWebShare",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "changeable": 1,
            "ext": "./json/xm.json?"
        },
        {
            "key": "至臻",
            "name": "❤至臻｜4K弹幕",
            "type": 3,
            "api": "csp_PanWebShare",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "changeable": 1,
            "ext": "./json/zz.json?"
        },
        {
            "key": "多多",
            "name": "❤多多｜4K弹幕",
            "type": 3,
            "api": "csp_PanWebShare",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "changeable": 1,
            "ext": "./json/yyds.json?"
        },
        {
            "key": "欧哥备",
            "name": "❤玩偶｜4K弹幕",
            "type": 3,
            "api": "csp_PanWebShare",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "changeable": 1,
            "ext": "./json/og.json?"
        },
        {
            "key": "雷鲸",
            "name": "❤雷鲸｜4K",
            "type": 3,
            "api": "csp_PanWebShareCloudLJ",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "changeable": 1,
            "style": {
                "type": "list",
                "ratio": 1.433
            },
            "ext": "./json/lj.json"
        },
        {
            "key": "趣盘",
            "name": "❤趣盘｜4K",
            "type": 3,
            "api": "csp_PanWebQu",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "style": {
                "type": "list",
                "ratio": 1.433
            },
            "ext": {
                "url": "https://www.qupanshe.com"
            }
        },
        {
            "key": "聚搜",
            "name": "❤聚搜｜4K",
            "type": 3,
            "api": "csp_PanWebSearch",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "style": {
                "type": "list",
                "ratio": 1.433
            },
            "ext": "./json/aliyunpansearch.json"
        },
        {
            "key": "盘库",
            "name": "❤盘库｜4K",
            "type": 3,
            "api": "csp_PanWebKuBa",
            "searchable": 1,
            "filterable": 0,
            "changeable": 0,
            "ext": {
                "url": "https://panku8.com,https://yipanso.com"
            }
        },
        {
            "key": "阿里云盘",
            "name": "❤我的｜阿里",
            "type": 3,
            "api": "csp_PanAli",
            "searchable": 0,
            "filterable": 0,
            "changeable": 0,
            "style": {
                "type": "list",
                "ratio": 1.433
            },
            "ext": "./json/aliShare.json"
        },
        {
            "key": "夸克云盘",
            "name": "❤我的｜夸克",
            "type": 3,
            "api": "csp_PanQuark",
            "searchable": 0,
            "filterable": 0,
            "changeable": 0,
            "style": {
                "type": "list",
                "ratio": 1.433
            },
            "ext": "./json/quarkShare.json"
        },
        {
            "key": "UC",
            "name": "❤我的｜UC",
            "type": 3,
            "api": "csp_PanUc",
            "searchable": 0,
            "filterable": 0,
            "changeable": 0,
            "style": {
                "type": "list",
                "ratio": 1.433
            },
            "ext": "./json/ucShare.json"
        },
        {
            "key": "百度云盘",
            "name": "❤我的｜百度",
            "type": 3,
            "api": "csp_PanBaiDu",
            "searchable": 0,
            "filterable": 0,
            "changeable": 0,
            "style": {
                "type": "list",
                "ratio": 1.433
            }
        },
        {
            "key": "push_agent",
            "name": "📢手机｜推送",
            "type": 3,
            "api": "csp_Push",
            "searchable": 0,
            "filterable": 0,
            "changeable": 0
        },
        {
            "key": "应用商店",
            "name": "🛒本地包更新",
            "type": 3,
            "api": "csp_Market",
            "searchable": 0,
            "changeable": 0,
            "ext": "http://123.yy.xn--dkw.xn--6qq986b3xl/zm/sd.php"
        },
        {
            "key": "豆瓣3",
            "name": "📢接口软件永远免费",
            "type": 3,
            "api": "csp_Douban",
            "searchable": 0
        },
        {
            "key": "豆瓣4",
            "name": "📢长期维护切勿贩卖",
            "type": 3,
            "api": "csp_Douban",
            "searchable": 0
        }
    ],
    "parses": [
        {
            "name": "-聚合-",
            "type": 3,
            "url": "Demo"
        },
        {
            "name": "欧歌解析",
            "type": "1",
            "url": "https://欧歌解析.v.nxog.top/m/jx.php?ou=公众号欧歌APP&mz=0&url=",
            "ext": {
                "header": {
                    "user-agent": "Mozilla/5.0(Linux;Android13;V2049ABuild/TP1A.220624.014;wv)AppleWebKit/537.36(KHTML,likeGecko)Version/4.0Chrome/*********MobileSafari/537.36"
                }
            }
        },
        {
            "name": "-关注-",
            "type": 1,
            "url": "https://2.nxog.eu.org/jx.php?ou=公众号欧歌APP&mz=1&url="
        },
        {
            "name": "-欧歌-",
            "type": 1,
            "url": "https://3.nxog.eu.org/jx.php?ou=公众号欧歌APP&mz=2&url="
        },
        {
            "name": "-公众号-",
            "type": 1,
            "url": "https://2.nxog.eu.org/jx.php?ou=公众号欧歌APP&mz=3&url="
        },
        {
            "name": "欧歌APP",
            "type": 1,
            "url": "https://mfjx.iwsyy.xyz/?url="
        },
        {
            "name": "线路1",
            "type": 0,
            "url": "https://jx.xmflv.com/?url=",
            "ext": {
                "header": {
                    "user-agent": "Mozilla/5.0(WindowsNT10.0;Win64;x64)AppleWebKit/537.36(KHTML,likeGecko)Chrome/110.0.0.0Safari/537.36Edg/110.0.1587.57"
                }
            }
        },
        {
            "name": "线路2",
            "type": 0,
            "url": "http://www.ckplayer.vip/jiexi/?url=",
            "ext": {
                "header": {
                    "user-agent": "Mozilla/5.0(Linux;Android13;V2049ABuild/TP1A.220624.014;wv)AppleWebKit/537.36(KHTML,likeGecko)Version/4.0Chrome/*********MobileSafari/537.36"
                }
            }
        },
        {
            "name": "线路3",
            "type": 0,
            "url": "https://jx.yparse.com/index.php?url=",
            "ext": {
                "header": {
                    "user-agent": "Mozilla/5.0(Linux;Android13;V2049ABuild/TP1A.220624.014;wv)AppleWebKit/537.36(KHTML,likeGecko)Version/4.0Chrome/*********MobileSafari/537.36"
                }
            }
        },
        {
            "name": "线路4",
            "type": 0,
            "url": "https://jx.m3u8.tv/jiexi/?url=",
            "ext": {
                "header": {
                    "user-agent": "Mozilla/5.0(Linux;Android13;V2049ABuild/TP1A.220624.014;wv)AppleWebKit/537.36(KHTML,likeGecko)Version/4.0Chrome/*********MobileSafari/537.36",
                    "referer": "https://jiejie.uk/"
                }
            }
        },
        {
            "name": "线路5",
            "type": 0,
            "url": "https://cdn.zyc888.top/?url="
        },
        {
            "name": "线路6",
            "type": 0,
            "url": "https://bd.jx.cn/?url="
        },
        {
            "name": "线路7",
            "type": 0,
            "url": "https://yparse.ik9.cc/index.php?url="
        }
    ],
    "rules": [
        {
            "name": "暴风",
            "hosts": [
                "bfzy",
                "bfbfvip",
                "bfengbf"
            ],
            "regex": [
                "#EXTINF.*?\\s+.*?adjump.*?\\.ts"
            ]
        },
        {
            "name": "量子",
            "hosts": [
                "vip.lz",
                "hd.lz",
                ".cdnlz"
            ],
            "regex": [
                "#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:7\\.166667,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:4\\.066667,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "17.19"
            ]
        },
        {
            "name": "非凡",
            "hosts": [
                "vip.ffzy",
                "hd.ffzy",
                "super.ffzy"
            ],
            "regex": [
                "#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:6\\.400000,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:6\\.666667,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXTINF.*?\\s+.*?1171(057).*?\\.ts",
                "#EXTINF.*?\\s+.*?6d7b(077).*?\\.ts",
                "#EXTINF.*?\\s+.*?6718a(403).*?\\.ts",
                "17.99",
                "14.45"
            ]
        },
        {
            "name": "索尼",
            "hosts": [
                "suonizy"
            ],
            "regex": [
                "#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:1\\.000000,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXTINF.*?\\s+.*?p1ayer.*?\\.ts",
                "#EXTINF.*?\\s+.*?\\/video\\/original.*?\\.ts"
            ]
        },
        {
            "name": "快看",
            "hosts": [
                "kuaikan"
            ],
            "regex": [
                "#EXT-X-KEY:METHOD=NONE\\r*\\n*#EXTINF:5,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXT-X-KEY:METHOD=NONE\\r*\\n*#EXTINF:2\\.4,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXT-X-KEY:METHOD=NONE\\r*\\n*#EXTINF:1\\.467,[\\s\\S]*?#EXT-X-DISCONTINUITY"
            ]
        },
        {
            "name": "一起看广告",
            "hosts": [
                "yqk88"
            ],
            "regex": [
                "18.4",
                "15.1666",
                "16.5333",
                "#EXT-X-DISCONTINUITY\\r*\\n*[\\s\\S]*?#EXT-X-CUE-IN"
            ]
        },
        {
            "name": "磁力广告",
            "hosts": [
                "magnet"
            ],
            "regex": [
                "更多",
                "请访问",
                "example",
                "社區",
                "xuu",
                "直播",
                "更新",
                "社区",
                "有趣",
                "有趣",
                "英皇体育",
                "全中文AV在线",
                "澳门皇冠赌场",
                "哥哥快来",
                "美女荷官",
                "裸聊",
                "新片首发",
                "UUE29"
            ]
        },
        {
            "name": "火山嗅探",
            "hosts": [
                "huoshan.com"
            ],
            "regex": [
                "item_id="
            ]
        },
        {
            "name": "proxy",
            "hosts": [
                "raw.githubusercontent.com",
                "googlevideo.com",
                "cdn.v82u1l.com",
                "cdn.iz8qkg.com",
                "cdn.kin6c1.com",
                "c.biggggg.com",
                "c.olddddd.com",
                "haiwaikan.com",
                "www.histar.tv",
                "access.mypikpak.com",
                "api-drive.mypikpak.com",
                "user.mypikpak.com",
                "youtube.com",
                "uhibo.com",
                "thze.cc",
                ".*boku.*",
                ".*nivod.*",
                "*.t4tv.hz.cz",
                ".*ulivetv.*"
            ]
        },
        {
            "host": "www.iesdouyin.com",
            "rule": [
                "playwm/?video_id="
            ]
        },
        {
            "host": "www.ysgc.vip",
            "rule": [
                "getm3u8?url=http"
            ]
        },
        {
            "host": "v.douyin.com",
            "rule": [
                "playwm/?video_id="
            ]
        },
        {
            "host": "dyxs20.com",
            "rule": [
                ".m3u8"
            ]
        },
        {
            "host": "www.agemys.cc",
            "rule": [
                "cdn-tos",
                "obj/tos-cn"
            ]
        },
        {
            "host": "*",
            "rule": [
                "default.365yg.com"
            ]
        },
        {
            "host": "www.sharenice.net",
            "rule": [
                "qianpailive.com",
                "vid="
            ]
        },
        {
            "host": "*",
            "rule": [
                "douyin.com/aweme",
                "video_id="
            ]
        },
        {
            "host": "*",
            "rule": [
                "huoshan.com",
                "/item/video/"
            ]
        },
        {
            "host": "*",
            "rule": [
                "http((?!http).){12,}?\\.(m3u8|mp4|flv|avi|mkv|rm|wmv|mpg|m4a)\\?.*"
            ]
        },
        {
            "host": "*",
            "rule": [
                "http((?!http).){12,}\\.(m3u8|mp4|flv|avi|mkv|rm|wmv|mpg|m4a)"
            ]
        },
        {
            "name": "多多影音：智能AI已过滤广告🥨欢迎继续收看节目",
            "hosts": [
                "http"
            ],
            "disable": [
                "aliyuncs.com",
                "olemovienews.com",
                "ninjia.online",
                "vdtuzv.com",
                "json.icu",
                "/asp/hls/",
                "huya.com",
                "zsyzcy.cn",
                "/nby/",
                "yjys.me",
                "************:4433/Cache",
                "huohua",
                "cdn.json.icu"
            ],
            "rules": [
                {
                    "regexp": "AI"
                }
            ],
            "toLog": 0
        },
        {
            "name": "多多影音：智能AI已过滤广告🥨欢迎继续收看节目",
            "hosts": [
                "http"
            ],
            "disable": [
                "aliyuncs.com",
                "TVOD"
            ],
            "rules": [
                {
                    "regexp": "AI2"
                }
            ],
            "toLog": 0
        },
        {
            "name": "抖音嗅探",
            "hosts": [
                "douyin.com"
            ],
            "regex": [
                "is_play_url="
            ]
        },
        {
            "name": "农民嗅探",
            "hosts": [
                "toutiaovod.com"
            ],
            "regex": [
                "video/tos/cn"
            ]
        }
    ],
    "hosts": [
        "cache.ott.*.itv.cmvideo.cn=base-v4-free-mghy.e.cdn.chinamobile.com",
        "cache.ott.ystenlive.itv.cmvideo.cn=base-v4-free-mghy.e.cdn.chinamobile.com",
        "cache.ott.bestlive.itv.cmvideo.cn=base-v4-free-mghy.e.cdn.chinamobile.com",
        "cache.ott.wasulive.itv.cmvideo.cn=base-v4-free-mghy.e.cdn.chinamobile.com",
        "cache.ott.fifalive.itv.cmvideo.cn=base-v4-free-mghy.e.cdn.chinamobile.com",
        "cache.ott.hnbblive.itv.cmvideo.cn=base-v4-free-mghy.e.cdn.chinamobile.com"
    ],
    "doh": [
        {
            "name": "Google",
            "url": "https://dns.google/dns-query",
            "ips": [
                "*******",
                "*******"
            ]
        },
        {
            "name": "Cloudflare",
            "url": "https://cloudflare-dns.com/dns-query",
            "ips": [
                "*******",
                "*******",
                "2606:4700:4700::1111",
                "2606:4700:4700::1001"
            ]
        },
        {
            "name": "AdGuard",
            "url": "https://dns.adguard.com/dns-query",
            "ips": [
                "*************",
                "*************"
            ]
        },
        {
            "name": "DNSWatch",
            "url": "https://resolver2.dns.watch/dns-query",
            "ips": [
                "************",
                "************"
            ]
        },
        {
            "name": "Quad9",
            "url": "https://dns.quad9.net/dns-quer",
            "ips": [
                "*******",
                "***************"
            ]
        }
    ],
    "ads": [
        "mimg.0c1q0l.cn",
        "www.googletagmanager.com",
        "www.google-analytics.com",
        "mc.usihnbcq.cn",
        "mg.g1mm3d.cn",
        "mscs.svaeuzh.cn",
        "cnzz.hhttm.top",
        "tp.vinuxhome.com",
        "cnzz.mmstat.com",
        "www.baihuillq.com",
        "s23.cnzz.com",
        "z3.cnzz.com",
        "c.cnzz.com",
        "stj.v1vo.top",
        "z12.cnzz.com",
        "img.mosflower.cn",
        "tips.gamevvip.com",
        "ehwe.yhdtns.com",
        "xdn.cqqc3.com",
        "www.jixunkyy.cn",
        "sp.chemacid.cn",
        "hm.baidu.com",
        "s9.cnzz.com",
        "z6.cnzz.com",
        "um.cavuc.com",
        "mav.mavuz.com",
        "wofwk.aoidf3.com",
        "z5.cnzz.com",
        "xc.hubeijieshikj.cn",
        "tj.tianwenhu.com",
        "xg.gars57.cn",
        "k.jinxiuzhilv.com",
        "cdn.bootcss.com",
        "ppl.xunzhuo123.com",
        "xomk.jiangjunmh.top",
        "img.xunzhuo123.com",
        "z1.cnzz.com",
        "s13.cnzz.com",
        "xg.huataisangao.cn",
        "z7.cnzz.com",
        "xg.huataisangao.cn",
        "z2.cnzz.com",
        "s96.cnzz.com",
        "q11.cnzz.com",
        "thy.dacedsfa.cn",
        "xg.whsbpw.cn",
        "s19.cnzz.com",
        "z8.cnzz.com",
        "s4.cnzz.com",
        "f5w.as12df.top",
        "ae01.alicdn.com",
        "www.92424.cn",
        "k.wudejia.com",
        "vivovip.mmszxc.top",
        "qiu.xixiqiu.com",
        "cdnjs.hnfenxun.com",
        "cms.qdwght.com"
    ],
    "proxy": [
        "raw.githubusercontent.com",
        "googlevideo.com",
        "cdn.v82u1l.com",
        "cdn.iz8qkg.com",
        "cdn.kin6c1.com",
        "c.biggggg.com",
        "c.olddddd.com",
        "haiwaikan.com",
        "www.histar.tv",
        "youtube.com",
        "uhibo.com",
        ".*boku.*",
        ".*nivod.*",
        ".*ulivetv.*"
    ],
    "flags": [
        "youku",
        "优酷",
        "优酷",
        "优酷视频",
        "qq",
        "腾讯",
        "腾讯",
        "腾讯视频",
        "iqiyi",
        "qiyi",
        "奇艺",
        "爱奇艺",
        "爱奇艺",
        "m1905",
        "xigua",
        "letv",
        "leshi",
        "乐视",
        "乐视",
        "sohu",
        "搜狐",
        "搜狐",
        "搜狐视频",
        "tudou",
        "pptv",
        "mgtv",
        "芒果",
        "imgo",
        "芒果TV",
        "芒果TV",
        "bilibili",
        "哔哩",
        "哔哩哔哩"
    ],
    "ijk": [
        {
            "group": "软解码",
            "options": [
                {
                    "category": 4,
                    "name": "opensles",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "overlay-format",
                    "value": "842225234"
                },
                {
                    "category": 4,
                    "name": "framedrop",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "soundtouch",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "start-on-prepared",
                    "value": "1"
                },
                {
                    "category": 1,
                    "name": "http-detect-range-support",
                    "value": "0"
                },
                {
                    "category": 1,
                    "name": "fflags",
                    "value": "fastseek"
                },
                {
                    "category": 2,
                    "name": "skip_loop_filter",
                    "value": "48"
                },
                {
                    "category": 4,
                    "name": "reconnect",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "enable-accurate-seek",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "mediacodec",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "mediacodec-auto-rotate",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "mediacodec-handle-resolution-change",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "mediacodec-hevc",
                    "value": "0"
                },
                {
                    "category": 1,
                    "name": "dns_cache_timeout",
                    "value": "600000000"
                }
            ]
        },
        {
            "group": "硬解码",
            "options": [
                {
                    "category": 4,
                    "name": "opensles",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "overlay-format",
                    "value": "842225234"
                },
                {
                    "category": 4,
                    "name": "framedrop",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "soundtouch",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "start-on-prepared",
                    "value": "1"
                },
                {
                    "category": 1,
                    "name": "http-detect-range-support",
                    "value": "0"
                },
                {
                    "category": 1,
                    "name": "fflags",
                    "value": "fastseek"
                },
                {
                    "category": 2,
                    "name": "skip_loop_filter",
                    "value": "48"
                },
                {
                    "category": 4,
                    "name": "reconnect",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "enable-accurate-seek",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "mediacodec",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "mediacodec-auto-rotate",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "mediacodec-handle-resolution-change",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "mediacodec-hevc",
                    "value": "1"
                },
                {
                    "category": 1,
                    "name": "dns_cache_timeout",
                    "value": "600000000"
                }
            ]
        }
    ]
}