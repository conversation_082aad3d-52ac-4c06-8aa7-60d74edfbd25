/* TVBox Manager - 后台管理样式
 * 全新设计 - 不基于现有代码
 * 版本 1.0
 */

:root {
  /* 主题色系 */
  --primary-color: #5046e5;
  --primary-light: #7b6ef6;
  --primary-dark: #3832a8;
  --primary-contrast: #ffffff;
  
  /* 辅助色 */
  --success-color: #22c55e;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --info-color: #3b82f6;
  
  /* 中性色 */
  --bg-color: #f3f4f6;
  --surface-color: #ffffff;
  --border-color: #e5e7eb;
  --divider-color: #f0f0f0;
  
  /* 文本色 */
  --text-primary: #111827;
  --text-secondary: #4b5563;
  --text-tertiary: #9ca3af;
  --text-on-primary: #ffffff;
  
  /* 尺寸变量 */
  --header-height: 60px;
  --sidebar-width: 240px;
  --sidebar-collapsed-width: 64px;
  --sidebar-transition: 0.3s ease;
  --content-padding: 24px;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0,0,0,0.05);
  --shadow-md: 0 4px 6px -1px rgba(0,0,0,0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0,0,0,0.1);
  
  /* 字体 */
  --font-sans: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
  
  /* 圆角 */
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  --radius-full: 9999px;
}

/* 基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-sans);
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-color);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 链接样式 */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--primary-light);
}

/* 标题样式 */
h1, h2, h3, h4, h5, h6 {
  margin: 0 0 0.5em;
  font-weight: 600;
  line-height: 1.25;
  color: var(--text-primary);
}

h1 { font-size: 1.75rem; }
h2 { font-size: 1.5rem; }
h3 { font-size: 1.25rem; }
h4 { font-size: 1.125rem; }
h5 { font-size: 1rem; }
h6 { font-size: 0.875rem; }

/* 布局容器 */
.admin-container {
  display: flex;
  min-height: 100vh;
}

/* 页面内容区 */
.admin-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

/* 顶部导航栏 */
.admin-header {
  height: var(--header-height);
  background-color: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  padding: 0 24px;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: var(--shadow-sm);
}

.header-logo {
  display: flex;
  align-items: center;
  font-weight: 700;
  font-size: 18px;
  color: var(--primary-color);
}

.header-logo img,
.header-logo svg {
  height: 28px;
  margin-right: 10px;
}

.header-brand {
  margin-left: 16px;
  font-weight: 600;
  font-size: 16px;
  display: flex;
  align-items: center;
}

.header-actions {
  margin-left: auto;
  display: flex;
  align-items: center;
}

/* 侧边栏 */
.admin-sidebar {
  width: var(--sidebar-width);
  background-color: var(--surface-color);
  border-right: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  overflow-y: auto;
  z-index: 20;
  transition: width var(--sidebar-transition);
  display: flex;
  flex-direction: column;
}

.sidebar-collapsed {
  width: var(--sidebar-collapsed-width);
}

.sidebar-header {
  height: var(--header-height);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  border-bottom: 1px solid var(--border-color);
}

.sidebar-toggle {
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-toggle:hover {
  background-color: var(--bg-color);
  color: var(--text-secondary);
}

.sidebar-content {
  flex: 1;
  padding: 16px 0;
}

.sidebar-section {
  margin-bottom: 16px;
}

.sidebar-title {
  padding: 0 16px;
  margin-bottom: 8px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.04em;
  color: var(--text-tertiary);
}

.sidebar-nav {
  list-style: none;
}

.sidebar-item {
  position: relative;
}

.sidebar-link {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  color: var(--text-secondary);
  border-radius: 0;
  font-weight: 500;
  transition: all 0.2s ease;
}

.sidebar-link:hover {
  color: var(--text-primary);
  background-color: var(--bg-color);
}

.sidebar-link.active {
  color: var(--primary-color);
  background-color: rgba(80, 70, 229, 0.08);
}

.sidebar-link.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: var(--primary-color);
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}

.sidebar-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-right: 12px;
  font-size: 18px;
}

/* 侧边栏折叠状态 */
.sidebar-collapsed .sidebar-text,
.sidebar-collapsed .sidebar-title {
  display: none;
}

.sidebar-collapsed .sidebar-link {
  padding: 12px;
  justify-content: center;
}

.sidebar-collapsed .sidebar-icon {
  margin-right: 0;
}

/* 主内容区 */
.admin-main {
  flex: 1;
  padding: var(--content-padding);
  overflow: auto;
}

/* 页头 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.page-actions {
  display: flex;
  gap: 8px;
}

/* 卡片组件 */
.card {
  background-color: var(--surface-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  margin-bottom: 24px;
  transition: box-shadow 0.3s ease;
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.card-body {
  padding: 20px;
}

.card-footer {
  padding: 16px 20px;
  border-top: 1px solid var(--border-color);
  background-color: rgba(0, 0, 0, 0.02);
}

/* 表格样式 */
.table-container {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 12px 16px;
  text-align: left;
  vertical-align: middle;
}

.table thead th {
  background-color: var(--bg-color);
  font-weight: 600;
  color: var(--text-secondary);
  border-bottom: 2px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 1;
}

.table tbody tr {
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.2s;
}

.table tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.01);
}

.table tbody tr:last-child {
  border-bottom: none;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 36px;
  padding: 6px 16px;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.5;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  transition: all 0.15s ease-in-out;
  cursor: pointer;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(80, 70, 229, 0.25);
}

.btn-primary {
  color: var(--text-on-primary);
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.btn-secondary {
  color: var(--text-primary);
  background-color: var(--surface-color);
  border-color: var(--border-color);
}

.btn-secondary:hover {
  background-color: var(--bg-color);
  border-color: var(--text-tertiary);
}

.btn-success {
  color: white;
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.btn-warning {
  color: white;
  background-color: var(--warning-color);
  border-color: var(--warning-color);
}

.btn-danger {
  color: white;
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

.btn-info {
  color: white;
  background-color: var(--info-color);
  border-color: var(--info-color);
}

.btn-sm {
  min-height: 30px;
  padding: 4px 12px;
  font-size: 12px;
}

.btn-lg {
  min-height: 44px;
  padding: 8px 20px;
  font-size: 16px;
}

.btn-icon {
  margin-right: 8px;
}

/* 表单样式 */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-secondary);
}

.form-control {
  display: block;
  width: 100%;
  padding: 8px 12px;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--surface-color);
  background-clip: padding-box;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
  border-color: var(--primary-light);
  outline: 0;
  box-shadow: 0 0 0 3px rgba(80, 70, 229, 0.25);
}

.form-control::placeholder {
  color: var(--text-tertiary);
  opacity: 1;
}

.form-text {
  margin-top: 4px;
  font-size: 12px;
  color: var(--text-tertiary);
}

/* 徽章 */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  line-height: 1;
  border-radius: var(--radius-full);
  white-space: nowrap;
}

.badge-primary {
  color: var(--text-on-primary);
  background-color: var(--primary-color);
}

.badge-success {
  color: white;
  background-color: var(--success-color);
}

.badge-warning {
  color: white;
  background-color: var(--warning-color);
}

.badge-danger {
  color: white;
  background-color: var(--danger-color);
}

.badge-info {
  color: white;
  background-color: var(--info-color);
}

.badge-outline {
  background-color: transparent;
  border: 1px solid currentColor;
}

/* 状态指示器 */
.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
}

.status-dot.active {
  background-color: var(--success-color);
}

.status-dot.inactive {
  background-color: var(--text-tertiary);
}

.status-dot.warning {
  background-color: var(--warning-color);
}

.status-dot.danger {
  background-color: var(--danger-color);
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  border-radius: var(--radius-full);
}

.status-badge .status-icon {
  margin-right: 4px;
}

.status-badge.active {
  color: var(--success-color);
  background-color: rgba(34, 197, 94, 0.1);
}

.status-badge.inactive {
  color: var(--text-tertiary);
  background-color: rgba(156, 163, 175, 0.1);
}

.status-badge.warning {
  color: var(--warning-color);
  background-color: rgba(245, 158, 11, 0.1);
}

.status-badge.danger {
  color: var(--danger-color);
  background-color: rgba(239, 68, 68, 0.1);
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 8px;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  background: transparent;
  color: var(--text-secondary);
  border: none;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn:hover {
  background-color: var(--bg-color);
  color: var(--text-primary);
}

.action-btn.edit:hover {
  color: var(--info-color);
  background-color: rgba(59, 130, 246, 0.1);
}

.action-btn.delete:hover {
  color: var(--danger-color);
  background-color: rgba(239, 68, 68, 0.1);
}

.action-btn.view:hover {
  color: var(--primary-color);
  background-color: rgba(80, 70, 229, 0.1);
}

.action-btn.refresh:hover {
  color: var(--success-color);
  background-color: rgba(34, 197, 94, 0.1);
}

/* 通知/提示 */
.alert {
  padding: 12px 16px;
  margin-bottom: 16px;
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  display: flex;
  align-items: flex-start;
}

.alert-icon {
  margin-right: 12px;
  font-size: 18px;
}

.alert-content {
  flex: 1;
}

.alert-success {
  color: #0f766e;
  background-color: #d1fae5;
  border-color: #a7f3d0;
}

.alert-warning {
  color: #9a3412;
  background-color: #fef3c7;
  border-color: #fde68a;
}

.alert-danger {
  color: #b91c1c;
  background-color: #fee2e2;
  border-color: #fecaca;
}

.alert-info {
  color: #1e40af;
  background-color: #dbeafe;
  border-color: #bfdbfe;
}

/* 空状态 */
.empty-state {
  padding: 48px 24px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  color: var(--text-tertiary);
  margin-bottom: 16px;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--text-primary);
}

.empty-description {
  color: var(--text-secondary);
  max-width: 400px;
  margin: 0 auto 24px;
}

/* 用户菜单 */
.user-menu {
  position: relative;
}

.user-menu-button {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  padding: 6px;
  cursor: pointer;
  color: var(--text-primary);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: var(--text-on-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 8px;
}

.user-info {
  text-align: left;
  margin-right: 8px;
}

.user-name {
  font-weight: 500;
  line-height: 1.2;
}

.user-role {
  font-size: 12px;
  color: var(--text-tertiary);
}

.user-menu-dropdown {
  position: absolute;
  top: calc(100% + 4px);
  right: 0;
  min-width: 200px;
  background-color: var(--surface-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  z-index: 100;
  display: none;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.user-menu-dropdown.show {
  display: block;
  animation: fadeIn 0.2s ease-out;
}

.user-menu-item {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  color: var(--text-secondary);
  transition: background-color 0.2s;
}

.user-menu-item:hover {
  background-color: var(--bg-color);
  color: var(--text-primary);
}

.user-menu-icon {
  margin-right: 12px;
  width: 18px;
  text-align: center;
}

/* 面包屑 */
.breadcrumb {
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  margin-bottom: 16px;
  list-style: none;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
}

.breadcrumb-item + .breadcrumb-item {
  padding-left: 8px;
}

.breadcrumb-item + .breadcrumb-item::before {
  display: inline-block;
  padding-right: 8px;
  color: var(--text-tertiary);
  content: "/";
}

.breadcrumb-item.active {
  color: var(--text-tertiary);
}

/* 动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideDown {
  from { transform: translateY(-10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* 小工具类 */
.text-primary { color: var(--primary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }
.text-info { color: var(--info-color); }

.bg-primary { background-color: var(--primary-color); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-danger { background-color: var(--danger-color); }
.bg-info { background-color: var(--info-color); }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }
.font-normal { font-weight: 400; }
.font-light { font-weight: 300; }

.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 响应式 */
@media (max-width: 768px) {
  .admin-sidebar {
    position: fixed;
    left: -100%;
    top: var(--header-height);
    height: calc(100vh - var(--header-height));
    transition: left var(--sidebar-transition);
    box-shadow: var(--shadow-lg);
  }
  
  .admin-sidebar.mobile-show {
    left: 0;
  }
  
  .mobile-overlay {
    position: fixed;
    top: var(--header-height);
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 15;
    display: none;
  }
  
  .mobile-overlay.show {
    display: block;
  }
  
  .mobile-menu-toggle {
    display: block;
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 24px;
    cursor: pointer;
    margin-right: 16px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .page-actions {
    margin-top: 12px;
    width: 100%;
  }
  
  .page-actions .btn {
    flex: 1;
  }
  
  .user-info {
    display: none;
  }
}

@media (min-width: 769px) {
  .mobile-menu-toggle {
    display: none;
  }
} 
 * 全新设计 - 不基于现有代码
 * 版本 1.0
 */

:root {
  /* 主题色系 */
  --primary-color: #5046e5;
  --primary-light: #7b6ef6;
  --primary-dark: #3832a8;
  --primary-contrast: #ffffff;
  
  /* 辅助色 */
  --success-color: #22c55e;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --info-color: #3b82f6;
  
  /* 中性色 */
  --bg-color: #f3f4f6;
  --surface-color: #ffffff;
  --border-color: #e5e7eb;
  --divider-color: #f0f0f0;
  
  /* 文本色 */
  --text-primary: #111827;
  --text-secondary: #4b5563;
  --text-tertiary: #9ca3af;
  --text-on-primary: #ffffff;
  
  /* 尺寸变量 */
  --header-height: 60px;
  --sidebar-width: 240px;
  --sidebar-collapsed-width: 64px;
  --sidebar-transition: 0.3s ease;
  --content-padding: 24px;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0,0,0,0.05);
  --shadow-md: 0 4px 6px -1px rgba(0,0,0,0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0,0,0,0.1);
  
  /* 字体 */
  --font-sans: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
  
  /* 圆角 */
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  --radius-full: 9999px;
}

/* 基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-sans);
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-color);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 链接样式 */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--primary-light);
}

/* 标题样式 */
h1, h2, h3, h4, h5, h6 {
  margin: 0 0 0.5em;
  font-weight: 600;
  line-height: 1.25;
  color: var(--text-primary);
}

h1 { font-size: 1.75rem; }
h2 { font-size: 1.5rem; }
h3 { font-size: 1.25rem; }
h4 { font-size: 1.125rem; }
h5 { font-size: 1rem; }
h6 { font-size: 0.875rem; }

/* 布局容器 */
.admin-container {
  display: flex;
  min-height: 100vh;
}

/* 页面内容区 */
.admin-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

/* 顶部导航栏 */
.admin-header {
  height: var(--header-height);
  background-color: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  padding: 0 24px;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: var(--shadow-sm);
}

.header-logo {
  display: flex;
  align-items: center;
  font-weight: 700;
  font-size: 18px;
  color: var(--primary-color);
}

.header-logo img,
.header-logo svg {
  height: 28px;
  margin-right: 10px;
}

.header-brand {
  margin-left: 16px;
  font-weight: 600;
  font-size: 16px;
  display: flex;
  align-items: center;
}

.header-actions {
  margin-left: auto;
  display: flex;
  align-items: center;
}

/* 侧边栏 */
.admin-sidebar {
  width: var(--sidebar-width);
  background-color: var(--surface-color);
  border-right: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  overflow-y: auto;
  z-index: 20;
  transition: width var(--sidebar-transition);
  display: flex;
  flex-direction: column;
}

.sidebar-collapsed {
  width: var(--sidebar-collapsed-width);
}

.sidebar-header {
  height: var(--header-height);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  border-bottom: 1px solid var(--border-color);
}

.sidebar-toggle {
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-toggle:hover {
  background-color: var(--bg-color);
  color: var(--text-secondary);
}

.sidebar-content {
  flex: 1;
  padding: 16px 0;
}

.sidebar-section {
  margin-bottom: 16px;
}

.sidebar-title {
  padding: 0 16px;
  margin-bottom: 8px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.04em;
  color: var(--text-tertiary);
}

.sidebar-nav {
  list-style: none;
}

.sidebar-item {
  position: relative;
}

.sidebar-link {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  color: var(--text-secondary);
  border-radius: 0;
  font-weight: 500;
  transition: all 0.2s ease;
}

.sidebar-link:hover {
  color: var(--text-primary);
  background-color: var(--bg-color);
}

.sidebar-link.active {
  color: var(--primary-color);
  background-color: rgba(80, 70, 229, 0.08);
}

.sidebar-link.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: var(--primary-color);
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}

.sidebar-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-right: 12px;
  font-size: 18px;
}

/* 侧边栏折叠状态 */
.sidebar-collapsed .sidebar-text,
.sidebar-collapsed .sidebar-title {
  display: none;
}

.sidebar-collapsed .sidebar-link {
  padding: 12px;
  justify-content: center;
}

.sidebar-collapsed .sidebar-icon {
  margin-right: 0;
}

/* 主内容区 */
.admin-main {
  flex: 1;
  padding: var(--content-padding);
  overflow: auto;
}

/* 页头 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.page-actions {
  display: flex;
  gap: 8px;
}

/* 卡片组件 */
.card {
  background-color: var(--surface-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  margin-bottom: 24px;
  transition: box-shadow 0.3s ease;
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.card-body {
  padding: 20px;
}

.card-footer {
  padding: 16px 20px;
  border-top: 1px solid var(--border-color);
  background-color: rgba(0, 0, 0, 0.02);
}

/* 表格样式 */
.table-container {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 12px 16px;
  text-align: left;
  vertical-align: middle;
}

.table thead th {
  background-color: var(--bg-color);
  font-weight: 600;
  color: var(--text-secondary);
  border-bottom: 2px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 1;
}

.table tbody tr {
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.2s;
}

.table tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.01);
}

.table tbody tr:last-child {
  border-bottom: none;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 36px;
  padding: 6px 16px;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.5;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  transition: all 0.15s ease-in-out;
  cursor: pointer;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(80, 70, 229, 0.25);
}

.btn-primary {
  color: var(--text-on-primary);
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.btn-secondary {
  color: var(--text-primary);
  background-color: var(--surface-color);
  border-color: var(--border-color);
}

.btn-secondary:hover {
  background-color: var(--bg-color);
  border-color: var(--text-tertiary);
}

.btn-success {
  color: white;
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.btn-warning {
  color: white;
  background-color: var(--warning-color);
  border-color: var(--warning-color);
}

.btn-danger {
  color: white;
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

.btn-info {
  color: white;
  background-color: var(--info-color);
  border-color: var(--info-color);
}

.btn-sm {
  min-height: 30px;
  padding: 4px 12px;
  font-size: 12px;
}

.btn-lg {
  min-height: 44px;
  padding: 8px 20px;
  font-size: 16px;
}

.btn-icon {
  margin-right: 8px;
}

/* 表单样式 */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-secondary);
}

.form-control {
  display: block;
  width: 100%;
  padding: 8px 12px;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--surface-color);
  background-clip: padding-box;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
  border-color: var(--primary-light);
  outline: 0;
  box-shadow: 0 0 0 3px rgba(80, 70, 229, 0.25);
}

.form-control::placeholder {
  color: var(--text-tertiary);
  opacity: 1;
}

.form-text {
  margin-top: 4px;
  font-size: 12px;
  color: var(--text-tertiary);
}

/* 徽章 */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  line-height: 1;
  border-radius: var(--radius-full);
  white-space: nowrap;
}

.badge-primary {
  color: var(--text-on-primary);
  background-color: var(--primary-color);
}

.badge-success {
  color: white;
  background-color: var(--success-color);
}

.badge-warning {
  color: white;
  background-color: var(--warning-color);
}

.badge-danger {
  color: white;
  background-color: var(--danger-color);
}

.badge-info {
  color: white;
  background-color: var(--info-color);
}

.badge-outline {
  background-color: transparent;
  border: 1px solid currentColor;
}

/* 状态指示器 */
.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
}

.status-dot.active {
  background-color: var(--success-color);
}

.status-dot.inactive {
  background-color: var(--text-tertiary);
}

.status-dot.warning {
  background-color: var(--warning-color);
}

.status-dot.danger {
  background-color: var(--danger-color);
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  border-radius: var(--radius-full);
}

.status-badge .status-icon {
  margin-right: 4px;
}

.status-badge.active {
  color: var(--success-color);
  background-color: rgba(34, 197, 94, 0.1);
}

.status-badge.inactive {
  color: var(--text-tertiary);
  background-color: rgba(156, 163, 175, 0.1);
}

.status-badge.warning {
  color: var(--warning-color);
  background-color: rgba(245, 158, 11, 0.1);
}

.status-badge.danger {
  color: var(--danger-color);
  background-color: rgba(239, 68, 68, 0.1);
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 8px;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  background: transparent;
  color: var(--text-secondary);
  border: none;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn:hover {
  background-color: var(--bg-color);
  color: var(--text-primary);
}

.action-btn.edit:hover {
  color: var(--info-color);
  background-color: rgba(59, 130, 246, 0.1);
}

.action-btn.delete:hover {
  color: var(--danger-color);
  background-color: rgba(239, 68, 68, 0.1);
}

.action-btn.view:hover {
  color: var(--primary-color);
  background-color: rgba(80, 70, 229, 0.1);
}

.action-btn.refresh:hover {
  color: var(--success-color);
  background-color: rgba(34, 197, 94, 0.1);
}

/* 通知/提示 */
.alert {
  padding: 12px 16px;
  margin-bottom: 16px;
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  display: flex;
  align-items: flex-start;
}

.alert-icon {
  margin-right: 12px;
  font-size: 18px;
}

.alert-content {
  flex: 1;
}

.alert-success {
  color: #0f766e;
  background-color: #d1fae5;
  border-color: #a7f3d0;
}

.alert-warning {
  color: #9a3412;
  background-color: #fef3c7;
  border-color: #fde68a;
}

.alert-danger {
  color: #b91c1c;
  background-color: #fee2e2;
  border-color: #fecaca;
}

.alert-info {
  color: #1e40af;
  background-color: #dbeafe;
  border-color: #bfdbfe;
}

/* 空状态 */
.empty-state {
  padding: 48px 24px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  color: var(--text-tertiary);
  margin-bottom: 16px;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--text-primary);
}

.empty-description {
  color: var(--text-secondary);
  max-width: 400px;
  margin: 0 auto 24px;
}

/* 用户菜单 */
.user-menu {
  position: relative;
}

.user-menu-button {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  padding: 6px;
  cursor: pointer;
  color: var(--text-primary);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: var(--text-on-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 8px;
}

.user-info {
  text-align: left;
  margin-right: 8px;
}

.user-name {
  font-weight: 500;
  line-height: 1.2;
}

.user-role {
  font-size: 12px;
  color: var(--text-tertiary);
}

.user-menu-dropdown {
  position: absolute;
  top: calc(100% + 4px);
  right: 0;
  min-width: 200px;
  background-color: var(--surface-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  z-index: 100;
  display: none;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.user-menu-dropdown.show {
  display: block;
  animation: fadeIn 0.2s ease-out;
}

.user-menu-item {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  color: var(--text-secondary);
  transition: background-color 0.2s;
}

.user-menu-item:hover {
  background-color: var(--bg-color);
  color: var(--text-primary);
}

.user-menu-icon {
  margin-right: 12px;
  width: 18px;
  text-align: center;
}

/* 面包屑 */
.breadcrumb {
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  margin-bottom: 16px;
  list-style: none;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
}

.breadcrumb-item + .breadcrumb-item {
  padding-left: 8px;
}

.breadcrumb-item + .breadcrumb-item::before {
  display: inline-block;
  padding-right: 8px;
  color: var(--text-tertiary);
  content: "/";
}

.breadcrumb-item.active {
  color: var(--text-tertiary);
}

/* 动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideDown {
  from { transform: translateY(-10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* 小工具类 */
.text-primary { color: var(--primary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }
.text-info { color: var(--info-color); }

.bg-primary { background-color: var(--primary-color); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-danger { background-color: var(--danger-color); }
.bg-info { background-color: var(--info-color); }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }
.font-normal { font-weight: 400; }
.font-light { font-weight: 300; }

.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 响应式 */
@media (max-width: 768px) {
  .admin-sidebar {
    position: fixed;
    left: -100%;
    top: var(--header-height);
    height: calc(100vh - var(--header-height));
    transition: left var(--sidebar-transition);
    box-shadow: var(--shadow-lg);
  }
  
  .admin-sidebar.mobile-show {
    left: 0;
  }
  
  .mobile-overlay {
    position: fixed;
    top: var(--header-height);
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 15;
    display: none;
  }
  
  .mobile-overlay.show {
    display: block;
  }
  
  .mobile-menu-toggle {
    display: block;
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 24px;
    cursor: pointer;
    margin-right: 16px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .page-actions {
    margin-top: 12px;
    width: 100%;
  }
  
  .page-actions .btn {
    flex: 1;
  }
  
  .user-info {
    display: none;
  }
}

@media (min-width: 769px) {
  .mobile-menu-toggle {
    display: none;
  }
} 