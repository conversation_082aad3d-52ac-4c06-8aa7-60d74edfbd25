<template>
  <div class="interface-detail-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button icon="ArrowLeft" @click="$router.back()">返回</el-button>
        <div class="header-info">
          <h2>{{ interfaceData?.name || '接口详情' }}</h2>
          <p>查看和管理接口源的详细信息</p>
        </div>
      </div>
      <div class="header-right">
        <el-button icon="Refresh" @click="refreshInterface">刷新接口</el-button>
        <el-button type="primary" icon="Star" @click="subscribeInterface">订阅</el-button>
      </div>
    </div>
    
    <div v-loading="loading" class="detail-content">
      <!-- 整体概览卡片 -->
      <el-card class="overview-card">
        <template #header>
          <span>接口概览</span>
        </template>

        <div class="overview-content">
          <el-row :gutter="24">
            <!-- 左侧：基本信息 -->
            <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
              <div class="info-section">
                <h4 class="section-title">基本信息</h4>
                <el-descriptions :column="1" size="small" border>
              <el-descriptions-item label="接口名称">
                {{ interfaceData?.name }}
              </el-descriptions-item>
              <el-descriptions-item label="接口地址">
                <div class="url-content">
                  <el-text class="url-text" truncated>{{ interfaceData?.url }}</el-text>
                  <el-button size="small" text icon="CopyDocument" @click="copyUrl" />
                </div>
              </el-descriptions-item>
              <el-descriptions-item label="分类">
                <el-tag v-if="interfaceData?.category" size="small">
                  {{ interfaceData.category }}
                </el-tag>
                <span v-else class="text-muted">-</span>
              </el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="getStatusType(interfaceData?.status)" size="small">
                  {{ getStatusText(interfaceData?.status) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="公开状态">
                <el-tag :type="interfaceData?.is_public ? 'success' : 'info'" size="small">
                  {{ interfaceData?.is_public ? '公开' : '私有' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="解密方法">
                {{ interfaceData?.decrypt_method || '无需解密' }}
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">
                {{ formatDate(interfaceData?.created_at) }}
              </el-descriptions-item>
              <el-descriptions-item label="更新时间">
                {{ formatDate(interfaceData?.updated_at) }}
              </el-descriptions-item>
              <el-descriptions-item label="自动刷新">
                <div class="refresh-setting">
                  <el-select
                    v-model="refreshInterval"
                    size="small"
                    style="width: 120px"
                    @change="updateRefreshInterval"
                  >
                    <el-option label="关闭" :value="0" />
                    <el-option label="5分钟" :value="300" />
                    <el-option label="10分钟" :value="600" />
                    <el-option label="30分钟" :value="1800" />
                    <el-option label="1小时" :value="3600" />
                    <el-option label="6小时" :value="21600" />
                    <el-option label="12小时" :value="43200" />
                    <el-option label="24小时" :value="86400" />
                  </el-select>
                </div>
              </el-descriptions-item>
            </el-descriptions>

            <!-- 标签区域 -->
            <div v-if="interfaceData?.tags" class="tags-section">
              <div class="section-label">标签</div>
              <div class="tags-list">
                <el-tag
                  v-for="tag in interfaceData.tags.split(',')"
                  :key="tag"
                  size="small"
                  class="tag-item"
                >
                  {{ tag.trim() }}
                </el-tag>
              </div>
            </div>

            <!-- 描述区域 -->
            <div v-if="interfaceData?.description" class="description-section">
              <div class="section-label">描述</div>
              <p class="description-text">{{ interfaceData.description }}</p>
            </div>
          </div>
        </el-col>

        <!-- 右侧：统计和时间信息 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <!-- 统计信息 -->
          <div class="stats-section">
            <h4 class="section-title">统计信息</h4>
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-icon sites">
                  <el-icon><Monitor /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ interfaceData?.sites_count || 0 }}</div>
                  <div class="stat-label">站点数量</div>
                </div>
              </div>

              <div class="stat-item">
                <div class="stat-icon lives">
                  <el-icon><VideoPlay /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ interfaceData?.lives_count || 0 }}</div>
                  <div class="stat-label">直播源</div>
                </div>
              </div>

              <div class="stat-item">
                <div class="stat-icon parses">
                  <el-icon><Link /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ interfaceData?.parses_count || 0 }}</div>
                  <div class="stat-label">解析器</div>
                </div>
              </div>

              <div class="stat-item">
                <div class="stat-icon success">
                  <el-icon><Check /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ interfaceData?.success_count || 0 }}</div>
                  <div class="stat-label">成功次数</div>
                </div>
              </div>

              <div class="stat-item">
                <div class="stat-icon error">
                  <el-icon><Close /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ interfaceData?.error_count || 0 }}</div>
                  <div class="stat-label">错误次数</div>
                </div>
              </div>

              <div class="stat-item">
                <div class="stat-icon rate">
                  <el-icon><TrendCharts /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ interfaceData?.success_rate || 0 }}%</div>
                  <div class="stat-label">成功率</div>
                </div>
              </div>
            </div>

            <!-- 成功率进度条 -->
            <div class="success-rate-section">
              <div class="section-label">成功率趋势</div>
              <el-progress
                :percentage="interfaceData?.success_rate || 0"
                :color="getProgressColor(interfaceData?.success_rate || 0)"
                :stroke-width="6"
              />
            </div>
          </div>

          <!-- 时间信息 -->
          <div class="time-section">
            <h4 class="section-title">时间信息</h4>
            <div class="time-grid">
              <div class="time-item">
                <el-icon color="#409EFF"><Clock /></el-icon>
                <span class="time-label">创建</span>
                <span class="time-value">{{ formatDate(interfaceData?.created_at) }}</span>
              </div>

              <div class="time-item">
                <el-icon color="#67C23A"><Refresh /></el-icon>
                <span class="time-label">更新</span>
                <span class="time-value">{{ formatDate(interfaceData?.last_update_at) }}</span>
              </div>

              <div class="time-item">
                <el-icon color="#67C23A"><Check /></el-icon>
                <span class="time-label">成功</span>
                <span class="time-value">{{ formatDate(interfaceData?.last_success_at) }}</span>
              </div>

              <div class="time-item">
                <el-icon color="#E6A23C"><Timer /></el-icon>
                <span class="time-label">下次</span>
                <span class="time-value">{{ formatDate(interfaceData?.next_update_at) }}</span>
              </div>
            </div>

            <!-- 错误信息 -->
            <div v-if="interfaceData?.last_error_message" class="error-section">
              <div class="section-label">错误信息</div>
              <el-alert
                :title="interfaceData.last_error_message"
                type="error"
                :closable="false"
                show-icon
                size="small"
              />
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </el-card>
      
      <!-- 配置内容和文件管理 -->
      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="24">
          <el-card class="config-card">
            <template #header>
              <div class="card-header">
                <span>配置管理</span>
              </div>
            </template>

            <el-tabs v-model="activeTab" type="border-card">
              <!-- 配置内容标签页 -->
              <el-tab-pane label="配置内容" name="config">
                <div class="tab-header">
                  <div class="config-controls">
                    <!-- 配置类型切换 -->
                    <el-radio-group v-model="configType" size="small" @change="onConfigTypeChange">
                      <el-radio-button label="original">原始配置</el-radio-button>
                      <el-radio-button
                        label="localized"
                        :disabled="!interfaceData?.enable_localization || !hasLocalizedConfig"
                      >
                        本地化配置
                      </el-radio-button>
                    </el-radio-group>

                    <!-- 操作按钮 -->
                    <el-button-group>
                      <el-button size="small" icon="Refresh" @click="loadConfig">刷新</el-button>
                      <el-button
                        size="small"
                        icon="Edit"
                        @click="toggleEdit"
                        :disabled="configType === 'localized'"
                      >
                        {{ isEditing ? '取消编辑' : '编辑' }}
                      </el-button>
                      <el-button v-if="isEditing" size="small" type="primary" icon="Check" @click="saveConfig">
                        保存
                      </el-button>
                      <el-button size="small" icon="Download" @click="downloadConfig">下载</el-button>
                    </el-button-group>
                  </div>
                </div>

                <div v-if="configContent" class="config-content">
                  <el-input
                    v-model="editableConfig"
                    type="textarea"
                    :rows="20"
                    :readonly="!isEditing"
                    class="config-textarea"
                    placeholder="请输入JSON配置内容..."
                  />
                </div>
                <div v-else class="config-placeholder">
                  <el-empty description="暂无配置内容" />
                </div>
              </el-tab-pane>

              <!-- 文件管理标签页 -->
              <el-tab-pane label="文件管理" name="files">
                <div class="file-management">
                  <div class="tab-header">
                    <div class="localization-info">
                      <el-tag v-if="interfaceData?.enable_localization" type="success" size="small">
                        本地化已启用
                      </el-tag>
                      <el-tag v-else type="info" size="small">
                        本地化未启用
                      </el-tag>
                      <span v-if="localizationStatus" class="status-text">
                        状态: {{ getLocalizationStatusText(localizationStatus.status) }}
                        ({{ localizationStatus.files_count }} 个文件)
                      </span>
                    </div>
                    <el-button-group>
                      <el-button size="small" icon="Refresh" @click="loadFileList">刷新文件</el-button>
                      <el-button
                        v-if="interfaceData?.enable_localization && localizationStatus?.files_count > 0"
                        size="small"
                        icon="View"
                        @click="viewLocalFiles"
                      >
                        查看详情
                      </el-button>
                    </el-button-group>
                  </div>

                  <!-- 文件列表 -->
                  <div v-if="interfaceData?.enable_localization" class="file-list">
                    <el-table
                      :data="fileList"
                      v-loading="fileListLoading"
                      style="width: 100%"
                      empty-text="暂无文件数据"
                    >
                      <el-table-column prop="file_type" label="文件类型" width="120">
                        <template #default="{ row }">
                          <el-tag size="small" :type="getFileTypeColor(row.file_type)">
                            {{ row.file_type }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column prop="original_filename" label="文件名" min-width="200" />
                      <el-table-column prop="download_status" label="状态" width="100">
                        <template #default="{ row }">
                          <el-tag
                            size="small"
                            :type="getStatusType(row.download_status)"
                          >
                            {{ getStatusText(row.download_status) }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column prop="file_size" label="文件大小" width="120">
                        <template #default="{ row }">
                          {{ formatFileSize(row.file_size) }}
                        </template>
                      </el-table-column>
                      <el-table-column prop="local_path" label="本地路径" min-width="200">
                        <template #default="{ row }">
                          <el-text class="path-text" truncated>{{ row.local_path }}</el-text>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" width="120">
                        <template #default="{ row }">
                          <el-button
                            v-if="row.download_status === 'completed'"
                            size="small"
                            type="primary"
                            link
                            @click="openFile(row)"
                          >
                            访问
                          </el-button>
                          <el-button
                            v-if="row.download_status === 'failed' || row.download_status === 'error'"
                            size="small"
                            type="danger"
                            link
                            @click="showError(row)"
                          >
                            查看错误
                          </el-button>
                          <el-button
                            v-if="row.download_status === 'skipped'"
                            size="small"
                            type="warning"
                            link
                            @click="showError(row)"
                          >
                            查看原因
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                  <div v-else class="no-localization">
                    <el-empty description="请先启用本地化功能">
                      <el-button type="primary" @click="goToEdit">去编辑页面启用</el-button>
                    </el-empty>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 本地化进度弹窗 -->
    <el-dialog
      v-model="showLocalizationDialog"
      title="本地化详情"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div v-if="localizationDialogData" class="localization-dialog">
        <!-- 阶段1：分析阶段 -->
        <div v-if="localizationPhase === 'analyzing'" class="phase-analyzing">
          <div class="phase-header">
            <el-icon class="rotating"><Loading /></el-icon>
            <h3>正在分析配置文件...</h3>
          </div>
          <p>正在扫描配置中需要本地化的文件，请稍候...</p>
        </div>

        <!-- 阶段2：下载阶段 -->
        <div v-else-if="localizationPhase === 'downloading'" class="phase-downloading">
          <div class="phase-header">
            <el-icon class="rotating"><Loading /></el-icon>
            <h3>正在下载文件...</h3>
          </div>

          <div class="download-progress">
            <el-progress
              :percentage="localizationProgress"
              :stroke-width="8"
              status="success"
            />
            <p class="progress-text">
              {{ downloadedFiles.length }} / {{ totalFiles }} 个文件已完成
            </p>
          </div>

          <div v-if="currentDownloadFile" class="current-download">
            <p><strong>正在下载：</strong>{{ currentDownloadFile }}</p>
          </div>

          <div class="downloaded-files">
            <h4>已下载文件：</h4>
            <div class="file-list">
              <div
                v-for="file in downloadedFiles"
                :key="file.name"
                class="file-item"
              >
                <el-tag :type="getFileTypeColor(file.type)" size="small">
                  {{ file.type }}
                </el-tag>
                <span class="file-name">{{ file.name }}</span>
                <el-icon class="success-icon"><Check /></el-icon>
              </div>
            </div>
          </div>
        </div>

        <!-- 阶段3：完成阶段 -->
        <div v-else-if="localizationPhase === 'completed'" class="phase-completed">
          <div class="phase-header">
            <el-icon class="success-icon"><CircleCheck /></el-icon>
            <h3>本地化完成</h3>
          </div>

          <div class="completion-stats">
            <el-row :gutter="16">
              <el-col :span="8">
                <div class="stat-item">
                  <div class="stat-number">{{ localizationDialogData.files_count }}</div>
                  <div class="stat-label">总文件数</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="stat-item">
                  <div class="stat-number success">{{ localizationDialogData.completed_files || localizationDialogData.files_count }}</div>
                  <div class="stat-label">成功下载</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="stat-item">
                  <div class="stat-number">{{ formatFileSize(localizationDialogData.total_size) }}</div>
                  <div class="stat-label">总大小</div>
                </div>
              </el-col>
            </el-row>
          </div>

          <div class="file-details">
            <h4>文件详情：</h4>
            <div class="file-categories">
              <div
                v-for="(files, category) in groupFilesByCategory(localizationDialogData.files)"
                :key="category"
                class="category-group"
              >
                <div class="category-header">
                  <el-tag :type="getFileTypeColor(category)" size="small">
                    {{ category.toUpperCase() }}
                  </el-tag>
                  <span class="category-count">{{ files.length }} 个文件</span>
                </div>
                <div class="category-files">
                  <div
                    v-for="file in files"
                    :key="file.local_path"
                    class="file-detail-item"
                  >
                    <span class="file-name">{{ file.original_filename }}</span>
                    <span class="file-size">{{ formatFileSize(file.file_size) }}</span>
                    <el-button
                      size="small"
                      type="primary"
                      link
                      @click="openFile(file)"
                    >
                      查看
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="local-path">
            <p><strong>本地化目录：</strong>{{ localizationDialogData.local_dir }}</p>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button
            v-if="localizationPhase === 'completed'"
            @click="showLocalizationDialog = false"
          >
            关闭
          </el-button>
          <el-button
            v-else
            @click="showLocalizationDialog = false"
            :disabled="localizationPhase === 'downloading'"
          >
            后台运行
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { interfaceApi } from '@/api/interfaces'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatDate } from '@/utils/date'
import {
  Monitor,
  VideoPlay,
  Link,
  Check,
  Close,
  TrendCharts,
  Clock,
  Refresh,
  Timer,
  Loading,
  CircleCheck
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const interfaceData = ref(null)
const configContent = ref('')
const editableConfig = ref('')
const isEditing = ref(false)
const configType = ref('original') // 'original' 或 'localized'
const refreshInterval = ref(0)
const refreshTimer = ref(null)
const activeTab = ref('config')
const fileList = ref([])
const fileListLoading = ref(false)
const localizationStatus = ref(null)
const hasLocalizedConfig = ref(false)

// 本地化弹窗相关
const showLocalizationDialog = ref(false)
const localizationDialogData = ref(null)
const localizationPhase = ref('analyzing') // analyzing, downloading, completed
const localizationProgress = ref(0)
const currentDownloadFile = ref('')
const downloadedFiles = ref([])
const totalFiles = ref(0)

// 方法
const loadInterfaceDetail = async () => {
  try {
    loading.value = true
    const response = await interfaceApi.getInterfaceSource(route.params.id)
    interfaceData.value = response.data

    // 恢复自动刷新设置
    if (interfaceData.value?.update_interval) {
      const intervalSeconds = interfaceData.value.update_interval
      refreshInterval.value = intervalSeconds

      // 如果有自动刷新设置，启动定时器
      if (intervalSeconds > 0) {
        refreshTimer.value = setInterval(() => {
          refreshInterface()
        }, intervalSeconds * 1000)
      }
    }
  } catch (error) {
    ElMessage.error('加载接口详情失败')
    router.back()
  } finally {
    loading.value = false
  }
}

const onConfigTypeChange = async () => {
  await loadConfig()
}

const loadConfig = async () => {
  try {
    // 根据配置类型获取不同的配置内容
    let rawContent = ''

    if (configType.value === 'localized') {
      // 获取本地化配置
      try {
        const response = await interfaceApi.getLocalizedConfig(route.params.id)
        // 如果返回的是对象，需要转换为JSON字符串
        if (typeof response.data === 'object') {
          rawContent = JSON.stringify(response.data, null, 2)
        } else {
          rawContent = response.data
        }
      } catch (error) {
        ElMessage.error('获取本地化配置失败')
        configType.value = 'original' // 回退到原始配置
        return
      }
    } else {
      // 获取原始配置
      rawContent = interfaceData.value?.config_content || ''
    }

    if (rawContent) {
      // 尝试解析为JSON
      try {
        const parsed = JSON.parse(rawContent)
        const formattedJson = JSON.stringify(parsed, null, 2)
        configContent.value = formattedJson
        editableConfig.value = formattedJson
      } catch (jsonError) {
        // 如果不是有效的JSON，尝试从混合内容中提取JSON
        try {
          // 查找Base64编码的JSON数据
          const base64Pattern = /[A-Za-z0-9+/]{200,}={0,2}/g
          const matches = rawContent.match(base64Pattern)

          let extractedJson = null
          if (matches) {
            for (const match of matches) {
              try {
                const decoded = atob(match)
                const parsed = JSON.parse(decoded)
                if (parsed && typeof parsed === 'object' && (parsed.sites || parsed.spider)) {
                  extractedJson = JSON.stringify(parsed, null, 2)
                  break
                }
              } catch (e) {
                continue
              }
            }
          }

          if (extractedJson) {
            configContent.value = extractedJson
            editableConfig.value = extractedJson
          } else {
            // 如果无法提取，显示原始内容
            configContent.value = rawContent
            editableConfig.value = rawContent
          }
        } catch (e) {
          configContent.value = rawContent
          editableConfig.value = rawContent
        }
      }
    } else {
      // 如果没有配置内容，显示空的模板
      const emptyConfig = {
        spider: "",
        sites: [],
        lives: [],
        parses: []
      }
      const formattedJson = JSON.stringify(emptyConfig, null, 2)
      configContent.value = formattedJson
      editableConfig.value = formattedJson
    }
  } catch (error) {
    ElMessage.error('加载配置内容失败')
  }
}

const refreshInterface = async () => {
  try {
    await interfaceApi.parseInterfaceSource(route.params.id)
    ElMessage.success('接口刷新成功')
    loadInterfaceDetail()
    loadConfig()
  } catch (error) {
    ElMessage.error('接口刷新失败')
  }
}

const toggleEdit = () => {
  if (isEditing.value) {
    // 取消编辑，恢复原内容
    editableConfig.value = configContent.value
  }
  isEditing.value = !isEditing.value
}

const saveConfig = async () => {
  try {
    // 验证JSON格式
    JSON.parse(editableConfig.value)

    // 这里应该调用保存配置的API
    // await interfaceApi.updateInterfaceConfig(route.params.id, editableConfig.value)

    configContent.value = editableConfig.value
    isEditing.value = false
    ElMessage.success('配置保存成功')
  } catch (error) {
    if (error instanceof SyntaxError) {
      ElMessage.error('JSON格式错误，请检查配置内容')
    } else {
      ElMessage.error('保存配置失败')
    }
  }
}

const updateRefreshInterval = async (interval) => {
  try {
    // 保存自动刷新设置到后端
    await interfaceApi.updateInterfaceSource(route.params.id, {
      update_interval: interval // 已经是秒数，不需要转换
    })

    // 清除现有定时器
    if (refreshTimer.value) {
      clearInterval(refreshTimer.value)
      refreshTimer.value = null
    }

    // 设置新的定时器
    if (interval > 0) {
      refreshTimer.value = setInterval(() => {
        refreshInterface()
      }, interval * 1000) // 转换为毫秒

      // 显示友好的时间格式
      const timeText = getTimeText(interval)
      ElMessage.success(`已设置每${timeText}自动刷新`)
    } else {
      ElMessage.info('已关闭自动刷新')
    }

    // 更新本地数据
    if (interfaceData.value) {
      interfaceData.value.update_interval = interval
    }
  } catch (error) {
    ElMessage.error('保存自动刷新设置失败')
  }
}

// 将秒数转换为友好的时间文本
const getTimeText = (seconds) => {
  if (seconds === 300) return '5分钟'
  if (seconds === 600) return '10分钟'
  if (seconds === 1800) return '30分钟'
  if (seconds === 3600) return '1小时'
  if (seconds === 21600) return '6小时'
  if (seconds === 43200) return '12小时'
  if (seconds === 86400) return '24小时'
  return `${seconds}秒`
}

const subscribeInterface = async () => {
  try {
    await interfaceApi.subscribeInterfaceSource(route.params.id, {})
    ElMessage.success('订阅成功')
  } catch (error) {
    ElMessage.error('订阅失败')
  }
}

const copyUrl = async () => {
  try {
    await navigator.clipboard.writeText(interfaceData.value.url)
    ElMessage.success('URL已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const downloadConfig = () => {
  const content = isEditing.value ? editableConfig.value : configContent.value
  if (!content) {
    ElMessage.warning('暂无配置内容可下载')
    return
  }

  try {
    // 验证JSON格式
    JSON.parse(content)

    const blob = new Blob([content], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${interfaceData.value?.name || 'config'}_config.json`
    a.click()
    URL.revokeObjectURL(url)
    ElMessage.success('配置下载成功')
  } catch (error) {
    ElMessage.error('配置格式错误，无法下载')
  }
}

const getStatusType = (status) => {
  const statusMap = {
    // 接口状态
    online: 'success',
    offline: 'info',
    error: 'danger',
    unknown: 'warning',
    // 下载状态
    completed: 'success',
    failed: 'danger',
    error: 'danger',
    skipped: 'warning',
    downloading: 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    // 接口状态
    online: '在线',
    offline: '离线',
    error: '错误',
    unknown: '未知',
    // 下载状态
    completed: '成功',
    failed: '失败',
    error: '失败',
    skipped: '已跳过',
    downloading: '下载中'
  }
  return statusMap[status] || '未知'
}

const getProgressColor = (percentage) => {
  if (percentage >= 80) return '#67c23a'
  if (percentage >= 60) return '#e6a23c'
  return '#f56c6c'
}

// 文件管理相关方法
const loadFileList = async () => {
  if (!interfaceData.value?.enable_localization) {
    return
  }

  try {
    fileListLoading.value = true
    const response = await interfaceApi.getLocalizationStatus(route.params.id)
    if (response.data.success) {
      localizationStatus.value = response.data
      fileList.value = response.data.files || []
      hasLocalizedConfig.value = response.data.status === 'completed'
    }
  } catch (error) {
    ElMessage.error('加载文件列表失败')
  } finally {
    fileListLoading.value = false
  }
}

const viewLocalFiles = async () => {
  try {
    const response = await interfaceApi.getLocalizationStatus(route.params.id)
    if (response.data.success) {
      const status = response.data
      showLocalizationDialog.value = true
      localizationDialogData.value = status

      // 如果是已完成状态，直接显示完成阶段
      if (status.status === 'completed') {
        localizationPhase.value = 'completed'
      } else {
        // 否则模拟本地化过程
        simulateLocalizationProcess()
      }
    }
  } catch (error) {
    ElMessage.error('获取文件信息失败')
  }
}

const getLocalizationStatusText = (status) => {
  const statusMap = {
    'not_localized': '未本地化',
    'completed': '已完成',
    'partial': '部分完成',
    'failed': '失败'
  }
  return statusMap[status] || status
}

const getFileTypeColor = (fileType) => {
  const colorMap = {
    'spider': 'primary',
    'api': 'success',
    'js': 'warning',
    'live': 'info',
    'json': 'danger'
  }
  return colorMap[fileType] || ''
}

const formatFileSize = (size) => {
  if (!size) return '-'
  if (size < 1024) return `${size} B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`
  return `${(size / (1024 * 1024)).toFixed(1)} MB`
}

const openFile = (file) => {
  // 从本地路径中提取接口目录名和相对路径
  // 例如：data\localized\interface_1_真实TVBox配置\js\drpy2.js -> interface_1_真实TVBox配置 和 js/drpy2.js
  const localPath = file.local_path.replace(/\\/g, '/') // 统一使用正斜杠
  const pathParts = localPath.split('/')
  const interfaceIndex = pathParts.findIndex(part => part.startsWith('interface_'))

  let interfaceDir, relativePath
  if (interfaceIndex >= 0 && interfaceIndex < pathParts.length - 1) {
    // 直接从路径中提取接口目录名，保持与后端一致
    interfaceDir = pathParts[interfaceIndex]
    relativePath = pathParts.slice(interfaceIndex + 1).join('/')
  } else {
    // 如果找不到接口目录，使用相对路径构建
    const interfaceName = interfaceData.value?.name || `interface_${route.params.id}`
    // 使用与后端相同的清理规则（Python的re.sub）
    const safeName = interfaceName.replace(/[^\w\-_.]/g, '_')
    interfaceDir = `interface_${route.params.id}_${safeName}`
    relativePath = file.relative_path.replace(/^\.\//, '') // 移除开头的 ./
  }

  // 使用前端端口，通过代理访问后端的静态文件
  const fileUrl = `/localized/${interfaceDir}/${relativePath}`
  console.log('打开文件URL:', fileUrl) // 调试日志
  console.log('接口目录:', interfaceDir) // 调试日志
  console.log('相对路径:', relativePath) // 调试日志
  window.open(fileUrl, '_blank')
}

const showError = (file) => {
  ElMessageBox.alert(
    file.error_message || '未知错误',
    '文件下载错误',
    {
      confirmButtonText: '确定',
      type: 'error'
    }
  )
}

const goToEdit = () => {
  router.push(`/interfaces/${route.params.id}/edit`)
}

// 本地化弹窗辅助方法
const groupFilesByCategory = (files) => {
  if (!files) return {}

  const grouped = {}
  files.forEach(file => {
    const category = file.file_type || 'unknown'
    if (!grouped[category]) {
      grouped[category] = []
    }
    grouped[category].push(file)
  })
  return grouped
}

const simulateLocalizationProcess = () => {
  // 模拟本地化过程的三个阶段
  localizationPhase.value = 'analyzing'

  // 阶段1：分析（2秒）
  setTimeout(() => {
    localizationPhase.value = 'downloading'
    totalFiles.value = localizationDialogData.value?.files_count || 0
    downloadedFiles.value = []
    localizationProgress.value = 0

    // 阶段2：模拟下载过程
    const files = localizationDialogData.value?.files || []
    let currentIndex = 0

    const downloadNext = () => {
      if (currentIndex < files.length) {
        const file = files[currentIndex]
        currentDownloadFile.value = file.original_filename

        setTimeout(() => {
          downloadedFiles.value.push({
            name: file.original_filename,
            type: file.file_type
          })
          localizationProgress.value = Math.round(((currentIndex + 1) / files.length) * 100)
          currentIndex++

          if (currentIndex < files.length) {
            downloadNext()
          } else {
            // 下载完成，进入完成阶段
            setTimeout(() => {
              localizationPhase.value = 'completed'
              currentDownloadFile.value = ''
            }, 500)
          }
        }, Math.random() * 1000 + 500) // 随机延迟500-1500ms
      }
    }

    downloadNext()
  }, 2000)
}

// 生命周期
onMounted(async () => {
  await loadInterfaceDetail()
  loadConfig()
  loadFileList()
})

onUnmounted(() => {
  // 清理定时器
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
})
</script>

<style lang="scss" scoped>
.interface-detail-page {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .header-info {
        h2 {
          margin: 0 0 4px 0;
          color: var(--el-text-color-primary);
        }
        
        p {
          margin: 0;
          color: var(--el-text-color-regular);
          font-size: 14px;
        }
      }
    }
    
    .header-right {
      display: flex;
      gap: 12px;
    }
  }

  .overview-card {
    margin-bottom: 20px;

    :deep(.el-card__header) {
      padding: 16px 24px;
      border-bottom: 1px solid var(--el-border-color-light);

      span {
        font-weight: 600;
        font-size: 18px;
        color: var(--el-text-color-primary);
      }
    }

    :deep(.el-card__body) {
      padding: 24px;
    }
  }

  .overview-content {
    .section-title {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      border-bottom: 2px solid var(--el-color-primary);
      padding-bottom: 8px;
    }

    .section-label {
      font-size: 14px;
      font-weight: 500;
      color: var(--el-text-color-regular);
      margin-bottom: 8px;
    }
  }

  .info-section {

    margin-bottom: 24px;

    .url-content {
      display: flex;
      align-items: center;
      gap: 8px;

      .url-text {
        flex: 1;
        font-family: monospace;
        font-size: 12px;
      }
    }

    .tags-section,
    .description-section {
      margin-top: 16px;

      .description-text {
        margin: 0;
        color: var(--el-text-color-regular);
        line-height: 1.6;
        font-size: 14px;
      }
    }

    .tags-list {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
    }
  }
  
  .stats-section {
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;
      margin-bottom: 16px;
      
      .stat-item {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .stat-icon {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          
          &.sites { background: var(--el-color-primary-light-9); color: var(--el-color-primary); }
          &.lives { background: var(--el-color-success-light-9); color: var(--el-color-success); }
          &.parses { background: var(--el-color-warning-light-9); color: var(--el-color-warning); }
          &.success { background: var(--el-color-success-light-9); color: var(--el-color-success); }
          &.error { background: var(--el-color-danger-light-9); color: var(--el-color-danger); }
          &.rate { background: var(--el-color-info-light-9); color: var(--el-color-info); }
        }
        
        .stat-info {
          .stat-value {
            font-size: 18px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            line-height: 1;
          }
          
          .stat-label {
            font-size: 12px;
            color: var(--el-text-color-regular);
            margin-top: 2px;
          }
        }
      }
    }
    
    .success-rate-section {
      margin-top: 16px;
    }
  }

  .time-section {
    .time-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
      margin-bottom: 16px;

      .time-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        background: var(--el-fill-color-lighter);
        border-radius: 6px;
        font-size: 12px;

        .time-label {
          color: var(--el-text-color-regular);
          min-width: 32px;
        }

        .time-value {
          color: var(--el-text-color-primary);
          font-weight: 500;
          flex: 1;
          text-align: right;
        }
      }
    }

    .error-section {
      margin-top: 16px;
    }
  }
  
  .config-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .config-textarea {
      :deep(.el-textarea__inner) {
        font-family: monospace;
        font-size: 12px;
        line-height: 1.5;
      }
    }
    
    .config-placeholder {
      text-align: center;
      padding: 40px 0;
    }
  }
  
  .text-muted {
    color: var(--el-text-color-placeholder);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .interface-detail-page {
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
      
      .header-left {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
      }
      
      .header-right {
        width: 100%;
        justify-content: flex-end;
      }
    }
    
    .stats-card .stats-grid {
      grid-template-columns: 1fr;
    }
  }
}

// 文件管理样式
.file-management {
  .tab-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px 0;
    border-bottom: 1px solid var(--el-border-color-lighter);

    .localization-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .status-text {
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
    }
  }

  .file-list {
    .path-text {
      font-family: 'Courier New', monospace;
      font-size: 12px;
    }
  }

  .no-localization {
    padding: 40px 0;
    text-align: center;
  }
}

.tab-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.config-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 100%;
  justify-content: space-between;
}

/* 本地化弹窗样式 */
.localization-dialog {
  padding: 20px 0;

  .phase-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;

    h3 {
      margin: 0;
      font-size: 18px;
      color: #303133;
    }
  }

  .rotating {
    animation: rotate 2s linear infinite;
    color: #409eff;
    font-size: 20px;
  }

  @keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  .success-icon {
    color: #67c23a;
    font-size: 20px;
  }

  // 下载阶段样式
  .download-progress {
    margin-bottom: 20px;

    .progress-text {
      text-align: center;
      margin-top: 8px;
      color: #606266;
      font-size: 14px;
    }
  }

  .current-download {
    background: #f5f7fa;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 20px;
  }

  .downloaded-files {
    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      color: #303133;
    }

    .file-list {
      max-height: 200px;
      overflow-y: auto;
    }

    .file-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 6px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .file-name {
        flex: 1;
        font-size: 13px;
        color: #606266;
      }

      .success-icon {
        font-size: 16px;
      }
    }
  }

  // 完成阶段样式
  .completion-stats {
    margin-bottom: 24px;

    .stat-item {
      text-align: center;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;

      .stat-number {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 4px;

        &.success {
          color: #67c23a;
        }
      }

      .stat-label {
        font-size: 12px;
        color: #909399;
      }
    }
  }

  .file-details {
    h4 {
      margin: 0 0 16px 0;
      font-size: 16px;
      color: #303133;
    }

    .category-group {
      margin-bottom: 16px;
      border: 1px solid #e4e7ed;
      border-radius: 6px;
      overflow: hidden;

      .category-header {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 16px;
        background: #f5f7fa;
        border-bottom: 1px solid #e4e7ed;

        .category-count {
          font-size: 12px;
          color: #909399;
        }
      }

      .category-files {
        padding: 8px 16px;

        .file-detail-item {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 8px 0;
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .file-name {
            flex: 1;
            font-size: 13px;
            color: #303133;
          }

          .file-size {
            font-size: 12px;
            color: #909399;
            min-width: 60px;
            text-align: right;
          }
        }
      }
    }
  }

  .local-path {
    margin-top: 20px;
    padding: 12px;
    background: #f0f9ff;
    border-radius: 6px;
    border-left: 4px solid #409eff;

    p {
      margin: 0;
      font-size: 13px;
      color: #303133;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
