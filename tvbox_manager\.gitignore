# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Flask
instance/
.webassets-cache

# SQLite
*.db
*.sqlite3
*.sqlite

# Virtual Environment
venv/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs
logs/
*.log

# Environment variables
.env
.env.*

# OS
.DS_Store
Thumbs.db

# Uploads
uploads/

# Coverage
.coverage
htmlcov/ 