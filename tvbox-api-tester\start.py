#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
TVBox API Web 测试工具 - 快速启动脚本
一键启动测试服务器并打开浏览器
"""

import os
import sys
import time
import subprocess
from pathlib import Path

def check_dependencies():
    """检查依赖"""
    try:
        import requests
        return True
    except ImportError:
        return False

def install_dependencies():
    """安装依赖"""
    print("📦 安装依赖包...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError:
        print("❌ 依赖安装失败")
        return False

def main():
    """主函数"""
    print("🚀 TVBox API Web 测试工具 - 快速启动")
    print("="*50)
    
    # 切换到脚本目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # 检查依赖
    if not check_dependencies():
        print("⚠️  缺少依赖包，正在安装...")
        if not install_dependencies():
            print("❌ 无法安装依赖，请手动运行: pip install -r requirements.txt")
            input("按回车键退出...")
            return
    
    # 启动服务器
    print("🌐 启动测试服务器...")
    try:
        from server import TVBoxTestServerManager
        
        server_manager = TVBoxTestServerManager()
        
        if server_manager.start_server():
            print("✅ 服务器启动成功")
            
            # 等待一秒后打开浏览器
            time.sleep(1)
            server_manager.open_browser()
            
            print("\n" + "="*50)
            print("🎉 TVBox API 测试工具已启动")
            print(f"📍 访问地址: http://localhost:{server_manager.port}")
            print("💡 使用说明:")
            print("   1. 在浏览器中配置后端API地址")
            print("   2. 输入用户邮箱和密码")
            print("   3. 开始手动或自动测试API")
            print("\n按 Ctrl+C 停止服务器")
            print("="*50)
            
            try:
                # 保持服务器运行
                while server_manager.running:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n🛑 正在停止服务器...")
                server_manager.stop_server()
                print("👋 再见！")
        else:
            print("❌ 服务器启动失败")
            input("按回车键退出...")
            
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        input("按回车键退出...")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
