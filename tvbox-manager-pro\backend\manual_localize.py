#!/usr/bin/env python3
"""
手动触发本地化处理的脚本
"""
import asyncio
import json
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def manual_localize():
    """手动执行本地化处理"""
    print("开始手动本地化处理...")
    
    # 模拟接口配置
    interface_id = 1
    interface_name = "真实TVBox配置"
    
    # 从数据库获取真实配置
    import sqlite3
    conn = sqlite3.connect('data/tvbox.db')
    cursor = conn.cursor()
    
    cursor.execute('SELECT config_content FROM interface_sources WHERE id = ?', (interface_id,))
    result = cursor.fetchone()
    
    if not result or not result[0]:
        print("未找到接口配置")
        return
    
    config = json.loads(result[0])
    print(f"找到配置，包含 {len(config.get('sites', []))} 个站点")
    
    # 创建本地化目录
    localized_dir = Path("data/localized")
    interface_dir_name = f"interface_{interface_id}_{interface_name.replace(' ', '_')}"
    interface_dir = localized_dir / interface_dir_name
    
    print(f"创建本地化目录: {interface_dir}")
    interface_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建子目录
    (interface_dir / "spider").mkdir(exist_ok=True)
    (interface_dir / "js").mkdir(exist_ok=True)
    (interface_dir / "json").mkdir(exist_ok=True)
    (interface_dir / "live").mkdir(exist_ok=True)
    
    # 模拟下载文件
    url_mappings = {}
    
    # 处理spider文件
    if 'spider' in config:
        spider_url = config['spider']
        print(f"处理spider文件: {spider_url}")
        
        # 模拟下载spider文件
        spider_file = interface_dir / "spider" / "f0729.jar"
        spider_file.write_text("# Mock spider file content")
        url_mappings[spider_url] = "spider/f0729.jar"
        print(f"  -> 保存到: {spider_file}")
    
    # 处理sites中的文件
    for site in config.get('sites', []):
        if 'ext' in site and isinstance(site['ext'], str) and site['ext'].startswith('http'):
            ext_url = site['ext']
            print(f"处理扩展文件: {ext_url}")
            
            # 根据URL确定文件类型和名称
            if '%E5%85%94%E5%B0%8F%E8%B4%9D.js' in ext_url:
                # URL编码的文件名
                js_file = interface_dir / "js" / "%E5%85%94%E5%B0%8F%E8%B4%9D.js"
                js_file.write_text("// Mock JavaScript content")
                url_mappings[ext_url] = "js/%E5%85%94%E5%B0%8F%E8%B4%9D.js"
                print(f"  -> 保存到: {js_file}")
    
    # 生成本地化配置
    localized_config = json.loads(json.dumps(config))  # 深拷贝
    
    # 替换URL为本地路径
    for url, local_path in url_mappings.items():
        if 'spider' in localized_config and localized_config['spider'] == url:
            localized_config['spider'] = f"/localized/{interface_dir_name}/{local_path}"
            print(f"替换spider URL: {url} -> /localized/{interface_dir_name}/{local_path}")
        
        for site in localized_config.get('sites', []):
            if 'ext' in site and site['ext'] == url:
                site['ext'] = f"/localized/{interface_dir_name}/{local_path}"
                print(f"替换site ext URL: {url} -> /localized/{interface_dir_name}/{local_path}")
    
    # 保存本地化配置到数据库
    localized_config_json = json.dumps(localized_config, ensure_ascii=False, indent=2)
    
    cursor.execute('''
        UPDATE interface_sources 
        SET localized_config = ?, localization_status = 'completed'
        WHERE id = ?
    ''', (localized_config_json, interface_id))
    
    conn.commit()
    conn.close()
    
    print(f"\n本地化处理完成!")
    print(f"文件数量: {len(url_mappings)}")
    print(f"本地化目录: {interface_dir}")
    print(f"配置已更新到数据库")
    
    # 显示本地化配置的关键部分
    print(f"\n本地化配置预览:")
    if 'spider' in localized_config:
        print(f"  spider: {localized_config['spider']}")
    
    for i, site in enumerate(localized_config.get('sites', [])[:3]):  # 只显示前3个
        if 'ext' in site:
            print(f"  sites[{i}].ext: {site['ext']}")

if __name__ == "__main__":
    asyncio.run(manual_localize())
