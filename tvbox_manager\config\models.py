#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
TVBox Manager 数据模型定义
"""

from flask_sqlalchemy import SQLAlchemy
from flask_security import UserMixin, RoleMixin
from datetime import datetime
import json

db = SQLAlchemy()

# 用户角色关联表
roles_users = db.Table('roles_users',
    db.Column('user_id', db.Integer(), db.Foreign<PERSON>ey('users.id')),
    db.<PERSON>um<PERSON>('role_id', db.Integer(), db.<PERSON><PERSON>('roles.id'))
)

class Role(db.Model, RoleMixin):
    """用户角色模型"""
    __tablename__ = 'roles'
    
    id = db.Column(db.Integer(), primary_key=True)
    name = db.Column(db.String(80), unique=True)
    description = db.Column(db.String(255))
    
    def __str__(self):
        return self.name

class User(db.Model, UserMixin):
    """用户模型"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(255), unique=True)
    username = db.Column(db.String(255), unique=True)
    password = db.Column(db.String(255))
    active = db.Column(db.Boolean())
    fs_uniquifier = db.Column(db.String(255), unique=True)
    confirmed_at = db.Column(db.DateTime())
    last_login_at = db.Column(db.DateTime())
    current_login_at = db.Column(db.DateTime())
    last_login_ip = db.Column(db.String(100))
    current_login_ip = db.Column(db.String(100))
    login_count = db.Column(db.Integer)
    
    # 关联角色，多对多关系
    roles = db.relationship('Role', secondary=roles_users,
                            backref=db.backref('users', lazy='dynamic'))
    
    def __str__(self):
        return self.username or self.email

class InterfaceSource(db.Model):
    """TVBox接口源模型"""
    __tablename__ = 'interface_sources'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)  # 接口名称
    url = db.Column(db.Text, nullable=False)  # 接口URL
    description = db.Column(db.Text)  # 接口描述
    type = db.Column(db.String(20), default='vod')  # 接口类型：vod, live, all
    status = db.Column(db.Boolean, default=True)  # 接口状态
    decrypt_method = db.Column(db.String(50))  # 解密方法
    update_frequency = db.Column(db.Integer, default=24)  # 更新频率(小时)
    last_update = db.Column(db.DateTime)  # 最后更新时间
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    # 关联配置，一对多关系
    configs = db.relationship('TVBoxConfig', back_populates='source')
    
    def __str__(self):
        return self.name

class TVBoxConfig(db.Model):
    """TVBox配置模型"""
    __tablename__ = 'tvbox_configs'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)  # 配置名称
    content = db.Column(db.Text)  # 配置内容(JSON)
    spider = db.Column(db.String(255))  # 爬虫规则
    wallpaper = db.Column(db.String(255))  # 壁纸
    logo = db.Column(db.String(255))  # Logo
    sites_count = db.Column(db.Integer, default=0)  # 站点数量
    parses_count = db.Column(db.Integer, default=0)  # 解析器数量
    lives_count = db.Column(db.Integer, default=0)  # 直播源数量
    is_active = db.Column(db.Boolean, default=True)  # 是否激活
    is_default = db.Column(db.Boolean, default=False)  # 是否默认配置
    source_id = db.Column(db.Integer, db.ForeignKey('interface_sources.id'))  # 关联接口源
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    # 关联接口源，多对一关系
    source = db.relationship('InterfaceSource', back_populates='configs')
    
    def __str__(self):
        return self.name
    
    def update_stats(self):
        """更新配置统计信息"""
        if not self.content:
            return
        
        try:
            data = json.loads(self.content)
            
            # 统计站点数
            sites = data.get('sites', data.get('video', {}).get('sites', []))
            self.sites_count = len(sites) if isinstance(sites, list) else 0
            
            # 统计解析器数
            parses = data.get('parses', [])
            self.parses_count = len(parses) if isinstance(parses, list) else 0
            
            # 统计直播源数
            lives_count = 0
            lives = data.get('lives', [])
            if isinstance(lives, list):
                for live in lives:
                    if isinstance(live, dict) and 'channels' in live:
                        lives_count += len(live.get('channels', []))
            self.lives_count = lives_count
            
            # 更新其他信息
            self.spider = data.get('spider', '')
            self.wallpaper = data.get('wallpaper', '')
            self.logo = data.get('logo', '')
            
        except Exception as e:
            print(f"更新配置统计信息失败: {e}")

class SiteInfo(db.Model):
    """站点信息模型"""
    __tablename__ = 'site_infos'
    
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), nullable=False)  # 站点key
    name = db.Column(db.String(100), nullable=False)  # 站点名称
    api = db.Column(db.Text, nullable=False)  # 站点API
    type = db.Column(db.Integer)  # 站点类型
    searchable = db.Column(db.Integer, default=1)  # 是否可搜索
    quickSearch = db.Column(db.Integer, default=1)  # 是否可快速搜索
    filterable = db.Column(db.Integer, default=1)  # 是否可筛选
    jar = db.Column(db.String(255))  # JAR包
    config_id = db.Column(db.Integer, db.ForeignKey('tvbox_configs.id'))  # 关联配置
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    def __str__(self):
        return self.name

class LiveGroup(db.Model):
    """直播分组模型"""
    __tablename__ = 'live_groups'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)  # 分组名称
    config_id = db.Column(db.Integer, db.ForeignKey('tvbox_configs.id'))  # 关联配置
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    # 关联频道，一对多关系
    channels = db.relationship('LiveChannel', back_populates='group')
    
    def __str__(self):
        return self.name

class LiveChannel(db.Model):
    """直播频道模型"""
    __tablename__ = 'live_channels'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)  # 频道名称
    urls = db.Column(db.Text, nullable=False)  # 频道URL，多个用逗号分隔
    group_id = db.Column(db.Integer, db.ForeignKey('live_groups.id'))  # 关联分组
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    # 关联分组，多对一关系
    group = db.relationship('LiveGroup', back_populates='channels')
    
    def __str__(self):
        return self.name

class ParseInfo(db.Model):
    """解析器信息模型"""
    __tablename__ = 'parse_infos'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)  # 解析器名称
    url = db.Column(db.Text, nullable=False)  # 解析器URL
    type = db.Column(db.Integer, default=0)  # 解析器类型
    ext = db.Column(db.Text)  # 扩展参数
    config_id = db.Column(db.Integer, db.ForeignKey('tvbox_configs.id'))  # 关联配置
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    def __str__(self):
        return self.name

class SystemSetting(db.Model):
    """系统设置模型"""
    __tablename__ = 'system_settings'
    
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False)  # 设置键
    value = db.Column(db.Text)  # 设置值
    description = db.Column(db.String(255))  # 设置描述
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    def __str__(self):
        return self.key 