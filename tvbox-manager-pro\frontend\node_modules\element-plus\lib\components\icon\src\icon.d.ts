import type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue';
import type Icon from './icon.vue';
export declare const iconProps: {
    readonly size: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => string | number) | (() => string | number) | ((new (...args: any[]) => string | number) | (() => string | number))[], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly color: {
        readonly type: import("vue").PropType<string>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
};
export type IconProps = ExtractPropTypes<typeof iconProps>;
export type IconPropsPublic = __ExtractPublicPropTypes<typeof iconProps>;
export type IconInstance = InstanceType<typeof Icon> & unknown;
