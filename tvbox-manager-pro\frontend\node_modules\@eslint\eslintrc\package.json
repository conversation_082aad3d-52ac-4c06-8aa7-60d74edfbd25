{"name": "@eslint/eslintrc", "version": "2.1.4", "description": "The legacy ESLintRC config file format for ESLint", "type": "module", "main": "./dist/eslintrc.cjs", "exports": {".": {"import": "./lib/index.js", "require": "./dist/eslintrc.cjs"}, "./package.json": "./package.json", "./universal": {"import": "./lib/index-universal.js", "require": "./dist/eslintrc-universal.cjs"}}, "files": ["lib", "conf", "LICENSE", "dist", "universal.js"], "publishConfig": {"access": "public"}, "scripts": {"build": "rollup -c", "lint": "eslint . --report-unused-disable-directives", "lint:fix": "npm run lint -- --fix", "prepare": "npm run build", "release:generate:latest": "eslint-generate-release", "release:generate:alpha": "eslint-generate-prerelease alpha", "release:generate:beta": "eslint-generate-prerelease beta", "release:generate:rc": "eslint-generate-prerelease rc", "release:publish": "eslint-publish-release", "test": "mocha -R progress -c 'tests/lib/*.cjs' && c8 mocha -R progress -c 'tests/lib/**/*.js'"}, "repository": "eslint/eslintrc", "funding": "https://opencollective.com/eslint", "keywords": ["ESLint", "ESLintRC", "Configuration"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/eslint/eslintrc/issues"}, "homepage": "https://github.com/eslint/eslintrc#readme", "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "eslint": "^7.31.0", "eslint-config-eslint": "^7.0.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-plugin-node": "^11.1.0", "eslint-release": "^3.2.0", "fs-teardown": "^0.1.3", "mocha": "^9.0.3", "rollup": "^2.70.1", "shelljs": "^0.8.4", "sinon": "^11.1.2", "temp-dir": "^2.0.0"}, "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.6.0", "globals": "^13.19.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}