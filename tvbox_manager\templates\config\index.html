{% extends "layouts/admin.html" %}
{% set active_nav = 'config' %}

{% block title %}配置管理 - TVBox Manager{% endblock %}

{% block content %}
<!-- 页面标题 -->
<div class="page-header">
    <h1 class="page-title">配置管理</h1>
    <div class="page-actions">
        <a href="{{ url_for('config.add') }}" class="btn btn-primary">
            <i class="fas fa-plus btn-icon"></i> 添加配置
        </a>
    </div>
</div>

<!-- 配置列表卡片 -->
<div class="card">
    <div class="card-header">
        <h2 class="card-title">配置列表</h2>
    </div>
    
    {% if configs %}
    <div class="table-container">
        <table class="table">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>类型</th>
                    <th>URL</th>
                    <th>状态</th>
                    <th>更新时间</th>
                    <th width="120">操作</th>
                </tr>
            </thead>
            <tbody>
                {% for config in configs %}
                <tr>
                    <td class="font-semibold">{{ config.name }}</td>
                    <td>{{ config.type }}</td>
                    <td>
                        <div class="truncate" style="max-width: 200px;" title="{{ config.url }}">
                            {{ config.url }}
                        </div>
                    </td>
                    <td>
                        {% if config.status %}
                        <span class="status-badge active">
                            <i class="fas fa-check-circle status-icon"></i> 正常
                        </span>
                        {% else %}
                        <span class="status-badge inactive">
                            <i class="fas fa-times-circle status-icon"></i> 停用
                        </span>
                        {% endif %}
                    </td>
                    <td>
                        {% if config.last_update %}
                        {{ config.last_update.strftime('%Y-%m-%d %H:%M') }}
                        {% else %}
                        <span class="text-tertiary">从未更新</span>
                        {% endif %}
                    </td>
                    <td>
                        <div class="action-buttons">
                            <a href="{{ url_for('config.edit', id=config.id) }}" class="action-btn edit" title="编辑">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="{{ url_for('config.refresh', id=config.id) }}" class="action-btn refresh" title="刷新">
                                <i class="fas fa-sync-alt"></i>
                            </a>
                            <a href="{{ url_for('config.delete', id=config.id) }}" class="action-btn delete" title="删除" 
                               onclick="return confirm('确认删除此配置?')">
                                <i class="fas fa-trash-alt"></i>
                            </a>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div class="empty-state">
        <div class="empty-icon">
            <i class="fas fa-cog"></i>
        </div>
        <h3 class="empty-title">暂无配置数据</h3>
        <p class="empty-description">
            您还没有添加任何配置。配置用于管理TVBox的设置，添加后可自定义TVBox的功能和外观。
        </p>
        <a href="{{ url_for('config.add') }}" class="btn btn-primary">
            <i class="fas fa-plus btn-icon"></i> 添加第一个配置
        </a>
    </div>
    {% endif %}
</div>

<!-- API接口卡片 -->
<div class="card">
    <div class="card-header">
        <h2 class="card-title">API接口说明</h2>
    </div>
    <div class="card-body">
        <p>TVBox Manager提供了以下API接口，可供应用程序或设备直接调用：</p>
        
        <div style="margin-top: 16px; background-color: var(--bg-color); border-radius: 8px; padding: 16px;">
            <div style="margin-bottom: 12px;">
                <h4 style="margin-bottom: 8px; font-weight: 600;">获取默认配置:</h4>
                <code style="background-color: rgba(0,0,0,0.05); padding: 6px 8px; border-radius: 4px; display: block; word-break: break-all;">{{ request.host_url }}config/api/default</code>
            </div>
            
            <div>
                <h4 style="margin-bottom: 8px; font-weight: 600;">获取指定配置:</h4>
                <code style="background-color: rgba(0,0,0,0.05); padding: 6px 8px; border-radius: 4px; display: block; word-break: break-all;">{{ request.host_url }}config/api/get/{id}</code>
            </div>
        </div>
    </div>
</div>
{% endblock %} 
<div class="card">
    <div class="card-header">
        <h2 class="card-title">API接口说明</h2>
    </div>
    <div class="card-body">
        <p>TVBox Manager提供了以下API接口，可供应用程序或设备直接调用：</p>
        
        <div style="margin-top: 16px; background-color: var(--bg-color); border-radius: 8px; padding: 16px;">
            <div style="margin-bottom: 12px;">
                <h4 style="margin-bottom: 8px; font-weight: 600;">获取默认配置:</h4>
                <code style="background-color: rgba(0,0,0,0.05); padding: 6px 8px; border-radius: 4px; display: block; word-break: break-all;">{{ request.host_url }}config/api/default</code>
            </div>
            
            <div>
                <h4 style="margin-bottom: 8px; font-weight: 600;">获取指定配置:</h4>
                <code style="background-color: rgba(0,0,0,0.05); padding: 6px 8px; border-radius: 4px; display: block; word-break: break-all;">{{ request.host_url }}config/api/get/{id}</code>
            </div>
        </div>
    </div>
</div>
{% endblock %} 