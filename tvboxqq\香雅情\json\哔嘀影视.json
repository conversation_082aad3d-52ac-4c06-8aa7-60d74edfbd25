{
    "规则名": "哔嘀影视",
    "规则作者": "xlys.me",
    "请求头参数": "User-Agent$电脑#Referer$https://v.xlys.ltd.ua",
    "网页编码格式": "UTF-8",
    "图片是否需要代理": "0",
    "是否开启获取首页数据": "1",
    "首页推荐链接": "https://v.xlys.ltd.ua",
    "首页列表数组规则": "body",
    "首页片单列表数组规则": ".card-link",
    "首页片单是否Jsoup写法": "1",
    //首页片单标题
    "首页片单标题": "h3&&Text",
    //首页推荐片单链接
    "首页片单链接": "a&&href",
    //首页推荐片单图片，支持自定义图片链接
    "首页片单图片": "img&&data-src",
    //首页推荐片单副标题
    "首页片单副标题":".badge||.ribbon&&Text",
    //首页推荐片单链接补前缀  
    "首页片单链接加前缀": "https://v.xlys.ltd.ua",
    //首页推荐片单链接补后缀
    "首页片单链接加后缀": "",
    "分类起始页码": "1",
    "分类链接": "https://v.xlys.ltd.ua/s/{cateId}/{catePg}?type={class}&year={year}&order={by}",
    "分类名称": "不限&动作&爱情&喜剧&科幻&恐怖&战争&武侠&魔幻&剧情&动画&惊悚&3D&灾难&悬疑&警匪&文艺&青春&冒险&犯罪&纪录&古装&奇幻&国语&综艺&历史&运动&原创压制&美剧&韩剧&国产电视剧&日剧&英剧&德剧&俄剧&巴剧&加剧&西剧&意大利剧&泰剧&港台剧&法剧&澳剧",
    "分类名称替换词": "all&dongzuo&aiqing&xiju&kehuan&kongbu&zhanzheng&wuxia&mohuan&juqing&donghua&jingsong&3D&zainan&xuanyi&jingfei&wenyi&qingchun&maoxian&fanzui&jilu&guzhuang&qihuan&guoyu&zongyi&lishi&yundong&yuanchuang&meiju&hanju&guoju&riju&yingju&deju&eju&baju&jiaju&spanish&yidaliju&taiju&gangtaiju&faju&aoju",
    //"筛选数据": {},
    "筛选数据": "ext",
    //{cateId}
    "筛选子分类名称": "",
    "筛选子分类替换词": "",
    //{class}
    "筛选类型名称": "全部&电影&电视剧",
    "筛选类型替换词": "&0&1",
    //{area}
    "筛选地区名称": "",
    "筛选地区替换词": "*",
    //{year}
    "筛选年份名称": "2023&2022&2021&2020&2019&2018&2017&2016&2015&2014&2013&2012&2011&2010&2009&2008&2007&2006&2005&2004&2003&2002&2001",
    "筛选年份替换词": "*",
    //{lang}
    "筛选语言名称": "",
    "筛选语言替换词": "*",
    //{by}
    "筛选排序名称": "更新时间&豆瓣评分",
    "筛选排序替换词": "0&1",
    "分类截取模式": "1",
    "分类列表数组规则": ".row-cards&&.card",
    "分类片单是否Jsoup写法": "1",
    "分类片单标题": "h3&&Text",
    "分类片单链接": "a&&href",
    "分类片单图片": "img&&data-src||src",
    "分类片单副标题": ".badge||p&&Text",
    "分类片单链接加前缀": "https://v.xlys.ltd.ua",
    "分类片单链接加后缀": "",
    "搜索请求头参数": "User-Agent$手机#Referer$https://cn.bing.com/",
    "搜索链接": "https://cn.bing.com/search?q=site:v.xlys.ltd.ua/ {wd}&qs=ds&form=QBRE",
    "POST请求数据": "",
    "搜索截取模式": "1",
    "搜索列表数组规则": "ol#b_results&&li.b_algo",
    "搜索片单是否Jsoup写法": "1",
    "搜索片单图片": "",
    "搜索片单标题": "h2&&Text",
    "搜索片单链接": "a&&href",
    "搜索片单副标题": "",
    "搜索片单链接加前缀": "",
    "搜索片单链接加后缀": "",
    "链接是否直接播放": "0",
    "直接播放链接加前缀": "",
    "直接播放链接加后缀": "",
    "直接播放直链视频请求头": "",
    "详情是否Jsoup写法": "1",
    "类型详情": "",
    "年代详情": "",
    "地区详情": "div.mb-2&&p,5&&Text!制片国家/地区：",
    "演员详情": "div.mb-2&&p,3&&Text!主演：",
    "简介详情": "#synopsis&&Text!剧情简介",
    "线路列表数组规则": "body&&.card:has(#download-list)||.card:has(#torrent-list)||.card:has(#play-list)",
    "线路标题": "h3&&Text",
    "播放列表数组规则": "body&&#download-list||#torrent-list||#play-list",
    "选集列表数组规则": "a:not([href^=http])",
    "选集标题链接是否Jsoup写法": "1",
    "选集标题": "a&&Text",
    "选集链接": "a&&href",
    "是否反转选集序列": "0",
    "选集链接加前缀": "https://v.xlys.ltd.ua",
    "选集链接加后缀": "",
    "分析MacPlayer": "0",
    "是否开启手动嗅探": "1",
    "手动嗅探视频链接关键词": ".mp4#.m3u8#.flv#video/tos#item/video#video_mp4",
    "手动嗅探视频链接过滤词": ".html#=http"
}