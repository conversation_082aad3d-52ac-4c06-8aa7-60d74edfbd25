@echo off
chcp 65001 >nul
title TVBox Manager Pro - 前端开发服务器

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    TVBox Manager Pro                         ║
echo ║                    前端开发服务器                            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请先安装 Node.js 16+
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo 📦 检查依赖...
if not exist "node_modules" (
    echo 📦 安装依赖...
    npm install
    
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

echo 🚀 启动开发服务器...
echo 📱 开发地址: http://localhost:5173
echo 🔧 API地址: http://localhost:8001
echo.
echo 按 Ctrl+C 可停止服务
echo.

npm run dev

echo.
echo 开发服务器已停止，按任意键退出...
pause >nul
