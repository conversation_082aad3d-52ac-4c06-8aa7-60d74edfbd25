{% extends "layouts/admin.html" %}
{% set active_nav = 'live' %}

{% block title %}直播管理 - TVBox Manager{% endblock %}

{% block content %}
<!-- 页面标题 -->
<div class="page-header">
    <h1 class="page-title">直播管理</h1>
    <div class="page-actions">
        <a href="{{ url_for('live.add') }}" class="btn btn-primary">
            <i class="fas fa-plus btn-icon"></i> 添加直播源
        </a>
    </div>
</div>

<!-- 直播源列表卡片 -->
<div class="card">
    <div class="card-header">
        <h2 class="card-title">直播源列表</h2>
    </div>
    
    {% if lives %}
    <div class="table-container">
        <table class="table">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>类型</th>
                    <th>URL</th>
                    <th>频道数</th>
                    <th>状态</th>
                    <th width="120">操作</th>
                </tr>
            </thead>
            <tbody>
                {% for live in lives %}
                <tr>
                    <td class="font-semibold">{{ live.name }}</td>
                    <td>{{ live.type }}</td>
                    <td>
                        <div class="truncate" style="max-width: 200px;" title="{{ live.url }}">
                            {{ live.url }}
                        </div>
                    </td>
                    <td>{{ live.channels_count or '未知' }}</td>
                    <td>
                        {% if live.status %}
                        <span class="status-badge active">
                            <i class="fas fa-check-circle status-icon"></i> 正常
                        </span>
                        {% else %}
                        <span class="status-badge inactive">
                            <i class="fas fa-times-circle status-icon"></i> 停用
                        </span>
                        {% endif %}
                    </td>
                    <td>
                        <div class="action-buttons">
                            <a href="{{ url_for('live.edit', id=live.id) }}" class="action-btn edit" title="编辑">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="{{ url_for('live.refresh', id=live.id) }}" class="action-btn refresh" title="刷新">
                                <i class="fas fa-sync-alt"></i>
                            </a>
                            <a href="{{ url_for('live.delete', id=live.id) }}" class="action-btn delete" title="删除" 
                               onclick="return confirm('确认删除此直播源?')">
                                <i class="fas fa-trash-alt"></i>
                            </a>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div class="empty-state">
        <div class="empty-icon">
            <i class="fas fa-tv"></i>
        </div>
        <h3 class="empty-title">暂无直播源数据</h3>
        <p class="empty-description">
            您还没有添加任何直播源。直播源用于提供电视直播功能，添加后可观看各种电视频道。
        </p>
        <a href="{{ url_for('live.add') }}" class="btn btn-primary">
            <i class="fas fa-plus btn-icon"></i> 添加第一个直播源
        </a>
    </div>
    {% endif %}
</div>

<!-- 直播源类型说明卡片 -->
<div class="card">
    <div class="card-header">
        <h2 class="card-title">直播源类型说明</h2>
    </div>
    <div class="card-body">
        <p>TVBox Manager支持多种直播源类型，常见类型包括：</p>
        
        <div style="margin-top: 16px; display: grid; grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); gap: 16px;">
            <div style="background-color: var(--bg-color); border-radius: 8px; padding: 16px;">
                <h4 style="margin-bottom: 8px; font-weight: 600; color: var(--primary-color);">
                    <i class="fas fa-satellite-dish" style="margin-right: 8px;"></i>M3U
                </h4>
                <p>最常见的直播源格式，包含频道列表和播放地址，可导入多个频道。</p>
            </div>
            
            <div style="background-color: var(--bg-color); border-radius: 8px; padding: 16px;">
                <h4 style="margin-bottom: 8px; font-weight: 600; color: var(--primary-color);">
                    <i class="fas fa-code" style="margin-right: 8px;"></i>JSON
                </h4>
                <p>以JSON格式存储的频道列表，支持分组和更多元数据。</p>
            </div>
            
            <div style="background-color: var(--bg-color); border-radius: 8px; padding: 16px;">
                <h4 style="margin-bottom: 8px; font-weight: 600; color: var(--primary-color);">
                    <i class="fas fa-file-alt" style="margin-right: 8px;"></i>TXT
                </h4>
                <p>简单的文本格式直播源，每行包含一个频道名称和播放地址。</p>
            </div>
        </div>
    </div>
</div>

<!-- 使用说明卡片 -->
<div class="card">
    <div class="card-header">
        <h2 class="card-title">直播功能使用说明</h2>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <div class="alert-icon">
                <i class="fas fa-info-circle"></i>
            </div>
            <div class="alert-content">
                <p>直播源添加后会自动解析频道列表，您可以在TVBox应用中直接使用。</p>
                <p style="margin-top: 8px;">要在TVBox中使用直播功能，请在设置中选择"默认直播源"或在配置文件中指定直播源。</p>
            </div>
        </div>
    </div>
</div>
{% endblock %} 
</div>

<!-- 直播源类型说明卡片 -->
<div class="card">
    <div class="card-header">
        <h2 class="card-title">直播源类型说明</h2>
    </div>
    <div class="card-body">
        <p>TVBox Manager支持多种直播源类型，常见类型包括：</p>
        
        <div style="margin-top: 16px; display: grid; grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); gap: 16px;">
            <div style="background-color: var(--bg-color); border-radius: 8px; padding: 16px;">
                <h4 style="margin-bottom: 8px; font-weight: 600; color: var(--primary-color);">
                    <i class="fas fa-satellite-dish" style="margin-right: 8px;"></i>M3U
                </h4>
                <p>最常见的直播源格式，包含频道列表和播放地址，可导入多个频道。</p>
            </div>
            
            <div style="background-color: var(--bg-color); border-radius: 8px; padding: 16px;">
                <h4 style="margin-bottom: 8px; font-weight: 600; color: var(--primary-color);">
                    <i class="fas fa-code" style="margin-right: 8px;"></i>JSON
                </h4>
                <p>以JSON格式存储的频道列表，支持分组和更多元数据。</p>
            </div>
            
            <div style="background-color: var(--bg-color); border-radius: 8px; padding: 16px;">
                <h4 style="margin-bottom: 8px; font-weight: 600; color: var(--primary-color);">
                    <i class="fas fa-file-alt" style="margin-right: 8px;"></i>TXT
                </h4>
                <p>简单的文本格式直播源，每行包含一个频道名称和播放地址。</p>
            </div>
        </div>
    </div>
</div>

<!-- 使用说明卡片 -->
<div class="card">
    <div class="card-header">
        <h2 class="card-title">直播功能使用说明</h2>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <div class="alert-icon">
                <i class="fas fa-info-circle"></i>
            </div>
            <div class="alert-content">
                <p>直播源添加后会自动解析频道列表，您可以在TVBox应用中直接使用。</p>
                <p style="margin-top: 8px;">要在TVBox中使用直播功能，请在设置中选择"默认直播源"或在配置文件中指定直播源。</p>
            </div>
        </div>
    </div>
</div>
{% endblock %} 