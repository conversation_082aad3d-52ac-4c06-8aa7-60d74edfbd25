@echo off
chcp 65001 >nul
title TVBox Manager Pro - 前端构建

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    TVBox Manager Pro                         ║
echo ║                      前端项目构建                            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请先安装 Node.js 16+
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo 📦 安装依赖...
npm install

if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

echo 🔨 开始构建生产版本...
npm run build

if %errorlevel% equ 0 (
    echo.
    echo ✅ 构建成功！
    echo 📁 构建文件位置: dist/
    echo 🌐 可以部署到任何静态文件服务器
    echo.
) else (
    echo ❌ 构建失败，请检查错误信息
)

pause
