var VueDemi=function(e,t,n){if(e.install)return e;if(!t)return console.error("[vue-demi] no Vue instance found, please be sure to import `vue` before `vue-demi`."),e;if("2.7."===t.version.slice(0,4)){for(var i in t)e[i]=t[i];e.isVue2=!0,e.isVue3=!1,e.install=function(){},e.Vue=t,e.Vue2=t,e.version=t.version,e.warn=t.util.warn,e.createApp=function(e,n){var i,r={},o={config:t.config,use:t.use.bind(t),mixin:t.mixin.bind(t),component:t.component.bind(t),provide:function(e,t){return r[e]=t,this},directive:function(e,n){return n?(t.directive(e,n),o):t.directive(e)},mount:function(o,s){return i||((i=new t(Object.assign({propsData:n},e,{provide:Object.assign(r,e.provide)}))).$mount(o,s),i)},unmount:function(){i&&(i.$destroy(),i=void 0)}};return o}}else if("2."===t.version.slice(0,2))if(n){for(var i in n)e[i]=n[i];e.isVue2=!0,e.isVue3=!1,e.install=function(){},e.Vue=t,e.Vue2=t,e.version=t.version}else console.error("[vue-demi] no VueCompositionAPI instance found, please be sure to import `@vue/composition-api` before `vue-demi`.");else if("3."===t.version.slice(0,2)){for(var i in t)e[i]=t[i];e.isVue2=!1,e.isVue3=!0,e.install=function(){},e.Vue=t,e.Vue2=void 0,e.version=t.version,e.set=function(e,t,n){return Array.isArray(e)?(e.length=Math.max(e.length,t),e.splice(t,1,n),n):(e[t]=n,n)},e.del=function(e,t){Array.isArray(e)?e.splice(t,1):delete e[t]}}else console.error("[vue-demi] Vue version "+t.version+" is unsupported.");return e}(this.VueDemi=this.VueDemi||(void 0!==VueDemi?VueDemi:{}),this.Vue||("undefined"!=typeof Vue?Vue:void 0),this.VueCompositionAPI||("undefined"!=typeof VueCompositionAPI?VueCompositionAPI:void 0));!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("echarts"),require("vue-demi"),require("echarts/core")):"function"==typeof define&&define.amd?define(["echarts","vue-demi","echarts/core"],t):(e="undefined"!=typeof globalThis?globalThis:e||self).VueECharts=t(e.echarts,e.VueDemi,e.echarts)}(this,(function(e,t,n){"use strict";var i=function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},i.apply(this,arguments)};"function"==typeof SuppressedError&&SuppressedError;var r=["getWidth","getHeight","getDom","getOption","resize","dispatchAction","convertToPixel","convertFromPixel","containPixel","getDataURL","getConnectedDataURL","appendData","clear","isDisposed","dispose"];function o(e){return t=Object.create(null),r.forEach((function(n){t[n]=function(t){return function(){for(var n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];if(!e.value)throw new Error("ECharts is not initialized yet.");return e.value[t].apply(e.value,n)}}(n)})),t;var t}var s=null;var a=null;function u(e,t){void 0===t&&(t={});var n=document.createElement(e);return Object.keys(t).forEach((function(e){n[e]=t[e]})),n}function c(e,t,n){return(window.getComputedStyle(e,n||null)||{display:"none"})[t]}function _(e){if(!document.documentElement.contains(e))return{detached:!0,rendered:!1};for(var t=e;t!==document;){if("none"===c(t,"display"))return{detached:!1,rendered:!1};t=t.parentNode}return{detached:!1,rendered:!0}}var l='.resize-triggers{visibility:hidden;opacity:0;pointer-events:none}.resize-contract-trigger,.resize-contract-trigger:before,.resize-expand-trigger,.resize-triggers{content:"";position:absolute;top:0;left:0;height:100%;width:100%;overflow:hidden}.resize-contract-trigger,.resize-expand-trigger{background:#eee;overflow:auto}.resize-contract-trigger:before{width:200%;height:200%}',d=0,f=null;function v(e,t){if(e.__resize_mutation_handler__||(e.__resize_mutation_handler__=h.bind(e)),!e.__resize_listeners__)if(e.__resize_listeners__=[],window.ResizeObserver){var n=e.offsetWidth,i=e.offsetHeight,r=new ResizeObserver((function(){(e.__resize_observer_triggered__||(e.__resize_observer_triggered__=!0,e.offsetWidth!==n||e.offsetHeight!==i))&&g(e)})),o=_(e),s=o.detached,a=o.rendered;e.__resize_observer_triggered__=!1===s&&!1===a,e.__resize_observer__=r,r.observe(e)}else if(e.attachEvent&&e.addEventListener)e.__resize_legacy_resize_handler__=function(){g(e)},e.attachEvent("onresize",e.__resize_legacy_resize_handler__),document.addEventListener("DOMSubtreeModified",e.__resize_mutation_handler__);else if(d||(f=function(e){var t=document.createElement("style");return t.styleSheet?t.styleSheet.cssText=e:t.appendChild(document.createTextNode(e)),(document.querySelector("head")||document.body).appendChild(t),t}(l)),function(e){var t=c(e,"position");t&&"static"!==t||(e.style.position="relative");e.__resize_old_position__=t,e.__resize_last__={};var n=u("div",{className:"resize-triggers"}),i=u("div",{className:"resize-expand-trigger"}),r=u("div"),o=u("div",{className:"resize-contract-trigger"});i.appendChild(r),n.appendChild(i),n.appendChild(o),e.appendChild(n),e.__resize_triggers__={triggers:n,expand:i,expandChild:r,contract:o},m(e),e.addEventListener("scroll",p,!0),e.__resize_last__={width:e.offsetWidth,height:e.offsetHeight}}(e),e.__resize_rendered__=_(e).rendered,window.MutationObserver){var v=new MutationObserver(e.__resize_mutation_handler__);v.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0}),e.__resize_mutation_observer__=v}e.__resize_listeners__.push(t),d++}function h(){var e=_(this),t=e.rendered,n=e.detached;t!==this.__resize_rendered__&&(!n&&this.__resize_triggers__&&(m(this),this.addEventListener("scroll",p,!0)),this.__resize_rendered__=t,g(this))}function p(){var e,t,n=this;m(this),this.__resize_raf__&&(e=this.__resize_raf__,a||(a=(window.cancelAnimationFrame||window.webkitCancelAnimationFrame||window.mozCancelAnimationFrame||function(e){clearTimeout(e)}).bind(window)),a(e)),this.__resize_raf__=(t=function(){var e,t,i,r,o,s,a=(t=(e=n).__resize_last__,i=t.width,r=t.height,o=e.offsetWidth,s=e.offsetHeight,o!==i||s!==r?{width:o,height:s}:null);a&&(n.__resize_last__=a,g(n))},s||(s=(window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){return setTimeout(e,16)}).bind(window)),s(t))}function g(e){e&&e.__resize_listeners__&&e.__resize_listeners__.forEach((function(t){t.call(e,e)}))}function m(e){var t=e.__resize_triggers__,n=t.expand,i=t.expandChild,r=t.contract,o=r.scrollWidth,s=r.scrollHeight,a=n.offsetWidth,u=n.offsetHeight,c=n.scrollWidth,_=n.scrollHeight;r.scrollLeft=o,r.scrollTop=s,i.style.width=a+1+"px",i.style.height=u+1+"px",n.scrollLeft=c,n.scrollTop=_}function z(e,i,r){var o=null;t.watch([r,e,i],(function(e,t,i){var r=e[0],s=e[1],a=e[2];if(r&&s&&a){var u=!0===a?{}:a,c=u.throttle,_=void 0===c?100:c,l=u.onResize,h=function(){s.resize(),null==l||l()};o=_?n.throttle(h,_):h,v(r,o)}i((function(){r&&o&&function(e,t){var n=e.__resize_listeners__;if(n){if(t&&n.splice(n.indexOf(t),1),!n.length||!t){if(e.detachEvent&&e.removeEventListener)return e.detachEvent("onresize",e.__resize_legacy_resize_handler__),void document.removeEventListener("DOMSubtreeModified",e.__resize_mutation_handler__);e.__resize_observer__?(e.__resize_observer__.unobserve(e),e.__resize_observer__.disconnect(),e.__resize_observer__=null):(e.__resize_mutation_observer__&&(e.__resize_mutation_observer__.disconnect(),e.__resize_mutation_observer__=null),e.removeEventListener("scroll",p),e.removeChild(e.__resize_triggers__.triggers),e.__resize_triggers__=null),e.__resize_listeners__=null}! --d&&f&&f.parentNode.removeChild(f)}}(r,o)}))}))}var b={autoresize:[Boolean,Object]},w=/^on[^a-z]/,O=function(e){return w.test(e)};function y(e,n){var i=t.isRef(e)?t.unref(e):e;return i&&"object"==typeof i&&"value"in i?i.value||n:i||n}var E="ecLoadingOptions";var V={loading:Boolean,loadingOptions:Object},x="x-vue-echarts";t.Vue2&&t.Vue2.config.ignoredElements.push(x);var A="ecTheme",C="ecInitOptions",j="ecUpdateOptions",L=/(^&?~?!?)native:/,D=t.defineComponent({name:"echarts",props:i(i({option:Object,theme:{type:[Object,String]},initOptions:Object,updateOptions:Object,group:String,manualUpdate:Boolean},b),V),emits:{},inheritAttrs:!1,setup:function(e,r){var s=r.attrs,a=t.shallowRef(),u=t.shallowRef(),c=t.shallowRef(),_=t.shallowRef(),l=t.inject(A,null),d=t.inject(C,null),f=t.inject(j,null),v=t.toRefs(e),h=v.autoresize,p=v.manualUpdate,g=v.loading,m=v.loadingOptions,b=t.computed((function(){return _.value||e.option||null})),w=t.computed((function(){return e.theme||y(l,{})})),V=t.computed((function(){return e.initOptions||y(d,{})})),x=t.computed((function(){return e.updateOptions||y(f,{})})),D=t.computed((function(){return function(e){var t={};for(var n in e)O(n)||(t[n]=e[n]);return t}(s)})),T={},R=t.getCurrentInstance().proxy.$listeners,S={};function I(i){if(u.value){var r=c.value=n.init(u.value,w.value,V.value);e.group&&(r.group=e.group),Object.keys(S).forEach((function(e){var t=S[e];if(t){var n=e.toLowerCase();"~"===n.charAt(0)&&(n=n.substring(1),t.__once__=!0);var i=r;if(0===n.indexOf("zr:")&&(i=r.getZr(),n=n.substring(3)),t.__once__){delete t.__once__;var o=t;t=function(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];o.apply(void 0,e),i.off(n,t)}}i.on(n,t)}})),h.value?t.nextTick((function(){r&&!r.isDisposed()&&r.resize(),o()})):o()}function o(){var e=i||b.value;e&&r.setOption(e,x.value)}}function P(){c.value&&(c.value.dispose(),c.value=void 0)}R?Object.keys(R).forEach((function(e){L.test(e)?T[e.replace(L,"$1")]=R[e]:S[e]=R[e]})):Object.keys(s).filter((function(e){return O(e)})).forEach((function(e){var t=e.charAt(2).toLowerCase()+e.slice(3);if(0!==t.indexOf("native:"))"Once"===t.substring(t.length-4)&&(t="~".concat(t.substring(0,t.length-4))),S[t]=s[e];else{var n="on".concat(t.charAt(7).toUpperCase()).concat(t.slice(8));T[n]=s[e]}}));var N=null;t.watch(p,(function(n){"function"==typeof N&&(N(),N=null),n||(N=t.watch((function(){return e.option}),(function(e,t){e&&(c.value?c.value.setOption(e,i({notMerge:e!==t},x.value)):I())}),{deep:!0}))}),{immediate:!0}),t.watch([w,V],(function(){P(),I()}),{deep:!0}),t.watchEffect((function(){e.group&&c.value&&(c.value.group=e.group)}));var M=o(c);return function(e,n,r){var o=t.inject(E,{}),s=t.computed((function(){return i(i({},y(o,{})),null==r?void 0:r.value)}));t.watchEffect((function(){var t=e.value;t&&(n.value?t.showLoading(s.value):t.hideLoading())}))}(c,g,m),z(c,h,u),t.onMounted((function(){I()})),t.onBeforeUnmount((function(){P()})),i({chart:c,root:a,inner:u,setOption:function(t,n){e.manualUpdate&&(_.value=t),c.value?c.value.setOption(t,n||{}):I(t)},nonEventAttrs:D,nativeListeners:T},M)},render:function(){var e=t.Vue2?{attrs:this.nonEventAttrs,on:this.nativeListeners}:i(i({},this.nonEventAttrs),this.nativeListeners);return e.ref="root",e.class=e.class?["echarts"].concat(e.class):"echarts",t.h(x,e,[t.h("div",{ref:"inner",class:"vue-echarts-inner"})])}}),T=Object.freeze({__proto__:null,default:D,LOADING_OPTIONS_KEY:E,THEME_KEY:A,INIT_OPTIONS_KEY:C,UPDATE_OPTIONS_KEY:j});return i(i({},D),T)}));
//# sourceMappingURL=index.umd.min.js.map
