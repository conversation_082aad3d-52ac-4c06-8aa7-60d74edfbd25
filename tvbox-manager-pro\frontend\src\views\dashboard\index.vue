<template>
  <div class="dashboard">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <el-card class="welcome-card">
        <div class="welcome-content">
          <div class="welcome-text">
            <h2>欢迎回来，{{ userStore.userName }}！</h2>
            <p>今天是 {{ currentDate }}，祝您工作愉快！</p>
          </div>
          <div class="welcome-avatar">
            <el-avatar :size="80" :src="userStore.userAvatar" :icon="UserFilled" />
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon primary">
                <el-icon size="24"><Link /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ stats.total_interfaces }}</div>
                <div class="stats-label">接口总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon success">
                <el-icon size="24"><Document /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ stats.active_interfaces }}</div>
                <div class="stats-label">活跃接口</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon warning">
                <el-icon size="24"><Star /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ stats.total_subscriptions }}</div>
                <div class="stats-label">订阅总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon danger">
                <el-icon size="24"><User /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ stats.total_users }}</div>
                <div class="stats-label">用户总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>接口解析统计</span>
                <el-button type="text" @click="refreshChart">刷新</el-button>
              </div>
            </template>
            <div class="chart-container">
              <v-chart
                class="chart"
                :option="parseStatsOption"
                :loading="chartLoading"
                autoresize
              />
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>系统活跃度</span>
                <el-button type="text" @click="refreshChart">刷新</el-button>
              </div>
            </template>
            <div class="chart-container">
              <v-chart
                class="chart"
                :option="activityOption"
                :loading="chartLoading"
                autoresize
              />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 快速操作和最近活动 -->
    <div class="bottom-section">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <el-card class="quick-actions-card">
            <template #header>
              <span>快速操作</span>
            </template>
            <div class="quick-actions">
              <el-button
                type="primary"
                icon="Plus"
                @click="$router.push('/interfaces/create')"
              >
                添加接口
              </el-button>
              <el-button
                type="success"
                icon="Refresh"
                @click="refreshAllInterfaces"
              >
                刷新所有接口
              </el-button>
              <el-button
                type="warning"
                icon="Download"
                @click="exportData"
              >
                导出数据
              </el-button>
              <el-button
                type="info"
                icon="Setting"
                @click="$router.push('/settings')"
              >
                系统设置
              </el-button>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <el-card class="recent-activity-card">
            <template #header>
              <span>最近活动</span>
            </template>
            <div class="activity-list">
              <div
                v-for="activity in recentActivities"
                :key="activity.id"
                class="activity-item"
              >
                <div class="activity-icon">
                  <el-icon :color="activity.color">
                    <component :is="activity.icon" />
                  </el-icon>
                </div>
                <div class="activity-content">
                  <div class="activity-title">{{ activity.title }}</div>
                  <div class="activity-time">{{ activity.time }}</div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { UserFilled, Link, Document, Star, User, Plus, Refresh, Download, Setting } from '@element-plus/icons-vue'
import { systemApi } from '@/api/system'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, BarChart, PieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import dayjs from 'dayjs'

// 注册ECharts组件
use([
  CanvasRenderer,
  LineChart,
  BarChart,
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

const userStore = useUserStore()

// 响应式数据
const chartLoading = ref(false)

const stats = reactive({
  total_users: 0,
  active_users: 0,
  total_interfaces: 0,
  active_interfaces: 0,
  total_subscriptions: 0,
  active_subscriptions: 0,
  today_logins: 0,
  today_operations: 0
})

const recentActivities = ref([])

// 计算属性
const currentDate = computed(() => {
  return dayjs().format('YYYY年MM月DD日 dddd')
})

const parseStatsOption = computed(() => ({
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['成功', '失败']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '成功',
      type: 'line',
      data: [0, 0, 0, 0, 0, 0, 0],
      smooth: true,
      itemStyle: {
        color: '#67C23A'
      }
    },
    {
      name: '失败',
      type: 'line',
      data: [0, 0, 0, 0, 0, 0, 0],
      smooth: true,
      itemStyle: {
        color: '#F56C6C'
      }
    }
  ]
}))

const activityOption = computed(() => ({
  tooltip: {
    trigger: 'item'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '活跃度',
      type: 'pie',
      radius: '50%',
      data: [
        { value: 0, name: '接口解析' },
        { value: 0, name: '配置更新' },
        { value: 0, name: '用户访问' },
        { value: 0, name: '数据导出' },
        { value: 0, name: '系统设置' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
}))

// 方法
const loadStats = async () => {
  try {
    const response = await systemApi.getSystemStats()
    const data = response.data

    // 更新统计数据
    Object.assign(stats, data)
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  }
}

const refreshChart = () => {
  chartLoading.value = true
  setTimeout(() => {
    chartLoading.value = false
  }, 1000)
}

const refreshAllInterfaces = () => {
  ElMessage.success('开始刷新所有接口')
}

const exportData = () => {
  ElMessage.info('数据导出功能开发中')
}

// 生命周期
onMounted(() => {
  loadStats()
})
</script>

<style lang="scss" scoped>
.dashboard {
  .welcome-section {
    margin-bottom: 20px;
    
    .welcome-card {
      .welcome-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .welcome-text {
          h2 {
            margin: 0 0 8px 0;
            color: var(--el-text-color-primary);
          }
          
          p {
            margin: 0;
            color: var(--el-text-color-regular);
          }
        }
      }
    }
  }
  
  .stats-section {
    margin-bottom: 20px;
    
    .stats-card {
      .stats-content {
        display: flex;
        align-items: center;
        
        .stats-icon {
          width: 60px;
          height: 60px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          
          &.primary {
            background: var(--el-color-primary-light-9);
            color: var(--el-color-primary);
          }
          
          &.success {
            background: var(--el-color-success-light-9);
            color: var(--el-color-success);
          }
          
          &.warning {
            background: var(--el-color-warning-light-9);
            color: var(--el-color-warning);
          }
          
          &.danger {
            background: var(--el-color-danger-light-9);
            color: var(--el-color-danger);
          }
        }
        
        .stats-info {
          .stats-number {
            font-size: 28px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            line-height: 1;
          }
          
          .stats-label {
            font-size: 14px;
            color: var(--el-text-color-regular);
            margin-top: 4px;
          }
        }
      }
    }
  }
  
  .charts-section {
    margin-bottom: 20px;
    
    .chart-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .chart-container {
        height: 300px;
        
        .chart {
          height: 100%;
          width: 100%;
        }
      }
    }
  }
  
  .bottom-section {
    .quick-actions-card {
      .quick-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
      }
    }
    
    .recent-activity-card {
      .activity-list {
        .activity-item {
          display: flex;
          align-items: center;
          padding: 12px 0;
          border-bottom: 1px solid var(--el-border-color-lighter);
          
          &:last-child {
            border-bottom: none;
          }
          
          .activity-icon {
            margin-right: 12px;
          }
          
          .activity-content {
            flex: 1;
            
            .activity-title {
              font-size: 14px;
              color: var(--el-text-color-primary);
              margin-bottom: 4px;
            }
            
            .activity-time {
              font-size: 12px;
              color: var(--el-text-color-regular);
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard {
    .welcome-section .welcome-card .welcome-content {
      flex-direction: column;
      text-align: center;
      gap: 16px;
    }
    
    .bottom-section .quick-actions-card .quick-actions {
      flex-direction: column;
      
      .el-button {
        width: 100%;
      }
    }
  }
}
</style>
