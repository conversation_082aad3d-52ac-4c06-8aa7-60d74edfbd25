{
    "规则名": "播视童趣",
    "规则作者": "香雅情",
    "请求头参数": "电脑",
    "网页编码格式": "UTF-8",
    "图片是否需要代理": "0",
    "是否开启获取首页数据": "1",
    "首页推荐链接": "https://www.boosj.com/baby/",
    "首页列表数组规则": "body&&.pub-hotplay-main||.pub-child-dance||.pub-toy-play||.pub-describe-story||.pub-child-song||.pub-child-eduction",
    "首页片单列表数组规则": "a:has(img)",
    "首页片单是否Jsoup写法": "1",
    "首页片单标题": "img&&alt",
    "首页片单链接": "a&&href",
    "首页片单图片": "img&&src",
    "首页片单副标题": "",
    "首页片单链接加前缀": "",
    "首页片单链接加后缀": "",
    "分类起始页码": "1",
    "分类链接": "https://www.boosj.com/search_res_3362__{cateId}_{catePg}_.html",
    "分类名称": "全部&辅食&动画&儿童舞蹈&少儿英语&儿童歌曲&才艺&播视自制&故事&亲子教育&美术&其他&儿童游戏&识物&绘本&古诗&科普&儿童玩具&播视童趣儿童玩具",
    "分类名称替换词": "&28&582&3364&3366&3367&3622&3782&3822&3842&4402&4583&4762&4842&4843&4844&4845&5102&5142",
    "筛选数据": {},
    //"筛选数据": "ext",
    //{cateId}
    "筛选子分类名称": "",
    "筛选子分类替换词": "",
    //{class}
    "筛选类型名称": "",
    "筛选类型替换词": "*",
    //{area}
    "筛选地区名称": "",
    "筛选地区替换词": "*",
    //{year}
    "筛选年份名称": "",
    "筛选年份替换词": "*",
    //{lang}
    "筛选语言名称": "",
    "筛选语言替换词": "*",
    //{by}
    "筛选排序名称": "",
    "筛选排序替换词": "",
    "分类截取模式": "1",
    "分类列表数组规则": "body&&.bj-col4:has(img)",
    "分类片单是否Jsoup写法": "1",
    "分类片单标题": "a&&title",
    "分类片单链接": "a&&href",
    "分类片单图片": "img&&data-original",
    "分类片单副标题": "",
    "分类片单链接加前缀": "",
    "分类片单链接加后缀": "",
    "搜索请求头参数": "User-Agent$手机",
    "搜索链接": "{wd}",
    "POST请求数据": "",
    "搜索截取模式": "1",
    "搜索列表数组规则": "",
    "搜索片单是否Jsoup写法": "1",
    "搜索片单图片": "",
    "搜索片单标题": "",
    "搜索片单链接": "",
    "搜索片单副标题": "",
    "搜索片单链接加前缀": "",
    "搜索片单链接加后缀": "",
    "链接是否直接播放": "1",
    "直接播放链接加前缀": "",
    "直接播放链接加后缀": "",
    "直接播放直链视频请求头": "",
    "是否开启手动嗅探": "0",
    "手动嗅探视频链接关键词": ".mp4#.m3u8#.flv#video/tos",
    "手动嗅探视频链接过滤词": ".html#=http"
}