#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
日志配置模块
"""

import logging
import logging.handlers
import os
import sys
from typing import Optional

from app.core.config import settings

def setup_logging(
    level: Optional[str] = None,
    log_file: Optional[str] = None,
    log_format: Optional[str] = None
) -> logging.Logger:
    """
    设置日志配置
    
    Args:
        level: 日志级别
        log_file: 日志文件路径
        log_format: 日志格式
        
    Returns:
        Logger: 配置好的日志记录器
    """
    # 设置日志级别
    if level is None:
        level = settings.LOG_LEVEL
    
    log_level = getattr(logging, level.upper(), logging.INFO)
    
    # 设置日志格式
    if log_format is None:
        log_format = (
            "%(asctime)s [%(levelname)s] %(name)s:%(lineno)d - %(message)s"
        )
    
    # 创建根日志记录器
    logger = logging.getLogger()
    logger.setLevel(log_level)
    
    # 清除现有处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 创建格式化器
    formatter = logging.Formatter(log_format)
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file or settings.LOG_FILE:
        file_path = log_file or settings.LOG_FILE
        
        # 确保日志目录存在
        log_dir = os.path.dirname(file_path)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
        
        # 使用轮转文件处理器
        file_handler = logging.handlers.TimedRotatingFileHandler(
            filename=file_path,
            when='midnight',
            interval=1,
            backupCount=30,
            encoding='utf-8'
        )
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    # 设置第三方库日志级别
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    
    return logger

class LoggerMixin:
    """日志混入类"""
    
    @property
    def logger(self) -> logging.Logger:
        """获取日志记录器"""
        return logging.getLogger(self.__class__.__name__)

def get_logger(name: str) -> logging.Logger:
    """获取指定名称的日志记录器"""
    return logging.getLogger(name)
