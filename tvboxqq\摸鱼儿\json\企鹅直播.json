{
    "author": "率性而活",
    "ua":"Mozilla/5.0 (Linux; Android 8.1.0; OPPO R11t Build/OPM1.171019.011; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/76.0.3809.89 Mobile Safari/537.36 T7/11.19 SP-engine/2.15.0 baiduboxapp/********** (Baidu; P1 8.1.0)",
//************* m.live.qq.com

    //"dcVipFlag": "true",
    //"dcPlayUrl": "true",
    "homeUrl": "https://m.live.qq.com/directory/game/Basketball",
    "cateManual": {
        "篮球": "@directory/game/Basketball",
        "足球":"@directory/game/Football",
        "搏击":"@directory/game/Fight",
        "网球排球":"@directory/game/Tennis",
        "英文原声":"@directory/game/English",
        "台球":"@directory/game/Billiards",
        "棒球/橄榄球/冰球":"@directory/game/MLB",
        "NBA":"@directory/game/NBA",
        "CBA":"@directory/game/CBA",
        "颜值":"@directory/game/YZ",
        "棋牌/游戏":"@directory/game/Game"
   
    },
    "homeVodNode": "//a[contains(@href,'/10')]","homeVodName": "/div/following-sibling::p[1]/text()","homeVodId": "/@href","homeVodIdR": "/(\\S+)","homeVodImg": "//div[contains(@style,'http')]/@style","homeVodImgR": "(http.*?jpg)","homeVodMark": "",
    
      "cateUrl2": "https://m.live.qq.com/{cateId}",
  
  "cateVodNode": "//a[contains(@href,'/10')]",
  
  "cateVodName": "/div/following-sibling::p[1]/text()",
  
  "cateVodId": "/@href",
  
  "cateVodIdR": "/(\\S+)",
  
  "cateVodImg": "//div[contains(@style,'http')]/@style",
  
  "cateVodImgR": "(http.*?jpg)",
  
  "cateVodMark": "",
    

    //
    "dtUrl": "https://m.live.qq.com/{vid}",
    "dtNode": "//body",
    "dtName": "//p[contains(@class,'p-title')]/text()",
    "dtNameR": "(“.*?”)",
    "dtImg": "//div[contains(@class,'share-bar')]/@data-pic",
    "dtImgR": "",
    "dtCate": "",
    "dtCateR": "",
    "dtYear": "",
    "dtYearR": "",
    "dtArea": "",
    "dtAreaR": "",
    "dtDirector": "",
    "dtDirectorR": "",
    "dtActor": "",
    "dtActorR": "",
    "dtDesc": "",
    "dtDescR": "",
    "dtFromNode": "//p[contains(@class,'p-title')]",
    "dtFromName": "/text()",
    "dtFromNameR": "(\\企鹅体育)",
    "dtUrlNode": "//div[contains(@class,'wenzi')]",
    "dtUrlSubNode": "/a",
    "dtUrlId": "/text()",
    "dtUrlIdR": "m.live.qq.com/(\\S+)",
    "dtUrlName": "/text()",
    "dtUrlNameR": "(\\d+)",
    "playUrl": "https://m.live.qq.com/{playUrl}",
    "playUa":"{\"User-Agent\":\"okhttp/3.12.11\"}"
}