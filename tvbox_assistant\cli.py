#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
TVBox助手命令行接口
提供命令行工具进行配置解析、解密和处理
"""

import os
import sys
import asyncio
import argparse
import logging
import json
from .utils import TVBoxParser, TVBoxDecryptor, ResourceDownloader, ConfigProcessor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
    ]
)

logger = logging.getLogger('tvbox_assistant')

class TVBoxAssistant:
    """TVBox助手主类"""
    
    def __init__(self, config_path=None, output_dir="./downloads"):
        """
        初始化TVBox助手
        
        Args:
            config_path: 配置文件路径
            output_dir: 输出目录
        """
        self.config_path = config_path
        self.output_dir = output_dir
        self.parser = TVBoxParser()
        self.decryptor = TVBoxDecryptor()
        self.downloader = ResourceDownloader(output_dir)
        self.processor = ConfigProcessor()
    
    async def process(self):
        """
        处理配置文件
        
        Returns:
            dict: 处理结果
        """
        try:
            logger.info(f"开始处理配置文件: {self.config_path}")
            
            # 解析配置文件
            parse_result = self.parser.parse_config(self.config_path)
            config = parse_result['config']
            resources = parse_result['resources']
            
            # 分析加密内容
            encrypted_count = sum(1 for info in resources.values() if info.get('encrypted', False))
            logger.info(f"发现 {len(resources)} 个远程资源，其中 {encrypted_count} 个可能需要解密")
            
            # 下载资源
            download_map = await self.downloader.download_resources(resources)
            
            # 更新配置文件中的URL
            updated_config = self.processor.update_config(config, resources, download_map)
            
            # 保存更新后的配置文件
            output_config_path = os.path.join(self.output_dir, 'config_local.json')
            self.processor.save_config(updated_config, output_config_path)
            
            # 提取配置信息
            config_info = self.processor.extract_config_info(updated_config)
            
            logger.info(f"处理完成! 更新后的配置文件已保存到: {output_config_path}")
            
            return {
                'original_config': config,
                'updated_config': updated_config,
                'resources': resources,
                'download_map': download_map,
                'config_info': config_info
            }
        except Exception as e:
            logger.error(f"处理配置文件失败: {str(e)}")
            raise e

async def analyze_url(url):
    """
    分析单个URL
    
    Args:
        url: URL字符串
        
    Returns:
        dict: 分析结果
    """
    decryptor = TVBoxDecryptor()
    return decryptor.analyze_url(url)

def main():
    """命令行入口函数"""
    parser = argparse.ArgumentParser(description='TVBox助手 - 解析、解密和管理TVBox配置文件')
    
    subparsers = parser.add_subparsers(dest='command', help='子命令')
    
    # 处理配置命令
    process_parser = subparsers.add_parser('process', help='处理配置文件')
    process_parser.add_argument('config_path', help='配置文件路径')
    process_parser.add_argument('-o', '--output-dir', default='./downloads', help='输出目录')
    process_parser.add_argument('-v', '--verbose', action='store_true', help='显示详细日志')
    
    # 解密URL命令
    decrypt_parser = subparsers.add_parser('decrypt', help='解密URL')
    decrypt_parser.add_argument('url', help='要解密的URL')
    decrypt_parser.add_argument('-t', '--type', choices=['api', 'ext', 'jar', 'url', 'json'], help='URL类型提示')
    
    # 解析配置命令
    parse_parser = subparsers.add_parser('parse', help='仅解析配置文件')
    parse_parser.add_argument('config_path', help='配置文件路径')
    parse_parser.add_argument('-o', '--output', help='输出结果文件路径')
    
    # 版本命令
    version_parser = subparsers.add_parser('version', help='显示版本信息')
    
    args = parser.parse_args()
    
    # 设置日志级别
    if hasattr(args, 'verbose') and args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 处理命令
    if args.command == 'process':
        try:
            assistant = TVBoxAssistant(args.config_path, args.output_dir)
            asyncio.run(assistant.process())
        except Exception as e:
            logger.error(f"处理失败: {str(e)}")
            sys.exit(1)
    
    elif args.command == 'decrypt':
        try:
            decryptor = TVBoxDecryptor()
            result, method = decryptor.decrypt(args.url, args.type)
            if result != args.url:
                logger.info(f"解密成功，使用方法: {method}")
                logger.info(f"原始URL: {args.url}")
                logger.info(f"解密结果: {result}")
            else:
                logger.info("未能解密URL或URL无需解密")
        except Exception as e:
            logger.error(f"解密失败: {str(e)}")
            sys.exit(1)
    
    elif args.command == 'parse':
        try:
            parser = TVBoxParser()
            parse_result = parser.parse_config(args.config_path)
            config = parse_result['config']
            resources = parse_result['resources']
            
            # 打印资源数量
            logger.info(f"共解析出 {len(resources)} 个远程资源")
            
            # 分类统计
            resource_types = {}
            for url, info in resources.items():
                res_type = info['type']
                resource_types[res_type] = resource_types.get(res_type, 0) + 1
            
            for res_type, count in resource_types.items():
                logger.info(f"- {res_type}: {count} 个")
            
            # 如果指定了输出文件，保存解析结果
            if args.output:
                # 将资源信息转换为可序列化格式
                serializable_resources = {}
                for url, info in resources.items():
                    serializable_info = dict(info)
                    serializable_info['path'] = str(serializable_info['path'])
                    serializable_resources[url] = serializable_info
                
                with open(args.output, 'w', encoding='utf-8') as f:
                    json.dump({
                        'resources': serializable_resources,
                        'resource_types': resource_types
                    }, f, ensure_ascii=False, indent=2)
                
                logger.info(f"解析结果已保存到: {args.output}")
        except Exception as e:
            logger.error(f"解析失败: {str(e)}")
            sys.exit(1)
    
    elif args.command == 'version':
        from . import __version__
        print(f"TVBox助手 v{__version__}")
    
    else:
        parser.print_help()

if __name__ == "__main__":
    main() 