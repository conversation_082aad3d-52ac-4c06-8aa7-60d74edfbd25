//资源全部免费，仅供测试交流，请勿内置传播与转卖；关注微信公众号：奇奇乐分享，发送“本地包”获取教程与更多资源！

{
    "spider": "./spider.jar",
    "wallpaper": "http://bobohome.ignorelist.com:20247/bing",
    "logo": "http://127.0.0.1:9978/file/TVBox/logo.png",
    "sites": [
        {
            "key": "豆瓣",
            "name": "豆瓣｜首页",
            "type": 3,
            "api": "csp_Douban",
            "searchable": 0
        },
        {
            "key": "预告",
            "name": "新片｜预告",
            "type": 3,
            "api": "csp_YGP",
            "searchable": 0
        },
        {
            "key": "本地",
            "name": "本地｜视频",
            "type": 3,
            "api": "csp_LocalFile"
        },
        {
            "key": "配置中心",
            "name": "配置｜中心",
            "type": 3,
            "api": "csp_Config",
            "searchable": 0,
            "changeable": 0,
            "indexs": 0,
            "style": {
                "type": "rect",
                "ratio": 1.597
            }
        },
        {
            "key": "热播影视",
            "name": "热播｜APP",
            "type": 3,
            "api": "csp_AppRJ",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 0,
            "ext": {
                "url": "http://v.rbotv.cn"
            }
        },
        {
            "key": "天天影视",
            "name": "天天｜APP",
            "type": 3,
            "api": "csp_AppRJ",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 0,
            "ext": {
                "url": "http://tt.ysdqjs.cn"
            }
        },
        {
            "key": "浪酷影视",
            "name": "浪酷｜APP",
            "type": 3,
            "api": "csp_AppRJ",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 0,
            "ext": {
                "url": "http://v.lkuys.cn"
            }
        },
        {
            "key": "闪影",
            "name": "闪影｜APP",
            "type": 3,
            "api": "csp_AppYs",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "http://38.47.213.61:41271/mogai_api.php/v1.vod"
        },
        {
            "key": "行动",
            "name": "行动｜APP",
            "type": 3,
            "api": "csp_AppSy",
            "ext": {
                "url": "http://160.202.246.9:2356",
                "key1": "aassddwwxxllsx1x",
                "key2": "aassddwwxxllsx1x",
                "key3": "aassddwwxxllsx1x"
            }
        },
        {
            "key": "咖啡",
            "name": "咖啡｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet2",
            "ext": {
                "url": "",
                "site": "./txt/2.txt",
                "dataKey": "qwertyuiopqwertt",
                "dataIv": "qwertyuiopqwertt",
                "deviceId": "",
                "version": "109",
                "ua": "okhttp/3.10.0"
            }
        },
        {
            "key": "知了",
            "name": "知了｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "https://zzos.cc",
                "dataKey": "zzoccc2025555555",
                "dataIv": "zzoccc2025555555",
                "deviceId": "",
                "version": "119"
            }
        },
        {
            "key": "APP4K",
            "name": "蓝光｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "http://59.153.167.186",
                "dataKey": "SDSFET23215FDSF2",
                "dataIv": "SDSFET23215FDSF2",
                "deviceId": "",
                "version": "119"
            }
        },
        {
            "key": "麻花",
            "name": "麻花｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "http://124.223.27.166:130",
                "site": "",
                "dataKey": "q7gj4f9br3fls6nh",
                "dataIv": "q7gj4f9br3fls6nh",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "豆丁",
            "name": "豆丁｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "",
                "site": "./txt/xfdd.txt",
                "dataKey": "xasdasdqwertyuio",
                "dataIv": "xasdasdqwertyuio",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "灵虎",
            "name": "灵虎｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "",
                "site": "./txt/89.txt",
                "dataKey": "#getapp@TMD@2025",
                "dataIv": "#getapp@TMD@2025",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "橘猫",
            "name": "橘猫｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet2",
            "ext": {
                "url": "https://qjappcms.jm4k.top",
                "dataKey": "pBVmysmGX8TsgrQN",
                "dataIv": "pBVmysmGX8TsgrQN",
                "deviceId": "",
                "version": "120"
            }
        },
        {
            "key": "顾我",
            "name": "顾我｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet2",
            "ext": {
                "url": "http://121.62.21.14:520",
                "dataKey": "ca94b06ca3c7d80e",
                "dataIv": "ca94b06ca3c7d80e",
                "deviceId": "",
                "version": "120"
            }
        },
        {
            "key": "雄鹰",
            "name": "雄鹰｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet2",
            "ext": {
                "url": "",
                "site": "./txt/lanyingxmy.txt",
                "dataKey": "ca94b06ca359d80e",
                "dataIv": "ca94b06ca359d80e",
                "deviceId": "",
                "version": "120"
            }
        },
        {
            "key": "榴莲",
            "name": "榴莲｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet2",
            "ext": {
                "url": "https://qjappcms.ll4k.xyz",
                "dataKey": "1yGA85sJ5STtE7uj",
                "dataIv": "1yGA85sJ5STtE7uj",
                "deviceId": "",
                "version": "50000"
            }
        },
        {
            "key": "蓝鹰",
            "name": "蓝鹰｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet2",
            "ext": {
                "url": "http://172.247.192.138:18520",
                "dataKey": "SuNlEkOLAoWJj1Oe",
                "dataIv": "SuNlEkOLAoWJj1Oe",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "仓鼠",
            "name": "仓鼠｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet2",
            "ext": {
                "url": "https://qjappcms.cs4k.top",
                "dataKey": "Z98KXaLtO2wC1Pte",
                "dataIv": "Z98KXaLtO2wC1Pte",
                "deviceId": "",
                "version": "120"
            }
        },
        {
            "key": "鲸鱼",
            "name": "鲸鱼｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet2",
            "ext": {
                "url": "",
                "site": "./json/1.json",
                "dataKey": "AAdgrdghjfgswerA",
                "dataIv": "AAdgrdghjfgswerA",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "热剧",
            "name": "热剧｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet2",
            "ext": {
                "url": "",
                "site": "./txt/rebo.txt",
                "dataKey": "8191A7F47B37882F",
                "dataIv": "8191A7F47B37882F",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "码头",
            "name": "码头｜APP",
            "type": 3,
            "api": "csp_AppGet",
            "ext": {
                "url": "https://www.lgmt.cc",
                "dataKey": "asfjaskasgggassf",
                "dataIv": "asfjaskasgggassf",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "米诺",
            "name": "米诺｜APP",
            "type": 3,
            "api": "csp_AppGet",
            "ext": {
                "url": "http://www.milkidc.cn",
                "dataKey": "20c79c979da8db0f",
                "dataIv": "20c79c979da8db0f",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "云速",
            "name": "云速｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "",
                "site": "./json/1.json",
                "dataKey": "4d83b87c4c5ea111",
                "dataIv": "4d83b87c4c5ea111",
                "deviceId": "",
                "version": "105"
            }
        },
        {
            "key": "桃子",
            "name": "桃子｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "https://appcms.tzys.xyz",
                "dataKey": "KL6vlZkw6WL5x90U",
                "dataIv": "KL6vlZkw6WL5x90U",
                "deviceId": "4b4c36766c5a6b7736574c3578393055",
                "version": "119"
            }
        },
        {
            "key": "魔方",
            "name": "魔方｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "https://mfsp33.top",
                "dataKey": "1234567887654321",
                "dataIv": "1234567887654321",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "海豚",
            "name": "海豚｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "http://getapp.hiapp.com.cn",
                "dataKey": "2268331221bobobo",
                "dataIv": "2268331221bobobo",
                "deviceId": "",
                "version": "107"
            }
        },
        {
            "key": "溜溜",
            "name": "溜溜｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "https://appcms.ll4k.xyz",
                "dataKey": "NiDGaKiVnkO3QX1Q",
                "dataIv": "NiDGaKiVnkO3QX1Q",
                "deviceId": "2fbaf48ee97783260bc907e3ab0bd40c3",
                "version": "200"
            }
        },
        {
            "key": "瓜萌",
            "name": "瓜萌｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "https://www.guahd.com",
                "dataKey": "f2A7D4B9E8C16531",
                "dataIv": "f2A7D4B9E8C16531",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "晴天",
            "name": "晴天｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "https://qjappcms.sun4k.top",
                "dataKey": "sBxqXVF5pAHbGzrH",
                "dataIv": "sBxqXVF5pAHbGzrH",
                "deviceId": "",
                "version": "119"
            }
        },
        {
            "key": "再看",
            "name": "再看｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "http://149.88.64.161:8627",
                "dataKey": "123456789ABCDEFG",
                "dataIv": "123456789ABCDEFG",
                "deviceId": "2bb4c10f3e043307dbfc579bd0db23f4e",
                "version": "110"
            }
        },
        {
            "key": "橘子",
            "name": "橘子｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "https://api1.bffree.cn",
                "dataKey": "2015692015692015",
                "dataIv": "2015692015692015",
                "deviceId": "",
                "version": "300"
            }
        },
        {
            "key": "彼岸",
            "name": "彼岸｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "https://mfsp33.top",
                "dataKey": "1234567887654321",
                "dataIv": "1234567887654321",
                "deviceId": "298e5fe29c74b35aabb9836ee2f6f449f",
                "version": "315"
            }
        },
        {
            "key": "雨滴",
            "name": "雨滴｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "https://c8w2ov7u5wg2z1o8p21c.aliyuncs.click:27899",
                "dataKey": "k9o3p2c8b7m3z0o8",
                "dataIv": "k9o3p2c8b7m3z0o8",
                "deviceId": "",
                "version": "100"
            }
        },
        {
            "key": "萝卜",
            "name": "萝卜｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "https://apiapplbys.lbys.app:5678",
                "dataKey": "apiapplbyskey168",
                "dataIv": "apiapplbyskey168",
                "deviceId": "",
                "version": "107"
            }
        },
        {
            "key": "米兔",
            "name": "米兔｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "https://new.tkbot.fun",
                "dataKey": "d032c12876bc6848",
                "dataIv": "d032c12876bc6848",
                "deviceId": "",
                "version": "200"
            }
        },
        {
            "key": "小红",
            "name": "小红｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "https://www.xiaohys.com",
                "dataKey": "ENonBHeVBoYZhVUV",
                "dataIv": "ENonBHeVBoYZhVUV",
                "deviceId": "298e5fe29c74b35aabb9836ee2f6f449f",
                "version": "166"
            }
        },
        {
            "key": "在看",
            "name": "在看｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "http://149.88.64.161:9525",
                "dataKey": "123456789ABCDEFG",
                "dataIv": "123456789ABCDEFG",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "星河",
            "name": "星河｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "http://121.62.22.204:9876",
                "dataKey": "f5e2tx53ykp6s2c9",
                "dataIv": "f5e2tx53ykp6s2c9",
                "deviceId": "",
                "version": "361"
            }
        },
        {
            "key": "若惜",
            "name": "若惜｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "http://110.40.59.188:9527",
                "dataKey": "ebad3f1a58b13933",
                "dataIv": "ebad3f1a58b13933",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "外剧",
            "name": "外剧｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "https://guowaiju.com",
                "dataKey": "7xv16h7qgkrs9b1p",
                "dataIv": "7xv16h7qgkrs9b1p",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "诺映",
            "name": "诺映｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "https://www.noad.top",
                "dataKey": "708FA298F0855840",
                "dataIv": "708FA298F0855840",
                "deviceId": "2129ec9e6e5703cb0aeeddd79554e38f8",
                "version": "103"
            }
        },
        {
            "key": "樱桃",
            "name": "樱桃｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppMuou",
            "ext": {
                "url": "http://sp.dxgsp.cc",
                "dataKey": "25f9e794323b4538",
                "dataIv": "25f9e794323b4538",
                "jxurl": "https://ap.dxgsp.cc"
            }
        },
        {
            "key": "趣看",
            "name": "趣看｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppMuou",
            "ext": {
                "url": "https://xjuzi.top",
                "dataKey": "6a482a70b80eefc9",
                "dataIv": "c995826a3e86fedd",
                "jxurl": "https://www.ququkan.cc"
            }
        },
        {
            "key": "火猫",
            "name": "火猫｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppMuou",
            "ext": {
                "url": "http://154.12.22.168:14988",
                "dataKey": "531f6082a43ac5d7",
                "dataIv": "531f6082a43ac5d7",
                "jxurl": "http://app.789dd.cn"
            }
        },
        {
            "key": "哔哩视频",
            "name": "哔哩｜视频",
            "type": 3,
            "api": "csp_BiliYS",
            "searchable": 1,
            "quickSearch": 0,
            "filterable": 0,
            "changeable": 0,
            "style": {
                "type": "rect",
                "ratio": 1.433
            },
            "ext": {
                "json": "./json/BLSP.json",
                "cookie": "http://127.0.0.1:9978/file/TVBox/bili_cookie.txt"
            }
        },
        {
            "key": "腾讯视频",
            "name": "腾讯｜视频",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/TXSP.js"
        },
        {
            "key": "优酷视频",
            "name": "优酷｜视频",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/YKSP.js"
        },
        {
            "key": "芒果视频",
            "name": "芒果｜视频",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/MGSP.js"
        },
        {
            "key": "爱奇艺",
            "name": "爱奇艺｜视频",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/AQY.js"
        },
        {
            "key": "三六零",
            "name": "三六零｜视频",
            "type": 3,
            "api": "csp_SP360"
        },
        {
            "key": "小斑快映",
            "name": "小斑快映｜4K弹幕",
            "type": 3,
            "api": "csp_PanWebShare",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "changeable": 1,
            "ext": "./json/xbky.json"
        },
        {
            "key": "玩偶哥哥",
            "name": "玩偶｜4K弹幕",
            "type": 3,
            "api": "csp_PanWebShare",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/wogg.json"
        },
        {
            "key": "木偶",
            "name": "木偶｜4K弹幕",
            "type": 3,
            "api": "csp_PanWebShare",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/mogg.json"
        },
        {
            "key": "蜡笔",
            "name": "蜡笔｜4K弹幕",
            "type": 3,
            "api": "csp_PanWebShare",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "changeable": 1,
            "ext": "./json/lb.json"
        },
        {
            "key": "至臻",
            "name": "至臻｜4K弹幕",
            "type": 3,
            "api": "csp_PanWebShare",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "changeable": 1,
            "ext": "./json/zz.json"
        },
        {
            "key": "多多",
            "name": "多多｜4K弹幕",
            "type": 3,
            "api": "csp_PanWebShare",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "changeable": 1,
            "ext": "./json/yyds.json"
        },
        {
            "key": "欧哥",
            "name": "欧哥｜4K弹幕",
            "type": 3,
            "api": "csp_PanWebShare",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "changeable": 1,
            "ext": "./json/og.json"
        },
        {
            "key": "二小",
            "name": "二小｜4K弹幕",
            "type": 3,
            "api": "csp_PanWebShare",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "changeable": 1,
            "ext": "./json/ex.json"
        },
        {
            "key": "大玩",
            "name": "大玩｜4K弹幕",
            "type": 3,
            "api": "csp_PanWebShare",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "changeable": 1,
            "ext": "./json/dawo.json"
        },
        {
            "key": "虎斑",
            "name": "虎斑｜4K弹幕",
            "type": 3,
            "api": "csp_PanWebShare",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "changeable": 1,
            "ext": "./json/hb.json"
        },
        {
            "key": "闪电",
            "name": "闪电｜4K弹幕",
            "type": 3,
            "api": "csp_PanWebShare",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "changeable": 1,
            "ext": "./json/sd.json"
        },
        {
            "key": "奥秘",
            "name": "奥秘｜4K弹幕",
            "type": 3,
            "api": "csp_PanWebShare",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "changeable": 1,
            "ext": "./json/am.json"
        },
        {
            "key": "团长",
            "name": "团长｜4K弹幕",
            "type": 3,
            "api": "csp_PanWebTz",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "changeable": 1
        },
        {
            "key": "雷鲸",
            "name": "雷鲸｜4K弹幕",
            "type": 3,
            "api": "csp_PanWebShareCloudLJ",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "changeable": 1,
            "style": {
                "type": "list",
                "ratio": 1.433
            },
            "ext": "./json/lj.json"
        },
        {
            "key": "海绵",
            "name": "海绵｜4K弹幕",
            "type": 3,
            "api": "csp_PanWebShareCloudHM",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "changeable": 1,
            "style": {
                "type": "list",
                "ratio": 1.433
            },
            "ext": "./json/hm.json"
        },
        {
            "key": "夸父",
            "name": "夸父｜4K弹幕",
            "type": 3,
            "api": "csp_PanWebShareCloudKF",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "changeable": 1,
            "style": {
                "type": "list",
                "ratio": 1.433
            },
            "ext": "./json/kf.json"
        },
        {
            "key": "123",
            "name": "123｜4K弹幕",
            "type": 3,
            "api": "csp_PanWebShareCloud123",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "changeable": 1,
            "style": {
                "type": "list",
                "ratio": 1.433
            },
            "ext": "./json/123.json"
        },
        {
            "key": "趣盘",
            "name": "趣盘｜4K弹幕",
            "type": 3,
            "api": "csp_PanWebQu",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "changeable": 1,
            "style": {
                "type": "list",
                "ratio": 1.433
            },
            "ext": {
                "url": "https://www.qupanshe.com"
            }
        },
        {
            "key": "盘库",
            "name": "盘库｜4K弹幕",
            "type": 3,
            "api": "csp_PanWebKuBa",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "changeable": 1,
            "ext": {
                "url": "https://panku8.com,https://yipanso.com"
            }
        },
        {
            "key": "双星",
            "name": "双星｜4K弹幕",
            "type": 3,
            "api": "./api/cloud.min.js",
            "ext": "./js/SX.js",
            "style": {
                "type": "list"
            }
        },
        {
            "key": "懒盘",
            "name": "懒盘｜4K弹幕",
            "type": 3,
            "api": "./api/cloud.min.js",
            "ext": "./js/LP.js",
            "style": {
                "type": "list"
            }
        },
        {
            "key": "百酷",
            "name": "百酷｜4K弹幕",
            "type": 3,
            "api": "./api/cloud.min.js",
            "ext": "./js/BK.js",
            "style": {
                "type": "list"
            }
        },
        {
            "key": "校长",
            "name": "校长｜4K弹幕",
            "type": 3,
            "api": "./api/cloud.min.js",
            "ext": "./js/XZ.js",
            "style": {
                "type": "list"
            }
        },
        {
            "key": "天堂",
            "name": "天堂｜4K弹幕",
            "type": 3,
            "api": "./api/cloud.min.js",
            "ext": "./js/TT.js",
            "style": {
                "type": "list"
            }
        },
        {
            "key": "人人电影网",
            "name": "人人｜4K弹幕",
            "type": 3,
            "api": "./api/cloud.min.js",
            "ext": "./js/RRDYW.js",
            "style": {
                "type": "list"
            }
        },
        {
            "key": "爱搜",
            "name": "爱搜｜4K弹幕",
            "type": 3,
            "api": "./api/cloud.min.js",
            "ext": "./js/AS.js",
            "style": {
                "type": "list"
            }
        },
        {
            "key": "移动",
            "name": "移动｜4K",
            "type": 3,
            "api": "csp_YD",
            "searchable": 1,
            "quickSearch": 1,
            "style": {
                "type": "list"
            }
        },
        {
            "key": "糖果",
            "name": "糖果｜搜索",
            "type": 3,
            "api": "csp_TGSou",
            "searchable": 1,
            "changeable": 0
        },
        {
            "key": "米搜",
            "name": "米搜｜搜索",
            "type": 3,
            "api": "csp_MiSou"
        },
        {
            "key": "聚搜",
            "name": "聚搜｜搜索",
            "type": 3,
            "api": "csp_PanWebSearch",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "changeable": 1
        },
        {
            "key": "人人分享站",
            "name": "人人｜搜索",
            "type": 3,
            "api": "./api/cloud.min.js",
            "ext": "./js/RR.js",
            "style": {
                "type": "list"
            }
        },
        {
            "key": "全盘",
            "name": "全盘｜搜索",
            "type": 3,
            "api": "./api/cloud.min.js",
            "ext": "./js/QP.js",
            "style": {
                "type": "list"
            }
        },
        {
            "key": "夸克盘搜",
            "name": "盘搜｜搜索",
            "type": 3,
            "api": "./api/cloud.min.js",
            "ext": "./js/KKPS.js",
            "style": {
                "type": "list"
            }
        },
        {
            "key": "星芽短剧",
            "name": "星芽｜短剧",
            "type": 3,
            "api": "csp_AppXY",
            "searchable": 1,
            "quickSearch": 0,
            "filterable": 0
        },
        {
            "key": "河马短剧",
            "name": "河马｜短剧",
            "type": 3,
            "api": "./api/HMDJ.py",
            "searchable": 1,
            "changeable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "playerType": 2
        },
        {
            "key": "偷乐短剧",
            "name": "偷乐｜短剧",
            "type": 3,
            "api": "./api/TLDJ.py",
            "searchable": 1,
            "changeable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "playerType": 2
        },
        {
            "key": "短剧网",
            "name": "短剧网｜短剧",
            "type": 3,
            "api": "csp_XBPQ",
            "ext": "./json/DJWang.json"
        },
        {
            "key": "短剧屋",
            "name": "短剧屋｜短剧",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/DJWu.json"
        },
        {
            "key": "起点",
            "name": "起点｜影视",
            "type": 3,
            "api": "csp_AppYsV2",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "http://172.83.156.150:1010/api.php/app/"
        },
        {
            "key": "金牌影视",
            "name": "金牌｜影视",
            "type": 3,
            "api": "./api/JPYS.py",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "playerType": 2,
            "ext": {
                "site": "https://m.hkybqufgh.com,https://m.sizhengxt.com,https://m.9zhoukj.com,https://m.sizhengxt.com,https://m.jiabaide.cn"
            }
        },
        {
            "key": "猎手影视",
            "name": "猎手｜影视",
            "type": 3,
            "api": "./api/LSYS.py",
            "searchable": 1,
            "changeable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "playerType": 2
        },
        {
            "key": "厂长影视",
            "name": "厂长｜影视",
            "type": 3,
            "playerType": "2",
            "api": "csp_Czsapp",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "https://www.czzymovie.com/"
        },
        {
            "key": "绝对影视",
            "name": "绝对｜影视",
            "type": 3,
            "api": "csp_FourK",
            "ext": "https://www.4kvm.tv"
        },
        {
            "key": "云播影视",
            "name": "云播｜影视",
            "type": 3,
            "api": "csp_Tvyb"
        },
        {
            "key": "奇优影视",
            "name": "奇优｜影视",
            "type": 3,
            "api": "csp_Qiyou"
        },
        {
            "key": "苹果影视",
            "name": "苹果｜影视",
            "type": 3,
            "api": "csp_LiteApple",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "全网影视",
            "name": "全看｜影视",
            "type": 3,
            "api": "csp_Quanwk",
            "ext": "https://www.91qkw.com"
        },
        {
            "key": "饺子影视",
            "name": "饺子｜影视",
            "type": 3,
            "api": "csp_Jiaozi",
            "playerType": 2
        },
        {
            "key": "尘落影视",
            "name": "尘落｜影视",
            "type": 3,
            "api": "csp_Wetv",
            "searchable": 1,
            "quickSearch": 1,
            "ext": "https://v.wetv.wang"
        },
        {
            "key": "鸭梨影视",
            "name": "鸭梨｜影视",
            "type": 3,
            "api": "csp_KmeiJu"
        },
        {
            "key": "白白影视",
            "name": "白白｜影视",
            "type": 3,
            "api": "csp_SuBaiBai",
            "ext": "https://www.subaibai.com"
        },
        {
            "key": "低端影视",
            "name": "低端｜影视",
            "type": 3,
            "api": "csp_Ddys",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "来看影视",
            "name": "来看｜影视",
            "type": 3,
            "api": "csp_Lkdy",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "瓜子影视",
            "name": "瓜子｜影视",
            "type": 3,
            "api": "csp_Gz360",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "快看影视",
            "name": "快看｜影视",
            "type": 3,
            "api": "csp_Kuaikan",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "采集之王",
            "name": "采集｜合集",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/CJZW.js?type=url&params=./json/CJJT.json$1$1"
        },
        {
            "key": "爱看机器人",
            "name": "爱看｜影视",
            "type": 3,
            "api": "csp_Ikanbot",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "海纳影视",
            "name": "海纳｜影视",
            "type": 3,
            "api": "csp_XBPQ",
            "ext": "./json/HNYS.json"
        },
        {
            "key": "面包影视",
            "name": "面包｜影视",
            "type": 3,
            "api": "csp_XBPQ",
            "ext": "./json/MBYS.json"
        },
        {
            "key": "永乐影视",
            "name": "永乐｜影视",
            "type": 3,
            "api": "csp_XBPQ",
            "ext": "./json/YLYS.json"
        },
        {
            "key": "雪糕影视",
            "name": "雪糕｜影视",
            "type": 3,
            "api": "csp_XBPQ",
            "ext": "./json/XGYS.json"
        },
        {
            "key": "骚火影视",
            "name": "骚火｜影视",
            "type": 3,
            "api": "csp_XBPQ",
            "ext": "./json/SHYS.json"
        },
        {
            "key": "七点影视",
            "name": "七点｜影视",
            "type": 3,
            "api": "csp_XBPQ",
            "ext": "./json/QDYS.json"
        },
        {
            "key": "三九影视",
            "name": "三九｜影视",
            "type": 3,
            "api": "csp_XBPQ",
            "ext": "./json/SJYS.json"
        },
        {
            "key": "三四影视",
            "name": "三四｜影视",
            "type": 3,
            "api": "csp_XBPQ",
            "ext": "./json/SSYS.json"
        },
        {
            "key": "农民影视",
            "name": "农民｜影视",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/NMYS.json"
        },
        {
            "key": "盒子影视",
            "name": "盒子｜影视",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/HZYS.json"
        },
        {
            "key": "电影牛",
            "name": "电影牛｜影视",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/DYN.json"
        },
        {
            "key": "剧圈圈",
            "name": "剧圈圈｜影视",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/JQQ.js"
        },
        {
            "key": "1905",
            "name": "1905｜影视",
            "type": 3,
            "api": "csp_Web1905",
            "searchable": 1,
            "quickSearch": 0,
            "filterable": 0
        },
        {
            "key": "无上资源",
            "name": "无上｜采集",
            "type": 1,
            "api": "https://mfgc.iwsyy.xyz/api.php/provide/vod/",
            "searchable": 1,
            "changeable": 1,
            "categories": [
                "电影",
                "连续剧",
                "综艺",
                "动漫",
                "纪录片",
                "少儿",
                "短剧"
            ]
        },
        {
            "key": "哆啦新番社",
            "name": "哆啦｜新番社",
            "type": 3,
            "api": "csp_XBPQ",
            "style": {
                "type": "list"
            },
            "ext": "./json/DLXFS.json"
        },
        {
            "key": "56动漫",
            "name": "56｜动漫",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/56DM.js"
        },
        {
            "key": "NT动漫",
            "name": "NT｜动漫",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/NTDM.js"
        },
        {
            "key": "Anime1",
            "name": "Anime1｜动漫",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/Anime1.js"
        },
        {
            "key": "曼波动漫",
            "name": "曼波｜动漫",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "https://app.omofun1.top",
                "dataKey": "66dc309cbeeca454",
                "dataIv": "66dc309cbeeca454",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "稀饭动漫",
            "name": "稀饭｜动漫",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "",
                "site": "./txt/getapp.txt",
                "dataKey": "1yZ2Spn9krnzVKoC",
                "dataIv": "1yZ2Spn9krnzVKoC",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "咕咕动漫",
            "name": "咕咕｜动漫",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "https://www.gugu3.com",
                "dataKey": "nKfZ8KX6JTNWRzTD",
                "dataIv": "nKfZ8KX6JTNWRzTD",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "米饭动漫",
            "name": "米饭｜动漫",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "http://45.43.29.111:9527",
                "dataKey": "GETMIFUNGEIMIFUN",
                "dataIv": "GETMIFUNGEIMIFUN",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "次元动漫",
            "name": "次元｜动漫",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "https://jjjj.nilinili.org",
                "dataKey": "672263e98f232a05",
                "dataIv": "672263e98f232a05",
                "deviceId": "",
                "version": "170"
            }
        },
        {
            "key": "派对动漫",
            "name": "派对｜动漫",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "https://qjappcms.acg.party",
                "dataKey": "AmtMYFCJDPoTlK7z",
                "dataIv": "AmtMYFCJDPoTlK7z",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "丫丫",
            "name": "丫丫｜APP",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "http://tv.yy-fun.cc",
                "dataKey": "qkxnwkfjwpcnwycl",
                "dataIv": "qkxnwkfjwpcnwycl",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "黑猫动漫",
            "name": "黑猫｜动漫",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "https://dm.xxdm123.top:9991",
                "dataKey": "0fe3b5781782c621",
                "dataIv": "0fe3b5781782c621",
                "deviceId": "",
                "version": "203"
            }
        },
        {
            "key": "樱花动漫",
            "name": "樱花｜动漫",
            "type": 3,
            "api": "csp_XBPQ",
            "ext": "./json/YHDM.json"
        },
        {
            "key": "巴士动漫",
            "name": "巴士｜动漫",
            "type": 3,
            "api": "csp_XYQHiker",
            "ext": "./json/bsdm.json"
        },
        {
            "key": "好看动漫",
            "name": "好看｜动漫",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/HKDM.js"
        },
        {
            "key": "奇米动漫",
            "name": "奇米｜动漫",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/QMDM.js"
        },
        {
            "key": "花子动漫",
            "name": "花子｜动漫",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/HZDM.js"
        },
        {
            "key": "动画片",
            "name": "动画片｜动漫",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/DHPDQ.js"
        },
        {
            "key": "路漫漫",
            "name": "路漫漫｜动漫",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/LMM.js"
        },
        {
            "key": "动漫岛",
            "name": "动漫岛｜动漫",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/DMD.js"
        },
        {
            "key": "去看吧",
            "name": "去看吧｜动漫",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/QKB.js"
        },
        {
            "key": "爱弹幕",
            "name": "爱弹幕｜动漫",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/ADM.js"
        },
        {
            "key": "异世界",
            "name": "异世界｜动漫",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/YSJ.js"
        },
        {
            "key": "荐片",
            "name": "荐片｜磁力",
            "api": "csp_Jianpian",
            "type": 3,
            "playerType": 1
        },
        {
            "key": "修罗影视",
            "name": "修罗｜磁力",
            "type": 3,
            "api": "csp_XBPQ",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/XLYS.json"
        },
        {
            "key": "七味",
            "name": "七味｜磁力",
            "type": 3,
            "api": "csp_QnMp4",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "80S",
            "name": "80S｜磁力",
            "type": 3,
            "api": "csp_BLSGod",
            "playerType": 1,
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "New6v",
            "name": "New6V｜磁力",
            "type": 3,
            "api": "csp_New6v",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "https://www.xb6v.com"
        },
        {
            "key": "SeedHub",
            "name": "SeedHub｜磁力",
            "type": 3,
            "api": "csp_SeedHub",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "美剧迷",
            "name": "美剧迷｜磁力",
            "type": 3,
            "api": "csp_MeijuMi",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "迅雷吧",
            "name": "迅雷吧｜磁力",
            "type": 3,
            "api": "csp_Xunlei8",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "电影港",
            "name": "电影港｜磁力",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/DYG.json"
        },
        {
            "key": "狐狸君",
            "name": "狐狸君｜磁力",
            "type": 3,
            "api": "csp_XBPQ",
            "changeable": 1,
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/HLJ.json"
        },
        {
            "key": "BT天堂",
            "name": "BT天堂｜磁力",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/BTTT.json"
        },
        {
            "key": "Mp4电影",
            "name": "Mp4电影｜磁力",
            "type": 3,
            "api": "csp_Mp4Mov",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "美剧天堂",
            "name": "美剧天堂｜磁力",
            "type": 3,
            "api": "csp_MeijuTT",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "电影天堂",
            "name": "电影天堂｜磁力",
            "type": 3,
            "api": "csp_DyGod",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "阿里云盘",
            "name": "阿里｜云盘",
            "type": 3,
            "api": "csp_PanAli",
            "searchable": 0,
            "filterable": 0,
            "changeable": 0,
            "style": {
                "type": "list",
                "ratio": 1.433
            },
            "ext": "./json/aliShare.json"
        },
        {
            "key": "夸克云盘",
            "name": "夸克｜云盘",
            "type": 3,
            "api": "csp_PanQuark",
            "searchable": 0,
            "filterable": 0,
            "changeable": 0,
            "style": {
                "type": "list",
                "ratio": 1.433
            },
            "ext": "./json/quarkShare.json"
        },
        {
            "key": "UC",
            "name": "UC｜云盘",
            "type": 3,
            "api": "csp_PanUc",
            "searchable": 0,
            "filterable": 0,
            "changeable": 0,
            "style": {
                "type": "list",
                "ratio": 1.433
            },
            "ext": "./json/ucShare.json"
        },
        {
            "key": "百度云盘",
            "name": "百度｜云盘",
            "type": 3,
            "api": "csp_PanBaiDu",
            "searchable": 0,
            "filterable": 0,
            "changeable": 0,
            "style": {
                "type": "list",
                "ratio": 1.433
            }
        },
        {
            "key": "短剧合集",
            "name": "短剧｜合集",
            "type": 3,
            "api": "./api/cloud.min.js",
            "ext": "./js/DJHJ.js",
            "style": {
                "type": "list"
            }
        },
        {
            "key": "短剧大全",
            "name": "短剧｜大全",
            "type": 3,
            "api": "./api/cloud.min.js",
            "ext": "./js/DJDQ.js",
            "style": {
                "type": "list"
            }
        },
        {
            "key": "AList",
            "name": "AList｜合集",
            "type": 3,
            "api": "csp_Alist",
            "searchable": 1,
            "filterable": 1,
            "changeable": 0,
            "style": {
                "type": "list"
            },
            "ext": "./json/alist.json"
        },
        {
            "key": "网络直播",
            "name": "网络｜直播",
            "type": 3,
            "api": "./api/WLZB.py"
        },
        {
            "key": "88看球",
            "name": "88｜看球",
            "type": 3,
            "api": "csp_Kanqiu",
            "gridview": 3,
            "style": {
                "type": "list"
            }
        },
        {
            "key": "哔哩合集",
            "name": "哔哩｜合集",
            "type": 3,
            "api": "csp_Bili",
            "searchable": 1,
            "quickSearch": 0,
            "filterable": 0,
            "changeable": 0,
            "style": {
                "type": "rect",
                "ratio": 1.433
            },
            "ext": {
                "json": "./json/BLHJ.json",
                "cookie": "http://127.0.0.1:9978/file/TVBox/bili_cookie.txt"
            }
        },
        {
            "key": "哔哩哔哩戏曲",
            "name": "哔哩｜音乐",
            "type": 3,
            "api": "csp_Bili",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 0,
            "ext": {
                "cookie": "http://127.0.0.1:9978/file/TVBox/bili_cookie.txt",
                "json": "./json/BLYCH.json"
            },
            "style": {
                "type": "rect",
                "ratio": 1.597
            }
        },
        {
            "key": "哔哩哔哩听书",
            "name": "哔哩｜听书",
            "type": 3,
            "api": "csp_Bili",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 0,
            "ext": {
                "cookie": "http://127.0.0.1:9978/file/TVBox/bili_cookie.txt",
                "json": "./json/BLTS.json"
            },
            "style": {
                "type": "rect",
                "ratio": 1.597
            }
        },
        {
            "key": "哔哩哔哩相声",
            "name": "哔哩｜相声",
            "type": 3,
            "api": "csp_Bili",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 0,
            "ext": {
                "cookie": "http://127.0.0.1:9978/file/TVBox/bili_cookie.txt",
                "json": "./json/BLXS.json"
            },
            "style": {
                "type": "rect",
                "ratio": 1.597
            }
        },
        {
            "key": "哔哩哔哩小品",
            "name": "哔哩｜小品",
            "type": 3,
            "api": "csp_Bili",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 0,
            "ext": {
                "cookie": "http://127.0.0.1:9978/file/TVBox/bili_cookie.txt",
                "json": "./json/BLXP.json"
            },
            "style": {
                "type": "rect",
                "ratio": 1.597
            }
        },
        {
            "key": "哔哩哔哩戏曲",
            "name": "哔哩｜戏曲",
            "type": 3,
            "api": "csp_Bili",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 0,
            "ext": {
                "cookie": "http://127.0.0.1:9978/file/TVBox/bili_cookie.txt",
                "json": "./json/BLXQ.json"
            },
            "style": {
                "type": "rect",
                "ratio": 1.597
            }
        },
        {
            "key": "急救教学",
            "name": "急救｜教学",
            "type": 3,
            "api": "csp_FirstAid",
            "searchable": 0,
            "quickSearch": 0,
            "changeable": 0,
            "style": {
                "type": "rect",
                "ratio": 3.8
            },
            "gridview": "0-0-4.1"
        },
        {
            "key": "养生堂",
            "name": "养生｜知识",
            "type": 3,
            "api": "csp_XBPQ",
            "ext": "./json/YST.json",
            "style": {
                "type": "rect",
                "ratio": 1.597
            }
        },
        {
            "key": "push_agent",
            "name": "手机｜推送",
            "type": 3,
            "api": "csp_Push",
            "searchable": 0,
            "filterable": 0,
            "changeable": 0
        }
    ],
    "parses": [
        {
            "name": "无上",
            "type": "1",
            "url": "https://mfjx.iwsyy.xyz/?url=",
            "ext": {
                "flag": [
                    "qq",
                    "腾讯",
                    "qiyi",
                    "爱奇艺",
                    "奇艺",
                    "youku",
                    "优酷",
                    "sohu",
                    "搜狐",
                    "letv",
                    "乐视",
                    "mgtv",
                    "芒果",
                    "tnmb",
                    "seven",
                    "bilibili",
                    "1905"
                ],
                "header": {
                    "User-Agent": "okhttp/4.9.1"
                }
            }
        },
        {
            "name": "巧计",
            "type": 1,
            "url": "https://zy.qiaoji8.com/xiafan.php?url=",
            "ext": {
                "flag": [
                    "QD4K",
                    "iyf",
                    "duanju",
                    "gzcj",
                    "GTV",
                    "GZYS",
                    "weggz",
                    "Ace"
                ],
                "header": {
                    "User-Agent": "okhttp/4.9.1"
                }
            }
        },
        {
            "name": "咸鱼",
            "type": 0,
            "url": "https://jx.xymp4.cc/?url=",
            "ext": {
                "header": {
                    "user-agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 Edg/134.0.3124.68"
                }
            }
        },
        {
            "name": "虾米",
            "type": 0,
            "url": "https://jx.xmflv.com/?url=",
            "ext": {
                "header": {
                    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.57"
                }
            }
        },
        {
            "name": "淘片",
            "type": 0,
            "url": "https://jx.yparse.com/index.php?url=",
            "ext": {
                "header": {
                    "user-agent": "Mozilla/5.0 (Linux; Android 13; V2049A Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/********* Mobile Safari/537.36"
                }
            }
        },
        {
            "name": "冰豆",
            "type": 0,
            "url": "https://bd.jx.cn/?url=",
            "ext": {
                "header": {
                    "user-agent": "Mozilla/5.0 (Linux; Android 13; V2049A Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/********* Mobile Safari/537.36"
                }
            }
        },
        {
            "name": "七七",
            "type": 0,
            "url": "https://jx.77flv.cc/?url=",
            "ext": {
                "header": {
                    "user-agent": "Mozilla/5.0 (Linux; Android 13; V2049A Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/********* Mobile Safari/537.36"
                }
            }
        },
        {
            "name": "盘古",
            "type": 0,
            "url": "https://www.playm3u8.cn/jiexi.php?url=",
            "ext": {
                "header": {
                    "user-agent": "Mozilla/5.0 (Linux; Android 13; V2049A Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/********* Mobile Safari/537.36"
                }
            }
        },
        {
            "name": "夜幕",
            "type": 0,
            "url": "https://yemu.xyz/?url=",
            "ext": {
                "header": {
                    "user-agent": "Mozilla/5.0 (Linux; Android 13; V2049A Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/********* Mobile Safari/537.36"
                }
            }
        }
    ],
    "rules": [
        {
            "name": "农民",
            "hosts": [
                "toutiaovod.com"
            ],
            "regex": [
                "video/tos/cn"
            ]
        },
        {
            "name": "火山",
            "hosts": [
                "huoshan.com"
            ],
            "regex": [
                "item_id="
            ]
        },
        {
            "name": "抖音",
            "hosts": [
                "douyin.com"
            ],
            "regex": [
                "is_play_url="
            ]
        },
        {
            "name": "饭团点击",
            "hosts": [
                "dadagui",
                "freeok",
                "dadagui"
            ],
            "script": [
                "document.querySelector(\"#playleft iframe\").contentWindow.document.querySelector(\"#start\").click();"
            ]
        },
        {
            "name": "毛驴点击",
            "hosts": [
                "www.maolvys.com"
            ],
            "script": [
                "document.getElementsByClassName('swal-button swal-button--confirm')[0].click()"
            ]
        },
        {
            "name": "ofiii",
            "hosts": [
                "www.ofiii.com"
            ],
            "script": [
                "const play=document.getElementsByClassName('play_icon')[0],event=new MouseEvent('click',{bubbles:!0,cancelable:!0,view:window,screenX:100,screenY:100,clientX:50,clientY:50,button:0,shiftKey:!1,ctrlKey:!1,altKey:!1,metaKey:!1,modifierState:0});play.dispatchEvent(event);"
            ]
        }
    ],
    "doh": [
        {
            "name": "Google",
            "url": "https://dns.google/dns-query",
            "ips": [
                "*******",
                "*******"
            ]
        },
        {
            "name": "Cloudflare",
            "url": "https://cloudflare-dns.com/dns-query",
            "ips": [
                "*******",
                "*******",
                "2606:4700:4700::1111",
                "2606:4700:4700::1001"
            ]
        },
        {
            "name": "AdGuard",
            "url": "https://dns.adguard.com/dns-query",
            "ips": [
                "*************",
                "*************"
            ]
        },
        {
            "name": "DNSWatch",
            "url": "https://resolver2.dns.watch/dns-query",
            "ips": [
                "************",
                "************"
            ]
        },
        {
            "name": "Quad9",
            "url": "https://dns.quad9.net/dns-quer",
            "ips": [
                "*******",
                "***************"
            ]
        }
    ],
    "lives": [
        {
            "name": "AI直播",
            "type": 0,
            "url": "./lives/AI直播.txt",
            "epg": "http://cdn.1678520.xyz/epg/?ch={name}&date={date}"
        },
        {
            "name": "Guovin",
            "type": 0,
            "url": "./lives/Guovin.txt",
            "epg": "http://cdn.1678520.xyz/epg/?ch={name}&date={date}"
        },
        {
            "name": "电视Live",
            "type": 0,
            "url": "./lives/电视Live.txt"
        },
        {
            "name": "网络Live",
            "type": 0,
            "url": "./lives/网络Live.txt"
        },
        {
            "name": "范明明",
            "type": 0,
            "url": "./lives/范明明.txt"
        },
        {
            "name": "APTV",
            "type": 0,
            "url": "./lives/APTV.txt",
            "epg": "http://epg.51zmt.top:8000/api/diyp/?ch={name}&date={date}",
            "ua": "okhttp/3.15,AptvPlayer/1.4.0"
        }
    ],
    "hosts": [
        "cache.ott.*.itv.cmvideo.cn=base-v4-free-mghy.e.cdn.chinamobile.com",
        "cache.ott.ystenlive.itv.cmvideo.cn=base-v4-free-mghy.e.cdn.chinamobile.com",
        "cache.ott.bestlive.itv.cmvideo.cn=base-v4-free-mghy.e.cdn.chinamobile.com",
        "cache.ott.wasulive.itv.cmvideo.cn=base-v4-free-mghy.e.cdn.chinamobile.com",
        "cache.ott.fifalive.itv.cmvideo.cn=base-v4-free-mghy.e.cdn.chinamobile.com",
        "cache.ott.hnbblive.itv.cmvideo.cn=base-v4-free-mghy.e.cdn.chinamobile.com"
    ],
    "flags": [
        "youku",
        "优酷",
        "优 酷",
        "优酷视频",
        "qq",
        "腾讯",
        "腾 讯",
        "腾讯视频",
        "iqiyi",
        "qiyi",
        "奇艺",
        "爱奇艺",
        "爱 奇 艺",
        "m1905",
        "xigua",
        "letv",
        "leshi",
        "乐视",
        "乐 视",
        "sohu",
        "搜狐",
        "搜 狐",
        "搜狐视频",
        "tudou",
        "pptv",
        "mgtv",
        "芒果",
        "imgo",
        "芒果TV",
        "芒 果 T V",
        "bilibili",
        "哔 哩",
        "哔 哩 哔 哩"
    ],
    "ijk": [
        {
            "group": "软解码",
            "options": [
                {
                    "category": 4,
                    "name": "opensles",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "overlay-format",
                    "value": "842225234"
                },
                {
                    "category": 4,
                    "name": "framedrop",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "soundtouch",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "start-on-prepared",
                    "value": "1"
                },
                {
                    "category": 1,
                    "name": "http-detect-range-support",
                    "value": "0"
                },
                {
                    "category": 1,
                    "name": "fflags",
                    "value": "fastseek"
                },
                {
                    "category": 2,
                    "name": "skip_loop_filter",
                    "value": "48"
                },
                {
                    "category": 4,
                    "name": "reconnect",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "enable-accurate-seek",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "mediacodec",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "mediacodec-auto-rotate",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "mediacodec-handle-resolution-change",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "mediacodec-hevc",
                    "value": "0"
                },
                {
                    "category": 1,
                    "name": "dns_cache_timeout",
                    "value": "600000000"
                }
            ]
        },
        {
            "group": "硬解码",
            "options": [
                {
                    "category": 4,
                    "name": "opensles",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "overlay-format",
                    "value": "842225234"
                },
                {
                    "category": 4,
                    "name": "framedrop",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "soundtouch",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "start-on-prepared",
                    "value": "1"
                },
                {
                    "category": 1,
                    "name": "http-detect-range-support",
                    "value": "0"
                },
                {
                    "category": 1,
                    "name": "fflags",
                    "value": "fastseek"
                },
                {
                    "category": 2,
                    "name": "skip_loop_filter",
                    "value": "48"
                },
                {
                    "category": 4,
                    "name": "reconnect",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "enable-accurate-seek",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "mediacodec",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "mediacodec-auto-rotate",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "mediacodec-handle-resolution-change",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "mediacodec-hevc",
                    "value": "1"
                },
                {
                    "category": 1,
                    "name": "dns_cache_timeout",
                    "value": "600000000"
                }
            ]
        }
    ],
    "ads": [
        "static-mozai.4gtv.tv"
    ]
}