#!/usr/bin/env python3
"""
获取原始配置
"""
import sqlite3
import os
import json
import requests

def fetch_original_config():
    """获取原始配置"""
    db_path = "data/tvbox.db"
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取接口1的URL
        cursor.execute("SELECT id, name, url FROM interface_sources WHERE id = 1")
        result = cursor.fetchone()
        
        if result:
            interface_id, name, url = result
            print(f"接口ID: {interface_id}")
            print(f"接口名称: {name}")
            print(f"接口URL: {url}")
            
            # 尝试获取原始配置
            try:
                print(f"\n正在获取原始配置...")
                response = requests.get(url, timeout=10)
                response.raise_for_status()
                
                # 检查响应内容
                content = response.text
                print(f"响应内容类型: {response.headers.get('content-type', 'unknown')}")
                print(f"响应内容长度: {len(content)}")
                print(f"响应内容前200字符: {content[:200]}")

                # 尝试解析为JSON
                original_config = response.json()

                print(f"原始配置中的spider: {original_config.get('spider', 'N/A')}")
                
                # 检查sites中的api字段
                sites = original_config.get('sites', [])
                if sites:
                    print(f"原始配置中第一个site的api: {sites[0].get('api', 'N/A')}")
                    
                    # 查找包含HTTP URL的api字段
                    http_apis = []
                    for i, site in enumerate(sites):
                        api = site.get('api', '')
                        if isinstance(api, str) and api.startswith(('http://', 'https://')):
                            http_apis.append((i, api))
                    
                    print(f"\n找到 {len(http_apis)} 个HTTP API URL:")
                    for i, api in http_apis[:5]:  # 只显示前5个
                        print(f"  sites[{i}].api: {api}")
                
                # 查找spider字段
                spider = original_config.get('spider', '')
                if isinstance(spider, str) and spider.startswith(('http://', 'https://')):
                    print(f"\nSpider URL: {spider}")
                else:
                    print(f"\nSpider不是HTTP URL: {spider}")
                    
            except Exception as e:
                print(f"获取原始配置失败: {e}")
        else:
            print("未找到接口1")
            
    except Exception as e:
        print(f"检查失败: {str(e)}")
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    fetch_original_config()
