{% extends "base.html" %}

{% block title %}重置密码 - TVBox Manager{% endblock %}

{% block content %}
<div class="flex items-center justify-center py-6">
    <div class="w-full max-w-md">
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="bg-gradient-to-r from-primary-600 to-primary-800 px-6 py-8 text-center text-white">
                <h2 class="text-2xl font-bold mb-2">重置密码</h2>
                <p class="text-primary-100 text-sm">找回您的账户访问权限</p>
            </div>
            
            <div class="px-6 py-8">
                <form action="{{ url_for('security.forgot_password') }}" method="POST" name="forgot_password_form" id="forgot-password-form" class="custom-form">
                    {{ forgot_password_form.hidden_tag() }}
                    
                    <div class="form-group">
                        <label for="email" class="form-label flex items-center">
                            <i class="fas fa-envelope mr-2 text-primary-600"></i> 电子邮箱
                        </label>
                        {{ forgot_password_form.email(class="form-input", placeholder="请输入注册邮箱") }}
                        {% if forgot_password_form.email.errors %}
                        <div class="text-red-600 text-sm mt-1">
                            {% for error in forgot_password_form.email.errors %}
                            <p class="flex items-center"><i class="fas fa-exclamation-circle mr-1"></i> {{ error }}</p>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="text-gray-600 text-sm mt-4 mb-6 p-3 bg-gray-50 rounded-lg border border-gray-200">
                        <p class="flex items-start">
                            <i class="fas fa-info-circle mr-2 mt-1 text-primary-500"></i>
                            <span>我们将向您提供的电子邮箱地址发送密码重置链接，请确保输入正确的邮箱地址。</span>
                        </p>
                    </div>
                    
                    <div class="flex items-center justify-between mt-6 mb-4">
                        <button type="submit" class="btn btn-primary w-full flex items-center justify-center">
                            <i class="fas fa-paper-plane mr-2"></i> 发送重置链接
                        </button>
                    </div>
                </form>
                
                <div class="mt-6 pt-6 border-t border-gray-200 text-center text-sm text-gray-600">
                    <div class="grid grid-cols-2 gap-4">
                        <a href="{{ url_for('security.login') }}" class="text-primary-600 font-medium hover:text-primary-800 hover:underline">
                            <i class="fas fa-sign-in-alt mr-1"></i> 返回登录
                        </a>
                        <a href="{{ url_for('security.register') }}" class="text-primary-600 font-medium hover:text-primary-800 hover:underline">
                            <i class="fas fa-user-plus mr-1"></i> 注册新账户
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 移除Flask-Security额外的页面元素
    const securityContainers = document.querySelectorAll('.container');
    securityContainers.forEach(container => {
        if (container !== document.getElementById('forgot-password-form').closest('.container')) {
            container.style.display = 'none';
        }
    });
    
    // 移除额外的标题
    const titles = document.querySelectorAll('h1');
    titles.forEach(title => {
        if (title.textContent.includes('Reset') || title.textContent.includes('重置')) {
            title.style.display = 'none';
        }
    });
});
</script>
{% endblock %} 