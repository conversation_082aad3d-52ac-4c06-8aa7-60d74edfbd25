{"version": 3, "file": "constants.mjs", "sources": ["../../../../../../packages/components/popper/src/constants.ts"], "sourcesContent": ["import type { CSSProperties, ComputedRef, InjectionKey, Ref } from 'vue'\nimport type { Instance } from '@popperjs/core'\n\nexport type Measurable = {\n  getBoundingClientRect: () => DOMRect\n}\n\n/**\n * triggerRef indicates the element that triggers popper\n * contentRef indicates the element of popper content\n * referenceRef indicates the element that popper content relative with\n */\nexport type ElPopperInjectionContext = {\n  triggerRef: Ref<Measurable | undefined>\n  contentRef: Ref<HTMLElement | undefined>\n  popperInstanceRef: Ref<Instance | undefined>\n  referenceRef: Ref<Measurable | undefined>\n  role: ComputedRef<string>\n}\n\nexport type ElPopperContentInjectionContext = {\n  arrowRef: Ref<HTMLElement | undefined>\n  arrowStyle: ComputedRef<CSSProperties>\n}\n\nexport const POPPER_INJECTION_KEY: InjectionKey<ElPopperInjectionContext> =\n  Symbol('popper')\n\nexport const POPPER_CONTENT_INJECTION_KEY: InjectionKey<ElPopperContentInjectionContext> =\n  Symbol('popperContent')\n"], "names": [], "mappings": "AAAY,MAAC,oBAAoB,GAAG,MAAM,CAAC,QAAQ,EAAE;AACzC,MAAC,4BAA4B,GAAG,MAAM,CAAC,eAAe;;;;"}