{"version": 3, "file": "use-button.mjs", "sources": ["../../../../../../packages/components/button/src/use-button.ts"], "sourcesContent": ["import { Text, computed, inject, ref, useSlots } from 'vue'\nimport {\n  useFormDisabled,\n  useFormItem,\n  useFormSize,\n} from '@element-plus/components/form'\nimport { useGlobalConfig } from '@element-plus/components/config-provider'\nimport { useDeprecated } from '@element-plus/hooks'\nimport { buttonGroupContextKey } from './constants'\n\nimport type { SetupContext } from 'vue'\nimport type { ButtonEmits, ButtonProps } from './button'\n\nexport const useButton = (\n  props: ButtonProps,\n  emit: SetupContext<ButtonEmits>['emit']\n) => {\n  useDeprecated(\n    {\n      from: 'type.text',\n      replacement: 'link',\n      version: '3.0.0',\n      scope: 'props',\n      ref: 'https://element-plus.org/en-US/component/button.html#button-attributes',\n    },\n    computed(() => props.type === 'text')\n  )\n\n  const buttonGroupContext = inject(buttonGroupContextKey, undefined)\n  const globalConfig = useGlobalConfig('button')\n  const { form } = useFormItem()\n  const _size = useFormSize(computed(() => buttonGroupContext?.size))\n  const _disabled = useFormDisabled()\n  const _ref = ref<HTMLButtonElement>()\n  const slots = useSlots()\n\n  const _type = computed(\n    () =>\n      props.type || buttonGroupContext?.type || globalConfig.value?.type || ''\n  )\n  const autoInsertSpace = computed(\n    () => props.autoInsertSpace ?? globalConfig.value?.autoInsertSpace ?? false\n  )\n  const _plain = computed(\n    () => props.plain ?? globalConfig.value?.plain ?? false\n  )\n  const _round = computed(\n    () => props.round ?? globalConfig.value?.round ?? false\n  )\n\n  const _props = computed(() => {\n    if (props.tag === 'button') {\n      return {\n        ariaDisabled: _disabled.value || props.loading,\n        disabled: _disabled.value || props.loading,\n        autofocus: props.autofocus,\n        type: props.nativeType,\n      }\n    }\n    return {}\n  })\n\n  // add space between two characters in Chinese\n  const shouldAddSpace = computed(() => {\n    const defaultSlot = slots.default?.()\n    if (autoInsertSpace.value && defaultSlot?.length === 1) {\n      const slot = defaultSlot[0]\n      if (slot?.type === Text) {\n        const text = slot.children as string\n        return /^\\p{Unified_Ideograph}{2}$/u.test(text.trim())\n      }\n    }\n    return false\n  })\n\n  const handleClick = (evt: MouseEvent) => {\n    if (_disabled.value || props.loading) {\n      evt.stopPropagation()\n      return\n    }\n    if (props.nativeType === 'reset') {\n      form?.resetFields()\n    }\n    emit('click', evt)\n  }\n\n  return {\n    _disabled,\n    _size,\n    _type,\n    _ref,\n    _props,\n    _plain,\n    _round,\n    shouldAddSpace,\n    handleClick,\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AASY,MAAC,SAAS,GAAG,CAAC,KAAK,EAAE,IAAI,KAAK;AAC1C,EAAE,aAAa,CAAC;AAChB,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,WAAW,EAAE,MAAM;AACvB,IAAI,OAAO,EAAE,OAAO;AACpB,IAAI,KAAK,EAAE,OAAO;AAClB,IAAI,GAAG,EAAE,wEAAwE;AACjF,GAAG,EAAE,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC;AAC5C,EAAE,MAAM,kBAAkB,GAAG,MAAM,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC,CAAC;AACnE,EAAE,MAAM,YAAY,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC;AACjD,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,WAAW,EAAE,CAAC;AACjC,EAAE,MAAM,KAAK,GAAG,WAAW,CAAC,QAAQ,CAAC,MAAM,kBAAkB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;AAC3G,EAAE,MAAM,SAAS,GAAG,eAAe,EAAE,CAAC;AACtC,EAAE,MAAM,IAAI,GAAG,GAAG,EAAE,CAAC;AACrB,EAAE,MAAM,KAAK,GAAG,QAAQ,EAAE,CAAC;AAC3B,EAAE,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM;AAC/B,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,kBAAkB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,YAAY,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;AACzJ,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM;AACzC,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACnB,IAAI,OAAO,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,eAAe,KAAK,IAAI,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,YAAY,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,eAAe,KAAK,IAAI,GAAG,EAAE,GAAG,KAAK,CAAC;AACnJ,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM;AAChC,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACnB,IAAI,OAAO,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,KAAK,KAAK,IAAI,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,YAAY,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,KAAK,IAAI,GAAG,EAAE,GAAG,KAAK,CAAC;AAC/H,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM;AAChC,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACnB,IAAI,OAAO,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,KAAK,KAAK,IAAI,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,YAAY,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,KAAK,IAAI,GAAG,EAAE,GAAG,KAAK,CAAC;AAC/H,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM;AAChC,IAAI,IAAI,KAAK,CAAC,GAAG,KAAK,QAAQ,EAAE;AAChC,MAAM,OAAO;AACb,QAAQ,YAAY,EAAE,SAAS,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO;AACtD,QAAQ,QAAQ,EAAE,SAAS,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO;AAClD,QAAQ,SAAS,EAAE,KAAK,CAAC,SAAS;AAClC,QAAQ,IAAI,EAAE,KAAK,CAAC,UAAU;AAC9B,OAAO,CAAC;AACR,KAAK;AACL,IAAI,OAAO,EAAE,CAAC;AACd,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM;AACxC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,WAAW,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC/E,IAAI,IAAI,eAAe,CAAC,KAAK,IAAI,CAAC,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,WAAW,CAAC,MAAM,MAAM,CAAC,EAAE;AAC5F,MAAM,MAAM,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;AAClC,MAAM,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,MAAM,IAAI,EAAE;AACxD,QAAQ,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;AACnC,QAAQ,OAAO,6BAA6B,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;AAC/D,OAAO;AACP,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,WAAW,GAAG,CAAC,GAAG,KAAK;AAC/B,IAAI,IAAI,SAAS,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,EAAE;AAC1C,MAAM,GAAG,CAAC,eAAe,EAAE,CAAC;AAC5B,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,UAAU,KAAK,OAAO,EAAE;AACtC,MAAM,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;AACjD,KAAK;AACL,IAAI,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AACvB,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,SAAS;AACb,IAAI,KAAK;AACT,IAAI,KAAK;AACT,IAAI,IAAI;AACR,IAAI,MAAM;AACV,IAAI,MAAM;AACV,IAAI,MAAM;AACV,IAAI,cAAc;AAClB,IAAI,WAAW;AACf,GAAG,CAAC;AACJ;;;;"}