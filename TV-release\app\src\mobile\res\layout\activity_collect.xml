<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <EditText
            android:id="@+id/keyword"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@null"
            android:hint="@string/search_keyword"
            android:imeOptions="actionDone"
            android:inputType="textCapWords|textAutoCorrect|textAutoComplete"
            android:letterSpacing="0.02"
            android:maxLength="255"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textColorHint="@color/white"
            android:textCursorDrawable="@drawable/shape_cursor"
            android:textSize="20sp" />

        <ImageView
            android:id="@+id/site"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:src="@drawable/ic_action_site" />

        <ImageView
            android:id="@+id/view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:src="@drawable/ic_action_list"
            android:visibility="gone"
            tools:visibility="visible" />

    </LinearLayout>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/agent"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        tools:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/record"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="16dp"
                android:text="@string/search_record"
                android:textColor="@color/white"
                android:textSize="18sp" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recordRecycler"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="-8dp"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:nestedScrollingEnabled="false"
                android:overScrollMode="never"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:paddingBottom="8dp"
                tools:itemCount="5"
                tools:listitem="@layout/adapter_collect_record" />

            <TextView
                android:id="@+id/word"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="16dp"
                android:text="@string/search_hot"
                android:textColor="@color/white"
                android:textSize="18sp" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/wordRecycler"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="-8dp"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:nestedScrollingEnabled="false"
                android:overScrollMode="never"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:paddingBottom="8dp"
                tools:itemCount="5"
                tools:listitem="@layout/adapter_collect_word" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <RelativeLayout
        android:id="@+id/result"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        tools:visibility="visible">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/collect"
            android:layout_width="120dp"
            android:layout_height="match_parent"
            android:layout_marginTop="-8dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:overScrollMode="never"
            android:paddingStart="8dp"
            android:paddingEnd="0dp"
            android:paddingBottom="8dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/adapter_collect" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="-8dp"
            android:layout_toEndOf="@+id/collect"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:overScrollMode="never"
            android:paddingStart="0dp"
            android:paddingEnd="8dp"
            android:paddingBottom="8dp"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:spanCount="2"
            tools:listitem="@layout/adapter_vod_rect" />

    </RelativeLayout>
</LinearLayout>