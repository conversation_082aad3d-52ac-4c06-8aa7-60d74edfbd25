@echo off
chcp 65001 >nul
title TVBox Manager Pro - 快速测试

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    TVBox Manager Pro                         ║
echo ║                      快速测试工具                            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 请选择启动方式:
echo.
echo [1] Docker 方式启动 (推荐)
echo [2] 本地开发环境启动
echo [3] 仅测试后端解密功能
echo [4] 查看系统要求
echo [0] 退出
echo.

set /p choice=请输入选择 (0-4): 

if "%choice%"=="1" goto docker_start
if "%choice%"=="2" goto dev_start
if "%choice%"=="3" goto test_decrypt
if "%choice%"=="4" goto show_requirements
if "%choice%"=="0" goto exit
goto invalid_choice

:docker_start
echo.
echo 🐳 使用 Docker 方式启动...
call scripts\start.bat
goto end

:dev_start
echo.
echo 🔧 使用本地开发环境启动...
call scripts\dev-start.bat
goto end

:test_decrypt
echo.
echo 🔍 测试后端解密功能...
cd backend
python -c "
import sys
sys.path.append('.')
from app.services.tvbox_decryptor import TVBoxDecryptor

print('🧪 测试TVBox解密器...')
decryptor = TVBoxDecryptor()

# 测试URL
test_urls = [
    'https://raw.githubusercontent.com/example/config.json',
    'http://example.com/tvbox.json'
]

for url in test_urls:
    try:
        print(f'\\n📡 测试URL: {url}')
        content, method = decryptor.decrypt_config_url(url)
        print(f'✅ 解密成功，方法: {method}')
        print(f'📄 内容长度: {len(content)} 字符')
    except Exception as e:
        print(f'❌ 解密失败: {str(e)}')

print('\\n🎉 解密功能测试完成！')
"
pause
goto end

:show_requirements
echo.
echo 📋 系统要求:
echo.
echo 🐳 Docker 方式 (推荐):
echo    - Windows 10/11 (64位)
echo    - Docker Desktop for Windows
echo    - 4GB+ 内存
echo    - 2GB+ 磁盘空间
echo.
echo 🔧 本地开发方式:
echo    - Windows 10/11
echo    - Python 3.8+ (https://www.python.org/downloads/)
echo    - Node.js 16+ (https://nodejs.org/)
echo    - Git (https://git-scm.com/)
echo    - 4GB+ 内存
echo    - 1GB+ 磁盘空间
echo.
echo 📱 浏览器支持:
echo    - Chrome 80+
echo    - Firefox 75+
echo    - Edge 80+
echo    - Safari 13+
echo.
pause
goto end

:invalid_choice
echo.
echo ❌ 无效选择，请重新输入
timeout /t 2 /nobreak >nul
cls
goto start

:exit
echo.
echo 👋 再见！
timeout /t 1 /nobreak >nul
exit /b 0

:end
echo.
echo 按任意键返回主菜单...
pause >nul
cls
goto start

:start
goto :eof
