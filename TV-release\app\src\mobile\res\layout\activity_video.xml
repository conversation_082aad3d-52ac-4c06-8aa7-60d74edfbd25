<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:tag="port">

    <FrameLayout
        android:id="@+id/video"
        android:layout_width="match_parent"
        android:layout_height="220dp"
        android:background="@color/black"
        android:clickable="true"
        android:focusable="true">

        <androidx.media3.ui.PlayerView
            android:id="@+id/exo"
            style="@style/Player.Vod"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:surface_type="surface_view" />

        <master.flame.danmaku.ui.widget.DanmakuView
            android:id="@+id/danmaku"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <include
            android:id="@+id/widget"
            layout="@layout/view_widget_vod"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <include
            android:id="@+id/control"
            layout="@layout/view_control_vod"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone" />

    </FrameLayout>

    <include
        layout="@layout/view_shadow"
        android:layout_width="match_parent"
        android:layout_height="4dp"
        android:layout_below="@+id/video" />

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/video">

        <com.fongmi.android.tv.ui.custom.ProgressLayout
            android:id="@+id/progressLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.core.widget.NestedScrollView
                android:id="@+id/scroll"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fillViewport="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:clipChildren="false"
                    android:clipToPadding="false"
                    android:orientation="vertical"
                    android:paddingTop="16dp"
                    android:paddingBottom="8dp">

                    <TextView
                        android:id="@+id/name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:ellipsize="end"
                        android:maxLines="2"
                        android:textColor="@color/white"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        tools:text="慶餘年第二季" />

                    <TextView
                        android:id="@+id/remark"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginEnd="16dp"
                        android:singleLine="true"
                        android:textColor="@color/white"
                        android:textSize="14sp"
                        tools:text="更新到第二季" />

                    <TextView
                        android:id="@+id/site"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginEnd="16dp"
                        android:singleLine="true"
                        android:textColor="@color/white"
                        android:textSize="14sp"
                        tools:text="站源：泥巴" />

                    <TextView
                        android:id="@+id/other"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginEnd="16dp"
                        android:singleLine="true"
                        android:textColor="@color/white"
                        android:textSize="14sp"
                        tools:text="年份：2022 地區：台灣 類型：科幻" />

                    <TextView
                        android:id="@+id/director"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginEnd="16dp"
                        android:singleLine="true"
                        android:textColor="@color/white"
                        android:textSize="14sp"
                        tools:text="導演：FongMi" />

                    <TextView
                        android:id="@+id/actor"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginEnd="16dp"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textColor="@color/white"
                        android:textSize="14sp"
                        tools:text="演員：FongMi" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginEnd="16dp"
                        android:text="@string/detail_flag"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/flag"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:clipChildren="false"
                        android:clipToPadding="false"
                        android:orientation="horizontal"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                    <TextView
                        android:id="@+id/quality_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginEnd="16dp"
                        android:text="@string/detail_quality"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/quality"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:clipChildren="false"
                        android:clipToPadding="false"
                        android:orientation="horizontal"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:visibility="gone"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:visibility="visible" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginEnd="12dp"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/detail_episode"
                            android:textColor="@color/white"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <ImageView
                            android:id="@+id/reverse"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_marginStart="8dp"
                            android:background="?attr/selectableItemBackgroundBorderless"
                            android:src="@drawable/ic_detail_reverse"
                            android:visibility="gone"
                            tools:visibility="visible" />

                        <View
                            android:layout_width="0dp"
                            android:layout_height="0dp"
                            android:layout_weight="1" />

                        <TextView
                            android:id="@+id/more"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:text="@string/detail_more"
                            android:textColor="@color/white"
                            android:textSize="14sp"
                            android:visibility="gone"
                            app:drawableEndCompat="@drawable/ic_detail_more"
                            tools:visibility="visible" />

                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/episode"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="8dp"
                        android:clipChildren="false"
                        android:clipToPadding="false"
                        android:orientation="horizontal"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                    <LinearLayout
                        android:id="@+id/content_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:layout_marginTop="8dp"
                            android:layout_marginEnd="16dp"
                            android:text="@string/detail_content"
                            android:textColor="@color/white"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/content"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:layout_marginTop="8dp"
                            android:layout_marginEnd="16dp"
                            android:layout_marginBottom="8dp"
                            android:ellipsize="end"
                            android:lineSpacingExtra="4dp"
                            android:maxLines="2"
                            android:textColor="@color/white"
                            android:textSize="14sp"
                            tools:text="一個身世神秘的少年范閒（張若昀飾），因對自身身世的好奇而離開故鄉澹州，前赴南慶京都。范閒在京中各大勢力之間遊走，以探查自己屢遭刺殺的原因。其間結識靖王世子、二皇子，與太子、長公主等人對抗，並遊歷四方、闖蕩江湖，卻意外發現這世界隱藏著的秘密。本劇第一季上半部主要描述范閒在南慶京都追查刺殺的幕後黑手，京中各式人等對范閒繼承內庫的態度和應對，以及監察院院長陳萍萍希望范閒接掌監察院所做的一切舉動。下半部劇情發生在北齊上京。南慶駐北齊上京諜報首領言冰雲被北齊官方抓獲，慶帝與陳萍萍同意用已被關在監察院大牢多年的前北魏諜報首領肖恩交換言冰雲，並派范閒親率南慶使團負責此事。" />

                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/quick"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:clipChildren="false"
                        android:clipToPadding="false"
                        android:nestedScrollingEnabled="false"
                        android:paddingStart="8dp"
                        android:paddingEnd="8dp"
                        android:visibility="gone"
                        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                        app:spanCount="2"
                        tools:listitem="@layout/adapter_quick"
                        tools:visibility="visible" />

                </LinearLayout>
            </androidx.core.widget.NestedScrollView>
        </com.fongmi.android.tv.ui.custom.ProgressLayout>
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
</RelativeLayout>