import Scrollbar from './src/scrollbar2.mjs';
export { BAR_MAP, GAP, renderThumbStyle } from './src/util.mjs';
export { scrollbarEmits, scrollbarProps } from './src/scrollbar.mjs';
export { thumbProps } from './src/thumb.mjs';
export { scrollbarContextKey } from './src/constants.mjs';
import { withInstall } from '../../utils/vue/install.mjs';

const ElScrollbar = withInstall(Scrollbar);

export { ElScrollbar, ElScrollbar as default };
//# sourceMappingURL=index.mjs.map
