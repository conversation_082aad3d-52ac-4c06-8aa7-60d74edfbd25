{"version": 3, "file": "button-group2.mjs", "sources": ["../../../../../../packages/components/button/src/button-group.vue"], "sourcesContent": ["<template>\n  <div :class=\"ns.b('group')\">\n    <slot />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { provide, reactive, toRef } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { buttonGroupProps } from './button-group'\nimport { buttonGroupContextKey } from './constants'\n\ndefineOptions({\n  name: 'ElButtonGroup',\n})\nconst props = defineProps(buttonGroupProps)\nprovide(\n  buttonGroupContextKey,\n  reactive({\n    size: toRef(props, 'size'),\n    type: toRef(props, 'type'),\n  })\n)\nconst ns = useNamespace('button')\n</script>\n"], "names": ["_openBlock", "_createElementBlock", "_normalizeClass", "_unref"], "mappings": ";;;;;;mCAYc,CAAA;AAAA,EACZ,IAAM,EAAA,eAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAEA,IAAA,OAAA,CAAA,qBAAA,EAAA,QAAA,CAAA;AAAA,MACE,IAAA,EAAA,KAAA,CAAA,KAAA,EAAA,MAAA,CAAA;AAAA,MACA,IAAS,EAAA,KAAA,CAAA,KAAA,EAAA,MAAA,CAAA;AAAA,KACP,CAAA,CAAA,CAAA;AAAyB,IACzB,MAAA,EAAA,GAAY,YAAO,CAAM,QAAA,CAAA,CAAA;AAAA,IAAA,OAC1B,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MACH,OAAAA,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AACA,QAAM,KAAA,EAAKC,cAAqB,CAAAC,KAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA;;;;;;;;;;;"}