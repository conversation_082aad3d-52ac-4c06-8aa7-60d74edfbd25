// TVBox API 测试工具主应用
class TVBoxAPITester {
    constructor() {
        this.config = {
            apiBaseUrl: 'http://localhost:8001',
            timeout: 30000,
            userEmail: '<EMAIL>',
            userPassword: 'admin123'
        };
        this.currentCategory = 'auth';
        this.testResults = [];
        this.isAutoTesting = false;
        
        this.init();
    }
    
    init() {
        // 加载配置
        this.loadConfig();
        
        // 初始化界面
        this.initUI();
        
        // 测试服务器连接
        this.testConnection();
        
        // 选择默认分类
        this.selectCategory('auth');
        
        console.log('TVBox API 测试工具已初始化');
    }
    
    loadConfig() {
        // 从localStorage加载配置
        const savedConfig = localStorage.getItem('tvbox-api-tester-config');
        if (savedConfig) {
            try {
                this.config = { ...this.config, ...JSON.parse(savedConfig) };
            } catch (e) {
                console.warn('加载配置失败:', e);
            }
        }
        
        // 更新界面
        document.getElementById('apiBaseUrl').value = this.config.apiBaseUrl;
        document.getElementById('timeout').value = this.config.timeout / 1000;
        document.getElementById('userEmail').value = this.config.userEmail;
        document.getElementById('userPassword').value = this.config.userPassword;
    }
    
    saveConfig() {
        // 获取界面值
        this.config.apiBaseUrl = document.getElementById('apiBaseUrl').value.trim();
        this.config.timeout = parseInt(document.getElementById('timeout').value) * 1000;
        this.config.userEmail = document.getElementById('userEmail').value.trim();
        this.config.userPassword = document.getElementById('userPassword').value.trim();
        
        // 保存到localStorage
        localStorage.setItem('tvbox-api-tester-config', JSON.stringify(this.config));
        
        // 显示成功消息
        this.showToast('配置已保存', 'success');
        
        // 重新测试连接
        this.testConnection();
    }
    
    initUI() {
        // 初始化工具提示
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
        
        // 初始化日志区域
        this.initLogs();
    }
    
    initLogs() {
        const logsContent = document.getElementById('logsContent');
        logsContent.innerHTML = '<div class="text-muted">等待日志输出...</div>';
    }
    
    async testConnection() {
        const statusElement = document.getElementById('serverStatus');
        statusElement.textContent = '测试中...';
        statusElement.className = 'badge status-testing';
        
        try {
            const response = await axios.get(`${this.config.apiBaseUrl}/docs`, {
                timeout: 5000
            });
            
            if (response.status === 200) {
                statusElement.textContent = '在线';
                statusElement.className = 'badge status-online';
                this.log('服务器连接成功', 'success');
            } else {
                throw new Error(`HTTP ${response.status}`);
            }
        } catch (error) {
            statusElement.textContent = '离线';
            statusElement.className = 'badge status-offline';
            this.log(`服务器连接失败: ${error.message}`, 'error');
        }
    }
    
    selectCategory(categoryKey) {
        this.currentCategory = categoryKey;
        
        // 更新侧边栏选中状态
        document.querySelectorAll('.list-group-item').forEach(item => {
            item.classList.remove('active');
        });
        
        const categoryElement = document.querySelector(`[onclick="selectCategory('${categoryKey}')"]`);
        if (categoryElement) {
            categoryElement.classList.add('active');
        }
        
        // 更新API选择下拉框
        this.updateApiSelect();
        
        // 清空手动测试表单
        this.clearManualTestForm();
        
        this.log(`选择分类: ${API_DEFINITIONS[categoryKey]?.name || categoryKey}`, 'info');
    }
    
    updateApiSelect() {
        const apiSelect = document.getElementById('apiSelect');
        const category = API_DEFINITIONS[this.currentCategory];
        
        if (!category) {
            apiSelect.innerHTML = '<option value="">无可用API</option>';
            return;
        }
        
        let options = '<option value="">选择API接口</option>';
        for (const [apiKey, api] of Object.entries(category.apis)) {
            options += `<option value="${apiKey}">${api.name} (${api.method})</option>`;
        }
        
        apiSelect.innerHTML = options;
    }
    
    selectApi() {
        const apiSelect = document.getElementById('apiSelect');
        const selectedApi = apiSelect.value;
        
        if (!selectedApi) {
            this.clearManualTestForm();
            return;
        }
        
        const apiDef = getApiDefinition(this.currentCategory, selectedApi);
        if (!apiDef) {
            this.clearManualTestForm();
            return;
        }
        
        this.renderManualTestForm(apiDef);
        this.log(`选择API: ${apiDef.name}`, 'info');
    }
    
    clearManualTestForm() {
        const formContainer = document.getElementById('manualTestForm');
        formContainer.innerHTML = `
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i> 
                请先选择左侧的API分类，然后选择具体的API接口进行测试。
            </div>
        `;
    }
    
    renderManualTestForm(apiDef) {
        const formContainer = document.getElementById('manualTestForm');
        
        let formHtml = `
            <div class="row mb-3">
                <div class="col-md-6">
                    <h6><i class="bi bi-info-circle"></i> 接口信息</h6>
                    <p><strong>名称:</strong> ${apiDef.name}</p>
                    <p><strong>方法:</strong> <span class="badge bg-primary">${apiDef.method}</span></p>
                    <p><strong>路径:</strong> <code>${apiDef.path}</code></p>
                    <p><strong>描述:</strong> ${apiDef.description}</p>
                    <p><strong>需要认证:</strong> ${apiDef.requiresAuth ? '是' : '否'}</p>
                </div>
                <div class="col-md-6">
                    <h6><i class="bi bi-gear"></i> 请求参数</h6>
                    <form id="apiTestForm">
        `;
        
        // 渲染参数表单
        if (Object.keys(apiDef.parameters).length === 0) {
            formHtml += '<p class="text-muted">此接口无需参数</p>';
        } else {
            for (const [paramKey, param] of Object.entries(apiDef.parameters)) {
                if (param.isPathParam) continue; // 路径参数单独处理
                
                formHtml += `<div class="mb-3">`;
                formHtml += `<label class="form-label">${param.description || paramKey}`;
                if (param.required) {
                    formHtml += ' <span class="text-danger">*</span>';
                }
                formHtml += '</label>';
                
                if (param.type === 'textarea') {
                    formHtml += `<textarea class="form-control" name="${paramKey}" 
                                placeholder="${param.default || ''}" 
                                ${param.required ? 'required' : ''}>${param.default || ''}</textarea>`;
                } else if (param.type === 'select') {
                    formHtml += `<select class="form-select" name="${paramKey}" ${param.required ? 'required' : ''}>`;
                    if (param.options) {
                        for (const option of param.options) {
                            const selected = option === param.default ? 'selected' : '';
                            formHtml += `<option value="${option}" ${selected}>${option}</option>`;
                        }
                    }
                    formHtml += '</select>';
                } else if (param.type === 'checkbox') {
                    const checked = param.default ? 'checked' : '';
                    formHtml += `<div class="form-check">
                        <input class="form-check-input" type="checkbox" name="${paramKey}" ${checked}>
                        <label class="form-check-label">${param.description}</label>
                    </div>`;
                } else {
                    formHtml += `<input type="${param.type}" class="form-control" name="${paramKey}" 
                                value="${param.default || ''}" 
                                placeholder="${param.default || ''}" 
                                ${param.required ? 'required' : ''}>`;
                }
                formHtml += '</div>';
            }
        }
        
        formHtml += `
                    </form>
                </div>
            </div>
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button class="btn btn-success" onclick="sendManualRequest()">
                    <i class="bi bi-send"></i> 发送请求
                </button>
                <button class="btn btn-secondary" onclick="fillExampleData()">
                    <i class="bi bi-clipboard"></i> 填充示例
                </button>
                <button class="btn btn-outline-danger" onclick="clearForm()">
                    <i class="bi bi-x-circle"></i> 清空表单
                </button>
            </div>
            <div id="manualTestResult" class="mt-3"></div>
        `;
        
        formContainer.innerHTML = formHtml;
    }
    
    log(message, level = 'info') {
        const logsContent = document.getElementById('logsContent');
        const timestamp = new Date().toLocaleTimeString();
        
        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';
        logEntry.innerHTML = `
            <span class="log-timestamp">[${timestamp}]</span>
            <span class="log-level-${level}">[${level.toUpperCase()}]</span>
            ${message}
        `;
        
        logsContent.appendChild(logEntry);
        logsContent.scrollTop = logsContent.scrollHeight;
        
        // 限制日志条数
        const logEntries = logsContent.querySelectorAll('.log-entry');
        if (logEntries.length > 1000) {
            logEntries[0].remove();
        }
    }
    
    showToast(message, type = 'info') {
        // 创建简单的提示消息
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(toast);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }
}

// 全局变量
let app;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    app = new TVBoxAPITester();
});

// 全局函数
function testConnection() {
    app.testConnection();
}

function saveConfig() {
    app.saveConfig();
}

function selectCategory(categoryKey) {
    app.selectCategory(categoryKey);
}

function selectApi() {
    app.selectApi();
}

function togglePasswordVisibility() {
    const passwordInput = document.getElementById('userPassword');
    const toggleBtn = passwordInput.nextElementSibling.querySelector('i');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleBtn.className = 'bi bi-eye-slash';
    } else {
        passwordInput.type = 'password';
        toggleBtn.className = 'bi bi-eye';
    }
}

function clearLogs() {
    const logsContent = document.getElementById('logsContent');
    logsContent.innerHTML = '<div class="text-muted">日志已清空</div>';
    app.log('日志已清空', 'info');
}

function loadApiDocs() {
    const docsUrl = `${app.config.apiBaseUrl}/docs`;
    window.open(docsUrl, '_blank');
    app.log(`打开API文档: ${docsUrl}`, 'info');
}
