#!/usr/bin/env python3
import requests
import json

def test_interfaces_api():
    base_url = "http://127.0.0.1:8001"
    
    # 1. 登录获取token
    print("1. 登录...")
    login_response = requests.post(f"{base_url}/api/v1/auth/login", json={
        "username": "admin",
        "password": "admin123"
    })
    
    if login_response.status_code != 200:
        print(f"登录失败: {login_response.status_code} - {login_response.text}")
        return
    
    token = login_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    print("登录成功!")
    
    # 2. 获取接口列表
    print("\n2. 获取接口列表...")
    interfaces_response = requests.get(f"{base_url}/api/v1/interfaces", headers=headers, params={
        "skip": 0,
        "limit": 20
    })
    
    print(f"状态码: {interfaces_response.status_code}")
    if interfaces_response.status_code == 200:
        data = interfaces_response.json()
        print(f"接口总数: {data.get('total', 0)}")
        print(f"返回接口数: {len(data.get('items', []))}")
        
        for item in data.get('items', []):
            print(f"- ID: {item.get('id')}, 名称: {item.get('name')}, 本地化: {item.get('enable_localization')}")
    else:
        print(f"获取接口列表失败: {interfaces_response.text}")
    
    # 3. 获取接口详情
    print("\n3. 获取接口详情...")
    detail_response = requests.get(f"{base_url}/api/v1/interfaces/1", headers=headers)
    print(f"状态码: {detail_response.status_code}")
    if detail_response.status_code == 200:
        detail = detail_response.json()
        print(f"接口名称: {detail.get('name')}")
        print(f"本地化状态: {detail.get('enable_localization')}")
        print(f"本地化进度: {detail.get('localization_status')}")
    else:
        print(f"获取接口详情失败: {detail_response.text}")

if __name__ == "__main__":
    test_interfaces_api()
