#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据模型模块
"""

# 导入所有模型
from .user import User, Role, Permission, ApiKey, user_roles, role_permissions
from .tvbox import InterfaceSource, TvboxConfig, SiteInfo, LiveGroup, ParseInfo
from .system import SystemSetting, OperationLog, Subscription, ScheduledTask, BackupRecord, SystemStatus

# 导出所有模型
__all__ = [
    # 用户相关模型
    "User",
    "Role", 
    "Permission",
    "ApiKey",
    "user_roles",
    "role_permissions",
    
    # TVBox相关模型
    "InterfaceSource",
    "TvboxConfig",
    "SiteInfo",
    "LiveGroup",
    "ParseInfo",

    
    # 系统相关模型
    "SystemSetting",
    "OperationLog",
    "Subscription",
    "ScheduledTask",
    "BackupRecord",
    "SystemStatus"
]
