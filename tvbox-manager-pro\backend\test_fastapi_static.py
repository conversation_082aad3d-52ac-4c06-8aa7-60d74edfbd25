#!/usr/bin/env python3
"""测试FastAPI静态文件配置"""

import os
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
import uvicorn

app = FastAPI()

# 检查目录
localized_dir = "data/localized"
print(f"当前工作目录: {os.getcwd()}")
print(f"本地化目录: {localized_dir}")
print(f"目录存在: {os.path.exists(localized_dir)}")

if os.path.exists(localized_dir):
    print(f"挂载静态文件: /localized -> {localized_dir}")
    app.mount("/localized", StaticFiles(directory=localized_dir), name="localized")
    
    # 列出目录内容
    print("目录内容:")
    for root, dirs, files in os.walk(localized_dir):
        for file in files:
            file_path = os.path.join(root, file)
            print(f"  {file_path}")

@app.get("/")
async def root():
    return {"message": "Hello World"}

@app.get("/test")
async def test():
    return {"localized_dir": localized_dir, "exists": os.path.exists(localized_dir)}

if __name__ == "__main__":
    uvicorn.run(app, host="127.0.0.1", port=8002)
