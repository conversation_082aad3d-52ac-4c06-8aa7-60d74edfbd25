{"version": 3, "file": "te.mjs", "sources": ["../../../../../packages/locale/lang/te.ts"], "sourcesContent": ["export default {\n  name: 'te',\n  el: {\n    breadcrumb: {\n      label: 'బ్రెడ్‌క్రంబ్',\n    },\n    colorpicker: {\n      confirm: 'సరే',\n      clear: 'తొలగించు',\n      defaultLabel: 'రంగు ఎంచుకోండి',\n      description:\n        'ప్రస్తుత రంగు {color}. కొత్త రంగును ఎంచుకోవడానికి ఎంటర్ నొక్కండి.',\n      alphaLabel: 'అల్ఫా విలువను ఎంచుకోండి',\n    },\n    datepicker: {\n      now: 'ఇప్పుడు',\n      today: 'ఈ రోజు',\n      cancel: 'రద్దు',\n      clear: 'తొలగించు',\n      confirm: 'సరే',\n      dateTablePrompt:\n        'నెలలోని రోజును ఎంచుకోవడానికి ఈతలు మరియు ఎంటర్ ఉపయోగించండి',\n      monthTablePrompt: 'నెలను ఎంచుకోవడానికి ఈతలు మరియు ఎంటర్ ఉపయోగించండి',\n      yearTablePrompt:\n        'సంవత్సరాన్ని ఎంచుకోవడానికి ఈతలు మరియు ఎంటర్ ఉపయోగించండి',\n      selectedDate: 'ఎంచుకున్న తేదీ',\n      selectDate: 'తేదీ ఎంచుకోండి',\n      selectTime: 'సమయం ఎంచుకోండి',\n      startDate: 'ప్రారంభ తేదీ',\n      startTime: 'ప్రారంభ సమయం',\n      endDate: 'ముగింపు తేదీ',\n      endTime: 'ముగింపు సమయం',\n      prevYear: 'గత సంవత్సరం',\n      nextYear: 'తదుపరి సంవత్సరం',\n      prevMonth: 'గత నెల',\n      nextMonth: 'తదుపరి నెల',\n      year: 'సంవత్సరం',\n      month1: 'జనవరి',\n      month2: 'ఫిబ్రవరి',\n      month3: 'మార్చి',\n      month4: 'ఏప్రిల్',\n      month5: 'మే',\n      month6: 'జూన్',\n      month7: 'జూలై',\n      month8: 'ఆగస్టు',\n      month9: 'సెప్టెంబర్',\n      month10: 'అక్టోబర్',\n      month11: 'నవంబర్',\n      month12: 'డిసెంబర్',\n      week: 'వారం',\n      weeks: {\n        sun: 'ఆది',\n        mon: 'సోమ',\n        tue: 'మంగళ',\n        wed: 'బుధ',\n        thu: 'గురు',\n        fri: 'శుక్ర',\n        sat: 'శని',\n      },\n      weeksFull: {\n        sun: 'ఆదివారం',\n        mon: 'సోమవారం',\n        tue: 'మంగళవారం',\n        wed: 'బుధవారం',\n        thu: 'గురువారం',\n        fri: 'శుక్రవారం',\n        sat: 'శనివారం',\n      },\n      months: {\n        jan: 'జన',\n        feb: 'ఫిబ్ర',\n        mar: 'మార్చి',\n        apr: 'ఏప్రి',\n        may: 'మే',\n        jun: 'జూన్',\n        jul: 'జూలై',\n        aug: 'ఆగ',\n        sep: 'సెప్',\n        oct: 'అక్టో',\n        nov: 'నవం',\n        dec: 'డిసెం',\n      },\n    },\n    inputNumber: {\n      decrease: 'సంఖ్య తగ్గించు',\n      increase: 'సంఖ్య పెంచు',\n    },\n    select: {\n      loading: 'లోడ్ అవుతోంది',\n      noMatch: 'ఫలితాలు కనబడలేదు',\n      noData: 'డేటా లేదు',\n      placeholder: 'ఎంచుకోండి',\n    },\n    mention: {\n      loading: 'లోడ్ అవుతోంది',\n    },\n    dropdown: {\n      toggleDropdown: 'డ్రాప్‌డౌన్ మార్చు',\n    },\n    cascader: {\n      noMatch: 'ఫలితాలు కనబడలేదు',\n      loading: 'లోడ్ అవుతోంది',\n      placeholder: 'ఎంచుకోండి',\n      noData: 'డేటా లేదు',\n    },\n    pagination: {\n      goto: 'వెళ్ళండి',\n      pagesize: '/పేజీ',\n      total: 'మొత్తం {total}',\n      pageClassifier: '',\n      page: 'పేజీ',\n      prev: 'మునుపటి పేజీకి వెళ్ళండి',\n      next: 'తదుపరి పేజీకి వెళ్ళండి',\n      currentPage: 'పేజీ {pager}',\n      prevPages: 'మునుపటి {pager} పేజీలు',\n      nextPages: 'తదుపరి {pager} పేజీలు',\n      deprecationWarning:\n        'పాత పద్ధతులు గుర్తించబడ్డాయి, మరిన్ని వివరాల కోసం el-pagination డాక్యుమెంటేషన్ చూడండి',\n    },\n    dialog: {\n      close: 'ఈ డైలాగ్ మూసివేయి',\n    },\n    drawer: {\n      close: 'ఈ డైలాగ్ మూసివేయి',\n    },\n    messagebox: {\n      title: 'సందేశం',\n      confirm: 'సరే',\n      cancel: 'రద్దు',\n      error: 'చెల్లని ఇన్‌పుట్',\n      close: 'ఈ డైలాగ్ మూసివేయి',\n    },\n    upload: {\n      deleteTip: 'తొలగించడానికి డిలీట్ నొక్కండి',\n      delete: 'తొలగించు',\n      preview: 'ప్రివ్యూ',\n      continue: 'కొనసాగించు',\n    },\n    slider: {\n      defaultLabel: '{min} మరియు {max} మధ్య స్లైడర్',\n      defaultRangeStartLabel: 'ప్రారంభ విలువ ఎంచుకోండి',\n      defaultRangeEndLabel: 'ముగింపు విలువ ఎంచుకోండి',\n    },\n    table: {\n      emptyText: 'డేటా లేదు',\n      confirmFilter: 'నిర్ధారించు',\n      resetFilter: 'రీసెట్',\n      clearFilter: 'తొలగించు',\n      sumText: 'మొత్తం',\n    },\n    tour: {\n      next: 'తదుపరి',\n      previous: 'గత',\n      finish: 'ముగించు',\n    },\n    tree: {\n      emptyText: 'డేటా లేదు',\n    },\n    transfer: {\n      noMatch: 'ఫలితాలు కనబడలేదు',\n      noData: 'డేటా లేదు',\n      titles: ['జాబితా 1', 'జాబితా 2'],\n      filterPlaceholder: 'కీవర్డ్ నమోదు చేయండి',\n      noCheckedFormat: '{total} అంశాలు',\n      hasCheckedFormat: '{checked}/{total} ఎంపిక చేయబడ్డాయి',\n    },\n    image: {\n      error: 'విఫలమైంది',\n    },\n    pageHeader: {\n      title: 'వెనక్కి',\n    },\n    popconfirm: {\n      confirmButtonText: 'అవును',\n      cancelButtonText: 'కాదు',\n    },\n    carousel: {\n      leftArrow: 'క్యారసెల్ ఎడమ బాణం',\n      rightArrow: 'క్యారసెల్ కుడి బాణం',\n      indicator: 'క్యారసెల్ సూచిక {index} కి మార్చు',\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,gFAAgF;AAC7F,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,oBAAoB;AACnC,MAAM,KAAK,EAAE,kDAAkD;AAC/D,MAAM,YAAY,EAAE,iFAAiF;AACrG,MAAM,WAAW,EAAE,wTAAwT;AAC3U,MAAM,UAAU,EAAE,kIAAkI;AACpJ,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,4CAA4C;AACvD,MAAM,KAAK,EAAE,iCAAiC;AAC9C,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,KAAK,EAAE,kDAAkD;AAC/D,MAAM,OAAO,EAAE,oBAAoB;AACnC,MAAM,eAAe,EAAE,0TAA0T;AACjV,MAAM,gBAAgB,EAAE,yQAAyQ;AACjS,MAAM,eAAe,EAAE,mTAAmT;AAC1U,MAAM,YAAY,EAAE,iFAAiF;AACrG,MAAM,UAAU,EAAE,iFAAiF;AACnG,MAAM,UAAU,EAAE,iFAAiF;AACnG,MAAM,SAAS,EAAE,qEAAqE;AACtF,MAAM,SAAS,EAAE,qEAAqE;AACtF,MAAM,OAAO,EAAE,qEAAqE;AACpF,MAAM,OAAO,EAAE,qEAAqE;AACpF,MAAM,QAAQ,EAAE,+DAA+D;AAC/E,MAAM,QAAQ,EAAE,uFAAuF;AACvG,MAAM,SAAS,EAAE,iCAAiC;AAClD,MAAM,SAAS,EAAE,yDAAyD;AAC1E,MAAM,IAAI,EAAE,kDAAkD;AAC9D,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,kDAAkD;AAChE,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,8DAA8D;AAC5E,MAAM,OAAO,EAAE,kDAAkD;AACjE,MAAM,OAAO,EAAE,sCAAsC;AACrD,MAAM,OAAO,EAAE,kDAAkD;AACjE,MAAM,IAAI,EAAE,0BAA0B;AACtC,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,oBAAoB;AACjC,OAAO;AACP,MAAM,SAAS,EAAE;AACjB,QAAQ,GAAG,EAAE,4CAA4C;AACzD,QAAQ,GAAG,EAAE,4CAA4C;AACzD,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,4CAA4C;AACzD,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,wDAAwD;AACrE,QAAQ,GAAG,EAAE,4CAA4C;AACzD,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,OAAO;AACP,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,QAAQ,EAAE,iFAAiF;AACjG,MAAM,QAAQ,EAAE,+DAA+D;AAC/E,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,2EAA2E;AAC1F,MAAM,OAAO,EAAE,6FAA6F;AAC5G,MAAM,MAAM,EAAE,mDAAmD;AACjE,MAAM,WAAW,EAAE,wDAAwD;AAC3E,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,2EAA2E;AAC1F,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,cAAc,EAAE,yGAAyG;AAC/H,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,6FAA6F;AAC5G,MAAM,OAAO,EAAE,2EAA2E;AAC1F,MAAM,WAAW,EAAE,wDAAwD;AAC3E,MAAM,MAAM,EAAE,mDAAmD;AACjE,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,kDAAkD;AAC9D,MAAM,QAAQ,EAAE,2BAA2B;AAC3C,MAAM,KAAK,EAAE,8CAA8C;AAC3D,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,0BAA0B;AACtC,MAAM,IAAI,EAAE,kIAAkI;AAC9I,MAAM,IAAI,EAAE,4HAA4H;AACxI,MAAM,WAAW,EAAE,kCAAkC;AACrD,MAAM,SAAS,EAAE,yFAAyF;AAC1G,MAAM,SAAS,EAAE,mFAAmF;AACpG,MAAM,kBAAkB,EAAE,kZAAkZ;AAC5a,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,8FAA8F;AAC3G,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,8FAA8F;AAC3G,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,sCAAsC;AACnD,MAAM,OAAO,EAAE,oBAAoB;AACnC,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,KAAK,EAAE,6FAA6F;AAC1G,MAAM,KAAK,EAAE,8FAA8F;AAC3G,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,sKAAsK;AACvL,MAAM,MAAM,EAAE,kDAAkD;AAChE,MAAM,OAAO,EAAE,kDAAkD;AACjE,MAAM,QAAQ,EAAE,8DAA8D;AAC9E,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,YAAY,EAAE,gHAAgH;AACpI,MAAM,sBAAsB,EAAE,kIAAkI;AAChK,MAAM,oBAAoB,EAAE,kIAAkI;AAC9J,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,mDAAmD;AACpE,MAAM,aAAa,EAAE,oEAAoE;AACzF,MAAM,WAAW,EAAE,sCAAsC;AACzD,MAAM,WAAW,EAAE,kDAAkD;AACrE,MAAM,OAAO,EAAE,sCAAsC;AACrD,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,sCAAsC;AAClD,MAAM,QAAQ,EAAE,cAAc;AAC9B,MAAM,MAAM,EAAE,4CAA4C;AAC1D,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,mDAAmD;AACpE,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,6FAA6F;AAC5G,MAAM,MAAM,EAAE,mDAAmD;AACjE,MAAM,MAAM,EAAE,CAAC,wCAAwC,EAAE,wCAAwC,CAAC;AAClG,MAAM,iBAAiB,EAAE,gHAAgH;AACzI,MAAM,eAAe,EAAE,8CAA8C;AACrE,MAAM,gBAAgB,EAAE,+GAA+G;AACvI,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,wDAAwD;AACrE,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,4CAA4C;AACzD,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,gCAAgC;AACzD,MAAM,gBAAgB,EAAE,0BAA0B;AAClD,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,oGAAoG;AACrH,MAAM,UAAU,EAAE,0GAA0G;AAC5H,MAAM,SAAS,EAAE,iJAAiJ;AAClK,KAAK;AACL,GAAG;AACH,CAAC;;;;"}