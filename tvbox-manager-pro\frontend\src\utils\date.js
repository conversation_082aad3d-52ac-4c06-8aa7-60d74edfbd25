import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

// 扩展dayjs插件
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

/**
 * 格式化日期
 * @param {string|Date} date 日期
 * @param {string} format 格式
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return '-'
  return dayjs(date).format(format)
}

/**
 * 格式化相对时间
 * @param {string|Date} date 日期
 * @returns {string} 相对时间字符串
 */
export function formatRelativeTime(date) {
  if (!date) return '-'
  return dayjs(date).fromNow()
}

/**
 * 格式化日期为简短格式
 * @param {string|Date} date 日期
 * @returns {string} 简短日期字符串
 */
export function formatDateShort(date) {
  if (!date) return '-'
  const now = dayjs()
  const target = dayjs(date)
  
  if (now.isSame(target, 'day')) {
    return target.format('HH:mm')
  } else if (now.isSame(target, 'year')) {
    return target.format('MM-DD HH:mm')
  } else {
    return target.format('YYYY-MM-DD')
  }
}

/**
 * 判断日期是否为今天
 * @param {string|Date} date 日期
 * @returns {boolean} 是否为今天
 */
export function isToday(date) {
  if (!date) return false
  return dayjs().isSame(dayjs(date), 'day')
}

/**
 * 判断日期是否为昨天
 * @param {string|Date} date 日期
 * @returns {boolean} 是否为昨天
 */
export function isYesterday(date) {
  if (!date) return false
  return dayjs().subtract(1, 'day').isSame(dayjs(date), 'day')
}

/**
 * 获取日期范围
 * @param {string} type 类型：today, yesterday, week, month
 * @returns {Array} [开始日期, 结束日期]
 */
export function getDateRange(type) {
  const now = dayjs()
  
  switch (type) {
    case 'today':
      return [now.startOf('day'), now.endOf('day')]
    case 'yesterday':
      const yesterday = now.subtract(1, 'day')
      return [yesterday.startOf('day'), yesterday.endOf('day')]
    case 'week':
      return [now.startOf('week'), now.endOf('week')]
    case 'month':
      return [now.startOf('month'), now.endOf('month')]
    case 'last7days':
      return [now.subtract(6, 'day').startOf('day'), now.endOf('day')]
    case 'last30days':
      return [now.subtract(29, 'day').startOf('day'), now.endOf('day')]
    default:
      return [now.startOf('day'), now.endOf('day')]
  }
}

/**
 * 计算两个日期之间的差值
 * @param {string|Date} date1 日期1
 * @param {string|Date} date2 日期2
 * @param {string} unit 单位：day, hour, minute, second
 * @returns {number} 差值
 */
export function dateDiff(date1, date2, unit = 'day') {
  if (!date1 || !date2) return 0
  return dayjs(date1).diff(dayjs(date2), unit)
}

/**
 * 添加时间
 * @param {string|Date} date 日期
 * @param {number} amount 数量
 * @param {string} unit 单位
 * @returns {string} 新日期
 */
export function addTime(date, amount, unit = 'day') {
  if (!date) return null
  return dayjs(date).add(amount, unit).toISOString()
}

/**
 * 减去时间
 * @param {string|Date} date 日期
 * @param {number} amount 数量
 * @param {string} unit 单位
 * @returns {string} 新日期
 */
export function subtractTime(date, amount, unit = 'day') {
  if (!date) return null
  return dayjs(date).subtract(amount, unit).toISOString()
}
