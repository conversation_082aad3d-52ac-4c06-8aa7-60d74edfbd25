#!/usr/bin/env python3
"""
检查配置内容
"""
import sqlite3
import os
import json

def check_config_content():
    """检查配置内容"""
    db_path = "data/tvbox.db"
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查接口1的配置
        cursor.execute("""
            SELECT id, name, 
                   LENGTH(config_content) as config_len,
                   LENGTH(localized_config) as localized_len,
                   SUBSTR(config_content, 1, 200) as config_preview,
                   SUBSTR(localized_config, 1, 200) as localized_preview
            FROM interface_sources 
            WHERE id = 1
        """)
        
        result = cursor.fetchone()
        if result:
            print(f"接口ID: {result[0]}")
            print(f"接口名称: {result[1]}")
            print(f"原始配置长度: {result[2]}")
            print(f"本地化配置长度: {result[3]}")
            print(f"原始配置预览: {result[4]}")
            print(f"本地化配置预览: {result[5]}")
            
            # 解析配置内容，查看spider字段
            cursor.execute("SELECT config_content, localized_config FROM interface_sources WHERE id = 1")
            config_result = cursor.fetchone()
            
            if config_result[0]:
                try:
                    config_data = json.loads(config_result[0])
                    print(f"\n原始配置中的spider: {config_data.get('spider', 'N/A')}")
                    
                    # 检查sites中的api字段
                    sites = config_data.get('sites', [])
                    if sites:
                        print(f"原始配置中第一个site的api: {sites[0].get('api', 'N/A')}")
                except Exception as e:
                    print(f"解析原始配置失败: {e}")
            
            if config_result[1]:
                try:
                    localized_data = json.loads(config_result[1])
                    print(f"本地化配置中的spider: {localized_data.get('spider', 'N/A')}")
                    
                    # 检查sites中的api字段
                    sites = localized_data.get('sites', [])
                    if sites:
                        print(f"本地化配置中第一个site的api: {sites[0].get('api', 'N/A')}")
                except Exception as e:
                    print(f"解析本地化配置失败: {e}")
        else:
            print("未找到接口1")
            
    except Exception as e:
        print(f"检查失败: {str(e)}")
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    check_config_content()
