#!/usr/bin/env python3
"""
测试下载功能的脚本
"""
import asyncio
import aiohttp
import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.append('.')

from app.services.localization_service import LocalizationService

async def test_download():
    """测试下载功能"""
    service = LocalizationService()
    
    # 测试下载一个简单的文件
    test_url = "https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/drpy2.js"
    output_path = Path("test_download.js")
    
    print(f"测试下载: {test_url}")
    print(f"输出路径: {output_path}")
    
    try:
        result = await service.download_file(test_url, output_path)
        print(f"下载结果: {result}")
        
        if result['success']:
            print(f"文件大小: {output_path.stat().st_size} bytes")
            # 读取文件前几行
            with open(output_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read(200)
                print(f"文件内容预览: {content[:100]}...")
        else:
            print(f"下载失败: {result.get('error', '未知错误')}")
            
    except Exception as e:
        print(f"异常: {str(e)}")
    finally:
        # 清理测试文件
        if output_path.exists():
            output_path.unlink()

if __name__ == "__main__":
    asyncio.run(test_download())
