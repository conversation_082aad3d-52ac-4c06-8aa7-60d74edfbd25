{"spider": "./config/jar/jar/250410.jar", "wallpaper": "https://深色壁纸.xxooo.cf/", "sites": [{"key": "豆瓣", "name": "豆瓣🌕lic10.cn余生的客栈", "type": 3, "api": "csp_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indexs": 1, "searchable": 0, "quickSearch": 0, "changeable": 0}, {"key": "抠搜", "name": "🍄抠抠┃搜搜", "type": 3, "api": "csp_KkSsGuard", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": {"Cloud-drive": "tvfan/Cloud-drive.txt", "from": "4k|auto"}}, {"key": "UC", "name": "🌈优汐┃搜搜", "type": 3, "api": "csp_UuSsGuard", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": {"Cloud-drive": "tvfan/Cloud-drive.txt", "from": "4k|auto"}}, {"key": "玩偶", "name": "👽玩偶哥哥┃4K弹幕", "type": 3, "api": "csp_WoGGGuard", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": {"Cloud-drive": "tvfan/Cloud-drive.txt", "from": "4k|auto", "siteUrl": "https://www.wogg.com/", "danMu": "弹"}}, {"key": "TV121", "name": "TV121.js", "type": 3, "api": "./config/api/drpy2.min.js", "ext": "./config/js/TV121.js"}, {"key": "alllive", "name": "直播┃LIVE", "type": 3, "api": "csp_AllliveGuard", "playerType": 2, "searchable": 0, "quickSearch": 0, "changeable": 0}, {"key": "荐片", "name": "🐭荐片┃P2P", "type": 3, "api": "csp_<PERSON><PERSON><PERSON><PERSON>", "timeout": 15, "playerType": 2, "searchable": 1, "quickSearch": 1, "changeable": 0}, {"key": "白白", "name": "🐟白白┃秒播", "type": 3, "api": "csp_SbaibaiGuard", "timeout": 15, "playerType": 2, "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "厂长", "name": "📔厂长┃不卡", "type": 3, "api": "csp_NewCzGuard", "timeout": 15, "playerType": 2, "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "原创", "name": "☀原创┃不卡", "type": 3, "api": "csp_YCyzGuard", "timeout": 30, "playerType": 1, "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "苹果", "name": "🍎苹果┃不卡", "type": 3, "api": "csp_LiteAppleGuard", "timeout": 15, "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "糯米", "name": "🍓糯米┃秒播", "type": 3, "api": "csp_NmyswvGuard", "timeout": 15, "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "文采", "name": "💮文采┃秒播", "type": 3, "api": "csp_JpysGuard", "timeout": 15, "playerType": 2, "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "溢彩", "name": "💡溢彩┃秒播", "type": 3, "api": "csp_AppSxGuard", "timeout": 15, "searchable": 1, "quickSearch": 1, "changeable": 1, "ext": "rfOb1uAWbkRHp7hdxprG9un3+TfN183v1zIyaYDoDAIaLw5L8Dp8+v88LrEL3dBzrmWbdMBX0WNm7HtkQuw0AIzUurGBVyPqCKzDmbriATuukhctJlsLo8KxCw=="}, {"key": "Lib", "name": "🌟立播┃秒播", "type": 3, "api": "csp_LibvioGuard", "timeout": 15, "searchable": 1, "quickSearch": 1, "changeable": 1, "ext": {"Cloud-drive": "tvfan/Cloud-drive.txt", "from": "4k|auto"}}, {"key": "zxzj", "name": "🍊在线┃秒播", "type": 3, "api": "csp_ZxzjGuard", "timeout": 15, "searchable": 1, "quickSearch": 1, "changeable": 1, "ext": "https://www.zxzjhd.com/"}, {"key": "比特", "name": "🍄比特┃手机", "type": 3, "api": "csp_BttwooGuard", "timeout": 15, "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "低端", "name": "⏮️低端┃外剧", "type": 3, "api": "csp_DdrkGuard", "timeout": 15, "playerType": "2", "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "萌米", "name": "👀萌米┃多线", "type": 3, "api": "csp_AppTTGuard", "timeout": 15, "playerType": 2, "searchable": 1, "quickSearch": 1, "changeable": 1, "ext": "uqGL1bNENEIVq+dC1p/Y9uWjuA=="}, {"key": "热播", "name": "📺热播┃多线", "type": 3, "api": "csp_AppTTGuard", "timeout": 15, "playerType": 2, "searchable": 1, "quickSearch": 1, "changeable": 1, "ext": "uqGL1bNENExT7/hGxpSE5qU="}, {"key": "兄弟", "name": "🍊水星┃多线", "type": 3, "api": "csp_AppSxGuard", "timeout": 15, "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "rfOb1uAWbkRHp7hdxprG9un3+SLP183q3ik3cJDiAwlFdF8L6SIvrvc9LrpTyIg76T7QJZdEkWNj43wiSaA0TJyQpu2IF2jsSLWFx7WkAmG40hFxJ1tI+Jf+EVG8DtoDRcNi+TtVGULnWrSz3EWnVcxR3EJhXnrwYWe1kJtNW5txuHAO"}, {"key": "即看", "name": "🐻即看┃多线", "type": 3, "api": "csp_AppSxGuard", "timeout": 15, "searchable": 1, "quickSearch": 0, "changeable": 0, "ext": "rfOX1voDIQhH8epBwtCFsub1+2maloq8lmJuL821WUsZJAZft2UtrrwhKK5Zxt1toWyFctBUmThhuDAjVuU="}, {"key": "欢视", "name": "👓欢视┃多线", "type": 3, "api": "csp_AppTTGuard", "timeout": 15, "playerType": 2, "searchable": 1, "quickSearch": 1, "changeable": 1, "ext": "uqGL1bNENExT9fFAy5mE5qU="}, {"key": "奥特", "name": "🏝奥特┃多线", "type": 3, "api": "csp_<PERSON><PERSON>Guard", "timeout": 15, "searchable": 1, "quickSearch": 1, "changeable": 1, "ext": "https://auete.com/"}, {"key": "4KHDR", "name": "🧲世界磁力┃4KHDR", "type": 3, "searchable": 1, "quickSearch": 1, "changeable": 0, "api": "./config/api/drpy2.min.js", "ext": "./config/js/4khdr.js"}, {"key": "新6V", "name": "🧲新6V┃磁力", "type": 3, "api": "csp_SixVGuard", "timeout": 10, "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": "https://www.xb6v.com/"}, {"key": "js_6V", "name": "✡️┃六维┃磁力", "type": 3, "api": "https://jihulab.com/iduoduo/xduo/-/raw/main/libs/drpy2.min.js", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "https://jihulab.com/iduoduo/xduo/-/raw/main/libs/6V.js"}, {"key": "XYQH_电影港", "name": "🛳┃港口┃磁力", "type": 3, "playerType": 1, "api": "csp_XYQHiker", "jar": "https://jihulab.com/iduoduo/xduo/-/raw/main/spider.txt;md5;A21EA6F5DD3B5F922B3A7F54D3BD0F16", "ext": "https://agit.ai/leevi/duo/raw/branch/master/libs/港口.json"}, {"key": "js_酷吧", "name": "😎┃酷吧┃磁力", "type": 3, "api": "https://jihulab.com/iduoduo/xduo/-/raw/main/libs/drpy2.min.js", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "https://jihulab.com/iduoduo/xduo/-/raw/main/libs/kuba.js"}, {"key": "纸条弹幕版", "name": "纸条弹幕┃🍭", "type": 3, "api": "csp_<PERSON>", "searchable": 1, "filterable": 1, "changeable": 1, "ext": {"token": "http://127.0.0.1:9978/file/lxhfans/token.txt+4k|auto|fhd", "danmu": true}, "jar": "./jar/danmu.jar"}, {"key": "易搜弹幕版", "name": "易搜弹幕┃🍭", "type": 3, "api": "csp_<PERSON>o", "searchable": 1, "filterable": 0, "changeable": 1, "ext": {"token": "http://127.0.0.1:9978/file/lxhfans/token.txt+4k|auto|fhd", "danmu": true}, "jar": "./config/jar/danmu.jar"}, {"key": "盘Se弹幕版", "name": "云搜弹幕┃🍭", "type": 3, "api": "csp_PanSearch", "searchable": 1, "filterable": 0, "changeable": 1, "ext": {"token": "http://127.0.0.1:9978/file/lxhfans/token.txt+4k|auto|fhd", "danmu": true}, "jar": "./config/jar/danmu.jar"}, {"key": "盘搜弹幕版", "name": "盘搜弹幕┃🍭", "type": 3, "api": "csp_Pan<PERSON>ou", "searchable": 1, "filterable": 0, "changeable": 1, "ext": {"token": "http://127.0.0.1:9978/file/lxhfans/token.txt+4k|auto|fhd", "danmu": true}, "jar": "./config/jar/danmu.jar"}, {"key": "csp_XBPQ_6V", "name": "宝宝┃4K", "type": 3, "api": "csp_XBPQ", "searchable": 1, "quickSearch": 1, "filterable": 1, "jar": "./config/jar/vip.jar", "ext": "./h/6Vip.json"}, {"key": "Gitcafe", "name": "咖咖┃4K", "type": 3, "api": "csp_Paper", "searchable": 1, "quickSearch": 1, "filterable": 1, "jar": "./config/jar/HeChengChaXiu.jar", "ext": "b4242bebe6f144d3aa6a2cd842ac65aa"}, {"key": "影巢", "name": "巢巢┃4K", "type": 3, "api": "csp_Hdhive", "searchable": 1, "quickSearch": 1, "filterable": 0, "jar": "./config/jar/xry.jar", "ext": "http://127.0.0.1:9978/file/lxhfans/token.txt"}, {"key": "Mm", "name": "毛毛┃4K", "type": 3, "api": "csp_AppYsV2", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "http://ys.qtw.asia/lvdou_api.php/v1.vod"}, {"key": "drpy_js_网飞.TV", "name": "🛜 影视 | 网飞.TV[js]", "type": 3, "api": "./config/api/drpy2.min.js", "ext": "./config/js/网飞.TV.js"}, {"key": "mf", "name": "🦆┃耐菲┃影视", "type": 3, "api": "./config/api/drpy2.min.js", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "./config/js/鸭奈飞.js"}, {"key": "drpy_js_奈飞中文", "name": "影视 | 奈飞中文[js]", "type": 3, "api": "./config/api/drpy2.min.js", "ext": "./config/js/奈飞中文.js"}, {"key": "drpy_js_奈飞狗[V2]", "name": "影视 | 奈飞狗[V2]", "type": 3, "api": "./config/api/drpy2.min.js", "ext": "./config/js/奈飞狗[V2].js"}, {"key": "drpy_js_思古影视[V2]", "name": "影视 | 思古影视[V2]", "type": 3, "api": "./config/api/drpy2.min.js", "ext": "./config/js/思古影视[V2].js"}, {"key": "haiwaikan", "name": "🌊 海外看┃此接口须有魔法上网", "type": 1, "api": "https://haiwaikan.com/api.php/provide/vod", "searchable": 1, "changeable": 1, "categories": ["日本动漫", "国产动漫", "欧美动漫", "国产剧", "韩剧", "日剧", "台剧", "泰剧", "港剧", "欧美剧", "动画电影", "韩国综艺", "国产综艺", "日本综艺", "欧美综艺", "冒险片", "剧情片", "动作片", "同性片", "喜剧片", "奇幻片", "恐怖片", "悬疑片", "惊悚片", "战争片", "歌舞片", "灾难片", "爱情片", "犯罪片", "科幻片", "纪录片", "经典片"]}, {"key": "短剧", "name": "🌈上头┃短剧", "type": 3, "api": "csp_Djtt", "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "Dm84", "name": "🚌巴士┃动漫", "type": 3, "api": "csp_Dm84Guard", "timeout": 10, "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "Ysj", "name": "🎀异界┃动漫", "type": 3, "api": "csp_<PERSON><PERSON>j<PERSON><PERSON>", "timeout": 15, "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "Anime1", "name": "🐾日本┃动漫", "type": 3, "api": "csp_Anime1Guard", "timeout": 15, "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "926", "name": "⚽926┃看球", "type": 3, "api": "csp_kanqiu926Guard", "timeout": 15, "searchable": 0, "changeable": 0, "style": {"type": "list"}}, {"key": "88", "name": "⚽88┃看球", "type": 3, "api": "csp_Sir88Guard", "timeout": 15, "searchable": 0, "changeable": 0, "style": {"type": "list"}}, {"key": "看球", "name": "⚽看球┃直播", "type": 3, "api": "csp_<PERSON><PERSON><PERSON><PERSON>", "timeout": 15, "searchable": 0, "changeable": 0, "style": {"type": "list"}}, {"key": "csp_qiumi", "name": "⚽ Jrs┃球迷", "type": 3, "api": "./config/api/drpy2.min.js", "ext": "./config/js/jrk.js", "style": {"type": "list"}, "timeout": 10, "searchable": 0, "quickSearch": 0, "changeable": 0}, {"key": "csp_310直播", "name": "⚽310┃看球", "type": 3, "api": "./config/api/drpy2.min.js", "ext": "./config/js/310直播.js", "style": {"type": "rect", "ratio": 1}, "searchable": 0, "quickSearch": 0, "changeable": 0}, {"key": "csp_XPath_企鹅体育", "name": "🐧企鹅┃体育", "type": 3, "api": "csp_XPath", "style": {"type": "rect", "ratio": 1.597}, "searchable": 0, "quickSearch": 0, "changeable": 0, "ext": "./config/json/企鹅直播.json"}, {"key": "虎牙直播js", "name": "🐯虎牙┃直播", "type": 3, "api": "https://fanty.run.goorm.site/ext/js/drpy2.min.js", "ext": "https://fanty.run.goorm.site/ext/虎牙直播.js", "style": {"type": "list"}, "searchable": 0, "quickSearch": 0, "changeable": 0}, {"key": "csp_XYQBiu_斗鱼", "name": "🐟斗鱼┃直播", "type": 3, "api": "https://fanty.run.goorm.site/ext/js/drpy2.min.js", "ext": "https://fanty.run.goorm.site/ext/斗鱼直播.js", "style": {"type": "list"}, "searchable": 0, "quickSearch": 0, "changeable": 0}, {"key": "drpy_js_喜马拉雅", "name": "📗 听书 | 喜马拉雅[js]", "type": 3, "api": "./config/api/drpy2.min.js", "ext": "./config/js/喜马拉雅.js", "playerType": "2"}, {"key": "有声小说js", "name": "🎧有声┃小说", "type": 3, "api": "./config/api/drpy2.min.js", "ext": "./config/js/有声小说吧.js", "style": {"type": "rect", "ratio": 1}, "searchable": 0, "quickSearch": 0, "changeable": 0}, {"key": "聚短视频js", "name": "📽️聚短┃视频", "type": 3, "api": "https://agit.ai/fantaiying/fty/raw/branch/master/ext/drpy2.min.js", "ext": "https://agit.ai/fantaiying/dr_py/raw/branch/main/js/短视频.js", "searchable": 0, "quickSearch": 0, "changeable": 0}, {"key": "YpanSo", "name": "🐟盘她┃三盘", "type": 3, "api": "csp_YpanSoGuard", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": {"Cloud-drive": "tvfan/Cloud-drive.txt", "from": "4k|auto"}}, {"key": "xzso", "name": "👻盘它┃三盘", "type": 3, "api": "csp_<PERSON>zsoGuard", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": {"Cloud-drive": "tvfan/Cloud-drive.txt", "from": "4k|auto"}}, {"key": "米搜", "name": "🦋米搜┃夸父", "type": 3, "api": "csp_MIPanSoGuard", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": {"Cloud-drive": "tvfan/Cloud-drive.txt", "from": "4k|auto"}}, {"key": "夸搜", "name": "😻夸搜┃夸父", "type": 3, "api": "csp_PanSearchGuard", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": {"pan": "quark", "Cloud-drive": "tvfan/Cloud-drive.txt", "from": "4k|auto"}}, {"key": "<PERSON><PERSON>", "name": "🙀盘搜┃阿狸", "type": 3, "api": "csp_PanSearchGuard", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": {"Cloud-drive": "tvfan/Cloud-drive.txt", "from": "4k|auto"}}, {"key": "YiSo", "name": "😹易搜┃阿狸", "type": 3, "api": "csp_<PERSON><PERSON><PERSON>", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": {"Cloud-drive": "tvfan/Cloud-drive.txt", "from": "4k|auto"}}, {"key": "push_agent", "name": "🛴手机┃推送", "type": 3, "api": "csp_<PERSON><PERSON><PERSON><PERSON>", "searchable": 0, "quickSearch": 0, "ext": {"Cloud-drive": "tvfan/Cloud-drive.txt", "from": "4k|auto"}}, {"key": "csp_<PERSON>bys", "name": "🛫泥巴┃飞", "type": 3, "api": "csp_Ni<PERSON>i", "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "csp_trj", "name": "🛫唐人街┃飞", "type": 3, "api": "csp_<PERSON>", "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "Bili", "name": "🅱哔哩哔哩┃合集", "type": 3, "api": "csp_<PERSON><PERSON>", "style": {"type": "rect", "ratio": 1.597}, "searchable": 1, "quickSearch": 0, "changeable": 0, "ext": "./config/json/bilibili.json"}, {"key": "<PERSON><PERSON><PERSON>", "name": "🅱哔哩哔哩┃歌曲", "type": 3, "api": "csp_<PERSON><PERSON>", "style": {"type": "rect", "ratio": 1.597}, "searchable": 1, "quickSearch": 0, "changeable": 0, "ext": "./config/js/biliych.json"}, {"key": "dr_兔小贝", "name": "📚儿童┃启蒙", "type": 3, "api": "./config/api/drpy2.min.js", "ext": "./config/js/%E5%85%94%E5%B0%8F%E8%B4%9D.js", "style": {"type": "rect", "ratio": 1.597}, "searchable": 0, "quickSearch": 0, "changeable": 0}, {"key": "少儿教育", "name": "📚少儿┃教育", "type": 3, "api": "csp_<PERSON><PERSON><PERSON><PERSON>", "style": {"type": "rect", "ratio": 1.597}, "searchable": 0, "quickSearch": 0, "changeable": 0, "ext": "./config/json/少儿教育.json"}, {"key": "小学课堂", "name": "📚小学┃课堂", "type": 3, "api": "csp_<PERSON><PERSON><PERSON><PERSON>", "style": {"type": "rect", "ratio": 1.597}, "searchable": 0, "quickSearch": 0, "changeable": 0, "ext": "./config/js/小学课堂.json"}, {"key": "初中课堂", "name": "📚初中┃课堂", "type": 3, "api": "csp_<PERSON><PERSON>", "style": {"type": "rect", "ratio": 1.597}, "searchable": 0, "quickSearch": 0, "changeable": 0, "ext": "./config/json/初中课堂.json"}, {"key": "高中教育", "name": "📚高中┃课堂", "type": 3, "api": "csp_<PERSON><PERSON><PERSON><PERSON>", "style": {"type": "rect", "ratio": 1.597}, "searchable": 0, "quickSearch": 0, "changeable": 0, "ext": "./config/json/高中课堂.json"}, {"key": "ext_live_protocol", "name": "导航 https://ysys.lic10.cn", "type": 3, "api": "csp_XPath", "searchable": 0, "quickSearch": 0, "changeable": 0}, {"key": "cc", "name": "请勿相信视频中任何广告", "type": 3, "api": "csp_XPath", "searchable": 0, "quickSearch": 0}], "parses": [{"name": "聚合", "type": 3, "url": "Demo"}, {"name": "余生客栈", "type": 1, "url": "http://api.888484.xyz/神秘哥哥/super.php?v=", "ext": {"flag": ["qq", "腾讯", "qiyi", "爱奇艺", "奇艺", "youku", "优酷", "tucheng", "sohu", "搜狐", "letv", "乐视", "mgtv", "芒果", "tnmb", "seven", "yzm", "<PERSON><PERSON><PERSON>", "R<PERSON><PERSON><PERSON><PERSON>", "bilibili", "1905", "xinvip", "XAL", "qiqi", "XALS", "Yu<PERSON>i-vip"]}}, {"name": "余生影视", "type": 1, "url": "https://jx.xmflv.com/?url=", "ext": {"flag": ["qq", "腾讯", "qiyi", "爱奇艺", "奇艺", "youku", "优酷", "mgtv", "芒果", "imgo", "letv", "乐视", "pptv", "PPTV", "sohu", "bilibili", "哔哩哔哩", "哔哩"], "header": {"User-Agent": "okhttp/4.1.0"}}}], "doh": [{"name": "Google", "url": "https://dns.google/dns-query", "ips": ["*******", "*******"]}, {"name": "Cloudflare", "url": "https://cloudflare-dns.com/dns-query", "ips": ["*******", "*******", "2606:4700:4700::1111", "2606:4700:4700::1001"]}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://dns.adguard.com/dns-query", "ips": ["*************", "*************"]}, {"name": "DNSWatch", "url": "https://resolver2.dns.watch/dns-query", "ips": ["************", "************"]}, {"name": "Quad9", "url": "https://dns.quad9.net/dns-quer", "ips": ["*******", "***************"]}], "rules": [{"name": "hwk", "hosts": ["haiwaikan"], "regex": ["10.0099", "10.3333", "16.0599", "8.1748", "10.85"]}, {"name": "yqk", "hosts": ["yqk88"], "regex": ["18.4", "15.1666"]}, {"name": "sn", "hosts": ["suonizy"], "regex": ["15.1666", "15.2666"]}, {"name": "bf", "hosts": ["bfzy"], "regex": ["#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:3,[\\s\\S]*?#EXT-X-DISCONTINUITY"]}, {"name": "xx", "hosts": ["aws.ulivetv.net"], "regex": ["#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:8,[\\s\\S]*?#EXT-X-DISCONTINUITY"]}, {"name": "lz", "hosts": ["vip.lz", "hd.lz", "v.cdnlz1", "v.cdnlz"], "regex": ["18.5333"]}, {"name": "非凡", "hosts": ["vip.ffzy", "hd.ffzy"], "regex": ["25.0666"]}, {"name": "hs", "hosts": ["huoshan.com"], "regex": ["item_id="]}, {"name": "dy", "hosts": ["douyin.com"], "regex": ["is_play_url="]}, {"name": "nm", "hosts": ["toutiaovod.com"], "regex": ["video/tos/cn"]}, {"name": "cl", "hosts": ["magnet"], "regex": ["最 新", "直 播", "更 新"]}, {"host": "*", "rule": ["default.365yg.com"]}, {"host": "dyxs20.com", "rule": [".m3u8"]}, {"host": "www.agemys.cc", "rule": ["cdn-tos", "obj/tos-cn"]}, {"host": "zjmiao.com", "rule": ["play.videomiao.vip/API.php", "time="]}, {"host": "www.sharenice.net", "rule": ["http.*?/play.{0,3}\\?[^url]{2,8}=.*"]}, {"host": "www.sharenice.net", "rule": ["qianpailive.com", "vid="]}, {"host": "*", "rule": ["douyin.com/aweme", "video_id="]}, {"host": "*", "rule": ["huoshan.com", "/item/video/"]}, {"host": "*", "rule": ["http((?!http).){12,}?\\.(m3u8|mp4|flv|avi|mkv|rm|wmv|mpg|m4a)\\?.*"]}, {"host": "*", "rule": ["http((?!http).){12,}\\.(m3u8|mp4|flv|avi|mkv|rm|wmv|mpg|m4a)"]}], "flags": ["youku", "qq", "<PERSON><PERSON><PERSON>", "qiyi", "letv", "sohu", "tudou", "pptv", "mgtv", "wasu", "bilibili", "csm3u8"], "lives": [{"name": "电视直播", "type": 0, "url": "http://home.jundie.top:81/Cat/tv/live.txt", "playerType": 1, "ua": "okhttp/3.12.13", "epg": "http://epg.112114.xyz/?ch={name}&date={date}", "logo": "https://epg.112114.xyz/logo/{name}.png"}, {"name": "V4聚合（卡顿请按左┃右键换线）", "type": 0, "url": "https://gh-proxy.net/https://raw.githubusercontent.com/wwb521/live/main/tv.m3u", "playerType": 2, "epg": "http://epg.51zmt.top:8000/api/diyp/?ch={name}&date={date}", "logo": "https://epg.v1.mk/logo/{name}.png"}, {"name": "V6", "type": 0, "url": "https://gh-proxy.net/https://raw.githubusercontent.com/fanmingming/live/refs/heads/main/tv/m3u/ipv6.m3u", "playerType": 2}, {"name": "牛播一", "type": 0, "url": "http://127.0.0.1:9978/proxy?do=饭太硬&type=liveList", "playerType": 2, "epg": "http://epg.51zmt.top:8000/api/diyp/?ch={name}&date={date}", "logo": "https://epg.v1.mk/logo/{name}.png"}, {"name": "平台直播", "type": 0, "url": "http://tv.iill.top/m3u/Live", "ua": "okhttp/3.15", "playerType": 2}]}