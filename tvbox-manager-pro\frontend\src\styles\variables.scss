// 颜色变量
$primary-color: #409EFF;
$success-color: #67C23A;
$warning-color: #E6A23C;
$danger-color: #F56C6C;
$info-color: #909399;

// 文本颜色
$text-color-primary: #303133;
$text-color-regular: #606266;
$text-color-secondary: #909399;
$text-color-placeholder: #C0C4CC;

// 边框颜色
$border-color-base: #DCDFE6;
$border-color-light: #E4E7ED;
$border-color-lighter: #EBEEF5;
$border-color-extra-light: #F2F6FC;

// 背景颜色
$bg-color-white: #FFFFFF;
$bg-color-page: #F2F3F5;
$bg-color-overlay: #FFFFFF;

// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// 字体大小
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-md: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;

// 字体粗细
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 行高
$line-height-xs: 1.2;
$line-height-sm: 1.4;
$line-height-md: 1.5;
$line-height-lg: 1.6;
$line-height-xl: 1.8;

// 圆角
$border-radius-xs: 2px;
$border-radius-sm: 4px;
$border-radius-md: 6px;
$border-radius-lg: 8px;
$border-radius-xl: 12px;
$border-radius-circle: 50%;

// 阴影
$box-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
$box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
$box-shadow-dark: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);

// 过渡动画
$transition-base: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
$transition-fade: opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);
$transition-md-fade: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1), opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);
$transition-border: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
$transition-color: color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

// 层级
$z-index-normal: 1;
$z-index-top: 1000;
$z-index-popper: 2000;

// 断点
$breakpoint-xs: 480px;
$breakpoint-sm: 768px;
$breakpoint-md: 992px;
$breakpoint-lg: 1200px;
$breakpoint-xl: 1920px;

// 组件尺寸
$component-size-large: 40px;
$component-size-default: 32px;
$component-size-small: 24px;

// 侧边栏
$sidebar-width: 210px;
$sidebar-width-collapsed: 64px;

// 顶部导航栏
$navbar-height: 50px;

// 标签页
$tags-view-height: 34px;

// 内容区域
$app-main-padding: 20px;

// 卡片
$card-border-radius: 8px;
$card-padding: 20px;

// 表格
$table-border-color: #EBEEF5;
$table-header-bg: #F5F7FA;
$table-row-hover-bg: #F5F7FA;

// 表单
$form-item-margin-bottom: 18px;
$form-label-font-size: 14px;

// 按钮
$button-border-radius: 4px;
$button-padding-horizontal: 15px;
$button-padding-vertical: 8px;

// 输入框
$input-border-radius: 4px;
$input-padding-horizontal: 11px;
$input-padding-vertical: 8px;

// 暗色模式变量
$dark-bg-color: #141414;
$dark-bg-color-page: #0a0a0a;
$dark-bg-color-overlay: #1d1e1f;
$dark-text-color-primary: #E5EAF3;
$dark-text-color-regular: #CFD3DC;
$dark-text-color-secondary: #A3A6AD;
$dark-border-color-base: #4C4D4F;
$dark-border-color-light: #414243;
$dark-border-color-lighter: #363637;

// 混合器
@mixin clearfix {
  &::after {
    content: "";
    display: table;
    clear: both;
  }
}

@mixin ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin multi-ellipsis($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@mixin button-variant($color, $background, $border) {
  color: $color;
  background-color: $background;
  border-color: $border;
  
  &:hover {
    color: $color;
    background-color: lighten($background, 10%);
    border-color: lighten($border, 10%);
  }
  
  &:active {
    color: $color;
    background-color: darken($background, 10%);
    border-color: darken($border, 10%);
  }
}

// 响应式断点混合器
@mixin respond-to($breakpoint) {
  @if $breakpoint == xs {
    @media (max-width: #{$breakpoint-xs - 1px}) {
      @content;
    }
  }
  @if $breakpoint == sm {
    @media (min-width: #{$breakpoint-xs}) and (max-width: #{$breakpoint-sm - 1px}) {
      @content;
    }
  }
  @if $breakpoint == md {
    @media (min-width: #{$breakpoint-sm}) and (max-width: #{$breakpoint-md - 1px}) {
      @content;
    }
  }
  @if $breakpoint == lg {
    @media (min-width: #{$breakpoint-md}) and (max-width: #{$breakpoint-lg - 1px}) {
      @content;
    }
  }
  @if $breakpoint == xl {
    @media (min-width: #{$breakpoint-lg}) {
      @content;
    }
  }
}
