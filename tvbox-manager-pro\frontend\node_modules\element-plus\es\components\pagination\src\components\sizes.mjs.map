{"version": 3, "file": "sizes.mjs", "sources": ["../../../../../../../packages/components/pagination/src/components/sizes.ts"], "sourcesContent": ["import { buildProps, definePropType, mutable } from '@element-plus/utils'\nimport { componentSizes } from '@element-plus/constants'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type Sizes from './sizes.vue'\n\nexport const paginationSizesProps = buildProps({\n  pageSize: {\n    type: Number,\n    required: true,\n  },\n  pageSizes: {\n    type: definePropType<number[]>(Array),\n    default: () => mutable([10, 20, 30, 40, 50, 100] as const),\n  },\n  popperClass: {\n    type: String,\n  },\n  disabled: Boolean,\n  teleported: Boolean,\n  size: {\n    type: String,\n    values: componentSizes,\n  },\n  appendSizeTo: String,\n} as const)\n\nexport type PaginationSizesProps = ExtractPropTypes<typeof paginationSizesProps>\nexport type PaginationSizesPropsPublic = __ExtractPublicPropTypes<\n  typeof paginationSizesProps\n>\n\nexport type SizesInstance = InstanceType<typeof Sizes> & unknown\n"], "names": [], "mappings": ";;;;AAEY,MAAC,oBAAoB,GAAG,UAAU,CAAC;AAC/C,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,MAAM,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;AACrD,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,UAAU,EAAE,OAAO;AACrB,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,cAAc;AAC1B,GAAG;AACH,EAAE,YAAY,EAAE,MAAM;AACtB,CAAC;;;;"}