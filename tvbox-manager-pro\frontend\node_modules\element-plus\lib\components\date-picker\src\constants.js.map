{"version": 3, "file": "constants.js", "sources": ["../../../../../../packages/components/date-picker/src/constants.ts"], "sourcesContent": ["import type { Injection<PERSON><PERSON>, SetupContext } from 'vue'\nimport type { UseNamespaceReturn } from '@element-plus/hooks'\n\ninterface DatePickerContext {\n  slots: SetupContext['slots']\n  pickerNs: UseNamespaceReturn\n}\n\nexport const ROOT_PICKER_INJECTION_KEY: InjectionKey<DatePickerContext> =\n  Symbol()\n\nexport const ROOT_PICKER_IS_DEFAULT_FORMAT_INJECTION_KEY = 'ElIsDefaultFormat'\n"], "names": [], "mappings": ";;;;AAAY,MAAC,yBAAyB,GAAG,MAAM,GAAG;AACtC,MAAC,2CAA2C,GAAG;;;;;"}