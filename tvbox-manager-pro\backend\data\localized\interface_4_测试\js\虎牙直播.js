var rule = {
    title:'虎牙直播',
    host:'https://www.huya.com',
    homeUrl:'/cache.php?m=LiveList&do=getLiveListByPage&gameId=2168&tagAll=0&page=1',//用于"分类获取"和"推荐获取"
    url:'/cache.php?m=LiveList&do=getLiveListByPage&gameId=fyfilter&tagAll=0&page=fypage',
    class_name:'娱乐&网游&单机&手游',
    class_url:'8&1&2&3',
    // detailUrl:'https://m.huya.com/fyid',//二级详情拼接链接(json格式用)
    detailUrl:'http://live.yj1211.work/api/live/getRoomInfo?uid=&platform=huya&roomId=fyid',//二级详情拼接链接(json格式用)
    filterable: 1,
    filter_url: '{{fl.cateId}}',
    filter_def:{
        8:{cateId:'2135'},
        1:{cateId:'1'},
        2:{cateId:'1732'},
        3:{cateId:'2336'}
    },
    filter:{
        "8":[{"key":"cateId","name":"分类","value":[{"n":"一起看","v":"2135"},{"n":"星秀","v":"1663"},{"n":"户外","v":"2165"},{"n":"二次元","v":"2633"},{"n":"虚拟偶像","v":"6055"},{"n":"旅游","v":"6791"},{"n":"娱乐天地","v":"100022"},{"n":"交友","v":"4079"},{"n":"组队","v":"5367"},{"n":"吃喝玩乐","v":"100044"},{"n":"原创","v":"6861"},{"n":"虎牙文化","v":"4089"},{"n":"体育","v":"2356"},{"n":"虎牙地方","v":"5123"},{"n":"颜值","v":"2168"},{"n":"音乐","v":"3793"},{"n":"趣分享","v":"5883"},{"n":"一起买","v":"7759"},{"n":"科技","v":"2408"}]}],
        "1":[{"key":"cateId","name":"分类","value":[{"n":"英雄联盟","v":"1"},{"n":"CS2","v":"862"},{"n":"穿越火线","v":"4"},{"n":"lol云顶之弈","v":"5485"},{"n":"无畏契约","v":"5937"},{"n":"从军","v":"7079"},{"n":"CFHD","v":"6079"},{"n":"逆战","v":"135"},{"n":"炉石传说","v":"393"},{"n":"DOTA2","v":"7"},{"n":"地下城与勇士","v":"2"},{"n":"魔兽世界","v":"8"},{"n":"坦克世界","v":"802"},{"n":"DOTA1","v":"6"},{"n":"梦三国","v":"489"},{"n":"魔兽争霸3","v":"4615"},{"n":"问道","v":"107"},{"n":"命运方舟","v":"3058"},{"n":"QQ飞车","v":"9"},{"n":"星际争霸","v":"5"},{"n":"网游竞技","v":"100023"},{"n":"暴雪专区","v":"100043"},{"n":"射击综合游戏","v":"100141"},{"n":"彩虹岛Online","v":"683"},{"n":"冒险岛","v":"2243"},{"n":"军事游戏","v":"100133"},{"n":"暗黑破坏神","v":"1123"},{"n":"剑灵","v":"897"},{"n":"诛仙3","v":"1646"},{"n":"热血江湖","v":"387"},{"n":"起凡：群雄逐鹿","v":"1612"},{"n":"英魂之刃","v":"1830"},{"n":"神武4电脑版","v":"3227"},{"n":"龙之谷","v":"15"},{"n":"炉石战棋","v":"5751"},{"n":"全民街篮","v":"9519"},{"n":"永恒之塔","v":"446"},{"n":"武林外传一世琴缘","v":"1661"},{"n":"全境封锁2","v":"5023"},{"n":"体育游戏","v":"100135"},{"n":"全球使命","v":"939"},{"n":"九阴真经","v":"1009"},{"n":"逆水寒","v":"2952"},{"n":"征途","v":"2715"},{"n":"寻仙","v":"734"},{"n":"大话西游：归来","v":"8239"},{"n":"枪神纪","v":"496"},{"n":"战舰世界","v":"1947"},{"n":"反恐精英Online","v":"1918"},{"n":"生死狙击","v":"2471"},{"n":"千年3","v":"878"},{"n":"荒野行动PC版","v":"3185"},{"n":"QQ三国","v":"1090"},{"n":"跑跑卡丁车","v":"162"},{"n":"QQ华夏","v":"1878"},{"n":"街头篮球","v":"206"},{"n":"新飞飞(FlyFF)","v":"1582"},{"n":"战争雷霆","v":"624"},{"n":"坦克大战","v":"4359"},{"n":"造梦西游OL","v":"6815"},{"n":"御龙在天","v":"675"},{"n":"天涯明月刀","v":"1219"},{"n":"天翼决","v":"779"},{"n":"铁甲雄兵","v":"2765"},{"n":"诛仙世界","v":"7749"},{"n":"星际战甲","v":"627"},{"n":"流放之路","v":"427"},{"n":"全球使命3","v":"2953"},{"n":"希望OL","v":"1161"},{"n":"刀剑英雄","v":"915"},{"n":"新剑侠情缘","v":"586"},{"n":"FIFA Online系列","v":"100079"},{"n":"生死狙击2","v":"6091"},{"n":"NBA2KOL系列","v":"3959"},{"n":"QQ自由幻想","v":"1862"},{"n":"天下","v":"1597"},{"n":"反恐行动online","v":"861"},{"n":"英雄年代3","v":"1232"},{"n":"天堂","v":"1966"},{"n":"梦想世界3","v":"486"},{"n":"剑网3","v":"900"},{"n":"使命召唤：战区","v":"5911"},{"n":"大话西游2","v":"2975"},{"n":"洛奇英雄传","v":"432"},{"n":"梦幻诛仙2","v":"488"},{"n":"QQ幻想","v":"2419"},{"n":"火线精英","v":"2550"},{"n":"完美世界：诸神之战","v":"7217"},{"n":"DJMAX三部曲","v":"1122"},{"n":"斗战神","v":"591"},{"n":"QQ音速","v":"1085"},{"n":"丝路传说2","v":"1026"},{"n":"麻辣江湖Online","v":"43"},{"n":"守望先锋归来","v":"2174"},{"n":"征途2","v":"677"},{"n":"战意","v":"2599"},{"n":"泡泡堂","v":"440"},{"n":"新天龙八部","v":"5671"},{"n":"QQ幻想世界","v":"2897"},{"n":"激战2","v":"406"},{"n":"QQ炫舞","v":"2440"},{"n":"天书奇谈","v":"2225"},{"n":"西游3","v":"200"},{"n":"无限法则","v":"3189"},{"n":"全面战争：竞技场","v":"5901"},{"n":"KARDS","v":"8261"},{"n":"极光世界 弑神传","v":"514"},{"n":"领地人生","v":"2282"},{"n":"忍者村大战2","v":"2369"},{"n":"劲舞团","v":"2420"},{"n":"夺宝传世","v":"772"},{"n":"战地之王","v":"618"},{"n":"王权与自由","v":"9987"},{"n":"真·三国无双OL","v":"324"},{"n":"圣斗士星矢ol","v":"1652"},{"n":"新倩女幽魂","v":"1579"},{"n":"天谕","v":"1899"},{"n":"QQ仙侠传","v":"2291"},{"n":"Governor of Poker 3","v":"2423"}]}],
        "2":[{"key":"cateId","name":"分类","value":[{"n":"天天吃鸡","v":"2793"},{"n":"永劫无间","v":"6219"},{"n":"主机游戏","v":"100032"},{"n":"幻兽帕鲁","v":"9961"},{"n":"互动点播","v":"5907"},{"n":"我的世界","v":"1732"},{"n":"方舟","v":"1997"},{"n":"单机热游","v":"100002"},{"n":"怀旧游戏","v":"100125"},{"n":"逃离塔科夫","v":"3493"},{"n":"生化危机4重制版","v":"8013"},{"n":"恐惧之间","v":"6679"},{"n":"港诡实录","v":"5853"},{"n":"俄罗斯钓鱼4","v":"5495"},{"n":"完蛋！我被美女包围了！","v":"10199"},{"n":"Dread Hunger","v":"7601"},{"n":"部落：上升","v":"1318"},{"n":"Apex英雄","v":"5011"},{"n":"互动剧游","v":"6919"},{"n":"饥荒","v":"74"},{"n":"艾尔登法环","v":"5801"},{"n":"DayZ独立版","v":"1125"},{"n":"罗布乐思","v":"5771"},{"n":"骑马与砍杀系列","v":"4783"},{"n":"洛克王国","v":"2864"},{"n":"Among Us","v":"6163"},{"n":"卧龙：苍天陨落","v":"7859"},{"n":"猛兽派对","v":"6165"},{"n":"怪物猎人：崛起","v":"6479"},{"n":"怪物猎人物语","v":"7101"},{"n":"无人深空","v":"2566"},{"n":"塞尔达传说：王国之泪","v":"7883"},{"n":"恐鬼症","v":"6205"},{"n":"欧洲卡车模拟","v":"475"},{"n":"极限竞速：地平线","v":"2634"},{"n":"海贼王 寻秘世界","v":"5097"},{"n":"Dark and Darker","v":"7905"},{"n":"SCUM","v":"4245"},{"n":"战地5","v":"4371"},{"n":"纸人","v":"5257"},{"n":"星空","v":"7857"},{"n":"只狼：影逝二度","v":"4505"},{"n":"森林之子","v":"7943"},{"n":"仁王2","v":"5795"},{"n":"原子之心","v":"7925"},{"n":"盗贼之海","v":"3641"},{"n":"仙剑奇侠传七","v":"6509"},{"n":"星球大战系列","v":"554"},{"n":"音乐游戏","v":"2761"},{"n":"雾锁王国","v":"9965"},{"n":"创世理想乡","v":"6137"},{"n":"归家异途","v":"2949"},{"n":"帝国神话","v":"6821"},{"n":"鬼谷八荒","v":"6571"},{"n":"最终幻想7：重制版","v":"5809"},{"n":"重生边缘","v":"6201"},{"n":"其他单机","v":"3069"},{"n":"反转21克","v":"10013"},{"n":"战神：诸神黄昏","v":"7771"},{"n":"甜蜜之家","v":"6739"},{"n":"博德之门3","v":"6147"},{"n":"双人成行","v":"6737"},{"n":"全面战争","v":"3521"},{"n":"霍格沃茨之遗","v":"7881"},{"n":"消逝的光芒2","v":"7581"},{"n":"荒野大镖客2","v":"4319"},{"n":"海底大作战","v":"3101"},{"n":"致命公司","v":"9959"},{"n":"弈仙牌","v":"7871"},{"n":"帝国时代4","v":"4835"},{"n":"渡神记","v":"6231"},{"n":"瑞奇与叮当","v":"2455"},{"n":"幽灵线：东京","v":"7669"},{"n":"看门狗：军团","v":"6155"},{"n":"碧蓝幻想：Versus","v":"5869"},{"n":"战锤40K：暗潮","v":"3016"},{"n":"碧蓝幻想：Relink","v":"10215"},{"n":"四海兄弟","v":"5995"},{"n":"奥奇传说","v":"2848"},{"n":"育碧游戏","v":"100139"},{"n":"扫雷","v":"2349"},{"n":"三国志","v":"2270"},{"n":"沙盒与副本","v":"9151"},{"n":"禁闭求生","v":"6065"},{"n":"真三国无双","v":"1599"},{"n":"石油骚动","v":"2585"},{"n":"英灵神殿","v":"6609"},{"n":"仙剑奇侠传四","v":"1659"},{"n":"暗黑地牢","v":"2384"},{"n":"流星蝴蝶剑","v":"426"},{"n":"恶魔之魂","v":"6151"},{"n":"怪物猎人世界","v":"3519"},{"n":"三国志曹操传","v":"2592"},{"n":"杀戮尖塔","v":"3601"},{"n":"最终幻想：起源","v":"7653"},{"n":"太荒初境","v":"7685"},{"n":"九霄缳神记","v":"5941"},{"n":"马里奥赛车8","v":"5947"},{"n":"模拟人生4","v":"6607"},{"n":"街机游戏","v":"5999"},{"n":"极品飞车系列","v":"1307"},{"n":"摔跤城大乱斗","v":"7773"},{"n":"阿尔比恩","v":"8115"},{"n":"使命召唤系列","v":"100137"},{"n":"精灵与萤火意志","v":"5895"},{"n":"宝可梦：剑盾","v":"5715"},{"n":"流放者柯南","v":"2772"},{"n":"全面战争：三国","v":"3004"},{"n":"边境","v":"4779"},{"n":"黎明传说","v":"39"},{"n":"对马岛之魂","v":"6039"},{"n":"哈迪斯","v":"6153"},{"n":"最终幻想16","v":"7869"},{"n":"糖豆人：终极淘汰赛","v":"6083"},{"n":"戴森球计划","v":"6523"},{"n":"霓虹深渊","v":"5743"},{"n":"斩妖行","v":"6105"},{"n":"这是我的战争","v":"1885"},{"n":"幽灵行动：荒野","v":"2794"},{"n":"小缇娜的奇幻之地","v":"7647"},{"n":"仁王","v":"3277"},{"n":"刺客信条","v":"1962"},{"n":"复仇者联盟","v":"6121"},{"n":"方舟2","v":"7875"},{"n":"辐射76","v":"4365"},{"n":"维京传奇","v":"265"},{"n":"漫漫长夜","v":"2303"}]}],
        "3":[{"key":"cateId","name":"分类","value":[{"n":"元梦之星","v":"9521"},{"n":"王者荣耀","v":"2336"},{"n":"和平精英","v":"3203"},{"n":"英雄联盟手游","v":"6203"},{"n":"金铲铲之战","v":"7185"},{"n":"CF手游","v":"2413"},{"n":"冒险岛：联盟的意志","v":"10139"},{"n":"三国杀","v":"1669"},{"n":"原神","v":"5489"},{"n":"棋牌桌游","v":"100036"},{"n":"综合手游","v":"100029"},{"n":"新游广场","v":"100052"},{"n":"崩坏：星穹铁道","v":"7349"},{"n":"火影忍者手游","v":"2429"},{"n":"第五人格","v":"3115"},{"n":"问道手游","v":"2477"},{"n":"暗区突围","v":"7209"},{"n":"QQ飞车手游","v":"2928"},{"n":"球球大作战","v":"2411"},{"n":"明日之后","v":"3483"},{"n":"皇室战争","v":"2439"},{"n":"COD手游","v":"4769"},{"n":"手游休闲","v":"100004"},{"n":"二次元手游","v":"100091"},{"n":"摸了个鱼","v":"9283"},{"n":"MMORPG","v":"100273"},{"n":"动作游戏","v":"100197"},{"n":"幻塔","v":"6437"},{"n":"忍者必须死3","v":"4041"},{"n":"虎牙领主争霸","v":"7529"},{"n":"逆水寒手游","v":"7725"},{"n":"王者模拟战","v":"5699"},{"n":"一起玩","v":"6613"},{"n":"荣耀远征","v":"9385"},{"n":"英雄联盟电竞经理","v":"7177"},{"n":"战争冲突","v":"7449"},{"n":"欢乐斗地主","v":"1749"},{"n":"欢乐麻将","v":"1751"},{"n":"狼人杀手游","v":"100049"},{"n":"斗罗大陆：魂师对决","v":"6745"},{"n":"白荆回廊","v":"7505"},{"n":"新天龙八部手游","v":"6945"},{"n":"天龙八部手游","v":"2852"},{"n":"DNF手游","v":"4921"},{"n":"天天狼人","v":"2774"},{"n":"寻仙手游","v":"2979"},{"n":"迷你世界","v":"2683"},{"n":"SKY光遇","v":"3719"},{"n":"御龙在天手游","v":"2568"},{"n":"魔兽弧光大作战","v":"9455"},{"n":"决胜巅峰","v":"7537"},{"n":"中国象棋","v":"1671"},{"n":"天天象棋","v":"4997"},{"n":"三国志战略版","v":"5619"},{"n":"三国战纪2","v":"6049"},{"n":"奇迹MU：觉醒","v":"3116"},{"n":"诛仙手游","v":"2647"},{"n":"完美世界手游","v":"4237"},{"n":"仙境传说RO","v":"2675"},{"n":"妄想山海","v":"6007"},{"n":"航海王：燃烧意志","v":"3943"},{"n":"萤火突击","v":"6859"},{"n":"阴阳师","v":"2598"},{"n":"率土之滨","v":"2691"},{"n":"部落冲突","v":"1797"},{"n":"暗黑破坏神：不朽","v":"6385"},{"n":"英雄杀","v":"2688"},{"n":"围棋","v":"2694"},{"n":"云上城之歌","v":"5977"},{"n":"指尖四川麻将","v":"7215"},{"n":"奶块","v":"2775"},{"n":"神武4手游","v":"3135"},{"n":"三国战纪","v":"6047"},{"n":"天涯明月刀手游","v":"5115"},{"n":"高能英雄","v":"8359"},{"n":"JJ棋牌","v":"3841"},{"n":"剑侠世界：起源","v":"9655"},{"n":"创造与魔法","v":"2931"},{"n":"巅峰战舰","v":"2502"},{"n":"梦幻新诛仙","v":"5975"},{"n":"狼人杀","v":"2785"},{"n":"武侠乂手游","v":"4929"},{"n":"热血江湖手游","v":"2817"},{"n":"风云","v":"3061"},{"n":"新剑侠情缘手游","v":"6259"},{"n":"跑跑卡丁车手游","v":"2620"},{"n":"逃跑吧！少年","v":"4137"},{"n":"掼蛋","v":"6225"},{"n":"崩坏3","v":"2639"},{"n":"斗破苍穹手游","v":"4337"},{"n":"狼人杀官方","v":"3679"},{"n":"塔瑞斯·世界","v":"7915"},{"n":"巅峰极速","v":"6979"},{"n":"王者荣耀星之破晓","v":"7927"},{"n":"FC 足球世界","v":"3873"},{"n":"魔力宝贝","v":"2891"},{"n":"植物大战僵尸","v":"485"},{"n":"梦幻诛仙手游","v":"2672"},{"n":"海岛奇兵","v":"2624"},{"n":"黎明觉醒：生机","v":"6131"},{"n":"天天酷跑","v":"1715"},{"n":"鸣潮","v":"8037"},{"n":"星球：重启","v":"7681"},{"n":"最强NBA","v":"2988"},{"n":"新笑傲江湖","v":"5669"},{"n":"宝可梦大集结","v":"7115"},{"n":"蛋仔派对","v":"6909"},{"n":"原始征途","v":"7713"},{"n":"女神异闻录：夜幕魅影","v":"8093"},{"n":"异侠传：道消魔长","v":"7619"},{"n":"凡人修仙传：人界篇","v":"8297"},{"n":"精灵盛典：黎明","v":"6123"},{"n":"超凡先锋","v":"6507"},{"n":"晶核","v":"7279"},{"n":"永恒纪元：戒","v":"2646"},{"n":"蛇蛇争霸","v":"2680"},{"n":"欢乐升级","v":"3925"},{"n":"多多自走棋","v":"5133"},{"n":"JJ斗地主","v":"6271"},{"n":"全民枪战2","v":"3027"},{"n":"口袋妖怪","v":"2541"},{"n":"王牌竞速","v":"6463"},{"n":"明日方舟","v":"4925"},{"n":"曙光英雄","v":"6169"},{"n":"游戏王：决斗链接","v":"4451"},{"n":"一拳超人：最强之男","v":"4629"},{"n":"航海王热血航线","v":"6181"},{"n":"口袋觉醒","v":"5953"},{"n":"石器时代：觉醒","v":"9159"},{"n":"米加小镇","v":"7269"},{"n":"单机手游","v":"2777"},{"n":"征途手游","v":"2556"},{"n":"摩尔庄园","v":"5981"},{"n":"方舟手游","v":"4035"},{"n":"一梦江湖","v":"3082"},{"n":"重返帝国","v":"6955"},{"n":"全明星街球派对","v":"8401"},{"n":"虎牙吃鸡","v":"7465"},{"n":"军棋","v":"2561"},{"n":"真三国无双霸","v":"6071"},{"n":"弹弹堂手游","v":"2857"},{"n":"王牌战争：文明重启","v":"5479"},{"n":"多乐棋牌","v":"6209"},{"n":"哈利波特：魔法觉醒","v":"5835"},{"n":"香肠派对","v":"3639"},{"n":"七日世界","v":"9995"},{"n":"火影忍者：忍者新世代","v":"6307"},{"n":"时空召唤","v":"2551"},{"n":"永劫无间手游","v":"7579"},{"n":"少年三国志2","v":"6125"},{"n":"神雕侠侣2","v":"4209"},{"n":"龙之谷2手游","v":"2736"},{"n":"魂斗罗：归来","v":"2824"},{"n":"荣耀新三国","v":"6943"},{"n":"剑网3：指尖江湖","v":"3885"},{"n":"铃兰之剑","v":"8723"},{"n":"神将三国","v":"6621"},{"n":"远光84","v":"9457"},{"n":"征途2手游","v":"2811"},{"n":"斗斗堂","v":"7133"},{"n":"冒险岛：枫之传说","v":"8005"},{"n":"自由幻想手游","v":"4015"},{"n":"新游推荐","v":"3160"},{"n":"长安幻想","v":"6727"},{"n":"拳皇98终极之战OL","v":"2687"},{"n":"奇迹：最强者","v":"3215"},{"n":"流星群侠传","v":"3927"},{"n":"火炬之光：无限","v":"6399"},{"n":"鸿图之下","v":"6027"},{"n":"绝世仙王","v":"6619"},{"n":"三国志战棋版","v":"7937"},{"n":"幻世九歌","v":"7199"},{"n":"尘白禁区","v":"7297"},{"n":"合金弹头：觉醒","v":"6931"},{"n":"新神魔大陆","v":"5939"},{"n":"剑侠情缘2剑歌行","v":"4805"},{"n":"青云诀2","v":"6009"},{"n":"决战平安京","v":"3064"},{"n":"小花仙","v":"7923"},{"n":"警匪杀","v":"7191"},{"n":"未来之役","v":"6831"},{"n":"拉轰西游","v":"9543"},{"n":"大话西游手游","v":"2626"},{"n":"天天吃鸡手机版","v":"4341"},{"n":"贪吃蛇大作战","v":"2584"},{"n":"倩女幽魂手游","v":"2503"},{"n":"剑灵：革命","v":"4545"},{"n":"我的起源","v":"5365"},{"n":"剑侠情缘手游","v":"2621"},{"n":"斗罗大陆-斗神再临","v":"6631"},{"n":"蜀山战纪之剑侠传奇","v":"2654"},{"n":"英魂之刃口袋版","v":"2760"},{"n":"荒野乱斗","v":"4613"},{"n":"拳皇命运","v":"3379"}]}]
    },
    searchUrl:'https://search.cdn.huya.com/?m=Search&do=getSearchContent&q=**&uid=0&v=4&typ=-5&livestate=0&rows=40&start=0',
    searchable:2,
    quickSearch:0,
    headers:{
        'User-Agent':'MOBILE_UA'
    },
    timeout:5000,
    limit:8,
    play_parse:true,
    lazy:'',
    lazy:`js:
        if (/m\\.huya/.test(input)) {
            rule.sniffer = 0
        }
    `,
    推荐:`js:
        let d = [];
        let jo = JSON.parse(request(input)).data.datas;
        jo.forEach(it => {
                d.push({
                    url: it.profileRoom,
                    title: it.introduction,
                    img: it.screenshot,
                    desc: '👁' + it.totalCount + '  🆙' + it.nick,
                })
        });
        setResult(d);
    `,
    一级:`js:
        let d = [];
        let jo = JSON.parse(request(input)).data.datas;
        jo.forEach(it => {
                d.push({
                    url: it.profileRoom,
                    title: it.introduction,
                    img: it.screenshot,
                    desc: '👁' + it.totalCount + '  🆙' + it.nick,
                })
        });
        setResult(d);
    `,
    // 二级:'*',
    二级: `js:
        try {
            if (typeof play_url === "undefined") {
                var play_url = ""
            }
            var jo = JSON.parse(request(input)).data;
            VOD = {
                vod_id: jo.roomId,
                vod_name: jo.roomName,
                vod_pic: jo.roomPic,
                type_name: "虎牙." + jo.categoryName,
                vod_director: '🆙 ' + jo.ownerName,
                vod_content: "🏷分区：虎牙" + "·" + jo.categoryName + " 🏷UP主：" + jo.ownerName + " 🏷人气：" + jo.online + (jo.isLive === 1 ? " 🏷状态：正在直播" : "状态：未开播")
            };
            let episodes = JSON.parse(request("http://live.yj1211.work/api/live/getRealUrlMultiSource?platform=" + jo.platForm + "&roomId=" + jo.roomId)).data; //多线路
            if (Object.keys(episodes).length !== 0) {
                let playFrom = [];
                let playList = [];
                let kplayList = [];
                Object.keys(episodes).forEach(function(key) {
                    playFrom.append(key);
                    kplayList = episodes[key].map(function(it) {
                        let title = it.qualityName;
                        let playUrl = it.playUrl
                        return title + "$" + play_url + urlencode(playUrl)
                    }).join("#")
                    playList.append(kplayList);
                });
                let vod_play_from = playFrom.join("$$$");
                let vod_play_url = playList.join("$$$");
                VOD["vod_play_from"] = vod_play_from;
                VOD["vod_play_url"] = vod_play_url;
            } else {
                var d = [];
                episodes = JSON.parse(request("http://live.yj1211.work/api/live/getRealUrl?platform=" + jo.platForm + "&roomId=" + jo.roomId)).data; //单线路
                var name = {
                    "OD": "原画",
                    "FD": "流畅",
                    "LD": "标清",
                    "SD": "高清",
                    "HD": "超清",
                    "2K": "2K",
                    "4K": "4K",
                    "FHD": "全高清",
                    "XLD": "极速",
                    "SQ": "普通音质",
                    "HQ": "高音质"
                };
                Object.keys(episodes).forEach(function(key) {
                    if (!/ayyuid|to/.test(key)) {
                        d.push({
                            title: name[key],
                            url: episodes[key]
                        })
                    }
                });
                d.push(
                    {
                        title: "虎牙解析",
                        url: "http://cfss.cc/cdn/hy/" + jo.roomId + ".flv"
                    },
                    {
                        title: "解析1",
                        url: "http://epg.112114.xyz/huya/" + jo.roomId
                    },
                    {
                        title: "解析2",
                        url: "https://www.aois.eu.org/live/huya/" + jo.roomId
                    },
                    {
                        title: "解析3",
                        url: "https://www.goodiptv.club/huya/" + jo.roomId
                    },
                    {
                        title: "解析4",
                        url: "http://maomao.kandiantv.cn/huya1.php?id=" + jo.roomId
                    },
                    {
                        title: "解析5",
                        url: "http://*************:35455/huya/" + jo.roomId
                    },
                    {
                        title: "解析6",
                        url: "http://*************/php/huya.php?id=" + jo.roomId
                    },
                    {
                        title: "原址嗅探",
                        url: "https://m.huya.com/" + jo.roomId
                    },
                );
                VOD["vod_play_from"] = "播放源";
                VOD["vod_play_url"] = d.map(function(it) {
                    return it.title + "$" + it.url
                }).join("#");
                setResult(d);
            }
        } catch (e) {
            log("获取二级详情页发生错误:" + e.message);
        }
    `,
    // 搜索:'json:response.3.docs;game_roomName;game_screenshot;game_nick;room_id',
    搜索: `js:
        var d = [];
        let jo = JSON.parse(request(input)).response[3].docs;
        jo.forEach(it => {
            d.push({
                url: it.room_id,
                title: it.game_roomName,
                img: it.game_screenshot,
                desc: '👁' + it.game_total_count + '  🆙' + it.game_nick,
            })
        });
        setResult(d);
    `,

    //是否启用辅助嗅探: 1,0
    sniffer:1,
    // 辅助嗅探规则js写法
    isVideo: `js:
        log(input);
        if(/\\/huya/.test(input)) {
            input = true
        } else if(/\\.flv?|\\.m3u8?|\\.mp4?/.test(input)){
            input = true
        }else{
            input = false
        }
    `,
}