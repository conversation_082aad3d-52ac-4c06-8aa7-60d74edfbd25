<template>
  <div class="interfaces-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>接口管理</h2>
        <p>管理和监控TVBox接口源</p>
      </div>
      <div class="header-right">
        <el-button type="primary" icon="Plus" @click="$router.push('/interfaces/create')">
          <span class="button-text">添加接口</span>
        </el-button>
        <el-dropdown @command="handleBatchUpdate">
          <el-button type="success" icon="Refresh">
            <span class="button-text">批量更新</span>
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="smart" icon="MagicStick">
                智能更新 (跳过无变化)
              </el-dropdown-item>
              <el-dropdown-item command="force" icon="RefreshRight" divided>
                强制更新 (全部重新解析)
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button icon="Refresh" @click="refreshList">
          <span class="button-text">刷新</span>
        </el-button>
      </div>
    </div>
    
    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" class="search-form">
        <el-row :gutter="16">
          <el-col :xs="24" :sm="8" :md="6">
            <el-form-item label="接口名称">
              <el-input
                v-model="searchForm.name"
                placeholder="请输入接口名称"
                clearable
                @keyup.enter="handleSearch"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="6">
            <el-form-item label="分类">
              <el-select v-model="searchForm.category" placeholder="请选择分类" clearable style="width: 100%">
                <el-option
                  v-for="category in categories"
                  :key="category"
                  :label="category"
                  :value="category"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="6">
            <el-form-item label="状态">
              <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 100%">
                <el-option label="在线" value="online" />
                <el-option label="离线" value="offline" />
                <el-option label="错误" value="error" />
                <el-option label="未知" value="unknown" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :md="6">
            <el-form-item class="search-buttons">
              <el-button type="primary" icon="Search" @click="handleSearch">搜索</el-button>
              <el-button icon="Refresh" @click="resetSearch">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    
    <!-- 接口列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>接口列表 ({{ total }})</span>
          <div class="header-actions">
            <el-button-group>
              <el-button
                :type="viewMode === 'table' ? 'primary' : ''"
                icon="List"
                @click="viewMode = 'table'"
              >
                列表
              </el-button>
              <el-button
                :type="viewMode === 'card' ? 'primary' : ''"
                icon="Grid"
                @click="viewMode = 'card'"
              >
                卡片
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>
      
      <!-- 表格视图 -->
      <div v-if="viewMode === 'table'" class="table-responsive">
        <el-table
          v-loading="loading"
          :data="interfaceList"
          stripe
          style="min-width: 1200px"
          @selection-change="handleSelectionChange"
        >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="接口名称" min-width="150">
          <template #default="{ row }">
            <div class="interface-name">
              <el-link type="primary" @click="viewDetail(row)">
                {{ row.name }}
              </el-link>
              <el-tag v-if="row.is_public" size="small" type="success">公开</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="url" label="接口地址" min-width="200" show-overflow-tooltip class="url-column" />
        <el-table-column prop="category" label="分类" width="100" class="category-column">
          <template #default="{ row }">
            <el-tag v-if="row.category" size="small">{{ row.category }}</el-tag>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="统计信息" width="120" class="stats-column">
          <template #default="{ row }">
            <div class="stats-info">
              <div>站点: {{ row.sites_count }}</div>
              <div>直播: {{ row.lives_count }}</div>
              <div>解析: {{ row.parses_count }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="success_rate" label="成功率" width="80" class="success-rate-column">
          <template #default="{ row }">
            <el-progress
              :percentage="row.success_rate"
              :color="getProgressColor(row.success_rate)"
              :show-text="false"
              :stroke-width="6"
            />
            <div class="text-center">{{ row.success_rate }}%</div>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="120" class="created-time-column">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button size="small" icon="View" @click="viewDetail(row)">
                详情
              </el-button>
              <el-button size="small" icon="Star" @click="subscribeInterface(row)">
                订阅
              </el-button>
              <el-dropdown @command="(command) => handleAction(command, row)">
                <el-button size="small" icon="More" />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="edit" icon="Edit">编辑</el-dropdown-item>
                    <el-dropdown-item command="copy" icon="CopyDocument">复制</el-dropdown-item>
                    <el-dropdown-item command="delete" icon="Delete" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </el-button-group>
          </template>
        </el-table-column>
        </el-table>
      </div>

      <!-- 卡片视图 -->
      <div v-else class="card-view">
        <el-row :gutter="20">
          <el-col
            v-for="item in interfaceList"
            :key="item.id"
            :xs="24"
            :sm="12"
            :md="8"
            :lg="6"
            :xl="6"
          >
            <InterfaceCard
              :interface="item"
              @view="viewDetail"
              @subscribe="subscribeInterface"
              @edit="editInterface"
              @delete="deleteInterface"
            />
          </el-col>
        </el-row>
      </div>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { interfaceApi } from '@/api/interfaces'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'
import { formatDate } from '@/utils/date'
import InterfaceCard from './components/InterfaceCard.vue'

const router = useRouter()

// 组件注册
const components = {
  ArrowDown,
  InterfaceCard
}

// 响应式数据
const loading = ref(false)
const viewMode = ref('table')
const interfaceList = ref([])
const categories = ref([])
const selectedRows = ref([])
const total = ref(0)

const searchForm = reactive({
  name: '',
  category: '',
  status: ''
})

const pagination = reactive({
  page: 1,
  size: 20
})

// 计算属性
const searchParams = computed(() => ({
  skip: (pagination.page - 1) * pagination.size,
  limit: pagination.size,
  ...searchForm
}))

// 方法
const loadInterfaceList = async () => {
  try {
    loading.value = true
    const response = await interfaceApi.getInterfaceSources(searchParams.value)

    // 处理后端返回的数据格式
    if (response.data && Array.isArray(response.data.items)) {
      interfaceList.value = response.data.items
      total.value = response.data.total || response.data.items.length
    } else if (Array.isArray(response.data)) {
      // 兼容直接返回数组的情况
      interfaceList.value = response.data
      total.value = response.data.length
    } else {
      interfaceList.value = []
      total.value = 0
    }
  } catch (error) {
    ElMessage.error('加载接口列表失败')
    interfaceList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

const loadCategories = async () => {
  try {
    const response = await interfaceApi.getInterfaceCategories()
    categories.value = response.data.categories
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadInterfaceList()
}

const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  handleSearch()
}

const refreshList = () => {
  loadInterfaceList()
}

// 批量更新处理
const handleBatchUpdate = async (command) => {
  const forceUpdate = command === 'force'
  const updateType = forceUpdate ? '强制更新' : '智能更新'

  try {
    // 确认对话框
    await ElMessageBox.confirm(
      `确定要执行${updateType}吗？${forceUpdate ? '这将重新解析所有在线接口源。' : '这将只更新有变化的接口源。'}`,
      `批量${updateType}`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        distinguishCancelAndClose: true
      }
    )

    // 显示加载提示
    const loadingNotification = ElNotification({
      title: `正在执行批量${updateType}`,
      message: '请稍候，正在处理中...',
      type: 'info',
      duration: 0,
      showClose: false
    })

    // 调用批量更新API
    const response = await interfaceApi.batchUpdateInterfaceSources({
      force_update: forceUpdate
    })

    // 关闭加载提示
    loadingNotification.close()

    // 显示结果
    if (response.data.success) {
      const { total, updated, skipped, failed } = response.data

      ElNotification({
        title: `批量${updateType}完成`,
        message: `总计: ${total}个，更新: ${updated}个，跳过: ${skipped}个，失败: ${failed}个`,
        type: failed > 0 ? 'warning' : 'success',
        duration: 5000
      })

      // 如果有详细信息，显示在控制台
      if (response.data.details && response.data.details.length > 0) {
        console.log('批量更新详细结果:', response.data.details)
      }

      // 刷新列表
      await loadInterfaceList()
    } else {
      ElMessage.error(response.data.message || '批量更新失败')
    }
  } catch (error) {
    if (error !== 'cancel' && error !== 'close') {
      console.error('批量更新错误:', error)
      ElMessage.error('批量更新失败，请稍后重试')
    }
  }
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadInterfaceList()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadInterfaceList()
}

const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

const viewDetail = (row) => {
  router.push(`/interfaces/${row.id}`)
}



const subscribeInterface = async (row) => {
  try {
    await interfaceApi.subscribeInterfaceSource(row.id, {})
    ElMessage.success('订阅成功')
  } catch (error) {
    ElMessage.error('订阅失败')
  }
}

const editInterface = (row) => {
  router.push(`/interfaces/${row.id}/edit`)
}

const deleteInterface = (row) => {
  ElMessageBox.confirm(
    `确定要删除接口 "${row.name}" 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await interfaceApi.deleteInterfaceSource(row.id)
      ElMessage.success('删除成功')
      loadInterfaceList()
    } catch (error) {
      ElMessage.error('删除失败')
    }
  })
}

const handleAction = (command, row) => {
  switch (command) {
    case 'edit':
      editInterface(row)
      break
    case 'copy':
      // 复制接口逻辑
      ElMessage.info('复制功能开发中')
      break
    case 'delete':
      deleteInterface(row)
      break
  }
}



const getStatusType = (status) => {
  const statusMap = {
    online: 'success',
    offline: 'info',
    error: 'danger',
    unknown: 'warning'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    online: '在线',
    offline: '离线',
    error: '错误',
    unknown: '未知'
  }
  return statusMap[status] || '未知'
}

const getProgressColor = (percentage) => {
  if (percentage >= 80) return '#67c23a'
  if (percentage >= 60) return '#e6a23c'
  return '#f56c6c'
}

// 生命周期
onMounted(() => {
  loadInterfaceList()
  loadCategories()
})
</script>

<style lang="scss" scoped>
.interfaces-page {
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .header-left {
      h2 {
        margin: 0 0 4px 0;
        color: var(--el-text-color-primary);
      }
      
      p {
        margin: 0;
        color: var(--el-text-color-regular);
        font-size: 14px;
      }
    }
    
    .header-right {
      display: flex;
      gap: 12px;
    }
  }
  
  .search-card {
    margin-bottom: 20px;
  }
  
  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .interface-name {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .stats-info {
      font-size: 12px;
      color: var(--el-text-color-regular);
      line-height: 1.4;
    }
    
    .text-center {
      text-align: center;
      font-size: 12px;
      margin-top: 2px;
    }
    
    .text-muted {
      color: var(--el-text-color-placeholder);
    }
    
    .card-view {
      min-height: 400px;
    }
    
    .pagination-container {
      margin-top: 20px;
      text-align: right;
    }

    // 表格响应式容器
    .table-responsive {
      width: 100%;
      max-width: 100%;
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;

      // 确保表格容器不会被内容撑破
      .el-table {
        min-width: 1200px; // 确保所有列都有足够空间
      }

      &::-webkit-scrollbar {
        height: 6px;
      }

      &::-webkit-scrollbar-track {
        background: var(--el-fill-color-lighter);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: var(--el-border-color-darker);
        border-radius: 3px;

        &:hover {
          background: var(--el-border-color-dark);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .interfaces-page {
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-right {
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
        gap: 8px;
      }
    }

    .search-card {
      width: 100%;
      max-width: 100%;

      .search-form {
        .search-buttons {
          margin-top: 16px;

          .el-button {
            width: 100%;
            margin-bottom: 8px;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }

    .table-card {
      width: 100%;
      max-width: 100%;

      .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        .header-actions {
          width: 100%;
          display: flex;
          justify-content: center;
        }
      }

      .table-responsive {
        width: 100%;
        max-width: calc(100vw - 40px); // 减去卡片的padding
      }
    }
  }
}

// 平板响应式设计
@media (min-width: 768px) and (max-width: 1024px) {
  .interfaces-page {
    .search-card {
      .search-form {
        .el-col {
          margin-bottom: 16px;
        }
      }
    }
  }
}

// 小屏桌面设备
@media (min-width: 1025px) and (max-width: 1200px) {
  .interfaces-page {
    // 小屏桌面 - 隐藏统计信息列
    :deep(.el-table_1_column_6) { // 统计信息
      display: none !important;
    }
  }
}
</style>
