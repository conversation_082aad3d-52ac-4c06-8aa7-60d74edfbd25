{"version": 3, "file": "hy-am.js", "sources": ["../../../../../packages/locale/lang/hy-am.ts"], "sourcesContent": ["export default {\n  name: 'hy-am',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'Լաւ',\n      clear: 'Մաքրել',\n    },\n    datepicker: {\n      now: 'Հիմա',\n      today: 'Այսօր',\n      cancel: 'Չեղարկել',\n      clear: 'Մաքրել',\n      confirm: 'Լաւ',\n      selectDate: 'Ընտրեք ամսաթիւը',\n      selectTime: 'Ընտրեք ժամանակը',\n      startDate: 'Սկզբ. ամսաթիւը',\n      startTime: 'Սկզբ. ժամանակը',\n      endDate: 'Վերջ. ամսաթիվը',\n      endTime: 'Վերջ. ժամանակը',\n      prevYear: 'Նախորդ տարի',\n      nextYear: 'Յաջորդ տարի',\n      prevMonth: 'Նախորդ ամիս',\n      nextMonth: 'Յաջորդ ամիս',\n      year: 'Տարի',\n      month1: 'Յունուար',\n      month2: 'Փետրուար',\n      month3: 'Մարտ',\n      month4: 'Ապրիլ',\n      month5: 'Մայիս',\n      month6: 'Յունիս',\n      month7: 'Յուլիս',\n      month8: 'Օգոստոս',\n      month9: 'Սեպտեմբեր',\n      month10: 'Յոկտեմբեր',\n      month11: 'Նոյեմբեր',\n      month12: 'Դեկտեմբեր',\n      week: 'Շաբաթ',\n      weeks: {\n        sun: 'Կիր',\n        mon: 'Երկ',\n        tue: 'Եր',\n        wed: 'Չոր',\n        thu: 'Հինգ',\n        fri: 'Ուրբ',\n        sat: 'Շաբ',\n      },\n      months: {\n        jan: 'Յունվ',\n        feb: 'Փետ',\n        mar: 'Մար',\n        apr: 'Ապր',\n        may: 'Մայ',\n        jun: 'Յուն',\n        jul: 'Յուլ',\n        aug: 'Օգ',\n        sep: 'Սեպտ',\n        oct: 'Յոկ',\n        nov: 'Նոյ',\n        dec: 'Դեկ',\n      },\n    },\n    select: {\n      loading: 'Բեռնում',\n      noMatch: 'Համապատասխան տուեալներ չկան',\n      noData: 'Տվյալներ չկան',\n      placeholder: 'Ընտրել',\n    },\n    mention: {\n      loading: 'Բեռնում',\n    },\n    cascader: {\n      noMatch: 'Համապատասխան տուեալներ չկան',\n      loading: 'Բեռնում',\n      placeholder: 'Ընտրել',\n      noData: 'Տվյալներ չկան',\n    },\n    pagination: {\n      goto: 'Անցնել',\n      pagesize: ' էջում',\n      total: 'Ընդամենը {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Հաղորդագրութիւն',\n      confirm: 'Լաւ',\n      cancel: 'Չեղարկել',\n      error: 'Անվաւեր տուեալների մուտք',\n    },\n    upload: {\n      deleteTip: 'Սեղմեք [Ջնջել] ջնջելու համար',\n      delete: 'Ջնջել',\n      preview: 'Նախադիտում',\n      continue: 'Շարունակել',\n    },\n    table: {\n      emptyText: 'Տուեալներ չկան',\n      confirmFilter: 'Յաստատել',\n      resetFilter: 'Վերագործարկել',\n      clearFilter: 'Բոլորը',\n      sumText: 'Գումարը',\n    },\n    tree: {\n      emptyText: 'Տուեալներ չկան',\n    },\n    transfer: {\n      noMatch: 'Համապատասխան տուեալներ չկան',\n      noData: 'Տուեալներ չկան',\n      titles: ['Ցուցակ 1', 'Ցուցակ 2'],\n      filterPlaceholder: 'Մուտքագրեք բանալի բառ',\n      noCheckedFormat: '{total} միաւոր',\n      hasCheckedFormat: '{checked}/{total} ընտրուած է',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,WAAe;AACf,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,oBAAoB;AACnC,MAAM,KAAK,EAAE,sCAAsC;AACnD,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,0BAA0B;AACrC,MAAM,KAAK,EAAE,gCAAgC;AAC7C,MAAM,MAAM,EAAE,kDAAkD;AAChE,MAAM,KAAK,EAAE,sCAAsC;AACnD,MAAM,OAAO,EAAE,oBAAoB;AACnC,MAAM,UAAU,EAAE,uFAAuF;AACzG,MAAM,UAAU,EAAE,uFAAuF;AACzG,MAAM,SAAS,EAAE,4EAA4E;AAC7F,MAAM,SAAS,EAAE,4EAA4E;AAC7F,MAAM,OAAO,EAAE,4EAA4E;AAC3F,MAAM,OAAO,EAAE,4EAA4E;AAC3F,MAAM,QAAQ,EAAE,+DAA+D;AAC/E,MAAM,QAAQ,EAAE,+DAA+D;AAC/E,MAAM,SAAS,EAAE,+DAA+D;AAChF,MAAM,SAAS,EAAE,+DAA+D;AAChF,MAAM,IAAI,EAAE,0BAA0B;AACtC,MAAM,MAAM,EAAE,kDAAkD;AAChE,MAAM,MAAM,EAAE,kDAAkD;AAChE,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,MAAM,EAAE,wDAAwD;AACtE,MAAM,OAAO,EAAE,wDAAwD;AACvE,MAAM,OAAO,EAAE,kDAAkD;AACjE,MAAM,OAAO,EAAE,wDAAwD;AACvE,MAAM,IAAI,EAAE,gCAAgC;AAC5C,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,OAAO,EAAE,0JAA0J;AACzK,MAAM,MAAM,EAAE,2EAA2E;AACzF,MAAM,WAAW,EAAE,sCAAsC;AACzD,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,4CAA4C;AAC3D,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,0JAA0J;AACzK,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,WAAW,EAAE,sCAAsC;AACzD,MAAM,MAAM,EAAE,2EAA2E;AACzF,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,sCAAsC;AAClD,MAAM,QAAQ,EAAE,iCAAiC;AACjD,MAAM,KAAK,EAAE,0DAA0D;AACvE,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,4FAA4F;AACzG,MAAM,OAAO,EAAE,oBAAoB;AACnC,MAAM,MAAM,EAAE,kDAAkD;AAChE,MAAM,KAAK,EAAE,wIAAwI;AACrJ,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,iJAAiJ;AAClK,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,OAAO,EAAE,8DAA8D;AAC7E,MAAM,QAAQ,EAAE,8DAA8D;AAC9E,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,iFAAiF;AAClG,MAAM,aAAa,EAAE,kDAAkD;AACvE,MAAM,WAAW,EAAE,gFAAgF;AACnG,MAAM,WAAW,EAAE,sCAAsC;AACzD,MAAM,OAAO,EAAE,4CAA4C;AAC3D,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,iFAAiF;AAClG,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,0JAA0J;AACzK,MAAM,MAAM,EAAE,iFAAiF;AAC/F,MAAM,MAAM,EAAE,CAAC,wCAAwC,EAAE,wCAAwC,CAAC;AAClG,MAAM,iBAAiB,EAAE,sHAAsH;AAC/I,MAAM,eAAe,EAAE,8CAA8C;AACrE,MAAM,gBAAgB,EAAE,2EAA2E;AACnG,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,MAAM;AACnB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}