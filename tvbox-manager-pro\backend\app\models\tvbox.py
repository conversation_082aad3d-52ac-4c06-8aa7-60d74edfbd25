#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
TVBox相关数据模型
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, Float, JSON, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
import json

from app.core.database import Base

class InterfaceSource(Base):
    """接口源模型"""
    __tablename__ = "interface_sources"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    url = Column(String(500), nullable=False)
    description = Column(Text)
    
    # 分类和标签
    category = Column(String(50))  # 接口分类
    tags = Column(String(200))  # 标签，逗号分隔
    
    # 解密信息
    decrypt_method = Column(String(50))  # 解密方法
    decrypt_key = Column(String(255))  # 解密密钥（如果需要）
    
    # 状态信息
    is_active = Column(Boolean, default=True)
    is_public = Column(Boolean, default=False)  # 是否公开
    status = Column(String(20), default="unknown")  # online, offline, error, unknown
    
    # 统计信息
    success_count = Column(Integer, default=0)  # 成功解析次数
    error_count = Column(Integer, default=0)  # 错误次数
    last_success_at = Column(DateTime(timezone=True))
    last_error_at = Column(DateTime(timezone=True))
    last_error_message = Column(Text)
    
    # 配置信息
    sites_count = Column(Integer, default=0)  # 站点数量
    lives_count = Column(Integer, default=0)  # 直播源数量
    parses_count = Column(Integer, default=0)  # 解析器数量
    config_content = Column(Text)  # 原始配置内容
    localized_config = Column(Text)  # 本地化后的配置内容
    
    # 更新信息
    update_interval = Column(Integer, default=3600)  # 更新间隔（秒）
    last_update_at = Column(DateTime(timezone=True))
    next_update_at = Column(DateTime(timezone=True))

    # 新的本地化功能 - 简化字段
    enable_localization = Column(Boolean, default=False)  # 是否启用本地化

    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    configs = relationship("TvboxConfig", back_populates="source", cascade="all, delete-orphan")
    subscriptions = relationship("Subscription", back_populates="source")
    
    def __repr__(self):
        return f"<InterfaceSource(id={self.id}, name='{self.name}', url='{self.url}')>"
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        total = self.success_count + self.error_count
        if total == 0:
            return 0.0
        return round(self.success_count / total * 100, 2)

class TvboxConfig(Base):
    """TVBox配置模型"""
    __tablename__ = "tvbox_configs"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    content = Column(Text, nullable=False)  # JSON配置内容
    source_id = Column(Integer, ForeignKey("interface_sources.id"))
    
    # 配置信息
    spider = Column(String(500))  # 爬虫规则
    wallpaper = Column(String(500))  # 壁纸
    logo = Column(String(500))  # Logo
    
    # 统计信息
    sites_count = Column(Integer, default=0)
    lives_count = Column(Integer, default=0)
    parses_count = Column(Integer, default=0)
    
    # 状态字段
    is_active = Column(Boolean, default=True)
    is_default = Column(Boolean, default=False)
    
    # 版本信息
    version = Column(String(20))
    checksum = Column(String(64))  # MD5校验和
    
    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    source = relationship("InterfaceSource", back_populates="configs")
    sites = relationship("SiteInfo", back_populates="config", cascade="all, delete-orphan")
    lives = relationship("LiveGroup", back_populates="config", cascade="all, delete-orphan")
    parses = relationship("ParseInfo", back_populates="config", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<TvboxConfig(id={self.id}, name='{self.name}')>"
    
    def get_config_json(self) -> dict:
        """获取配置JSON"""
        try:
            return json.loads(self.content)
        except:
            return {}
    
    def update_stats(self):
        """更新统计信息"""
        config_data = self.get_config_json()
        self.sites_count = len(config_data.get('sites', []))
        self.lives_count = sum(len(group.get('channels', [])) for group in config_data.get('lives', []))
        self.parses_count = len(config_data.get('parses', []))
        self.spider = config_data.get('spider', '')
        self.wallpaper = config_data.get('wallpaper', '')
        self.logo = config_data.get('logo', '')

class SiteInfo(Base):
    """站点信息模型"""
    __tablename__ = "site_infos"
    
    id = Column(Integer, primary_key=True, index=True)
    config_id = Column(Integer, ForeignKey("tvbox_configs.id"), nullable=False)
    
    # 站点基本信息
    key = Column(String(100), nullable=False)
    name = Column(String(100), nullable=False)
    type = Column(Integer, default=0)  # 站点类型
    api = Column(String(500))  # API地址
    searchable = Column(Integer, default=1)  # 是否可搜索
    quickSearch = Column(Integer, default=1)  # 是否支持快速搜索
    filterable = Column(Integer, default=1)  # 是否可筛选
    
    # 扩展信息
    ext = Column(Text)  # 扩展配置
    categories = Column(Text)  # 分类信息
    
    # 状态信息
    is_active = Column(Boolean, default=True)
    
    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    config = relationship("TvboxConfig", back_populates="sites")
    
    def __repr__(self):
        return f"<SiteInfo(id={self.id}, key='{self.key}', name='{self.name}')>"

class LiveGroup(Base):
    """直播分组模型"""
    __tablename__ = "live_groups"
    
    id = Column(Integer, primary_key=True, index=True)
    config_id = Column(Integer, ForeignKey("tvbox_configs.id"), nullable=False)
    
    # 分组信息
    group = Column(String(100), nullable=False)  # 分组名称
    channels = Column(Text)  # 频道列表（JSON格式）
    
    # 状态信息
    is_active = Column(Boolean, default=True)
    
    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    config = relationship("TvboxConfig", back_populates="lives")
    
    def __repr__(self):
        return f"<LiveGroup(id={self.id}, group='{self.group}')>"
    
    def get_channels(self) -> list:
        """获取频道列表"""
        try:
            return json.loads(self.channels) if self.channels else []
        except:
            return []

class ParseInfo(Base):
    """解析器信息模型"""
    __tablename__ = "parse_infos"
    
    id = Column(Integer, primary_key=True, index=True)
    config_id = Column(Integer, ForeignKey("tvbox_configs.id"), nullable=False)
    
    # 解析器信息
    name = Column(String(100), nullable=False)
    url = Column(String(500), nullable=False)
    type = Column(Integer, default=0)  # 解析器类型
    ext = Column(Text)  # 扩展配置
    
    # 状态信息
    is_active = Column(Boolean, default=True)
    
    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    config = relationship("TvboxConfig", back_populates="parses")
    
    def __repr__(self):
        return f"<ParseInfo(id={self.id}, name='{self.name}', url='{self.url}')>"

class SubscriptionGroup(Base):
    """订阅组模型"""
    __tablename__ = "subscription_groups"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    access_key = Column(String(100), unique=True, nullable=False)
    is_active = Column(Boolean, default=True)

    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关联关系
    user = relationship("User", back_populates="subscription_groups")
    items = relationship("SubscriptionGroupItem", back_populates="subscription_group", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<SubscriptionGroup(id={self.id}, name='{self.name}', user_id={self.user_id})>"

class SubscriptionGroupItem(Base):
    """订阅组项模型"""
    __tablename__ = "subscription_group_items"

    id = Column(Integer, primary_key=True, index=True)
    subscription_group_id = Column(Integer, ForeignKey("subscription_groups.id"), nullable=False)
    interface_id = Column(Integer, ForeignKey("interface_sources.id"), nullable=False)

    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # 关联关系
    subscription_group = relationship("SubscriptionGroup", back_populates="items")
    interface = relationship("InterfaceSource")

    # 唯一约束
    __table_args__ = (
        UniqueConstraint('subscription_group_id', 'interface_id', name='uq_subscription_group_interface'),
    )

    def __repr__(self):
        return f"<SubscriptionGroupItem(id={self.id}, subscription_group_id={self.subscription_group_id}, interface_id={self.interface_id})>"

