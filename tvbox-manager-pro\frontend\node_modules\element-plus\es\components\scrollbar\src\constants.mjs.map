{"version": 3, "file": "constants.mjs", "sources": ["../../../../../../packages/components/scrollbar/src/constants.ts"], "sourcesContent": ["import type { InjectionKey } from 'vue'\n\nexport interface ScrollbarContext {\n  scrollbarElement: HTMLDivElement\n  wrapElement: HTMLDivElement\n}\n\nexport const scrollbarContextKey: InjectionKey<ScrollbarContext> = Symbol(\n  'scrollbarContextKey'\n)\n"], "names": [], "mappings": "AAAY,MAAC,mBAAmB,GAAG,MAAM,CAAC,qBAAqB;;;;"}