{"version": 3, "file": "container.js", "sources": ["../../../../../../packages/components/container/src/container.vue"], "sourcesContent": ["<template>\n  <section :class=\"[ns.b(), ns.is('vertical', isVertical)]\">\n    <slot />\n  </section>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, useSlots } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\n\nimport type { Component, VNode } from 'vue'\n\ndefineOptions({\n  name: 'ElContainer',\n})\nconst props = defineProps({\n  /**\n   * @description layout direction for child elements\n   */\n  direction: {\n    type: String,\n  },\n})\nconst slots = useSlots()\n\nconst ns = useNamespace('container')\n\nconst isVertical = computed(() => {\n  if (props.direction === 'vertical') {\n    return true\n  } else if (props.direction === 'horizontal') {\n    return false\n  }\n  if (slots && slots.default) {\n    const vNodes: VNode[] = slots.default()\n    return vNodes.some((vNode) => {\n      const tag = (vNode.type as Component).name\n      return tag === 'ElHeader' || tag === 'ElFooter'\n    })\n  } else {\n    return false\n  }\n})\n</script>\n"], "names": ["_openBlock", "_createElementBlock", "_normalizeClass", "_unref"], "mappings": ";;;;;;;;uCAYc,CAAA;AAAA,EACZ,IAAM,EAAA,aAAA;AACR,CAAA,CAAA,CAAA;;;;;;;;;;;;;AASA,MAAA,IAAM,eAAiB,KAAA,UAAA,EAAA;AAEvB,QAAM,OAAK;AAEX,OAAM,MAAA,IAAA,KAAA,CAAa,SAAS,KAAM,YAAA,EAAA;AAChC,QAAI,OAAA;AACF,OAAO;AAAA,MACT,IAAA,KAAA,IAAiB,KAAA,CAAA,OAAA,EAAA;AACf,QAAO,MAAA,MAAA,GAAA,KAAA,CAAA,OAAA,EAAA,CAAA;AAAA,QACT,OAAA,MAAA,CAAA,IAAA,CAAA,CAAA,KAAA,KAAA;AACA,UAAI,MAAA,GAAS,QAAe,CAAA,IAAA,CAAA,IAAA,CAAA;AAC1B,UAAM,OAAA,GAAA,eAAgC,IAAA,GAAA,KAAA,UAAA,CAAA;AACtC,SAAO,CAAA,CAAA;AACL,OAAM,MAAA;AACN,QAAO,OAAA,KAAA,CAAA;AAA8B,OAAA;AACtC,KAAA,CACH,CAAO;AACL,IAAO,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MACT,OAAAA,aAAA,EAAA,EAAAC,sBAAA,CAAA,SAAA,EAAA;AAAA,QACD,KAAA,EAAAC,kBAAA,CAAA,CAAAC,SAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,EAAAA,SAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,UAAA,EAAAA,SAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA;;;;;;;;;;;"}