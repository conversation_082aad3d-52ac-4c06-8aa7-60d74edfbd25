{"version": 3, "file": "main-table.js", "sources": ["../../../../../../../packages/components/table-v2/src/renderers/main-table.tsx"], "sourcesContent": ["import Table from '../table-grid'\n\nimport type { FunctionalComponent, Ref } from 'vue'\nimport type { TableV2GridProps } from '../grid'\nimport type { TableGridInstance } from '../table-grid'\n\nexport type MainTableRendererProps = TableV2GridProps & {\n  mainTableRef: Ref<TableGridInstance | undefined>\n}\n\nconst MainTable: FunctionalComponent<MainTableRendererProps> = (\n  props: MainTableRendererProps,\n  { slots }\n) => {\n  const { mainTableRef, ...rest } = props\n  return (\n    <Table ref={mainTableRef} {...rest}>\n      {slots}\n    </Table>\n  )\n}\n\nexport default MainTable\n"], "names": ["MainTable", "slots", "rest", "mainTableRef"], "mappings": ";;;;;;;;;;;;AAUA,CAAA,KAAMA;AAEFC,EAAAA,MAAAA;AAAF,IACG,YAAA;IACG,GAAA,IAAA;MAAA,KAAA,CAAA;SAAmBC,eAAAA,CAAAA,oBAAAA,EAAAA,cAAAA,CAAAA;AAAnB,IAAA,KAAN,EAAA,YAAA;AACA,GAAA,EAAA,IAAA,CAAA,EAAA,OAAA,CAAA,KAAA,CAAA,GAAA,KAAA,GAAA;IAAA,OACcC,EAAAA,MAAAA,CAAAA,KAAAA,CAAAA;AADd,GAAA,CAAA,CACgCD;AADhC,CAAA,CAAA;AAAA,kBAAA,SAAA;;;;"}