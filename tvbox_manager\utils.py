"""
实用工具函数模块
"""
import os
from flask import current_app


def get_current_template_dir():
    """
    获取当前使用的模板目录名称
    
    始终使用tabler模板，不再检查环境变量
    
    Returns:
        str: 模板目录名称，固定为'tabler'
    """
    # 直接返回tabler作为模板目录
    return 'tabler'


def format_datetime(date_obj, format_str='%Y-%m-%d %H:%M:%S'):
    """
    格式化日期时间对象为字符串
    
    Args:
        date_obj: 日期时间对象
        format_str: 格式化字符串，默认为'%Y-%m-%d %H:%M:%S'
        
    Returns:
        str: 格式化后的日期时间字符串
    """
    if date_obj is None:
        return ''
    return date_obj.strftime(format_str)


def truncate_string(text, length=50, suffix='...'):
    """
    截断字符串，超过指定长度时添加省略号
    
    Args:
        text: 要截断的字符串
        length: 最大长度，默认为50
        suffix: 省略号，默认为'...'
        
    Returns:
        str: 截断后的字符串
    """
    if text is None:
        return ''
    if len(text) <= length:
        return text
    return text[:length] + suffix


def is_url(text):
    """
    简单判断字符串是否为URL
    
    Args:
        text: 要判断的字符串
        
    Returns:
        bool: 是否为URL
    """
    if text is None:
        return False
    return text.startswith(('http://', 'https://', 'ftp://'))


def get_file_extension(filename):
    """
    获取文件扩展名
    
    Args:
        filename: 文件名
        
    Returns:
        str: 文件扩展名，例如'.txt'
    """
    if filename is None:
        return ''
    return os.path.splitext(filename)[1].lower()


def is_allowed_file(filename, allowed_extensions=None):
    """
    检查文件扩展名是否在允许的扩展名列表中
    
    Args:
        filename: 文件名
        allowed_extensions: 允许的扩展名列表，例如['.jpg', '.png']
        
    Returns:
        bool: 是否为允许的文件类型
    """
    if allowed_extensions is None:
        allowed_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.txt', '.json', '.m3u']
        
    return get_file_extension(filename) in allowed_extensions 