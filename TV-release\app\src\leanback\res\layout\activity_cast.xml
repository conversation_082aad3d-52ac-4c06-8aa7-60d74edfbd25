<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/video"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    android:keepScreenOn="true">

    <androidx.media3.ui.PlayerView
        android:id="@+id/exo"
        style="@style/Player.Vod"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:surface_type="surface_view" />

    <include
        android:id="@+id/widget"
        layout="@layout/view_widget_cast"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <include
        android:id="@+id/control"
        layout="@layout/view_control_cast"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:visibility="gone" />

</FrameLayout>