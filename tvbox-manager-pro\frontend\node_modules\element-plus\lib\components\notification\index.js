'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var notify = require('./src/notify.js');
var notification = require('./src/notification.js');
var install = require('../../utils/vue/install.js');

const ElNotification = install.withInstallFunction(notify["default"], "$notify");

exports.notificationEmits = notification.notificationEmits;
exports.notificationProps = notification.notificationProps;
exports.notificationTypes = notification.notificationTypes;
exports.ElNotification = ElNotification;
exports["default"] = ElNotification;
//# sourceMappingURL=index.js.map
