#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
用户服务
"""

import logging
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from fastapi import HTTPException, status

from app.core.database import get_db
from app.core.security import create_password_hash, verify_password, create_access_token, create_refresh_token
from app.core.config import settings
from app.models.user import User, Role, Permission, ApiKey
from app.models.system import OperationLog

logger = logging.getLogger(__name__)

class UserService:
    """用户服务类"""
    
    def __init__(self):
        self.logger = logger
    
    async def create_default_admin(self):
        """创建默认管理员用户"""
        db = next(get_db())
        try:
            # 检查是否已存在管理员
            admin_user = db.query(User).filter(User.email == settings.ADMIN_EMAIL).first()
            if admin_user:
                self.logger.info("默认管理员用户已存在")
                return
            
            # 创建管理员角色
            admin_role = db.query(Role).filter(Role.name == "admin").first()
            if not admin_role:
                admin_role = Role(
                    name="admin",
                    display_name="管理员",
                    description="系统管理员，拥有所有权限",
                    is_system=True
                )
                db.add(admin_role)
                db.flush()
            
            # 创建用户角色
            user_role = db.query(Role).filter(Role.name == "user").first()
            if not user_role:
                user_role = Role(
                    name="user",
                    display_name="普通用户",
                    description="普通用户，基础权限",
                    is_system=True
                )
                db.add(user_role)
                db.flush()
            
            # 创建默认管理员用户
            admin_user = User(
                username=settings.ADMIN_USERNAME,
                email=settings.ADMIN_EMAIL,
                hashed_password=create_password_hash(settings.ADMIN_PASSWORD),
                full_name="系统管理员",
                role="admin",  # 设置简单角色字段
                is_active=True,
                is_verified=True,
                is_superuser=True
            )
            admin_user.roles.append(admin_role)
            
            db.add(admin_user)
            db.commit()
            
            self.logger.info(f"默认管理员用户创建成功: {settings.ADMIN_EMAIL}")
            
        except Exception as e:
            db.rollback()
            self.logger.error(f"创建默认管理员失败: {str(e)}")
            raise
        finally:
            db.close()
    
    def authenticate_user(self, db: Session, email: str, password: str) -> Optional[User]:
        """用户认证"""
        try:
            # 查找用户
            user = db.query(User).filter(
                or_(User.email == email, User.username == email)
            ).first()
            
            if not user:
                return None
            
            # 检查账户状态
            if not user.is_active:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="账户已被禁用"
                )
            
            # 检查是否被锁定
            if user.locked_until and user.locked_until > datetime.utcnow():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="账户已被锁定，请稍后再试"
                )
            
            # 验证密码
            if not verify_password(password, user.hashed_password):
                # 增加失败次数
                user.failed_login_attempts += 1
                
                # 如果失败次数过多，锁定账户
                if user.failed_login_attempts >= 5:
                    user.locked_until = datetime.utcnow() + timedelta(minutes=30)
                    self.logger.warning(f"用户账户被锁定: {user.email}")
                
                db.commit()
                return None
            
            # 登录成功，重置失败次数
            user.failed_login_attempts = 0
            user.locked_until = None
            user.last_login_at = datetime.utcnow()
            user.login_count += 1
            
            db.commit()
            return user
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"用户认证失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="认证服务异常"
            )
    
    def create_user_tokens(self, user: User) -> Dict[str, str]:
        """创建用户令牌"""
        # 准备令牌数据
        token_data = {
            "sub": str(user.id),
            "email": user.email,
            "username": user.username,
            "roles": [role.name for role in user.roles],
            "permissions": user.permissions
        }
        
        # 创建访问令牌和刷新令牌
        access_token = create_access_token(token_data)
        refresh_token = create_refresh_token({"sub": str(user.id)})
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer"
        }
    
    def create_user(self, db: Session, user_data: Dict[str, Any]) -> User:
        """创建用户"""
        try:
            # 检查邮箱是否已存在
            existing_user = db.query(User).filter(User.email == user_data["email"]).first()
            if existing_user:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="邮箱已被注册"
                )
            
            # 检查用户名是否已存在
            existing_username = db.query(User).filter(User.username == user_data["username"]).first()
            if existing_username:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="用户名已被使用"
                )
            
            # 创建用户
            user = User(
                username=user_data["username"],
                email=user_data["email"],
                hashed_password=create_password_hash(user_data["password"]),
                full_name=user_data.get("full_name", ""),
                is_active=True,
                is_verified=False
            )
            
            # 分配默认角色
            default_role = db.query(Role).filter(Role.name == "user").first()
            if default_role:
                user.roles.append(default_role)
            
            db.add(user)
            db.commit()
            db.refresh(user)
            
            self.logger.info(f"用户创建成功: {user.email}")
            return user
            
        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            self.logger.error(f"创建用户失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="创建用户失败"
            )
    
    def get_user_by_id(self, db: Session, user_id: int) -> Optional[User]:
        """根据ID获取用户"""
        return db.query(User).filter(User.id == user_id).first()
    
    def get_user_by_email(self, db: Session, email: str) -> Optional[User]:
        """根据邮箱获取用户"""
        return db.query(User).filter(User.email == email).first()

    def get_user_by_username(self, db: Session, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        return db.query(User).filter(User.username == username).first()
    
    def get_users(self, db: Session, skip: int = 0, limit: int = 100, **filters) -> tuple[List[User], int]:
        """获取用户列表"""
        query = db.query(User)

        # 应用筛选条件
        if 'search' in filters and filters['search']:
            search_term = f"%{filters['search']}%"
            query = query.filter(
                or_(
                    User.username.like(search_term),
                    User.email.like(search_term),
                    User.nickname.like(search_term)
                )
            )

        if 'role' in filters and filters['role']:
            query = query.filter(User.role == filters['role'])

        if 'is_active' in filters and filters['is_active'] is not None:
            query = query.filter(User.is_active == filters['is_active'])

        # 获取总数
        total = query.count()

        # 获取分页数据
        users = query.offset(skip).limit(limit).all()

        return users, total
    
    def update_user(self, db: Session, user_id: int, user_data: Dict[str, Any]) -> User:
        """更新用户信息"""
        try:
            user = self.get_user_by_id(db, user_id)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="用户不存在"
                )
            
            # 更新字段
            for field, value in user_data.items():
                if field == "password":
                    user.hashed_password = create_password_hash(value)
                elif hasattr(user, field):
                    setattr(user, field, value)
            
            db.commit()
            db.refresh(user)
            
            self.logger.info(f"用户信息更新成功: {user.email}")
            return user
            
        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            self.logger.error(f"更新用户失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="更新用户失败"
            )
    
    def delete_user(self, db: Session, user_id: int) -> bool:
        """删除用户"""
        try:
            user = self.get_user_by_id(db, user_id)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="用户不存在"
                )
            
            # 不能删除超级用户
            if user.is_superuser:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="不能删除超级用户"
                )
            
            db.delete(user)
            db.commit()
            
            self.logger.info(f"用户删除成功: {user.email}")
            return True
            
        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            self.logger.error(f"删除用户失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="删除用户失败"
            )
