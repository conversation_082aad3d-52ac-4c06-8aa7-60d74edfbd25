#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
TVBox助手Web API接口
提供后台数据处理接口
"""

import os
import json
import asyncio
import logging
import traceback
import re
from flask import (
    Blueprint, request, jsonify, current_app, abort
)
from werkzeug.utils import secure_filename
from urllib.parse import urlparse

from ..utils import TVBoxParser, ResourceDownloader, ConfigProcessor
from ..utils.decryptor import TVBoxDecryptor

# 创建日志记录器
logger = logging.getLogger('tvbox_api')

# 创建蓝图
bp = Blueprint('api', __name__, url_prefix='/api')

def show_error_context(content, error_position, context_lines=5):
    """显示JSON错误周围的上下文"""
    lines = content.splitlines()
    
    # 找出错误位置所在的行
    line_count = 0
    char_count = 0
    error_line = 0
    for i, line in enumerate(lines):
        char_count += len(line) + 1  # +1 for newline
        if char_count >= error_position:
            error_line = i
            break
        line_count += 1
    
    # 计算要显示的行范围
    start_line = max(0, error_line - context_lines)
    end_line = min(len(lines), error_line + context_lines + 1)
    
    # 构建显示内容
    error_context = []
    for i in range(start_line, end_line):
        prefix = ">> " if i == error_line else "   "
        error_context.append(f"{prefix}{i+1:4d}: {lines[i]}")
    
    return "\n".join(error_context)

def remove_comments_preserve_url(content):
    """移除注释但保留URL结构"""
    # 分析内容的行
    lines = content.splitlines()
    cleaned_lines = []
    
    for line in lines:
        # 过滤掉整行注释 (以//开头的行，前面可能有空白)
        if re.match(r'^\s*//.*$', line):
            continue
        
        # 过滤掉行内注释，但保护URL中的 '//'
        # 先标记所有URL中的 '//' 为特殊标记
        line = re.sub(r'(https?:)//', r'\1URLSLASHSLASH', line)
        # 移除注释
        line = re.sub(r'//.*$', '', line)
        # 恢复URL
        line = re.sub(r'URLSLASHSLASH', '//', line)
        
        cleaned_lines.append(line)
    
    # 过滤掉多行注释，但保留格式
    content = '\n'.join(cleaned_lines)
    content = re.sub(r'/\*.*?\*/', '', content, flags=re.DOTALL)
    
    return content

@bp.route('/test', methods=['GET'])
def test_api():
    """测试API是否正常工作"""
    logger.info("API测试请求")
    return jsonify({
        'success': True,
        'message': 'API正常工作',
        'data': {
            'version': getattr(__import__('tvbox_assistant'), '__version__', '1.0.0')
        }
    })

@bp.route('/parse', methods=['POST'])
def parse_config():
    """解析TVBox配置文件"""
    try:
        logger.info("接收到解析配置文件请求")
        if 'file' not in request.files:
            logger.warning("未找到上传文件")
            return jsonify({
                'success': False,
                'message': '未找到文件'
            }), 400
        
        file = request.files['file']
        if file.filename == '':
            logger.warning("未选择文件")
            return jsonify({
                'success': False,
                'message': '未选择文件'
            }), 400
        
        # 保存上传文件
        filename = secure_filename(file.filename)
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)
        
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        file.save(file_path)
        logger.info(f"文件已保存到 {file_path}")
        
        # 解析配置文件
        parser = TVBoxParser()
        logger.debug("开始解析配置文件")
        parse_result = parser.parse_config(file_path)
        
        # 提取资源信息
        resources = parse_result['resources']
        
        # 分析加密内容
        encrypted_count = sum(1 for info in resources.values() if info.get('encrypted', False))
        logger.info(f"发现 {len(resources)} 个远程资源，其中 {encrypted_count} 个可能需要解密")
        
        # 提取配置信息
        processor = ConfigProcessor()
        config_info = processor.extract_config_info(parse_result['config'])
        
        # 将资源信息转换为可序列化格式
        serializable_resources = {}
        for url, info in resources.items():
            serializable_info = dict(info)
            serializable_info['path'] = str(serializable_info['path'])
            serializable_resources[url] = serializable_info
        
        # 统计资源类型数量
        resource_types = {}
        for url, info in resources.items():
            res_type = info['type']
            resource_types[res_type] = resource_types.get(res_type, 0) + 1
        
        # 解析TVBox线路配置
        try:
            decryptor = TVBoxDecryptor()
            
            # 读取配置内容
            with open(file_path, 'r', encoding='utf-8') as f:
                config_content = f.read()
            
            # 移除注释，保留URL格式
            cleaned_content = remove_comments_preserve_url(config_content)
            
            try:
                # 尝试解析JSON
                json_data = json.loads(cleaned_content)
                # 格式化输出但不添加空行
                formatted_json = json.dumps(json_data, ensure_ascii=False, indent=2, separators=(',', ':'))
                
                # 使用格式化后的内容进行解析
                lines_info = decryptor.parse_tvbox_config_content(formatted_json)
                
                # 计算站点和频道数量
                sites_count = len(lines_info.get('sites', []))
                lives_group_count = len(lines_info.get('lives', []))
                channel_count = 0
                
                # 计算直播频道总数
                if isinstance(lines_info.get('lives', []), list):
                    for live_group in lines_info.get('lives', []):
                        if isinstance(live_group, dict) and 'channels' in live_group:
                            channel_count += len(live_group.get('channels', []))
                
                logger.info(f"解析线路成功: {sites_count} 个站点, {lives_group_count} 个直播分组, {channel_count} 个直播频道")
                
            except json.JSONDecodeError as json_err:
                # 如果解析失败，显示错误上下文
                error_position = json_err.pos
                error_line = json_err.lineno
                error_col = json_err.colno
                logger.error(f"JSON解析错误: {json_err} (行 {error_line}, 列 {error_col}, 位置 {error_position})")
                
                # 显示错误周围的内容
                error_context = show_error_context(cleaned_content, error_position)
                logger.error(f"错误上下文:\n{error_context}")
                
                # 回退到原始解析方式
                lines_info = decryptor.parse_tvbox_config(parse_result['config'])
            
        except Exception as e:
            logger.error(f"解析线路失败: {str(e)}")
            lines_info = {'error': str(e)}
        
        logger.info("解析配置文件成功")
        return jsonify({
            'success': True,
            'message': '解析成功',
            'data': {
                'file_path': file_path,
                'config_info': config_info,
                'resources_count': len(resources),
                'encrypted_count': encrypted_count,
                'resource_types': resource_types,
                'resources': serializable_resources,
                'lines': lines_info
            }
        })
    
    except Exception as e:
        logger.error(f"解析配置文件失败: {str(e)}")
        logger.debug(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'解析失败: {str(e)}'
        }), 500

@bp.route('/decrypt', methods=['POST'])
def decrypt_url():
    """解密TVBox接口"""
    try:
        logger.info("接收到URL解密请求")
        data = request.get_json(force=True)
        
        # 获取输入参数
        url = data.get('url', '').strip()
        hint = data.get('hint', '').strip()
        is_config = data.get('is_config', False)
        
        if not url:
            return jsonify({
                'success': False,
                'message': '请输入URL',
                'data': None
            })
        
        logger.debug(f"解密URL: {url}, 类型提示: {hint}, 配置模式: {is_config}")
        
        # 创建解密器
        decryptor = TVBoxDecryptor()
        
        # 根据是否是配置模式选择解密方法
        if is_config:
            logger.debug("启用配置模式解析")
            result, method = decryptor.decrypt_config_url(url, hint)
            
            # 如果返回的是标准TVBox配置（包含sites字段的JSON）
            if isinstance(result, str):
                # 尝试验证是否是有效的TVBox配置
                if decryptor._validate_tvbox_config(result):
                    logger.info("成功获取有效的TVBox配置")
                    
                    # 解析配置内容获取详细信息
                    try:
                        # 移除注释，保留URL格式
                        cleaned_content = remove_comments_preserve_url(result)
                        
                        try:
                            # 尝试解析JSON
                            json_data = json.loads(cleaned_content)
                            # 格式化输出但不添加空行
                            formatted_json = json.dumps(json_data, ensure_ascii=False, indent=2, separators=(',', ':'))
                            
                            # 使用格式化后的内容进行解析
                            config_info = decryptor.parse_tvbox_config_content(formatted_json)
                        except json.JSONDecodeError as json_err:
                            logger.error(f"JSON解析错误: {json_err}")
                            # 回退到直接使用清理后的内容
                            config_info = decryptor.parse_tvbox_config_content(cleaned_content)
                        
                        # 计算站点、直播源和解析器的数量
                        sites_count = len(config_info.get('sites', []))
                        lives_group_count = len(config_info.get('lives', []))
                        channel_count = 0
                        
                        # 计算直播频道总数
                        if isinstance(config_info.get('lives', []), list):
                            for live_group in config_info.get('lives', []):
                                if isinstance(live_group, dict) and 'channels' in live_group:
                                    channel_count += len(live_group.get('channels', []))
                        
                        parses_count = len(config_info.get('parses', []))
                        
                        logger.info(f"配置解析结果: {sites_count}个站点, {lives_group_count}个直播分组, {channel_count}个频道, {parses_count}个解析器")
                        
                        # 返回配置结果
                        return jsonify({
                            'success': True,
                            'message': f'配置解析成功: {method}',
                            'data': {
                                'original': url,
                                'decrypted': result,
                                'method': method,
                                'is_config': True,
                                'config_info': {
                                    'sites_count': sites_count,
                                    'lives_count': channel_count,
                                    'lives_group_count': lives_group_count,
                                    'parses_count': parses_count,
                                    'spider': config_info.get('spider', ''),
                                    'wallpaper': config_info.get('wallpaper', ''),
                                    'logo': config_info.get('logo', '')
                                }
                            }
                        })
                    except Exception as e:
                        logger.error(f"解析配置详细信息失败: {str(e)}")
                        logger.debug(traceback.format_exc())
            
            # 返回一般解密结果
            return jsonify({
                'success': True,
                'message': f'解密成功: {method}',
                'data': {
                    'original': url,
                    'decrypted': result,
                    'method': method,
                    'is_config': False
                }
            })
        
        else:
            # 普通模式解密
            logger.debug("启用普通模式解析")
            result, method = decryptor.decrypt_content_url(url, hint)
            
            return jsonify({
                'success': True,
                'message': f'解密成功: {method}',
                'data': {
                    'original': url,
                    'decrypted': result,
                    'method': method
                }
            })
    
    except Exception as e:
        logger.error(f"解密URL失败: {str(e)}")
        logger.debug(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'解密失败: {str(e)}',
            'data': None
        }), 500

@bp.route('/parse_lines', methods=['POST'])
def parse_lines():
    """解析TVBox线路配置"""
    try:
        logger.info("接收到解析线路请求")
        data = request.get_json(force=True)
        
        # 获取输入参数
        content = data.get('content', '')
        
        if not content:
            return jsonify({
                'success': False,
                'message': '配置内容为空',
                'data': None
            })
        
        # 创建解密器
        decryptor = TVBoxDecryptor()
        
        # 移除注释，保留URL格式
        cleaned_content = remove_comments_preserve_url(content)
        
        try:
            # 尝试解析JSON
            json_data = json.loads(cleaned_content)
            # 格式化输出但不添加空行
            formatted_json = json.dumps(json_data, ensure_ascii=False, indent=2, separators=(',', ':'))
            
            # 使用格式化后的内容进行解析
            result = decryptor.parse_tvbox_config_content(formatted_json)
        except json.JSONDecodeError as json_err:
            logger.error(f"JSON解析错误: {json_err}")
            # 回退到直接使用清理后的内容
            result = decryptor.parse_tvbox_config_content(cleaned_content)
        
        # 计算站点、直播源和解析器的数量
        sites_count = len(result.get('sites', []))
        lives_group_count = len(result.get('lives', []))
        channel_count = 0
        
        # 计算直播频道总数
        if isinstance(result.get('lives', []), list):
            for live_group in result.get('lives', []):
                if isinstance(live_group, dict) and 'channels' in live_group:
                    channel_count += len(live_group.get('channels', []))
        
        parses_count = len(result.get('parses', []))
        
        # 返回结果
        return jsonify({
            'success': True,
            'message': '解析成功',
            'data': {
                'result': result,
                'info': {
                    'sites_count': sites_count,
                    'lives_group_count': lives_group_count,
                    'lives_count': channel_count,
                    'parses_count': parses_count,
                    'spider': result.get('spider', ''),
                    'wallpaper': result.get('wallpaper', ''),
                    'logo': result.get('logo', '')
                }
            }
        })
    
    except Exception as e:
        logger.error(f"解析线路失败: {str(e)}")
        logger.debug(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'解析失败: {str(e)}',
            'data': None
        }), 500

@bp.route('/process', methods=['POST'])
def process_config():
    """处理TVBox配置文件"""
    try:
        logger.info("接收到处理配置文件请求")
        data = request.get_json(force=True)
        
        file_path = data.get('file_path')
        output_dir = data.get('output_dir')
        
        if not file_path or not os.path.exists(file_path):
            return jsonify({
                'success': False,
                'message': '文件不存在'
            }), 400
        
        if not output_dir:
            output_dir = os.path.join(os.path.dirname(file_path), 'processed')
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 启动异步处理
        asyncio.run(_process_config_async(file_path, output_dir))
        
        return jsonify({
            'success': True,
            'message': '处理成功',
            'data': {
                'output_dir': output_dir
            }
        })
    
    except Exception as e:
        logger.error(f"处理配置文件失败: {str(e)}")
        logger.debug(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'处理失败: {str(e)}'
        }), 500

async def _process_config_async(file_path, output_dir):
    """异步处理配置文件"""
    try:
        logger.info(f"开始异步处理配置文件: {file_path}")
        
        # 解析配置文件
        parser = TVBoxParser()
        parse_result = parser.parse_config(file_path)
        
        # 提取资源信息
        resources = parse_result['resources']
        
        # 创建下载器
        downloader = ResourceDownloader()
        
        # 下载资源
        for url, info in resources.items():
            try:
                resource_type = info['type']
                resource_path = info['path']
                
                # 构建输出路径
                relative_path = os.path.relpath(resource_path, os.path.dirname(file_path))
                output_path = os.path.join(output_dir, relative_path)
                
                # 确保输出目录存在
                os.makedirs(os.path.dirname(output_path), exist_ok=True)
                
                logger.info(f"下载资源: {url} -> {output_path}")
                
                # 下载资源
                await downloader.download(url, output_path)
                
                logger.info(f"下载成功: {url}")
            except Exception as e:
                logger.error(f"下载资源失败 {url}: {str(e)}")
        
        # 处理配置文件
        processor = ConfigProcessor()
        output_config_path = os.path.join(output_dir, os.path.basename(file_path))
        processor.process_config(parse_result['config'], output_config_path)
        
        logger.info(f"配置文件处理完成: {output_config_path}")
        
    except Exception as e:
        logger.error(f"异步处理配置文件失败: {str(e)}")
        logger.debug(traceback.format_exc())

@bp.route('/configs', methods=['GET'])
def get_configs():
    """获取配置文件列表"""
    try:
        logger.info("接收到获取配置文件列表请求")
        
        upload_dir = current_app.config['UPLOAD_FOLDER']
        if not os.path.exists(upload_dir):
            return jsonify({
                'success': True,
                'message': '无配置文件',
                'data': []
            })
        
        configs = []
        for filename in os.listdir(upload_dir):
            if filename.endswith('.json'):
                file_path = os.path.join(upload_dir, filename)
                file_size = os.path.getsize(file_path)
                file_time = os.path.getmtime(file_path)
                
                configs.append({
                    'name': filename,
                    'path': file_path,
                    'size': file_size,
                    'time': file_time
                })
        
        # 按修改时间排序
        configs.sort(key=lambda x: x['time'], reverse=True)
        
        return jsonify({
            'success': True,
            'message': f'找到 {len(configs)} 个配置文件',
            'data': configs
        })
    
    except Exception as e:
        logger.error(f"获取配置文件列表失败: {str(e)}")
        logger.debug(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'获取失败: {str(e)}'
        }), 500

@bp.route('/configs/<filename>', methods=['GET'])
def get_config_content(filename):
    """获取配置文件内容"""
    try:
        logger.info(f"接收到获取配置文件内容请求: {filename}")
        
        upload_dir = current_app.config['UPLOAD_FOLDER']
        file_path = os.path.join(upload_dir, secure_filename(filename))
        
        if not os.path.exists(file_path):
            return jsonify({
                'success': False,
                'message': '文件不存在'
            }), 404
        
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 获取文件信息
        file_size = os.path.getsize(file_path)
        file_time = os.path.getmtime(file_path)
        
        # 创建解密器
        decryptor = TVBoxDecryptor()
        
        # 解析配置内容
        try:
            # 移除注释，保留URL格式
            cleaned_content = remove_comments_preserve_url(content)
            
            try:
                # 尝试解析JSON
                json_data = json.loads(cleaned_content)
                # 格式化输出但不添加空行
                formatted_json = json.dumps(json_data, ensure_ascii=False, indent=2, separators=(',', ':'))
                
                # 使用格式化后的内容进行解析
                config_info = decryptor.parse_tvbox_config_content(formatted_json)
            except json.JSONDecodeError as json_err:
                logger.error(f"JSON解析错误: {json_err}")
                # 回退到原始解析方法
                config_info = decryptor.parse_tvbox_config_content(cleaned_content)
            
            # 计算站点、直播源和解析器的数量
            sites_count = len(config_info.get('sites', []))
            lives_group_count = len(config_info.get('lives', []))
            channel_count = 0
            
            # 计算直播频道总数
            if isinstance(config_info.get('lives', []), list):
                for live_group in config_info.get('lives', []):
                    if isinstance(live_group, dict) and 'channels' in live_group:
                        channel_count += len(live_group.get('channels', []))
            
            parses_count = len(config_info.get('parses', []))
            
            info = {
                'sites_count': sites_count,
                'lives_group_count': lives_group_count,
                'lives_count': channel_count,
                'parses_count': parses_count,
                'spider': config_info.get('spider', ''),
                'wallpaper': config_info.get('wallpaper', ''),
                'logo': config_info.get('logo', '')
            }
        except Exception as e:
            logger.error(f"解析配置内容失败: {str(e)}")
            info = {
                'error': str(e)
            }
        
        return jsonify({
            'success': True,
            'message': '获取成功',
            'data': {
                'name': filename,
                'path': file_path,
                'size': file_size,
                'time': file_time,
                'content': content,
                'info': info
            }
        })
    
    except Exception as e:
        logger.error(f"获取配置文件内容失败: {str(e)}")
        logger.debug(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'获取失败: {str(e)}'
        }), 500

@bp.errorhandler(404)
def api_not_found(e):
    """API路由未找到处理程序"""
    logger.warning(f"API路由未找到: {request.path}")
    return jsonify({
        'success': False,
        'message': f'API路由未找到: {request.path}',
        'error': 'Not Found',
        'code': 404
    }), 404

@bp.errorhandler(500)
def server_error(e):
    """服务器错误处理程序"""
    logger.error(f"服务器错误: {str(e)}")
    return jsonify({
        'success': False,
        'message': f'服务器错误: {str(e)}',
        'error': 'Internal Server Error',
        'code': 500
    }), 500 