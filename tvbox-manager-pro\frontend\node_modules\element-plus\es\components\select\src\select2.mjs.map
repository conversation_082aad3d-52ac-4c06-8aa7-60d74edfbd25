{"version": 3, "file": "select2.mjs", "sources": ["../../../../../../packages/components/select/src/select.vue"], "sourcesContent": ["<template>\n  <div\n    ref=\"selectRef\"\n    v-click-outside:[popperRef]=\"handleClickOutside\"\n    :class=\"[nsSelect.b(), nsSelect.m(selectSize)]\"\n    @[mouseEnterEventName]=\"states.inputHovering = true\"\n    @mouseleave=\"states.inputHovering = false\"\n  >\n    <el-tooltip\n      ref=\"tooltipRef\"\n      :visible=\"dropdownMenuVisible\"\n      :placement=\"placement\"\n      :teleported=\"teleported\"\n      :popper-class=\"[nsSelect.e('popper'), popperClass]\"\n      :popper-options=\"popperOptions\"\n      :fallback-placements=\"fallbackPlacements\"\n      :effect=\"effect\"\n      pure\n      trigger=\"click\"\n      :transition=\"`${nsSelect.namespace.value}-zoom-in-top`\"\n      :stop-popper-mouse-event=\"false\"\n      :gpu-acceleration=\"false\"\n      :persistent=\"persistent\"\n      :append-to=\"appendTo\"\n      :show-arrow=\"showArrow\"\n      :offset=\"offset\"\n      @before-show=\"handleMenuEnter\"\n      @hide=\"states.isBeforeHide = false\"\n    >\n      <template #default>\n        <div\n          ref=\"wrapperRef\"\n          :class=\"[\n            nsSelect.e('wrapper'),\n            nsSelect.is('focused', isFocused),\n            nsSelect.is('hovering', states.inputHovering),\n            nsSelect.is('filterable', filterable),\n            nsSelect.is('disabled', selectDisabled),\n          ]\"\n          @click.prevent=\"toggleMenu\"\n        >\n          <div\n            v-if=\"$slots.prefix\"\n            ref=\"prefixRef\"\n            :class=\"nsSelect.e('prefix')\"\n          >\n            <slot name=\"prefix\" />\n          </div>\n          <div\n            ref=\"selectionRef\"\n            :class=\"[\n              nsSelect.e('selection'),\n              nsSelect.is(\n                'near',\n                multiple && !$slots.prefix && !!states.selected.length\n              ),\n            ]\"\n          >\n            <slot\n              v-if=\"multiple\"\n              name=\"tag\"\n              :data=\"states.selected\"\n              :delete-tag=\"deleteTag\"\n              :select-disabled=\"selectDisabled\"\n            >\n              <div\n                v-for=\"item in showTagList\"\n                :key=\"getValueKey(item)\"\n                :class=\"nsSelect.e('selected-item')\"\n              >\n                <el-tag\n                  :closable=\"!selectDisabled && !item.isDisabled\"\n                  :size=\"collapseTagSize\"\n                  :type=\"tagType\"\n                  :effect=\"tagEffect\"\n                  disable-transitions\n                  :style=\"tagStyle\"\n                  @close=\"deleteTag($event, item)\"\n                >\n                  <span :class=\"nsSelect.e('tags-text')\">\n                    <slot\n                      name=\"label\"\n                      :label=\"item.currentLabel\"\n                      :value=\"item.value\"\n                    >\n                      {{ item.currentLabel }}\n                    </slot>\n                  </span>\n                </el-tag>\n              </div>\n\n              <el-tooltip\n                v-if=\"collapseTags && states.selected.length > maxCollapseTags\"\n                ref=\"tagTooltipRef\"\n                :disabled=\"dropdownMenuVisible || !collapseTagsTooltip\"\n                :fallback-placements=\"['bottom', 'top', 'right', 'left']\"\n                :effect=\"effect\"\n                placement=\"bottom\"\n                :popper-class=\"popperClass\"\n                :teleported=\"teleported\"\n              >\n                <template #default>\n                  <div\n                    ref=\"collapseItemRef\"\n                    :class=\"nsSelect.e('selected-item')\"\n                  >\n                    <el-tag\n                      :closable=\"false\"\n                      :size=\"collapseTagSize\"\n                      :type=\"tagType\"\n                      :effect=\"tagEffect\"\n                      disable-transitions\n                      :style=\"collapseTagStyle\"\n                    >\n                      <span :class=\"nsSelect.e('tags-text')\">\n                        + {{ states.selected.length - maxCollapseTags }}\n                      </span>\n                    </el-tag>\n                  </div>\n                </template>\n                <template #content>\n                  <div ref=\"tagMenuRef\" :class=\"nsSelect.e('selection')\">\n                    <div\n                      v-for=\"item in collapseTagList\"\n                      :key=\"getValueKey(item)\"\n                      :class=\"nsSelect.e('selected-item')\"\n                    >\n                      <el-tag\n                        class=\"in-tooltip\"\n                        :closable=\"!selectDisabled && !item.isDisabled\"\n                        :size=\"collapseTagSize\"\n                        :type=\"tagType\"\n                        :effect=\"tagEffect\"\n                        disable-transitions\n                        @close=\"deleteTag($event, item)\"\n                      >\n                        <span :class=\"nsSelect.e('tags-text')\">\n                          <slot\n                            name=\"label\"\n                            :label=\"item.currentLabel\"\n                            :value=\"item.value\"\n                          >\n                            {{ item.currentLabel }}\n                          </slot>\n                        </span>\n                      </el-tag>\n                    </div>\n                  </div>\n                </template>\n              </el-tooltip>\n            </slot>\n            <div\n              :class=\"[\n                nsSelect.e('selected-item'),\n                nsSelect.e('input-wrapper'),\n                nsSelect.is('hidden', !filterable),\n              ]\"\n            >\n              <input\n                :id=\"inputId\"\n                ref=\"inputRef\"\n                v-model=\"states.inputValue\"\n                type=\"text\"\n                :name=\"name\"\n                :class=\"[nsSelect.e('input'), nsSelect.is(selectSize)]\"\n                :disabled=\"selectDisabled\"\n                :autocomplete=\"autocomplete\"\n                :style=\"inputStyle\"\n                :tabindex=\"tabindex\"\n                role=\"combobox\"\n                :readonly=\"!filterable\"\n                spellcheck=\"false\"\n                :aria-activedescendant=\"hoverOption?.id || ''\"\n                :aria-controls=\"contentId\"\n                :aria-expanded=\"dropdownMenuVisible\"\n                :aria-label=\"ariaLabel\"\n                aria-autocomplete=\"none\"\n                aria-haspopup=\"listbox\"\n                @keydown.down.stop.prevent=\"navigateOptions('next')\"\n                @keydown.up.stop.prevent=\"navigateOptions('prev')\"\n                @keydown.esc.stop.prevent=\"handleEsc\"\n                @keydown.enter.stop.prevent=\"selectOption\"\n                @keydown.delete.stop=\"deletePrevTag\"\n                @compositionstart=\"handleCompositionStart\"\n                @compositionupdate=\"handleCompositionUpdate\"\n                @compositionend=\"handleCompositionEnd\"\n                @input=\"onInput\"\n                @click.stop=\"toggleMenu\"\n              />\n              <span\n                v-if=\"filterable\"\n                ref=\"calculatorRef\"\n                aria-hidden=\"true\"\n                :class=\"nsSelect.e('input-calculator')\"\n                v-text=\"states.inputValue\"\n              />\n            </div>\n            <div\n              v-if=\"shouldShowPlaceholder\"\n              :class=\"[\n                nsSelect.e('selected-item'),\n                nsSelect.e('placeholder'),\n                nsSelect.is(\n                  'transparent',\n                  !hasModelValue || (expanded && !states.inputValue)\n                ),\n              ]\"\n            >\n              <slot\n                v-if=\"hasModelValue\"\n                name=\"label\"\n                :label=\"currentPlaceholder\"\n                :value=\"modelValue\"\n              >\n                <span>{{ currentPlaceholder }}</span>\n              </slot>\n              <span v-else>{{ currentPlaceholder }}</span>\n            </div>\n          </div>\n          <div ref=\"suffixRef\" :class=\"nsSelect.e('suffix')\">\n            <el-icon\n              v-if=\"iconComponent && !showClose\"\n              :class=\"[nsSelect.e('caret'), nsSelect.e('icon'), iconReverse]\"\n            >\n              <component :is=\"iconComponent\" />\n            </el-icon>\n            <el-icon\n              v-if=\"showClose && clearIcon\"\n              :class=\"[\n                nsSelect.e('caret'),\n                nsSelect.e('icon'),\n                nsSelect.e('clear'),\n              ]\"\n              @click=\"handleClearClick\"\n            >\n              <component :is=\"clearIcon\" />\n            </el-icon>\n            <el-icon\n              v-if=\"validateState && validateIcon && needStatusIcon\"\n              :class=\"[\n                nsInput.e('icon'),\n                nsInput.e('validateIcon'),\n                nsInput.is('loading', validateState === 'validating'),\n              ]\"\n            >\n              <component :is=\"validateIcon\" />\n            </el-icon>\n          </div>\n        </div>\n      </template>\n      <template #content>\n        <el-select-menu ref=\"menuRef\">\n          <div\n            v-if=\"$slots.header\"\n            :class=\"nsSelect.be('dropdown', 'header')\"\n            @click.stop\n          >\n            <slot name=\"header\" />\n          </div>\n          <el-scrollbar\n            v-show=\"states.options.size > 0 && !loading\"\n            :id=\"contentId\"\n            ref=\"scrollbarRef\"\n            tag=\"ul\"\n            :wrap-class=\"nsSelect.be('dropdown', 'wrap')\"\n            :view-class=\"nsSelect.be('dropdown', 'list')\"\n            :class=\"[nsSelect.is('empty', filteredOptionsCount === 0)]\"\n            role=\"listbox\"\n            :aria-label=\"ariaLabel\"\n            aria-orientation=\"vertical\"\n            @scroll=\"popupScroll\"\n          >\n            <el-option\n              v-if=\"showNewOption\"\n              :value=\"states.inputValue\"\n              :created=\"true\"\n            />\n            <el-options>\n              <slot />\n            </el-options>\n          </el-scrollbar>\n          <div\n            v-if=\"$slots.loading && loading\"\n            :class=\"nsSelect.be('dropdown', 'loading')\"\n          >\n            <slot name=\"loading\" />\n          </div>\n          <div\n            v-else-if=\"loading || filteredOptionsCount === 0\"\n            :class=\"nsSelect.be('dropdown', 'empty')\"\n          >\n            <slot name=\"empty\">\n              <span>{{ emptyText }}</span>\n            </slot>\n          </div>\n          <div\n            v-if=\"$slots.footer\"\n            :class=\"nsSelect.be('dropdown', 'footer')\"\n            @click.stop\n          >\n            <slot name=\"footer\" />\n          </div>\n        </el-select-menu>\n      </template>\n    </el-tooltip>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { computed, defineComponent, getCurrentInstance, onBeforeUnmount, provide, reactive, toRefs, watch } from 'vue'\nimport { ClickOutside } from '@element-plus/directives'\nimport ElTooltip from '@element-plus/components/tooltip'\nimport ElScrollbar from '@element-plus/components/scrollbar'\nimport ElTag from '@element-plus/components/tag'\nimport ElIcon from '@element-plus/components/icon'\nimport { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { flattedChildren, isArray, isObject } from '@element-plus/utils'\nimport { useCalcInputWidth } from '@element-plus/hooks'\nimport ElOption from './option.vue'\nimport ElSelectMenu from './select-dropdown.vue'\nimport { useSelect } from './useSelect'\nimport { selectKey } from './token'\nimport ElOptions from './options'\nimport { selectProps } from './select'\n\nimport type { VNode } from 'vue';\nimport type { SelectContext } from './type'\n\nconst COMPONENT_NAME = 'ElSelect'\nexport default defineComponent({\n  name: COMPONENT_NAME,\n  componentName: COMPONENT_NAME,\n  components: {\n    ElSelectMenu,\n    ElOption,\n    ElOptions,\n    ElTag,\n    ElScrollbar,\n    ElTooltip,\n    ElIcon,\n  },\n  directives: { ClickOutside },\n  props: selectProps,\n  emits: [\n    UPDATE_MODEL_EVENT,\n    CHANGE_EVENT,\n    'remove-tag',\n    'clear',\n    'visible-change',\n    'focus',\n    'blur',\n    'popup-scroll',\n  ],\n\n  setup(props, { emit, slots }) {\n    const instance = getCurrentInstance()!\n    instance.appContext.config.warnHandler = (...args) => {\n      // Overrides warnings about slots not being executable outside of a render function.\n      // We call slot below just to simulate data when persist is false, this warning message should be ignored\n      if (!args[0] || args[0].includes('Slot \"default\" invoked outside of the render function')) {\n        return\n      }\n      // eslint-disable-next-line no-console\n      console.warn(...args)\n    }\n    const modelValue = computed(() => {\n      const { modelValue: rawModelValue, multiple } = props\n      const fallback = multiple ? [] : undefined\n      // When it is array, we check if this is multi-select.\n      // Based on the result we get\n      if (isArray(rawModelValue)) {\n        return multiple ? rawModelValue : fallback\n      }\n\n      return multiple ? fallback : rawModelValue\n    })\n\n    const _props = reactive({\n      ...toRefs(props),\n      modelValue,\n    })\n\n    const API = useSelect(_props, emit)\n    const { calculatorRef, inputStyle } = useCalcInputWidth()\n\n    const flatTreeSelectData = (data: any[]) => {\n      return data.reduce((acc, item) => {\n        acc.push(item)\n        if (item.children && item.children.length > 0) {\n          acc.push(...flatTreeSelectData(item.children))\n        }\n        return acc\n      }, [])\n    }\n\n    const manuallyRenderSlots = (vnodes: VNode[] | undefined) => {\n      // After option rendering is completed, the useSelect internal state can collect the value of each option.\n      // If the persistent value is false, option will not be rendered by default, so in this case,\n      // manually render and load option data here.\n      const children = flattedChildren(vnodes || []) as VNode[]\n      children.forEach((item) => {\n        // @ts-expect-error\n        if (isObject(item) && (item.type.name === 'ElOption' || item.type.name === 'ElTree')) {\n          // @ts-expect-error\n          const _name = item.type.name\n          if (_name === 'ElTree') {\n            // tree-select component is a special case.\n            // So we need to handle it separately.\n            const treeData = item.props?.data || []\n            const flatData = flatTreeSelectData(treeData)\n            flatData.forEach((treeItem: any) => {\n              treeItem.currentLabel = treeItem.label || (isObject(treeItem.value) ? '' : treeItem.value)\n              API.onOptionCreate(treeItem)\n            })\n          } else if (_name === 'ElOption') {\n            const obj = { ...item.props } as any\n            obj.currentLabel = obj.label || (isObject(obj.value) ? '' : obj.value)\n            API.onOptionCreate(obj)\n          }\n        }\n      })\n    }\n    watch(() => {\n      const slotsContent = slots.default?.()\n      return slotsContent\n    }, newSlot => {\n      if (props.persistent) {\n        // If persistent is true, we don't need to manually render slots.\n        return\n      }\n      manuallyRenderSlots(newSlot)\n    }, {\n      immediate: true,\n    })\n\n    provide(\n      selectKey,\n      reactive({\n        props: _props,\n        states: API.states,\n        selectRef: API.selectRef,\n        optionsArray: API.optionsArray,\n        setSelected: API.setSelected,\n        handleOptionSelect: API.handleOptionSelect,\n        onOptionCreate: API.onOptionCreate,\n        onOptionDestroy: API.onOptionDestroy,\n      }) satisfies SelectContext\n    )\n\n    const selectedLabel = computed(() => {\n      if (!props.multiple) {\n        return API.states.selectedLabel\n      }\n      return API.states.selected.map((i) => i.currentLabel as string)\n    })\n\n    onBeforeUnmount(() => {\n      // https://github.com/element-plus/element-plus/issues/21279\n      instance.appContext.config.warnHandler = undefined\n    })\n\n    return {\n      ...API,\n      modelValue,\n      selectedLabel,\n      calculatorRef,\n      inputStyle,\n    }\n  },\n})\n</script>\n"], "names": ["ElOption", "_normalizeClass", "_createVNode", "_withCtx", "_createElementVNode", "_withModifiers", "_openBlock", "_createElementBlock", "_renderSlot", "_Fragment", "_renderList", "_normalizeStyle", "_createTextVNode", "_toDisplayString", "_createBlock", "_createCommentVNode", "_withDirectives", "_with<PERSON><PERSON><PERSON>", "_vModelText", "_resolveDynamicComponent"], "mappings": ";;;;;;;;;;;;;;;;;;AAwUA,MAAM,cAAiB,GAAA,UAAA,CAAA;AACvB,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,cAAA;AAAA,EACN,aAAe,EAAA,cAAA;AAAA,EACf,UAAY,EAAA;AAAA,IACV,YAAA;AAAA,cACAA,MAAA;AAAA,IACA,SAAA;AAAA,IACA,KAAA;AAAA,IACA,WAAA;AAAA,IACA,SAAA;AAAA,IACA,MAAA;AAAA,GACF;AAAA,EACA,UAAA,EAAY,EAAE,YAAa,EAAA;AAAA,EAC3B,KAAO,EAAA,WAAA;AAAA,EACP,KAAO,EAAA;AAAA,IACL,kBAAA;AAAA,IACA,YAAA;AAAA,IACA,YAAA;AAAA,IACA,OAAA;AAAA,IACA,gBAAA;AAAA,IACA,OAAA;AAAA,IACA,MAAA;AAAA,IACA,cAAA;AAAA,GACF;AAAA,EAEA,KAAM,CAAA,KAAA,EAAO,EAAE,IAAA,EAAM,OAAS,EAAA;AAC5B,IAAA,MAAM,WAAW,kBAAmB,EAAA,CAAA;AACpC,IAAA,QAAA,CAAS,UAAW,CAAA,MAAA,CAAO,WAAc,GAAA,CAAA,GAAI,IAAS,KAAA;AAGpD,MAAI,IAAA,CAAC,KAAK,CAAC,CAAA,IAAK,KAAK,CAAC,CAAA,CAAE,QAAS,CAAA,uDAAuD,CAAG,EAAA;AACzF,QAAA,OAAA;AAAA,OACF;AAEA,MAAQ,OAAA,CAAA,IAAA,CAAK,GAAG,IAAI,CAAA,CAAA;AAAA,KACtB,CAAA;AACA,IAAM,MAAA,UAAA,GAAa,SAAS,MAAM;AAChC,MAAA,MAAM,EAAE,UAAA,EAAY,aAAe,EAAA,QAAA,EAAa,GAAA,KAAA,CAAA;AAChD,MAAM,MAAA,QAAA,GAAW,QAAW,GAAA,EAAK,GAAA,KAAA,CAAA,CAAA;AAGjC,MAAI,IAAA,OAAA,CAAQ,aAAa,CAAG,EAAA;AAC1B,QAAA,OAAO,WAAW,aAAgB,GAAA,QAAA,CAAA;AAAA,OACpC;AAEA,MAAA,OAAO,WAAW,QAAW,GAAA,aAAA,CAAA;AAAA,KAC9B,CAAA,CAAA;AAED,IAAA,MAAM,SAAS,QAAS,CAAA;AAAA,MACtB,GAAG,OAAO,KAAK,CAAA;AAAA,MACf,UAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,GAAA,GAAM,SAAU,CAAA,MAAA,EAAQ,IAAI,CAAA,CAAA;AAClC,IAAA,MAAM,EAAE,aAAA,EAAe,UAAW,EAAA,GAAI,iBAAkB,EAAA,CAAA;AAExD,IAAM,MAAA,kBAAA,GAAqB,CAAC,IAAgB,KAAA;AAC1C,MAAA,OAAO,IAAK,CAAA,MAAA,CAAO,CAAC,GAAA,EAAK,IAAS,KAAA;AAChC,QAAA,GAAA,CAAI,KAAK,IAAI,CAAA,CAAA;AACb,QAAA,IAAI,IAAK,CAAA,QAAA,IAAY,IAAK,CAAA,QAAA,CAAS,SAAS,CAAG,EAAA;AAC7C,UAAA,GAAA,CAAI,IAAK,CAAA,GAAG,kBAAmB,CAAA,IAAA,CAAK,QAAQ,CAAC,CAAA,CAAA;AAAA,SAC/C;AACA,QAAO,OAAA,GAAA,CAAA;AAAA,OACT,EAAG,EAAE,CAAA,CAAA;AAAA,KACP,CAAA;AAEA,IAAM,MAAA,mBAAA,GAAsB,CAAC,MAAgC,KAAA;AAI3D,MAAA,MAAM,QAAW,GAAA,eAAA,CAAgB,MAAU,IAAA,EAAE,CAAA,CAAA;AAC7C,MAAS,QAAA,CAAA,OAAA,CAAQ,CAAC,IAAS,KAAA;AAEzB,QAAI,IAAA,EAAA,CAAA;AAEF,QAAM,IAAA,QAAA,CAAA,UAAkB,IAAA,CAAA,IAAA,CAAA,IAAA,KAAA,UAAA,IAAA,IAAA,CAAA,IAAA,CAAA,IAAA,KAAA,QAAA,CAAA,EAAA;AACxB,UAAA,cAAc,IAAU,CAAA,IAAA,CAAA,IAAA,CAAA;AAGtB,UAAA,IAAA,KAAiB,KAAA,QAAA,EAAK;AACtB,YAAM,MAAA,QAAA,GAAW,sBAA2B,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,KAAA,EAAA,CAAA;AAC5C,YAAS,MAAA,QAAA,GAAQ,kBAAmB,CAAA,QAAA,CAAA,CAAA;AAClC,YAAS,QAAA,CAAA,OAAA,CAAA,CAAA,QAAe;AACxB,cAAA,qBAA2B,GAAA,QAAA,CAAA,KAAA,KAAA,QAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,EAAA,GAAA,QAAA,CAAA,KAAA,CAAA,CAAA;AAAA,cAC5B,GAAA,CAAA,cAAA,CAAA,QAAA,CAAA,CAAA;AAAA,aACH,CAAA,CAAA;AACE,WAAA,MAAA,IAAY,KAAE,KAAG,UAAW,EAAA;AAC5B,YAAI,MAAA,GAAA,GAAA,EAAA,GAAA,UAA6B,EAAA,CAAA;AACjC,YAAA,GAAA,CAAI,eAAe,GAAG,CAAA,KAAA,KAAA,QAAA,CAAA,GAAA,CAAA,KAAA,CAAA,GAAA,EAAA,GAAA,GAAA,CAAA,KAAA,CAAA,CAAA;AAAA,YACxB,GAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA;AAAA,WACF;AAAA,SACD;AAAA,OACH,CAAA,CAAA;AACA,KAAA,CAAA;AACE,IAAM,KAAA,CAAA,MAAA;AACN,MAAO,IAAA,EAAA,CAAA;AAAA,YACK,YAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,OAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AACZ,MAAA,mBAAsB,CAAA;AAEpB,KAAA,EAAA,CAAA,OAAA,KAAA;AAAA,MACF,IAAA,KAAA,CAAA,UAAA,EAAA;AACA,QAAA,OAAA;AAA2B,OAC1B;AAAA,MACD,mBAAW,CAAA,OAAA,CAAA,CAAA;AAAA,KACZ,EAAA;AAED,MAAA,SAAA,EAAA,IAAA;AAAA,KACE,CAAA,CAAA;AAAA,IAAA,OACS,CAAA,SAAA,EAAA,QAAA,CAAA;AAAA,MAAA,KACA,EAAA,MAAA;AAAA,MAAA,WACK,CAAA,MAAA;AAAA,MAAA,cACG,CAAA,SAAA;AAAA,MAAA,iBACG,CAAA,YAAA;AAAA,MAAA,gBACD,CAAA,WAAA;AAAA,MAAA,uBACO,CAAA,kBAAA;AAAA,MAAA,mBACJ,CAAA,cAAA;AAAA,MAAA,oBACC,CAAA,eAAA;AAAA,KAAA,CACvB,CAAC,CAAA;AAAA,IACH,MAAA,aAAA,GAAA,QAAA,CAAA,MAAA;AAEA,MAAM,IAAA,CAAA,KAAA,CAAA,QAAA;AACJ,QAAI,UAAiB,CAAA,MAAA,CAAA,aAAA,CAAA;AACnB,OAAA;AAAkB,MACpB,OAAA,GAAA,CAAA,MAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,YAAA,CAAA,CAAA;AACA,KAAA,CAAA,CAAA;AAA8D,IAChE,eAAC,CAAA,MAAA;AAED,MAAA,QAAA,CAAA,UAAsB,CAAA,MAAA,CAAA,WAAA,GAAA,KAAA,CAAA,CAAA;AAEpB,KAAS,CAAA,CAAA;AAAgC,IAC3C,OAAC;AAED,MAAO,GAAA,GAAA;AAAA,MACL,UAAG;AAAA,MACH,aAAA;AAAA,MACA,aAAA;AAAA,MACA,UAAA;AAAA,KACA,CAAA;AAAA,GACF;AAAA,CACF,CAAA,CAAA;;;;;;;;;;;;AAnKM,IA/SJ,KAAI,EAAAC,cAAA,CAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,EAAA,EAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA;AAAA,IAEH,aAAQ,CAAA,IAAA,CAAA,oBAAU,GAAI,CAAA,MAAA,KAAA,IAAW,CAAU,MAAA,CAAA,aAAA,GAAA,IAAA;AAAA,IAC3C,YAAA,EAAA,CAAA,MAAA,KAAA,IAAA,CAAA,MAAA,CAAA,aAAuB,GAAA,KAAA;AAAoB,GAC3C,EAAA;AAAgC,IAAAC,WAAA,CAAA,qBAAA,EAAA;MA0SpB,GAAA,EAAA,YAAA;AAAA,MAvSX,OAAI,EAAA,IAAA,CAAA,mBAAA;AAAA,MACH,SAAS,EAAA,IAAA,CAAA,SAAA;AAAA,MACT,UAAW,EAAA,IAAA,CAAA,UAAA;AAAA,MACX,cAAY,EAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,QAAA,CAAA,EAAA,IAAA,CAAA,WAAA,CAAA;AAAA,MACZ,gBAAY,EAAY,IAAA,CAAA;AAAwB,MAChD,qBAAgB,EAAA,IAAA,CAAA,kBAAA;AAAA,MAChB,MAAqB,EAAA,IAAA,CAAA,MAAA;AAAA,MACrB,IAAQ,EAAA,EAAA;AAAA,MACT,OAAA,EAAA,OAAA;AAAA,MACA,UAAQ,EAAA,CAAA,EAAA,IAAA,CAAA,QAAA,CAAA,SAAA,CAAA,KAAA,CAAA,YAAA,CAAA;AAAA,MACP,yBAAwB,EAAA,KAAA;AAAe,MACvC,kBAAyB,EAAA,KAAA;AAAA,MACzB,UAAkB,EAAA,IAAA,CAAA,UAAA;AAAA,MAClB,WAAY,EAAA,IAAA,CAAA,QAAA;AAAA,MACZ,YAAW,EAAA,IAAA,CAAA,SAAA;AAAA,MACX,MAAY,EAAA,IAAA,CAAA,MAAA;AAAA,MACZ,YAAQ,EAAA,IAAA,CAAA,eAAA;AAAA,MACR,MAAa,EAAA,CAAA,MAAA,KAAA,IAAA,CAAA,MAAA,CAAA,YAAA,GAAA,KAAA;AAAA,KACb,EAAA;AAAyB,MAAA,OAAA,EAAAC,OAAA,CAAA,MAAA;AAEf,QAAA,IAAA,EAAA,CAAO;AA2NV,QA1NN,OA0NM;AAAA,UAzNJC,kBAAI,CAAA,KAAA,EAAA;AAAA,YACE,GAAA,EAAA,YAAA;AAAA,YAAgB,qBAAU,CAAA;AAAA,cAAyB,IAAA,CAAA,QAAW,CAAA,CAAA,CAAA,SAAA,CAAY;AAAS,cAAwB,IAAA,CAAA,QAAA,CAAA,EAAe,CAAA,SAAA,EAAA,IAAA,CAAA,SAAoB,CAAA;AAAA,cAAe,IAAA,CAAA,QAAW,CAAA,EAAA,CAAA,UAAA,EAAe,IAAU,CAAA,MAAA,CAAA,aAAA,CAAA;AAAA,cAAe,IAAA,CAAA,QAAW,CAAA,EAAA,CAAA,YAA2B,EAAA,IAAA,CAAA,UAAA,CAAA;AAAA,cAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,UAAA,EAAA,IAAA,CAAA,cAAA,CAAA;AAO3P,aAAA,CAAA;AAAyB,YAAA,OAAA,EAAAC,aAAA,CAAA,IAAA,CAAA,UAAA,EAAA,CAAA,SAAA,CAAA,CAAA;AAGlB,WAAA,EAAA;AADR,YAMM,IAAA,CAAA,MAAA,CAAA,MAAA,IAAAC,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,cAAA,GAAA,EAAA,CAAA;AAAA,cAAA,GAAA,EAAA,WAAA;cAJJ,KAAI,EAAAN,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA;AAAA,aACH,EAAA;AAAiB,cAAAO,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,QAAA,CAAA;;8BAEI,CAAA,KAAA,EAAA;AAAA,cAAA,GAAA,EAAA,cAAA;;;;AAExB,eAAA,CAAA;AAAA,aA0KM,EAAA;AAAA,cAAA,IAAA,CAAA,QAAA,GAAAA,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,KAAA,EAAA;AAAA,gBAzKA,GAAA,EAAA,CAAA;AAAA,gBACE,IAAA,EAAA,IAAA,CAAA,MAAA,CAAA,QAAA;AAAA,gBAAkB,eAAU,CAAA,SAAA;AAAA,gBAA6B,cAAS,EAAA,IAAA,CAAA,cAAA;AAAA,eAAA,EAAA,MAAA;AAA4C,iBAAAF,SAAA,CAAA,IAAA,CAAA,EAAQC,kBAAkB,CAAAE,gBAAaC,UAAS,CAAA,IAAA,CAAA,WAAA,EAAA,CAAA,IAAA,KAAA;AAAA,kBAAA,OAAAJ,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;;;;AAS5J,oBAAAL,WA2FD,CAAA,iBAAA,EAAA;AAAA,sBAAA,QAAA,EAAA,CAAA,IAAA,CAAA,cAAA,IAAA,CAAA,IAAA,CAAA,UAAA;AAzFJ,sBAAM,IAAO,EAAA,IAAA,CAAA,eAAA;AAAA,sBACD,IAAA,EAAA,IAAA,CAAA,OAAA;AAAA,sBACK,MAAA,EAAA,IAAA,CAAA,SAAA;AAAA,sBAuFb,qBAAA,EAAA,EAAA;AAAA,sBArFL,KAAA,EAAAS,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA;AAAA,sBAwBM,OAAA,EAAA,CAAA,MAAA,KAAA,IAAA,CAAA,SAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,qBAAA,EAAA;AAAA,sBAvBW,OAAA,EAAAR,OAAA,CAAA,MAAA;AADjB,wBAAAC,kBAAA,CAAA,MAAA,EAAA;AAAA,0BAwBM,KAAA,EAAAH,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,WAAA,CAAA,CAAA;AAAA,yBAAA,EAAA;AAAA,0BAtBHO,sBAAqB,EAAA,OAAA,EAAA;AAAA,4BACrB,KAAO,EAAA,IAAA,CAAA,YAAA;AAAU,4BAAA,KAAA,EAAA,IAAA,CAAA,KAAA;;4BAoBTI,eAAA,CAAAC,eAAA,CAAA,IAAA,CAAA,YAAA,CAAA,EAAA,CAAA,CAAA;AAAA,2BAjBE,CAAA;AAA2B,yBAAA,EAC7B,CAAA,CAAA;AAAA,uBAAA,CAAA;AACA,sBAAA,CAAA,EAAA,CACN;AAAQ,qBACT,EAAA,IAAA,EAAA,CAAA,UAAA,EAAA,MAAA,EAAA,MAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,CAAA,CAAA;AAAA,mBACC,EAAA,CAAA,CAAA,CAAA;AAAe,iBAAA,CAAA,EAAA,GAAA,CAAA;AACc,gBAAA,IAAA,CAAA,YAAA,IAAA,IAAA,CAAA,MAAA,CAAA,QAAA,CAAA,MAAA,GAAA,IAAA,CAAA,eAAA,IAAAP,SAAA,EAAA,EAAAQ,WAAA,CAAA,qBAAA,EAAA;;AAUvB,kBARP,GAAA,EAAA,eAAA;AAAA,kBAQO,QAAA,EAAA,IAAA,CAAA,mBAAA,IAAA,CAAA,IAAA,CAAA,mBAAA;AAAA,kBAAA,qBAAA,EAAA,CAAA,QAAA,EAAA,KAAA,EAAA,OAAA,EAAA,MAAA,CAAA;AAAA,kBARA,MAAA,EAAA,IAAA,CAAA,MAAA;AAAiB,kBAAA,SAAA,EAAA,QAAA;;kCAOf,CAAA,UAAA;AAAA,iBAAA,EAAA;AAJQ,kBAAA,OAAA,EAAAX,cACA;AAAA,oBAAAC,kBAGR,CAAA,KAAA,EAAA;AAAA,sBAAA,GAAA,EAAA,iBAAA;AADF,sBAAA,KAAA,EAAAH,cAAA,CAAA,IAAA,CAAA,QAAK,CAAY,CAAA,CAAA,eAAA,CAAA,CAAA;AAAA,qBAAA,EAAA;AAAA,sBAAAC,WAAA,CAAA,iBAAA,EAAA;AAAA,wBAAA,QAAA,EAAA,KAAA;AAAA,wBAAA,IAAA,EAAA,IAAA,CAAA,eAAA;;;;;;;;;;;;;;;;;AAOpB,sBAAA,GAAA,EAAA,YAAuB;AAyDlB,sBAAA,KAAA,EAAAD,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,WAAA,CAAA,CAAA;qBAxDP,EAAA;AAAA,uBACHK,mCAAkC,CAAAG,QAAA,EAAA,IAAA,EAAAC,UAAA,CAAA,IAAA,CAAA,eAAA,EAAA,CAAA,IAAA,KAAA;AAAA,wBACb,OAAAJ,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,0BACb,GAAA,EAAA,IAAA,CAAA,WAAA,CAAA,IAAA,CAAA;AAAA,0BACC,KAAA,EAAAN,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,eAAA,CAAA,CAAA;AAAA,yBACK,EAAA;AAAA,0BACFC,WAAA,CAAA,iBAAA,EAAA;AAAA,4BAAA,KAAA,EAAA,YAAA;AAEF,oCACT,EAgBM,CAAA,IAAA,CAAA,cAAA,IAAA,CAAA,IAAA,CAAA,UAAA;AAAA,4BAhBN,IAAA,EAAA,IAAA,CAAA,eAAA;AAAA,4BAgBM,IAAA,EAAA,IAAA,CAAA,OAAA;AAAA,4BAAA,MAAA,EAAA,IAAA,CAAA,SAAA;AAAA,4BAfA,qBAAA,EAAA,EAAA;AAAA,4BACH,OAAO,EAAA,CAAA,MAAA,KAAA,IAAA,CAAA,SAAU,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,2BAAA,EAAA;;8BAaTE,kBAAA,CAAA,MAAA,EAAA;AAAA,gCAVI,KAAA,EAAAH,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,WAAA,CAAA,CAAA;AAAA,+BACJ,EAAA;AAAA,gCACAO,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,OAAA,EAAA;AAAA,kCACE,KAAA,EAAA,IAAA,CAAA,YAAA;AAAA,kCACT,KAAA,EAAA,IAAA,CAAA,KAAA;AAAA,iCACM;AAAkB,kCAAAI,eAAA,CAAAC,eAAA,CAAA,IAAA,CAAA,YAAA,CAAA,EAAA,CAAA,CAAA;;AAIjB,+BAFP,EAAA,CAAA,CAAA;AAAA,6BAEO,CAAA;AAAA,4BAAA,CAAA,EAAA,CAAA;AAAA,2BAFA,EAAA,IAAA,EAAA,CAAA,UAAO,EAAA,MAAA,EAAA,MAAA,EAAA,QAAU,EAAA,SAAA,CAAA,CAAA;AAAA,yBAAA,EAAA,CAAA,CAAA,CAAA;AAAe,uBAAA,CAAA,EAAA,GAAA,CAAA;AACQ,qBAAA,EAAA,CAAA,CAAA;AAAA,mBAAA,CAAA;AAAA,kBAAA,CAAA,EAAA,CAAA;AAAA,iBAAA,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,QAAA,EAAA,cAAA,EAAA,YAAA,CAAA,CAAA,IAAAE,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;;;;;;;;;AAK1C,gBAAAC,iCA2BH,CAAA,OAAA,EAAA;AAAA,kBA1BN,EAAA,EAAA,IAAA,CAAA,OAAA;AAAA,kBA0BM,GAAA,EAAA,UAAA;AAAA,kBAAA,qBAAA,EAAA,CAAA,MAAA,KAAA,IAAA,CAAA,MAAA,CAAA,UAAA,GAAA,MAAA;AAAA,kBAAA,IAAA,EA1BD,MAAI;AAAA,kBAAc,IAAA,EAAA,IAAA,CAAA,IAAO;AAAU,kBAAA,KAAA,EAAAf,cAAA,CAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,OAAA,CAAA,EAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA;;AACtC,kBAAA,YAAA,EAAA,IAAA,CAAA,YAAA;AAAA,kBAwBM,KAAA,EAAAU,cAAA,CAAA,IAAA,CAAA,UAAA,CAAA;AAAA,kBAAA,QAAA,EAAA,IAAA,CAAA,QAAA;AAAA,kBAvBW,IAAA,EAAA,UAAA;AADjB,kBAAA,QAAA,EAAA,CAAA,IAAA,CAAA,UAAA;AAAA,kBAwBM,UAAA,EAAA,OAAA;AAAA,kBAAA,uBAAA,EAAA,CAAA,CAAA,EAAA,GAAA,IAAA,CAAA,WAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,EAAA,KAAA,EAAA;AAAA,kBAtBH,eAAA,EAAA;AAAqB,kBACrB,eAAA,EAAA,IAAK,CAAE,mBAAA;AAAU,kBAAA,YAAA,EAAA,IAAA,CAAA,SAAA;;iCAoBT,EAAA,SAAA;AAAA,kBAAA,SAAA,EAAA;AAjBD,oBAAAM,QAAA,CAAAZ,aACG,CAAG,CAAc,MAAA,KAAA,IAAA,CAAA,eAAU,CAAA,MAAA,CAAA,EAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA,EAAA,CAAA,MAAA,CAAA,CAAA;AAAA,oBAAAY,QAAA,CAAAZ,aAC7B,CAAA,CAAA,MAAA,KAAA,IAAA,CAAA,eAAA,CAAA,MAAA,CAAA,EAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA,EAAA,CAAA,IAAA,CAAA,CAAA;AAAA,oBAAAY,QAAA,CAAAZ,aACA,CAAA,IAAA,CAAA,SAAA,EAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA,EAAA,CAAA,KAAA,CAAA,CAAA;AAAA,oBAAAY,QAAA,CAAAZ,aACE,CAAA,IAAA,CAAA,YAAA,EAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA;AAAA,oBACTY,QAAA,CAAAZ,aAAA,CAAA,IAAA,CAAA,aAAA,EAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,QAAA,CAAA,CAAA;AAAA,mBAAA;AAC8B,kBAAA,kBAAA,EAAA,IAAA,CAAA,sBAAA;mEAUvB;AAAA,kBARP,gBAAA,EAAA,IAAA,CAAA,oBAAA;AAAA,kBAQO,OAAA,EAAA,IAAA,CAAA,OAAA;AAAA,kBAAA,OAAA,EAAAA,aAAA,CAAA,IAAA,CAAA,UAAA,EAAA,CAAA,MAAA,CAAA,CAAA;AAAA,iBARA,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,IAAA,EAAA,qBAAO,EAAA,MAAA,EAAA,UAAS,EAAC,cAAA,EAAA,UAAA,EAAA,UAAA,EAAA,uBAAA,EAAA,eAAA,EAAA,eAAA,EAAA,YAAA,EAAA,WAAA,EAAA,oBAAA,EAAA,qBAAA,EAAA,kBAAA,EAAA,SAAA,EAAA,SAAA,CAAA,CAAA,EAAA;AAAA,kBAAA,CAAAa,UAAA,EAAA,IAAA,CAAA,MAAA,CAAA,UAAA,CAAA;;4CAOf,EAAA,EAAAX,kBAAA,CAAA,MAAA,EAAA;AAAA,kBAAA,GAAA,EAAA,CAAA;AAJQ,kBAAA,GAAA,EAAA,eAAA;AACA,kBAAA,aAAA,EAAA,MAAA;AAGR,kBAAA,KAAA,EAAAN,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,kBAAA,CAAA,CAAA;AADF,kBAAA,WAAA,EAAAY,eAAA,CAAA,IAAA,CAAA,MAAA,CAAA,UAAiB,CAAA;AAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,aAAA,CAAA,CAAA,IAAAE,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,eAAA,EAAA,CAAA,CAAA;AAAA,cAAA,IAAA,CAAA,qBAAA,IAAAT,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,gBAAA,GAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AASpC,iBAAA,CAAA;AAAA,gBA6CM,CAAA,EAAA,CAAA;AAAA,eAAA,EAAA,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA,IAAAQ,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,cAAA,IA5CH,CAAK,SAAA,IAAA,IAAA,CAAA,SAAA,IAAAT,SAAA,EAAA,EAAAQ,WAAA,CAAA,kBAAA,EAAA;AAAA,gBAAA,GAAA;AAA8B,gBAAA,qBAA6C,CAAA;AAAA,kBAAmC,IAAA,CAAA,QAAA,CAAA,CAAA,CAAS,OAAE,CAAA;AAAsB,kBAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,MAAA,CAAA;;;8CAoCnJ;AAAA,eAAA,EAAA;AA7BK,gBAAA,OACD,EAAAX,OAAA,CAAA,MAAA;AAAA,mBAAAG,SAAA,EAAA,EAAAQ,WAAA,CAAAK,uBACsB,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA;AAAA,iBAAA,CAAA;AACrB,gBAAA,CAAA,EAAA,CACJ;AAAM,eAAA,EAAA,CAAA,EACN,QAAQ,EAAA,SAAA,CAAA,CAAA,IAAAJ,kBAAqB,CAAA,MAAA,EAAA,IAAA,CAAA;AAAsB,cAAA,IAAA,CAAA,aACzC,IAAA,IAAA,CAAA,YAAA,IAAA,IAAA,CAAA,cAAA,IAAAT,SAAA,EAAA,EAAAQ,WAAA,CAAA,kBAAA,EAAA;AAAA,gBAAA,GAAA,EACI,CAAA;AAAA,gBACd,KAAA,EAAAb;AAAiB,kBAAA,IACP,CAAA,OAAA,CAAA,CAAA,CAAA,MAAA,CAAA;AAAA,kBAAA,IACN,CAAA,OAAA,CAAA,CAAA,CAAA,cAAA,CAAA;AAAA,kBAAA,YACI,CAAG,EAAA,CAAA,SAAA,EAAA,IAAA,CAAA,aAAA,KAAA,YAAA,CAAA;AAAA,iBAAA,CAAA;AACD,eACV,EAAA;AAAsC,gBAAA,OACvB,EAAAE,OAAA,CAAA,MAAA;AAAA,mBAAAG,SACA,EAAA,EAAAQ,WAAA,CAAAK,uBAAA,CAAA,IAAA,CAAA,YAAA,CAAA,CAAA;AAAA,iBAAA,CAAA;AACH,gBAAA,CAAA,EAAA,CACb;AAAkB,eAAA,EAAA,CAAA,EAClB,CAAc,OAAA,CAAA,CAAA,IAAAJ,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,aAAA,EAAA,CAAA,CAAA;AACN,WAAA,EAAA,EAAA,EAAA,CAAA,SAAA,CAAA,CAAA;AAAmC,SAAA,CAAA;AACF,OAAA,CAAA;AACL,MAAA,OAAA,EAAAZ,OAAA,CAAA,MAAA;AACK,QAAAD,WAAA,CAAA,yBAAA,EACN,EAAA,GAAA,EAAA,SAAA,EAAA,EAAA;AAAA,UAAA,OAAA,EAAAC,OAAA,CAAA,MAAA;uBAChB,CAAA,MAAA,IAAAG,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,cAAA,GAAA,EAAA,CAClB;AAAmB,cAAA,KAAA,EACHN,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,UAAA,EAAA,QAAA,CAAA,CAAA;AAAA,cAAA,OACT,EAAAI,aAAA,CAAA,MAAA;AAAA,eACP,EAAA,CAAA,MAAA,CAAA,CAAA;AAAsB,aAAA,EAAA;AA1Bd,cAAAG,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,QAAiB,CAAA;AAAA,aAAA,EAAA,EAAA,EAAA,CAAA,SAAA,CAAA,CAAA,IAAAO,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AA6BpB,YAAAC,cAAA,CAAAd,WAAA,CAAA,uBAKN,EAAA;AAAA,cAAA,EAAA,EAAA,IAAA,CAAA,SAAA;iCAJI;AAAA,cAAA,GAAA,EAAA,IACQ;AAAA,cACX,YAAA,EAAO,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,UAAA,EAAS,MAAC,CAAA;AAAA,cAClB,YAAA,EAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,UAA0B,EAAA,MAAD,CAAA;AAAA,cAAA,KAAA,EAAAD,cAAA,CAAA,CAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,OAAA,EAAA,IAAA,CAAA,oBAAA,KAAA,CAAA,CAAA,CAAA,CAAA;;;;;eAG7B;AAAA,cAoBM,OAAA,EAAAE,OAAA,CAAA,MAAA;AAAA,gBAAA,IAAA,CAAA,aAAA,IAAAG,SAAA,EAAA,EAAAQ,WAAA,CAAA,oBAAA,EAAA;AAAA,kBAAA,GAAA,EAAA,CAAA;kBAlBH,KAAK,EAAA,IAAA,CAAA,MAAA,CAAA,UAAA;AAAA,kBAAA;AAA8B,iBAAA,EAAA,iBAA6C,CAAA,CAAA,IAAAC,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,gBAAAb,WAA0C,CAAA,qBAAA,EAAA,IAAA,EAAA;AAAA,kBAAA,OAAA,EAAAC,OAAA,CAAA,MAAA;8BAA0E,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,CAAA;AAAoB,mBAAA,CAAA;;;;AAUjN,cAAA,CAAA,EAAA,CAAA;AAMD,aAAA,EAAA,CAAA,EAAA,CAAA,IAAA,EAAA,YAAA,EAAA,YAAA,EAAA,OAAA,EAAA,YAAA,EAAA,UAAA,CAAA,CAAA,EAAA;oBAJG,EAAA,IAAA,CAAA,MAAA,CAAA,OAAA,CAAA,IAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,OAAA,CAAA;AAAA,aAAA,CAAA;AACA,YAAA,IAAA,CAAA,MAGH,CAAA,OAAA,IAAA,IAAA,CAAA,OAAA,IAAAG,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,cADL,GAAA,EAAA,CAAA;AAAA,cAAqC,KAAA,EAAAN,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,UAAA,EAAA,SAAA,CAAA,CAAA;AAAA,aAAA,EAAA;AAAA,cAAAO,UAAA,CAAA,IAAA,CAAA,MAAA,EAA5B,SAAkB,CAAA;AAAA,aAAA,EAAA,CAAA,CAAA,IAAA,IAAA,CAAA,OAAA,IAAA,IAAA,CAAA,oBAAA,KAAA,CAAA,IAAAF,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,cAAA,GAAA,EAAA,CAAA;AAAA,cAAA,KAAA,EAAAN,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,UAAA,EAAA,OAAA,CAAA,CAAA;AAAA,aAE7B,EAAA;AAAA,cAA4CO,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,OAAA,EAAA,EAAA,EAAA,MAAA;AAAA,gBAAAJ,kBAAA,CAAA,MAAA,EAAA,IAAA,EAAAS,eAAA,CAAA,IAAA,CAAA,SAAA,CAAA,EAAA,CAAA,CAAA;AAAA,eAAA,CAAA;AAAV,aAAA,EAAA,CAAA,CAAA,IAAAE,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,YAAA,IAAA,CAAA,MAAA,CAAA,MAAA,IAAAT,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,cAAA,GAAA,EAAA,CAAA;AAAA,cAAA,KAAA,EAAAN,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,UAAA,EAAA,QAAA,CAAA,CAAA;;;;;;;;AAGtC,SAAA,EAAA,GAAA,CAAA;AAAA,OA4BM,CAAA;AAAA,MAAA,CAAA,EAAA,CAAA;AAAA,KAAA,EAAA,CAAA,EAAA,CAAA,SA5BG,EAAA,WAAA,EAAA,YAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,qBAAA,EAAA,QAAA,EAAA,YAAA,EAAA,YAAA,EAAA,WAAA,EAAA,YAAA,EAAA,QAAA,EAAA,cAAA,EAAA,QAAA,CAAA,CAAA;AAAA,GAAa,EAAA,EAAA,EAAA,CAAA,cAAO,CAAA,CAAA,GAAA;AAAU,IAAA,CAAA,wBAAA,EAAA,IAAA,CAAA,kBAAA,EAAA,IAAA,CAAA,SAAA,CAAA;;;AAGlC,aAAA,2BAAiB,CAAA,SAAA,EAAA,CAAA,CAAC,UAAW,WAAS,CAAA,EAAA,CAAA,UAAW,YAAW,CAAA,CAAA,CAAA;;;;"}