{% extends 'base.html' %}

{% block title %}接口解密 - TVBox助手{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2 class="mb-4">TVBox接口解密</h2>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="bi bi-unlock-fill me-2"></i>解密工具
        </div>
        <div class="card-body">
            <form id="decryptForm">
                <div class="mb-3">
                    <label for="url" class="form-label">TVBox接口URL</label>
                    <input type="text" class="form-control" id="url" placeholder="输入需要解密的TVBox接口URL">
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="hintType" class="form-label">类型提示 (可选)</label>
                        <select class="form-select" id="hintType">
                            <option value="">自动检测</option>
                            <option value="base64">Base64</option>
                            <option value="reverse">字符串反转</option>
                            <option value="urldecode">URL解码</option>
                            <option value="clan">Clan协议</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check mt-4">
                            <input class="form-check-input" type="checkbox" id="configMode" checked>
                            <label class="form-check-label" for="configMode">
                                <strong>配置模式</strong> (获取完整配置内容)
                            </label>
                        </div>
                    </div>
                </div>
                <!-- 快速解析按钮组 -->
                <div class="mb-3">
                    <div class="d-flex flex-wrap gap-2">
                        <button type="button" id="ftyButton" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-lightning-charge"></i> 饭太硬
                        </button>
                        <button type="button" id="fmButton" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-lightning-charge"></i> 肥猫
                        </button>
                        <button type="button" id="werButton" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-lightning-charge"></i> 王二小
                        </button>
                        <button type="button" id="shButton" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-lightning-charge"></i> 神器
                        </button>
                        <button type="button" id="ddButton" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-lightning-charge"></i> 多多
                        </button>
                        <button type="button" id="netflixButton" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-lightning-charge"></i> 奈飞
                        </button>
                        <button type="button" id="fongmiButton" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-lightning-charge"></i> FongMi
                        </button>
                        <button type="button" id="aliButton" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-lightning-charge"></i> 阿里
                        </button>
                        <button type="button" id="aliDriveButton" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-lightning-charge"></i> 阿里云盘
                        </button>
                        <button type="button" id="customButton" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-folder"></i> 本地
                        </button>
                        <button type="button" id="singleLineButton" class="btn btn-sm btn-outline-info">
                            <i class="bi bi-link"></i> 单线路
                        </button>
                    </div>
                </div>

                <!-- 文件导入按钮 -->
                <div class="mb-3">
                    <label for="fileImport" class="form-label">导入本地配置文件:</label>
                    <input type="file" class="form-control" id="fileImport" accept="application/json,.json,text/plain">
                </div>
                <div class="d-flex justify-content-between">
                    <div>
                        <button type="submit" class="btn btn-primary" id="decryptBtn">
                            <i class="bi bi-unlock"></i> 解密
                        </button>
                    </div>
                    <div>
                        <span class="text-muted me-2">快速解析:</span>
                        <div class="btn-group">
                            <button type="button" class="btn btn-outline-success btn-sm" id="ftyButton">
                                饭太硬接口
                            </button>
                            <button type="button" class="btn btn-outline-success btn-sm" id="fmButton">
                                肥猫接口
                            </button>
                            <button type="button" class="btn btn-outline-success btn-sm" id="werButton">
                                王二小接口
                            </button>
                        </div>
                        <div class="btn-group ms-2">
                            <button type="button" class="btn btn-outline-secondary btn-sm dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                更多接口
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" id="ddButton">道长接口</a></li>
                                <li><a class="dropdown-item" href="#" id="shButton">神器接口</a></li>
                                <li><a class="dropdown-item" href="#" id="netflixButton">奈飞接口</a></li>
                                <li><a class="dropdown-item" href="#" id="customButton">本地接口</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" id="localJsonButton">导入本地JSON</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <div id="alertContainer"></div>
    <div id="decryptResult"></div>
</div>

<!-- 解析线路模态框 -->
<div class="modal fade" id="parseLineModal" tabindex="-1" aria-labelledby="parseLineModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="parseLineModalLabel">解析配置线路</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
            </div>
            <div class="modal-body">
                <div id="parseLineResult"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 文件导入隐藏表单 -->
<input type="file" id="jsonFileInput" accept="application/json" style="display: none;">
{% endblock %}

{% block scripts %}
<script>
    // 快速解析按钮
    $("#ftyButton").click(function() {
        $("#url").val("http://饭太硬.top/tv");
        $("#configMode").prop("checked", true);
        $("#decryptForm").submit();
    });
    
    $("#fmButton").click(function() {
        $("#url").val("http://肥猫.live");
        $("#configMode").prop("checked", true);
        $("#decryptForm").submit();
    });
    
    $("#werButton").click(function() {
        $("#url").val("http://tvbox.rzdpai.top/tvbox.json");
        $("#configMode").prop("checked", true);
        $("#decryptForm").submit();
    });
    
    $("#shButton").click(function() {
        $("#url").val("https://raw.liucn.cc/box/m.json");
        $("#configMode").prop("checked", true);
        $("#decryptForm").submit();
    });
    
    $("#ddButton").click(function() {
        $("#url").val("https://dxawi.github.io/0/0.json");
        $("#configMode").prop("checked", true);
        $("#decryptForm").submit();
    });
    
    $("#netflixButton").click(function() {
        $("#url").val("https://jihulab.com/clear1/tvbox/-/raw/main/0.json");
        $("#configMode").prop("checked", true);
        $("#decryptForm").submit();
    });
    
    $("#customButton").click(function() {
        $("#url").val("clan://localhost/tvfans/tv.json");
        $("#configMode").prop("checked", true);
        $("#decryptForm").submit();
    });
    
    // 导入本地JSON文件
    $("#localJsonButton").click(function() {
        $("#jsonFileInput").click();
    });
    
    $("#jsonFileInput").change(function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const content = e.target.result;
                    // 尝试解析JSON
                    JSON.parse(content);
                    // 解析成功，直接提交
                    $("#url").val("本地文件: " + file.name);
                    // 创建表单数据，包含文件
                    const formData = new FormData();
                    formData.append("file", file);
                    
                    // 显示加载状态
                    $("#decryptResult").html('<div class="text-center my-5"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">正在解析文件...</p></div>');
                    
                    // 发送到解析接口
                    $.ajax({
                        url: "/api/parse",
                        type: "POST",
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function(response) {
                            if (response.success) {
                                displayTVBoxConfig({
                                    original: "本地文件: " + file.name,
                                    decrypted: JSON.stringify(response.data, null, 2),
                                    method: "本地文件解析",
                                    config_info: {
                                        sites_count: response.data.sites ? response.data.sites.length : 0,
                                        lives_count: response.data.lives ? response.data.lives.length : 0,
                                        parses_count: response.data.parses ? response.data.parses.length : 0,
                                        spider: response.data.spider || "",
                                        wallpaper: response.data.wallpaper || "",
                                        logo: response.data.logo || ""
                                    }
                                });
                            } else {
                                $("#decryptResult").html(`<div class="alert alert-danger">文件解析失败: ${response.message}</div>`);
                            }
                        },
                        error: function() {
                            $("#decryptResult").html('<div class="alert alert-danger">文件上传或解析失败</div>');
                        }
                    });
                    
                } catch (error) {
                    showAlert("danger", "无效的JSON文件: " + error.message);
                }
            };
            reader.readAsText(file);
        }
    });
</script>
{% endblock %} 