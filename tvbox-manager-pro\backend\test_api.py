#!/usr/bin/env python3
"""
测试本地化API
"""
import requests
import json

def test_api():
    """测试API"""
    base_url = "http://localhost:8001/api"
    
    # 登录获取token
    login_data = {
        "email": "<EMAIL>",
        "password": "admin123"
    }
    
    try:
        print("🔐 登录获取token...")
        login_response = requests.post(f"{base_url}/v1/auth/login", json=login_data)
        
        if login_response.status_code == 200:
            token_data = login_response.json()
            access_token = token_data["access_token"]
            print("✅ 登录成功")
            
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
            
            # 测试获取接口1的本地化状态
            print("\n📊 获取接口1的本地化状态...")
            status_response = requests.get(f"{base_url}/v1/interfaces/1/localization-status", headers=headers)
            
            if status_response.status_code == 200:
                status_data = status_response.json()
                print("✅ 获取状态成功")
                print(f"📄 响应数据:")
                print(json.dumps(status_data, indent=2, ensure_ascii=False))
                
                # 检查files字段
                if 'files' in status_data and status_data['files']:
                    print(f"\n📁 文件列表 ({len(status_data['files'])} 个):")
                    for i, file in enumerate(status_data['files']):
                        print(f"   {i+1}. {file}")
                else:
                    print("\n⚠️  没有文件数据")
            else:
                print(f"❌ 获取状态失败: {status_response.status_code}")
                print(f"错误信息: {status_response.text}")
        else:
            print(f"❌ 登录失败: {login_response.status_code}")
            print(f"错误信息: {login_response.text}")
            
    except Exception as e:
        print(f"❌ 异常: {str(e)}")

if __name__ == "__main__":
    test_api()
