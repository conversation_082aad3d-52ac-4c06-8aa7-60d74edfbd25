#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
TVBox助手Web应用
提供基于浏览器的TVBox配置管理界面
"""

import os
import logging
from flask import Flask, render_template
from flask_cors import CORS

def create_app(test_config=None):
    """创建并配置Flask应用实例"""
    # 获取当前模块所在目录
    base_dir = os.path.abspath(os.path.dirname(__file__))
    template_dir = os.path.join(base_dir, 'templates')
    static_dir = os.path.join(base_dir, 'static')
    
    # 创建Flask应用
    app = Flask(__name__, 
                instance_relative_config=True,
                template_folder=template_dir,
                static_folder=static_dir)
    
    # 打印路径信息（调试用）
    logger = logging.getLogger('tvbox_web')
    logger.info(f"模板路径: {template_dir}")
    logger.info(f"静态文件路径: {static_dir}")
    
    # 允许跨域请求
    CORS(app)
    
    # 默认配置
    app.config.from_mapping(
        SECRET_KEY='dev',
        UPLOAD_FOLDER=os.path.join(app.instance_path, 'uploads'),
        MAX_CONTENT_LENGTH=16 * 1024 * 1024,  # 最大16MB上传
        ALLOWED_EXTENSIONS={'json', 'txt', 'jar'},
    )
    
    # 加载测试配置或实例配置
    if test_config is None:
        app.config.from_pyfile('config.py', silent=True)
    else:
        app.config.from_mapping(test_config)
    
    # 确保实例文件夹存在
    try:
        os.makedirs(app.instance_path, exist_ok=True)
        os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    except OSError:
        pass
    
    # 添加错误处理路由
    @app.errorhandler(404)
    def page_not_found(e):
        return render_template('404.html'), 404
    
    # 注册路由
    from . import routes, api
    app.register_blueprint(routes.bp)
    app.register_blueprint(api.bp)
    
    # 打印所有注册的路由（调试用）
    logger.info("注册的路由:")
    for rule in app.url_map.iter_rules():
        logger.info(f"{rule} -> {rule.endpoint}")
    
    return app

def run_app(host='127.0.0.1', port=5000, debug=True):
    """运行Web应用"""
    app = create_app()
    app.run(host=host, port=port, debug=debug)

if __name__ == '__main__':
    run_app() 