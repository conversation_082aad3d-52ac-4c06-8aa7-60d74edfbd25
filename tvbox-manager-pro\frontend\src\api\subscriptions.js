import { request } from '@/utils/request'

export const subscriptionsApi = {
  // 获取订阅列表
  getSubscriptions(params = {}) {
    return request.get('/v1/subscriptions', params)
  },

  // 创建订阅
  createSubscription(data) {
    return request.post('/v1/subscriptions', data)
  },

  // 获取订阅详情
  getSubscriptionById(id) {
    return request.get(`/v1/subscriptions/${id}`)
  },

  // 更新订阅
  updateSubscription(id, data) {
    return request.put(`/v1/subscriptions/${id}`, data)
  },

  // 删除订阅
  deleteSubscription(id) {
    return request.delete(`/v1/subscriptions/${id}`)
  },

  // 获取订阅配置
  getSubscriptionConfig(id) {
    return request.get(`/v1/subscriptions/${id}/config`)
  },

  // 生成订阅链接
  generateSubscriptionUrl(id) {
    return request.post(`/v1/subscriptions/${id}/generate-url`)
  },

  // 刷新订阅
  refreshSubscription(id) {
    return request.post(`/v1/subscriptions/${id}/refresh`)
  },

  // 获取订阅统计
  getSubscriptionStats(id) {
    return request.get(`/v1/subscriptions/${id}/stats`)
  },

  // 批量删除订阅
  batchDeleteSubscriptions(ids) {
    return request.post('/v1/subscriptions/batch-delete', { ids })
  },

  // 启用/禁用订阅
  toggleSubscriptionStatus(id, isActive) {
    return request.patch(`/v1/subscriptions/${id}/status`, { is_active: isActive })
  },

  // 复制订阅
  copySubscription(id) {
    return request.post(`/v1/subscriptions/${id}/copy`)
  }
}
