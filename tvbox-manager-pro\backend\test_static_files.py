#!/usr/bin/env python3
"""测试静态文件访问"""

import os
import requests
from pathlib import Path

def test_static_files():
    """测试静态文件访问"""
    base_url = "http://localhost:8001"
    
    # 检查本地文件是否存在
    localized_dir = Path("data/localized")
    print(f"本地化目录存在: {localized_dir.exists()}")
    
    if localized_dir.exists():
        print("本地化目录内容:")
        for item in localized_dir.rglob("*"):
            if item.is_file():
                print(f"  {item}")
    
    # 测试静态文件访问
    test_urls = [
        "/localized/",
        "/localized/interface_1_真实TVBox配置/",
        "/localized/interface_1_真实TVBox配置/js/",
        "/localized/interface_1_真实TVBox配置/js/%E5%85%94%E5%B0%8F%E8%B4%9D.js",
    ]
    
    for url in test_urls:
        full_url = base_url + url
        try:
            response = requests.get(full_url, timeout=5)
            print(f"URL: {url}")
            print(f"状态码: {response.status_code}")
            if response.status_code == 200:
                print(f"内容长度: {len(response.content)}")
            else:
                print(f"错误: {response.text[:200]}")
            print("-" * 50)
        except Exception as e:
            print(f"URL: {url}")
            print(f"异常: {str(e)}")
            print("-" * 50)

if __name__ == "__main__":
    test_static_files()
