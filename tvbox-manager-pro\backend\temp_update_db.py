import sqlite3

# 连接数据库
conn = sqlite3.connect('data/tvbox.db')
cursor = conn.cursor()

# 更新本地化状态为pending
cursor.execute('UPDATE interface_sources SET localization_status = "pending" WHERE id = 1')
conn.commit()

# 查询更新后的状态
cursor.execute('SELECT id, name, enable_localization, localization_status FROM interface_sources WHERE id = 1')
result = cursor.fetchone()

print(f'更新后的接口信息:')
print(f'  ID: {result[0]}')
print(f'  Name: {result[1]}')
print(f'  enable_localization: {result[2]}')
print(f'  localization_status: {result[3]}')

conn.close()
