{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/image-viewer/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport ImageViewer from './src/image-viewer.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElImageViewer: SFCWithInstall<typeof ImageViewer> =\n  withInstall(ImageViewer)\nexport default ElImageViewer\n\nexport * from './src/image-viewer'\n"], "names": [], "mappings": ";;;;AAEY,MAAC,aAAa,GAAG,WAAW,CAAC,WAAW;;;;"}