/*!
* Tabler v1.0.0-beta20 (https://tabler.io)
* @version 1.0.0-beta20
* @link https://tabler.io
* Copyright 2018-2023 The Tabler Authors
* Copyright 2018-2023 codecalm.net Paweł Kuna
* Licensed under MIT (https://github.com/tabler/tabler/blob/master/LICENSE)
*/
(function(factory){"use strict";typeof define==="function"&&define.amd?define(["jquery"],factory):typeof exports==="object"&&typeof module!=="undefined"?module.exports=factory(require("jquery")):factory(jQuery)})(function($){"use strict";const bootstrap=window.bootstrap;const SIDEBAR_TRANSITION_DURATION=200;let sidebarResizeEvent;let mql=window.matchMedia("(min-width: 992px)");const debounce=(func,wait,immediate)=>{let timeout;return function(){let context=this,args=arguments;let later=function(){timeout=null;if(!immediate)func.apply(context,args)};let callNow=immediate&&!timeout;clearTimeout(timeout);timeout=setTimeout(later,wait);if(callNow)func.apply(context,args)}};const resizeSidebar=()=>{if(typeof sidebarResizeEvent==="undefined"){sidebarResizeEvent=new Event("sidebar-resize")}}
const Sidebar=function(element,options){this.config=Object.assign({},{transition:true,activeLinks:true},options);this.element=element;this.shown=false;this.init()};Sidebar.prototype.init=function(){const self=this;const body=document.querySelector("body");const sidebar=this.element;this.shown=body.classList.contains("sidebar-show")||sidebar.classList.contains("show");body.style.overflow="";document.documentElement.style.overflow="";body.style.paddingRight="";if(this.shown){body.classList.add("sidebar-show")}else{body.classList.remove("sidebar-show")}
if(this.config.activeLinks){let currentPage=window.location.pathname.split("/").pop();if(currentPage==="")currentPage="index.html";let currentLink=sidebar.querySelector(".sidebar-link[href$='"+currentPage+"']:not(.sidebar-link--disabled)");if(currentLink){currentLink.classList.add("active");let parentEl=currentLink.parentNode.closest(".sidebar-dropdown-item");if(parentEl){let sidebarLink=parentEl.querySelector(".sidebar-link");if(sidebarLink){sidebarLink.classList.add("active");const sidebarGroup=sidebarLink.closest(".sidebar-group");if(sidebarGroup){sidebarGroup.classList.add("show");const submenu=sidebarLink.nextElementSibling;if(submenu){submenu.classList.add("show")}}}}}}}
sidebar.querySelectorAll(".sidebar-dropdown-toggle").forEach(toggle=>{const parent=toggle.closest(".sidebar-dropdown-item");toggle.addEventListener("click",()=>{const sidebarGroup=toggle.parentNode.querySelector(".sidebar-group");if(sidebarGroup){sidebarGroup.classList.toggle("show")}
const submenu=toggle.nextElementSibling;if(submenu){submenu.classList.toggle("show");submenu.style.maxHeight=submenu.classList.contains("show")?submenu.scrollHeight+submenu.querySelectorAll(".sidebar-submenu").length*12+"px":"0px"}})});this.listeners();return this};Sidebar.prototype.listeners=function(){let self=this;const sidebar=this.element;document.addEventListener("click",function(event){const element=event.target;if(element.classList.contains("sidebar-toggle")||element.closest(".sidebar-toggle")){self.toggle()}else if(element.classList.contains("sidebar-close")||element.closest(".sidebar-close")){self.close()}});document.addEventListener("keydown",function(event){if(event.key==="Escape"&&self.shown&&!sidebar.dataset.sidebarOverlay){self.close()}})};Sidebar.prototype.toggle=function(){if(this.shown){this.close()}else{this.open()}};Sidebar.prototype.open=function(){const self=this;const body=document.querySelector("body");this.shown=true;body.classList.add("sidebar-show");setTimeout(function(){resizeSidebar()},SIDEBAR_TRANSITION_DURATION);return this};Sidebar.prototype.close=function(){const self=this;const body=document.querySelector("body");this.shown=false;body.classList.remove("sidebar-show");return this};document.addEventListener("DOMContentLoaded",()=>{const sidebarToggle=document.querySelector(".sidebar-toggle");const sidebar=document.getElementById("sidebar");if(sidebarToggle&&sidebar){sidebarToggle.addEventListener("click",()=>{sidebar.classList.toggle("collapsed");const isCollapsed=sidebar.classList.contains("collapsed");localStorage.setItem("sidebarCollapsed",isCollapsed);const sidebarToggleIcon=document.querySelector(".sidebar-toggle i");if(sidebarToggleIcon){sidebarToggleIcon.classList.toggle("fa-chevron-left",!isCollapsed);sidebarToggleIcon.classList.toggle("fa-chevron-right",isCollapsed)}});const storedState=localStorage.getItem("sidebarCollapsed");if(storedState==="true"){sidebar.classList.add("collapsed");const sidebarToggleIcon=document.querySelector(".sidebar-toggle i");if(sidebarToggleIcon){sidebarToggleIcon.classList.remove("fa-chevron-left");sidebarToggleIcon.classList.add("fa-chevron-right")}}}
const offcanvasSidebar=new Sidebar(document.querySelector(".sidebar"))});$(document).on("click",".alert-close",function(e){e.preventDefault();const alert=$(this).closest(".alert");alert.fadeOut(400,function(){alert.remove()})});$(document).ready(function(){$(".table-responsive").on("show.bs.dropdown",function(){$(".table-responsive").css("overflow","inherit")});$(".table-responsive").on("hide.bs.dropdown",function(){$(".table-responsive").css("overflow","auto")})});return $}); 
* Tabler v1.0.0-beta20 (https://tabler.io)
* @version 1.0.0-beta20
* @link https://tabler.io
* Copyright 2018-2023 The Tabler Authors
* Copyright 2018-2023 codecalm.net Paweł Kuna
* Licensed under MIT (https://github.com/tabler/tabler/blob/master/LICENSE)
*/
(function(factory){"use strict";typeof define==="function"&&define.amd?define(["jquery"],factory):typeof exports==="object"&&typeof module!=="undefined"?module.exports=factory(require("jquery")):factory(jQuery)})(function($){"use strict";const bootstrap=window.bootstrap;const SIDEBAR_TRANSITION_DURATION=200;let sidebarResizeEvent;let mql=window.matchMedia("(min-width: 992px)");const debounce=(func,wait,immediate)=>{let timeout;return function(){let context=this,args=arguments;let later=function(){timeout=null;if(!immediate)func.apply(context,args)};let callNow=immediate&&!timeout;clearTimeout(timeout);timeout=setTimeout(later,wait);if(callNow)func.apply(context,args)}};const resizeSidebar=()=>{if(typeof sidebarResizeEvent==="undefined"){sidebarResizeEvent=new Event("sidebar-resize")}}
const Sidebar=function(element,options){this.config=Object.assign({},{transition:true,activeLinks:true},options);this.element=element;this.shown=false;this.init()};Sidebar.prototype.init=function(){const self=this;const body=document.querySelector("body");const sidebar=this.element;this.shown=body.classList.contains("sidebar-show")||sidebar.classList.contains("show");body.style.overflow="";document.documentElement.style.overflow="";body.style.paddingRight="";if(this.shown){body.classList.add("sidebar-show")}else{body.classList.remove("sidebar-show")}
if(this.config.activeLinks){let currentPage=window.location.pathname.split("/").pop();if(currentPage==="")currentPage="index.html";let currentLink=sidebar.querySelector(".sidebar-link[href$='"+currentPage+"']:not(.sidebar-link--disabled)");if(currentLink){currentLink.classList.add("active");let parentEl=currentLink.parentNode.closest(".sidebar-dropdown-item");if(parentEl){let sidebarLink=parentEl.querySelector(".sidebar-link");if(sidebarLink){sidebarLink.classList.add("active");const sidebarGroup=sidebarLink.closest(".sidebar-group");if(sidebarGroup){sidebarGroup.classList.add("show");const submenu=sidebarLink.nextElementSibling;if(submenu){submenu.classList.add("show")}}}}}}}
sidebar.querySelectorAll(".sidebar-dropdown-toggle").forEach(toggle=>{const parent=toggle.closest(".sidebar-dropdown-item");toggle.addEventListener("click",()=>{const sidebarGroup=toggle.parentNode.querySelector(".sidebar-group");if(sidebarGroup){sidebarGroup.classList.toggle("show")}
const submenu=toggle.nextElementSibling;if(submenu){submenu.classList.toggle("show");submenu.style.maxHeight=submenu.classList.contains("show")?submenu.scrollHeight+submenu.querySelectorAll(".sidebar-submenu").length*12+"px":"0px"}})});this.listeners();return this};Sidebar.prototype.listeners=function(){let self=this;const sidebar=this.element;document.addEventListener("click",function(event){const element=event.target;if(element.classList.contains("sidebar-toggle")||element.closest(".sidebar-toggle")){self.toggle()}else if(element.classList.contains("sidebar-close")||element.closest(".sidebar-close")){self.close()}});document.addEventListener("keydown",function(event){if(event.key==="Escape"&&self.shown&&!sidebar.dataset.sidebarOverlay){self.close()}})};Sidebar.prototype.toggle=function(){if(this.shown){this.close()}else{this.open()}};Sidebar.prototype.open=function(){const self=this;const body=document.querySelector("body");this.shown=true;body.classList.add("sidebar-show");setTimeout(function(){resizeSidebar()},SIDEBAR_TRANSITION_DURATION);return this};Sidebar.prototype.close=function(){const self=this;const body=document.querySelector("body");this.shown=false;body.classList.remove("sidebar-show");return this};document.addEventListener("DOMContentLoaded",()=>{const sidebarToggle=document.querySelector(".sidebar-toggle");const sidebar=document.getElementById("sidebar");if(sidebarToggle&&sidebar){sidebarToggle.addEventListener("click",()=>{sidebar.classList.toggle("collapsed");const isCollapsed=sidebar.classList.contains("collapsed");localStorage.setItem("sidebarCollapsed",isCollapsed);const sidebarToggleIcon=document.querySelector(".sidebar-toggle i");if(sidebarToggleIcon){sidebarToggleIcon.classList.toggle("fa-chevron-left",!isCollapsed);sidebarToggleIcon.classList.toggle("fa-chevron-right",isCollapsed)}});const storedState=localStorage.getItem("sidebarCollapsed");if(storedState==="true"){sidebar.classList.add("collapsed");const sidebarToggleIcon=document.querySelector(".sidebar-toggle i");if(sidebarToggleIcon){sidebarToggleIcon.classList.remove("fa-chevron-left");sidebarToggleIcon.classList.add("fa-chevron-right")}}}
const offcanvasSidebar=new Sidebar(document.querySelector(".sidebar"))});$(document).on("click",".alert-close",function(e){e.preventDefault();const alert=$(this).closest(".alert");alert.fadeOut(400,function(){alert.remove()})});$(document).ready(function(){$(".table-responsive").on("show.bs.dropdown",function(){$(".table-responsive").css("overflow","inherit")});$(".table-responsive").on("hide.bs.dropdown",function(){$(".table-responsive").css("overflow","auto")})});return $}); 