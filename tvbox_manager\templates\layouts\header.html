<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}TVBox Manager{% endblock %}</title>
    
    <!-- 字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- 图标 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    
    <!-- 主样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    
    {% block extra_head %}{% endblock %}
</head>
<body>
    <div class="app-container">
        <!-- 顶部导航栏 -->
        <nav class="navbar">
            <!-- 移动端菜单按钮 -->
            <button id="mobile-menu-toggle" class="mobile-toggle">
                <i class="fas fa-bars"></i>
            </button>
            
            <!-- 品牌标识 -->
            <div class="navbar-brand">
                <i class="fas fa-tv mr-2"></i>
                TVBox Manager
            </div>
            
            <!-- 导航菜单 -->
            <div class="navbar-menu">
                {% if current_user.is_authenticated %}
                <div class="user-menu">
                    <div class="user-menu-button" id="user-menu-button">
                        <div class="user-avatar">
                            {{ current_user.email[0].upper() }}
                        </div>
                        <div class="user-info">
                            <div class="user-name">{{ current_user.email.split('@')[0] }}</div>
                            <div class="user-email">{{ current_user.email }}</div>
                        </div>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    
                    <div class="user-menu-dropdown" id="user-menu-dropdown">
                        <a href="{{ url_for('security.change_password') }}" class="user-menu-item">
                            <i class="fas fa-key user-menu-icon"></i>
                            修改密码
                        </a>
                        <a href="{{ url_for('security.logout') }}" class="user-menu-item">
                            <i class="fas fa-sign-out-alt user-menu-icon"></i>
                            退出登录
                        </a>
                    </div>
                </div>
                {% else %}
                <div class="auth-buttons">
                    <a href="{{ url_for('security.login') }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-sign-in-alt mr-2"></i> 登录
                    </a>
                    {% if security.registerable %}
                    <a href="{{ url_for('security.register') }}" class="btn btn-primary btn-sm ml-2">
                        <i class="fas fa-user-plus mr-2"></i> 注册
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </nav> 
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}TVBox Manager{% endblock %}</title>
    
    <!-- 字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- 图标 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    
    <!-- 主样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    
    {% block extra_head %}{% endblock %}
</head>
<body>
    <div class="app-container">
        <!-- 顶部导航栏 -->
        <nav class="navbar">
            <!-- 移动端菜单按钮 -->
            <button id="mobile-menu-toggle" class="mobile-toggle">
                <i class="fas fa-bars"></i>
            </button>
            
            <!-- 品牌标识 -->
            <div class="navbar-brand">
                <i class="fas fa-tv mr-2"></i>
                TVBox Manager
            </div>
            
            <!-- 导航菜单 -->
            <div class="navbar-menu">
                {% if current_user.is_authenticated %}
                <div class="user-menu">
                    <div class="user-menu-button" id="user-menu-button">
                        <div class="user-avatar">
                            {{ current_user.email[0].upper() }}
                        </div>
                        <div class="user-info">
                            <div class="user-name">{{ current_user.email.split('@')[0] }}</div>
                            <div class="user-email">{{ current_user.email }}</div>
                        </div>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    
                    <div class="user-menu-dropdown" id="user-menu-dropdown">
                        <a href="{{ url_for('security.change_password') }}" class="user-menu-item">
                            <i class="fas fa-key user-menu-icon"></i>
                            修改密码
                        </a>
                        <a href="{{ url_for('security.logout') }}" class="user-menu-item">
                            <i class="fas fa-sign-out-alt user-menu-icon"></i>
                            退出登录
                        </a>
                    </div>
                </div>
                {% else %}
                <div class="auth-buttons">
                    <a href="{{ url_for('security.login') }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-sign-in-alt mr-2"></i> 登录
                    </a>
                    {% if security.registerable %}
                    <a href="{{ url_for('security.register') }}" class="btn btn-primary btn-sm ml-2">
                        <i class="fas fa-user-plus mr-2"></i> 注册
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </nav> 