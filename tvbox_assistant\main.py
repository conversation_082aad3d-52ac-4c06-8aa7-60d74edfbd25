#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
TVBox助手主程序入口
提供Web界面的启动方式
"""

import os
import sys
import argparse
import asyncio
import logging
import inspect

# 修复相对导入问题
# 1. 获取当前文件的目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 2. 将父目录添加到sys.path
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# 改用绝对导入
from tvbox_assistant.cli import main as cli_main
from tvbox_assistant.web.app import run_app as web_main
from tvbox_assistant.web_init import init_web_app

def configure_logging(level=logging.INFO, log_file=None):
    """配置日志系统"""
    # 格式化
    log_format = '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    
    # 创建格式化器
    formatter = logging.Formatter(fmt=log_format, datefmt=date_format)
    
    # 根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # 清除现有处理程序
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 控制台处理程序
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # 文件处理程序(如果指定)
    if log_file:
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    # 设置各模块日志级别
    logging.getLogger('werkzeug').setLevel(logging.INFO)  # Flask的WSGI日志
    logging.getLogger('urllib3').setLevel(logging.INFO)   # HTTP请求日志
    
    # 设置更详细的日志输出
    logging.getLogger('tvbox_web').setLevel(level)
    logging.getLogger('tvbox_routes').setLevel(level)
    logging.getLogger('tvbox_api').setLevel(level)
    logging.getLogger('tvbox_parser').setLevel(level)
    logging.getLogger('tvbox_decryptor').setLevel(level)
    logging.getLogger('tvbox_downloader').setLevel(level)
    logging.getLogger('tvbox_processor').setLevel(level)
    logging.getLogger('tvbox_init').setLevel(level)
    
    # 确保新的解密器日志也能显示
    logging.getLogger('tvbox_assistant.decryptor').setLevel(level)
    
    return root_logger

def main():
    """主程序入口"""
    parser = argparse.ArgumentParser(description='TVBox助手 - 解析、解密和管理TVBox配置文件')
    
    # Web模式参数
    parser.add_argument('-H', '--host', default='127.0.0.1', help='Web服务主机地址')
    parser.add_argument('-p', '--port', type=int, default=5000, help='Web服务端口')
    parser.add_argument('-d', '--debug', action='store_true', help='开启调试模式')
    parser.add_argument('-l', '--log-file', help='日志文件路径')
    parser.add_argument('-v', '--verbose', action='store_true', help='显示详细日志')
    parser.add_argument('--init-only', action='store_true', help='仅初始化目录结构，不启动服务')
    
    # 解析参数
    args = parser.parse_args()
    
    # 配置日志系统
    log_level = logging.DEBUG if args.debug or args.verbose else logging.INFO
    logger = configure_logging(level=log_level, log_file=args.log_file)
    
    # 打印环境信息
    try:
        from tvbox_assistant import __version__
        version = __version__
    except ImportError:
        version = '1.0.0'
    
    logger.info(f"TVBox助手启动，版本: {version}")
    logger.info(f"Python版本: {sys.version}")
    logger.info(f"工作目录: {os.getcwd()}")
    
    # 初始化Web应用目录结构
    init_web_app()
    
    # 如果只需初始化，则退出
    if args.init_only:
        logger.info("初始化完成，退出程序")
        return
    
    # 检查模块路径
    module_path = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
    package_path = os.path.dirname(module_path)
    template_path = os.path.join(module_path, 'web', 'templates')
    static_path = os.path.join(module_path, 'web', 'static')
    
    logger.info(f"模块路径: {module_path}")
    logger.info(f"包路径: {package_path}")
    logger.info(f"模板路径: {template_path}")
    logger.info(f"静态文件路径: {static_path}")
    
    # 检查模板文件夹是否存在
    if os.path.exists(template_path):
        logger.info(f"模板目录存在，包含文件: {os.listdir(template_path)}")
    else:
        logger.warning(f"模板目录不存在: {template_path}")
    
    # 启动Web服务
    logger.info(f"以Web模式启动，访问地址: http://{args.host}:{args.port}")
    
    try:
        web_main(host=args.host, port=args.port, debug=args.debug)
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序异常: {str(e)}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main() 