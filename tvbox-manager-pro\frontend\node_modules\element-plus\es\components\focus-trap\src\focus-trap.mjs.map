{"version": 3, "file": "focus-trap.mjs", "sources": ["../../../../../../packages/components/focus-trap/src/focus-trap.vue"], "sourcesContent": ["<template>\n  <slot :handle-keydown=\"onKeydown\" />\n</template>\n\n<script lang=\"ts\">\nimport {\n  defineComponent,\n  nextTick,\n  onBeforeUnmount,\n  onMounted,\n  provide,\n  ref,\n  unref,\n  watch,\n} from 'vue'\nimport { isNil } from 'lodash-unified'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport { useEscapeKeydown } from '@element-plus/hooks'\nimport { isString } from '@element-plus/utils'\nimport {\n  createFocusOutPreventedEvent,\n  focusFirstDescendant,\n  focusableStack,\n  getEdges,\n  isFocusCausedByUserEvent,\n  obtainAllFocusableElements,\n  tryFocus,\n  useFocusReason,\n} from './utils'\nimport {\n  FOCUS_AFTER_RELEASED,\n  FOCUS_AFTER_TRAPPED,\n  FOCUS_AFTER_TRAPPED_OPTS,\n  FOCUS_TRAP_INJECTION_KEY,\n  ON_RELEASE_FOCUS_EVT,\n  ON_TRAP_FOCUS_EVT,\n} from './tokens'\n\nimport type { PropType } from 'vue'\nimport type { FocusLayer } from './utils'\n\nexport default defineComponent({\n  name: 'ElFocusTrap',\n  inheritAttrs: false,\n  props: {\n    loop: Boolean,\n    trapped: Boolean,\n    focusTrapEl: Object as PropType<HTMLElement>,\n    focusStartEl: {\n      type: [Object, String] as PropType<'container' | 'first' | HTMLElement>,\n      default: 'first',\n    },\n  },\n  emits: [\n    ON_TRAP_FOCUS_EVT,\n    ON_RELEASE_FOCUS_EVT,\n    'focusin',\n    'focusout',\n    'focusout-prevented',\n    'release-requested',\n  ],\n  setup(props, { emit }) {\n    const forwardRef = ref<HTMLElement | undefined>()\n    let lastFocusBeforeTrapped: HTMLElement | null\n    let lastFocusAfterTrapped: HTMLElement | null\n\n    const { focusReason } = useFocusReason()\n\n    useEscapeKeydown((event) => {\n      if (props.trapped && !focusLayer.paused) {\n        emit('release-requested', event)\n      }\n    })\n\n    const focusLayer: FocusLayer = {\n      paused: false,\n      pause() {\n        this.paused = true\n      },\n      resume() {\n        this.paused = false\n      },\n    }\n\n    const onKeydown = (e: KeyboardEvent) => {\n      if (!props.loop && !props.trapped) return\n      if (focusLayer.paused) return\n\n      const { code, altKey, ctrlKey, metaKey, currentTarget, shiftKey } = e\n      const { loop } = props\n      const isTabbing =\n        code === EVENT_CODE.tab && !altKey && !ctrlKey && !metaKey\n\n      const currentFocusingEl = document.activeElement\n      if (isTabbing && currentFocusingEl) {\n        const container = currentTarget as HTMLElement\n        const [first, last] = getEdges(container)\n        const isTabbable = first && last\n        if (!isTabbable) {\n          if (currentFocusingEl === container) {\n            const focusoutPreventedEvent = createFocusOutPreventedEvent({\n              focusReason: focusReason.value,\n            })\n            emit('focusout-prevented', focusoutPreventedEvent)\n            if (!focusoutPreventedEvent.defaultPrevented) {\n              e.preventDefault()\n            }\n          }\n        } else {\n          if (!shiftKey && currentFocusingEl === last) {\n            const focusoutPreventedEvent = createFocusOutPreventedEvent({\n              focusReason: focusReason.value,\n            })\n            emit('focusout-prevented', focusoutPreventedEvent)\n            if (!focusoutPreventedEvent.defaultPrevented) {\n              e.preventDefault()\n              if (loop) tryFocus(first, true)\n            }\n          } else if (\n            shiftKey &&\n            [first, container].includes(currentFocusingEl as HTMLElement)\n          ) {\n            const focusoutPreventedEvent = createFocusOutPreventedEvent({\n              focusReason: focusReason.value,\n            })\n            emit('focusout-prevented', focusoutPreventedEvent)\n            if (!focusoutPreventedEvent.defaultPrevented) {\n              e.preventDefault()\n              if (loop) tryFocus(last, true)\n            }\n          }\n        }\n      }\n    }\n\n    provide(FOCUS_TRAP_INJECTION_KEY, {\n      focusTrapRef: forwardRef,\n      onKeydown,\n    })\n\n    watch(\n      () => props.focusTrapEl,\n      (focusTrapEl) => {\n        if (focusTrapEl) {\n          forwardRef.value = focusTrapEl\n        }\n      },\n      { immediate: true }\n    )\n\n    watch([forwardRef], ([forwardRef], [oldForwardRef]) => {\n      if (forwardRef) {\n        forwardRef.addEventListener('keydown', onKeydown)\n        forwardRef.addEventListener('focusin', onFocusIn)\n        forwardRef.addEventListener('focusout', onFocusOut)\n      }\n      if (oldForwardRef) {\n        oldForwardRef.removeEventListener('keydown', onKeydown)\n        oldForwardRef.removeEventListener('focusin', onFocusIn)\n        oldForwardRef.removeEventListener('focusout', onFocusOut)\n      }\n    })\n\n    const trapOnFocus = (e: Event) => {\n      emit(ON_TRAP_FOCUS_EVT, e)\n    }\n    const releaseOnFocus = (e: Event) => emit(ON_RELEASE_FOCUS_EVT, e)\n\n    const onFocusIn = (e: FocusEvent) => {\n      const trapContainer = unref(forwardRef)\n      if (!trapContainer) return\n\n      const target = e.target as HTMLElement | null\n      const relatedTarget = e.relatedTarget as HTMLElement | null\n      const isFocusedInTrap = target && trapContainer.contains(target)\n\n      if (!props.trapped) {\n        const isPrevFocusedInTrap =\n          relatedTarget && trapContainer.contains(relatedTarget)\n        if (!isPrevFocusedInTrap) {\n          lastFocusBeforeTrapped = relatedTarget\n        }\n      }\n\n      if (isFocusedInTrap) emit('focusin', e)\n\n      if (focusLayer.paused) return\n\n      if (props.trapped) {\n        if (isFocusedInTrap) {\n          lastFocusAfterTrapped = target\n        } else {\n          tryFocus(lastFocusAfterTrapped, true)\n        }\n      }\n    }\n\n    const onFocusOut = (e: Event) => {\n      const trapContainer = unref(forwardRef)\n      if (focusLayer.paused || !trapContainer) return\n\n      if (props.trapped) {\n        const relatedTarget = (e as FocusEvent)\n          .relatedTarget as HTMLElement | null\n        if (!isNil(relatedTarget) && !trapContainer.contains(relatedTarget)) {\n          // Give embedded focus layer time to pause this layer before reclaiming focus\n          // And only reclaim focus if it should currently be trapping\n          setTimeout(() => {\n            if (!focusLayer.paused && props.trapped) {\n              const focusoutPreventedEvent = createFocusOutPreventedEvent({\n                focusReason: focusReason.value,\n              })\n              emit('focusout-prevented', focusoutPreventedEvent)\n              if (!focusoutPreventedEvent.defaultPrevented) {\n                tryFocus(lastFocusAfterTrapped, true)\n              }\n            }\n          }, 0)\n        }\n      } else {\n        const target = e.target as HTMLElement | null\n        const isFocusedInTrap = target && trapContainer.contains(target)\n        if (!isFocusedInTrap) emit('focusout', e)\n      }\n    }\n\n    async function startTrap() {\n      // Wait for forwardRef to resolve\n      await nextTick()\n      const trapContainer = unref(forwardRef)\n      if (trapContainer) {\n        focusableStack.push(focusLayer)\n        const prevFocusedElement = trapContainer.contains(\n          document.activeElement\n        )\n          ? lastFocusBeforeTrapped\n          : document.activeElement\n        lastFocusBeforeTrapped = prevFocusedElement as HTMLElement | null\n        const isPrevFocusContained = trapContainer.contains(prevFocusedElement)\n        if (!isPrevFocusContained) {\n          const focusEvent = new Event(\n            FOCUS_AFTER_TRAPPED,\n            FOCUS_AFTER_TRAPPED_OPTS\n          )\n          trapContainer.addEventListener(FOCUS_AFTER_TRAPPED, trapOnFocus)\n          trapContainer.dispatchEvent(focusEvent)\n          if (!focusEvent.defaultPrevented) {\n            nextTick(() => {\n              let focusStartEl = props.focusStartEl\n              if (!isString(focusStartEl)) {\n                tryFocus(focusStartEl)\n                if (document.activeElement !== focusStartEl) {\n                  focusStartEl = 'first'\n                }\n              }\n              if (focusStartEl === 'first') {\n                focusFirstDescendant(\n                  obtainAllFocusableElements(trapContainer),\n                  true\n                )\n              }\n              if (\n                document.activeElement === prevFocusedElement ||\n                focusStartEl === 'container'\n              ) {\n                tryFocus(trapContainer)\n              }\n            })\n          }\n        }\n      }\n    }\n\n    function stopTrap() {\n      const trapContainer = unref(forwardRef)\n\n      if (trapContainer) {\n        trapContainer.removeEventListener(FOCUS_AFTER_TRAPPED, trapOnFocus)\n\n        const releasedEvent = new CustomEvent(FOCUS_AFTER_RELEASED, {\n          ...FOCUS_AFTER_TRAPPED_OPTS,\n          detail: {\n            focusReason: focusReason.value,\n          },\n        })\n        trapContainer.addEventListener(FOCUS_AFTER_RELEASED, releaseOnFocus)\n        trapContainer.dispatchEvent(releasedEvent)\n        if (\n          !releasedEvent.defaultPrevented &&\n          (focusReason.value == 'keyboard' ||\n            !isFocusCausedByUserEvent() ||\n            trapContainer.contains(document.activeElement))\n        ) {\n          tryFocus(lastFocusBeforeTrapped ?? document.body)\n        }\n\n        trapContainer.removeEventListener(FOCUS_AFTER_RELEASED, releaseOnFocus)\n        focusableStack.remove(focusLayer)\n      }\n    }\n\n    onMounted(() => {\n      if (props.trapped) {\n        startTrap()\n      }\n\n      watch(\n        () => props.trapped,\n        (trapped) => {\n          if (trapped) {\n            startTrap()\n          } else {\n            stopTrap()\n          }\n        }\n      )\n    })\n\n    onBeforeUnmount(() => {\n      if (props.trapped) {\n        stopTrap()\n      }\n\n      if (forwardRef.value) {\n        forwardRef.value.removeEventListener('keydown', onKeydown)\n        forwardRef.value.removeEventListener('focusin', onFocusIn)\n        forwardRef.value.removeEventListener('focusout', onFocusOut)\n        forwardRef.value = undefined\n      }\n    })\n\n    return {\n      onKeydown,\n    }\n  },\n})\n</script>\n"], "names": ["forwardRef", "_renderSlot"], "mappings": ";;;;;;;;;AAyCA,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,aAAA;AAAA,EACN,YAAc,EAAA,KAAA;AAAA,EACd,KAAO,EAAA;AAAA,IACL,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,OAAA;AAAA,IACT,WAAa,EAAA,MAAA;AAAA,IACb,YAAc,EAAA;AAAA,MACZ,IAAA,EAAM,CAAC,MAAA,EAAQ,MAAM,CAAA;AAAA,MACrB,OAAS,EAAA,OAAA;AAAA,KACX;AAAA,GACF;AAAA,EACA,KAAO,EAAA;AAAA,IACL,iBAAA;AAAA,IACA,oBAAA;AAAA,IACA,SAAA;AAAA,IACA,UAAA;AAAA,IACA,oBAAA;AAAA,IACA,mBAAA;AAAA,GACF;AAAA,EACA,KAAM,CAAA,KAAA,EAAO,EAAE,IAAA,EAAQ,EAAA;AACrB,IAAA,MAAM,aAAa,GAA6B,EAAA,CAAA;AAChD,IAAI,IAAA,sBAAA,CAAA;AACJ,IAAI,IAAA,qBAAA,CAAA;AAEJ,IAAM,MAAA,EAAE,WAAY,EAAA,GAAI,cAAe,EAAA,CAAA;AAEvC,IAAA,gBAAA,CAAiB,CAAC,KAAU,KAAA;AAC1B,MAAA,IAAI,KAAM,CAAA,OAAA,IAAW,CAAC,UAAA,CAAW,MAAQ,EAAA;AACvC,QAAA,IAAA,CAAK,qBAAqB,KAAK,CAAA,CAAA;AAAA,OACjC;AAAA,KACD,CAAA,CAAA;AAED,IAAA,MAAM,UAAyB,GAAA;AAAA,MAC7B,MAAQ,EAAA,KAAA;AAAA,MACR,KAAQ,GAAA;AACN,QAAA,IAAA,CAAK,MAAS,GAAA,IAAA,CAAA;AAAA,OAChB;AAAA,MACA,MAAS,GAAA;AACP,QAAA,IAAA,CAAK,MAAS,GAAA,KAAA,CAAA;AAAA,OAChB;AAAA,KACF,CAAA;AAEA,IAAM,MAAA,SAAA,GAAY,CAAC,CAAqB,KAAA;AACtC,MAAA,IAAI,CAAC,KAAA,CAAM,IAAQ,IAAA,CAAC,MAAM,OAAS;AACnC,QAAA;AAEA,MAAA,IAAA,UAAc,CAAA,MAAA;AACd,QAAM;AACN,MAAM,MAAA,EAAA,IAAA,EAAA,eACgB,EAAA,OAAA,EAAA,aAAkB,EAAA,aAAY,CAAC,CAAA;AAErD,MAAA,MAAM;AACN,MAAA,kBAAoC,IAAA,KAAA,UAAA,CAAA,GAAA,IAAA,CAAA,MAAA,IAAA,CAAA,OAAA,IAAA,CAAA,OAAA,CAAA;AAClC,MAAA,MAAA,iBAAkB,GAAA,QAAA,CAAA,aAAA,CAAA;AAClB,MAAA,IAAA,SAAO,IAAW,iBAAsB,EAAA;AACxC,QAAA,MAAM,yBAAsB,CAAA;AAC5B,QAAA,MAAiB,CAAA,KAAA,EAAA,IAAA,CAAA,GAAA,QAAA,CAAA,SAAA,CAAA,CAAA;AACf,QAAA,MAAI,sBAAsB,IAAW,CAAA;AACnC,QAAA,IAAA,CAAA;AAA4D,UAAA,IAC1D,iBAAyB,KAAA,SAAA,EAAA;AAAA,YAC3B,MAAC,sBAAA,GAAA,4BAAA,CAAA;AACD,cAAA,yBAA2B,KAAsB;AACjD,aAAI,CAAA,CAAA;AACF,YAAA,IAAE,CAAe,oBAAA,EAAA,sBAAA,CAAA,CAAA;AAAA,YACnB,IAAA,CAAA,sBAAA,CAAA,gBAAA,EAAA;AAAA,cACF,CAAA,CAAA,cAAA,EAAA,CAAA;AAAA,aACK;AACL,WAAI;AACF,SAAA,MAAA;AAA4D,UAAA,IAC1D,aAAa,iBAAY,KAAA,IAAA,EAAA;AAAA,YAC3B,MAAC,sBAAA,GAAA,4BAAA,CAAA;AACD,cAAA,yBAA2B,KAAsB;AACjD,aAAI,CAAA,CAAA;AACF,YAAA,IAAE,CAAe,oBAAA,EAAA,sBAAA,CAAA,CAAA;AACjB,YAAI,IAAA,CAAA,sBAAe,CAAO,gBAAI,EAAA;AAAA,cAChC,CAAA,CAAA,cAAA,EAAA,CAAA;AAAA,cACF,IAAA;AAIE,gBAAA;AAA4D,aAAA;AACjC,WAAA,MAC1B,IAAA,QAAA,IAAA,CAAA,KAAA,EAAA,SAAA,CAAA,CAAA,QAAA,CAAA,iBAAA,CAAA,EAAA;AACD,YAAA,4BAAiD,GAAA,4BAAA,CAAA;AACjD,cAAI,8BAA0C;AAC5C,aAAA,CAAA,CAAA;AACA,YAAI,IAAA,CAAA,oBAAe,EAAM,sBAAI,CAAA,CAAA;AAAA,YAC/B,IAAA,CAAA,sBAAA,CAAA,gBAAA,EAAA;AAAA,cACF,CAAA,CAAA,cAAA,EAAA,CAAA;AAAA,cACF,IAAA,IAAA;AAAA,gBACF,QAAA,CAAA,IAAA,EAAA,IAAA,CAAA,CAAA;AAAA,aACF;AAEA,WAAA;AAAkC,SAClB;AAAA,OACd;AAAA,KACD,CAAA;AAED,IAAA,OAAA,CAAA,wBAAA,EAAA;AAAA,MACE,YAAY,EAAA,UAAA;AAAA,MACZ,SAAiB;AACf,KAAA,CAAA,CAAA;AACE,IAAA,KAAA,CAAA,MAAA,KAAW,CAAQ,WAAA,EAAA,CAAA,WAAA,KAAA;AAAA,MACrB,IAAA,WAAA,EAAA;AAAA,QACF,UAAA,CAAA,KAAA,GAAA,WAAA,CAAA;AAAA,OACA;AAAkB,KACpB,EAAA,EAAA,SAAA,EAAA,IAAA,EAAA,CAAA,CAAA;AAEA,IAAM,KAAA,CAAA,CAAC,UAAU,CAAG,EAAA,CAAC,CAACA,WAAU,CAAA,EAAG,CAAC,aAAa,CAAM,KAAA;AACrD,MAAA,IAAIA,WAAY,EAAA;AACd,QAAAA,WAAAA,CAAW,gBAAiB,CAAA,SAAA,EAAW,SAAS,CAAA,CAAA;AAChD,QAAAA,WAAAA,CAAW,gBAAiB,CAAA,SAAA,EAAW,SAAS,CAAA,CAAA;AAChD,QAAAA,WAAAA,CAAW,gBAAiB,CAAA,UAAA,EAAY,UAAU,CAAA,CAAA;AAAA,OACpD;AACA,MAAA,IAAI,aAAe,EAAA;AACjB,QAAc,aAAA,CAAA,mBAAA,CAAoB,WAAW,SAAS,CAAA,CAAA;AACtD,QAAc,aAAA,CAAA,mBAAA,CAAoB,WAAW,SAAS,CAAA,CAAA;AACtD,QAAc,aAAA,CAAA,mBAAA,CAAoB,YAAY,UAAU,CAAA,CAAA;AAAA,OAC1D;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,WAAA,GAAc,CAAC,CAAa,KAAA;AAChC,MAAA,IAAA,CAAK,mBAAmB,CAAC,CAAA,CAAA;AAAA,KAC3B,CAAA;AACA,IAAA,MAAM,cAAiB,GAAA,CAAC,CAAa,KAAA,IAAA,CAAK,sBAAsB,CAAC,CAAA,CAAA;AAEjE,IAAM,MAAA,SAAA,GAAY,CAAC,CAAkB,KAAA;AACnC,MAAM,MAAA,aAAA,GAAgB,MAAM,UAAU,CAAA,CAAA;AACtC,MAAA,IAAI,CAAC,aAAe;AAEpB,QAAA;AACA,MAAA,MAAM,iBAAkB,CAAA;AACxB,MAAA,MAAM,aAAkB,GAAA,CAAA,CAAA,aAAwB,CAAA;AAEhD,MAAI,qBAAgB,GAAA,MAAA,IAAA,aAAA,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA;AAClB,MAAA,IAAA,CAAA,KACE,CAAA,OAAA,EAAA;AACF,QAAA,MAA0B,mBAAA,GAAA,aAAA,IAAA,aAAA,CAAA,QAAA,CAAA,aAAA,CAAA,CAAA;AACxB,QAAyB,IAAA,CAAA,mBAAA,EAAA;AAAA,UAC3B,sBAAA,GAAA,aAAA,CAAA;AAAA,SACF;AAEA,OAAI;AAEJ,MAAA,IAAI,eAAmB;AAEvB,QAAA,cAAmB,EAAA,CAAA,CAAA,CAAA;AACjB,MAAA,IAAA,UAAqB,CAAA,MAAA;AACnB,QAAwB,OAAA;AAAA,MAAA,IACnB,KAAA,CAAA,OAAA,EAAA;AACL,QAAA,IAAA;AAAoC,UACtC,qBAAA,GAAA,MAAA,CAAA;AAAA,SACF,MAAA;AAAA,UACF,QAAA,CAAA,qBAAA,EAAA,IAAA,CAAA,CAAA;AAEA,SAAM;AACJ,OAAM;AACN,KAAI,CAAA;AAEJ,IAAA,MAAI,UAAe,GAAA,CAAA,CAAA,KAAA;AACjB,MAAA,MAAA,qBACG,CAAA,UAAA,CAAA,CAAA;AACH,MAAI,IAAA,UAAoB,CAAA,MAAA,IAAA,CAAA,aAAoB;AAG1C,QAAA,OAAA;AACE,MAAA,IAAA,KAAA,CAAI,OAAC,EAAA;AACH,QAAA,MAAA,+BAA+B,CAA6B;AAAA,QAAA,IAAA,CAAA,mBACjC,CAAA,IAAA,CAAA,aAAA,CAAA,QAAA,CAAA,aAAA,CAAA,EAAA;AAAA,UAAA,UAC1B,CAAA,MAAA;AACD,YAAA,IAAA,CAAA,0BAAiD,CAAA,OAAA,EAAA;AACjD,cAAI,4BAAwB,GAAkB,4BAAA,CAAA;AAC5C,gBAAA;AAAoC,eACtC,CAAA,CAAA;AAAA,cACF,IAAA,CAAA,oBAAA,EAAA,sBAAA,CAAA,CAAA;AAAA,cACE,IAAA,CAAA,sBAAA,CAAA,gBAAA,EAAA;AAAA,gBACN,QAAA,CAAA,qBAAA,EAAA,IAAA,CAAA,CAAA;AAAA,eACK;AACL,aAAA;AACA,WAAA,EAAA,CAAM,CAAkB,CAAA;AACxB,SAAA;AAAwC,OAC1C,MAAA;AAAA,QACF,MAAA,MAAA,GAAA,CAAA,CAAA,MAAA,CAAA;AAEA,QAAA,MAAA,eAA2B,GAAA,MAAA,IAAA,aAAA,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA;AAEzB,QAAA,IAAM,CAAS,eAAA;AACf,UAAM,IAAA,CAAA,UAAA,EAAA,CAAgB;AACtB,OAAA;AACE,KAAA,CAAA;AACA,IAAA;AAAyC,MAAA,MAC9B,QAAA,EAAA,CAAA;AAAA,MACX,MACI,gCACS,CAAA,CAAA;AACb,MAAyB,IAAA,aAAA,EAAA;AACzB,QAAM,cAAA,CAAA,IAAA,CAAA,UAAqC,CAAA,CAAA;AAC3C,QAAA,MAA2B,kBAAA,GAAA,aAAA,CAAA,QAAA,CAAA,QAAA,CAAA,aAAA,CAAA,GAAA,sBAAA,GAAA,QAAA,CAAA,aAAA,CAAA;AACzB,QAAA,sBAAuB,GAAA,kBAAA,CAAA;AAAA,QACrB,MAAA,oBAAA,GAAA,aAAA,CAAA,QAAA,CAAA,kBAAA,CAAA,CAAA;AAAA,QACA,IAAA,CAAA,oBAAA,EAAA;AAAA,UACF,MAAA,UAAA,GAAA,IAAA,KAAA,CAAA,mBAAA,EAAA,wBAAA,CAAA,CAAA;AACA,UAAc,aAAA,CAAA,gBAAA,CAAiB,qBAAqB,WAAW,CAAA,CAAA;AAC/D,UAAA,aAAA,CAAc,cAAc,UAAU,CAAA,CAAA;AACtC,UAAI,IAAA,CAAC,WAAW,gBAAkB,EAAA;AAChC,YAAA,QAAA,CAAS,MAAM;AACb,cAAA,IAAI,eAAe,KAAM,CAAA,YAAA,CAAA;AACzB,cAAI,IAAA,CAAC,QAAS,CAAA,YAAY,CAAG,EAAA;AAC3B,gBAAA,QAAA,CAAS,YAAY,CAAA,CAAA;AACrB,gBAAI,IAAA,QAAA,CAAS,kBAAkB,YAAc,EAAA;AAC3C,kBAAe,YAAA,GAAA,OAAA,CAAA;AAAA,iBACjB;AAAA,eACF;AACA,cAAA,IAAI,iBAAiB,OAAS,EAAA;AAC5B,gBAAA,oBAAA,CAAA,0BAAA,CAAA,aAAA,CAAA,EAAA,IAAA,CAAA,CAAA;AAAA,eAAA;AAC0C,cACxC,IAAA,QAAA,CAAA,aAAA,KAAA,kBAAA,IAAA,YAAA,KAAA,WAAA,EAAA;AAAA,gBACF,QAAA,CAAA,aAAA,CAAA,CAAA;AAAA,eACF;AACA,aAAA,CAAA,CAAA;AAIE,WAAA;AAAsB,SACxB;AAAA,OAAA;AACD,KACH;AAAA,IACF,SAAA,QAAA,GAAA;AAAA,MACF,MAAA,aAAA,GAAA,KAAA,CAAA,UAAA,CAAA,CAAA;AAAA,MACF,IAAA,aAAA,EAAA;AAEA,QAAA,aAAoB,CAAA,mBAAA,CAAA,mBAAA,EAAA,WAAA,CAAA,CAAA;AAClB,QAAM,MAAA,aAAA,OAAsB,WAAU,CAAA,oBAAA,EAAA;AAEtC,UAAI,GAAe,wBAAA;AACjB,UAAc,MAAA,EAAA;AAEd,YAAM,WAAA,EAAA,WAAoB,CAAA,KAAA;AAAkC,WACvD;AAAA,SAAA,CACH,CAAQ;AAAA,QAAA,8BACmB,CAAA,oBAAA,EAAA,cAAA,CAAA,CAAA;AAAA,QAC3B,aAAA,CAAA,aAAA,CAAA,aAAA,CAAA,CAAA;AAAA,QACF,IAAC,CAAA,aAAA,CAAA,gBAAA,KAAA,WAAA,CAAA,KAAA,IAAA,UAAA,IAAA,CAAA,wBAAA,EAAA,IAAA,aAAA,CAAA,QAAA,CAAA,QAAA,CAAA,aAAA,CAAA,CAAA,EAAA;AACD,UAAc,QAAA,CAAA,uDAAqD,GAAA,QAAA,CAAA,IAAA,CAAA,CAAA;AACnE,SAAA;AACA,QAAA,aACG,CAAA,mBACA,CAAA,oBAAqB,EAAA,cAAA,CAAA,CAAA;AAItB,QAAS,cAAA,CAAA,MAAA,CAAA,UAAA,CAAA,CAAA;AAAuC,OAClD;AAEA,KAAc;AACd,IAAA,SAAA,CAAA,MAAA;AAAgC,MAClC,IAAA,KAAA,CAAA,OAAA,EAAA;AAAA,QACF,SAAA,EAAA,CAAA;AAEA,OAAA;AACE,MAAA,YAAmB,KAAA,CAAA,OAAA,EAAA,CAAA,OAAA,KAAA;AACjB,QAAU,IAAA,OAAA,EAAA;AAAA,UACZ,SAAA,EAAA,CAAA;AAEA,SAAA,MAAA;AAAA,kBACc,EAAA,CAAA;AAAA,SACX;AACC,OAAA,CAAA,CAAA;AACE,KAAU,CAAA,CAAA;AAAA,IAAA,eACL,CAAA,MAAA;AACL,MAAS,IAAA,KAAA,CAAA,OAAA,EAAA;AAAA,QACX,QAAA,EAAA,CAAA;AAAA,OACF;AAAA,MACF,IAAA,UAAA,CAAA,KAAA,EAAA;AAAA,QACD,UAAA,CAAA,KAAA,CAAA,mBAAA,CAAA,SAAA,EAAA,SAAA,CAAA,CAAA;AAED,QAAA,UAAA,CAAA,KAAsB,CAAA,mBAAA,CAAA,SAAA,EAAA,SAAA,CAAA,CAAA;AACpB,QAAA,UAAmB,CAAA,KAAA,CAAA,mBAAA,CAAA,UAAA,EAAA,UAAA,CAAA,CAAA;AACjB,QAAS,UAAA,CAAA,KAAA,GAAA,KAAA,CAAA,CAAA;AAAA,OACX;AAEA,KAAA,CAAA,CAAA;AACE,IAAW,OAAA;AACX,MAAW,SAAA;AACX,KAAW,CAAA;AACX,GAAA;AAAmB,CACrB,CAAA,CAAA;AAGF,SAAO,WAAA,CAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AAAA,EACL,OAAAC,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,EAAA,EAAA,aAAA,EAAA,IAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAA,CACF;AAEJ,kBAAC,gBAAA,WAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,WAAA,CAAA,EAAA,CAAA,QAAA,EAAA,gBAAA,CAAA,CAAA,CAAA;;;;"}