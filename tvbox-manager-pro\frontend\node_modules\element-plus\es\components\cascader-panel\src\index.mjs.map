{"version": 3, "file": "index.mjs", "sources": ["../../../../../../packages/components/cascader-panel/src/index.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"[ns.b('panel'), ns.is('bordered', border)]\"\n    @keydown=\"handleKeyDown\"\n  >\n    <el-cascader-menu\n      v-for=\"(menu, index) in menus\"\n      :key=\"index\"\n      :ref=\"(item) => (menuList[index] = item as CascaderMenuInstance)\"\n      :index=\"index\"\n      :nodes=\"[...menu]\"\n    >\n      <template #empty>\n        <slot name=\"empty\" />\n      </template>\n    </el-cascader-menu>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  nextTick,\n  onBeforeUpdate,\n  onMounted,\n  provide,\n  reactive,\n  ref,\n  useSlots,\n  watch,\n} from 'vue'\nimport { cloneDeep, flattenDeep, isEqual } from 'lodash-unified'\nimport {\n  castArray,\n  focusNode,\n  getSibling,\n  isClient,\n  isEmpty,\n  scrollIntoView,\n  unique,\n} from '@element-plus/utils'\nimport {\n  CHANGE_EVENT,\n  EVENT_CODE,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { useNamespace } from '@element-plus/hooks'\nimport ElCascaderMenu from './menu.vue'\nimport Store from './store'\nimport Node from './node'\nimport {\n  cascaderPanelEmits,\n  cascaderPanelProps,\n  useCascaderConfig,\n} from './config'\nimport { checkNode, getMenuIndex, sortByOriginalOrder } from './utils'\nimport { CASCADER_PANEL_INJECTION_KEY } from './types'\n\nimport type {\n  default as CascaderNode,\n  CascaderNodeValue,\n  CascaderOption,\n  CascaderValue,\n} from './node'\nimport type { ElCascaderPanelContext } from './types'\nimport type { CascaderMenuInstance } from './instance'\n\ndefineOptions({\n  name: 'ElCascaderPanel',\n  inheritAttrs: false,\n})\n\nconst props = defineProps(cascaderPanelProps)\nconst emit = defineEmits(cascaderPanelEmits)\n\n// for interrupt sync check status in lazy mode\nlet manualChecked = false\n\nconst ns = useNamespace('cascader')\nconst config = useCascaderConfig(props)\nconst slots = useSlots()\n\nlet store: Store\nconst initialLoaded = ref(true)\nconst menuList = ref<CascaderMenuInstance[]>([])\nconst checkedValue = ref<CascaderValue>()\nconst menus = ref<CascaderNode[][]>([])\nconst expandingNode = ref<CascaderNode>()\nconst checkedNodes = ref<CascaderNode[]>([])\n\nconst isHoverMenu = computed(() => config.value.expandTrigger === 'hover')\nconst renderLabelFn = computed(() => props.renderLabel || slots.default)\n\nconst initStore = () => {\n  const { options } = props\n  const cfg = config.value\n\n  manualChecked = false\n  store = new Store(options, cfg)\n  menus.value = [store.getNodes()]\n\n  if (cfg.lazy && isEmpty(props.options)) {\n    initialLoaded.value = false\n    lazyLoad(undefined, (list) => {\n      if (list) {\n        store = new Store(list, cfg)\n        menus.value = [store.getNodes()]\n      }\n      initialLoaded.value = true\n      syncCheckedValue(false, true)\n    })\n  } else {\n    syncCheckedValue(false, true)\n  }\n}\n\nconst lazyLoad: ElCascaderPanelContext['lazyLoad'] = (node, cb) => {\n  const cfg = config.value\n  node! = node || new Node({}, cfg, undefined, true)\n  node.loading = true\n\n  const resolve = (dataList?: CascaderOption[]) => {\n    const _node = node as Node\n    const parent = _node.root ? null : _node\n    dataList && store?.appendNodes(dataList, parent as Node)\n    _node.loading = false\n    _node.loaded = true\n    _node.childrenData = _node.childrenData || []\n    dataList && cb?.(dataList)\n  }\n\n  cfg.lazyLoad(node, resolve)\n}\n\nconst expandNode: ElCascaderPanelContext['expandNode'] = (node, silent) => {\n  const { level } = node\n  const newMenus = menus.value.slice(0, level)\n  let newExpandingNode: CascaderNode\n\n  if (node.isLeaf) {\n    newExpandingNode = node.pathNodes[level - 2]\n  } else {\n    newExpandingNode = node\n    newMenus.push(node.children)\n  }\n\n  if (expandingNode.value?.uid !== newExpandingNode?.uid) {\n    expandingNode.value = node\n    menus.value = newMenus\n    !silent && emit('expand-change', node?.pathValues || [])\n  }\n}\n\nconst handleCheckChange: ElCascaderPanelContext['handleCheckChange'] = (\n  node,\n  checked,\n  emitClose = true\n) => {\n  const { checkStrictly, multiple } = config.value\n  const oldNode = checkedNodes.value[0]\n  manualChecked = true\n\n  !multiple && oldNode?.doCheck(false)\n  node.doCheck(checked)\n  calculateCheckedValue()\n  emitClose && !multiple && !checkStrictly && emit('close')\n  !emitClose && !multiple && !checkStrictly && expandParentNode(node)\n}\n\nconst expandParentNode = (node: Node | undefined) => {\n  if (!node) return\n  node = node.parent\n  expandParentNode(node)\n  node && expandNode(node)\n}\n\nconst getFlattedNodes = (leafOnly: boolean) => store?.getFlattedNodes(leafOnly)\n\nconst getCheckedNodes = (leafOnly: boolean) => {\n  return getFlattedNodes(leafOnly)?.filter(({ checked }) => checked !== false)\n}\n\nconst clearCheckedNodes = () => {\n  checkedNodes.value.forEach((node) => node.doCheck(false))\n  calculateCheckedValue()\n  menus.value = menus.value.slice(0, 1)\n  expandingNode.value = undefined\n  emit('expand-change', [])\n}\n\nconst calculateCheckedValue = () => {\n  const { checkStrictly, multiple } = config.value\n  const oldNodes = checkedNodes.value\n  const newNodes = getCheckedNodes(!checkStrictly)!\n  // ensure the original order\n  const nodes = sortByOriginalOrder(oldNodes, newNodes)\n  const values = nodes.map((node) => node.valueByOption)\n  checkedNodes.value = nodes\n  checkedValue.value = multiple ? values : values[0]\n}\n\nconst syncCheckedValue = (loaded = false, forced = false) => {\n  const { modelValue } = props\n  const { lazy, multiple, checkStrictly } = config.value\n  const leafOnly = !checkStrictly\n\n  if (\n    !initialLoaded.value ||\n    manualChecked ||\n    (!forced && isEqual(modelValue, checkedValue.value))\n  )\n    return\n\n  if (lazy && !loaded) {\n    const values: CascaderNodeValue[] = unique(\n      flattenDeep(castArray(modelValue as CascaderNodeValue[]))\n    )\n    const nodes = values\n      .map((val) => store?.getNodeByValue(val))\n      .filter((node) => !!node && !node.loaded && !node.loading) as Node[]\n\n    if (nodes.length) {\n      nodes.forEach((node) => {\n        lazyLoad(node, () => syncCheckedValue(false, forced))\n      })\n    } else {\n      syncCheckedValue(true, forced)\n    }\n  } else {\n    const values = multiple ? castArray(modelValue) : [modelValue]\n    const nodes = unique(\n      values.map((val) =>\n        store?.getNodeByValue(val as CascaderNodeValue, leafOnly)\n      )\n    ) as Node[]\n    syncMenuState(nodes, forced)\n    checkedValue.value = cloneDeep(modelValue)\n  }\n}\n\nconst syncMenuState = (\n  newCheckedNodes: CascaderNode[],\n  reserveExpandingState = true\n) => {\n  const { checkStrictly } = config.value\n  const oldNodes = checkedNodes.value\n  const newNodes = newCheckedNodes.filter(\n    (node) => !!node && (checkStrictly || node.isLeaf)\n  )\n  const oldExpandingNode = store?.getSameNode(expandingNode.value!)\n  const newExpandingNode =\n    (reserveExpandingState && oldExpandingNode) || newNodes[0]\n\n  if (newExpandingNode) {\n    newExpandingNode.pathNodes.forEach((node) => expandNode(node, true))\n  } else {\n    expandingNode.value = undefined\n  }\n\n  oldNodes.forEach((node) => node.doCheck(false))\n  reactive(newNodes).forEach((node) => node.doCheck(true))\n  checkedNodes.value = newNodes\n  nextTick(scrollToExpandingNode)\n}\n\nconst scrollToExpandingNode = () => {\n  if (!isClient) return\n\n  menuList.value.forEach((menu) => {\n    const menuElement = menu?.$el\n    if (menuElement) {\n      const container = menuElement.querySelector(\n        `.${ns.namespace.value}-scrollbar__wrap`\n      )\n      const activeNode =\n        menuElement.querySelector(\n          `.${ns.b('node')}.${ns.is('active')}:last-child`\n        ) || menuElement.querySelector(`.${ns.b('node')}.in-active-path`)\n      scrollIntoView(container, activeNode)\n    }\n  })\n}\n\nconst handleKeyDown = (e: KeyboardEvent) => {\n  const target = e.target as HTMLElement\n  const { code } = e\n\n  switch (code) {\n    case EVENT_CODE.up:\n    case EVENT_CODE.down: {\n      e.preventDefault()\n      const distance = code === EVENT_CODE.up ? -1 : 1\n      focusNode(\n        getSibling(\n          target,\n          distance,\n          `.${ns.b('node')}[tabindex=\"-1\"]`\n        ) as HTMLElement\n      )\n      break\n    }\n    case EVENT_CODE.left: {\n      e.preventDefault()\n      const preMenu = menuList.value[getMenuIndex(target) - 1]\n      const expandedNode = preMenu?.$el.querySelector(\n        `.${ns.b('node')}[aria-expanded=\"true\"]`\n      )\n      focusNode(expandedNode)\n      break\n    }\n    case EVENT_CODE.right: {\n      e.preventDefault()\n      const nextMenu = menuList.value[getMenuIndex(target) + 1]\n      const firstNode = nextMenu?.$el.querySelector(\n        `.${ns.b('node')}[tabindex=\"-1\"]`\n      )\n      focusNode(firstNode)\n      break\n    }\n    case EVENT_CODE.enter:\n    case EVENT_CODE.numpadEnter:\n      checkNode(target)\n      break\n  }\n}\n\nprovide(\n  CASCADER_PANEL_INJECTION_KEY,\n  reactive({\n    config,\n    expandingNode,\n    checkedNodes,\n    isHoverMenu,\n    initialLoaded,\n    renderLabelFn,\n    lazyLoad,\n    expandNode,\n    handleCheckChange,\n  })\n)\n\nwatch(\n  [config, () => props.options],\n  (newVal, oldVal) => {\n    if (isEqual(newVal, oldVal)) return\n    initStore()\n  },\n  {\n    deep: true,\n    immediate: true,\n  }\n)\n\nwatch(\n  () => props.modelValue,\n  () => {\n    manualChecked = false\n    syncCheckedValue()\n  },\n  {\n    deep: true,\n  }\n)\n\nwatch(\n  () => checkedValue.value,\n  (val) => {\n    if (!isEqual(val, props.modelValue)) {\n      emit(UPDATE_MODEL_EVENT, val)\n      emit(CHANGE_EVENT, val)\n    }\n  }\n)\n\nonBeforeUpdate(() => (menuList.value = []))\n\nonMounted(() => !isEmpty(props.modelValue) && syncCheckedValue())\n\ndefineExpose({\n  menuList,\n  menus,\n  checkedNodes,\n  handleKeyDown,\n  handleCheckChange,\n  getFlattedNodes,\n  /**\n   * @description get an array of currently selected node,(leafOnly) whether only return the leaf checked nodes, default is `false`\n   */\n  getCheckedNodes,\n  /**\n   * @description clear checked nodes\n   */\n  clearCheckedNodes,\n  calculateCheckedValue,\n  scrollToExpandingNode,\n})\n</script>\n"], "names": ["_openBlock", "_createElementBlock", "_normalizeClass", "_unref", "_Fragment", "_renderList", "_createBlock", "_renderSlot"], "mappings": ";;;;;;;;;;;;;;;;;;mCAmEc,CAAA;AAAA,EACZ,IAAM,EAAA,iBAAA;AAAA,EACN,YAAc,EAAA,KAAA;AAChB,CAAA,CAAA,CAAA;;;;;;;AAMA,IAAA,IAAI,aAAgB,GAAA,KAAA,CAAA;AAEpB,IAAM,MAAA,EAAA,GAAK,aAAa,UAAU,CAAA,CAAA;AAClC,IAAM,MAAA,MAAA,GAAS,kBAAkB,KAAK,CAAA,CAAA;AACtC,IAAA,MAAM,QAAQ,QAAS,EAAA,CAAA;AAEvB,IAAI,IAAA,KAAA,CAAA;AACJ,IAAM,MAAA,aAAA,GAAgB,IAAI,IAAI,CAAA,CAAA;AAC9B,IAAM,MAAA,QAAA,GAAW,GAA4B,CAAA,EAAE,CAAA,CAAA;AAC/C,IAAA,MAAM,eAAe,GAAmB,EAAA,CAAA;AACxC,IAAM,MAAA,KAAA,GAAQ,GAAsB,CAAA,EAAE,CAAA,CAAA;AACtC,IAAA,MAAM,gBAAgB,GAAkB,EAAA,CAAA;AACxC,IAAM,MAAA,YAAA,GAAe,GAAoB,CAAA,EAAE,CAAA,CAAA;AAE3C,IAAA,MAAM,cAAc,QAAS,CAAA,MAAM,MAAO,CAAA,KAAA,CAAM,kBAAkB,OAAO,CAAA,CAAA;AACzE,IAAA,MAAM,gBAAgB,QAAS,CAAA,MAAM,KAAM,CAAA,WAAA,IAAe,MAAM,OAAO,CAAA,CAAA;AAEvE,IAAA,MAAM,YAAY,MAAM;AACtB,MAAM,MAAA,EAAE,SAAY,GAAA,KAAA,CAAA;AACpB,MAAA,MAAM,MAAM,MAAO,CAAA,KAAA,CAAA;AAEnB,MAAgB,aAAA,GAAA,KAAA,CAAA;AAChB,MAAQ,KAAA,GAAA,IAAI,KAAM,CAAA,OAAA,EAAS,GAAG,CAAA,CAAA;AAC9B,MAAA,KAAA,CAAM,KAAQ,GAAA,CAAC,KAAM,CAAA,QAAA,EAAU,CAAA,CAAA;AAE/B,MAAA,IAAI,GAAI,CAAA,IAAA,IAAQ,OAAQ,CAAA,KAAA,CAAM,OAAO,CAAG,EAAA;AACtC,QAAA,aAAA,CAAc,KAAQ,GAAA,KAAA,CAAA;AACtB,QAAS,QAAA,CAAA,KAAA,CAAA,EAAW,CAAC,IAAS,KAAA;AAC5B,UAAA,IAAI,IAAM,EAAA;AACR,YAAQ,KAAA,GAAA,IAAI,KAAM,CAAA,IAAA,EAAM,GAAG,CAAA,CAAA;AAC3B,YAAA,KAAA,CAAM,KAAQ,GAAA,CAAC,KAAM,CAAA,QAAA,EAAU,CAAA,CAAA;AAAA,WACjC;AACA,UAAA,aAAA,CAAc,KAAQ,GAAA,IAAA,CAAA;AACtB,UAAA,gBAAA,CAAiB,OAAO,IAAI,CAAA,CAAA;AAAA,SAC7B,CAAA,CAAA;AAAA,OACI,MAAA;AACL,QAAA,gBAAA,CAAiB,OAAO,IAAI,CAAA,CAAA;AAAA,OAC9B;AAAA,KACF,CAAA;AAEA,IAAM,MAAA,QAAA,GAA+C,CAAC,IAAA,EAAM,EAAO,KAAA;AACjE,MAAA,MAAM,MAAM,MAAO,CAAA,KAAA,CAAA;AACnB,MAAA,IAAA,GAAQ,QAAQ,IAAI,IAAA,CAAK,EAAI,EAAA,GAAA,EAAK,QAAW,IAAI,CAAA,CAAA;AACjD,MAAA,IAAA,CAAK,OAAU,GAAA,IAAA,CAAA;AAEf,MAAM,MAAA,OAAA,GAAU,CAAC,QAAgC,KAAA;AAC/C,QAAA,MAAM,KAAQ,GAAA,IAAA,CAAA;AACd,QAAM,MAAA,MAAA,GAAS,KAAM,CAAA,IAAA,GAAO,IAAO,GAAA,KAAA,CAAA;AACnC,QAAY,QAAA,KAAA,KAAA,IAAmB,IAAA,GAAA,KAAA,CAAA,GAAA,KAAwB,CAAA,WAAA,CAAA,QAAA,EAAA,MAAA,CAAA,CAAA,CAAA;AACvD,QAAA,KAAA,CAAM,OAAU,GAAA,KAAA,CAAA;AAChB,QAAA,KAAA,CAAM,MAAS,GAAA,IAAA,CAAA;AACf,QAAM,KAAA,CAAA,YAAA,GAAe,KAAM,CAAA,YAAA,IAAgB,EAAC,CAAA;AAC5C,QAAA,QAAA,WAAyB,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,QAAA,CAAA,CAAA,CAAA;AAAA,OAC3B,CAAA;AAEA,MAAI,GAAA,CAAA,QAAA,CAAS,MAAM,OAAO,CAAA,CAAA;AAAA,KAC5B,CAAA;AAEA,IAAM,MAAA,UAAA,GAAmD,CAAC,IAAA,EAAM,MAAW,KAAA;AACzE,MAAM,IAAA,EAAA,CAAA;AACN,MAAA,MAAM,EAAW,KAAA,EAAA,GAAA,IAAA,CAAM;AACvB,MAAI,MAAA,QAAA,GAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA;AAEJ,MAAA,IAAI,gBAAa,CAAA;AACf,MAAmB,IAAA,IAAA,CAAA,MAAA,EAAA;AAAwB,QACtC,gBAAA,GAAA,IAAA,CAAA,SAAA,CAAA,KAAA,GAAA,CAAA,CAAA,CAAA;AACL,OAAmB,MAAA;AACnB,QAAS,mBAAU,IAAQ,CAAA;AAAA,QAC7B,QAAA,CAAA,IAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA;AAEA,OAAA;AACE,MAAA,IAAA,CAAA,CAAA,EAAA,GAAA,aAAsB,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,OAAA,gBAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,gBAAA,CAAA,GAAA,CAAA,EAAA;AACtB,QAAA,aAAc,CAAA,KAAA,GAAA,IAAA,CAAA;AACd,QAAA,WAAW,GAAK,QAAA,CAAA;AAAuC,QACzD,CAAA,MAAA,IAAA,IAAA,CAAA,eAAA,EAAA,CAAA,IAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,IAAA,CAAA,UAAA,KAAA,EAAA,CAAA,CAAA;AAAA,OACF;AAEA,KAAA,CAAA;AAKE,IAAA,MAAA,iBAAQ,GAAwB,CAAA,IAAA,EAAA,OAAW,EAAA,SAAA,GAAA,IAAA,KAAA;AAC3C,MAAM,MAAA,EAAA,aAAuB,EAAA,QAAA,EAAA,GAAO,MAAA,CAAA,KAAA,CAAA;AACpC,MAAgB,MAAA,OAAA,GAAA,YAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AAEhB,MAAC,aAAY,GAAS,IAAA,CAAA;AACtB,MAAA,CAAA,aAAoB,OAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,OAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACpB,MAAsB,IAAA,CAAA,OAAA,CAAA,OAAA,CAAA,CAAA;AACtB,MAAA,qBAAc,EAAA,CAAA;AACd,MAAA,cAAc,QAAC,IAAA,CAAY,aAAC,IAAA;AAAsC,MACpE,CAAA,SAAA,IAAA,CAAA,QAAA,IAAA,CAAA,aAAA,IAAA,gBAAA,CAAA,IAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAA,MAAI,gBAAO,GAAA,CAAA,IAAA,KAAA;AACX,MAAA,IAAA,CAAA,IAAY;AACZ,QAAA,OAAA;AACA,MAAA,IAAA,GAAA,YAAmB;AAAI,MACzB,gBAAA,CAAA,IAAA,CAAA,CAAA;AAEA,MAAA,IAAM,IAAkB,UAAA,CAAA,IAAC,CAAsB,CAAA;AAE/C,KAAM,CAAA;AACJ,IAAO,MAAA,eAAA,GAAA,CAAgB,QAAQ,KAAG,KAAO,IAAG,IAAA,GAAQ,KAAM,CAAA,GAAA,KAAA,CAAA,eAAiB,CAAA,QAAA,CAAA,CAAA;AAAA,IAC7E,MAAA,eAAA,GAAA,CAAA,QAAA,KAAA;AAEA,MAAA,IAAM;AACJ,MAAA,OAAA,CAAA,EAAA,GAAa,eAAe,SAAS,CAAK,KAAA,IAAA,GAAQ,KAAK,CAAC,GAAA,EAAA,CAAA,MAAA,CAAA,CAAA,EAAA,OAAA,EAAA,KAAA,OAAA,KAAA,KAAA,CAAA,CAAA;AACxD,KAAsB,CAAA;AACtB,IAAA,MAAA,iBAA0B,GAAA,MAAA;AAC1B,MAAA,YAAA,CAAA,KAAsB,CAAA,OAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACtB,MAAK,qBAAA,EAAkB,CAAC;AAAA,MAC1B,KAAA,CAAA,KAAA,GAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;AAEA,MAAA,4BAA8B,CAAM;AAClC,MAAA,IAAA,CAAA,eAAQ,EAAA,EAAwB,CAAA,CAAA;AAChC,KAAA,CAAA;AACA,IAAM,MAAA,qBAA2B,GAAA,MAAC;AAElC,MAAM,MAAA,EAAA,aAA4B,EAAA,QAAA,EAAA,GAAA,MAAA,CAAA,KAAkB,CAAA;AACpD,MAAA,MAAM,WAAe,YAAK,CAAA,KAAS;AACnC,MAAA,MAAA,QAAqB,GAAA,eAAA,CAAA,CAAA,aAAA,CAAA,CAAA;AACrB,MAAA,MAAA,KAAA,GAAqB,mBAAoB,CAAA,QAAA,EAAA,QAAQ,CAAA,CAAA;AAAA,MACnD,MAAA,MAAA,GAAA,KAAA,CAAA,GAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,aAAA,CAAA,CAAA;AAEA,MAAA,YAAyB,CAAA,KAAA,GAAA,KAAU,CAAA;AACjC,MAAM,qBAAiB,QAAA,GAAA,MAAA,GAAA,MAAA,CAAA,CAAA,CAAA,CAAA;AACvB,KAAA,CAAA;AACA,IAAA,MAAA,gBAAkB,GAAA,CAAA,MAAA,GAAA,KAAA,EAAA,MAAA,GAAA,KAAA,KAAA;AAElB,MACE,oBACA,GAAA,KAAA,CAAA;AAGA,MAAA,MAAA,EAAA,IAAA,EAAA,QAAA,EAAA,aAAA,EAAA,GAAA,MAAA,CAAA,KAAA,CAAA;AAEF,MAAI,MAAA,QAAiB,GAAA,CAAA,aAAA,CAAA;AACnB,MAAA,IAAA,CAAA,aAAoC,CAAA,KAAA,IAAA,aAAA,IAAA,CAAA,MAAA,IAAA,OAAA,CAAA,UAAA,EAAA,YAAA,CAAA,KAAA,CAAA;AAAA,QAClC,OAAA;AAAwD,MAC1D,IAAA,IAAA,IAAA,CAAA,MAAA,EAAA;AACA,QAAM,MAAA,MAAA,SACH,CAAI,YAAS,SAAO,CAAA,UAAA,CAAA,CAAe;AAGtC,QAAA,WAAkB,GAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAA,KAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,KAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,KAAA,CAAA,CAAA,IAAA,IAAA,CAAA,IAAA,CAAA,MAAA,IAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA;AAChB,QAAM,IAAA,KAAA,CAAA,MAAQ,EAAU;AACtB,UAAA,KAAA,CAAA,OAAe,CAAA,CAAA,IAAA,KAAuB;AAAc,YACrD,QAAA,CAAA,IAAA,EAAA,MAAA,gBAAA,CAAA,KAAA,EAAA,MAAA,CAAA,CAAA,CAAA;AAAA,WACI,CAAA,CAAA;AACL,SAAA,MAAA;AAA6B,UAC/B,gBAAA,CAAA,IAAA,EAAA,MAAA,CAAA,CAAA;AAAA,SACK;AACL,OAAA,MAAA;AACA,QAAA,MAAM,MAAQ,GAAA,QAAA,GAAA,SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,UAAA,CAAA,CAAA;AAAA,QAAA,MACL,KAAA,GAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAA,KAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,KAAA,CAAA,cAAA,CAAA,GAAA,EAAA,QAAA,CAAA,CAAA,CAAA,CAAA;AAAA,QAAA,aACL,CAAO,KAAA,EAAA,MAAA,CAAA,CAAA;AAAiD,QAC1D,YAAA,CAAA,KAAA,GAAA,SAAA,CAAA,UAAA,CAAA,CAAA;AAAA,OACF;AACA,KAAA,CAAA;AACA,IAAa,MAAA,aAAA,GAAA,CAAA,eAA4B,EAAA,qBAAA,GAAA,IAAA,KAAA;AAAA,MAC3C,MAAA,EAAA,aAAA,EAAA,GAAA,MAAA,CAAA,KAAA,CAAA;AAAA,MACF,MAAA,QAAA,GAAA,YAAA,CAAA,KAAA,CAAA;AAEA,MAAA,MAAsB,QAAA,GAAA,eAEpB,CAAA,MAAA,CAAA,CAAA,IAAA,KAAA,CAAA,CAAA,IAAA,KAAA,aACG,IAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AACH,MAAM,MAAA,gBAAgB,GAAA,KAAW,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,KAAA,CAAA,WAAA,CAAA,aAAA,CAAA,KAAA,CAAA,CAAA;AACjC,MAAA,MAAM,gBAAwB,GAAA,qBAAA,IAAA,gBAAA,IAAA,QAAA,CAAA,CAAA,CAAA,CAAA;AAC9B,MAAA,IAAA,gBAAiC,EAAA;AAAA,QAC/B,gBAAY,CAAA,uBAA+B,KAAA,UAAA,CAAA,IAAA,EAAA,IAAA,CAAA,CAAA,CAAA;AAAA,OAC7C,MAAA;AACA,QAAA,aAAyB,CAAA,KAAA,GAAA,KAAA,CAAA,CAAA;AACzB,OAAA;AAGA,MAAA,QAAsB,CAAA,OAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACpB,MAAA,QAAA,CAAA,QAAA,CAAA,CAAiB,aAAkB,KAAA,YAAqB,CAAA,IAAA,CAAA,CAAA,CAAA;AAAW,MACrE,YAAO,CAAA,KAAA,GAAA,QAAA,CAAA;AACL,MAAA,QAAA,CAAA,qBAAsB,CAAA,CAAA;AAAA,KACxB,CAAA;AAEA,IAAA,MAAA,qBAAkB,GAAc,MAAA;AAChC,MAAS,IAAA,CAAA,QAAA;AACT,QAAA,OAAA;AACA,MAAA,QAAA,CAAS,KAAqB,CAAA,OAAA,CAAA,CAAA,IAAA,KAAA;AAAA,QAChC,MAAA,WAAA,GAAA,IAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,IAAA,CAAA,GAAA,CAAA;AAEA,QAAA;AACE,UAAI,MAAW,SAAA,GAAA,WAAA,CAAA,aAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,SAAA,CAAA,KAAA,CAAA,gBAAA,CAAA,CAAA,CAAA;AAEf,UAAS,MAAA,UAAc,GAAA,WAAU,CAAA,aAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,EAAA,CAAA,QAAA,CAAA,CAAA,WAAA,CAAA,CAAA,IAAA,WAAA,CAAA,aAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,eAAA,CAAA,CAAA,CAAA;AAC/B,UAAA,wBAA0B,EAAA,UAAA,CAAA,CAAA;AAC1B,SAAA;AACE,OAAA,CAAA,CAAA;AAA8B,KAC5B,CAAA;AAAsB,IACxB,MAAA,aAAA,GAAA,CAAA,CAAA,KAAA;AACA,MAAA,MAAA,iBACE,CAAY;AAAA,MACV,MAAA,EAAA,MAAS,GAAA,CAAA,CAAA;AAA0B,MACrC,QAAA;AACF,QAAA,KAAA,UAAA,CAAA;AAAoC,QACtC,KAAA,UAAA,CAAA,IAAA,EAAA;AAAA,UACD,CAAA,CAAA,cAAA,EAAA,CAAA;AAAA,UACH,MAAA,QAAA,GAAA,IAAA,KAAA,UAAA,CAAA,EAAA,GAAA,CAAA,CAAA,GAAA,CAAA,CAAA;AAEA,UAAM,SAAA,CAAA,UAAsC,CAAA,MAAA,EAAA,QAAA,EAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA;AAC1C,UAAA;AACA,SAAM;AAEN,QAAA,KAAA,UAAc,CAAA,IAAA,EAAA;AAAA,0BACI,EAAA,CAAA;AAAA,UAChB,gBAAsB,QAAA,CAAA,KAAA,CAAA,YAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACpB,UAAA,MAAiB,YAAA,GAAA,OAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,OAAA,CAAA,GAAA,CAAA,aAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,sBAAA,CAAA,CAAA,CAAA;AACjB,UAAA,SAAiB,CAAA,YAAA,CAAA,CAAA;AACjB,UAAA,MAAA;AAAA,SACE;AAAA,QACE,KAAA,UAAA,CAAA,KAAA,EAAA;AAAA,UACA,CAAA,CAAA,cAAA,EAAA,CAAA;AAAA,UAAA,MACI,QAAK,GAAA,QAAO,CAAA,KAAA,CAAA,YAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AAAA,UAClB,MAAA,SAAA,GAAA,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,GAAA,CAAA,aAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,eAAA,CAAA,CAAA,CAAA;AAAA,UACF,SAAA,CAAA,SAAA,CAAA,CAAA;AACA,UAAA,MAAA;AAAA,SACF;AAAA,QACA,KAAK,WAAW,KAAM,CAAA;AACpB,QAAA,KAAiB,UAAA,CAAA,WAAA;AACjB,UAAA,gBAAgB,CAAS,CAAA;AACzB,UAAM,MAAA;AAA4B,OAAA;AAChB,KAClB,CAAA;AACA,IAAA,OAAA,CAAA,4BAAsB,EAAA,QAAA,CAAA;AACtB,MAAA,MAAA;AAAA,MACF,aAAA;AAAA,MACA;AACE,MAAA,WAAiB;AACjB,MAAA;AACA,MAAM,aAAA;AAA0B,MAAA,QAC1B;AAAY,MAClB,UAAA;AACA,MAAA,iBAAmB;AACnB,KAAA,CAAA,CAAA,CAAA;AAAA,IACF,KAAA,CAAA,CAAA,MAAA,EAAA,MAAA,KAAA,CAAA,OAAA,CAAA,EAAA,CAAA,MAAA,EAAA,MAAA,KAAA;AAAA,MAAA,WACgB,CAAA,MAAA,EAAA,MAAA,CAAA;AAAA,QAChB,OAAgB;AACd,MAAA,SAAA,EAAA,CAAA;AACA,KAAA,EAAA;AAAA,MACJ,IAAA,EAAA,IAAA;AAAA,MACF,SAAA,EAAA,IAAA;AAEA,KAAA,CAAA,CAAA;AAAA,IACE,KAAA,CAAA,MAAA,KAAA,CAAA,UAAA,EAAA,MAAA;AAAA,MACA,aAAS,GAAA,KAAA,CAAA;AAAA,MACP,gBAAA,EAAA,CAAA;AAAA,KACA,EAAA;AAAA,MACA,IAAA,EAAA,IAAA;AAAA,KACA,CAAA,CAAA;AAAA,IACA,KAAA,CAAA,MAAA,YAAA,CAAA,KAAA,EAAA,CAAA,GAAA,KAAA;AAAA,MACA,IAAA,CAAA,OAAA,CAAA,GAAA,EAAA,KAAA,CAAA,UAAA,CAAA,EAAA;AAAA,QACA,IAAA,CAAA,kBAAA,EAAA,GAAA,CAAA,CAAA;AAAA,QACA,IAAA,CAAA,YAAA,EAAA,GAAA,CAAA,CAAA;AAAA,OACA;AAAA,KAAA,CACF,CAAC;AAAA,IACH,cAAA,CAAA,MAAA,QAAA,CAAA,KAAA,GAAA,EAAA,CAAA,CAAA;AAEA,IAAA,SAAA,CAAA,MAAA,CAAA,OAAA,CAAA,KAAA,CAAA,UAAA,CAAA,IAAA,gBAAA,EAAA,CAAA,CAAA;AAAA,IAAA,MACG,CAAA;AAA2B,MAC5B;AACE,MAAI,KAAA;AACJ,MAAU,YAAA;AAAA,MACZ,aAAA;AAAA,MACA,iBAAA;AAAA,MAAA,eACQ;AAAA,MAAA,eACK;AAAA,MACb,iBAAA;AAAA,MACF,qBAAA;AAEA,MAAA,qBAAA;AAAA,KAAA,CACE;AAAY,IAAA,OACN,CAAA,IAAA,EAAA,MAAA,KAAA;AACJ,MAAgB,OAAAA,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAChB,QAAiB,KAAA,EAAAC,cAAA,CAAA,CAAAC,KAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,EAAAA,KAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,UAAA,EAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAAA,QACnB,SAAA,EAAA,aAAA;AAAA,OACA,EAAA;AAAA,SACQH,SAAA,CAAA,IAAA,CAAA,EAAAC,kBAAA,CAAAG,QAAA,EAAA,IAAA,EAAAC,UAAA,CAAA,KAAA,CAAA,KAAA,EAAA,CAAA,IAAA,EAAA,KAAA,KAAA;AAAA,UACR,OAAAL,SAAA,EAAA,EAAAM,WAAA,CAAA,cAAA,EAAA;AAAA,YACF,GAAA,EAAA,KAAA;AAEA,YAAA,OAAA,EAAA,IAAA;AAAA,YACQ,GAAa,EAAA,CAAA,IAAA,KAAA,QAAA,CAAA,KAAA,CAAA,KAAA,CAAA,GAAA,IAAA;AAAA,YACV,KAAA;AACP,YAAI,KAAC,EAAA,CAAA,GAAa,IAAA,CAAA;AAChB,WAAA,EAAA;AACA,YAAA,qBAAsB;AAAA,cACxBC,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,OAAA,CAAA;AAAA,aACF,CAAA;AAAA,YACF,CAAA,EAAA,CAAA;AAEA,WAAA,EAAA,IAAA,EAAe,CAAO,OAAA,EAAA,OAAiB,CAAA,CAAA,CAAA;AAEvC,SAAA,CAAA,EAAA;AAEA,OAAa,EAAA,EAAA,CAAA,CAAA;AAAA,KACX,CAAA;AAAA,GACA;AAAA,CACA,CAAA,CAAA;AACA,oBACA,gBAAA,WAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,WAAA,CAAA,CAAA,CAAA;;;;"}