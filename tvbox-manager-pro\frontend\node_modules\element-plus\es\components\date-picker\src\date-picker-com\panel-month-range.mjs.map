{"version": 3, "file": "panel-month-range.mjs", "sources": ["../../../../../../../packages/components/date-picker/src/date-picker-com/panel-month-range.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"[\n      ppNs.b(),\n      drpNs.b(),\n      {\n        'has-sidebar': Bo<PERSON>an($slots.sidebar) || hasShortcuts,\n      },\n    ]\"\n  >\n    <div :class=\"ppNs.e('body-wrapper')\">\n      <slot name=\"sidebar\" :class=\"ppNs.e('sidebar')\" />\n      <div v-if=\"hasShortcuts\" :class=\"ppNs.e('sidebar')\">\n        <button\n          v-for=\"(shortcut, key) in shortcuts\"\n          :key=\"key\"\n          type=\"button\"\n          :class=\"ppNs.e('shortcut')\"\n          @click=\"handleShortcutClick(shortcut)\"\n        >\n          {{ shortcut.text }}\n        </button>\n      </div>\n      <div :class=\"ppNs.e('body')\">\n        <div :class=\"[ppNs.e('content'), drpNs.e('content')]\" class=\"is-left\">\n          <div :class=\"drpNs.e('header')\">\n            <button\n              type=\"button\"\n              :class=\"ppNs.e('icon-btn')\"\n              class=\"d-arrow-left\"\n              @click=\"leftPrevYear\"\n            >\n              <slot name=\"prev-year\">\n                <el-icon><d-arrow-left /></el-icon>\n              </slot>\n            </button>\n            <button\n              v-if=\"unlinkPanels\"\n              type=\"button\"\n              :disabled=\"!enableYearArrow\"\n              :class=\"[\n                ppNs.e('icon-btn'),\n                { [ppNs.is('disabled')]: !enableYearArrow },\n              ]\"\n              class=\"d-arrow-right\"\n              @click=\"leftNextYear\"\n            >\n              <slot name=\"next-year\">\n                <el-icon><d-arrow-right /></el-icon>\n              </slot>\n            </button>\n            <div>{{ leftLabel }}</div>\n          </div>\n          <month-table\n            selection-mode=\"range\"\n            :date=\"leftDate\"\n            :min-date=\"minDate\"\n            :max-date=\"maxDate\"\n            :range-state=\"rangeState\"\n            :disabled-date=\"disabledDate\"\n            @changerange=\"handleChangeRange\"\n            @pick=\"handleRangePick\"\n            @select=\"onSelect\"\n          />\n        </div>\n        <div :class=\"[ppNs.e('content'), drpNs.e('content')]\" class=\"is-right\">\n          <div :class=\"drpNs.e('header')\">\n            <button\n              v-if=\"unlinkPanels\"\n              type=\"button\"\n              :disabled=\"!enableYearArrow\"\n              :class=\"[ppNs.e('icon-btn'), { 'is-disabled': !enableYearArrow }]\"\n              class=\"d-arrow-left\"\n              @click=\"rightPrevYear\"\n            >\n              <slot name=\"prev-year\">\n                <el-icon><d-arrow-left /></el-icon>\n              </slot>\n            </button>\n            <button\n              type=\"button\"\n              :class=\"ppNs.e('icon-btn')\"\n              class=\"d-arrow-right\"\n              @click=\"rightNextYear\"\n            >\n              <slot name=\"next-year\">\n                <el-icon><d-arrow-right /></el-icon>\n              </slot>\n            </button>\n            <div>{{ rightLabel }}</div>\n          </div>\n          <month-table\n            selection-mode=\"range\"\n            :date=\"rightDate\"\n            :min-date=\"minDate\"\n            :max-date=\"maxDate\"\n            :range-state=\"rangeState\"\n            :disabled-date=\"disabledDate\"\n            @changerange=\"handleChangeRange\"\n            @pick=\"handleRangePick\"\n            @select=\"onSelect\"\n          />\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, ref, toRef, unref, watch } from 'vue'\nimport dayjs from 'dayjs'\nimport ElIcon from '@element-plus/components/icon'\nimport { isArray } from '@element-plus/utils'\nimport { useLocale } from '@element-plus/hooks'\nimport { DArrowLeft, DArrowRight } from '@element-plus/icons-vue'\nimport { PICKER_BASE_INJECTION_KEY } from '@element-plus/components/time-picker'\nimport {\n  correctlyParseUserInput,\n  getDefaultValue,\n  isValidRange,\n} from '../utils'\nimport {\n  panelMonthRangeEmits,\n  panelMonthRangeProps,\n} from '../props/panel-month-range'\nimport { useMonthRangeHeader } from '../composables/use-month-range-header'\nimport { useRangePicker } from '../composables/use-range-picker'\nimport { ROOT_PICKER_IS_DEFAULT_FORMAT_INJECTION_KEY } from '../constants'\nimport MonthTable from './basic-month-table.vue'\n\nimport type { Dayjs } from 'dayjs'\n\ndefineOptions({\n  name: 'DatePickerMonthRange',\n})\n\nconst props = defineProps(panelMonthRangeProps)\nconst emit = defineEmits(panelMonthRangeEmits)\nconst unit = 'year'\n\nconst { lang } = useLocale()\nconst pickerBase = inject(PICKER_BASE_INJECTION_KEY) as any\nconst isDefaultFormat = inject(\n  ROOT_PICKER_IS_DEFAULT_FORMAT_INJECTION_KEY\n) as any\nconst { shortcuts, disabledDate } = pickerBase.props\nconst format = toRef(pickerBase.props, 'format')\nconst defaultValue = toRef(pickerBase.props, 'defaultValue')\nconst leftDate = ref(dayjs().locale(lang.value))\nconst rightDate = ref(dayjs().locale(lang.value).add(1, unit))\n\nconst {\n  minDate,\n  maxDate,\n  rangeState,\n  ppNs,\n  drpNs,\n\n  handleChangeRange,\n  handleRangeConfirm,\n  handleShortcutClick,\n  onSelect,\n  onReset,\n} = useRangePicker(props, {\n  defaultValue,\n  leftDate,\n  rightDate,\n  unit,\n  onParsedValueChanged,\n})\n\nconst hasShortcuts = computed(() => !!shortcuts.length)\n\nconst {\n  leftPrevYear,\n  rightNextYear,\n  leftNextYear,\n  rightPrevYear,\n  leftLabel,\n  rightLabel,\n  leftYear,\n  rightYear,\n} = useMonthRangeHeader({\n  unlinkPanels: toRef(props, 'unlinkPanels'),\n  leftDate,\n  rightDate,\n})\n\nconst enableYearArrow = computed(() => {\n  return props.unlinkPanels && rightYear.value > leftYear.value + 1\n})\n\ntype RangePickValue = {\n  minDate: Dayjs\n  maxDate: Dayjs\n}\n\nconst handleRangePick = (val: RangePickValue, close = true) => {\n  // const defaultTime = props.defaultTime || []\n  // const minDate_ = modifyWithTimeString(val.minDate, defaultTime[0])\n  // const maxDate_ = modifyWithTimeString(val.maxDate, defaultTime[1])\n  // todo\n  const minDate_ = val.minDate\n  const maxDate_ = val.maxDate\n  if (maxDate.value === maxDate_ && minDate.value === minDate_) {\n    return\n  }\n  emit('calendar-change', [minDate_.toDate(), maxDate_ && maxDate_.toDate()])\n  maxDate.value = maxDate_\n  minDate.value = minDate_\n\n  if (!close) return\n  handleRangeConfirm()\n}\n\nconst handleClear = () => {\n  leftDate.value = getDefaultValue(unref(defaultValue), {\n    lang: unref(lang),\n    unit: 'year',\n    unlinkPanels: props.unlinkPanels,\n  })[0]\n  rightDate.value = leftDate.value.add(1, 'year')\n  emit('pick', null)\n}\n\nconst formatToString = (value: Dayjs | Dayjs[]) => {\n  return isArray(value)\n    ? value.map((_) => _.format(format.value))\n    : value.format(format.value)\n}\n\nconst parseUserInput = (value: Dayjs | Dayjs[]) => {\n  return correctlyParseUserInput(\n    value,\n    format.value,\n    lang.value,\n    isDefaultFormat\n  )\n}\n\nfunction onParsedValueChanged(\n  minDate: Dayjs | undefined,\n  maxDate: Dayjs | undefined\n) {\n  if (props.unlinkPanels && maxDate) {\n    const minDateYear = minDate?.year() || 0\n    const maxDateYear = maxDate.year()\n    rightDate.value =\n      minDateYear === maxDateYear ? maxDate.add(1, unit) : maxDate\n  } else {\n    rightDate.value = leftDate.value.add(1, unit)\n  }\n}\n\nwatch(\n  () => props.visible,\n  (visible) => {\n    if (!visible && rangeState.value.selecting) {\n      onReset(props.parsedValue)\n      onSelect(false)\n    }\n  }\n)\n\nemit('set-picker-option', ['isValidValue', isValidRange])\nemit('set-picker-option', ['formatToString', formatToString])\nemit('set-picker-option', ['parseUserInput', parseUserInput])\nemit('set-picker-option', ['handleClear', handleClear])\n</script>\n"], "names": ["minDate", "maxDate", "_openBlock", "_createElementBlock", "_normalizeClass", "_unref"], "mappings": ";;;;;;;;;;;;;;;;mCAoIc,CAAA;AAAA,EACZ,IAAM,EAAA,sBAAA;AACR;;;;;;;AAMA,IAAM,MAAA,EAAE,IAAK,EAAA,GAAI,SAAU,EAAA,CAAA;AAC3B,IAAM,MAAA,UAAA,GAAa,OAAO,yBAAyB,CAAA,CAAA;AACnD,IAAA,MAAM,eAAkB,GAAA,MAAA,CAAA,2CAAA,CAAA,CAAA;AAAA,IACtB,MAAA,EAAA,SAAA,EAAA,YAAA,EAAA,GAAA,UAAA,CAAA,KAAA,CAAA;AAAA,IACF,MAAA,MAAA,GAAA,KAAA,CAAA,UAAA,CAAA,KAAA,EAAA,QAAA,CAAA,CAAA;AACA,IAAA,MAAM,YAAE,GAAwB,KAAA,CAAA,UAAe,CAAA,KAAA,EAAA,cAAA,CAAA,CAAA;AAC/C,IAAA,MAAM,QAAS,GAAA,GAAA,CAAM,KAAW,EAAA,CAAA,MAAA,CAAA,IAAe,CAAA,KAAA,CAAA,CAAA,CAAA;AAC/C,IAAA,MAAM,SAAe,GAAA,GAAA,CAAA,KAAM,EAAW,CAAA,MAAA,CAAA,IAAA,CAAA,KAAqB,CAAA,CAAA,GAAA,CAAA,CAAA,EAAA,IAAA,CAAA,CAAA,CAAA;AAC3D,IAAA,MAAM;AACN,MAAM,OAAA;AAEN,MAAM,OAAA;AAAA,MACJ,UAAA;AAAA,MACA,IAAA;AAAA,MACA,KAAA;AAAA,MACA,iBAAA;AAAA,MACA,kBAAA;AAAA,MAEA,mBAAA;AAAA,MACA,QAAA;AAAA,MACA,OAAA;AAAA,KACA,GAAA,cAAA,CAAA,KAAA,EAAA;AAAA,MACA,YAAA;AAAA,MACF;AAA0B,MACxB,SAAA;AAAA,MACA,IAAA;AAAA,MACA,oBAAA;AAAA,KACA,CAAA,CAAA;AAAA,IACA,MAAA,YAAA,GAAA,QAAA,CAAA,MAAA,CAAA,CAAA,SAAA,CAAA,MAAA,CAAA,CAAA;AAAA,IACF,MAAC;AAED,MAAA;AAEA,MAAM,aAAA;AAAA,MACJ,YAAA;AAAA,MACA,aAAA;AAAA,MACA,SAAA;AAAA,MACA,UAAA;AAAA,MACA,QAAA;AAAA,MACA,SAAA;AAAA,KACA,GAAA,mBAAA,CAAA;AAAA,MACA,YAAA,EAAA,KAAA,CAAA,KAAA,EAAA,cAAA,CAAA;AAAA,cACsB;AAAA,MACtB,SAAA;AAAyC,KACzC,CAAA,CAAA;AAAA,IACA,MAAA,eAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MACD,OAAA,KAAA,CAAA,YAAA,IAAA,SAAA,CAAA,KAAA,GAAA,QAAA,CAAA,KAAA,GAAA,CAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAA,MAAA,eAAa,GAAA,CAAA,GAAA,EAAA,KAA0B,GAAA,IAAA,KAAA;AAAyB,MACjE,MAAA,QAAA,GAAA,GAAA,CAAA,OAAA,CAAA;AAOD,MAAA,MAAwB,QAAA,GAAA,GAAA,CAAA,OAAsB,CAAA;AAK5C,MAAA,IAAA,aAAiB,KAAI,QAAA,IAAA,OAAA,CAAA,KAAA,KAAA,QAAA,EAAA;AACrB,QAAA;AACA,OAAA;AACE,MAAA,IAAA,CAAA,iBAAA,EAAA,CAAA,QAAA,CAAA,MAAA,EAAA,EAAA,QAAA,IAAA,QAAA,CAAA,MAAA,EAAA,CAAA,CAAA,CAAA;AAAA,MACF,OAAA,CAAA,KAAA,GAAA,QAAA,CAAA;AACA,MAAK,OAAA,CAAA,KAAA,GAAA,QAAmB,CAAC;AACzB,MAAA,IAAA,CAAA,KAAgB;AAChB,QAAA,OAAgB;AAEhB,MAAA,kBAAY,EAAA,CAAA;AACZ,KAAmB,CAAA;AAAA,IACrB,MAAA,WAAA,GAAA,MAAA;AAEA,MAAA,gCAA0B,CAAA,KAAA,CAAA,YAAA,CAAA,EAAA;AACxB,QAAA,IAAA,EAAA,KAAiB,CAAA,IAAA,CAAA;AAAqC,QACpD,IAAA,EAAM,MAAM;AAAI,QAChB,YAAM,EAAA,KAAA,CAAA,YAAA;AAAA,OAAA,CACN;AAAoB,MACtB,SAAI,CAAA,KAAA,GAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,EAAA,MAAA,CAAA,CAAA;AACJ,MAAA,IAAA,CAAA,MAAkB,EAAA,IAAA,CAAA,CAAA;AAClB,KAAA,CAAA;AAAiB,IACnB,MAAA,cAAA,GAAA,CAAA,KAAA,KAAA;AAEA,MAAM,OAAA,OAAA,CAAA,KAAA,CAAiB,GAA4B,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAA,CAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA;AACjD,KAAA,CAAA;AAE6B,IAC/B,MAAA,cAAA,GAAA,CAAA,KAAA,KAAA;AAEA,MAAM,OAAA,uBAA6C,CAAA,KAAA,EAAA,MAAA,CAAA,KAAA,EAAA,IAAA,CAAA,KAAA,EAAA,eAAA,CAAA,CAAA;AACjD,KAAO,CAAA;AAAA,IACL,SAAA,oBAAA,CAAA,QAAA,EAAA,QAAA,EAAA;AAAA,MAAA,IACO,KAAA,CAAA,YAAA,IAAA,QAAA,EAAA;AAAA,QACP,MAAK,WAAA,GAAA,CAAA,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,IAAA,EAAA,KAAA,CAAA,CAAA;AAAA,QACL,MAAA,WAAA,GAAA,QAAA,CAAA,IAAA,EAAA,CAAA;AAAA,QACF,SAAA,CAAA,KAAA,GAAA,WAAA,KAAA,WAAA,GAAA,QAAA,CAAA,GAAA,CAAA,CAAA,EAAA,IAAA,CAAA,GAAA,QAAA,CAAA;AAAA,OACF,MAAA;AAEA,QAAS,SAAA,CAAA,KAAA,GAAA,QACPA,UACAC,CACA,CAAA,EAAA,IAAA,CAAA,CAAA;AACA,OAAI;AACF,KAAM;AACN,IAAM,KAAA,CAAA,MAAA,KAAA,CAAA,iBAA2B,KAAA;AACjC,MAAA,IAAA,CAAA,OAAU,cACQ,CAAA,KAAA,CAAA,SAAA,EAAA;AAAqC,QAClD,OAAA,CAAA,KAAA,CAAA,WAAA,CAAA,CAAA;AACL,QAAA,QAAA,CAAA,KAAkB,CAAA,CAAA;AAA0B,OAC9C;AAAA,KACF,CAAA,CAAA;AAEA,IAAA,IAAA,CAAA,mBAAA,EAAA,CAAA,cAAA,EAAA,YAAA,CAAA,CAAA,CAAA;AAAA,IAAA,wBACc,EAAA,CAAA,gBAAA,EAAA,cAAA,CAAA,CAAA,CAAA;AAAA,IAAA,IACC,CAAA,mBAAA,EAAA,CAAA,gBAAA,EAAA,cAAA,CAAA,CAAA,CAAA;AACX,IAAA,IAAA,CAAA,mBAA2B,EAAA,CAAA,aAAiB,EAAA,WAAA,CAAA,CAAA,CAAA;AAC1C,IAAA,OAAA,CAAA,IAAA,EAAQ,MAAM,KAAW;AACzB,MAAA,OAAAC,SAAc,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,QAChB,KAAA,EAAAC,cAAA,CAAA;AAAA,UACFC,KAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA;AAAA,UACFA,KAAA,CAAA,KAAA,CAAA,CAAA,CAAA,EAAA;AAEA,UAA0B;AAC1B,YAA0B,aAAA,EAAA,OAAmB,CAAA,IAAA,CAAA,MAAA,CAAA,OAAA,CAAA,IAAAA,KAAe,CAAA,YAAA,CAAA;AAC5D,WAA0B;AAC1B,SAAK,CAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}