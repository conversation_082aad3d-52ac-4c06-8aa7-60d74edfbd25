#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库初始化脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from app.core.database import engine, Base, create_tables
from app.models import user, tvbox, system
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def init_database():
    """初始化数据库"""
    try:
        logger.info("开始初始化数据库...")
        
        # 确保数据目录存在
        data_dir = current_dir / "data"
        data_dir.mkdir(exist_ok=True)
        
        # 创建所有表
        logger.info("创建数据库表...")
        Base.metadata.create_all(bind=engine)
        
        logger.info("数据库初始化完成！")
        return True
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = init_database()
    if success:
        print("✅ 数据库初始化成功！")
        sys.exit(0)
    else:
        print("❌ 数据库初始化失败！")
        sys.exit(1)
