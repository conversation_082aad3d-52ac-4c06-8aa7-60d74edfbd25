#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
TVBox接口解密工具
支持各种加密方式的解密操作
"""

import json
import base64
import re
import traceback
import logging
import requests
import ipaddress
import time
import hashlib
from urllib.parse import urlparse, urljoin, parse_qs
from pathlib import Path
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
import binascii

class TVBoxDecryptor:
    """TVBox配置解密工具类，完全模拟TVBox客户端行为"""
    
    def __init__(self):
        """初始化解密器"""
        self.logger = logging.getLogger('tvbox_manager.decryptor')
        # 定义JS路径模式，匹配相对路径JS
        self.js_uri_pattern = re.compile(r'"(\.|\.\.)/(.?|.+?)\.js\?(.?|.+?)"')
        
    def decrypt(self, content, hint_type=None):
        """
        尝试使用多种方法解密TVBox配置或地址
        完全对标TVBox客户端Decoder类实现
        
        Args:
            content: 配置内容或地址
            hint_type: 提示的类型
            
        Returns:
            tuple: (解密后的内容, 使用的解密方法)
        """
        self.logger.debug(f"尝试解密内容: {content[:100]}...")
        
        # 处理空内容
        if not content or not content.strip():
            return content, "无内容"
        
        # 尝试进行URL编码解码
        if '%' in content:
            try:
                from urllib.parse import unquote
                decoded = unquote(content)
                if decoded != content:
                    return decoded, "URL解码"
            except Exception as e:
                self.logger.debug(f"URL解码失败: {str(e)}")
        
        # 尝试JSON解析
        if content.strip().startswith('{') and self._is_json(content):
            return content, "JSON格式"
            
        # 尝试Base64解码 (对应TVBox中的base64方法)
        if "**" in content:
            try:
                extracted = self._extract_base64(content)
                if extracted:
                    decoded = base64.b64decode(extracted).decode('utf-8')
                    return decoded, "Base64解码"
            except Exception as e:
                self.logger.debug(f"Base64解码失败: {str(e)}")
                
        # 尝试AES/CBC解密 (对应TVBox中的cbc方法)
        if content.startswith("2423"):
            try:
                decoded = self._decrypt_cbc(content)
                return decoded, "AES/CBC解密"
            except Exception as e:
                self.logger.debug(f"AES/CBC解密失败: {str(e)}")
        
        # 特殊情况: clan://协议处理
        if content.startswith('clan://'):
            try:
                # clan://localhost/文件路径 转换为 http://127.0.0.1:9978/file/文件路径
                clan_path = content.replace('clan://', '')
                parts = clan_path.split('/', 1)
                if len(parts) == 2:
                    host, path = parts
                    if host.lower() == 'localhost':
                        decoded = f"http://127.0.0.1:9978/file/{path}"
                        return decoded, "clan协议转换"
            except Exception as e:
                self.logger.debug(f"clan协议转换失败: {str(e)}")
                
        # 特殊情况：字符串反转
        if content.strip() and not content.startswith(('http://', 'https://')):
            reversed_content = content[::-1]
            # 如果反转后是一个看起来像URL的字符串
            if reversed_content.startswith(('http://', 'https://')) or \
               'www.' in reversed_content:
                return reversed_content, "字符串反转"
                
        # 特殊情况：直接替换内容
        replacements = {
            'fongmi': 'https://raw.githubusercontent.com/FongMi/TV/release/json/config.json',
            'fm': 'https://raw.githubusercontent.com/FongMi/TV/release/json/config.json',
            'ali': 'http://饭太硬.top/tv',
            'xo': 'https://jihulab.com/clear1/tvbox/-/raw/main/0.json',
            'xiaoguazi': 'https://dxawi.github.io/0/0.json',
            # 添加更多替换规则
        }
        
        for key, value in replacements.items():
            if content.strip().lower() == key:
                return value, f"关键词替换[{key}]"
                
        # 默认返回原内容
        return content, "无需解密"
    
    def decrypt_config_url(self, url, config_type=None):
        """
        专门用于解密TVBox配置URL的方法
        完全模拟TVBox客户端行为，对任意URL进行解析获取配置
        对标VodConfig和LiveConfig的loadConfig方法
        
        Args:
            url: 配置URL
            config_type: 配置类型提示
            
        Returns:
            tuple: (配置内容, 解密方法)
        """
        self.logger.debug(f"尝试解析配置URL: {url}")
        
        try:
            # 1. 标准化URL
            normalized_url = self._normalize_url(url)
            
            # 2. 获取JSON内容 (对应TVBox的Decoder.getJson方法)
            content, final_url = self._get_json(normalized_url)
            
            # 3. 验证并处理内容 (对应TVBox的verify方法)
            verified_content = self._verify_content(final_url, content)
            
            # 4. 检查是否是有效的JSON (对应VodConfig的checkJson方法)
            if self._is_json(verified_content):
                json_data = json.loads(verified_content)
                
                # 5. 检查是否包含消息
                if isinstance(json_data, dict) and "msg" in json_data:
                    return json_data["msg"], "错误消息"
                    
                # 6. 检查是否是仓库配置
                if isinstance(json_data, dict) and "urls" in json_data and isinstance(json_data["urls"], list):
                    # 解析仓库配置，获取第一个URL
                    try:
                        first_url = json_data["urls"][0].get("url", "")
                        if first_url:
                            return self.decrypt_config_url(first_url, config_type)
                    except Exception as e:
                        self.logger.error(f"解析仓库配置失败: {str(e)}")
                
                # 7. 返回处理后的JSON配置
                return json.dumps(json_data, ensure_ascii=False, indent=2), "配置解析"
                
            return verified_content, "直接内容"
            
        except Exception as e:
            self.logger.error(f"解析配置URL失败: {str(e)}")
            self.logger.debug(traceback.format_exc())
            
            # 8. 如果失败且内容非空，尝试常规解密
            if url:
                return self.decrypt(url, config_type)
            
            return url, "解析失败"

    def _get_json(self, url, tag="vod"):
        """
        获取JSON内容，对标TVBox的Decoder.getJson方法
        
        Args:
            url: 请求URL
            tag: 请求标签
            
        Returns:
            tuple: (内容, 最终URL)
        """
        # 设置TVBox客户端请求头
        headers = {
            'User-Agent': 'okhttp/3.12.11',
            'Connection': 'keep-alive',
            'Accept': '*/*',
            'Accept-Encoding': 'gzip, deflate'
        }
        
        # 不自动处理重定向，与TVBox一致
        response = requests.get(url, headers=headers, timeout=10, allow_redirects=False)
        
        # 处理重定向
        if response.status_code in (301, 302, 303, 307, 308):
            redirect_url = response.headers.get('Location')
            if redirect_url:
                self.logger.debug(f"请求被重定向到: {redirect_url}")
                # 如果重定向URL不是绝对URL，转换为绝对URL
                if not redirect_url.startswith(('http://', 'https://')):
                    redirect_url = urljoin(url, redirect_url)
                
                # 请求重定向URL
                return self._get_json(redirect_url, tag)
        
        # 获取内容
        if response.ok:
            # 使用相同的URL参数数量检查，与TVBox一致
            try:
                parsed = urlparse(url)
                request_url = response.request.url
                parsed_request = urlparse(request_url)
                
                query_size = len(parse_qs(parsed.query))
                request_query_size = len(parse_qs(parsed_request.query))
                
                if request_query_size == query_size:
                    url = request_url
            except Exception as e:
                self.logger.debug(f"URL参数检查失败: {str(e)}")
            
            return response.text, url
        
        # 请求失败，抛出异常
        response.raise_for_status()
    
    def _verify_content(self, url, data):
        """
        验证内容，对标TVBox的verify方法
        
        Args:
            url: 请求URL
            data: 响应内容
            
        Returns:
            str: 处理后的内容
        """
        if not data:
            raise Exception("响应内容为空")
            
        # 如果已经是JSON对象，直接修正并返回
        if self._is_json(data):
            return self._fix_urls(url, data)
        
        # 检查是否包含Base64标记
        if "**" in data:
            data = self._extract_and_decode_base64(data)
            
        # 检查是否需要AES/CBC解密
        if data.startswith("2423"):
            data = self._decrypt_cbc(data)
            
        # 修正URL并返回
        return self._fix_urls(url, data)
    
    def _is_json(self, text):
        """
        检查文本是否是有效的JSON
        
        Args:
            text: 要检查的文本
            
        Returns:
            bool: 是否是JSON
        """
        try:
            if not isinstance(text, str):
                return False
            json.loads(text)
            return True
        except:
            return False
    
    def _extract_base64(self, data):
        """
        提取Base64编码部分，对标TVBox的extract方法
        
        Args:
            data: 包含Base64的数据
            
        Returns:
            str: 提取的Base64部分
        """
        # 匹配Base64特征
        matcher = re.compile(r"[A-Za-z0-9]{8}\*\*").search(data)
        if matcher:
            return data[data.index(matcher.group()) + 10:]
        return ""
    
    def _extract_and_decode_base64(self, data):
        """
        提取并解码Base64内容，对标TVBox的base64方法
        
        Args:
            data: 包含Base64的数据
            
        Returns:
            str: 解码后的内容
        """
        extracted = self._extract_base64(data)
        if not extracted:
            return data
        return base64.b64decode(extracted).decode('utf-8')
    
    def _decrypt_cbc(self, data):
        """
        AES/CBC解密，对标TVBox的cbc方法
        
        Args:
            data: 要解密的数据
            
        Returns:
            str: 解密后的内容
        """
        # 将16进制字符串转换为字节
        hex_bytes = binascii.unhexlify(data)
        decode = hex_bytes.decode('latin1').lower()
        
        # 提取密钥和IV
        key = self._pad_end(decode[decode.index("$#") + 2:decode.index("#$")])
        iv = self._pad_end(decode[-13:])
        
        # 准备解密
        cipher = AES.new(key.encode(), AES.MODE_CBC, iv.encode())
        
        # 提取加密数据
        encrypted_data = data[data.index("2324") + 4:len(data) - 26]
        encrypted_bytes = binascii.unhexlify(encrypted_data)
        
        # 解密
        decrypted_bytes = cipher.decrypt(encrypted_bytes)
        
        # 去除填充并返回
        try:
            return unpad(decrypted_bytes, 16).decode('utf-8')
        except:
            # 如果去除填充失败，直接返回解密结果
            return decrypted_bytes.decode('utf-8', errors='ignore')
    
    def _pad_end(self, key):
        """
        填充字符串到16字节，对标TVBox的padEnd方法
        
        Args:
            key: 要填充的字符串
            
        Returns:
            str: 填充后的字符串
        """
        return key + "0000000000000000"[len(key):]
    
    def _fix_urls(self, url, data):
        """
        修正相对URL，对标TVBox的fix方法
        
        Args:
            url: 基础URL
            data: 要修正的内容
            
        Returns:
            str: 修正后的内容
        """
        # 替换JS URI
        for match in self.js_uri_pattern.finditer(data):
            data = self._replace_js_uri(url, data, match.group())
        
        # 替换相对路径
        base_url = self._get_base_url(url)
        if "../" in data:
            data = data.replace("../", urljoin(base_url, "../"))
        if "./" in data:
            data = data.replace("./", urljoin(base_url, "./"))
        if "__JS1__" in data:
            data = data.replace("__JS1__", "./")
        if "__JS2__" in data:
            data = data.replace("__JS2__", "../")
            
        return data
    
    def _replace_js_uri(self, url, data, ext):
        """
        替换JS URI，对标TVBox的replace方法
        
        Args:
            url: 基础URL
            data: 要修正的内容
            ext: 要替换的JS URI
            
        Returns:
            str: 修正后的内容
        """
        base_url = self._get_base_url(url)
        t = ext.replace("\"./", "\"" + urljoin(base_url, "./"))
        t = t.replace("\"../", "\"" + urljoin(base_url, "../"))
        t = t.replace("./", "__JS1__").replace("../", "__JS2__")
        return data.replace(ext, t)
    
    def _get_base_url(self, url):
        """
        获取基础URL
        
        Args:
            url: 完整URL
            
        Returns:
            str: 基础URL
        """
        parsed = urlparse(url)
        path_parts = parsed.path.split("/")
        if len(path_parts) > 1:
            path = "/".join(path_parts[:-1]) + "/"
        else:
            path = "/"
        return f"{parsed.scheme}://{parsed.netloc}{path}"
    
    def _normalize_url(self, url):
        """
        标准化URL，处理中文域名、协议前缀等
        
        Args:
            url: 原始URL
            
        Returns:
            str: 标准化后的URL
        """
        # 添加协议前缀
        if not url.startswith(('http://', 'https://')):
            if url.startswith('//'):
                url = 'https:' + url
            else:
                url = 'https://' + url
        
        # 标准化域名中的中文
        try:
            parsed = urlparse(url)
            if any(ord(c) > 127 for c in parsed.netloc):
                # 使用Punycode转换中文域名
                ascii_domain = parsed.netloc.encode('idna').decode('ascii')
                url = url.replace(parsed.netloc, ascii_domain)
        except Exception as e:
            self.logger.debug(f"标准化域名失败: {str(e)}")
            
        return url
    
    def _validate_tvbox_config(self, content):
        """
        验证配置内容是否是有效的TVBox配置
        
        Args:
            content: 配置内容
            
        Returns:
            bool: 是否是有效的TVBox配置
        """
        try:
            # 尝试解析JSON
            if not content or not isinstance(content, str):
                return False
                
            json_data = json.loads(content)
            
            # 必须包含sites字段
            if 'sites' not in json_data and ('video' not in json_data or 'sites' not in json_data.get('video', {})):
                return False
                
            # 检查sites字段是否是数组
            sites = json_data.get('sites', json_data.get('video', {}).get('sites', []))
            if not isinstance(sites, list) or len(sites) == 0:
                return False
                
            # 检查至少一个站点是否包含基本的必要字段
            valid_sites = 0
            for site in sites:
                if isinstance(site, dict) and 'key' in site and 'name' in site and 'api' in site:
                    valid_sites += 1
                    
            if valid_sites == 0:
                return False
                
            # 检查可选但常见的字段格式是否正确
            if 'lives' in json_data and not isinstance(json_data['lives'], list):
                return False
                
            if 'parses' in json_data and not isinstance(json_data['parses'], list):
                return False
                
            # 通过所有检查，认为是有效的TVBox配置
            self.logger.info(f"验证通过: 找到{valid_sites}个有效站点")
            return True
            
        except Exception as e:
            self.logger.debug(f"配置验证失败: {str(e)}")
            return False
            
    def parse_tvbox_config_content(self, content):
        """
        解析TVBox配置内容，提取关键信息
        
        Args:
            content: JSON配置内容
            
        Returns:
            dict: 配置信息对象
        """
        try:
            # 解析JSON
            if isinstance(content, str):
                json_data = json.loads(content)
            else:
                json_data = content
                
            # 提取基本信息
            result = {
                'spider': json_data.get('spider', ''),
                'wallpaper': json_data.get('wallpaper', ''),
                'logo': json_data.get('logo', ''),
                'sites': [],
                'lives': [],
                'parses': []
            }
            
            # 提取站点信息
            sites = json_data.get('sites', json_data.get('video', {}).get('sites', []))
            for site in sites:
                if isinstance(site, dict):
                    site_info = {
                        'key': site.get('key', ''),
                        'name': site.get('name', ''),
                        'type': site.get('type', 0),
                        'api': site.get('api', ''),
                        'searchable': site.get('searchable', 1),
                        'quickSearch': site.get('quickSearch', 1),
                        'filterable': site.get('filterable', 1),
                        'hide': site.get('hide', 0),
                        'jar': site.get('jar', '')
                    }
                    result['sites'].append(site_info)
            
            # 提取解析器信息
            parses = json_data.get('parses', [])
            for parse in parses:
                if isinstance(parse, dict):
                    parse_info = {
                        'name': parse.get('name', ''),
                        'url': parse.get('url', ''),
                        'type': parse.get('type', 0)
                    }
                    result['parses'].append(parse_info)
            
            # 提取直播分组信息
            lives = json_data.get('lives', [])
            if lives and len(lives) > 0:
                # 检查第一个元素是否包含特殊处理字段
                if 'proxy' in str(lives[0]):
                    result['lives'].append({
                        'group': '默认分组',
                        'channels': [],
                        'proxy': True
                    })
                else:
                    # 常规直播源处理
                    for live in lives:
                        if isinstance(live, dict) and 'group' in live:
                            live_info = {
                                'group': live.get('group', ''),
                                'channels': []
                            }
                            
                            # 处理频道
                            channels = live.get('channels', [])
                            for channel in channels:
                                if isinstance(channel, dict):
                                    channel_info = {
                                        'name': channel.get('name', ''),
                                        'urls': channel.get('urls', [])
                                    }
                                    live_info['channels'].append(channel_info)
                                    
                            result['lives'].append(live_info)
            
            return result
            
        except Exception as e:
            self.logger.error(f"解析配置内容失败: {str(e)}")
            self.logger.debug(traceback.format_exc())
            return {
                'spider': '',
                'wallpaper': '',
                'logo': '',
                'sites': [],
                'lives': [],
                'parses': []
            }
    
    def remove_comments_preserve_url(self, content):
        """
        移除JSON注释但保留URL结构
        
        Args:
            content: 原始JSON字符串
            
        Returns:
            str: 处理后的JSON字符串
        """
        # 分析内容的行
        lines = content.splitlines()
        cleaned_lines = []
        
        for line in lines:
            # 过滤掉整行注释 (以//开头的行，前面可能有空白)
            if re.match(r'^\s*//.*$', line):
                continue
            
            # 过滤掉行内注释，但保护URL中的 '//'
            # 先标记所有URL中的 '//' 为特殊标记
            line = re.sub(r'(https?:)//', r'\1URLSLASHSLASH', line)
            # 移除注释
            line = re.sub(r'//.*$', '', line)
            # 恢复URL
            line = re.sub(r'URLSLASHSLASH', '//', line)
            
            cleaned_lines.append(line)
        
        # 过滤掉多行注释，但保留格式
        content = '\n'.join(cleaned_lines)
        content = re.sub(r'/\*.*?\*/', '', content, flags=re.DOTALL)
        
        return content

    def show_error_context(self, content, error_position, context_lines=5):
        """
        显示JSON错误周围的上下文
        
        Args:
            content: JSON字符串内容
            error_position: 错误位置
            context_lines: 上下文行数
            
        Returns:
            str: 错误上下文字符串
        """
        lines = content.splitlines()
        
        # 找出错误位置所在的行
        line_count = 0
        char_count = 0
        error_line = 0
        for i, line in enumerate(lines):
            char_count += len(line) + 1  # +1 for newline
            if char_count >= error_position:
                error_line = i
                break
            line_count += 1
        
        # 计算要显示的行范围
        start_line = max(0, error_line - context_lines)
        end_line = min(len(lines), error_line + context_lines + 1)
        
        # 构建显示内容
        error_context = []
        for i in range(start_line, end_line):
            prefix = ">> " if i == error_line else "   "
            error_context.append(f"{prefix}{i+1:4d}: {lines[i]}")
        
        return "\n".join(error_context) 