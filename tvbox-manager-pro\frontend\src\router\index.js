import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({ 
  showSpinner: false,
  minimum: 0.2,
  speed: 500
})

// 路由配置
const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: {
      title: '登录',
      requiresAuth: false,
      hideInMenu: true
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/auth/Register.vue'),
    meta: {
      title: '注册',
      requiresAuth: false,
      hideInMenu: true
    }
  },
  {
    path: '/',
    component: () => import('@/layout/index.vue'),
    redirect: '/dashboard',
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: {
          title: '控制台',
          icon: 'Dashboard',
          affix: true
        }
      },
      {
        path: 'interfaces',
        name: 'Interfaces',
        component: () => import('@/views/interfaces/index.vue'),
        meta: {
          title: '接口管理',
          icon: 'Link'
        }
      },
      {
        path: 'interfaces/create',
        name: 'InterfaceCreate',
        component: () => import('@/views/interfaces/create.vue'),
        meta: {
          title: '添加接口',
          hideInMenu: true,
          activeMenu: '/interfaces'
        }
      },
      {
        path: 'interfaces/:id',
        name: 'InterfaceDetail',
        component: () => import('@/views/interfaces/detail.vue'),
        meta: {
          title: '接口详情',
          hideInMenu: true,
          activeMenu: '/interfaces'
        }
      },
      {
        path: 'interfaces/:id/edit',
        name: 'InterfaceEdit',
        component: () => import('@/views/interfaces/edit.vue'),
        meta: {
          title: '编辑接口',
          hideInMenu: true,
          activeMenu: '/interfaces'
        }
      },
      {
        path: 'configs',
        name: 'Configs',
        component: () => import('@/views/configs/index.vue'),
        meta: {
          title: '配置管理',
          icon: 'Document'
        }
      },
      {
        path: 'subscriptions',
        name: 'Subscriptions',
        component: () => import('@/views/subscriptions/index.vue'),
        meta: {
          title: '我的订阅',
          icon: 'Star'
        }
      },
      {
        path: 'users',
        name: 'Users',
        component: () => import('@/views/users/index.vue'),
        meta: {
          title: '用户管理',
          icon: 'User',
          roles: ['admin']
        }
      },
      {
        path: 'decrypt',
        name: 'Decrypt',
        component: () => import('@/views/decrypt/index.vue'),
        meta: {
          title: '解密工具',
          icon: 'Key'
        }
      },
      {
        path: 'settings',
        name: 'Settings',
        component: () => import('@/views/settings/index.vue'),
        meta: {
          title: '系统设置',
          icon: 'Setting',
          roles: ['admin']
        }
      },
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('@/views/profile/index.vue'),
        meta: {
          title: '个人中心',
          hideInMenu: true
        }
      },
      {
        path: 'test/api',
        name: 'ApiTest',
        component: () => import('@/views/test/ApiTest.vue'),
        meta: {
          title: 'API测试',
          hideInMenu: true
        }
      }
    ]
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: {
      title: '页面不存在',
      hideInMenu: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 开始进度条
  NProgress.start()
  
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - TVBox Manager Pro` : 'TVBox Manager Pro'
  
  const userStore = useUserStore()
  
  // 检查是否需要认证
  if (to.meta.requiresAuth !== false) {
    // 检查是否已登录
    if (!userStore.isLoggedIn) {
      // 尝试从本地存储恢复用户信息
      await userStore.checkAuth()
      
      if (!userStore.isLoggedIn) {
        ElMessage.warning('请先登录')
        next({
          path: '/login',
          query: { redirect: to.fullPath }
        })
        return
      }
    }
    
    // 检查角色权限
    if (to.meta.roles && to.meta.roles.length > 0) {
      const hasPermission = to.meta.roles.some(role => 
        userStore.user.roles.includes(role)
      )
      
      if (!hasPermission) {
        ElMessage.error('权限不足')
        next('/404')
        return
      }
    }
  } else {
    // 如果已登录，访问登录页面时重定向到首页
    if (userStore.isLoggedIn && (to.path === '/login' || to.path === '/register')) {
      next('/')
      return
    }
  }
  
  next()
})

router.afterEach(() => {
  // 结束进度条
  NProgress.done()
})

export default router
