//资源全部免费，仅供测试交流，请勿内置传播与转卖；关注微信公众号：奇奇乐分享，发送“本地包”获取教程与更多资源！

{
    "spider": "./spider.jar",
    "wallpaper": "https://深色壁纸.xxooo.cf/",
    "sites": [
        {
            "key": "Tg|豆瓣",
            "name": "🔍Tg豆瓣┃本接口免费-🈲贩卖",
            "type": 3,
            "api": "csp_TgYunDouBanPan",
            "searchable": 1,
            "changeable": 0,
            "ext": {
                "siteUrl": "http://127.0.0.1:9999",
                "count": 4,
                "channelUsername": "bdwpzhpd,bdwpzhpd,alyp_TV,ucquark,wp123zy,oneonefivewpfx,tyypzhpd,cloudtianyi,ydypzyfx,yunpan139,guaguale115,Mbox115,shares_115,zai<PERSON>yun,PanjClub,NewQuark,yunpanpan,kuake_movie,Quark_Movies,alyp_4K_Movies,alyp_TV,yunpanshare,shareAliyun,alyp_1,BaiduCloudDisk",
                "commonConfig": "./json/peizhi.json",
                "filter": "./json/douban.json"
            }
        },
        {
            "key": "弹幕",
            "name": "🅿弹幕|搜索",
            "type": 3,
            "api": "csp_Huban",
            "jar": "./jars/弹幕.jar"
        },
        {
            "key": "ConfigCenter",
            "name": "🅿网盘配置中心",
            "type": 3,
            "api": "csp_ConfigCenter",
            "searchable": 0,
            "changeable": 0,
            "ext": {
                "commonConfig": "./json/peizhi.json"
            }
        },
        {
            "key": "csp_Gz360",
            "name": "🍉瓜子",
            "type": 3,
            "api": "csp_Gz360",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "csp_LiteApple",
            "name": "🍎苹果",
            "type": 3,
            "api": "csp_LiteApple",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "巧技二",
            "name": "💢聚搜┃仅搜索",
            "type": 3,
            "api": "csp_qiao2",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "7lj763gg402i79425739i7jghj118797l4hj840gi18633331l4708g2h7145403549g44l8ii56i187681hkjj3hhgh1ih3l32j250lk1k786lj20j468hk3hli4l46gig4i3g7g2722328j0136h01i7g5183k22k7gg3i72hk81gl8k9839kl7i0707"
        },
        {
            "key": "天天",
            "name": "💢天天",
            "type": 3,
            "api": "csp_TTian",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "7lj763gg09397919456493i0h44j8681highi4"
        },
        {
            "key": "热播",
            "name": "💢热播",
            "type": 3,
            "api": "csp_TTian",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "7lj763gg0939791h1l3888jig44gi291li"
        },
        {
            "key": "追剧",
            "name": "💢追剧",
            "type": 3,
            "api": "csp_TTian",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "7lj763gg0939791h1l2681i6g94li291li"
        },
        {
            "key": "lanyingys",
            "name": "💢橘子",
            "type": 3,
            "api": "csp_Qiji",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "7lj763gg0939795i0678i481k40hi2i3ghlg840i9lj166700g1449g3k5244k2017h35698h4739ih31117kigil3k02hl2jk6i5155ih9kkgl311gl37g240g85111ggg2"
        },
        {
            "key": "huomaoys",
            "name": "💢火猫",
            "type": 3,
            "api": "csp_Muou",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "7lj763gg0939790i413gi484k8058896highi4414h68l7g6hk8qiaojig9k2k289l9ik807i213k5j602"
        },
        {
            "key": "yizys",
            "name": "💢驿站",
            "type": 3,
            "api": "csp_Muou",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "7lj763gg402i7942403h83i2h945858hljhji148i18k2837535112l2qiaojik9075l17028i49g192419i8g3245h3j1l9gi02h6k7732650h3h09jkg759j65hj39l50347k3gj97l4g12l7h6418h6k9j04l26i1glgj3631973hh280lkihjh"
        },
        {
            "key": "永夜",
            "name": "💢永夜",
            "type": 3,
            "api": "csp_Qiji",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "7lj763gg402i794240208hi3g05l8181highj909i7i7282k425j21ggg13i630l378272k58l71i2h42l18g1h9glg433jll14i6071h6g5k8gj169138kl3kll1j"
        },
        {
            "key": "趣看",
            "name": "💢趣看",
            "type": 3,
            "api": "csp_Muou",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "7lj763gg402i7942463j9j9jg1499j87l9g9i413il8gqiaoji20g7g4919j3g019296lk9351hh3k5gg38lj0754il3jg"
        },
        {
            "key": "公共",
            "name": "💢公共",
            "type": 3,
            "api": "csp_Muou",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "7lj763gg402i7942463ji4qiaojijjh456889il6k6i35kj995h4j18li7kl2870klhg8hi647j5707k4ki7ig6953kj"
        },
        {
            "key": "主角",
            "name": "💢主角",
            "type": 3,
            "api": "csp_Muou",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "7lj763gg402i794247258k9jh6598585l3g6ij13il8g20g9qiaoji8j9i9k1g3k90h7i507i213k5j602"
        },
        {
            "key": "kafeiys",
            "name": "💢狂风",
            "type": 3,
            "api": "csp_Qiji",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "7lj763gg402i7942522k849kg4499i9hl1k5ik48i38l292l1l4l1klgl41255091i8313k2ik478h8j751kh28kh9gk56l3k874684ll3h7j9k83l9034li7lhi5g45k8k3j6hkk760276g8h5h295i9jk7597177g3kk7g3hh79ili"
        },
        {
            "key": "csp_Qiyou",
            "name": "🦌奇优",
            "type": 3,
            "api": "csp_Qiyou",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "csp_Jiaozi",
            "name": "🥣饺子",
            "type": 3,
            "api": "csp_Jiaozi",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "csp_Czsapp",
            "name": "🏭厂长",
            "type": 3,
            "api": "csp_Czsapp",
            "playerType": 2,
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "https://www.czzymovie.com/"
        },
        {
            "key": "农民",
            "name": "👩‍🌾农民",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/农民影视.json"
        },
        {
            "key": "骚火",
            "name": "🔥骚火",
            "type": 3,
            "api": "csp_XBPQ",
            "ext": {
                "简介": "p_txt show_part\">&&</p>",
                "副标题": "v_note\">&&</div>",
                "分类url": "https://saohuody.com/list/{cateId}-{catePg}.html;;d1",
                "分类": "国产剧$12#港剧$13#台剧$14#日剧$15#韩剧$16#美剧$17#海外$18#泰剧$19#动作$5#喜剧$6#爱情$7#科幻$8#恐怖$9#剧情$10#战争$11#动画$33"
            }
        },
        {
            "key": "可可",
            "name": "☕️可可",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/可可影视.json"
        },
        {
            "key": "剧圈圈",
            "name": "⭕剧圈圈",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/剧圈圈.json",
            "click": "document.getElementById('playleft').children[0].contentWindow.document.getElementById('start').click()"
        },
        {
            "key": "影视大全",
            "name": "🎞影视大全",
            "type": 3,
            "api": "csp_XBPQ",
            "ext": {
                "分类url": "https://www.iysdq.cc/vodshow/{cateId}-{area}-------{catePg}---.html",
                "分类": "电影$1#电视剧$2#综艺$3#动漫$4#短剧$5"
            }
        },
        {
            "key": "永乐",
            "name": "🍰永乐",
            "type": 3,
            "api": "csp_XBPQ",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": {
                "请求头": "User-Agent$MOBILE_UA",
                "编码": "UTF-8",
                "分类": "电影$1#电视剧$2#综艺$3#动漫$4",
                "类型": "动作片$6#喜剧片$7#爱情片$8#科幻片$9#奇幻片$10#恐怖片$11#剧情片$12#战争片$20#动画片$26#悬疑片$22#冒险片$23#犯罪片$24#惊悚片$45#歌舞片$46#灾难片$47#网络片$48||国产剧$13#港台剧$14#日剧$15#韩剧$33#欧美剧$16#泰剧$34#新马剧$35#其他剧$25||内地综艺$27#港台综艺$28#日本综艺$29#韩国综艺$36#欧美综艺$30#新马泰综艺$37#其他综艺$38||国产动漫$31#日本动漫$32#韩国动漫$39#港台动漫$40#新马泰动漫$41#欧美动漫$42#其他动漫$43",
                "分类url": "https://www.ylys.tv/vodshow/{cateId}-{area}-{by}-{class}-{lang}-{letter}---{catePg}---{year}.html"
            }
        },
        {
            "key": "面包",
            "name": "🎁面包",
            "type": 3,
            "api": "csp_XBPQ",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": {
                "请求头": "User-Agent$MOBILE_UA",
                "编码": "UTF-8",
                "分类url": "https://v.aiwule.com/vodshow/{cateId}-{area}-{by}-{class}-{lang}-{letter}---{catePg}---{year}.html",
                "分类": "电影$20#电视剧$21#动漫$23#综艺$22#短剧$47",
                "简介": "简介：&&"
            }
        },
        {
            "key": "西瓜",
            "name": "🍉西瓜",
            "type": 3,
            "api": "csp_XBPQ",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": {
                "主页url": "https://sszzyy.com/",
                "分类url": "https://sszzyy.com/index.php/vod/show/id/{cateId}/page/{catePg}.html",
                "分类": "电影$20#剧集$37",
                "标题": "title=\"*\">&&</div>",
                "图片": "data-original=\"&&\"",
                "链接": "href=\"&&\"",
                "播放数组": "content_playlist&&</ul>",
                "播放列表": "<a&&</a>",
                "播放标题": ">&&</a>",
                "跳转播放链接": "src=\"&&\"",
                "线路数组": "class=\"titleName cr3 swiper-slide&&</a>",
                "线路标题": ">&&</a>"
            }
        },
        {
            "key": "百思派",
            "name": "💯百思派",
            "type": 3,
            "api": "csp_XBPQ",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "click": "document.querySelector(\"#playleft iframe\").contentWindow.document.querySelector(\"#start\").click();",
            "ext": {
                "分类url": "https://www.bestpipe.cn/vodshow/{cateId}-{area}-------{catePg}---.html",
                "分类": "电影$20#剧集$21#短剧$24#综艺$23#动漫$22"
            }
        },
        {
            "key": "csp_Lkdy",
            "name": "🏔️来看",
            "type": 3,
            "api": "csp_Lkdy",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "csp_Fantuan",
            "name": "🍙饭团",
            "type": 3,
            "api": "csp_Fantuan",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "click": "document.querySelector(\"#playleft iframe\").contentWindow.document.querySelector(\"#start\").click();",
            "ext": "https://www.fantuan.vip"
        },
        {
            "key": "ZXZJ",
            "name": "🏠在线",
            "api": "csp_Zxzj",
            "type": 3,
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "7lj763gg402i7942463j9j9jgg449698khhh845ki38473"
        },
        {
            "key": "csp_Ddys",
            "name": "📺低端",
            "type": 3,
            "api": "csp_Ddys",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "csp_Kuaikan",
            "name": "👀快看",
            "type": 3,
            "api": "csp_Kuaikan",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "csp_Ikanbot",
            "name": "👾Ikanbot",
            "type": 3,
            "api": "csp_Ikanbot",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "csp_Jianpian",
            "name": "🧲荐片",
            "type": 3,
            "api": "csp_Jianpian",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/jianpian.json"
        },
        {
            "key": "csp_xlys",
            "name": "🧲修罗",
            "type": 3,
            "api": "csp_xlys",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "https://v.xlys.ltd.ua"
        },
        {
            "key": "csp_New6v",
            "name": "🧲新6V",
            "type": 3,
            "api": "csp_New6v",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "csp_DyGod",
            "name": "🧲电影天堂",
            "type": 3,
            "api": "csp_DyGod",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "csp_QnMp4",
            "name": "🧲七妹",
            "type": 3,
            "api": "csp_QnMp4",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "csp_MeijuTT",
            "name": "🧲美剧天堂",
            "type": 3,
            "api": "csp_MeijuTT",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "csp_MeijuMi",
            "name": "🧲美剧迷",
            "type": 3,
            "api": "csp_MeijuMi",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "https://www.meijumi.net/"
        },
        {
            "key": "csp_BLSGod",
            "name": "🧲80S影视",
            "type": 3,
            "api": "csp_BLSGod",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "csp_SP360",
            "name": "💘360",
            "type": 3,
            "api": "csp_SP360",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "changeable": 1
        },
        {
            "key": "csp_Ysj",
            "name": "📮异世界",
            "type": 3,
            "api": "csp_Ysj",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "樱花",
            "name": "💮樱花",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/樱花动漫.json"
        },
        {
            "key": "动漫巴士",
            "name": "🚌动漫巴士",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/动漫巴士.json"
        },
        {
            "key": "豆瓣",
            "name": "🚀豆瓣预告",
            "type": 3,
            "api": "csp_YGP",
            "playerType": 2,
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "csp_Kanqiu",
            "name": "⚾看球",
            "type": 3,
            "api": "csp_Kanqiu",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 0,
            "gridview": 3,
            "style": {
                "type": "list"
            }
        },
        {
            "key": "csp_Kugou",
            "name": "🐶酷狗",
            "type": 3,
            "api": "csp_Kugou",
            "playerType": 2,
            "ext": {
                "classes": [
                    {
                        "type_name": "酷狗",
                        "type_id": "kugou"
                    }
                ]
            }
        },
        {
            "key": "csp_BookTing",
            "name": "📚听书",
            "type": 3,
            "playerType": 2,
            "api": "csp_BookTing",
            "searchable": 0,
            "ext": "https://m.ting275.com",
            "style": {
                "type": "rect",
                "ratio": 1.333
            }
        },
        {
            "key": "csp_FirstAid",
            "name": "🚑急救教学",
            "type": 3,
            "api": "csp_FirstAid",
            "searchable": 0,
            "quickSearch": 0,
            "style": {
                "type": "rect",
                "ratio": 3.8
            }
        },
        {
            "key": "玩偶gg",
            "name": "👽玩偶",
            "type": 3,
            "api": "csp_Wogg",
            "searchable": 1,
            "changeable": 0,
            "ext": {
                "commonConfig": "./json/peizhi.json",
                "siteUrl": "https://wogg.xxooo.cf",
                "filter": "./json/wogg.json"
            }
        },
        {
            "key": "片库",
            "name": "🅿片库",
            "type": 3,
            "api": "csp_Qiwei",
            "searchable": 1,
            "filterable": 0,
            "switchable": 0,
            "ext": {
                "siteUrl": "https://www.qwnull.com",
                "commonConfig": "./json/peizhi.json"
            }
        },
        {
            "key": "二小",
            "name": "😈二小",
            "type": 3,
            "api": "csp_kongbai",
            "searchable": 1,
            "changeable": 0,
            "ext": {
                "commonConfig": "./json/peizhi.json",
                "siteUrl": "https://erxiaofn.click/",
                "filter": "./json/wogg.json"
            }
        },
        {
            "key": "至臻",
            "name": "💯至臻",
            "type": 3,
            "api": "csp_kongbai",
            "searchable": 1,
            "changeable": 0,
            "ext": {
                "commonConfig": "./json/peizhi.json",
                "siteUrl": "https://www.mihdr.top",
                "filter": "./json/wogg.json"
            }
        },
        {
            "key": "蜡笔",
            "name": "🖍︎蜡笔",
            "type": 3,
            "api": "csp_kongbai",
            "searchable": 1,
            "changeable": 0,
            "ext": {
                "commonConfig": "./json/peizhi.json",
                "siteUrl": "https://feimao666.fun",
                "filter": "./json/wogg.json"
            }
        },
        {
            "key": "欧哥",
            "name": "🅾欧哥",
            "type": 3,
            "api": "csp_kongbai",
            "searchable": 1,
            "changeable": 0,
            "ext": {
                "commonConfig": "./json/peizhi.json",
                "siteUrl": "https://woog.nxog.eu.org",
                "filter": "./json/wogg.json"
            }
        },
        {
            "key": "多多",
            "name": "🌟多多",
            "type": 3,
            "api": "csp_kongbai",
            "searchable": 1,
            "changeable": 0,
            "ext": {
                "commonConfig": "./json/peizhi.json",
                "siteUrl": "https://tv.yydsys.top",
                "filter": "./json/wogg.json"
            }
        },
        {
            "key": "星剧社",
            "name": "⭐️星剧社",
            "type": 3,
            "api": "csp_Star2",
            "searchable": 1,
            "filterable": 0,
            "switchable": 0,
            "ext": {
                "siteUrl": "https://1.star2.cn",
                "commonConfig": "./json/peizhi.json"
            }
        },
        {
            "key": "csp_PanSearch",
            "name": "📀盘搜索",
            "type": 3,
            "api": "csp_PanSearch",
            "searchable": 1,
            "changeable": 0,
            "ext": {
                "commonConfig": "./json/peizhi.json"
            }
        },
        {
            "key": "木偶",
            "name": "🧸木偶",
            "type": 3,
            "api": "csp_kongbai",
            "searchable": 1,
            "changeable": 0,
            "ext": {
                "commonConfig": "./json/peizhi.json",
                "siteUrl": "http://123.666291.xyz/",
                "filter": "./json/wogg.json"
            }
        },
        {
            "key": "闪电",
            "name": "⚡闪电",
            "type": 3,
            "api": "csp_kongbai",
            "searchable": 1,
            "changeable": 0,
            "ext": {
                "commonConfig": "./json/peizhi.json",
                "siteUrl": "http://1.95.79.193",
                "filter": "./json/wogg.json"
            }
        },
        {
            "key": "盘Ta",
            "name": "🍥盘Ta",
            "type": 3,
            "api": "csp_PanTa",
            "searchable": 1,
            "filterable": 0,
            "switchable": 0,
            "ext": {
                "siteUrl": "https://www.91panta.cn/",
                "commonConfig": "./json/peizhi.json"
            }
        },
        {
            "key": "AList",
            "name": "🅰AList",
            "type": 3,
            "api": "csp_AList",
            "searchable": 1,
            "changeable": 0,
            "ext": "./json/alist.json"
        },
        {
            "key": "网盘集合",
            "name": "🅿网盘集合|4K",
            "type": 3,
            "api": "csp_网盘集合",
            "searchable": 1,
            "filterable": 0,
            "switchable": 0,
            "ext": {
                "commonConfig": "./json/peizhi.json",
                "share": "./json/网盘集合.json"
            }
        },
        {
            "key": "Youtube",
            "name": "⚡Youtube（墙外）",
            "type": 3,
            "api": "csp_Youtube",
            "searchable": 1,
            "changeable": 1,
            "style": {
                "type": "rect",
                "ratio": 1.433
            },
            "ext": {
                "commonConfig": "./json/peizhi.json",
                "json": "./json/youtube.json",
                "cookie": "http://127.0.0.1:9978/file/TVBox/youtubecookie.json"
            }
        },
        {
            "key": "TgYunPan|本地",
            "name": "🅿TgYunPan|本地（墙外）",
            "type": 3,
            "api": "csp_TgYunPanLocal",
            "searchable": 1,
            "filterable": 0,
            "switchable": 0,
            "ext": {
                "count": 4,
                "channelUsername": "bdwpzhpd,bdwpzhpd,alyp_TV,ucquark,wp123zy,oneonefivewpfx,tyypzhpd,cloudtianyi,ydypzyfx,yunpan139,guaguale115,Mbox115,shares_115,zaihuayun,PanjClub,NewQuark,yunpanpan,kuake_movie,Quark_Movies,alyp_4K_Movies,alyp_TV,yunpanshare,shareAliyun,alyp_1,BaiduCloudDisk",
                "commonConfig": "./json/peizhi.json"
            }
        },
        {
            "key": "push_agent",
            "name": "🐧裙：926953902",
            "type": 3,
            "api": "csp_Push",
            "searchable": 0,
            "quickSearch": 0,
            "ext": {
                "commonConfig": "./json/peizhi.json"
            }
        },
        {
            "key": "软件下载",
            "name": "🅿软件下载",
            "type": 3,
            "api": "csp_网盘集合",
            "searchable": 0,
            "filterable": 0,
            "switchable": 0,
            "ext": {
                "commonConfig": "./json/peizhi.json",
                "share": "./json/apk.json"
            }
        },
        {
            "key": "游戏直播",
            "name": "🙀游戏直播",
            "type": 3,
            "api": "csp_Living",
            "searchable": 1,
            "changeable": 0,
            "ext": "https://lemonlive25.pages.dev"
        },
        {
            "key": "虎牙",
            "name": "🐯虎牙直播",
            "type": 3,
            "api": "./api/drpy2.js",
            "ext": "./js/huya2.js",
            "style": {
                "type": "rect",
                "ratio": 1.755
            },
            "timeout": 15,
            "playerType": "2",
            "searchable": 0,
            "quickSearch": 0,
            "changeable": 0
        },
        {
            "key": "斗鱼",
            "name": "🐟斗鱼直播",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/斗鱼直播.js",
            "style": {
                "type": "rect",
                "ratio": 1.755
            },
            "playerType": "2",
            "searchable": 0,
            "quickSearch": 0,
            "changeable": 0
        },
        {
            "key": "有声小说吧",
            "name": "📚有声小说吧",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "playerType": "2",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 0,
            "ext": "./js/有声小说吧.js"
        },
        {
            "key": "哔哩",
            "name": "🅱️哔哩哔哩",
            "type": 3,
            "api": "csp_Bili",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "style": {
                "type": "rect",
                "ratio": 1.433
            },
            "ext": {
                "json": "./json/Bili.json",
                "cookie": ""
            }
        },
        {
            "key": "相声小品",
            "name": "🅱️相声小品",
            "type": 3,
            "api": "csp_Bili",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "style": {
                "type": "rect",
                "ratio": 1.433
            },
            "ext": {
                "json": "./json/Blixs.json",
                "cookie": ""
            }
        },
        {
            "key": "戏曲",
            "name": "🅱️戏曲",
            "type": 3,
            "api": "csp_Bili",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "style": {
                "type": "rect",
                "ratio": 1.433
            },
            "ext": {
                "json": "./json/Blixq.json",
                "cookie": ""
            }
        },
        {
            "key": "少儿",
            "name": "🅱️少儿",
            "type": 3,
            "api": "csp_Bili",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "style": {
                "type": "rect",
                "ratio": 1.433
            },
            "ext": {
                "json": "./json/少儿教育.json",
                "cookie": ""
            }
        },
        {
            "key": "小学",
            "name": "🅱️小学",
            "type": 3,
            "api": "csp_Bili",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "style": {
                "type": "rect",
                "ratio": 1.433
            },
            "ext": {
                "json": "./json/小学课堂.json",
                "cookie": ""
            }
        },
        {
            "key": "初中",
            "name": "🅱️初中",
            "type": 3,
            "api": "csp_Bili",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "style": {
                "type": "rect",
                "ratio": 1.433
            },
            "ext": {
                "json": "./json/初中课堂.json",
                "cookie": ""
            }
        },
        {
            "key": "高中",
            "name": "🅱️高中",
            "type": 3,
            "api": "csp_Bili",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "style": {
                "type": "rect",
                "ratio": 1.433
            },
            "ext": {
                "json": "./json/高中课堂.json",
                "cookie": ""
            }
        }
    ],
    "parses": [
        {
            "name": "Web聚合",
            "type": 3,
            "url": "Web"
        },
        {
            "name": "Json聚合",
            "type": 3,
            "url": "Demo"
        },
        {
            "name": "云",
            "type": 0,
            "url": "https://yparse.ik9.cc/index.php?url=",
            "ext": {
                "flag": [
                    "qq",
                    "腾讯",
                    "qiyi",
                    "爱奇艺",
                    "奇艺",
                    "youku",
                    "优酷",
                    "mgtv",
                    "芒果",
                    "letv",
                    "乐视",
                    "pptv",
                    "PPTV",
                    "sohu",
                    "bilibili",
                    "哔哩哔哩",
                    "哔哩"
                ],
                "header": {
                    "User-Agent": "okhttp/4.1.0"
                }
            }
        },
        {
            "name": "HLS",
            "type": 0,
            "url": "https://jx.hls.one/?url=",
            "ext": {
                "flag": [
                    "qq",
                    "腾讯",
                    "qiyi",
                    "爱奇艺",
                    "奇艺",
                    "youku",
                    "优酷",
                    "mgtv",
                    "芒果",
                    "letv",
                    "乐视",
                    "pptv",
                    "PPTV",
                    "sohu",
                    "bilibili",
                    "哔哩哔哩",
                    "哔哩"
                ],
                "header": {
                    "User-Agent": "okhttp/4.1.0"
                }
            }
        },
        {
            "name": "m3u8TV",
            "type": 0,
            "url": "https://jx.m3u8.tv/jiexi/?url=",
            "ext": {
                "flag": [
                    "qq",
                    "腾讯",
                    "qiyi",
                    "爱奇艺",
                    "奇艺",
                    "youku",
                    "优酷",
                    "mgtv",
                    "芒果",
                    "letv",
                    "乐视",
                    "pptv",
                    "PPTV",
                    "sohu",
                    "bilibili",
                    "哔哩哔哩",
                    "哔哩"
                ],
                "header": {
                    "User-Agent": "okhttp/4.1.0"
                }
            }
        },
        {
            "name": "77",
            "type": 0,
            "url": "https://jx.77flv.cc/?url=",
            "ext": {
                "flag": [
                    "qq",
                    "腾讯",
                    "qiyi",
                    "爱奇艺",
                    "奇艺",
                    "youku",
                    "优酷",
                    "mgtv",
                    "芒果",
                    "letv",
                    "乐视",
                    "pptv",
                    "PPTV",
                    "sohu",
                    "bilibili",
                    "哔哩哔哩",
                    "哔哩"
                ]
            }
        },
        {
            "name": "咸鱼",
            "type": 0,
            "url": "https://jx.xymp4.cc/?url=",
            "ext": {
                "flag": [
                    "qq",
                    "腾讯",
                    "qiyi",
                    "爱奇艺",
                    "奇艺",
                    "youku",
                    "优酷",
                    "mgtv",
                    "芒果",
                    "letv",
                    "乐视",
                    "pptv",
                    "PPTV",
                    "sohu",
                    "bilibili",
                    "哔哩哔哩",
                    "哔哩"
                ]
            }
        }
    ],
    "lives": [
        {
            "name": "Yoursmile",
            "type": 0,
            "url": "./lives/Yoursmile.txt",
            "epg": "http://epg.51zmt.top:8000/api/diyp/?ch={name}&date={date}",
            "ua": "okhttp/3.15"
        },
        {
            "name": "feiyang.allinone",
            "type": 0,
            "url": "./lives/feiyang.allinone.txt",
            "epg": "http://epg.51zmt.top:8000/api/diyp/?ch={name}&date={date}",
            "ua": "okhttp/3.15"
        },
        {
            "name": "feiyang.bililive",
            "type": 0,
            "url": "./lives/feiyang.bililive.txt",
            "epg": "http://epg.51zmt.top:8000/api/diyp/?ch={name}&date={date}",
            "ua": "okhttp/3.15"
        },
        {
            "name": "feiyang.huyayqk",
            "type": 0,
            "url": "./lives/feiyang.huyayqk.txt",
            "epg": "http://epg.51zmt.top:8000/api/diyp/?ch={name}&date={date}",
            "ua": "okhttp/3.15"
        },
        {
            "name": "feiyang.douyuyqk",
            "type": 0,
            "url": "./lives/feiyang.douyuyqk.txt",
            "epg": "http://epg.51zmt.top:8000/api/diyp/?ch={name}&date={date}",
            "ua": "okhttp/3.15"
        },
        {
            "name": "feiyang.yylunbo",
            "type": 0,
            "url": "./lives/feiyang.yylunbo.txt",
            "epg": "http://epg.51zmt.top:8000/api/diyp/?ch={name}&date={date}",
            "ua": "okhttp/3.15"
        }
    ],
    "logo": "https://ghfast.top/raw.githubusercontent.com/yoursmile66/TVBox/refs/heads/main/json/NanFeng.gif",
    "rules": [
        {
            "name": "量非",
            "hosts": [
                "lz",
                "vip.lz",
                "v.cdnlz",
                "hd.lz",
                "ffzy",
                "vip.ffzy",
                "hd.ffzy"
            ],
            "regex": [
                "#EXT-X-DISCONTINUITY\r*\n*#EXTINF:6.666667,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXT-X-DISCONTINUITY\r*\n*#EXTINF:6.600000,[\\s\\S]*?#EXT-X-DISCONTINUITY"
            ]
        },
        {
            "name": "索尼",
            "hosts": [
                "suonizy",
                "qrssv.com"
            ],
            "regex": [
                "15.1666",
                "15.2666"
            ]
        },
        {
            "name": "乐视",
            "hosts": [
                "leshiyun"
            ],
            "regex": [
                "15.92"
            ]
        },
        {
            "name": "优质",
            "hosts": [
                "yzzy",
                "playback"
            ],
            "regex": [
                "16.63",
                "18.66",
                "17.66",
                "19.13"
            ]
        },
        {
            "name": "快看",
            "hosts": [
                "kuaikan",
                "vip.kuaikan"
            ],
            "regex": [
                "15.32",
                "15.231",
                "18.066"
            ]
        },
        {
            "name": "360",
            "hosts": [
                "lyhuicheng"
            ],
            "regex": [
                "#EXTINF.*?\\s+.*?hrz8QcR9.*?\\.ts\\s+",
                "#EXT-X-KEY:METHOD=NONE[\\s\\S]*?#EXT-X-DISCONTINUITY"
            ]
        },
        {
            "name": "开源棋牌",
            "hosts": [
                "askzycdn",
                "jkunbf",
                "bfikuncdn",
                "bfaskcdn"
            ],
            "regex": [
                "#EXT-X-KEY:METHOD=NONE\r*\n*#EXTINF:5,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXT-X-KEY:METHOD=AES-128,URI=\"[^\"]+\"\r*\n*#EXTINF:3.333,[\\s\\S]*?#EXT-X-DISCONTINUITY"
            ]
        },
        {
            "name": "暴风",
            "hosts": [
                "bfengbf.com",
                "bfzy",
                "c1"
            ],
            "regex": [
                "#EXTINF.*?\\s+.*?adjump.*?\\.ts\\s+",
                "#EXT-X-DISCONTINUITY\r*\n*#EXTINF:3,[\\s\\S]*?#EXT-X-DISCONTINUITY"
            ]
        },
        {
            "name": "农民",
            "hosts": [
                "toutiaovod.com"
            ],
            "regex": [
                "video/tos/cn"
            ]
        },
        {
            "name": "火山",
            "hosts": [
                "huoshan.com"
            ],
            "regex": [
                "item_id="
            ]
        },
        {
            "name": "抖音",
            "hosts": [
                "douyin.com"
            ],
            "regex": [
                "is_play_url="
            ]
        },
        {
            "name": "磁力",
            "hosts": [
                "magnet"
            ],
            "regex": [
                "最 新",
                "直 播",
                "更 新"
            ]
        },
        {
            "name": "饭团点击",
            "hosts": [
                "dadagui",
                "freeok",
                "dadagui"
            ],
            "script": [
                "document.querySelector(\"#playleft iframe\").contentWindow.document.querySelector(\"#start\").click();"
            ]
        },
        {
            "name": "毛驴点击",
            "hosts": [
                "www.maolvys.com"
            ],
            "script": [
                "document.getElementsByClassName('swal-button swal-button--confirm')[0].click()"
            ]
        }
    ],
    "doh": [
        {
            "name": "Google",
            "url": "https://dns.google/dns-query",
            "ips": [
                "*******",
                "*******"
            ]
        },
        {
            "name": "Cloudflare",
            "url": "https://cloudflare-dns.com/dns-query",
            "ips": [
                "*******",
                "*******",
                "2606:4700:4700::1111",
                "2606:4700:4700::1001"
            ]
        },
        {
            "name": "AdGuard",
            "url": "https://dns.adguard.com/dns-query",
            "ips": [
                "*************",
                "*************"
            ]
        },
        {
            "name": "DNSWatch",
            "url": "https://resolver2.dns.watch/dns-query",
            "ips": [
                "************",
                "************"
            ]
        },
        {
            "name": "Quad9",
            "url": "https://dns.quad9.net/dns-quer",
            "ips": [
                "*******",
                "***************"
            ]
        }
    ],
    "ijk": [
        {
            "group": "软解码",
            "options": [
                {
                    "category": 4,
                    "name": "opensles",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "overlay-format",
                    "value": "842225234"
                },
                {
                    "category": 4,
                    "name": "framedrop",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "soundtouch",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "start-on-prepared",
                    "value": "1"
                },
                {
                    "category": 1,
                    "name": "http-detect-rangeupport",
                    "value": "0"
                },
                {
                    "category": 1,
                    "name": "fflags",
                    "value": "fastseek"
                },
                {
                    "category": 2,
                    "name": "skip_loop_filter",
                    "value": "48"
                },
                {
                    "category": 4,
                    "name": "reconnect",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "enable-accurateeek",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "mediacodec",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "mediacodec-auto-rotate",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "mediacodec-handle-resolution-change",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "mediacodec-hevc",
                    "value": "0"
                },
                {
                    "category": 1,
                    "name": "dns_cache_timeout",
                    "value": "600000000"
                }
            ]
        },
        {
            "group": "硬解码",
            "options": [
                {
                    "category": 4,
                    "name": "opensles",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "overlay-format",
                    "value": "842225234"
                },
                {
                    "category": 4,
                    "name": "framedrop",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "soundtouch",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "start-on-prepared",
                    "value": "1"
                },
                {
                    "category": 1,
                    "name": "http-detect-rangeupport",
                    "value": "0"
                },
                {
                    "category": 1,
                    "name": "fflags",
                    "value": "fastseek"
                },
                {
                    "category": 2,
                    "name": "skip_loop_filter",
                    "value": "48"
                },
                {
                    "category": 4,
                    "name": "reconnect",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "enable-accurateeek",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "mediacodec",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "mediacodec-auto-rotate",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "mediacodec-handle-resolution-change",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "mediacodec-hevc",
                    "value": "1"
                },
                {
                    "category": 1,
                    "name": "dns_cache_timeout",
                    "value": "600000000"
                }
            ]
        }
    ],
    "ads": [
        "mozai.4gtv.tv",
        "pv.vipwm.cc"
    ]
}