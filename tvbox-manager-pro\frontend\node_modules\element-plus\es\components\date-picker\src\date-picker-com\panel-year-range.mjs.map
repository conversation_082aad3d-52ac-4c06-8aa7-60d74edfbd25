{"version": 3, "file": "panel-year-range.mjs", "sources": ["../../../../../../../packages/components/date-picker/src/date-picker-com/panel-year-range.vue"], "sourcesContent": ["<template>\n  <div :class=\"panelKls\">\n    <div :class=\"ppNs.e('body-wrapper')\">\n      <slot name=\"sidebar\" :class=\"ppNs.e('sidebar')\" />\n      <div v-if=\"hasShortcuts\" :class=\"ppNs.e('sidebar')\">\n        <button\n          v-for=\"(shortcut, key) in shortcuts\"\n          :key=\"key\"\n          type=\"button\"\n          :class=\"ppNs.e('shortcut')\"\n          @click=\"handleShortcutClick(shortcut)\"\n        >\n          {{ shortcut.text }}\n        </button>\n      </div>\n      <div :class=\"ppNs.e('body')\">\n        <div :class=\"leftPanelKls.content\">\n          <div :class=\"drpNs.e('header')\">\n            <button\n              type=\"button\"\n              :class=\"leftPanelKls.arrowLeftBtn\"\n              @click=\"leftPrevYear\"\n            >\n              <slot name=\"prev-year\">\n                <el-icon><d-arrow-left /></el-icon>\n              </slot>\n            </button>\n            <button\n              v-if=\"unlinkPanels\"\n              type=\"button\"\n              :disabled=\"!enableYearArrow\"\n              :class=\"leftPanelKls.arrowRightBtn\"\n              @click=\"leftNextYear\"\n            >\n              <slot name=\"next-year\">\n                <el-icon><d-arrow-right /></el-icon>\n              </slot>\n            </button>\n            <div>{{ leftLabel }}</div>\n          </div>\n          <year-table\n            selection-mode=\"range\"\n            :date=\"leftDate\"\n            :min-date=\"minDate\"\n            :max-date=\"maxDate\"\n            :range-state=\"rangeState\"\n            :disabled-date=\"disabledDate\"\n            @changerange=\"handleChangeRange\"\n            @pick=\"handleRangePick\"\n            @select=\"onSelect\"\n          />\n        </div>\n        <div :class=\"rightPanelKls.content\">\n          <div :class=\"drpNs.e('header')\">\n            <button\n              v-if=\"unlinkPanels\"\n              type=\"button\"\n              :disabled=\"!enableYearArrow\"\n              :class=\"rightPanelKls.arrowLeftBtn\"\n              @click=\"rightPrevYear\"\n            >\n              <slot name=\"prev-year\">\n                <el-icon><d-arrow-left /></el-icon>\n              </slot>\n            </button>\n            <button\n              type=\"button\"\n              :class=\"rightPanelKls.arrowRightBtn\"\n              @click=\"rightNextYear\"\n            >\n              <slot name=\"next-year\">\n                <el-icon><d-arrow-right /></el-icon>\n              </slot>\n            </button>\n            <div>{{ rightLabel }}</div>\n          </div>\n          <year-table\n            selection-mode=\"range\"\n            :date=\"rightDate\"\n            :min-date=\"minDate\"\n            :max-date=\"maxDate\"\n            :range-state=\"rangeState\"\n            :disabled-date=\"disabledDate\"\n            @changerange=\"handleChangeRange\"\n            @pick=\"handleRangePick\"\n            @select=\"onSelect\"\n          />\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, ref, toRef, unref, useSlots, watch } from 'vue'\nimport dayjs from 'dayjs'\nimport { isArray } from '@element-plus/utils'\nimport { DArrowLeft, DArrowRight } from '@element-plus/icons-vue'\nimport ElIcon from '@element-plus/components/icon'\nimport { useLocale } from '@element-plus/hooks'\nimport { PICKER_BASE_INJECTION_KEY } from '@element-plus/components/time-picker'\nimport {\n  panelYearRangeEmits,\n  panelYearRangeProps,\n} from '../props/panel-year-range'\nimport { useYearRangeHeader } from '../composables/use-year-range-header'\nimport { useRangePicker } from '../composables/use-range-picker'\nimport {\n  correctlyParseUserInput,\n  getDefaultValue,\n  isValidRange,\n} from '../utils'\nimport { ROOT_PICKER_IS_DEFAULT_FORMAT_INJECTION_KEY } from '../constants'\nimport YearTable from './basic-year-table.vue'\n\nimport type { Dayjs } from 'dayjs'\n\ndefineOptions({\n  name: 'DatePickerYearRange',\n})\n\nconst props = defineProps(panelYearRangeProps)\nconst emit = defineEmits(panelYearRangeEmits)\nconst step = 10\nconst unit = 'year'\n\nconst { lang } = useLocale()\nconst leftDate = ref(dayjs().locale(lang.value))\nconst rightDate = ref(dayjs().locale(lang.value).add(step, unit))\nconst isDefaultFormat = inject(\n  ROOT_PICKER_IS_DEFAULT_FORMAT_INJECTION_KEY\n) as any\nconst pickerBase = inject(PICKER_BASE_INJECTION_KEY) as any\nconst { shortcuts, disabledDate } = pickerBase.props\nconst format = toRef(pickerBase.props, 'format')\nconst defaultValue = toRef(pickerBase.props, 'defaultValue')\n\nconst {\n  minDate,\n  maxDate,\n  rangeState,\n  ppNs,\n  drpNs,\n\n  handleChangeRange,\n  handleRangeConfirm,\n  handleShortcutClick,\n  onSelect,\n  onReset,\n} = useRangePicker(props, {\n  defaultValue,\n  leftDate,\n  rightDate,\n  step,\n  unit,\n  onParsedValueChanged,\n})\n\nconst {\n  leftPrevYear,\n  rightNextYear,\n  leftNextYear,\n  rightPrevYear,\n  leftLabel,\n  rightLabel,\n  leftYear,\n  rightYear,\n} = useYearRangeHeader({\n  unlinkPanels: toRef(props, 'unlinkPanels'),\n  leftDate,\n  rightDate,\n})\n\nconst hasShortcuts = computed(() => !!shortcuts.length)\n\nconst panelKls = computed(() => [\n  ppNs.b(),\n  drpNs.b(),\n  {\n    'has-sidebar': Boolean(useSlots().sidebar) || hasShortcuts.value,\n  },\n])\n\nconst leftPanelKls = computed(() => {\n  return {\n    content: [ppNs.e('content'), drpNs.e('content'), 'is-left'],\n    arrowLeftBtn: [ppNs.e('icon-btn'), 'd-arrow-left'],\n    arrowRightBtn: [\n      ppNs.e('icon-btn'),\n      { [ppNs.is('disabled')]: !enableYearArrow.value },\n      'd-arrow-right',\n    ],\n  }\n})\n\nconst rightPanelKls = computed(() => {\n  return {\n    content: [ppNs.e('content'), drpNs.e('content'), 'is-right'],\n    arrowLeftBtn: [\n      ppNs.e('icon-btn'),\n      { 'is-disabled': !enableYearArrow.value },\n      'd-arrow-left',\n    ],\n    arrowRightBtn: [ppNs.e('icon-btn'), 'd-arrow-right'],\n  }\n})\n\nconst enableYearArrow = computed(() => {\n  return props.unlinkPanels && rightYear.value > leftYear.value + 1\n})\n\ntype RangePickValue = {\n  minDate: Dayjs\n  maxDate: Dayjs\n}\nconst handleRangePick = (val: RangePickValue, close = true) => {\n  const minDate_ = val.minDate\n  const maxDate_ = val.maxDate\n  if (maxDate.value === maxDate_ && minDate.value === minDate_) {\n    return\n  }\n  emit('calendar-change', [minDate_.toDate(), maxDate_ && maxDate_.toDate()])\n  maxDate.value = maxDate_\n  minDate.value = minDate_\n\n  if (!close) return\n  handleRangeConfirm()\n}\n\nconst parseUserInput = (value: Dayjs | Dayjs[]) => {\n  return correctlyParseUserInput(\n    value,\n    format.value,\n    lang.value,\n    isDefaultFormat\n  )\n}\n\nconst formatToString = (value: Dayjs[] | Dayjs) => {\n  return isArray(value)\n    ? value.map((day) => day.format(format.value))\n    : value.format(format.value)\n}\n\nconst isValidValue = (date: [Dayjs, Dayjs]) => {\n  return (\n    isValidRange(date) &&\n    (disabledDate\n      ? !disabledDate(date[0].toDate()) && !disabledDate(date[1].toDate())\n      : true)\n  )\n}\n\nconst handleClear = () => {\n  const defaultArr = getDefaultValue(unref(defaultValue), {\n    lang: unref(lang),\n    step,\n    unit,\n    unlinkPanels: props.unlinkPanels,\n  })\n  leftDate.value = defaultArr[0]\n  rightDate.value = defaultArr[1]\n  emit('pick', null)\n}\n\nfunction onParsedValueChanged(\n  minDate: Dayjs | undefined,\n  maxDate: Dayjs | undefined\n) {\n  if (props.unlinkPanels && maxDate) {\n    const minDateYear = minDate?.year() || 0\n    const maxDateYear = maxDate.year()\n\n    rightDate.value =\n      minDateYear + step > maxDateYear ? maxDate.add(step, unit) : maxDate\n  } else {\n    rightDate.value = leftDate.value.add(step, unit)\n  }\n}\n\nwatch(\n  () => props.visible,\n  (visible) => {\n    if (!visible && rangeState.value.selecting) {\n      onReset(props.parsedValue)\n      onSelect(false)\n    }\n  }\n)\n\nemit('set-picker-option', ['isValidValue', isValidValue])\nemit('set-picker-option', ['parseUserInput', parseUserInput])\nemit('set-picker-option', ['formatToString', formatToString])\nemit('set-picker-option', ['handleClear', handleClear])\n</script>\n"], "names": ["minDate", "maxDate", "_openBlock", "_createElementBlock", "_normalizeClass", "_unref", "_createElementVNode", "_renderSlot"], "mappings": ";;;;;;;;;;;;;;;;;mCAqHc,CAAA;AAAA,EACZ,IAAM,EAAA,qBAAA;AACR;;;;;;;AAOA,IAAM,MAAA,EAAE,IAAK,EAAA,GAAI,SAAU,EAAA,CAAA;AAC3B,IAAA,MAAM,WAAW,GAAI,CAAA,KAAA,GAAQ,MAAO,CAAA,IAAA,CAAK,KAAK,CAAC,CAAA,CAAA;AAC/C,IAAM,MAAA,SAAA,GAAY,GAAI,CAAA,KAAA,EAAQ,CAAA,MAAA,CAAO,IAAK,CAAA,KAAK,CAAE,CAAA,GAAA,CAAI,IAAM,EAAA,IAAI,CAAC,CAAA,CAAA;AAChE,IAAA,MAAM,eAAkB,GAAA,MAAA,CAAA,2CAAA,CAAA,CAAA;AAAA,IACtB,MAAA,UAAA,GAAA,MAAA,CAAA,yBAAA,CAAA,CAAA;AAAA,IACF,MAAA,EAAA,SAAA,EAAA,YAAA,EAAA,GAAA,UAAA,CAAA,KAAA,CAAA;AACA,IAAM,MAAA,MAAA,GAAA,gBAA6C,CAAA,KAAA,EAAA,QAAA,CAAA,CAAA;AACnD,IAAA,MAAM,YAAE,GAAwB,KAAA,CAAA,UAAe,CAAA,KAAA,EAAA,cAAA,CAAA,CAAA;AAC/C,IAAA,MAAM;AACN,MAAA,OAAqB;AAErB,MAAM,OAAA;AAAA,MACJ,UAAA;AAAA,MACA,IAAA;AAAA,MACA,KAAA;AAAA,MACA,iBAAA;AAAA,MACA,kBAAA;AAAA,MAEA,mBAAA;AAAA,MACA,QAAA;AAAA,MACA,OAAA;AAAA,KACA,GAAA,cAAA,CAAA,KAAA,EAAA;AAAA,MACA,YAAA;AAAA,MACF;AAA0B,MACxB,SAAA;AAAA,MACA,IAAA;AAAA,MACA,IAAA;AAAA,MACA,oBAAA;AAAA,KACA,CAAA,CAAA;AAAA,IACA,MAAA;AAAA,MACD,YAAA;AAED,MAAM,aAAA;AAAA,MACJ,YAAA;AAAA,MACA,aAAA;AAAA,MACA,SAAA;AAAA,MACA,UAAA;AAAA,MACA,QAAA;AAAA,MACA,SAAA;AAAA,KACA,GAAA,kBAAA,CAAA;AAAA,MACA,YAAA,EAAA,KAAA,CAAA,KAAA,EAAA,cAAA,CAAA;AAAA,cACqB;AAAA,MACrB,SAAA;AAAyC,KACzC,CAAA,CAAA;AAAA,IACA,MAAA,YAAA,GAAA,QAAA,CAAA,MAAA,CAAA,CAAA,SAAA,CAAA,MAAA,CAAA,CAAA;AAAA,IACF,MAAC,QAAA,GAAA,QAAA,CAAA,MAAA;AAED,MAAA,IAAM;AAEN,MAAM,KAAA,CAAA,CAAA,EAAA;AAA0B,MAC9B;AAAO,qBACC,EAAA,OAAA,CAAA,QAAA,EAAA,CAAA,OAAA,CAAA,IAAA,YAAA,CAAA,KAAA;AAAA,OACR;AAAA,KAAA,CAAA,CAAA;AAC6D,IAC7D,MAAA,YAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MACD,OAAA;AAED,QAAM,OAAA,EAAA,CAAA,IAAA,CAAA,CAAA,CAAe,SAAS,CAAM,EAAA,KAAA,CAAA,CAAA,CAAA,SAAA,CAAA,EAAA,SAAA,CAAA;AAClC,QAAO,YAAA,EAAA,CAAA,IAAA,CAAA,CAAA,CAAA,UAAA,CAAA,EAAA,cAAA,CAAA;AAAA,QACL,aAAU,EAAK;AAA2C,2BAC3C,CAAK;AAA6B,UAClC,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,UAAA,CAAA,GAAA,CAAA,eAAA,CAAA,KAAA,EAAA;AAAA,UACb,eAAiB;AAAA,SACjB;AAAgD,OAChD,CAAA;AAAA,KACF,CAAA,CAAA;AAAA,IACF,MAAA,aAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MACD,OAAA;AAED,QAAM,OAAA,EAAA,CAAA,IAAA,CAAA,CAAA,CAAA,UAAyB,EAAM,KAAA,CAAA,CAAA,CAAA,SAAA,CAAA,EAAA,UAAA,CAAA;AACnC,QAAO,YAAA,EAAA;AAAA,UACL,IAAA,CAAA,CAAA,CAAS,UAAQ,CAAA;AAA0C,UAC7C,EAAA,aAAA,EAAA,CAAA,eAAA,CAAA,KAAA,EAAA;AAAA,UACZ,cAAiB;AAAA,SAAA;AACuB,QACxC,aAAA,EAAA,CAAA,IAAA,CAAA,CAAA,CAAA,UAAA,CAAA,EAAA,eAAA,CAAA;AAAA,OACF,CAAA;AAAA,KAAA,CAAA,CAAA;AACmD,IACrD,MAAA,eAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MACD,OAAA,KAAA,CAAA,YAAA,IAAA,SAAA,CAAA,KAAA,GAAA,QAAA,CAAA,KAAA,GAAA,CAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAA,MAAA,eAAa,GAAA,CAAA,GAAA,EAAA,KAA0B,GAAA,IAAA,KAAA;AAAyB,MACjE,MAAA,QAAA,GAAA,GAAA,CAAA,OAAA,CAAA;AAMD,MAAA,MAAwB,QAAA,GAAA,GAAA,CAAA,OAAsB,CAAA;AAC5C,MAAA,IAAA,aAAiB,KAAI,QAAA,IAAA,OAAA,CAAA,KAAA,KAAA,QAAA,EAAA;AACrB,QAAA;AACA,OAAA;AACE,MAAA,IAAA,CAAA,iBAAA,EAAA,CAAA,QAAA,CAAA,MAAA,EAAA,EAAA,QAAA,IAAA,QAAA,CAAA,MAAA,EAAA,CAAA,CAAA,CAAA;AAAA,MACF,OAAA,CAAA,KAAA,GAAA,QAAA,CAAA;AACA,MAAK,OAAA,CAAA,KAAA,GAAA,QAAmB,CAAC;AACzB,MAAA,IAAA,CAAA,KAAgB;AAChB,QAAA,OAAgB;AAEhB,MAAA,kBAAY,EAAA,CAAA;AACZ,KAAmB,CAAA;AAAA,IACrB,MAAA,cAAA,GAAA,CAAA,KAAA,KAAA;AAEA,MAAM,OAAA,uBAA6C,CAAA,KAAA,EAAA,MAAA,CAAA,KAAA,EAAA,IAAA,CAAA,KAAA,EAAA,eAAA,CAAA,CAAA;AACjD,KAAO,CAAA;AAAA,IACL,MAAA,cAAA,GAAA,CAAA,KAAA,KAAA;AAAA,MAAA,OACO,OAAA,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAA,GAAA,CAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAA,CAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA;AAAA,KAAA,CAAA;AACF,IACL,MAAA,YAAA,GAAA,CAAA,IAAA,KAAA;AAAA,MACF,OAAA,YAAA,CAAA,IAAA,CAAA,KAAA,YAAA,GAAA,CAAA,YAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,YAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,MAAA,EAAA,CAAA,GAAA,IAAA,CAAA,CAAA;AAAA,KACF,CAAA;AAEA,IAAM,MAAA,WAAA,GAAA,MAA6C;AACjD,MAAA,MAAA,UAAoB,GAAA,eACN,CAAC,KAAQ,CAAA,YAAW,CAAA,EAAA;AACL,QAC/B,IAAA,EAAA,KAAA,CAAA,IAAA,CAAA;AAEA,QAAM,IAAA;AACJ,QAAA,IAAA;AAIM,QAER,YAAA,EAAA,KAAA,CAAA,YAAA;AAEA,OAAA,CAAA,CAAA;AACE,MAAA,QAAmB,CAAA,KAAA,GAAA,UAAA,CAAA,CAAA,CAAA,CAAA;AAAqC,MACtD,eAAgB,GAAA,UAAA,CAAA,CAAA,CAAA,CAAA;AAAA,MAChB,IAAA,CAAA,MAAA,EAAA,IAAA,CAAA,CAAA;AAAA,KACA,CAAA;AAAA,IAAA,6BACoB,CAAA,QAAA,EAAA,QAAA,EAAA;AAAA,MACtB,IAAC,KAAA,CAAA,YAAA,IAAA,QAAA,EAAA;AACD,QAAS,MAAA,uBAAoB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,IAAA,EAAA,KAAA,CAAA,CAAA;AAC7B,QAAU,MAAA,sBAAoB,CAAA,IAAA,EAAA,CAAA;AAC9B,QAAA,eAAiB,GAAA,WAAA,GAAA,IAAA,GAAA,WAAA,GAAA,QAAA,CAAA,GAAA,CAAA,IAAA,EAAA,IAAA,CAAA,GAAA,QAAA,CAAA;AAAA,OACnB,MAAA;AAEA,QAAS,SAAA,CAAA,KAAA,GAAA,QACPA,UACAC,CACA,IAAA,EAAA,IAAA,CAAA,CAAA;AACA,OAAI;AACF,KAAM;AACN,IAAM,KAAA,CAAA,MAAA,KAAA,CAAA,iBAA2B,KAAA;AAEjC,MAAU,IAAA,CAAA,OAAA,IAAA,0BACa,EAAA;AAAwC,QAC1D,OAAA,CAAA,KAAA,CAAA,WAAA,CAAA,CAAA;AACL,QAAA,QAAA,CAAA,KAAkB,CAAA,CAAA;AAA6B,OACjD;AAAA,KACF,CAAA,CAAA;AAEA,IAAA,IAAA,CAAA,mBAAA,EAAA,CAAA,cAAA,EAAA,YAAA,CAAA,CAAA,CAAA;AAAA,IAAA,wBACc,EAAA,CAAA,gBAAA,EAAA,cAAA,CAAA,CAAA,CAAA;AAAA,IAAA,IACC,CAAA,mBAAA,EAAA,CAAA,gBAAA,EAAA,cAAA,CAAA,CAAA,CAAA;AACX,IAAA,IAAA,CAAA,mBAA2B,EAAA,CAAA,aAAiB,EAAA,WAAA,CAAA,CAAA,CAAA;AAC1C,IAAA,OAAA,CAAA,IAAA,EAAQ,MAAM,KAAW;AACzB,MAAA,OAAAC,SAAc,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,QAChB,KAAA,EAAAC,cAAA,CAAAC,KAAA,CAAA,QAAA,CAAA,CAAA;AAAA,OACF,EAAA;AAAA,QACFC,kBAAA,CAAA,KAAA,EAAA;AAEA,UAA0B,KAAA,EAAAF,cAAiB,CAAAC,KAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,cAAa,CAAA,CAAA;AACxD,SAAK,EAAqB;AAC1B,UAA0BE,UAAA,CAAA,IAAA,CAAA,MAAmB,EAAA,SAAA,EAAA;AAC7C,YAA0B,KAAA,EAAAH,cAAgB,CAAAC,KAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,SAAY,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}