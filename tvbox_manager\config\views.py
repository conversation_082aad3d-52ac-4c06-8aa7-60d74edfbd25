#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
配置管理视图
实现配置管理功能
"""

import json
import logging
from datetime import datetime
from flask import (
    Blueprint, render_template, request, jsonify, 
    current_app, flash, redirect, url_for, send_file
)
from flask_security import login_required, roles_required, current_user

from tvbox_manager.utils.decryptor import TVBoxDecryptor
from tvbox_manager.config.models import db, TVBoxConfig, SiteInfo, ParseInfo, LiveGroup, LiveChannel

# 创建蓝图
config_bp = Blueprint('config', __name__, url_prefix='/config')

# 获取日志记录器
logger = logging.getLogger('tvbox_manager.config')

# 初始化解密器
decryptor = TVBoxDecryptor()

@config_bp.route('/')
@login_required
def index():
    """配置管理首页"""
    # 获取所有配置
    configs = TVBoxConfig.query.all()
    
    return render_template(
        'tabler/config/index.html', 
        configs=configs,
        active_nav='config'
    )

@config_bp.route('/view/<int:id>')
@login_required
def view(id):
    """查看配置详情"""
    config = TVBoxConfig.query.get_or_404(id)
    
    try:
        # 解析JSON配置
        content = json.loads(config.content)
        
        # 获取配置统计信息
        config_info = decryptor.parse_tvbox_config_content(config.content)
        sites = config_info.get('sites', [])
        lives = config_info.get('lives', [])
        parses = config_info.get('parses', [])
        
        return render_template(
            'tabler/config/view.html',
            config=config,
            content=json.dumps(content, indent=2, ensure_ascii=False),
            sites=sites,
            lives=lives,
            parses=parses,
            active_nav='config'
        )
    except Exception as e:
        logger.error(f"查看配置失败: {str(e)}")
        flash(f'查看配置失败: {str(e)}', 'error')
        return redirect(url_for('config.index'))

@config_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    """添加配置"""
    if request.method == 'POST':
        name = request.form.get('name', '')
        content = request.form.get('content', '')
        is_active = request.form.get('is_active', 'off') == 'on'
        is_default = request.form.get('is_default', 'off') == 'on'
        
        if not name or not content:
            flash('名称和内容不能为空', 'error')
            return redirect(url_for('config.add'))
        
        try:
            # 验证JSON格式
            json_content = json.loads(content)
            content = json.dumps(json_content, ensure_ascii=False)
            
            # 创建配置
            config = TVBoxConfig(
                name=name,
                content=content,
                is_active=is_active,
                is_default=is_default
            )
            config.update_stats()
            
            # 如果是默认配置，将其他配置设为非默认
            if is_default:
                TVBoxConfig.query.filter(TVBoxConfig.id != config.id).update({'is_default': False})
            
            db.session.add(config)
            db.session.commit()
            
            flash('配置添加成功', 'success')
            return redirect(url_for('config.index'))
        except json.JSONDecodeError as e:
            flash(f'JSON格式错误: {str(e)}', 'error')
            return redirect(url_for('config.add'))
        except Exception as e:
            logger.error(f"添加配置失败: {str(e)}")
            flash(f'添加配置失败: {str(e)}', 'error')
            return redirect(url_for('config.add'))
    
    return render_template('tabler/config/add.html', active_nav='config')

@config_bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit(id):
    """编辑配置"""
    config = TVBoxConfig.query.get_or_404(id)
    
    if request.method == 'POST':
        name = request.form.get('name', '')
        content = request.form.get('content', '')
        is_active = request.form.get('is_active', 'off') == 'on'
        is_default = request.form.get('is_default', 'off') == 'on'
        
        if not name or not content:
            flash('名称和内容不能为空', 'error')
            return redirect(url_for('config.edit', id=id))
        
        try:
            # 验证JSON格式
            json_content = json.loads(content)
            content = json.dumps(json_content, ensure_ascii=False)
            
            # 更新配置
            config.name = name
            config.content = content
            config.is_active = is_active
            config.is_default = is_default
            config.update_stats()
            
            # 如果是默认配置，将其他配置设为非默认
            if is_default:
                TVBoxConfig.query.filter(TVBoxConfig.id != id).update({'is_default': False})
            
            db.session.commit()
            
            flash('配置更新成功', 'success')
            return redirect(url_for('config.index'))
        except json.JSONDecodeError as e:
            flash(f'JSON格式错误: {str(e)}', 'error')
            return render_template('tabler/config/edit.html', config=config, active_nav='config')
        except Exception as e:
            db.session.rollback()
            logger.error(f"更新配置失败: {str(e)}")
            flash(f'更新配置失败: {str(e)}', 'error')
            return render_template('tabler/config/edit.html', config=config, active_nav='config')
    
    return render_template(
        'tabler/config/edit.html',
        config=config,
        content=config.content,
        active_nav='config'
    )

@config_bp.route('/delete/<int:id>')
@login_required
@roles_required('admin')
def delete(id):
    """删除配置"""
    config = TVBoxConfig.query.get_or_404(id)
    
    try:
        # 删除配置
        db.session.delete(config)
        db.session.commit()
        
        flash('配置删除成功', 'success')
    except Exception as e:
        db.session.rollback()
        logger.error(f"删除配置失败: {str(e)}")
        flash(f'删除配置失败: {str(e)}', 'error')
    
    return redirect(url_for('config.index'))

@config_bp.route('/download/<int:id>')
@login_required
def download(id):
    """下载配置文件"""
    config = TVBoxConfig.query.get_or_404(id)
    
    try:
        import tempfile
        import os
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.json', mode='w', encoding='utf-8') as f:
            f.write(config.content)
            temp_path = f.name
        
        # 发送文件
        return send_file(
            temp_path,
            as_attachment=True,
            download_name=f"{config.name}.json",
            mimetype='application/json'
        )
    except Exception as e:
        logger.error(f"下载配置失败: {str(e)}")
        flash(f'下载配置失败: {str(e)}', 'error')
        return redirect(url_for('config.index'))

@config_bp.route('/set_default/<int:id>')
@login_required
def set_default(id):
    """设置默认配置"""
    config = TVBoxConfig.query.get_or_404(id)
    
    try:
        # 将其他配置设为非默认
        TVBoxConfig.query.filter(TVBoxConfig.id != id).update({'is_default': False})
        
        # 将当前配置设为默认
        config.is_default = True
        db.session.commit()
        
        flash('已设置为默认配置', 'success')
    except Exception as e:
        db.session.rollback()
        logger.error(f"设置默认配置失败: {str(e)}")
        flash(f'设置默认配置失败: {str(e)}', 'error')
    
    return redirect(url_for('config.index'))

@config_bp.route('/api/get/<int:id>')
def api_get(id):
    """获取配置API"""
    config = TVBoxConfig.query.get_or_404(id)
    
    try:
        # 解析JSON配置
        content = json.loads(config.content)
        return jsonify(content)
    except Exception as e:
        logger.error(f"获取配置失败: {str(e)}")
        return jsonify({'error': f'获取配置失败: {str(e)}'}), 500

@config_bp.route('/api/default')
def api_default():
    """获取默认配置API"""
    config = TVBoxConfig.query.filter_by(is_default=True, is_active=True).first()
    
    if not config:
        return jsonify({'error': '未设置默认配置'}), 404
    
    try:
        # 解析JSON配置
        content = json.loads(config.content)
        return jsonify(content)
    except Exception as e:
        logger.error(f"获取默认配置失败: {str(e)}")
        return jsonify({'error': f'获取默认配置失败: {str(e)}'}), 500 