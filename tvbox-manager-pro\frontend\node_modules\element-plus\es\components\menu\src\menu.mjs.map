{"version": 3, "file": "menu.mjs", "sources": ["../../../../../../packages/components/menu/src/menu.ts"], "sourcesContent": ["import {\n  computed,\n  defineComponent,\n  getCurrentInstance,\n  h,\n  nextTick,\n  onMounted,\n  provide,\n  reactive,\n  ref,\n  watch,\n  watchEffect,\n  withDirectives,\n} from 'vue'\nimport { useResizeObserver } from '@vueuse/core'\nimport { isNil } from 'lodash-unified'\nimport ElIcon from '@element-plus/components/icon'\nimport { More } from '@element-plus/icons-vue'\nimport {\n  buildProps,\n  definePropType,\n  flattedChildren,\n  iconPropType,\n  isArray,\n  isObject,\n  isString,\n  isUndefined,\n  mutable,\n} from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { ClickOutside as vClickoutside } from '@element-plus/directives'\nimport Menubar from './utils/menu-bar'\nimport ElMenuCollapseTransition from './menu-collapse-transition.vue'\nimport ElSubMenu from './sub-menu'\nimport { useMenuCssVar } from './use-menu-css-var'\nimport { MENU_INJECTION_KEY, SUB_MENU_INJECTION_KEY } from './tokens'\n\nimport type { PopperEffect } from '@element-plus/components/popper'\nimport type { MenuItemClicked, MenuProvider, SubMenuProvider } from './types'\nimport type { NavigationFailure, Router } from 'vue-router'\nimport type {\n  Component,\n  DirectiveArguments,\n  ExtractPropTypes,\n  VNode,\n  VNodeArrayChildren,\n  __ExtractPublicPropTypes,\n} from 'vue'\nimport type { UseResizeObserverReturn } from '@vueuse/core'\n\nexport const menuProps = buildProps({\n  /**\n   * @description menu display mode\n   */\n  mode: {\n    type: String,\n    values: ['horizontal', 'vertical'],\n    default: 'vertical',\n  },\n  /**\n   * @description index of active menu on page load\n   */\n  defaultActive: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description array that contains indexes of currently active sub-menus\n   */\n  defaultOpeneds: {\n    type: definePropType<string[]>(Array),\n    default: () => mutable([] as const),\n  },\n  /**\n   * @description whether only one sub-menu can be active\n   */\n  uniqueOpened: Boolean,\n  /**\n   * @description whether `vue-router` mode is activated. If true, index will be used as 'path' to activate the route action. Use with `default-active` to set the active item on load.\n   */\n  router: Boolean,\n  /**\n   * @description how sub-menus are triggered, only works when `mode` is 'horizontal'\n   */\n  menuTrigger: {\n    type: String,\n    values: ['hover', 'click'],\n    default: 'hover',\n  },\n  /**\n   * @description whether the menu is collapsed (available only in vertical mode)\n   */\n  collapse: Boolean,\n  /**\n   * @description background color of Menu (hex format) (deprecated, use `--bg-color` instead)\n   * @deprecated use `--bg-color` instead\n   */\n  backgroundColor: String,\n  /**\n   * @description text color of Menu (hex format) (deprecated, use `--text-color` instead)\n   * @deprecated use `--text-color` instead\n   */\n  textColor: String,\n  /**\n   * @description text color of currently active menu item (hex format) (deprecated, use `--active-color` instead)\n   * @deprecated use `--active-color` instead\n   */\n  activeTextColor: String,\n  /**\n   * @description optional, whether menu is collapsed when clicking outside\n   */\n  closeOnClickOutside: Boolean,\n  /**\n   * @description whether to enable the collapse transition\n   */\n  collapseTransition: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description whether the menu is ellipsis (available only in horizontal mode)\n   */\n  ellipsis: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description offset of the popper (effective for all submenus)\n   */\n  popperOffset: {\n    type: Number,\n    default: 6,\n  },\n  /**\n   * @description custom ellipsis icon (available only in horizontal mode and ellipsis is true)\n   */\n  ellipsisIcon: {\n    type: iconPropType,\n    default: () => More,\n  },\n  /**\n   * @description Tooltip theme, built-in theme: `dark` / `light` when menu is collapsed\n   */\n  popperEffect: {\n    type: definePropType<PopperEffect>(String),\n    default: 'dark',\n  },\n  /**\n   * @description custom class name for all popup menus\n   */\n  popperClass: String,\n  /**\n   * @description control timeout for all menus before showing\n   */\n  showTimeout: {\n    type: Number,\n    default: 300,\n  },\n  /**\n   * @description control timeout for all menus before hiding\n   */\n  hideTimeout: {\n    type: Number,\n    default: 300,\n  },\n  /**\n   * @description when menu inactive and `persistent` is `false` , dropdown menu will be destroyed\n   */\n  persistent: {\n    type: Boolean,\n    default: true,\n  },\n} as const)\nexport type MenuProps = ExtractPropTypes<typeof menuProps>\nexport type MenuPropsPublic = __ExtractPublicPropTypes<typeof menuProps>\n\nconst checkIndexPath = (indexPath: unknown): indexPath is string[] =>\n  isArray(indexPath) && indexPath.every((path) => isString(path))\n\nexport const menuEmits = {\n  close: (index: string, indexPath: string[]) =>\n    isString(index) && checkIndexPath(indexPath),\n\n  open: (index: string, indexPath: string[]) =>\n    isString(index) && checkIndexPath(indexPath),\n\n  select: (\n    index: string,\n    indexPath: string[],\n    item: MenuItemClicked,\n    routerResult?: Promise<void | NavigationFailure>\n  ) =>\n    isString(index) &&\n    checkIndexPath(indexPath) &&\n    isObject(item) &&\n    (isUndefined(routerResult) || routerResult instanceof Promise),\n}\nexport type MenuEmits = typeof menuEmits\n\nexport default defineComponent({\n  name: 'ElMenu',\n\n  props: menuProps,\n  emits: menuEmits,\n\n  setup(props, { emit, slots, expose }) {\n    const instance = getCurrentInstance()!\n    const router = instance.appContext.config.globalProperties.$router as Router\n    const menu = ref<HTMLUListElement>()\n    const nsMenu = useNamespace('menu')\n    const nsSubMenu = useNamespace('sub-menu')\n\n    // data\n    const sliceIndex = ref(-1)\n\n    const openedMenus = ref<MenuProvider['openedMenus']>(\n      props.defaultOpeneds && !props.collapse\n        ? props.defaultOpeneds.slice(0)\n        : []\n    )\n    const activeIndex = ref<MenuProvider['activeIndex']>(props.defaultActive)\n    const items = ref<MenuProvider['items']>({})\n    const subMenus = ref<MenuProvider['subMenus']>({})\n\n    // computed\n    const isMenuPopup = computed<MenuProvider['isMenuPopup']>(\n      () =>\n        props.mode === 'horizontal' ||\n        (props.mode === 'vertical' && props.collapse)\n    )\n\n    // methods\n    const initMenu = () => {\n      const activeItem = activeIndex.value && items.value[activeIndex.value]\n      if (!activeItem || props.mode === 'horizontal' || props.collapse) return\n\n      const indexPath = activeItem.indexPath\n\n      // 展开该菜单项的路径上所有子菜单\n      // expand all subMenus of the menu item\n      indexPath.forEach((index) => {\n        const subMenu = subMenus.value[index]\n        subMenu && openMenu(index, subMenu.indexPath)\n      })\n    }\n\n    const openMenu: MenuProvider['openMenu'] = (index, indexPath) => {\n      if (openedMenus.value.includes(index)) return\n      // 将不在该菜单路径下的其余菜单收起\n      // collapse all menu that are not under current menu item\n      if (props.uniqueOpened) {\n        openedMenus.value = openedMenus.value.filter((index: string) =>\n          indexPath.includes(index)\n        )\n      }\n      openedMenus.value.push(index)\n      emit('open', index, indexPath)\n    }\n\n    const close = (index: string) => {\n      const i = openedMenus.value.indexOf(index)\n      if (i !== -1) {\n        openedMenus.value.splice(i, 1)\n      }\n    }\n\n    const closeMenu: MenuProvider['closeMenu'] = (index, indexPath) => {\n      close(index)\n      emit('close', index, indexPath)\n    }\n\n    const handleSubMenuClick: MenuProvider['handleSubMenuClick'] = ({\n      index,\n      indexPath,\n    }) => {\n      const isOpened = openedMenus.value.includes(index)\n\n      isOpened ? closeMenu(index, indexPath) : openMenu(index, indexPath)\n    }\n\n    const handleMenuItemClick: MenuProvider['handleMenuItemClick'] = (\n      menuItem\n    ) => {\n      if (props.mode === 'horizontal' || props.collapse) {\n        openedMenus.value = []\n      }\n      const { index, indexPath } = menuItem\n      if (isNil(index) || isNil(indexPath)) return\n\n      if (props.router && router) {\n        const route = menuItem.route || index\n        const routerResult = router.push(route).then((res) => {\n          if (!res) activeIndex.value = index\n          return res\n        })\n        emit(\n          'select',\n          index,\n          indexPath,\n          { index, indexPath, route },\n          routerResult\n        )\n      } else {\n        activeIndex.value = index\n        emit('select', index, indexPath, { index, indexPath })\n      }\n    }\n\n    const updateActiveIndex = (val: string) => {\n      const itemsInData = items.value\n      const item =\n        itemsInData[val] ||\n        (activeIndex.value && itemsInData[activeIndex.value]) ||\n        itemsInData[props.defaultActive]\n\n      activeIndex.value = item?.index ?? val\n    }\n\n    const calcMenuItemWidth = (menuItem: HTMLElement) => {\n      const computedStyle = getComputedStyle(menuItem)\n      const marginLeft = Number.parseInt(computedStyle.marginLeft, 10)\n      const marginRight = Number.parseInt(computedStyle.marginRight, 10)\n      return menuItem.offsetWidth + marginLeft + marginRight || 0\n    }\n\n    const calcSliceIndex = () => {\n      if (!menu.value) return -1\n      const items = Array.from(menu.value?.childNodes ?? []).filter(\n        (item) => item.nodeName !== '#text' || item.nodeValue\n      ) as HTMLElement[]\n      const moreItemWidth = 64\n      const computedMenuStyle = getComputedStyle(menu.value!)\n      const paddingLeft = Number.parseInt(computedMenuStyle.paddingLeft, 10)\n      const paddingRight = Number.parseInt(computedMenuStyle.paddingRight, 10)\n      const menuWidth = menu.value!.clientWidth - paddingLeft - paddingRight\n      let calcWidth = 0\n      let sliceIndex = 0\n      items.forEach((item, index) => {\n        if (item.nodeName === '#comment') return\n        calcWidth += calcMenuItemWidth(item)\n        if (calcWidth <= menuWidth - moreItemWidth) {\n          sliceIndex = index + 1\n        }\n      })\n      return sliceIndex === items.length ? -1 : sliceIndex\n    }\n\n    const getIndexPath = (index: string) => subMenus.value[index].indexPath\n\n    // Common computer monitor FPS is 60Hz, which means 60 redraws per second. Calculation formula: 1000ms/60 ≈ 16.67ms, In order to avoid a certain chance of repeated triggering when `resize`, set wait to 16.67 * 2 = 33.34\n    const debounce = (fn: () => void, wait = 33.34) => {\n      let timmer: ReturnType<typeof setTimeout> | null\n      return () => {\n        timmer && clearTimeout(timmer)\n        timmer = setTimeout(() => {\n          fn()\n        }, wait)\n      }\n    }\n\n    let isFirstTimeRender = true\n    const handleResize = () => {\n      if (sliceIndex.value === calcSliceIndex()) return\n      const callback = () => {\n        sliceIndex.value = -1\n        nextTick(() => {\n          sliceIndex.value = calcSliceIndex()\n        })\n      }\n      // execute callback directly when first time resize to avoid shaking\n      isFirstTimeRender ? callback() : debounce(callback)()\n      isFirstTimeRender = false\n    }\n\n    watch(\n      () => props.defaultActive,\n      (currentActive) => {\n        if (!items.value[currentActive]) {\n          activeIndex.value = ''\n        }\n        updateActiveIndex(currentActive)\n      }\n    )\n\n    watch(\n      () => props.collapse,\n      (value) => {\n        if (value) openedMenus.value = []\n      }\n    )\n\n    watch(items.value, initMenu)\n\n    let resizeStopper: UseResizeObserverReturn['stop']\n    watchEffect(() => {\n      if (props.mode === 'horizontal' && props.ellipsis)\n        resizeStopper = useResizeObserver(menu, handleResize).stop\n      else resizeStopper?.()\n    })\n\n    const mouseInChild = ref(false)\n\n    // provide\n    {\n      const addSubMenu: MenuProvider['addSubMenu'] = (item) => {\n        subMenus.value[item.index] = item\n      }\n\n      const removeSubMenu: MenuProvider['removeSubMenu'] = (item) => {\n        delete subMenus.value[item.index]\n      }\n\n      const addMenuItem: MenuProvider['addMenuItem'] = (item) => {\n        items.value[item.index] = item\n      }\n\n      const removeMenuItem: MenuProvider['removeMenuItem'] = (item) => {\n        delete items.value[item.index]\n      }\n\n      provide<MenuProvider>(\n        MENU_INJECTION_KEY,\n        reactive({\n          props,\n          openedMenus,\n          items,\n          subMenus,\n          activeIndex,\n          isMenuPopup,\n\n          addMenuItem,\n          removeMenuItem,\n          addSubMenu,\n          removeSubMenu,\n          openMenu,\n          closeMenu,\n          handleMenuItemClick,\n          handleSubMenuClick,\n        })\n      )\n\n      provide<SubMenuProvider>(`${SUB_MENU_INJECTION_KEY}${instance.uid}`, {\n        addSubMenu,\n        removeSubMenu,\n        mouseInChild,\n        level: 0,\n      })\n    }\n\n    // lifecycle\n    onMounted(() => {\n      if (props.mode === 'horizontal') {\n        new Menubar(instance.vnode.el!, nsMenu.namespace.value)\n      }\n    })\n\n    {\n      const open = (index: string) => {\n        const { indexPath } = subMenus.value[index]\n        indexPath.forEach((i) => openMenu(i, indexPath))\n      }\n\n      expose({\n        open,\n        close,\n        updateActiveIndex,\n        handleResize,\n      })\n    }\n\n    const ulStyle = useMenuCssVar(props, 0)\n\n    return () => {\n      let slot: VNodeArrayChildren = slots.default?.() ?? []\n      const vShowMore: VNode[] = []\n\n      if (props.mode === 'horizontal' && menu.value) {\n        const originalSlot = flattedChildren(slot) as VNodeArrayChildren\n        const slotDefault =\n          sliceIndex.value === -1\n            ? originalSlot\n            : originalSlot.slice(0, sliceIndex.value)\n\n        const slotMore =\n          sliceIndex.value === -1 ? [] : originalSlot.slice(sliceIndex.value)\n\n        if (slotMore?.length && props.ellipsis) {\n          slot = slotDefault\n          vShowMore.push(\n            h(\n              ElSubMenu,\n              {\n                index: 'sub-menu-more',\n                class: nsSubMenu.e('hide-arrow'),\n                popperOffset: props.popperOffset,\n              },\n              {\n                title: () =>\n                  h(\n                    ElIcon,\n                    {\n                      class: nsSubMenu.e('icon-more'),\n                    },\n                    {\n                      default: () => h(props.ellipsisIcon as Component),\n                    }\n                  ),\n                default: () => slotMore,\n              }\n            )\n          )\n        }\n      }\n\n      const directives: DirectiveArguments = props.closeOnClickOutside\n        ? [\n            [\n              vClickoutside,\n              () => {\n                if (!openedMenus.value.length) return\n\n                if (!mouseInChild.value) {\n                  openedMenus.value.forEach((openedMenu) =>\n                    emit('close', openedMenu, getIndexPath(openedMenu))\n                  )\n\n                  openedMenus.value = []\n                }\n              },\n            ],\n          ]\n        : []\n\n      const vMenu = withDirectives(\n        h(\n          'ul',\n          {\n            key: String(props.collapse),\n            role: 'menubar',\n            ref: menu,\n            style: ulStyle.value,\n            class: {\n              [nsMenu.b()]: true,\n              [nsMenu.m(props.mode)]: true,\n              [nsMenu.m('collapse')]: props.collapse,\n            },\n          },\n          [...slot, ...vShowMore]\n        ),\n        directives\n      )\n\n      if (props.collapseTransition && props.mode === 'vertical') {\n        return h(ElMenuCollapseTransition, () => vMenu)\n      }\n\n      return vMenu\n    }\n  },\n})\n"], "names": ["Men<PERSON><PERSON>", "ElSubMenu", "vClickoutside"], "mappings": ";;;;;;;;;;;;;;;;;;;AAoCY,MAAC,SAAS,GAAG,UAAU,CAAC;AACpC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC;AACtC,IAAI,OAAO,EAAE,UAAU;AACvB,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,cAAc,EAAE;AAClB,IAAI,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC;AAC9B,GAAG;AACH,EAAE,YAAY,EAAE,OAAO;AACvB,EAAE,MAAM,EAAE,OAAO;AACjB,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;AAC9B,IAAI,OAAO,EAAE,OAAO;AACpB,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,eAAe,EAAE,MAAM;AACzB,EAAE,SAAS,EAAE,MAAM;AACnB,EAAE,eAAe,EAAE,MAAM;AACzB,EAAE,mBAAmB,EAAE,OAAO;AAC9B,EAAE,kBAAkB,EAAE;AACtB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,YAAY;AACtB,IAAI,OAAO,EAAE,MAAM,IAAI;AACvB,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,MAAM;AACnB,GAAG;AACH,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,CAAC,EAAE;AACH,MAAM,cAAc,GAAG,CAAC,SAAS,KAAK,OAAO,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;AAC1F,MAAC,SAAS,GAAG;AACzB,EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,SAAS,KAAK,QAAQ,CAAC,KAAK,CAAC,IAAI,cAAc,CAAC,SAAS,CAAC;AAC3E,EAAE,IAAI,EAAE,CAAC,KAAK,EAAE,SAAS,KAAK,QAAQ,CAAC,KAAK,CAAC,IAAI,cAAc,CAAC,SAAS,CAAC;AAC1E,EAAE,MAAM,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,YAAY,KAAK,QAAQ,CAAC,KAAK,CAAC,IAAI,cAAc,CAAC,SAAS,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,WAAW,CAAC,YAAY,CAAC,IAAI,YAAY,YAAY,OAAO,CAAC;AACpL,EAAE;AACF,WAAe,eAAe,CAAC;AAC/B,EAAE,IAAI,EAAE,QAAQ;AAChB,EAAE,KAAK,EAAE,SAAS;AAClB,EAAE,KAAK,EAAE,SAAS;AAClB,EAAE,KAAK,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;AACxC,IAAI,MAAM,QAAQ,GAAG,kBAAkB,EAAE,CAAC;AAC1C,IAAI,MAAM,MAAM,GAAG,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC;AACvE,IAAI,MAAM,IAAI,GAAG,GAAG,EAAE,CAAC;AACvB,IAAI,MAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;AACxC,IAAI,MAAM,SAAS,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;AAC/C,IAAI,MAAM,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/B,IAAI,MAAM,WAAW,GAAG,GAAG,CAAC,KAAK,CAAC,cAAc,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AAC1G,IAAI,MAAM,WAAW,GAAG,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;AACjD,IAAI,MAAM,KAAK,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;AAC1B,IAAI,MAAM,QAAQ,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;AAC7B,IAAI,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,YAAY,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC;AACnH,IAAI,MAAM,QAAQ,GAAG,MAAM;AAC3B,MAAM,MAAM,UAAU,GAAG,WAAW,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC7E,MAAM,IAAI,CAAC,UAAU,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,IAAI,KAAK,CAAC,QAAQ;AACtE,QAAQ,OAAO;AACf,MAAM,MAAM,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC;AAC7C,MAAM,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK;AACnC,QAAQ,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC9C,QAAQ,OAAO,IAAI,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;AACtD,OAAO,CAAC,CAAC;AACT,KAAK,CAAC;AACN,IAAI,MAAM,QAAQ,GAAG,CAAC,KAAK,EAAE,SAAS,KAAK;AAC3C,MAAM,IAAI,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC;AAC3C,QAAQ,OAAO;AACf,MAAM,IAAI,KAAK,CAAC,YAAY,EAAE;AAC9B,QAAQ,WAAW,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;AAC7F,OAAO;AACP,MAAM,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACpC,MAAM,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;AACrC,KAAK,CAAC;AACN,IAAI,MAAM,KAAK,GAAG,CAAC,KAAK,KAAK;AAC7B,MAAM,MAAM,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACjD,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;AACpB,QAAQ,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACvC,OAAO;AACP,KAAK,CAAC;AACN,IAAI,MAAM,SAAS,GAAG,CAAC,KAAK,EAAE,SAAS,KAAK;AAC5C,MAAM,KAAK,CAAC,KAAK,CAAC,CAAC;AACnB,MAAM,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;AACtC,KAAK,CAAC;AACN,IAAI,MAAM,kBAAkB,GAAG,CAAC;AAChC,MAAM,KAAK;AACX,MAAM,SAAS;AACf,KAAK,KAAK;AACV,MAAM,MAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACzD,MAAM,QAAQ,GAAG,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,GAAG,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AAC1E,KAAK,CAAC;AACN,IAAI,MAAM,mBAAmB,GAAG,CAAC,QAAQ,KAAK;AAC9C,MAAM,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,IAAI,KAAK,CAAC,QAAQ,EAAE;AACzD,QAAQ,WAAW,CAAC,KAAK,GAAG,EAAE,CAAC;AAC/B,OAAO;AACP,MAAM,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,QAAQ,CAAC;AAC5C,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC;AAC1C,QAAQ,OAAO;AACf,MAAM,IAAI,KAAK,CAAC,MAAM,IAAI,MAAM,EAAE;AAClC,QAAQ,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,IAAI,KAAK,CAAC;AAC9C,QAAQ,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK;AAC9D,UAAU,IAAI,CAAC,GAAG;AAClB,YAAY,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC;AACtC,UAAU,OAAO,GAAG,CAAC;AACrB,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,EAAE,YAAY,CAAC,CAAC;AACpF,OAAO,MAAM;AACb,QAAQ,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC;AAClC,QAAQ,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC;AAC/D,OAAO;AACP,KAAK,CAAC;AACN,IAAI,MAAM,iBAAiB,GAAG,CAAC,GAAG,KAAK;AACvC,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC;AACtC,MAAM,MAAM,IAAI,GAAG,WAAW,CAAC,GAAG,CAAC,IAAI,WAAW,CAAC,KAAK,IAAI,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,WAAW,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;AAC/H,MAAM,WAAW,CAAC,KAAK,GAAG,CAAC,EAAE,GAAG,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,KAAK,IAAI,GAAG,EAAE,GAAG,GAAG,CAAC;AACvF,KAAK,CAAC;AACN,IAAI,MAAM,iBAAiB,GAAG,CAAC,QAAQ,KAAK;AAC5C,MAAM,MAAM,aAAa,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC;AACvD,MAAM,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;AACvE,MAAM,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;AACzE,MAAM,OAAO,QAAQ,CAAC,WAAW,GAAG,UAAU,GAAG,WAAW,IAAI,CAAC,CAAC;AAClE,KAAK,CAAC;AACN,IAAI,MAAM,cAAc,GAAG,MAAM;AACjC,MAAM,IAAI,EAAE,EAAE,EAAE,CAAC;AACjB,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK;AACrB,QAAQ,OAAO,CAAC,CAAC,CAAC;AAClB,MAAM,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,UAAU,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,QAAQ,KAAK,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;AAC3K,MAAM,MAAM,aAAa,GAAG,EAAE,CAAC;AAC/B,MAAM,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC7D,MAAM,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;AAC7E,MAAM,MAAM,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;AAC/E,MAAM,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,WAAW,GAAG,YAAY,CAAC;AAC5E,MAAM,IAAI,SAAS,GAAG,CAAC,CAAC;AACxB,MAAM,IAAI,WAAW,GAAG,CAAC,CAAC;AAC1B,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK;AACtC,QAAQ,IAAI,IAAI,CAAC,QAAQ,KAAK,UAAU;AACxC,UAAU,OAAO;AACjB,QAAQ,SAAS,IAAI,iBAAiB,CAAC,IAAI,CAAC,CAAC;AAC7C,QAAQ,IAAI,SAAS,IAAI,SAAS,GAAG,aAAa,EAAE;AACpD,UAAU,WAAW,GAAG,KAAK,GAAG,CAAC,CAAC;AAClC,SAAS;AACT,OAAO,CAAC,CAAC;AACT,MAAM,OAAO,WAAW,KAAK,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC;AAC9D,KAAK,CAAC;AACN,IAAI,MAAM,YAAY,GAAG,CAAC,KAAK,KAAK,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC;AACpE,IAAI,MAAM,QAAQ,GAAG,CAAC,EAAE,EAAE,IAAI,GAAG,KAAK,KAAK;AAC3C,MAAM,IAAI,MAAM,CAAC;AACjB,MAAM,OAAO,MAAM;AACnB,QAAQ,MAAM,IAAI,YAAY,CAAC,MAAM,CAAC,CAAC;AACvC,QAAQ,MAAM,GAAG,UAAU,CAAC,MAAM;AAClC,UAAU,EAAE,EAAE,CAAC;AACf,SAAS,EAAE,IAAI,CAAC,CAAC;AACjB,OAAO,CAAC;AACR,KAAK,CAAC;AACN,IAAI,IAAI,iBAAiB,GAAG,IAAI,CAAC;AACjC,IAAI,MAAM,YAAY,GAAG,MAAM;AAC/B,MAAM,IAAI,UAAU,CAAC,KAAK,KAAK,cAAc,EAAE;AAC/C,QAAQ,OAAO;AACf,MAAM,MAAM,QAAQ,GAAG,MAAM;AAC7B,QAAQ,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AAC9B,QAAQ,QAAQ,CAAC,MAAM;AACvB,UAAU,UAAU,CAAC,KAAK,GAAG,cAAc,EAAE,CAAC;AAC9C,SAAS,CAAC,CAAC;AACX,OAAO,CAAC;AACR,MAAM,iBAAiB,GAAG,QAAQ,EAAE,GAAG,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;AAC5D,MAAM,iBAAiB,GAAG,KAAK,CAAC;AAChC,KAAK,CAAC;AACN,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,aAAa,EAAE,CAAC,aAAa,KAAK;AACxD,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE;AACvC,QAAQ,WAAW,CAAC,KAAK,GAAG,EAAE,CAAC;AAC/B,OAAO;AACP,MAAM,iBAAiB,CAAC,aAAa,CAAC,CAAC;AACvC,KAAK,CAAC,CAAC;AACP,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,QAAQ,EAAE,CAAC,KAAK,KAAK;AAC3C,MAAM,IAAI,KAAK;AACf,QAAQ,WAAW,CAAC,KAAK,GAAG,EAAE,CAAC;AAC/B,KAAK,CAAC,CAAC;AACP,IAAI,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AACjC,IAAI,IAAI,aAAa,CAAC;AACtB,IAAI,WAAW,CAAC,MAAM;AACtB,MAAM,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,IAAI,KAAK,CAAC,QAAQ;AACvD,QAAQ,aAAa,GAAG,iBAAiB,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC,IAAI,CAAC;AACnE;AACA,QAAQ,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,aAAa,EAAE,CAAC;AACzD,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,YAAY,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AACpC,IAAI;AACJ,MAAM,MAAM,UAAU,GAAG,CAAC,IAAI,KAAK;AACnC,QAAQ,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;AAC1C,OAAO,CAAC;AACR,MAAM,MAAM,aAAa,GAAG,CAAC,IAAI,KAAK;AACtC,QAAQ,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC1C,OAAO,CAAC;AACR,MAAM,MAAM,WAAW,GAAG,CAAC,IAAI,KAAK;AACpC,QAAQ,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;AACvC,OAAO,CAAC;AACR,MAAM,MAAM,cAAc,GAAG,CAAC,IAAI,KAAK;AACvC,QAAQ,OAAO,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACvC,OAAO,CAAC;AACR,MAAM,OAAO,CAAC,kBAAkB,EAAE,QAAQ,CAAC;AAC3C,QAAQ,KAAK;AACb,QAAQ,WAAW;AACnB,QAAQ,KAAK;AACb,QAAQ,QAAQ;AAChB,QAAQ,WAAW;AACnB,QAAQ,WAAW;AACnB,QAAQ,WAAW;AACnB,QAAQ,cAAc;AACtB,QAAQ,UAAU;AAClB,QAAQ,aAAa;AACrB,QAAQ,QAAQ;AAChB,QAAQ,SAAS;AACjB,QAAQ,mBAAmB;AAC3B,QAAQ,kBAAkB;AAC1B,OAAO,CAAC,CAAC,CAAC;AACV,MAAM,OAAO,CAAC,CAAC,EAAE,sBAAsB,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;AAC1D,QAAQ,UAAU;AAClB,QAAQ,aAAa;AACrB,QAAQ,YAAY;AACpB,QAAQ,KAAK,EAAE,CAAC;AAChB,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,SAAS,CAAC,MAAM;AACpB,MAAM,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE;AACvC,QAAQ,IAAIA,MAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,EAAE,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AAC/D,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI;AACJ,MAAM,MAAM,IAAI,GAAG,CAAC,KAAK,KAAK;AAC9B,QAAQ,MAAM,EAAE,SAAS,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACpD,QAAQ,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;AACzD,OAAO,CAAC;AACR,MAAM,MAAM,CAAC;AACb,QAAQ,IAAI;AACZ,QAAQ,KAAK;AACb,QAAQ,iBAAiB;AACzB,QAAQ,YAAY;AACpB,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,MAAM,OAAO,GAAG,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAC5C,IAAI,OAAO,MAAM;AACjB,MAAM,IAAI,EAAE,EAAE,EAAE,CAAC;AACjB,MAAM,IAAI,IAAI,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;AACjG,MAAM,MAAM,SAAS,GAAG,EAAE,CAAC;AAC3B,MAAM,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,IAAI,IAAI,CAAC,KAAK,EAAE;AACrD,QAAQ,MAAM,YAAY,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;AACnD,QAAQ,MAAM,WAAW,GAAG,UAAU,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC;AAC7G,QAAQ,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,EAAE,GAAG,YAAY,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AAC7F,QAAQ,IAAI,CAAC,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,MAAM,KAAK,KAAK,CAAC,QAAQ,EAAE;AAC7E,UAAU,IAAI,GAAG,WAAW,CAAC;AAC7B,UAAU,SAAS,CAAC,IAAI,CAAC,CAAC,CAACC,OAAS,EAAE;AACtC,YAAY,KAAK,EAAE,eAAe;AAClC,YAAY,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,YAAY,CAAC;AAC5C,YAAY,YAAY,EAAE,KAAK,CAAC,YAAY;AAC5C,WAAW,EAAE;AACb,YAAY,KAAK,EAAE,MAAM,CAAC,CAAC,MAAM,EAAE;AACnC,cAAc,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC;AAC7C,aAAa,EAAE;AACf,cAAc,OAAO,EAAE,MAAM,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC;AAClD,aAAa,CAAC;AACd,YAAY,OAAO,EAAE,MAAM,QAAQ;AACnC,WAAW,CAAC,CAAC,CAAC;AACd,SAAS;AACT,OAAO;AACP,MAAM,MAAM,UAAU,GAAG,KAAK,CAAC,mBAAmB,GAAG;AACrD,QAAQ;AACR,UAAUC,YAAa;AACvB,UAAU,MAAM;AAChB,YAAY,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM;AACzC,cAAc,OAAO;AACrB,YAAY,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;AACrC,cAAc,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,UAAU,KAAK,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC7G,cAAc,WAAW,CAAC,KAAK,GAAG,EAAE,CAAC;AACrC,aAAa;AACb,WAAW;AACX,SAAS;AACT,OAAO,GAAG,EAAE,CAAC;AACb,MAAM,MAAM,KAAK,GAAG,cAAc,CAAC,CAAC,CAAC,IAAI,EAAE;AAC3C,QAAQ,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;AACnC,QAAQ,IAAI,EAAE,SAAS;AACvB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,KAAK,EAAE,OAAO,CAAC,KAAK;AAC5B,QAAQ,KAAK,EAAE;AACf,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,IAAI;AAC5B,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI;AACtC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC,QAAQ;AAChD,SAAS;AACT,OAAO,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;AAC/C,MAAM,IAAI,KAAK,CAAC,kBAAkB,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE;AACjE,QAAQ,OAAO,CAAC,CAAC,wBAAwB,EAAE,MAAM,KAAK,CAAC,CAAC;AACxD,OAAO;AACP,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK,CAAC;AACN,GAAG;AACH,CAAC,CAAC;;;;"}