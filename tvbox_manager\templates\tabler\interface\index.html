{% extends "tabler/base.html" %}

{% set active_nav = 'interface' %}
{% block title %}接口管理 - TVBox Manager{% endblock %}
{% block page_title %}接口管理{% endblock %}

{% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <h2 class="page-title">
          接口管理
        </h2>
        <div class="text-muted mt-1">管理所有TVBox视频接口资源</div>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <a href="{{ url_for('interface.add') }}" class="btn btn-primary d-none d-sm-inline-block">
            <i class="ti ti-plus"></i>
            添加接口
          </a>
          <a href="{{ url_for('interface.add') }}" class="btn btn-primary d-sm-none btn-icon">
            <i class="ti ti-plus"></i>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    {% if sources %}
    <!-- 接口列表卡片 -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">接口列表</h3>
        <div class="card-actions">
          <a href="#" class="btn btn-outline-secondary btn-sm">
            <i class="ti ti-refresh"></i>
            刷新
          </a>
        </div>
      </div>
      <div class="card-body border-bottom py-3">
        <div class="d-flex">
          <div class="text-muted">
            共 <strong>{{ sources|length }}</strong> 个接口
          </div>
          <div class="ms-auto text-muted">
            搜索:
            <div class="ms-2 d-inline-block">
              <input type="text" class="form-control form-control-sm" id="search-input" aria-label="搜索接口">
            </div>
          </div>
        </div>
      </div>
      <div class="table-responsive">
        <table class="table card-table table-vcenter text-nowrap datatable">
          <thead>
            <tr>
              <th>名称</th>
              <th>URL</th>
              <th>类型</th>
              <th>解密方法</th>
              <th>状态</th>
              <th>更新时间</th>
              <th class="w-1"></th>
            </tr>
          </thead>
          <tbody>
            {% for source in sources %}
            <tr>
              <td>
                <span class="fw-bold">{{ source.name }}</span>
              </td>
              <td>
                <div class="text-truncate" style="max-width: 200px;" title="{{ source.url }}">
                  {{ source.url }}
                </div>
              </td>
              <td>{{ source.type }}</td>
              <td>{{ source.decrypt_method or '未知' }}</td>
              <td>
                {% if source.status %}
                <span class="badge bg-success-lt">
                  <i class="ti ti-check me-1"></i> 正常
                </span>
                {% else %}
                <span class="badge bg-secondary-lt">
                  <i class="ti ti-x me-1"></i> 停用
                </span>
                {% endif %}
              </td>
              <td>
                {% if source.last_update %}
                <span title="{{ source.last_update }}">{{ source.last_update.strftime('%Y-%m-%d %H:%M') }}</span>
                {% else %}
                <span class="text-muted">从未更新</span>
                {% endif %}
              </td>
              <td>
                <div class="btn-list flex-nowrap">
                  <a href="{{ url_for('interface.edit', id=source.id) }}" class="btn btn-icon btn-sm" title="编辑">
                    <i class="ti ti-edit text-primary"></i>
                  </a>
                  <a href="{{ url_for('interface.refresh', id=source.id) }}" class="btn btn-icon btn-sm" title="刷新">
                    <i class="ti ti-refresh text-info"></i>
                  </a>
                  <a href="{{ url_for('interface.delete', id=source.id) }}" class="btn btn-icon btn-sm" 
                     onclick="return confirm('确认删除此接口?')" title="删除">
                    <i class="ti ti-trash text-danger"></i>
                  </a>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
    {% else %}
    <!-- 空状态卡片 -->
    <div class="card">
      <div class="card-body empty">
        <div class="empty-img">
          <i class="ti ti-link" style="font-size: 3rem; opacity: 0.5;"></i>
        </div>
        <p class="empty-title">没有可用接口</p>
        <p class="empty-subtitle text-muted">
          您还没有添加任何接口。接口是获取视频源的重要渠道，添加后可获取丰富的视频资源。
        </p>
        <div class="empty-action">
          <a href="{{ url_for('interface.add') }}" class="btn btn-primary">
            <i class="ti ti-plus me-2"></i>
            添加第一个接口
          </a>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- 接口说明卡片 -->
    <div class="card mt-3">
      <div class="card-header">
        <h3 class="card-title">接口说明</h3>
      </div>
      <div class="card-body">
        <p>接口是TVBox获取视频资源的主要途径，通常包含以下类型：</p>
        
        <div class="row mt-3">
          <div class="col-md-4">
            <div class="card">
              <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                  <div class="avatar bg-primary-lt">
                    <i class="ti ti-world"></i>
                  </div>
                  <div class="ms-3">
                    <h4 class="m-0">直接接口</h4>
                  </div>
                </div>
                <div class="text-muted">无需解密可直接使用</div>
              </div>
            </div>
          </div>
          
          <div class="col-md-4">
            <div class="card">
              <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                  <div class="avatar bg-warning-lt">
                    <i class="ti ti-lock-open"></i>
                  </div>
                  <div class="ms-3">
                    <h4 class="m-0">加密接口</h4>
                  </div>
                </div>
                <div class="text-muted">需要先解密后使用</div>
              </div>
            </div>
          </div>
          
          <div class="col-md-4">
            <div class="card">
              <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                  <div class="avatar bg-info-lt">
                    <i class="ti ti-file"></i>
                  </div>
                  <div class="ms-3">
                    <h4 class="m-0">本地接口</h4>
                  </div>
                </div>
                <div class="text-muted">从本地文件加载</div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="row mt-3">
          <div class="col-md-6">
            <div class="card">
              <div class="card-body">
                <h3 class="card-title">接口状态说明</h3>
                <div class="mt-3">
                  <div class="d-flex align-items-center mb-2">
                    <span class="badge bg-success-lt me-2">
                      <i class="ti ti-check me-1"></i> 正常
                    </span>
                    <span class="text-muted">接口可正常访问和使用</span>
                  </div>
                  <div class="d-flex align-items-center">
                    <span class="badge bg-secondary-lt me-2">
                      <i class="ti ti-x me-1"></i> 停用
                    </span>
                    <span class="text-muted">接口暂时无法访问或已停用</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="col-md-6">
            <div class="card">
              <div class="card-body">
                <h3 class="card-title">接口使用提示</h3>
                <div class="mt-3">
                  <div class="alert alert-info mb-0">
                    <div class="d-flex">
                      <div>
                        <i class="ti ti-info-circle me-2"></i>
                      </div>
                      <div>
                        <h4>接口定期更新</h4>
                        <div class="text-muted">建议定期刷新接口，确保内容的及时更新。如遇接口失效，可尝试解密或更换接口源。</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
  const searchInput = document.getElementById('search-input');
  if(searchInput) {
    searchInput.addEventListener('keyup', function() {
      const searchValue = this.value.toLowerCase();
      const tableRows = document.querySelectorAll('.datatable tbody tr');
      
      tableRows.forEach(function(row) {
        const name = row.querySelector('td:first-child').textContent.toLowerCase();
        const url = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
        
        if(name.includes(searchValue) || url.includes(searchValue)) {
          row.style.display = '';
        } else {
          row.style.display = 'none';
        }
      });
    });
  }
});
</script>
{% endblock %} 

{% set active_nav = 'interface' %}
{% block title %}接口管理 - TVBox Manager{% endblock %}
{% block page_title %}接口管理{% endblock %}

{% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <h2 class="page-title">
          接口管理
        </h2>
        <div class="text-muted mt-1">管理所有TVBox视频接口资源</div>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <a href="{{ url_for('interface.add') }}" class="btn btn-primary d-none d-sm-inline-block">
            <i class="ti ti-plus"></i>
            添加接口
          </a>
          <a href="{{ url_for('interface.add') }}" class="btn btn-primary d-sm-none btn-icon">
            <i class="ti ti-plus"></i>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    {% if sources %}
    <!-- 接口列表卡片 -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">接口列表</h3>
        <div class="card-actions">
          <a href="#" class="btn btn-outline-secondary btn-sm">
            <i class="ti ti-refresh"></i>
            刷新
          </a>
        </div>
      </div>
      <div class="card-body border-bottom py-3">
        <div class="d-flex">
          <div class="text-muted">
            共 <strong>{{ sources|length }}</strong> 个接口
          </div>
          <div class="ms-auto text-muted">
            搜索:
            <div class="ms-2 d-inline-block">
              <input type="text" class="form-control form-control-sm" id="search-input" aria-label="搜索接口">
            </div>
          </div>
        </div>
      </div>
      <div class="table-responsive">
        <table class="table card-table table-vcenter text-nowrap datatable">
          <thead>
            <tr>
              <th>名称</th>
              <th>URL</th>
              <th>类型</th>
              <th>解密方法</th>
              <th>状态</th>
              <th>更新时间</th>
              <th class="w-1"></th>
            </tr>
          </thead>
          <tbody>
            {% for source in sources %}
            <tr>
              <td>
                <span class="fw-bold">{{ source.name }}</span>
              </td>
              <td>
                <div class="text-truncate" style="max-width: 200px;" title="{{ source.url }}">
                  {{ source.url }}
                </div>
              </td>
              <td>{{ source.type }}</td>
              <td>{{ source.decrypt_method or '未知' }}</td>
              <td>
                {% if source.status %}
                <span class="badge bg-success-lt">
                  <i class="ti ti-check me-1"></i> 正常
                </span>
                {% else %}
                <span class="badge bg-secondary-lt">
                  <i class="ti ti-x me-1"></i> 停用
                </span>
                {% endif %}
              </td>
              <td>
                {% if source.last_update %}
                <span title="{{ source.last_update }}">{{ source.last_update.strftime('%Y-%m-%d %H:%M') }}</span>
                {% else %}
                <span class="text-muted">从未更新</span>
                {% endif %}
              </td>
              <td>
                <div class="btn-list flex-nowrap">
                  <a href="{{ url_for('interface.edit', id=source.id) }}" class="btn btn-icon btn-sm" title="编辑">
                    <i class="ti ti-edit text-primary"></i>
                  </a>
                  <a href="{{ url_for('interface.refresh', id=source.id) }}" class="btn btn-icon btn-sm" title="刷新">
                    <i class="ti ti-refresh text-info"></i>
                  </a>
                  <a href="{{ url_for('interface.delete', id=source.id) }}" class="btn btn-icon btn-sm" 
                     onclick="return confirm('确认删除此接口?')" title="删除">
                    <i class="ti ti-trash text-danger"></i>
                  </a>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
    {% else %}
    <!-- 空状态卡片 -->
    <div class="card">
      <div class="card-body empty">
        <div class="empty-img">
          <i class="ti ti-link" style="font-size: 3rem; opacity: 0.5;"></i>
        </div>
        <p class="empty-title">没有可用接口</p>
        <p class="empty-subtitle text-muted">
          您还没有添加任何接口。接口是获取视频源的重要渠道，添加后可获取丰富的视频资源。
        </p>
        <div class="empty-action">
          <a href="{{ url_for('interface.add') }}" class="btn btn-primary">
            <i class="ti ti-plus me-2"></i>
            添加第一个接口
          </a>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- 接口说明卡片 -->
    <div class="card mt-3">
      <div class="card-header">
        <h3 class="card-title">接口说明</h3>
      </div>
      <div class="card-body">
        <p>接口是TVBox获取视频资源的主要途径，通常包含以下类型：</p>
        
        <div class="row mt-3">
          <div class="col-md-4">
            <div class="card">
              <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                  <div class="avatar bg-primary-lt">
                    <i class="ti ti-world"></i>
                  </div>
                  <div class="ms-3">
                    <h4 class="m-0">直接接口</h4>
                  </div>
                </div>
                <div class="text-muted">无需解密可直接使用</div>
              </div>
            </div>
          </div>
          
          <div class="col-md-4">
            <div class="card">
              <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                  <div class="avatar bg-warning-lt">
                    <i class="ti ti-lock-open"></i>
                  </div>
                  <div class="ms-3">
                    <h4 class="m-0">加密接口</h4>
                  </div>
                </div>
                <div class="text-muted">需要先解密后使用</div>
              </div>
            </div>
          </div>
          
          <div class="col-md-4">
            <div class="card">
              <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                  <div class="avatar bg-info-lt">
                    <i class="ti ti-file"></i>
                  </div>
                  <div class="ms-3">
                    <h4 class="m-0">本地接口</h4>
                  </div>
                </div>
                <div class="text-muted">从本地文件加载</div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="row mt-3">
          <div class="col-md-6">
            <div class="card">
              <div class="card-body">
                <h3 class="card-title">接口状态说明</h3>
                <div class="mt-3">
                  <div class="d-flex align-items-center mb-2">
                    <span class="badge bg-success-lt me-2">
                      <i class="ti ti-check me-1"></i> 正常
                    </span>
                    <span class="text-muted">接口可正常访问和使用</span>
                  </div>
                  <div class="d-flex align-items-center">
                    <span class="badge bg-secondary-lt me-2">
                      <i class="ti ti-x me-1"></i> 停用
                    </span>
                    <span class="text-muted">接口暂时无法访问或已停用</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="col-md-6">
            <div class="card">
              <div class="card-body">
                <h3 class="card-title">接口使用提示</h3>
                <div class="mt-3">
                  <div class="alert alert-info mb-0">
                    <div class="d-flex">
                      <div>
                        <i class="ti ti-info-circle me-2"></i>
                      </div>
                      <div>
                        <h4>接口定期更新</h4>
                        <div class="text-muted">建议定期刷新接口，确保内容的及时更新。如遇接口失效，可尝试解密或更换接口源。</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
  const searchInput = document.getElementById('search-input');
  if(searchInput) {
    searchInput.addEventListener('keyup', function() {
      const searchValue = this.value.toLowerCase();
      const tableRows = document.querySelectorAll('.datatable tbody tr');
      
      tableRows.forEach(function(row) {
        const name = row.querySelector('td:first-child').textContent.toLowerCase();
        const url = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
        
        if(name.includes(searchValue) || url.includes(searchValue)) {
          row.style.display = '';
        } else {
          row.style.display = 'none';
        }
      });
    });
  }
});
</script>
{% endblock %} 