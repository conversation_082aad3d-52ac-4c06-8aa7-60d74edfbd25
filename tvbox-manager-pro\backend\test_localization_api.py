#!/usr/bin/env python3
"""
测试本地化API
"""
import requests
import json

def test_localization_api():
    """测试本地化API"""
    base_url = "http://localhost:8000/api"
    
    # 首先登录获取token
    login_data = {
        "email": "<EMAIL>",
        "password": "admin123"
    }
    
    try:
        print("🔐 登录获取token...")
        login_response = requests.post(f"{base_url}/v1/auth/login", json=login_data)
        
        if login_response.status_code == 200:
            token_data = login_response.json()
            access_token = token_data["access_token"]
            print("✅ 登录成功")
            
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
            
            # 测试获取接口1的本地化状态
            print("\n📊 获取接口1的本地化状态...")
            status_response = requests.get(f"{base_url}/v1/interfaces/1/localization-status", headers=headers)
            
            if status_response.status_code == 200:
                status_data = status_response.json()
                print("✅ 获取状态成功")
                print(f"📋 接口ID: {status_data.get('interface_id')}")
                print(f"🔄 本地化开关: {status_data.get('enable_localization')}")
                print(f"📊 本地化状态: {status_data.get('localization_status')}")
                print(f"📈 本地化进度: {status_data.get('localization_progress')}%")
                print(f"📁 本地路径: {status_data.get('local_base_path')}")
                print(f"📅 完成时间: {status_data.get('last_localization_at')}")
                print(f"📄 总文件数: {status_data.get('total_files')}")
                print(f"✅ 完成文件数: {status_data.get('completed_files')}")
                print(f"❌ 错误文件数: {status_data.get('error_files')}")
                
                files = status_data.get('files', [])
                if files:
                    print(f"\n📄 文件列表 ({len(files)} 个):")
                    for file in files:
                        status_icon = "✅" if file['download_status'] == 'completed' else "❌"
                        print(f"   {status_icon} {file['file_type']}: {file.get('original_filename', 'N/A')}")
                        print(f"      状态: {file['download_status']}")
                        if file.get('file_size'):
                            print(f"      大小: {file['file_size']:,} bytes")
                        if file.get('error_message'):
                            print(f"      错误: {file['error_message']}")
                
            else:
                print(f"❌ 获取状态失败: {status_response.status_code}")
                print(f"   响应: {status_response.text}")
            
        else:
            print(f"❌ 登录失败: {login_response.status_code}")
            print(f"   响应: {login_response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    test_localization_api()
