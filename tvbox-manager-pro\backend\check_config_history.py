#!/usr/bin/env python3
"""
检查配置历史
"""
import sqlite3
import os
import json

def check_config_history():
    """检查配置历史"""
    db_path = "data/tvbox.db"
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查所有接口的配置
        cursor.execute("""
            SELECT id, name, url, 
                   LENGTH(config_content) as config_len,
                   SUBSTR(config_content, 1, 300) as config_preview
            FROM interface_sources 
            ORDER BY id
        """)
        
        results = cursor.fetchall()
        for result in results:
            interface_id, name, url, config_len, config_preview = result
            print(f"\n接口ID: {interface_id}")
            print(f"接口名称: {name}")
            print(f"接口URL: {url}")
            print(f"配置长度: {config_len}")
            
            if config_preview:
                try:
                    # 尝试解析配置
                    config_data = json.loads(config_preview + "}")  # 可能被截断，添加结束符
                    spider = config_data.get('spider', 'N/A')
                    print(f"Spider: {spider}")
                    
                    # 检查是否是真实配置（包含真实URL）
                    if spider and isinstance(spider, str):
                        if spider.startswith('http') and 'example.com' not in spider:
                            print("✅ 这看起来是真实配置")
                        elif spider.startswith('./'):
                            print("📁 这是本地化配置")
                        elif 'example.com' in spider:
                            print("🧪 这是测试配置")
                        else:
                            print("❓ 配置类型未知")
                except Exception as e:
                    print(f"配置解析失败: {e}")
            
            print("-" * 50)
            
    except Exception as e:
        print(f"检查失败: {str(e)}")
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    check_config_history()
