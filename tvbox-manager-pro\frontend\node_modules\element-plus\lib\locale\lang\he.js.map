{"version": 3, "file": "he.js", "sources": ["../../../../../packages/locale/lang/he.ts"], "sourcesContent": ["export default {\n  name: 'he',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'אישור',\n      clear: 'נקה',\n    },\n    datepicker: {\n      now: 'כעת',\n      today: 'היום',\n      cancel: 'בטל',\n      clear: 'נקה',\n      confirm: 'אישור',\n      selectDate: 'בחר תאריך',\n      selectTime: 'בחר זמן',\n      startDate: 'תאריך התחלה',\n      startTime: 'זמן התחלה',\n      endDate: 'תאריך סיום',\n      endTime: 'זמן סיום',\n      prevYear: 'שנה קודמת',\n      nextYear: 'שנה הבאה',\n      prevMonth: 'חודש קודם',\n      nextMonth: 'חודש הבא',\n      year: 'שנה',\n      month1: 'ינואר',\n      month2: 'פברואר',\n      month3: 'מרץ',\n      month4: 'אפריל',\n      month5: 'מאי',\n      month6: 'יוני',\n      month7: 'יולי',\n      month8: 'אוגוסט',\n      month9: 'ספטמבר',\n      month10: 'אוקטובר',\n      month11: 'נובמבר',\n      month12: 'דצמבר',\n      week: 'שבוע',\n      weeks: {\n        sun: 'א׳',\n        mon: 'ב׳',\n        tue: 'ג׳',\n        wed: 'ד׳',\n        thu: 'ה׳',\n        fri: 'ו׳',\n        sat: 'שבת',\n      },\n      months: {\n        jan: 'ינואר',\n        feb: 'פברואר',\n        mar: 'מרץ',\n        apr: 'אפריל',\n        may: 'מאי',\n        jun: 'יוני',\n        jul: 'יולי',\n        aug: 'אוגוסט',\n        sep: 'ספטמבר',\n        oct: 'אוקטובר',\n        nov: 'נובמבר',\n        dec: 'דצמבר',\n      },\n    },\n    select: {\n      loading: 'טוען',\n      noMatch: 'לא נמצאה התאמה',\n      noData: 'אין נתונים',\n      placeholder: 'שומר מקום',\n    },\n    mention: {\n      loading: 'טוען',\n    },\n    cascader: {\n      noMatch: 'לא נמצאה התאמה',\n      loading: 'טוען',\n      placeholder: 'שומר מקום',\n      noData: 'אין נתונים',\n    },\n    pagination: {\n      goto: 'עבור ל',\n      pagesize: '/עמוד',\n      total: 'כולל {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'הודעה',\n      confirm: 'אישור',\n      cancel: 'בטל',\n      error: 'קלט לא תקין',\n    },\n    upload: {\n      deleteTip: 'לחץ כדי למחוק',\n      delete: 'מחק',\n      preview: 'תצוגה מקדימה',\n      continue: 'המשך',\n    },\n    table: {\n      emptyText: 'אין נתונים',\n      confirmFilter: 'אישור',\n      resetFilter: 'נקה',\n      clearFilter: 'הכל',\n      sumText: 'סך הכל',\n    },\n    tree: {\n      emptyText: 'אין נתונים',\n    },\n    transfer: {\n      noMatch: 'לא נמצאה התאמה',\n      noData: 'אין נתונים',\n      titles: ['רשימה 1', 'רשימה 2'],\n      filterPlaceholder: 'סנן לפי...',\n      noCheckedFormat: 'פריטים {total}',\n      hasCheckedFormat: ' נבחרו {checked}/{total}',\n    },\n    image: {\n      error: 'שגיאה',\n    },\n    pageHeader: {\n      title: 'חזרה',\n    },\n    popconfirm: {\n      confirmButtonText: 'כן',\n      cancelButtonText: 'לא',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,KAAK,EAAE,oBAAoB;AACjC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,oBAAoB;AAC/B,MAAM,KAAK,EAAE,0BAA0B;AACvC,MAAM,MAAM,EAAE,oBAAoB;AAClC,MAAM,KAAK,EAAE,oBAAoB;AACjC,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,UAAU,EAAE,mDAAmD;AACrE,MAAM,UAAU,EAAE,uCAAuC;AACzD,MAAM,SAAS,EAAE,+DAA+D;AAChF,MAAM,SAAS,EAAE,mDAAmD;AACpE,MAAM,OAAO,EAAE,yDAAyD;AACxE,MAAM,OAAO,EAAE,6CAA6C;AAC5D,MAAM,QAAQ,EAAE,mDAAmD;AACnE,MAAM,QAAQ,EAAE,6CAA6C;AAC7D,MAAM,SAAS,EAAE,mDAAmD;AACpE,MAAM,SAAS,EAAE,6CAA6C;AAC9D,MAAM,IAAI,EAAE,oBAAoB;AAChC,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,oBAAoB;AAClC,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,oBAAoB;AAClC,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,OAAO,EAAE,sCAAsC;AACrD,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,IAAI,EAAE,0BAA0B;AACtC,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,oBAAoB;AACjC,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,4CAA4C;AACzD,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,0BAA0B;AACzC,MAAM,OAAO,EAAE,4EAA4E;AAC3F,MAAM,MAAM,EAAE,yDAAyD;AACvE,MAAM,WAAW,EAAE,mDAAmD;AACtE,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,0BAA0B;AACzC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,4EAA4E;AAC3F,MAAM,OAAO,EAAE,0BAA0B;AACzC,MAAM,WAAW,EAAE,mDAAmD;AACtE,MAAM,MAAM,EAAE,yDAAyD;AACvE,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,iCAAiC;AAC7C,MAAM,QAAQ,EAAE,2BAA2B;AAC3C,MAAM,KAAK,EAAE,kCAAkC;AAC/C,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,gCAAgC;AAC7C,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,MAAM,EAAE,oBAAoB;AAClC,MAAM,KAAK,EAAE,0DAA0D;AACvE,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,sEAAsE;AACvF,MAAM,MAAM,EAAE,oBAAoB;AAClC,MAAM,OAAO,EAAE,qEAAqE;AACpF,MAAM,QAAQ,EAAE,0BAA0B;AAC1C,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,yDAAyD;AAC1E,MAAM,aAAa,EAAE,gCAAgC;AACrD,MAAM,WAAW,EAAE,oBAAoB;AACvC,MAAM,WAAW,EAAE,oBAAoB;AACvC,MAAM,OAAO,EAAE,iCAAiC;AAChD,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,yDAAyD;AAC1E,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,4EAA4E;AAC3F,MAAM,MAAM,EAAE,yDAAyD;AACvE,MAAM,MAAM,EAAE,CAAC,kCAAkC,EAAE,kCAAkC,CAAC;AACtF,MAAM,iBAAiB,EAAE,0CAA0C;AACnE,MAAM,eAAe,EAAE,8CAA8C;AACrE,MAAM,gBAAgB,EAAE,mDAAmD;AAC3E,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,gCAAgC;AAC7C,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,0BAA0B;AACvC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,cAAc;AACvC,MAAM,gBAAgB,EAAE,cAAc;AACtC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}