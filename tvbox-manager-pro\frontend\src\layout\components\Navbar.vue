<template>
  <div class="navbar">
    <!-- 左侧区域 -->
    <div class="navbar-left">
      <!-- 折叠按钮 -->
      <div class="hamburger-container" @click="toggleSidebar">
        <el-icon size="20">
          <Expand v-if="appStore.isCollapsed" />
          <Fold v-else />
        </el-icon>
      </div>
      
      <!-- 面包屑导航 -->
      <Breadcrumb class="breadcrumb-container" />
    </div>
    
    <!-- 右侧区域 -->
    <div class="navbar-right">
      <!-- 搜索 -->
      <div class="search-container">
        <el-tooltip content="搜索" placement="bottom">
          <el-icon size="18" @click="showSearch">
            <Search />
          </el-icon>
        </el-tooltip>
      </div>
      
      <!-- 全屏 -->
      <div class="fullscreen-container">
        <el-tooltip :content="isFullscreen ? '退出全屏' : '全屏'" placement="bottom">
          <el-icon size="18" @click="toggleFullscreen">
            <FullScreen v-if="!isFullscreen" />
            <Aim v-else />
          </el-icon>
        </el-tooltip>
      </div>
      
      <!-- 主题切换 -->
      <div class="theme-container">
        <el-tooltip :content="appStore.isDark ? '切换到亮色模式' : '切换到暗色模式'" placement="bottom">
          <el-icon size="18" @click="appStore.toggleTheme">
            <Sunny v-if="appStore.isDark" />
            <Moon v-else />
          </el-icon>
        </el-tooltip>
      </div>
      
      <!-- 通知 -->
      <div class="notification-container">
        <el-badge :value="notificationCount" :hidden="notificationCount === 0">
          <el-tooltip content="通知" placement="bottom">
            <el-icon size="18" @click="showNotifications">
              <Bell />
            </el-icon>
          </el-tooltip>
        </el-badge>
      </div>
      
      <!-- 用户头像和菜单 -->
      <el-dropdown class="avatar-container" trigger="click" @command="handleCommand">
        <div class="avatar-wrapper">
          <el-avatar
            :size="32"
            :src="userStore.userAvatar"
            :icon="UserFilled"
          />
          <span class="username">{{ userStore.userName }}</span>
          <el-icon class="dropdown-icon">
            <ArrowDown />
          </el-icon>
        </div>
        
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">
              <el-icon><User /></el-icon>
              个人中心
            </el-dropdown-item>
            <el-dropdown-item command="settings">
              <el-icon><Setting /></el-icon>
              系统设置
            </el-dropdown-item>
            <el-dropdown-item divided command="logout">
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import { ElMessageBox } from 'element-plus'
import {
  Expand,
  Fold,
  Search,
  FullScreen,
  Aim,
  Sunny,
  Moon,
  Bell,
  UserFilled,
  User,
  Setting,
  SwitchButton,
  ArrowDown
} from '@element-plus/icons-vue'
import Breadcrumb from './Breadcrumb.vue'

const router = useRouter()
const appStore = useAppStore()
const userStore = useUserStore()

// 响应式数据
const isFullscreen = ref(false)
const notificationCount = ref(3) // 示例通知数量

// 计算属性
const device = computed(() => appStore.device)

// 方法
const toggleSidebar = () => {
  appStore.toggleSidebar()
}

const showSearch = () => {
  // 显示搜索对话框
  console.log('显示搜索')
}

const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isFullscreen.value = true
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen()
      isFullscreen.value = false
    }
  }
}

const showNotifications = () => {
  // 显示通知列表
  console.log('显示通知')
}

const handleCommand = (command) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'logout':
      handleLogout()
      break
  }
}

const handleLogout = () => {
  ElMessageBox.confirm(
    '确定要退出登录吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    userStore.logout().then(() => {
      router.push('/login')
    })
  }).catch(() => {
    // 取消退出
  })
}

// 监听全屏状态变化
document.addEventListener('fullscreenchange', () => {
  isFullscreen.value = !!document.fullscreenElement
})
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  background: var(--el-bg-color);
}

.navbar-left {
  display: flex;
  align-items: center;
  
  .hamburger-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.3s;
    
    &:hover {
      background-color: var(--el-color-primary-light-9);
    }
  }
  
  .breadcrumb-container {
    margin-left: 16px;
  }
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: 16px;
  
  .search-container,
  .fullscreen-container,
  .theme-container,
  .notification-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.3s;
    
    &:hover {
      background-color: var(--el-color-primary-light-9);
    }
  }
  
  .avatar-container {
    cursor: pointer;
    
    .avatar-wrapper {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 4px 8px;
      border-radius: 4px;
      transition: background-color 0.3s;
      
      &:hover {
        background-color: var(--el-color-primary-light-9);
      }
      
      .username {
        font-size: 14px;
        color: var(--el-text-color-primary);
        white-space: nowrap;
      }
      
      .dropdown-icon {
        font-size: 12px;
        color: var(--el-text-color-regular);
        transition: transform 0.3s;
      }
    }
    
    &:hover .dropdown-icon {
      transform: rotate(180deg);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .navbar {
    padding: 0 15px;
  }
  
  .navbar-right {
    gap: 12px;
    
    .username {
      display: none;
    }
  }
}

@media (max-width: 480px) {
  .navbar {
    padding: 0 10px;
  }
  
  .navbar-right {
    gap: 8px;
    
    .search-container,
    .fullscreen-container {
      display: none;
    }
  }
}
</style>
