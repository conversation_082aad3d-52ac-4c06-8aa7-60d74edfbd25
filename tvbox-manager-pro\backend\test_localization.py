import asyncio
import os
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.new_localization_service import NewLocalizationService
from app.database import get_db

async def test_localization():
    print("开始测试本地化服务...")

    # 创建服务实例
    service = NewLocalizationService()

    # 获取数据库会话
    db = next(get_db())

    # 接口ID
    interface_id = 1

    print(f"接口ID: {interface_id}")

    try:
        # 测试本地化处理
        print("\n开始本地化处理...")
        result = await service.localize_interface(db, interface_id)
        
        print(f"\n本地化结果:")
        print(f"成功: {result.get('success', False)}")
        print(f"消息: {result.get('message', 'N/A')}")
        print(f"文件数量: {result.get('file_count', 0)}")
        
        if result.get('localized_config'):
            print(f"\n本地化配置:")
            print(json.dumps(result['localized_config'], ensure_ascii=False, indent=2))
            
        # 检查本地化目录
        localized_dir = Path("data/localized")
        if localized_dir.exists():
            print(f"\n本地化目录内容:")
            for item in localized_dir.rglob("*"):
                if item.is_file():
                    print(f"  文件: {item}")
                elif item.is_dir():
                    print(f"  目录: {item}")
        else:
            print(f"\n本地化目录不存在: {localized_dir}")
            
    except Exception as e:
        print(f"本地化失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_localization())
