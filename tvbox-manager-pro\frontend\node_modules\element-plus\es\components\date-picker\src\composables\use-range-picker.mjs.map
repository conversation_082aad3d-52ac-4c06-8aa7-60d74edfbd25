{"version": 3, "file": "use-range-picker.mjs", "sources": ["../../../../../../../packages/components/date-picker/src/composables/use-range-picker.ts"], "sourcesContent": ["import { getCurrentInstance, inject, ref, unref, watch } from 'vue'\nimport dayjs from 'dayjs'\nimport { isArray } from '@element-plus/utils'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { getDefaultValue, isValidRange } from '../utils'\nimport { ROOT_PICKER_INJECTION_KEY } from '../constants'\nimport { useShortcut } from './use-shortcut'\n\nimport type { Ref } from 'vue'\nimport type { Dayjs } from 'dayjs'\nimport type { PanelRangeSharedProps, RangeState } from '../props/shared'\nimport type { DefaultValue } from '../utils'\n\ntype UseRangePickerProps = {\n  onParsedValueChanged: (\n    minDate: Dayjs | undefined,\n    maxDate: Dayjs | undefined\n  ) => void\n  defaultValue: Ref<DefaultValue>\n  defaultTime?: Ref<DefaultValue>\n  leftDate: Ref<Dayjs>\n  rightDate: Ref<Dayjs>\n  step?: number\n  unit: 'month' | 'year'\n}\n\nexport const useRangePicker = (\n  props: PanelRangeSharedProps,\n  {\n    defaultValue,\n    defaultTime,\n    leftDate,\n    rightDate,\n    step,\n    unit,\n\n    onParsedValueChanged,\n  }: UseRangePickerProps\n) => {\n  const { emit } = getCurrentInstance()!\n\n  const { pickerNs } = inject(ROOT_PICKER_INJECTION_KEY)!\n  const drpNs = useNamespace('date-range-picker')\n  const { t, lang } = useLocale()\n  const handleShortcutClick = useShortcut(lang)\n  const minDate = ref<Dayjs>()\n  const maxDate = ref<Dayjs>()\n  const rangeState = ref<RangeState>({\n    endDate: null,\n    selecting: false,\n  })\n\n  const handleChangeRange = (val: RangeState) => {\n    rangeState.value = val\n  }\n\n  const handleRangeConfirm = (visible = false) => {\n    const _minDate = unref(minDate)\n    const _maxDate = unref(maxDate)\n\n    if (isValidRange([_minDate, _maxDate])) {\n      emit('pick', [_minDate, _maxDate], visible)\n    }\n  }\n\n  const onSelect = (selecting: boolean) => {\n    rangeState.value.selecting = selecting\n    if (!selecting) {\n      rangeState.value.endDate = null\n    }\n  }\n\n  const onReset = (parsedValue: PanelRangeSharedProps['parsedValue']) => {\n    if (isArray(parsedValue) && parsedValue.length === 2) {\n      const [start, end] = parsedValue\n      minDate.value = start\n      leftDate.value = start\n      maxDate.value = end\n      onParsedValueChanged(unref(minDate), unref(maxDate))\n    } else {\n      restoreDefault()\n    }\n  }\n\n  const restoreDefault = () => {\n    let [start, end] = getDefaultValue(unref(defaultValue), {\n      lang: unref(lang),\n      step,\n      unit,\n      unlinkPanels: props.unlinkPanels,\n    })\n    const getShift = (day: Dayjs) => {\n      return day.diff(day.startOf('d'), 'ms')\n    }\n    const maybeTimes = unref(defaultTime)\n    if (maybeTimes) {\n      let leftShift = 0\n      let rightShift = 0\n      if (isArray(maybeTimes)) {\n        const [timeStart, timeEnd] = maybeTimes.map(dayjs)\n        leftShift = getShift(timeStart)\n        rightShift = getShift(timeEnd)\n      } else {\n        const shift = getShift(dayjs(maybeTimes))\n        leftShift = shift\n        rightShift = shift\n      }\n      start = start.startOf('d').add(leftShift, 'ms')\n      end = end.startOf('d').add(rightShift, 'ms')\n    }\n\n    minDate.value = undefined\n    maxDate.value = undefined\n    leftDate.value = start\n    rightDate.value = end\n  }\n\n  watch(\n    defaultValue,\n    (val) => {\n      if (val) {\n        restoreDefault()\n      }\n    },\n    { immediate: true }\n  )\n\n  watch(() => props.parsedValue, onReset, { immediate: true })\n\n  return {\n    minDate,\n    maxDate,\n    rangeState,\n    lang,\n    ppNs: pickerNs,\n    drpNs,\n\n    handleChangeRange,\n    handleRangeConfirm,\n    handleShortcutClick,\n    onSelect,\n    onReset,\n    t,\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAOY,MAAC,cAAc,GAAG,CAAC,KAAK,EAAE;AACtC,EAAE,YAAY;AACd,EAAE,WAAW;AACb,EAAE,QAAQ;AACV,EAAE,SAAS;AACX,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,oBAAoB;AACtB,CAAC,KAAK;AACN,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,kBAAkB,EAAE,CAAC;AACxC,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC,yBAAyB,CAAC,CAAC;AACzD,EAAE,MAAM,KAAK,GAAG,YAAY,CAAC,mBAAmB,CAAC,CAAC;AAClD,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,GAAG,SAAS,EAAE,CAAC;AAClC,EAAE,MAAM,mBAAmB,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;AAChD,EAAE,MAAM,OAAO,GAAG,GAAG,EAAE,CAAC;AACxB,EAAE,MAAM,OAAO,GAAG,GAAG,EAAE,CAAC;AACxB,EAAE,MAAM,UAAU,GAAG,GAAG,CAAC;AACzB,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,SAAS,EAAE,KAAK;AACpB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,iBAAiB,GAAG,CAAC,GAAG,KAAK;AACrC,IAAI,UAAU,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3B,GAAG,CAAC;AACJ,EAAE,MAAM,kBAAkB,GAAG,CAAC,OAAO,GAAG,KAAK,KAAK;AAClD,IAAI,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;AACpC,IAAI,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;AACpC,IAAI,IAAI,YAAY,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,EAAE;AAC5C,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,OAAO,CAAC,CAAC;AAClD,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,QAAQ,GAAG,CAAC,SAAS,KAAK;AAClC,IAAI,UAAU,CAAC,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;AAC3C,IAAI,IAAI,CAAC,SAAS,EAAE;AACpB,MAAM,UAAU,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC;AACtC,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,OAAO,GAAG,CAAC,WAAW,KAAK;AACnC,IAAI,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;AAC1D,MAAM,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,WAAW,CAAC;AACvC,MAAM,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;AAC5B,MAAM,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;AAC7B,MAAM,OAAO,CAAC,KAAK,GAAG,GAAG,CAAC;AAC1B,MAAM,oBAAoB,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;AAC3D,KAAK,MAAM;AACX,MAAM,cAAc,EAAE,CAAC;AACvB,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,MAAM;AAC/B,IAAI,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;AAC5D,MAAM,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC;AACvB,MAAM,IAAI;AACV,MAAM,IAAI;AACV,MAAM,YAAY,EAAE,KAAK,CAAC,YAAY;AACtC,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,QAAQ,GAAG,CAAC,GAAG,KAAK;AAC9B,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;AAC9C,KAAK,CAAC;AACN,IAAI,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC;AAC1C,IAAI,IAAI,UAAU,EAAE;AACpB,MAAM,IAAI,SAAS,GAAG,CAAC,CAAC;AACxB,MAAM,IAAI,UAAU,GAAG,CAAC,CAAC;AACzB,MAAM,IAAI,OAAO,CAAC,UAAU,CAAC,EAAE;AAC/B,QAAQ,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC3D,QAAQ,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;AACxC,QAAQ,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;AACvC,OAAO,MAAM;AACb,QAAQ,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;AAClD,QAAQ,SAAS,GAAG,KAAK,CAAC;AAC1B,QAAQ,UAAU,GAAG,KAAK,CAAC;AAC3B,OAAO;AACP,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;AACtD,MAAM,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;AACnD,KAAK;AACL,IAAI,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;AAC3B,IAAI,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;AAC3B,IAAI,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;AAC3B,IAAI,SAAS,CAAC,KAAK,GAAG,GAAG,CAAC;AAC1B,GAAG,CAAC;AACJ,EAAE,KAAK,CAAC,YAAY,EAAE,CAAC,GAAG,KAAK;AAC/B,IAAI,IAAI,GAAG,EAAE;AACb,MAAM,cAAc,EAAE,CAAC;AACvB,KAAK;AACL,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;AAC1B,EAAE,KAAK,CAAC,MAAM,KAAK,CAAC,WAAW,EAAE,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;AAC/D,EAAE,OAAO;AACT,IAAI,OAAO;AACX,IAAI,OAAO;AACX,IAAI,UAAU;AACd,IAAI,IAAI;AACR,IAAI,IAAI,EAAE,QAAQ;AAClB,IAAI,KAAK;AACT,IAAI,iBAAiB;AACrB,IAAI,kBAAkB;AACtB,IAAI,mBAAmB;AACvB,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,CAAC;AACL,GAAG,CAAC;AACJ;;;;"}