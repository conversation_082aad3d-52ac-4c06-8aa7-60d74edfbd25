{"version": 3, "file": "watcher.mjs", "sources": ["../../../../../../../packages/components/table/src/store/watcher.ts"], "sourcesContent": ["import { computed, getCurrentInstance, ref, toRefs, unref, watch } from 'vue'\nimport { ensureArray, hasOwn, isArray, isString } from '@element-plus/utils'\nimport {\n  getColumnById,\n  getColumnByKey,\n  getKeysMap,\n  getRowIdentity,\n  orderBy,\n  toggleRowStatus,\n} from '../util'\nimport useExpand from './expand'\nimport useCurrent from './current'\nimport useTree from './tree'\n\nimport type { Ref } from 'vue'\nimport type { TableColumnCtx } from '../table-column/defaults'\nimport type {\n  DefaultRow,\n  Table,\n  TableRefs,\n  TableSortOrder,\n} from '../table/defaults'\nimport type { StoreFilter } from '.'\n\nconst sortData = <T extends DefaultRow>(\n  data: T[],\n  states: {\n    sortingColumn: TableColumnCtx<T> | null\n    sortProp: string | null\n    sortOrder: string | number | null\n  }\n) => {\n  const sortingColumn = states.sortingColumn\n  if (!sortingColumn || isString(sortingColumn.sortable)) {\n    return data\n  }\n  return orderBy(\n    data,\n    states.sortProp,\n    states.sortOrder,\n    sortingColumn.sortMethod,\n    sortingColumn.sortBy\n  )\n}\n\nconst doFlattenColumns = <T extends DefaultRow>(\n  columns: TableColumnCtx<T>[]\n) => {\n  const result: TableColumnCtx<T>[] = []\n  columns.forEach((column) => {\n    if (column.children && column.children.length > 0) {\n      // eslint-disable-next-line prefer-spread\n      result.push.apply(result, doFlattenColumns(column.children))\n    } else {\n      result.push(column)\n    }\n  })\n  return result\n}\n\nfunction useWatcher<T extends DefaultRow>() {\n  const instance = getCurrentInstance() as Table<T>\n  const { size: tableSize } = toRefs(instance.proxy?.$props as any)\n  const rowKey: Ref<string | null> = ref(null)\n  const data: Ref<T[]> = ref([])\n  const _data: Ref<T[]> = ref([])\n  const isComplex = ref(false)\n  const _columns: Ref<TableColumnCtx<T>[]> = ref([])\n  const originColumns: Ref<TableColumnCtx<T>[]> = ref([])\n  const columns: Ref<TableColumnCtx<T>[]> = ref([])\n  const fixedColumns: Ref<TableColumnCtx<T>[]> = ref([])\n  const rightFixedColumns: Ref<TableColumnCtx<T>[]> = ref([])\n  const leafColumns: Ref<TableColumnCtx<T>[]> = ref([])\n  const fixedLeafColumns: Ref<TableColumnCtx<T>[]> = ref([])\n  const rightFixedLeafColumns: Ref<TableColumnCtx<T>[]> = ref([])\n  const updateOrderFns: (() => void)[] = []\n  const leafColumnsLength = ref(0)\n  const fixedLeafColumnsLength = ref(0)\n  const rightFixedLeafColumnsLength = ref(0)\n  const isAllSelected = ref(false)\n  const selection: Ref<T[]> = ref([])\n  const reserveSelection = ref(false)\n  const selectOnIndeterminate = ref(false)\n  const selectable: Ref<((row: T, index: number) => boolean) | null> = ref(null)\n  const filters: Ref<StoreFilter> = ref({})\n  const filteredData: Ref<T[] | null> = ref(null)\n  const sortingColumn: Ref<TableColumnCtx<T> | null> = ref(null)\n  const sortProp: Ref<string | null> = ref(null)\n  const sortOrder: Ref<string | number | null> = ref(null)\n  const hoverRow: Ref<T | null> = ref(null)\n\n  const selectedMap = computed(() => {\n    return rowKey.value ? getKeysMap(selection.value, rowKey.value) : undefined\n  })\n\n  watch(\n    data,\n    () => {\n      if (instance.state) {\n        scheduleLayout(false)\n        const needUpdateFixed = instance.props.tableLayout === 'auto'\n        if (needUpdateFixed) {\n          instance.refs.tableHeaderRef?.updateFixedColumnStyle()\n        }\n      }\n    },\n    {\n      deep: true,\n    }\n  )\n\n  // 检查 rowKey 是否存在\n  const assertRowKey = () => {\n    if (!rowKey.value) throw new Error('[ElTable] prop row-key is required')\n  }\n\n  // 更新 fixed\n  const updateChildFixed = (column: TableColumnCtx<T>) => {\n    column.children?.forEach((childColumn) => {\n      childColumn.fixed = column.fixed\n      updateChildFixed(childColumn)\n    })\n  }\n\n  // 更新列\n  const updateColumns = () => {\n    _columns.value.forEach((column) => {\n      updateChildFixed(column)\n    })\n    fixedColumns.value = _columns.value.filter((column) =>\n      [true, 'left'].includes(column.fixed)\n    )\n\n    const selectColumn = _columns.value.find(\n      (column) => column.type === 'selection'\n    )\n\n    let selectColFixLeft: boolean\n    if (\n      selectColumn &&\n      selectColumn.fixed !== 'right' &&\n      !fixedColumns.value.includes(selectColumn)\n    ) {\n      const selectColumnIndex = _columns.value.indexOf(selectColumn)\n      if (selectColumnIndex === 0 && fixedColumns.value.length) {\n        fixedColumns.value.unshift(selectColumn)\n        selectColFixLeft = true\n      }\n    }\n\n    rightFixedColumns.value = _columns.value.filter(\n      (column) => column.fixed === 'right'\n    )\n\n    const notFixedColumns = _columns.value.filter(\n      (column) =>\n        (selectColFixLeft ? column.type !== 'selection' : true) && !column.fixed\n    )\n\n    originColumns.value = Array.from(fixedColumns.value)\n      .concat(notFixedColumns)\n      .concat(rightFixedColumns.value)\n    const leafColumns = doFlattenColumns(notFixedColumns)\n    const fixedLeafColumns = doFlattenColumns(fixedColumns.value)\n    const rightFixedLeafColumns = doFlattenColumns(rightFixedColumns.value)\n\n    leafColumnsLength.value = leafColumns.length\n    fixedLeafColumnsLength.value = fixedLeafColumns.length\n    rightFixedLeafColumnsLength.value = rightFixedLeafColumns.length\n\n    columns.value = Array.from(fixedLeafColumns)\n      .concat(leafColumns)\n      .concat(rightFixedLeafColumns)\n    isComplex.value =\n      fixedColumns.value.length > 0 || rightFixedColumns.value.length > 0\n  }\n\n  // 更新 DOM\n  const scheduleLayout = (needUpdateColumns?: boolean, immediate = false) => {\n    if (needUpdateColumns) {\n      updateColumns()\n    }\n    if (immediate) {\n      instance.state.doLayout()\n    } else {\n      instance.state.debouncedUpdateLayout()\n    }\n  }\n\n  // 选择\n  const isSelected = (row: T) => {\n    if (selectedMap.value) {\n      return !!selectedMap.value[getRowIdentity(row, rowKey.value)]\n    } else {\n      return selection.value.includes(row)\n    }\n  }\n\n  const clearSelection = () => {\n    isAllSelected.value = false\n    const oldSelection = selection.value\n    selection.value = []\n    if (oldSelection.length) {\n      instance.emit('selection-change', [])\n    }\n  }\n\n  const cleanSelection = () => {\n    let deleted\n    if (rowKey.value) {\n      deleted = []\n      const childrenKey = instance?.store?.states?.childrenColumnName.value\n      const dataMap = getKeysMap(data.value, rowKey.value, true, childrenKey)\n      for (const key in selectedMap.value) {\n        if (hasOwn(selectedMap.value, key) && !dataMap[key]) {\n          deleted.push(selectedMap.value[key].row)\n        }\n      }\n    } else {\n      deleted = selection.value.filter((item) => !data.value.includes(item))\n    }\n    if (deleted.length) {\n      const newSelection = selection.value.filter(\n        (item) => !deleted.includes(item)\n      )\n      selection.value = newSelection\n      instance.emit('selection-change', newSelection.slice())\n    }\n  }\n\n  const getSelectionRows = () => {\n    return (selection.value || []).slice()\n  }\n\n  const toggleRowSelection = (\n    row: T,\n    selected?: boolean,\n    emitChange = true,\n    ignoreSelectable = false\n  ) => {\n    const treeProps = {\n      children: instance?.store?.states?.childrenColumnName.value,\n      checkStrictly: instance?.store?.states?.checkStrictly.value,\n    }\n    const changed = toggleRowStatus(\n      selection.value,\n      row,\n      selected,\n      treeProps,\n      ignoreSelectable ? undefined : selectable.value,\n      data.value.indexOf(row),\n      rowKey.value\n    )\n    if (changed) {\n      const newSelection = (selection.value || []).slice()\n      // 调用 API 修改选中值，不触发 select 事件\n      if (emitChange) {\n        instance.emit('select', newSelection, row)\n      }\n      instance.emit('selection-change', newSelection)\n    }\n  }\n\n  const _toggleAllSelection = () => {\n    // when only some rows are selected (but not all), select or deselect all of them\n    // depending on the value of selectOnIndeterminate\n    const value = selectOnIndeterminate.value\n      ? !isAllSelected.value\n      : !(isAllSelected.value || selection.value.length)\n    isAllSelected.value = value\n\n    let selectionChanged = false\n    let childrenCount = 0\n    const rowKey = instance?.store?.states?.rowKey.value\n    const { childrenColumnName } = instance.store.states\n    const treeProps = {\n      children: childrenColumnName.value,\n      checkStrictly: false, // Disable checkStrictly when selecting all\n    }\n\n    data.value.forEach((row, index) => {\n      const rowIndex = index + childrenCount\n      if (\n        toggleRowStatus(\n          selection.value,\n          row,\n          value,\n          treeProps,\n          selectable.value,\n          rowIndex,\n          rowKey\n        )\n      ) {\n        selectionChanged = true\n      }\n      childrenCount += getChildrenCount(getRowIdentity(row, rowKey))\n    })\n\n    if (selectionChanged) {\n      instance.emit(\n        'selection-change',\n        selection.value ? selection.value.slice() : []\n      )\n    }\n    instance.emit('select-all', (selection.value || []).slice())\n  }\n\n  const updateAllSelected = () => {\n    // data 为 null 时，解构时的默认值会被忽略\n    if (data.value?.length === 0) {\n      isAllSelected.value = false\n      return\n    }\n\n    const { childrenColumnName } = instance.store.states\n    let rowIndex = 0\n    let selectedCount = 0\n\n    const checkSelectedStatus = (data: T[]) => {\n      for (const row of data) {\n        const isRowSelectable =\n          selectable.value && selectable.value.call(null, row, rowIndex)\n\n        if (!isSelected(row)) {\n          if (!selectable.value || isRowSelectable) {\n            return false\n          }\n        } else {\n          selectedCount++\n        }\n        rowIndex++\n\n        if (\n          row[childrenColumnName.value]?.length &&\n          !checkSelectedStatus(row[childrenColumnName.value])\n        ) {\n          return false\n        }\n      }\n      return true\n    }\n\n    const isAllSelected_ = checkSelectedStatus(data.value || [])\n    isAllSelected.value = selectedCount === 0 ? false : isAllSelected_\n  }\n\n  const getChildrenCount = (rowKey: string) => {\n    if (!instance || !instance.store) return 0\n    const { treeData } = instance.store.states\n    let count = 0\n    const children = treeData.value[rowKey]?.children\n    if (children) {\n      count += children.length\n      children.forEach((childKey) => {\n        count += getChildrenCount(childKey)\n      })\n    }\n    return count\n  }\n\n  // 过滤与排序\n  const updateFilters = (column: TableColumnCtx<T>, values: string[]) => {\n    const filters_: Record<string, string[]> = {}\n    ensureArray(column).forEach((col) => {\n      filters.value[col.id] = values\n      filters_[col.columnKey || col.id] = values\n    })\n    return filters_\n  }\n\n  const updateSort = (\n    column: TableColumnCtx<T> | null,\n    prop: string | null,\n    order: TableSortOrder | null\n  ) => {\n    if (sortingColumn.value && sortingColumn.value !== column) {\n      sortingColumn.value.order = null\n    }\n    sortingColumn.value = column\n    sortProp.value = prop\n    sortOrder.value = order\n  }\n\n  const execFilter = () => {\n    let sourceData = unref(_data)\n    Object.keys(filters.value).forEach((columnId) => {\n      const values = filters.value[columnId]\n      if (!values || values.length === 0) return\n      const column = getColumnById(\n        {\n          columns: columns.value,\n        },\n        columnId\n      )\n      if (column && column.filterMethod) {\n        sourceData = sourceData.filter((row) => {\n          return values.some((value) =>\n            column.filterMethod.call(null, value, row, column)\n          )\n        })\n      }\n    })\n    filteredData.value = sourceData\n  }\n\n  const execSort = () => {\n    data.value = sortData(filteredData.value ?? [], {\n      sortingColumn: sortingColumn.value,\n      sortProp: sortProp.value,\n      sortOrder: sortOrder.value,\n    })\n  }\n\n  // 根据 filters 与 sort 去过滤 data\n  const execQuery = (ignore: { filter: boolean } | undefined = undefined) => {\n    if (!ignore?.filter) {\n      execFilter()\n    }\n    execSort()\n  }\n\n  const clearFilter = (columnKeys?: string[] | string) => {\n    const { tableHeaderRef } = instance.refs as TableRefs\n    if (!tableHeaderRef) return\n    const panels = Object.assign({}, tableHeaderRef.filterPanels)\n\n    const keys = Object.keys(panels)\n    if (!keys.length) return\n\n    if (isString(columnKeys)) {\n      columnKeys = [columnKeys]\n    }\n\n    if (isArray(columnKeys)) {\n      const columns_ = columnKeys.map((key) =>\n        getColumnByKey(\n          {\n            columns: columns.value,\n          },\n          key\n        )\n      )\n      keys.forEach((key) => {\n        const column = columns_.find((col) => col.id === key)\n        if (column) {\n          column.filteredValue = []\n        }\n      })\n      instance.store.commit('filterChange', {\n        column: columns_,\n        values: [],\n        silent: true,\n        multi: true,\n      })\n    } else {\n      keys.forEach((key) => {\n        const column = columns.value.find((col) => col.id === key)\n        if (column) {\n          column.filteredValue = []\n        }\n      })\n\n      filters.value = {}\n      instance.store.commit('filterChange', {\n        column: {},\n        values: [],\n        silent: true,\n      })\n    }\n  }\n\n  const clearSort = () => {\n    if (!sortingColumn.value) return\n\n    updateSort(null, null, null)\n    instance.store.commit('changeSortCondition', {\n      silent: true,\n    })\n  }\n  const {\n    setExpandRowKeys,\n    toggleRowExpansion,\n    updateExpandRows,\n    states: expandStates,\n    isRowExpanded,\n  } = useExpand({\n    data,\n    rowKey,\n  })\n  const {\n    updateTreeExpandKeys,\n    toggleTreeExpansion,\n    updateTreeData,\n    updateKeyChildren,\n    loadOrToggle,\n    states: treeStates,\n  } = useTree({\n    data,\n    rowKey,\n  })\n  const {\n    updateCurrentRowData,\n    updateCurrentRow,\n    setCurrentRowKey,\n    states: currentData,\n  } = useCurrent({\n    data,\n    rowKey,\n  })\n  // 适配层，expand-row-keys 在 Expand 与 TreeTable 中都有使用\n  const setExpandRowKeysAdapter = (val: (string | number)[]) => {\n    // 这里会触发额外的计算，但为了兼容性，暂时这么做\n    setExpandRowKeys(val)\n    updateTreeExpandKeys(val)\n  }\n\n  // 展开行与 TreeTable 都要使用\n  const toggleRowExpansionAdapter = (row: T, expanded?: boolean) => {\n    const hasExpandColumn = columns.value.some(({ type }) => type === 'expand')\n    if (hasExpandColumn) {\n      toggleRowExpansion(row, expanded)\n    } else {\n      toggleTreeExpansion(row, expanded)\n    }\n  }\n\n  return {\n    assertRowKey,\n    updateColumns,\n    scheduleLayout,\n    isSelected,\n    clearSelection,\n    cleanSelection,\n    getSelectionRows,\n    toggleRowSelection,\n    _toggleAllSelection,\n    toggleAllSelection: null as (() => void) | null,\n    updateAllSelected,\n    updateFilters,\n    updateCurrentRow,\n    updateSort,\n    execFilter,\n    execSort,\n    execQuery,\n    clearFilter,\n    clearSort,\n    toggleRowExpansion,\n    setExpandRowKeysAdapter,\n    setCurrentRowKey,\n    toggleRowExpansionAdapter,\n    isRowExpanded,\n    updateExpandRows,\n    updateCurrentRowData,\n    loadOrToggle,\n    updateTreeData,\n    updateKeyChildren,\n    states: {\n      tableSize,\n      rowKey,\n      data,\n      _data,\n      isComplex,\n      _columns,\n      originColumns,\n      columns,\n      fixedColumns,\n      rightFixedColumns,\n      leafColumns,\n      fixedLeafColumns,\n      rightFixedLeafColumns,\n      updateOrderFns,\n      leafColumnsLength,\n      fixedLeafColumnsLength,\n      rightFixedLeafColumnsLength,\n      isAllSelected,\n      selection,\n      reserveSelection,\n      selectOnIndeterminate,\n      selectable,\n      filters,\n      filteredData,\n      sortingColumn,\n      sortProp,\n      sortOrder,\n      hoverRow,\n      ...expandStates,\n      ...treeStates,\n      ...currentData,\n    },\n  }\n}\n\nexport default useWatcher\n"], "names": ["ensureArray"], "mappings": ";;;;;;;;AAaA,MAAM,QAAQ,GAAG,CAAC,IAAI,EAAE,MAAM,KAAK;AACnC,EAAE,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;AAC7C,EAAE,IAAI,CAAC,aAAa,IAAI,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE;AAC1D,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,OAAO,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,SAAS,EAAE,aAAa,CAAC,UAAU,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;AAC1G,CAAC,CAAC;AACF,MAAM,gBAAgB,GAAG,CAAC,OAAO,KAAK;AACtC,EAAE,MAAM,MAAM,GAAG,EAAE,CAAC;AACpB,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AAC9B,IAAI,IAAI,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;AACvD,MAAM,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;AACnE,KAAK,MAAM;AACX,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC1B,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AACF,SAAS,UAAU,GAAG;AACtB,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,MAAM,QAAQ,GAAG,kBAAkB,EAAE,CAAC;AACxC,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,MAAM,CAAC,CAAC,EAAE,GAAG,QAAQ,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC;AACzF,EAAE,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AAC3B,EAAE,MAAM,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;AACvB,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;AACxB,EAAE,MAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AAC/B,EAAE,MAAM,QAAQ,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;AAC3B,EAAE,MAAM,aAAa,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;AAChC,EAAE,MAAM,OAAO,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;AAC1B,EAAE,MAAM,YAAY,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;AAC/B,EAAE,MAAM,iBAAiB,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;AACpC,EAAE,MAAM,WAAW,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;AAC9B,EAAE,MAAM,gBAAgB,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;AACnC,EAAE,MAAM,qBAAqB,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;AACxC,EAAE,MAAM,cAAc,GAAG,EAAE,CAAC;AAC5B,EAAE,MAAM,iBAAiB,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACnC,EAAE,MAAM,sBAAsB,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACxC,EAAE,MAAM,2BAA2B,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7C,EAAE,MAAM,aAAa,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AACnC,EAAE,MAAM,SAAS,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;AAC5B,EAAE,MAAM,gBAAgB,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AACtC,EAAE,MAAM,qBAAqB,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AAC3C,EAAE,MAAM,UAAU,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AAC/B,EAAE,MAAM,OAAO,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;AAC1B,EAAE,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AACjC,EAAE,MAAM,aAAa,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AAClC,EAAE,MAAM,QAAQ,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AAC7B,EAAE,MAAM,SAAS,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AAC9B,EAAE,MAAM,QAAQ,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AAC7B,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM;AACrC,IAAI,OAAO,MAAM,CAAC,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAC7E,GAAG,CAAC,CAAC;AACL,EAAE,KAAK,CAAC,IAAI,EAAE,MAAM;AACpB,IAAI,IAAI,GAAG,CAAC;AACZ,IAAI,IAAI,QAAQ,CAAC,KAAK,EAAE;AACxB,MAAM,cAAc,CAAC,KAAK,CAAC,CAAC;AAC5B,MAAM,MAAM,eAAe,GAAG,QAAQ,CAAC,KAAK,CAAC,WAAW,KAAK,MAAM,CAAC;AACpE,MAAM,IAAI,eAAe,EAAE;AAC3B,QAAQ,CAAC,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,cAAc,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,sBAAsB,EAAE,CAAC;AAC7F,OAAO;AACP,KAAK;AACL,GAAG,EAAE;AACL,IAAI,IAAI,EAAE,IAAI;AACd,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,YAAY,GAAG,MAAM;AAC7B,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK;AACrB,MAAM,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;AAC5D,GAAG,CAAC;AACJ,EAAE,MAAM,gBAAgB,GAAG,CAAC,MAAM,KAAK;AACvC,IAAI,IAAI,GAAG,CAAC;AACZ,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,CAAC,WAAW,KAAK;AAC5E,MAAM,WAAW,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;AACvC,MAAM,gBAAgB,CAAC,WAAW,CAAC,CAAC;AACpC,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,MAAM;AAC9B,IAAI,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AACvC,MAAM,gBAAgB,CAAC,MAAM,CAAC,CAAC;AAC/B,KAAK,CAAC,CAAC;AACP,IAAI,YAAY,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AAClG,IAAI,MAAM,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC;AACtF,IAAI,IAAI,gBAAgB,CAAC;AACzB,IAAI,IAAI,YAAY,IAAI,YAAY,CAAC,KAAK,KAAK,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;AACtG,MAAM,MAAM,iBAAiB,GAAG,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;AACrE,MAAM,IAAI,iBAAiB,KAAK,CAAC,IAAI,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE;AAChE,QAAQ,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;AACjD,QAAQ,gBAAgB,GAAG,IAAI,CAAC;AAChC,OAAO;AACP,KAAK;AACL,IAAI,iBAAiB,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,KAAK,KAAK,OAAO,CAAC,CAAC;AAC1F,IAAI,MAAM,eAAe,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,gBAAgB,GAAG,MAAM,CAAC,IAAI,KAAK,WAAW,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACxI,IAAI,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;AACjH,IAAI,MAAM,YAAY,GAAG,gBAAgB,CAAC,eAAe,CAAC,CAAC;AAC3D,IAAI,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AACnE,IAAI,MAAM,sBAAsB,GAAG,gBAAgB,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;AAC7E,IAAI,iBAAiB,CAAC,KAAK,GAAG,YAAY,CAAC,MAAM,CAAC;AAClD,IAAI,sBAAsB,CAAC,KAAK,GAAG,iBAAiB,CAAC,MAAM,CAAC;AAC5D,IAAI,2BAA2B,CAAC,KAAK,GAAG,sBAAsB,CAAC,MAAM,CAAC;AACtE,IAAI,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC;AACtG,IAAI,SAAS,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,iBAAiB,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AAC1F,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,CAAC,iBAAiB,EAAE,SAAS,GAAG,KAAK,KAAK;AACnE,IAAI,IAAI,iBAAiB,EAAE;AAC3B,MAAM,aAAa,EAAE,CAAC;AACtB,KAAK;AACL,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;AAChC,KAAK,MAAM;AACX,MAAM,QAAQ,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC;AAC7C,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,UAAU,GAAG,CAAC,GAAG,KAAK;AAC9B,IAAI,IAAI,WAAW,CAAC,KAAK,EAAE;AAC3B,MAAM,OAAO,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AACpE,KAAK,MAAM;AACX,MAAM,OAAO,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAC3C,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,MAAM;AAC/B,IAAI,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC;AAChC,IAAI,MAAM,YAAY,GAAG,SAAS,CAAC,KAAK,CAAC;AACzC,IAAI,SAAS,CAAC,KAAK,GAAG,EAAE,CAAC;AACzB,IAAI,IAAI,YAAY,CAAC,MAAM,EAAE;AAC7B,MAAM,QAAQ,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC;AAC5C,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,MAAM;AAC/B,IAAI,IAAI,GAAG,EAAE,EAAE,CAAC;AAChB,IAAI,IAAI,OAAO,CAAC;AAChB,IAAI,IAAI,MAAM,CAAC,KAAK,EAAE;AACtB,MAAM,OAAO,GAAG,EAAE,CAAC;AACnB,MAAM,MAAM,WAAW,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,kBAAkB,CAAC,KAAK,CAAC;AACjK,MAAM,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;AAC9E,MAAM,KAAK,MAAM,GAAG,IAAI,WAAW,CAAC,KAAK,EAAE;AAC3C,QAAQ,IAAI,MAAM,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;AAC7D,UAAU,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACnD,SAAS;AACT,OAAO;AACP,KAAK,MAAM;AACX,MAAM,OAAO,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;AAC7E,KAAK;AACL,IAAI,IAAI,OAAO,CAAC,MAAM,EAAE;AACxB,MAAM,MAAM,YAAY,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;AACrF,MAAM,SAAS,CAAC,KAAK,GAAG,YAAY,CAAC;AACrC,MAAM,QAAQ,CAAC,IAAI,CAAC,kBAAkB,EAAE,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC;AAC9D,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,gBAAgB,GAAG,MAAM;AACjC,IAAI,OAAO,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC;AAC3C,GAAG,CAAC;AACJ,EAAE,MAAM,kBAAkB,GAAG,CAAC,GAAG,EAAE,QAAQ,EAAE,UAAU,GAAG,IAAI,EAAE,gBAAgB,GAAG,KAAK,KAAK;AAC7F,IAAI,IAAI,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACxB,IAAI,MAAM,SAAS,GAAG;AACtB,MAAM,QAAQ,EAAE,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,kBAAkB,CAAC,KAAK;AACtJ,MAAM,aAAa,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,aAAa,CAAC,KAAK;AACpJ,KAAK,CAAC;AACN,IAAI,MAAM,OAAO,GAAG,eAAe,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,SAAS,EAAE,gBAAgB,GAAG,KAAK,CAAC,GAAG,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;AACpK,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,MAAM,YAAY,GAAG,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC;AAC3D,MAAM,IAAI,UAAU,EAAE;AACtB,QAAQ,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,EAAE,GAAG,CAAC,CAAC;AACnD,OAAO;AACP,MAAM,QAAQ,CAAC,IAAI,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAC;AACtD,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,mBAAmB,GAAG,MAAM;AACpC,IAAI,IAAI,GAAG,EAAE,EAAE,CAAC;AAChB,IAAI,MAAM,KAAK,GAAG,qBAAqB,CAAC,KAAK,GAAG,CAAC,aAAa,CAAC,KAAK,GAAG,EAAE,aAAa,CAAC,KAAK,IAAI,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACxH,IAAI,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC;AAChC,IAAI,IAAI,gBAAgB,GAAG,KAAK,CAAC;AACjC,IAAI,IAAI,aAAa,GAAG,CAAC,CAAC;AAC1B,IAAI,MAAM,OAAO,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC;AAC/I,IAAI,MAAM,EAAE,kBAAkB,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC;AACzD,IAAI,MAAM,SAAS,GAAG;AACtB,MAAM,QAAQ,EAAE,kBAAkB,CAAC,KAAK;AACxC,MAAM,aAAa,EAAE,KAAK;AAC1B,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,KAAK;AACvC,MAAM,MAAM,QAAQ,GAAG,KAAK,GAAG,aAAa,CAAC;AAC7C,MAAM,IAAI,eAAe,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAE;AACxG,QAAQ,gBAAgB,GAAG,IAAI,CAAC;AAChC,OAAO;AACP,MAAM,aAAa,IAAI,gBAAgB,CAAC,cAAc,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;AACtE,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,gBAAgB,EAAE;AAC1B,MAAM,QAAQ,CAAC,IAAI,CAAC,kBAAkB,EAAE,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;AACxF,KAAK;AACL,IAAI,QAAQ,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;AACjE,GAAG,CAAC;AACJ,EAAE,MAAM,iBAAiB,GAAG,MAAM;AAClC,IAAI,IAAI,GAAG,CAAC;AACZ,IAAI,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,MAAM,MAAM,CAAC,EAAE;AAClE,MAAM,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC;AAClC,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,EAAE,kBAAkB,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC;AACzD,IAAI,IAAI,QAAQ,GAAG,CAAC,CAAC;AACrB,IAAI,IAAI,aAAa,GAAG,CAAC,CAAC;AAC1B,IAAI,MAAM,mBAAmB,GAAG,CAAC,KAAK,KAAK;AAC3C,MAAM,IAAI,GAAG,CAAC;AACd,MAAM,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE;AAC/B,QAAQ,MAAM,eAAe,GAAG,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC;AAC/F,QAAQ,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;AAC9B,UAAU,IAAI,CAAC,UAAU,CAAC,KAAK,IAAI,eAAe,EAAE;AACpD,YAAY,OAAO,KAAK,CAAC;AACzB,WAAW;AACX,SAAS,MAAM;AACf,UAAU,aAAa,EAAE,CAAC;AAC1B,SAAS;AACT,QAAQ,QAAQ,EAAE,CAAC;AACnB,QAAQ,IAAI,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,kBAAkB,CAAC,KAAK,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,MAAM,KAAK,CAAC,mBAAmB,CAAC,GAAG,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE;AAC1I,UAAU,OAAO,KAAK,CAAC;AACvB,SAAS;AACT,OAAO;AACP,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK,CAAC;AACN,IAAI,MAAM,cAAc,GAAG,mBAAmB,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;AACjE,IAAI,aAAa,CAAC,KAAK,GAAG,aAAa,KAAK,CAAC,GAAG,KAAK,GAAG,cAAc,CAAC;AACvE,GAAG,CAAC;AACJ,EAAE,MAAM,gBAAgB,GAAG,CAAC,OAAO,KAAK;AACxC,IAAI,IAAI,GAAG,CAAC;AACZ,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,KAAK;AACpC,MAAM,OAAO,CAAC,CAAC;AACf,IAAI,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC;AAC/C,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC;AAClB,IAAI,MAAM,QAAQ,GAAG,CAAC,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,QAAQ,CAAC;AACrF,IAAI,IAAI,QAAQ,EAAE;AAClB,MAAM,KAAK,IAAI,QAAQ,CAAC,MAAM,CAAC;AAC/B,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAK;AACrC,QAAQ,KAAK,IAAI,gBAAgB,CAAC,QAAQ,CAAC,CAAC;AAC5C,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,CAAC,MAAM,EAAE,MAAM,KAAK;AAC5C,IAAI,MAAM,QAAQ,GAAG,EAAE,CAAC;AACxB,IAAIA,SAAW,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AACzC,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC;AACrC,MAAM,QAAQ,CAAC,GAAG,CAAC,SAAS,IAAI,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC;AACjD,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,QAAQ,CAAC;AACpB,GAAG,CAAC;AACJ,EAAE,MAAM,UAAU,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,KAAK;AAC9C,IAAI,IAAI,aAAa,CAAC,KAAK,IAAI,aAAa,CAAC,KAAK,KAAK,MAAM,EAAE;AAC/D,MAAM,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC;AACvC,KAAK;AACL,IAAI,aAAa,CAAC,KAAK,GAAG,MAAM,CAAC;AACjC,IAAI,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC;AAC1B,IAAI,SAAS,CAAC,KAAK,GAAG,KAAK,CAAC;AAC5B,GAAG,CAAC;AACJ,EAAE,MAAM,UAAU,GAAG,MAAM;AAC3B,IAAI,IAAI,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;AAClC,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAK;AACrD,MAAM,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AAC7C,MAAM,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;AACxC,QAAQ,OAAO;AACf,MAAM,MAAM,MAAM,GAAG,aAAa,CAAC;AACnC,QAAQ,OAAO,EAAE,OAAO,CAAC,KAAK;AAC9B,OAAO,EAAE,QAAQ,CAAC,CAAC;AACnB,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,YAAY,EAAE;AACzC,QAAQ,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK;AAChD,UAAU,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC;AAC5F,SAAS,CAAC,CAAC;AACX,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,YAAY,CAAC,KAAK,GAAG,UAAU,CAAC;AACpC,GAAG,CAAC;AACJ,EAAE,MAAM,QAAQ,GAAG,MAAM;AACzB,IAAI,IAAI,GAAG,CAAC;AACZ,IAAI,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,CAAC,GAAG,GAAG,YAAY,CAAC,KAAK,KAAK,IAAI,GAAG,GAAG,GAAG,EAAE,EAAE;AACzE,MAAM,aAAa,EAAE,aAAa,CAAC,KAAK;AACxC,MAAM,QAAQ,EAAE,QAAQ,CAAC,KAAK;AAC9B,MAAM,SAAS,EAAE,SAAS,CAAC,KAAK;AAChC,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK;AACzC,IAAI,IAAI,EAAE,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE;AACpD,MAAM,UAAU,EAAE,CAAC;AACnB,KAAK;AACL,IAAI,QAAQ,EAAE,CAAC;AACf,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,CAAC,UAAU,KAAK;AACtC,IAAI,MAAM,EAAE,cAAc,EAAE,GAAG,QAAQ,CAAC,IAAI,CAAC;AAC7C,IAAI,IAAI,CAAC,cAAc;AACvB,MAAM,OAAO;AACb,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,cAAc,CAAC,YAAY,CAAC,CAAC;AAClE,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACrC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM;AACpB,MAAM,OAAO;AACb,IAAI,IAAI,QAAQ,CAAC,UAAU,CAAC,EAAE;AAC9B,MAAM,UAAU,GAAG,CAAC,UAAU,CAAC,CAAC;AAChC,KAAK;AACL,IAAI,IAAI,OAAO,CAAC,UAAU,CAAC,EAAE;AAC7B,MAAM,MAAM,QAAQ,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,cAAc,CAAC;AAC9D,QAAQ,OAAO,EAAE,OAAO,CAAC,KAAK;AAC9B,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;AACf,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AAC5B,QAAQ,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC;AAC9D,QAAQ,IAAI,MAAM,EAAE;AACpB,UAAU,MAAM,CAAC,aAAa,GAAG,EAAE,CAAC;AACpC,SAAS;AACT,OAAO,CAAC,CAAC;AACT,MAAM,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,cAAc,EAAE;AAC5C,QAAQ,MAAM,EAAE,QAAQ;AACxB,QAAQ,MAAM,EAAE,EAAE;AAClB,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,KAAK,EAAE,IAAI;AACnB,OAAO,CAAC,CAAC;AACT,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AAC5B,QAAQ,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC;AACnE,QAAQ,IAAI,MAAM,EAAE;AACpB,UAAU,MAAM,CAAC,aAAa,GAAG,EAAE,CAAC;AACpC,SAAS;AACT,OAAO,CAAC,CAAC;AACT,MAAM,OAAO,CAAC,KAAK,GAAG,EAAE,CAAC;AACzB,MAAM,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,cAAc,EAAE;AAC5C,QAAQ,MAAM,EAAE,EAAE;AAClB,QAAQ,MAAM,EAAE,EAAE;AAClB,QAAQ,MAAM,EAAE,IAAI;AACpB,OAAO,CAAC,CAAC;AACT,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,MAAM;AAC1B,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK;AAC5B,MAAM,OAAO;AACb,IAAI,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AACjC,IAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,qBAAqB,EAAE;AACjD,MAAM,MAAM,EAAE,IAAI;AAClB,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ,EAAE,MAAM;AACR,IAAI,gBAAgB;AACpB,IAAI,kBAAkB;AACtB,IAAI,gBAAgB;AACpB,IAAI,MAAM,EAAE,YAAY;AACxB,IAAI,aAAa;AACjB,GAAG,GAAG,SAAS,CAAC;AAChB,IAAI,IAAI;AACR,IAAI,MAAM;AACV,GAAG,CAAC,CAAC;AACL,EAAE,MAAM;AACR,IAAI,oBAAoB;AACxB,IAAI,mBAAmB;AACvB,IAAI,cAAc;AAClB,IAAI,iBAAiB;AACrB,IAAI,YAAY;AAChB,IAAI,MAAM,EAAE,UAAU;AACtB,GAAG,GAAG,OAAO,CAAC;AACd,IAAI,IAAI;AACR,IAAI,MAAM;AACV,GAAG,CAAC,CAAC;AACL,EAAE,MAAM;AACR,IAAI,oBAAoB;AACxB,IAAI,gBAAgB;AACpB,IAAI,gBAAgB;AACpB,IAAI,MAAM,EAAE,WAAW;AACvB,GAAG,GAAG,UAAU,CAAC;AACjB,IAAI,IAAI;AACR,IAAI,MAAM;AACV,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,uBAAuB,GAAG,CAAC,GAAG,KAAK;AAC3C,IAAI,gBAAgB,CAAC,GAAG,CAAC,CAAC;AAC1B,IAAI,oBAAoB,CAAC,GAAG,CAAC,CAAC;AAC9B,GAAG,CAAC;AACJ,EAAE,MAAM,yBAAyB,GAAG,CAAC,GAAG,EAAE,QAAQ,KAAK;AACvD,IAAI,MAAM,eAAe,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,IAAI,KAAK,QAAQ,CAAC,CAAC;AAChF,IAAI,IAAI,eAAe,EAAE;AACzB,MAAM,kBAAkB,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;AACxC,KAAK,MAAM;AACX,MAAM,mBAAmB,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;AACzC,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,YAAY;AAChB,IAAI,aAAa;AACjB,IAAI,cAAc;AAClB,IAAI,UAAU;AACd,IAAI,cAAc;AAClB,IAAI,cAAc;AAClB,IAAI,gBAAgB;AACpB,IAAI,kBAAkB;AACtB,IAAI,mBAAmB;AACvB,IAAI,kBAAkB,EAAE,IAAI;AAC5B,IAAI,iBAAiB;AACrB,IAAI,aAAa;AACjB,IAAI,gBAAgB;AACpB,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,WAAW;AACf,IAAI,SAAS;AACb,IAAI,kBAAkB;AACtB,IAAI,uBAAuB;AAC3B,IAAI,gBAAgB;AACpB,IAAI,yBAAyB;AAC7B,IAAI,aAAa;AACjB,IAAI,gBAAgB;AACpB,IAAI,oBAAoB;AACxB,IAAI,YAAY;AAChB,IAAI,cAAc;AAClB,IAAI,iBAAiB;AACrB,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS;AACf,MAAM,MAAM;AACZ,MAAM,IAAI;AACV,MAAM,KAAK;AACX,MAAM,SAAS;AACf,MAAM,QAAQ;AACd,MAAM,aAAa;AACnB,MAAM,OAAO;AACb,MAAM,YAAY;AAClB,MAAM,iBAAiB;AACvB,MAAM,WAAW;AACjB,MAAM,gBAAgB;AACtB,MAAM,qBAAqB;AAC3B,MAAM,cAAc;AACpB,MAAM,iBAAiB;AACvB,MAAM,sBAAsB;AAC5B,MAAM,2BAA2B;AACjC,MAAM,aAAa;AACnB,MAAM,SAAS;AACf,MAAM,gBAAgB;AACtB,MAAM,qBAAqB;AAC3B,MAAM,UAAU;AAChB,MAAM,OAAO;AACb,MAAM,YAAY;AAClB,MAAM,aAAa;AACnB,MAAM,QAAQ;AACd,MAAM,SAAS;AACf,MAAM,QAAQ;AACd,MAAM,GAAG,YAAY;AACrB,MAAM,GAAG,UAAU;AACnB,MAAM,GAAG,WAAW;AACpB,KAAK;AACL,GAAG,CAAC;AACJ;;;;"}