<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <application>

        <activity
            android:name=".ui.activity.HomeActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|uiMode|orientation"
            android:exported="true"
            android:screenOrientation="fullUser"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.SEND" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="text/plain" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="content" />
                <data android:scheme="file" />
                <data android:mimeType="video/*" />
                <data android:mimeType="audio/*" />
                <data android:mimeType="text/plain" />
                <data android:mimeType="application/x-bittorrent" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="smb" />
                <data android:scheme="rtmp" />
                <data android:scheme="rtsp" />
                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:mimeType="video/*" />
                <data android:mimeType="audio/*" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="ed2k" />
                <data android:scheme="magnet" />
                <data android:scheme="thunder" />
                <data android:scheme="jianpian" />
            </intent-filter>
        </activity>

        <activity
            android:name=".ui.activity.CollectActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|uiMode"
            android:screenOrientation="fullUser"
            android:windowSoftInputMode="adjustPan" />

        <activity
            android:name=".ui.activity.FileActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|uiMode"
            android:screenOrientation="fullUser" />

        <activity
            android:name=".ui.activity.FolderActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|uiMode"
            android:screenOrientation="fullUser" />

        <activity
            android:name=".ui.activity.HistoryActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|uiMode"
            android:screenOrientation="fullUser" />

        <activity
            android:name=".ui.activity.KeepActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|uiMode"
            android:screenOrientation="fullUser" />

        <activity
            android:name=".ui.activity.LiveActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|uiMode|orientation"
            android:exported="true"
            android:launchMode="singleTop"
            android:resizeableActivity="true"
            android:screenOrientation="sensorLandscape"
            android:supportsPictureInPicture="true"
            android:theme="@style/AppTheme.Live" />

        <activity
            android:name=".ui.activity.ScanActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|uiMode"
            android:screenOrientation="fullUser" />

        <activity
            android:name=".ui.activity.VideoActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|uiMode|orientation"
            android:launchMode="singleTop"
            android:resizeableActivity="true"
            android:screenOrientation="fullUser"
            android:supportsPictureInPicture="true"
            android:theme="@style/AppTheme.Detail" />

        <receiver
            android:name=".receiver.ShortcutReceiver"
            android:exported="false" />

        <service
            android:name="com.android.cast.dlna.dmc.DLNACastService"
            android:exported="false" />

    </application>
</manifest>