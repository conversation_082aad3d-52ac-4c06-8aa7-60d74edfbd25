<template>
  <div id="app" :class="{ 'dark': isDark }">
    <router-view />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useAppStore } from '@/stores/app'

const appStore = useAppStore()

// 计算属性
const isDark = computed(() => appStore.isDark)

// 初始化应用
appStore.initApp()
</script>

<style lang="scss">
#app {
  height: 100vh;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 全局滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--el-bg-color-page);
}

::-webkit-scrollbar-thumb {
  background: var(--el-border-color-light);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color);
}

// 暗色模式下的滚动条
.dark {
  ::-webkit-scrollbar-track {
    background: var(--el-bg-color-page);
  }
  
  ::-webkit-scrollbar-thumb {
    background: var(--el-border-color-darker);
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: var(--el-border-color-dark);
  }
}

// 响应式断点
@media (max-width: 768px) {
  #app {
    font-size: 14px;
  }
}
</style>
