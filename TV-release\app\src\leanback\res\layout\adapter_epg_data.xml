<?xml version="1.0" encoding="utf-8"?>
<com.fongmi.android.tv.ui.custom.CustomLeftRightLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="1.5dp"
    android:background="@drawable/selector_epg"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical"
    android:paddingStart="20dp"
    android:paddingTop="12dp"
    android:paddingEnd="20dp"
    android:paddingBottom="12dp">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:duplicateParentState="true"
        android:ellipsize="marquee"
        android:singleLine="true"
        android:textColor="@color/epg"
        android:textSize="16sp"
        tools:text="七龍珠" />

    <TextView
        android:id="@+id/time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:duplicateParentState="true"
        android:ellipsize="marquee"
        android:singleLine="true"
        android:textColor="@color/epg"
        android:textSize="14sp"
        tools:text="18:00 ~ 19:00" />

</com.fongmi.android.tv.ui.custom.CustomLeftRightLayout>