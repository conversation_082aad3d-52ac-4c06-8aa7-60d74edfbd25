{"version": 3, "file": "af.min.mjs", "sources": ["../../../../packages/locale/lang/af.ts"], "sourcesContent": ["export default {\n  name: 'af',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'Bevestig',\n      clear: '<PERSON>ak skoon',\n    },\n    datepicker: {\n      now: 'Nou',\n      today: 'Vandag',\n      cancel: '<PERSON><PERSON><PERSON><PERSON>',\n      clear: 'Maak skoon',\n      confirm: 'Bevestig',\n      selectDate: 'Kies datum',\n      selectTime: 'Kies tyd',\n      startDate: 'Begindatum',\n      startTime: 'Begintyd',\n      endDate: 'Einddatum',\n      endTime: 'Eindtyd',\n      prevYear: 'Previous Year', // to be translated\n      nextYear: 'Next Year', // to be translated\n      prevMonth: 'Previous Month', // to be translated\n      nextMonth: 'Next Month', // to be translated\n      year: 'Jaar',\n      month1: 'Jan',\n      month2: 'Feb',\n      month3: 'Mrt',\n      month4: 'Apr',\n      month5: 'Mei',\n      month6: 'Jun',\n      month7: 'Jul',\n      month8: 'Aug',\n      month9: 'Sep',\n      month10: 'Okt',\n      month11: 'Nov',\n      month12: 'Des',\n      // week: 'week',\n      weeks: {\n        sun: 'So',\n        mon: 'Ma',\n        tue: 'Di',\n        wed: 'Wo',\n        thu: 'Do',\n        fri: 'Vr',\n        sat: 'Sa',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Feb',\n        mar: 'Mrt',\n        apr: 'Apr',\n        may: 'Mei',\n        jun: 'Jun',\n        jul: 'Jul',\n        aug: 'Aug',\n        sep: 'Sep',\n        oct: 'Okt',\n        nov: 'Nov',\n        dec: 'Des',\n      },\n    },\n    select: {\n      loading: 'Laai',\n      noMatch: 'Geen toepaslike data',\n      noData: 'Geen data',\n      placeholder: 'Kies',\n    },\n    mention: {\n      loading: 'Laai',\n    },\n    cascader: {\n      noMatch: 'Geen toepaslike data',\n      loading: 'Laai',\n      placeholder: 'Kies',\n      noData: 'Geen data',\n    },\n    pagination: {\n      goto: 'Gaan na',\n      pagesize: '/page',\n      total: 'Totaal {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Boodskap',\n      confirm: 'Bevestig',\n      cancel: 'Kanselleer',\n      error: 'Ongeldige invoer',\n    },\n    upload: {\n      deleteTip: 'press delete to remove', // to be translated\n      delete: 'Verwyder',\n      preview: 'Voorskou',\n      continue: 'Gaan voort',\n    },\n    table: {\n      emptyText: 'Geen Data',\n      confirmFilter: 'Bevestig',\n      resetFilter: 'Herstel',\n      clearFilter: 'Alles',\n      sumText: 'Som',\n    },\n    tree: {\n      emptyText: 'Geen Data',\n    },\n    transfer: {\n      noMatch: 'Geen toepaslike data',\n      noData: 'Geen data',\n      titles: ['Lys 1', 'Lys 2'],\n      filterPlaceholder: 'Voer sleutelwoord in',\n      noCheckedFormat: '{total} items',\n      hasCheckedFormat: '{checked}/{total} gekies',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;AAAA,SAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,YAAY,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,YAAY,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,aAAa,CAAC,UAAU,CAAC,WAAW,CAAC,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,eAAe,CAAC,eAAe,CAAC,gBAAgB,CAAC,0BAA0B,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;"}