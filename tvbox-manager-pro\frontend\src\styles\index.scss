// 导入变量
@use './variables.scss' as *;

// 全局重置样式
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden; // 防止水平滚动条
}

// 清除默认样式
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: 600;
}

p {
  margin: 0;
}

ul, ol {
  margin: 0;
  padding: 0;
  list-style: none;
}

a {
  text-decoration: none;
  color: inherit;
}

// 工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-muted {
  color: var(--el-text-color-placeholder);
}

.text-primary {
  color: var(--el-color-primary);
}

.text-success {
  color: var(--el-color-success);
}

.text-warning {
  color: var(--el-color-warning);
}

.text-danger {
  color: var(--el-color-danger);
}

.text-info {
  color: var(--el-color-info);
}

// 间距工具类
@for $i from 0 through 5 {
  .m-#{$i} {
    margin: #{$i * 4}px;
  }
  
  .mt-#{$i} {
    margin-top: #{$i * 4}px;
  }
  
  .mr-#{$i} {
    margin-right: #{$i * 4}px;
  }
  
  .mb-#{$i} {
    margin-bottom: #{$i * 4}px;
  }
  
  .ml-#{$i} {
    margin-left: #{$i * 4}px;
  }
  
  .p-#{$i} {
    padding: #{$i * 4}px;
  }
  
  .pt-#{$i} {
    padding-top: #{$i * 4}px;
  }
  
  .pr-#{$i} {
    padding-right: #{$i * 4}px;
  }
  
  .pb-#{$i} {
    padding-bottom: #{$i * 4}px;
  }
  
  .pl-#{$i} {
    padding-left: #{$i * 4}px;
  }
}

// Flex布局工具类
.d-flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.justify-content-start {
  justify-content: flex-start;
}

.justify-content-end {
  justify-content: flex-end;
}

.justify-content-center {
  justify-content: center;
}

.justify-content-between {
  justify-content: space-between;
}

.justify-content-around {
  justify-content: space-around;
}

.align-items-start {
  align-items: flex-start;
}

.align-items-end {
  align-items: flex-end;
}

.align-items-center {
  align-items: center;
}

.align-items-stretch {
  align-items: stretch;
}

.flex-1 {
  flex: 1;
}

.flex-grow-1 {
  flex-grow: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

// 显示/隐藏工具类
.d-none {
  display: none;
}

.d-block {
  display: block;
}

.d-inline {
  display: inline;
}

.d-inline-block {
  display: inline-block;
}

// 响应式显示/隐藏
@media (max-width: 768px) {
  .d-md-none {
    display: none;
  }
  
  .d-sm-block {
    display: block;
  }
}

@media (min-width: 769px) {
  .d-md-block {
    display: block;
  }
  
  .d-sm-none {
    display: none;
  }
}

// 边框工具类
.border {
  border: 1px solid var(--el-border-color);
}

.border-top {
  border-top: 1px solid var(--el-border-color);
}

.border-right {
  border-right: 1px solid var(--el-border-color);
}

.border-bottom {
  border-bottom: 1px solid var(--el-border-color);
}

.border-left {
  border-left: 1px solid var(--el-border-color);
}

.border-0 {
  border: none;
}

.rounded {
  border-radius: 4px;
}

.rounded-circle {
  border-radius: 50%;
}

// 阴影工具类
.shadow-sm {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.shadow {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

.shadow-lg {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07), 0 1px 3px rgba(0, 0, 0, 0.06);
}

// 位置工具类
.position-relative {
  position: relative;
}

.position-absolute {
  position: absolute;
}

.position-fixed {
  position: fixed;
}

.position-sticky {
  position: sticky;
}

// 溢出处理
.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

.overflow-scroll {
  overflow: scroll;
}

// 文本溢出
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-break {
  word-break: break-all;
  word-wrap: break-word;
}

// 光标样式
.cursor-pointer {
  cursor: pointer;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

// 用户选择
.user-select-none {
  user-select: none;
}

.user-select-all {
  user-select: all;
}

// 过渡动画
.transition {
  transition: all 0.3s ease;
}

.transition-fast {
  transition: all 0.15s ease;
}

.transition-slow {
  transition: all 0.5s ease;
}

// 自定义滚动条
.custom-scrollbar {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: var(--el-bg-color-page);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color-light);
    border-radius: 3px;
    
    &:hover {
      background: var(--el-border-color);
    }
  }
}

// 加载状态
.loading {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
}

// 暗色模式适配
.dark {
  .loading::after {
    background: rgba(0, 0, 0, 0.8);
  }
}
