@echo off
chcp 65001 >nul
title TVBox Manager Pro - 简化启动

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    TVBox Manager Pro                         ║
echo ║                      简化启动脚本                            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 检查端口占用...
netstat -ano | findstr :8001 >nul
if %errorlevel% equ 0 (
    echo ⚠️  端口8001被占用，尝试结束相关进程...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8001') do (
        taskkill /PID %%a /F >nul 2>&1
    )
)

echo 📁 创建必要目录...
cd /d "%~dp0backend"
if not exist "data" mkdir data
if not exist "logs" mkdir logs

echo 🗄️ 初始化数据库...
python init_db.py

echo 🚀 启动后端服务 (端口: 8001)...
echo.
echo 访问地址:
echo   - 后端API: http://localhost:8001
echo   - API文档: http://localhost:8001/docs
echo.
echo 按 Ctrl+C 停止服务
echo.

python app/main.py

pause
