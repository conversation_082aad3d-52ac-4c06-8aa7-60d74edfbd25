<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="6dp"
    android:layout_marginBottom="6dp"
    android:background="@drawable/shape_group"
    android:gravity="center"
    android:orientation="horizontal"
    android:paddingStart="12dp"
    android:paddingTop="6dp"
    android:paddingEnd="12dp"
    android:paddingBottom="6dp">

    <TextView
        android:id="@+id/name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:duplicateParentState="true"
        android:ellipsize="marquee"
        android:singleLine="true"
        android:textColor="@color/group"
        android:textSize="14sp"
        tools:text="收藏" />

</LinearLayout>