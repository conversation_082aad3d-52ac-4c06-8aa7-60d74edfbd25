# TVBox Manager Pro

🚀 现代化的TVBox接口管理系统

## 项目概述

TVBox Manager Pro是一个基于FastAPI + Vue.js 3构建的现代化TVBox接口管理平台，提供接口解密、配置管理、用户权限控制等完整功能。

## 核心特性

- 🔐 **安全认证**: JWT + OAuth2认证，基于角色的权限控制
- 📡 **智能解密**: 支持Base64、AES/CBC等多种TVBox接口解密方式
- 📊 **可视化控制台**: 实时数据统计和系统监控
- 🔄 **自动订阅**: 智能订阅管理和自动更新机制
- 📱 **响应式设计**: 支持桌面、平板、手机多种设备
- 🐳 **容器化部署**: Docker + Docker Compose一键部署
- 💾 **多数据库支持**: SQLite(默认) / MySQL / PostgreSQL

## 技术栈

### 后端
- **FastAPI**: 现代Python Web框架
- **SQLAlchemy**: ORM数据库操作
- **Alembic**: 数据库迁移管理
- **Pydantic**: 数据验证和序列化
- **JWT**: 安全认证机制

### 前端
- **Vue.js 3**: 现代前端框架
- **Vite**: 快速构建工具
- **Element Plus**: UI组件库
- **Axios**: HTTP客户端
- **Pinia**: 状态管理

### 数据库
- **SQLite**: 默认数据库（开发环境）
- **MySQL**: 生产环境推荐
- **Redis**: 缓存和会话存储（可选）

## 快速开始

### 环境要求
- Python 3.8+
- Node.js 16+
- Docker & Docker Compose（可选）

### 开发环境部署

1. **克隆项目**
```bash
git clone <repository-url>
cd tvbox-manager-pro
```

2. **后端启动**
```bash
cd backend
pip install -r requirements.txt
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

3. **前端启动**
```bash
cd frontend
npm install
npm run dev
```

4. **访问应用**
- 前端: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

### Docker部署

```bash
# 一键启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 项目结构

```
tvbox-manager-pro/
├── backend/                    # 后端FastAPI服务
│   ├── app/
│   │   ├── api/               # API路由
│   │   ├── core/              # 核心配置
│   │   ├── models/            # 数据模型
│   │   ├── services/          # 业务逻辑
│   │   ├── utils/             # 工具函数
│   │   └── main.py            # 应用入口
│   ├── alembic/               # 数据库迁移
│   ├── tests/                 # 后端测试
│   └── requirements.txt       # Python依赖
├── frontend/                   # 前端Vue.js应用
│   ├── src/
│   │   ├── components/        # Vue组件
│   │   ├── views/             # 页面视图
│   │   ├── router/            # 路由配置
│   │   ├── store/             # 状态管理
│   │   ├── utils/             # 工具函数
│   │   └── main.js            # 应用入口
│   ├── public/                # 静态资源
│   └── package.json           # 前端依赖
├── docs/                       # 项目文档
├── docker/                     # Docker配置
├── scripts/                    # 部署脚本
└── README.md                   # 项目说明
```

## 主要功能

### 1. 用户管理
- 用户注册/登录
- 角色权限管理
- 个人资料设置

### 2. 控制台Dashboard
- 系统状态监控
- 数据统计图表
- 快速操作面板

### 3. 接口订阅管理
- 接口源添加/编辑
- 自动解密和解析
- 订阅状态监控
- 定时更新机制

### 4. 配置管理
- TVBox配置解析
- 配置文件管理
- 版本控制
- 批量操作

### 5. 数据管理
- 站点信息管理
- 直播源管理
- 解析器管理
- 数据导入导出

### 6. API设置
- API密钥管理
- 访问权限控制
- 频率限制设置

### 7. 系统设置
- 系统参数配置
- 日志管理
- 备份恢复

## 开发指南

详细的开发文档请参考 [docs/](./docs/) 目录：

- [技术架构设计](./docs/architecture.md)
- [API接口文档](./docs/api.md)
- [数据库设计](./docs/database.md)
- [前端开发指南](./docs/frontend.md)
- [部署运维手册](./docs/deployment.md)

## 贡献指南

欢迎提交Issue和Pull Request！

## 许可证

MIT License
