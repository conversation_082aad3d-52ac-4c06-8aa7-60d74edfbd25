<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:padding="16dp">

    <ImageView
        android:id="@+id/code"
        android:layout_width="180dp"
        android:layout_height="180dp"
        android:scaleType="centerCrop" />

    <TextView
        android:id="@+id/info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_toEndOf="@+id/code"
        android:ellipsize="end"
        android:focusable="true"
        android:lineSpacingExtra="4dp"
        android:maxLines="3"
        android:paddingStart="4dp"
        android:paddingEnd="4dp"
        android:textColor="@color/grey_700"
        android:textSize="18sp"
        tools:text="@string/push_info" />

    <com.fongmi.android.tv.ui.custom.CustomEditText
        android:id="@+id/text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/bottom"
        android:layout_alignStart="@+id/info"
        android:layout_marginBottom="10dp"
        android:hint="@string/player_ua"
        android:imeOptions="actionDone"
        android:importantForAutofill="no"
        android:inputType="text"
        android:nextFocusDown="@id/positive"
        android:singleLine="true"
        android:textSize="18sp" />

    <LinearLayout
        android:id="@+id/bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignStart="@+id/info"
        android:layout_alignBottom="@+id/code"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/positive"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:layout_weight="1"
            android:background="@drawable/selector_text"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:gravity="center"
            android:singleLine="true"
            android:text="@string/dialog_positive"
            android:textColor="@color/white"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/negative"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/selector_text"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:gravity="center"
            android:singleLine="true"
            android:text="@string/dialog_negative"
            android:textColor="@color/white"
            android:textSize="14sp" />

    </LinearLayout>
</RelativeLayout>