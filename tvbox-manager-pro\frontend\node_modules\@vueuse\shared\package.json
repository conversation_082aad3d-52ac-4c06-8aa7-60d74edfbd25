{"name": "@vueuse/shared", "version": "9.13.0", "author": "<PERSON> <https://github.com/antfu>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/vueuse/vueuse/tree/main/packages/shared#readme", "repository": {"type": "git", "url": "git+https://github.com/vueuse/vueuse.git", "directory": "packages/shared"}, "bugs": {"url": "https://github.com/vueuse/vueuse/issues"}, "keywords": ["vue", "vue-use", "utils"], "sideEffects": false, "exports": {".": {"types": "./index.d.ts", "require": "./index.cjs", "import": "./index.mjs"}, "./*": "./*"}, "main": "./index.cjs", "module": "./index.mjs", "unpkg": "./index.iife.min.js", "jsdelivr": "./index.iife.min.js", "types": "./index.d.ts", "dependencies": {"vue-demi": "*"}}