declare function __VLS_template(): {
    default?(_: {}): any;
};
declare const __VLS_component: import("vue").DefineComponent<{
    readonly tag: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "div", boolean>;
    readonly span: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 24, boolean>;
    readonly offset: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
    readonly pull: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
    readonly push: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
    readonly xs: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => number | import("./col").ColSizeObject) | (() => import("./col").ColSize) | ((new (...args: any[]) => number | import("./col").ColSizeObject) | (() => import("./col").ColSize))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
    readonly sm: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => number | import("./col").ColSizeObject) | (() => import("./col").ColSize) | ((new (...args: any[]) => number | import("./col").ColSizeObject) | (() => import("./col").ColSize))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
    readonly md: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => number | import("./col").ColSizeObject) | (() => import("./col").ColSize) | ((new (...args: any[]) => number | import("./col").ColSizeObject) | (() => import("./col").ColSize))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
    readonly lg: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => number | import("./col").ColSizeObject) | (() => import("./col").ColSize) | ((new (...args: any[]) => number | import("./col").ColSizeObject) | (() => import("./col").ColSize))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
    readonly xl: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => number | import("./col").ColSizeObject) | (() => import("./col").ColSize) | ((new (...args: any[]) => number | import("./col").ColSizeObject) | (() => import("./col").ColSize))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
}, {}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    readonly tag: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "div", boolean>;
    readonly span: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 24, boolean>;
    readonly offset: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
    readonly pull: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
    readonly push: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
    readonly xs: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => number | import("./col").ColSizeObject) | (() => import("./col").ColSize) | ((new (...args: any[]) => number | import("./col").ColSizeObject) | (() => import("./col").ColSize))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
    readonly sm: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => number | import("./col").ColSizeObject) | (() => import("./col").ColSize) | ((new (...args: any[]) => number | import("./col").ColSizeObject) | (() => import("./col").ColSize))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
    readonly md: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => number | import("./col").ColSizeObject) | (() => import("./col").ColSize) | ((new (...args: any[]) => number | import("./col").ColSizeObject) | (() => import("./col").ColSize))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
    readonly lg: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => number | import("./col").ColSizeObject) | (() => import("./col").ColSize) | ((new (...args: any[]) => number | import("./col").ColSizeObject) | (() => import("./col").ColSize))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
    readonly xl: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => number | import("./col").ColSizeObject) | (() => import("./col").ColSize) | ((new (...args: any[]) => number | import("./col").ColSizeObject) | (() => import("./col").ColSize))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
}>>, {
    readonly span: number;
    readonly push: number;
    readonly offset: number;
    readonly tag: string;
    readonly pull: number;
    readonly xs: import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => number | import("./col").ColSizeObject) | (() => import("./col").ColSize) | ((new (...args: any[]) => number | import("./col").ColSizeObject) | (() => import("./col").ColSize))[], unknown, unknown>;
    readonly sm: import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => number | import("./col").ColSizeObject) | (() => import("./col").ColSize) | ((new (...args: any[]) => number | import("./col").ColSizeObject) | (() => import("./col").ColSize))[], unknown, unknown>;
    readonly md: import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => number | import("./col").ColSizeObject) | (() => import("./col").ColSize) | ((new (...args: any[]) => number | import("./col").ColSizeObject) | (() => import("./col").ColSize))[], unknown, unknown>;
    readonly lg: import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => number | import("./col").ColSizeObject) | (() => import("./col").ColSize) | ((new (...args: any[]) => number | import("./col").ColSizeObject) | (() => import("./col").ColSize))[], unknown, unknown>;
    readonly xl: import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => number | import("./col").ColSizeObject) | (() => import("./col").ColSize) | ((new (...args: any[]) => number | import("./col").ColSizeObject) | (() => import("./col").ColSize))[], unknown, unknown>;
}>;
declare const _default: __VLS_WithTemplateSlots<typeof __VLS_component, ReturnType<typeof __VLS_template>>;
export default _default;
type __VLS_WithTemplateSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};
