"use strict";
// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.
// See <PERSON><PERSON><PERSON><PERSON> in the project root for license information.
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.BULK_SUPPRESSIONS_CLI_ESLINT_PACKAGE_NAME = exports.ESLINT_PACKAGE_NAME_ENV_VAR_NAME = exports.VSCODE_PID_ENV_VAR_NAME = exports.ESLINT_BULK_FORCE_REGENERATE_PATCH_ENV_VAR_NAME = exports.ESLINT_BULK_DETECT_ENV_VAR_NAME = exports.ESLINT_BULK_PRUNE_ENV_VAR_NAME = exports.ESLINT_BULK_ENABLE_ENV_VAR_NAME = exports.ESLINT_BULK_SUPPRESS_ENV_VAR_NAME = exports.ESLINT_BULK_PATCH_PATH_ENV_VAR_NAME = void 0;
exports.ESLINT_BULK_PATCH_PATH_ENV_VAR_NAME = 'RUSHSTACK_ESLINT_BULK_PATCH_PATH';
exports.ESLINT_BULK_SUPPRESS_ENV_VAR_NAME = 'RUSHSTACK_ESLINT_BULK_SUPPRESS';
exports.ESLINT_BULK_ENABLE_ENV_VAR_NAME = 'ESLINT_BULK_ENABLE';
exports.ESLINT_BULK_PRUNE_ENV_VAR_NAME = 'ESLINT_BULK_PRUNE';
exports.ESLINT_BULK_DETECT_ENV_VAR_NAME = '_RUSHSTACK_ESLINT_BULK_DETECT';
exports.ESLINT_BULK_FORCE_REGENERATE_PATCH_ENV_VAR_NAME = 'RUSHSTACK_ESLINT_BULK_FORCE_REGENERATE_PATCH';
exports.VSCODE_PID_ENV_VAR_NAME = 'VSCODE_PID';
exports.ESLINT_PACKAGE_NAME_ENV_VAR_NAME = '_RUSHSTACK_ESLINT_PACKAGE_NAME';
exports.BULK_SUPPRESSIONS_CLI_ESLINT_PACKAGE_NAME = (_a = process.env[exports.ESLINT_PACKAGE_NAME_ENV_VAR_NAME]) !== null && _a !== void 0 ? _a : 'eslint';
//# sourceMappingURL=constants.js.map