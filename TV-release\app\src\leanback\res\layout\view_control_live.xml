<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="@drawable/shape_controller"
    android:orientation="vertical"
    android:paddingStart="16dp"
    android:paddingTop="18dp"
    android:paddingEnd="16dp"
    android:paddingBottom="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/home"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:background="@drawable/selector_text"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:nextFocusLeft="@id/change"
            android:textColor="@color/white"
            android:textSize="14sp"
            tools:text="首頁" />

        <TextView
            android:id="@+id/action"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:background="@drawable/selector_text"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:text="@string/play"
            android:textColor="@color/white"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/player"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:background="@drawable/selector_text"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:text="@string/play_exo"
            android:textColor="@color/white"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/decode"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:background="@drawable/selector_text"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:textColor="@color/white"
            android:textSize="14sp"
            tools:text="硬解" />

        <com.fongmi.android.tv.ui.custom.CustomUpDownView
            android:id="@+id/speed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:background="@drawable/selector_text"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:visibility="gone"
            tools:text="1.00"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/scale"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:background="@drawable/selector_text"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:textColor="@color/white"
            android:textSize="14sp"
            tools:text="預設" />

        <TextView
            android:id="@+id/line"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:background="@drawable/selector_text"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:visibility="gone"
            tools:text="來源 1"
            tools:visibility="visible" />

        <com.fongmi.android.tv.ui.custom.CustomUpDownView
            android:id="@+id/text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:background="@drawable/selector_text"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:tag="3"
            android:text="@string/play_track_text"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:visibility="gone"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/audio"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:background="@drawable/selector_text"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:tag="1"
            android:text="@string/play_track_audio"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:visibility="gone"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/video"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:background="@drawable/selector_text"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:tag="2"
            android:text="@string/play_track_video"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:visibility="gone"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/invert"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:background="@drawable/selector_text"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:text="@string/play_invert"
            android:textColor="@color/text"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/across"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:background="@drawable/selector_text"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:text="@string/play_across"
            android:textColor="@color/text"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/change"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/selector_text"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:nextFocusRight="@id/home"
            android:text="@string/play_change"
            android:textColor="@color/text"
            android:textSize="14sp" />

    </LinearLayout>

    <com.fongmi.android.tv.ui.custom.CustomSeekView
        android:id="@+id/seek"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp" />

</LinearLayout>