@echo off
chcp 65001 >nul
title TVBox Manager Pro - 前端预览

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    TVBox Manager Pro                         ║
echo ║                    前端生产预览                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 检查是否已构建
if not exist "dist" (
    echo ❌ 未找到构建文件，请先运行构建
    echo 运行命令: build.bat
    pause
    exit /b 1
)

echo 🚀 启动预览服务器...
echo 📱 预览地址: http://localhost:4173
echo.
echo 按 Ctrl+C 可停止服务
echo.

npm run preview

echo.
echo 预览服务已停止，按任意键退出...
pause >nul
