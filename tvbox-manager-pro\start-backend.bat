@echo off
chcp 65001 >nul
title TVBox Manager Pro - 后端服务

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    TVBox Manager Pro                         ║
echo ║                      后端服务启动                            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python 未安装，请先安装 Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo 📁 进入后端目录...
cd /d "%~dp0backend"

echo 📦 检查并安装依赖...
if not exist "requirements.txt" (
    echo ❌ 找不到 requirements.txt 文件
    pause
    exit /b 1
)

REM 尝试安装依赖（忽略错误，可能已经安装过）
pip install -r requirements.txt >nul 2>&1

echo 🚀 启动后端服务...
echo.
python run.py

echo.
echo 服务已停止，按任意键退出...
pause >nul
