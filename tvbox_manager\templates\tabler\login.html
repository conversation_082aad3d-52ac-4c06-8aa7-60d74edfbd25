<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>登录 - TVBox Manager</title>
  
  <!-- Favicon -->
  <link rel="icon" href="{{ url_for('static', filename='img/favicon.ico') }}" type="image/x-icon">
  
  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@2.44.0/tabler-icons.min.css">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/tabler.min.css') }}">
  
  <style>
    body {
      display: flex;
      align-items: center;
      min-height: 100vh;
      background: linear-gradient(135deg, #f6f9fc 0%, #edf2f7 100%);
    }
    .card {
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
      border: none;
    }
    .card-header {
      background: transparent;
      border-bottom: 1px solid rgba(98, 105, 118, 0.16);
      padding: 1.5rem 1.5rem 1rem;
    }
    .card-body {
      padding: 2rem 1.5rem;
    }
    .form-control:focus {
      border-color: #5046e5;
      box-shadow: 0 0 0 0.25rem rgba(80, 70, 229, 0.1);
    }
    .btn-primary {
      background-color: #5046e5;
      border-color: #5046e5;
    }
    .btn-primary:hover, .btn-primary:focus {
      background-color: #3832a8;
      border-color: #3832a8;
    }
    .auth-logo {
      margin-bottom: 2rem;
      text-align: center;
    }
    .auth-logo i {
      font-size: 3rem;
      color: #5046e5;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-lg-6 col-md-8 col-sm-10">
        <div class="text-center mb-4">
          <a href="{{ url_for('interface.index') }}" class="navbar-brand">
            <i class="ti ti-device-tv me-2" style="font-size: 2.5rem;"></i>
            <span class="fs-3 fw-bold">TVBox Manager</span>
          </a>
        </div>
        
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">登录账户</h3>
          </div>
          <div class="card-body">
            {% with messages = get_flashed_messages(with_categories=true) %}
              {% if messages %}
                {% for category, message in messages %}
                  {% set alert_class = "alert-" + category if category in ['success', 'danger', 'warning', 'info'] else "alert-info" %}
                  <div class="alert {{ alert_class }} alert-dismissible mb-3" role="alert">
                    <div class="d-flex">
                      <div>
                        {% if category == 'success' %}
                          <i class="ti ti-check icon"></i>
                        {% elif category == 'danger' or category == 'error' %}
                          <i class="ti ti-alert-circle icon"></i>
                        {% elif category == 'warning' %}
                          <i class="ti ti-alert-triangle icon"></i>
                        {% else %}
                          <i class="ti ti-info-circle icon"></i>
                        {% endif %}
                      </div>
                      <div>
                        {{ message }}
                      </div>
                    </div>
                    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
                  </div>
                {% endfor %}
              {% endif %}
            {% endwith %}
            
            <form action="" method="post">
              {{ form.hidden_tag() }}
              
              <div class="mb-3">
                <label class="form-label">{{ form.email.label }}</label>
                {{ form.email(class="form-control form-control-lg", placeholder="请输入邮箱地址") }}
                {% if form.email.errors %}
                  <div class="invalid-feedback d-block">
                    {% for error in form.email.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
              </div>
              
              <div class="mb-3">
                <label class="form-label d-flex justify-content-between">
                  <span>{{ form.password.label }}</span>
                  {% if url_for('security.forgot_password') %}
                  <a href="{{ url_for('security.forgot_password') }}" class="small">忘记密码?</a>
                  {% endif %}
                </label>
                {{ form.password(class="form-control form-control-lg", placeholder="请输入密码") }}
                {% if form.password.errors %}
                  <div class="invalid-feedback d-block">
                    {% for error in form.password.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
              </div>
              
              <div class="mb-3">
                <label class="form-check">
                  {{ form.remember(class="form-check-input") }}
                  <span class="form-check-label">{{ form.remember.label }}</span>
                </label>
              </div>
              
              <div class="form-footer">
                <button type="submit" class="btn btn-primary w-100 btn-lg">登录</button>
              </div>
            </form>
          </div>
        </div>
        
        {% if security.registerable %}
        <div class="text-center text-muted mt-3">
          还没有账号? <a href="{{ url_for('security.register') }}" tabindex="-1">立即注册</a>
        </div>
        {% endif %}
        
        <div class="text-center text-muted mt-3">
          <small>&copy; 2025 TVBox Manager - 版本 1.0.0</small>
        </div>
      </div>
    </div>
  </div>
  
  <!-- JS -->
  <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>登录 - TVBox Manager</title>
  
  <!-- Favicon -->
  <link rel="icon" href="{{ url_for('static', filename='img/favicon.ico') }}" type="image/x-icon">
  
  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@2.44.0/tabler-icons.min.css">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/tabler.min.css') }}">
  
  <style>
    body {
      display: flex;
      align-items: center;
      min-height: 100vh;
      background: linear-gradient(135deg, #f6f9fc 0%, #edf2f7 100%);
    }
    .card {
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
      border: none;
    }
    .card-header {
      background: transparent;
      border-bottom: 1px solid rgba(98, 105, 118, 0.16);
      padding: 1.5rem 1.5rem 1rem;
    }
    .card-body {
      padding: 2rem 1.5rem;
    }
    .form-control:focus {
      border-color: #5046e5;
      box-shadow: 0 0 0 0.25rem rgba(80, 70, 229, 0.1);
    }
    .btn-primary {
      background-color: #5046e5;
      border-color: #5046e5;
    }
    .btn-primary:hover, .btn-primary:focus {
      background-color: #3832a8;
      border-color: #3832a8;
    }
    .auth-logo {
      margin-bottom: 2rem;
      text-align: center;
    }
    .auth-logo i {
      font-size: 3rem;
      color: #5046e5;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-lg-6 col-md-8 col-sm-10">
        <div class="text-center mb-4">
          <a href="{{ url_for('interface.index') }}" class="navbar-brand">
            <i class="ti ti-device-tv me-2" style="font-size: 2.5rem;"></i>
            <span class="fs-3 fw-bold">TVBox Manager</span>
          </a>
        </div>
        
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">登录账户</h3>
          </div>
          <div class="card-body">
            {% with messages = get_flashed_messages(with_categories=true) %}
              {% if messages %}
                {% for category, message in messages %}
                  {% set alert_class = "alert-" + category if category in ['success', 'danger', 'warning', 'info'] else "alert-info" %}
                  <div class="alert {{ alert_class }} alert-dismissible mb-3" role="alert">
                    <div class="d-flex">
                      <div>
                        {% if category == 'success' %}
                          <i class="ti ti-check icon"></i>
                        {% elif category == 'danger' or category == 'error' %}
                          <i class="ti ti-alert-circle icon"></i>
                        {% elif category == 'warning' %}
                          <i class="ti ti-alert-triangle icon"></i>
                        {% else %}
                          <i class="ti ti-info-circle icon"></i>
                        {% endif %}
                      </div>
                      <div>
                        {{ message }}
                      </div>
                    </div>
                    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
                  </div>
                {% endfor %}
              {% endif %}
            {% endwith %}
            
            <form action="" method="post">
              {{ form.hidden_tag() }}
              
              <div class="mb-3">
                <label class="form-label">{{ form.email.label }}</label>
                {{ form.email(class="form-control form-control-lg", placeholder="请输入邮箱地址") }}
                {% if form.email.errors %}
                  <div class="invalid-feedback d-block">
                    {% for error in form.email.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
              </div>
              
              <div class="mb-3">
                <label class="form-label d-flex justify-content-between">
                  <span>{{ form.password.label }}</span>
                  {% if url_for('security.forgot_password') %}
                  <a href="{{ url_for('security.forgot_password') }}" class="small">忘记密码?</a>
                  {% endif %}
                </label>
                {{ form.password(class="form-control form-control-lg", placeholder="请输入密码") }}
                {% if form.password.errors %}
                  <div class="invalid-feedback d-block">
                    {% for error in form.password.errors %}
                      {{ error }}
                    {% endfor %}
                  </div>
                {% endif %}
              </div>
              
              <div class="mb-3">
                <label class="form-check">
                  {{ form.remember(class="form-check-input") }}
                  <span class="form-check-label">{{ form.remember.label }}</span>
                </label>
              </div>
              
              <div class="form-footer">
                <button type="submit" class="btn btn-primary w-100 btn-lg">登录</button>
              </div>
            </form>
          </div>
        </div>
        
        {% if security.registerable %}
        <div class="text-center text-muted mt-3">
          还没有账号? <a href="{{ url_for('security.register') }}" tabindex="-1">立即注册</a>
        </div>
        {% endif %}
        
        <div class="text-center text-muted mt-3">
          <small>&copy; 2025 TVBox Manager - 版本 1.0.0</small>
        </div>
      </div>
    </div>
  </div>
  
  <!-- JS -->
  <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 