<template>
  <div class="api-test-page">
    <el-card>
      <template #header>
        <span>API连接测试</span>
      </template>
      
      <el-space direction="vertical" style="width: 100%">
        <el-button type="primary" @click="testSystemStats" :loading="loading">
          测试系统统计API
        </el-button>
        
        <el-button type="success" @click="testDecryptMethods" :loading="loading">
          测试解密方法API
        </el-button>
        
        <el-button type="warning" @click="testLogin" :loading="loading">
          测试登录API
        </el-button>
        
        <el-divider>测试结果</el-divider>
        
        <el-input
          v-model="result"
          type="textarea"
          :rows="10"
          placeholder="测试结果将显示在这里"
          readonly
        />
      </el-space>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { systemApi, decryptApi, authApi } from '@/api'

const loading = ref(false)
const result = ref('')

const testSystemStats = async () => {
  loading.value = true
  try {
    const response = await systemApi.getSystemStats()
    result.value = `系统统计API测试成功:\n${JSON.stringify(response.data, null, 2)}`
    ElMessage.success('系统统计API测试成功')
  } catch (error) {
    result.value = `系统统计API测试失败:\n${JSON.stringify(error.response?.data || error.message, null, 2)}`
    ElMessage.error('系统统计API测试失败')
  } finally {
    loading.value = false
  }
}

const testDecryptMethods = async () => {
  loading.value = true
  try {
    const response = await decryptApi.getDecryptMethods()
    result.value = `解密方法API测试成功:\n${JSON.stringify(response.data, null, 2)}`
    ElMessage.success('解密方法API测试成功')
  } catch (error) {
    result.value = `解密方法API测试失败:\n${JSON.stringify(error.response?.data || error.message, null, 2)}`
    ElMessage.error('解密方法API测试失败')
  } finally {
    loading.value = false
  }
}

const testLogin = async () => {
  loading.value = true
  try {
    const response = await authApi.login({
      email: '<EMAIL>',
      password: 'admin123'
    })
    result.value = `登录API测试成功:\n${JSON.stringify(response.data, null, 2)}`
    ElMessage.success('登录API测试成功')
  } catch (error) {
    result.value = `登录API测试失败:\n${JSON.stringify(error.response?.data || error.message, null, 2)}`
    ElMessage.error('登录API测试失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.api-test-page {
  padding: 20px;
}
</style>
