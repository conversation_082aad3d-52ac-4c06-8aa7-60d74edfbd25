// TVBox API 接口定义
const API_DEFINITIONS = {
    auth: {
        name: '认证API',
        icon: 'bi-shield-lock',
        apis: {
            login: {
                name: '用户登录',
                method: 'POST',
                path: '/api/v1/auth/login',
                description: '用户登录获取访问令牌 (账号密码鉴权)',
                requiresAuth: false,
                parameters: {
                    email: {
                        type: 'email',
                        required: true,
                        default: '<EMAIL>',
                        description: '用户邮箱'
                    },
                    password: {
                        type: 'password',
                        required: true,
                        default: 'admin123',
                        description: '用户密码'
                    }
                },
                example: {
                    email: '<EMAIL>',
                    password: 'admin123'
                },
                expectedResponse: {
                    success: true,
                    message: '登录成功',
                    data: {
                        access_token: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...',
                        token_type: 'bearer',
                        expires_in: 3600,
                        user: {
                            id: 1,
                            email: '<EMAIL>',
                            username: 'admin',
                            nickname: '管理员'
                        }
                    }
                }
            },
            register: {
                name: '用户注册',
                method: 'POST',
                path: '/api/v1/auth/register',
                description: '注册新用户账号',
                requiresAuth: false,
                parameters: {
                    email: {
                        type: 'email',
                        required: true,
                        default: '<EMAIL>',
                        description: '用户邮箱'
                    },
                    username: {
                        type: 'text',
                        required: true,
                        default: 'testuser',
                        description: '用户名'
                    },
                    password: {
                        type: 'password',
                        required: true,
                        default: 'test123456',
                        description: '密码'
                    },
                    nickname: {
                        type: 'text',
                        required: false,
                        default: '测试用户',
                        description: '昵称'
                    }
                }
            },
            me: {
                name: '获取当前用户',
                method: 'GET',
                path: '/api/v1/auth/me',
                description: '获取当前登录用户信息',
                requiresAuth: true,
                parameters: {}
            },
            refresh: {
                name: '刷新令牌',
                method: 'POST',
                path: '/api/v1/auth/refresh',
                description: '刷新访问令牌',
                requiresAuth: true,
                parameters: {}
            }
        }
    },
    
    interfaces: {
        name: '接口管理',
        icon: 'bi-link-45deg',
        apis: {
            list: {
                name: '获取接口列表',
                method: 'GET',
                path: '/api/v1/interfaces/',
                description: '获取TVBox接口列表',
                requiresAuth: true,
                parameters: {
                    page: {
                        type: 'number',
                        required: false,
                        default: 1,
                        description: '页码'
                    },
                    limit: {
                        type: 'number',
                        required: false,
                        default: 10,
                        description: '每页数量'
                    },
                    category: {
                        type: 'text',
                        required: false,
                        default: '',
                        description: '分类筛选'
                    },
                    search: {
                        type: 'text',
                        required: false,
                        default: '',
                        description: '搜索关键词'
                    }
                }
            },
            create: {
                name: '创建接口',
                method: 'POST',
                path: '/api/v1/interfaces/',
                description: '创建新的TVBox接口',
                requiresAuth: true,
                parameters: {
                    name: {
                        type: 'text',
                        required: true,
                        default: '测试接口',
                        description: '接口名称'
                    },
                    url: {
                        type: 'url',
                        required: true,
                        default: 'https://example.com/config.json',
                        description: '接口URL'
                    },
                    description: {
                        type: 'textarea',
                        required: false,
                        default: '这是一个测试接口',
                        description: '接口描述'
                    },
                    category: {
                        type: 'text',
                        required: false,
                        default: '影视',
                        description: '接口分类'
                    },
                    tags: {
                        type: 'text',
                        required: false,
                        default: '测试,影视',
                        description: '标签 (逗号分隔)'
                    },
                    is_public: {
                        type: 'checkbox',
                        required: false,
                        default: false,
                        description: '是否公开'
                    }
                }
            },
            get: {
                name: '获取接口详情',
                method: 'GET',
                path: '/api/v1/interfaces/{id}',
                description: '获取指定接口的详细信息',
                requiresAuth: true,
                parameters: {
                    id: {
                        type: 'text',
                        required: true,
                        default: '1',
                        description: '接口ID',
                        isPathParam: true
                    }
                }
            },
            update: {
                name: '更新接口',
                method: 'PUT',
                path: '/api/v1/interfaces/{id}',
                description: '更新指定接口信息',
                requiresAuth: true,
                parameters: {
                    id: {
                        type: 'text',
                        required: true,
                        default: '1',
                        description: '接口ID',
                        isPathParam: true
                    },
                    name: {
                        type: 'text',
                        required: false,
                        default: '更新后的接口名称',
                        description: '接口名称'
                    },
                    description: {
                        type: 'textarea',
                        required: false,
                        default: '更新后的描述',
                        description: '接口描述'
                    }
                }
            },
            delete: {
                name: '删除接口',
                method: 'DELETE',
                path: '/api/v1/interfaces/{id}',
                description: '删除指定接口',
                requiresAuth: true,
                parameters: {
                    id: {
                        type: 'text',
                        required: true,
                        default: '1',
                        description: '接口ID',
                        isPathParam: true
                    }
                }
            },
            test: {
                name: '测试接口',
                method: 'POST',
                path: '/api/v1/interfaces/{id}/test',
                description: '测试指定接口的连通性',
                requiresAuth: true,
                parameters: {
                    id: {
                        type: 'text',
                        required: true,
                        default: '1',
                        description: '接口ID',
                        isPathParam: true
                    }
                }
            },
            refresh: {
                name: '刷新接口',
                method: 'POST',
                path: '/api/v1/interfaces/{id}/refresh',
                description: '刷新指定接口的数据',
                requiresAuth: true,
                parameters: {
                    id: {
                        type: 'text',
                        required: true,
                        default: '1',
                        description: '接口ID',
                        isPathParam: true
                    }
                }
            }
        }
    },
    
    subscriptions: {
        name: '订阅管理',
        icon: 'bi-star',
        apis: {
            list: {
                name: '获取订阅列表',
                method: 'GET',
                path: '/api/v1/subscriptions/',
                description: '获取用户订阅列表',
                requiresAuth: true,
                parameters: {}
            },
            create: {
                name: '创建订阅',
                method: 'POST',
                path: '/api/v1/subscriptions/',
                description: '创建新的订阅',
                requiresAuth: true,
                parameters: {
                    name: {
                        type: 'text',
                        required: true,
                        default: '我的订阅',
                        description: '订阅名称'
                    },
                    description: {
                        type: 'textarea',
                        required: false,
                        default: '这是我的测试订阅',
                        description: '订阅描述'
                    },
                    interface_ids: {
                        type: 'text',
                        required: false,
                        default: '[]',
                        description: '接口ID数组 (JSON格式)'
                    }
                }
            },
            get: {
                name: '获取订阅详情',
                method: 'GET',
                path: '/api/v1/subscriptions/{id}',
                description: '获取指定订阅的详细信息',
                requiresAuth: true,
                parameters: {
                    id: {
                        type: 'text',
                        required: true,
                        default: '1',
                        description: '订阅ID',
                        isPathParam: true
                    }
                }
            },
            config: {
                name: '获取订阅配置',
                method: 'GET',
                path: '/api/v1/subscriptions/{id}/config',
                description: '获取订阅的TVBox配置',
                requiresAuth: false,
                parameters: {
                    id: {
                        type: 'text',
                        required: true,
                        default: '1',
                        description: '订阅ID',
                        isPathParam: true
                    }
                }
            }
        }
    },
    
    users: {
        name: '用户管理',
        icon: 'bi-people',
        apis: {
            list: {
                name: '获取用户列表',
                method: 'GET',
                path: '/api/v1/users/',
                description: '获取系统用户列表',
                requiresAuth: true,
                parameters: {
                    page: {
                        type: 'number',
                        required: false,
                        default: 1,
                        description: '页码'
                    },
                    limit: {
                        type: 'number',
                        required: false,
                        default: 10,
                        description: '每页数量'
                    }
                }
            },
            create: {
                name: '创建用户',
                method: 'POST',
                path: '/api/v1/users/',
                description: '创建新用户',
                requiresAuth: true,
                parameters: {
                    email: {
                        type: 'email',
                        required: true,
                        default: '<EMAIL>',
                        description: '用户邮箱'
                    },
                    username: {
                        type: 'text',
                        required: true,
                        default: 'newuser',
                        description: '用户名'
                    },
                    password: {
                        type: 'password',
                        required: true,
                        default: 'password123',
                        description: '密码'
                    },
                    nickname: {
                        type: 'text',
                        required: false,
                        default: '新用户',
                        description: '昵称'
                    },
                    role: {
                        type: 'select',
                        required: false,
                        default: 'user',
                        options: ['admin', 'user'],
                        description: '用户角色'
                    }
                }
            }
        }
    },
    
    system: {
        name: '系统管理',
        icon: 'bi-gear',
        apis: {
            stats: {
                name: '系统统计',
                method: 'GET',
                path: '/api/v1/system/stats',
                description: '获取系统统计信息',
                requiresAuth: true,
                parameters: {}
            },
            settings: {
                name: '系统设置',
                method: 'GET',
                path: '/api/v1/system/settings',
                description: '获取系统设置',
                requiresAuth: true,
                parameters: {}
            }
        }
    },
    
    decrypt: {
        name: '解密API',
        icon: 'bi-unlock',
        apis: {
            url: {
                name: 'URL解密',
                method: 'POST',
                path: '/api/v1/decrypt/url',
                description: '解密TVBox配置URL',
                requiresAuth: false,
                parameters: {
                    url: {
                        type: 'url',
                        required: true,
                        default: 'https://example.com/config.json',
                        description: '要解密的URL'
                    }
                }
            },
            content: {
                name: '内容解密',
                method: 'POST',
                path: '/api/v1/decrypt/content',
                description: '解密配置内容',
                requiresAuth: false,
                parameters: {
                    content: {
                        type: 'textarea',
                        required: true,
                        default: 'eyJzaXRlcyI6W10sImxpdmVzIjpbXSwicGFyc2VzIjpbXX0=',
                        description: '要解密的内容'
                    },
                    method: {
                        type: 'select',
                        required: false,
                        default: 'base64',
                        options: ['base64', 'gzip', 'aes', 'custom'],
                        description: '解密方法'
                    }
                }
            },
            methods: {
                name: '解密方法',
                method: 'GET',
                path: '/api/v1/decrypt/methods',
                description: '获取支持的解密方法',
                requiresAuth: false,
                parameters: {}
            }
        }
    }
};

// 获取所有API定义
function getAllApis() {
    const allApis = [];
    for (const [categoryKey, category] of Object.entries(API_DEFINITIONS)) {
        for (const [apiKey, api] of Object.entries(category.apis)) {
            allApis.push({
                category: categoryKey,
                categoryName: category.name,
                key: apiKey,
                ...api
            });
        }
    }
    return allApis;
}

// 根据分类获取API
function getApisByCategory(categoryKey) {
    return API_DEFINITIONS[categoryKey] || null;
}

// 获取单个API定义
function getApiDefinition(categoryKey, apiKey) {
    const category = API_DEFINITIONS[categoryKey];
    if (!category) return null;
    return category.apis[apiKey] || null;
}
