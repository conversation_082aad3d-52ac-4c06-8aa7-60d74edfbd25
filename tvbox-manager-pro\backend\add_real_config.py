#!/usr/bin/env python3
"""
添加真实的TVBox配置
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.tvbox_decryptor import TVBoxDecryptor
import sqlite3
import json

def add_real_config():
    """添加真实的TVBox配置"""
    
    # 一些公开的TVBox配置URL（这些是社区维护的开源配置）
    test_urls = [
        "https://raw.githubusercontent.com/FongMi/CatVodSpider/main/json/config.json",
        "https://ghproxy.com/https://raw.githubusercontent.com/FongMi/CatVodSpider/main/json/config.json",
        "https://raw.githubusercontent.com/liu673cn/box/main/m.json",
        "https://ghproxy.com/https://raw.githubusercontent.com/liu673cn/box/main/m.json"
    ]
    
    decryptor = TVBoxDecryptor()
    
    for url in test_urls:
        print(f"\n尝试获取配置: {url}")
        try:
            content, method = decryptor.decrypt_config_url(url)
            if content:
                print(f"获取成功，解密方法: {method}")
                
                # 尝试解析配置
                try:
                    config_data = json.loads(content)
                    spider = config_data.get('spider', 'N/A')
                    sites = config_data.get('sites', [])
                    lives = config_data.get('lives', [])
                    parses = config_data.get('parses', [])
                    
                    print(f"Spider: {spider}")
                    print(f"Sites数量: {len(sites)}")
                    print(f"Lives数量: {len(lives)}")
                    print(f"Parses数量: {len(parses)}")
                    
                    # 检查是否是真实配置
                    if len(sites) >= 5:
                        print(f"✅ 这看起来是真实配置！")
                        
                        # 更新接口1的配置
                        db_path = "data/tvbox.db"
                        conn = sqlite3.connect(db_path)
                        cursor = conn.cursor()
                        
                        cursor.execute("""
                            UPDATE interface_sources 
                            SET config_content = ?, url = ?, localized_config = NULL
                            WHERE id = 1
                        """, (content, url))
                        
                        # 清理旧的本地化文件记录
                        cursor.execute("DELETE FROM localized_files WHERE interface_id = 1")
                        
                        conn.commit()
                        conn.close()
                        
                        print(f"✅ 已更新接口1为真实配置")
                        print(f"✅ URL: {url}")
                        print(f"✅ 配置长度: {len(content)}")
                        
                        # 显示前几个sites
                        print(f"\n前3个Sites:")
                        for i, site in enumerate(sites[:3]):
                            name = site.get('name', 'N/A')
                            api = site.get('api', 'N/A')
                            print(f"  {i+1}. {name} - {api}")
                        
                        return True
                    else:
                        print(f"❌ 站点数量太少，可能不是完整配置")
                        
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
            else:
                print(f"❌ 获取失败")
                
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    print(f"\n❌ 所有URL都无法获取到有效配置")
    return False

if __name__ == "__main__":
    add_real_config()
