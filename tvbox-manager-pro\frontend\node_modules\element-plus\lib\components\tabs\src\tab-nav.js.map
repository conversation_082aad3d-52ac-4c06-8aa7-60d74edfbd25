{"version": 3, "file": "tab-nav.js", "sources": ["../../../../../../packages/components/tabs/src/tab-nav.tsx"], "sourcesContent": ["import {\n  computed,\n  defineComponent,\n  inject,\n  nextTick,\n  onMounted,\n  onUpdated,\n  ref,\n  shallowRef,\n  triggerRef,\n  watch,\n} from 'vue'\nimport {\n  useDocumentVisibility,\n  useResizeObserver,\n  useWindowFocus,\n} from '@vueuse/core'\nimport {\n  buildProps,\n  capitalize,\n  definePropType,\n  mutable,\n  throwError,\n} from '@element-plus/utils'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { ArrowLeft, ArrowRight, Close } from '@element-plus/icons-vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport TabBar from './tab-bar.vue'\nimport { tabsRootContextKey } from './constants'\n\nimport type {\n  CSSProperties,\n  ComponentPublicInstance,\n  ExtractPropTypes,\n  __ExtractPublicPropTypes,\n} from 'vue'\nimport type { TabBarInstance } from './tab-bar'\nimport type { TabPaneName, TabsPaneContext } from './constants'\n\ninterface Scrollable {\n  next?: boolean\n  prev?: number\n}\n\nexport const tabNavProps = buildProps({\n  panes: {\n    type: definePropType<TabsPaneContext[]>(Array),\n    default: () => mutable([] as const),\n  },\n  currentName: {\n    type: [String, Number],\n    default: '',\n  },\n  editable: Boolean,\n  type: {\n    type: String,\n    values: ['card', 'border-card', ''],\n    default: '',\n  },\n  stretch: Boolean,\n} as const)\n\nexport const tabNavEmits = {\n  tabClick: (tab: TabsPaneContext, tabName: TabPaneName, ev: Event) =>\n    ev instanceof Event,\n  tabRemove: (tab: TabsPaneContext, ev: Event) => ev instanceof Event,\n}\n\nexport type TabNavProps = ExtractPropTypes<typeof tabNavProps>\nexport type TabNavPropsPublic = __ExtractPublicPropTypes<typeof tabNavProps>\nexport type TabNavEmits = typeof tabNavEmits\n\nconst COMPONENT_NAME = 'ElTabNav'\nconst TabNav = defineComponent({\n  name: COMPONENT_NAME,\n  props: tabNavProps,\n  emits: tabNavEmits,\n  setup(props, { expose, emit }) {\n    const rootTabs = inject(tabsRootContextKey)\n    if (!rootTabs) throwError(COMPONENT_NAME, `<el-tabs><tab-nav /></el-tabs>`)\n\n    const ns = useNamespace('tabs')\n    const visibility = useDocumentVisibility()\n    const focused = useWindowFocus()\n\n    const navScroll$ = ref<HTMLDivElement>()\n    const nav$ = ref<HTMLDivElement>()\n    const el$ = ref<HTMLDivElement>()\n    const tabRefsMap = ref<{ [key: TabPaneName]: HTMLDivElement }>({})\n\n    const tabBarRef = ref<TabBarInstance>()\n\n    const scrollable = ref<false | Scrollable>(false)\n    const navOffset = ref(0)\n    const isFocus = ref(false)\n    const focusable = ref(true)\n    const tracker = shallowRef()\n\n    const sizeName = computed(() =>\n      ['top', 'bottom'].includes(rootTabs.props.tabPosition)\n        ? 'width'\n        : 'height'\n    )\n    const navStyle = computed<CSSProperties>(() => {\n      const dir = sizeName.value === 'width' ? 'X' : 'Y'\n      return {\n        transform: `translate${dir}(-${navOffset.value}px)`,\n      }\n    })\n\n    const scrollPrev = () => {\n      if (!navScroll$.value) return\n\n      const containerSize =\n        navScroll$.value[`offset${capitalize(sizeName.value)}`]\n      const currentOffset = navOffset.value\n\n      if (!currentOffset) return\n\n      const newOffset =\n        currentOffset > containerSize ? currentOffset - containerSize : 0\n\n      navOffset.value = newOffset\n    }\n\n    const scrollNext = () => {\n      if (!navScroll$.value || !nav$.value) return\n\n      const navSize = nav$.value[`offset${capitalize(sizeName.value)}`]\n      const containerSize =\n        navScroll$.value[`offset${capitalize(sizeName.value)}`]\n      const currentOffset = navOffset.value\n\n      if (navSize - currentOffset <= containerSize) return\n\n      const newOffset =\n        navSize - currentOffset > containerSize * 2\n          ? currentOffset + containerSize\n          : navSize - containerSize\n\n      navOffset.value = newOffset\n    }\n\n    const scrollToActiveTab = async () => {\n      const nav = nav$.value\n      if (!scrollable.value || !el$.value || !navScroll$.value || !nav) return\n\n      await nextTick()\n\n      const activeTab = tabRefsMap.value[props.currentName]\n      if (!activeTab) return\n\n      const navScroll = navScroll$.value\n      const isHorizontal = ['top', 'bottom'].includes(\n        rootTabs.props.tabPosition\n      )\n      const activeTabBounding = activeTab.getBoundingClientRect()\n      const navScrollBounding = navScroll.getBoundingClientRect()\n      const maxOffset = isHorizontal\n        ? nav.offsetWidth - navScrollBounding.width\n        : nav.offsetHeight - navScrollBounding.height\n      const currentOffset = navOffset.value\n      let newOffset = currentOffset\n\n      if (isHorizontal) {\n        if (activeTabBounding.left < navScrollBounding.left) {\n          newOffset =\n            currentOffset - (navScrollBounding.left - activeTabBounding.left)\n        }\n        if (activeTabBounding.right > navScrollBounding.right) {\n          newOffset =\n            currentOffset + activeTabBounding.right - navScrollBounding.right\n        }\n      } else {\n        if (activeTabBounding.top < navScrollBounding.top) {\n          newOffset =\n            currentOffset - (navScrollBounding.top - activeTabBounding.top)\n        }\n        if (activeTabBounding.bottom > navScrollBounding.bottom) {\n          newOffset =\n            currentOffset +\n            (activeTabBounding.bottom - navScrollBounding.bottom)\n        }\n      }\n      newOffset = Math.max(newOffset, 0)\n      navOffset.value = Math.min(newOffset, maxOffset)\n    }\n\n    const update = () => {\n      if (!nav$.value || !navScroll$.value) return\n\n      props.stretch && tabBarRef.value?.update()\n\n      const navSize = nav$.value[`offset${capitalize(sizeName.value)}`]\n      const containerSize =\n        navScroll$.value[`offset${capitalize(sizeName.value)}`]\n      const currentOffset = navOffset.value\n\n      if (containerSize < navSize) {\n        scrollable.value = scrollable.value || {}\n        scrollable.value.prev = currentOffset\n        scrollable.value.next = currentOffset + containerSize < navSize\n        if (navSize - currentOffset < containerSize) {\n          navOffset.value = navSize - containerSize\n        }\n      } else {\n        scrollable.value = false\n        if (currentOffset > 0) {\n          navOffset.value = 0\n        }\n      }\n    }\n\n    const changeTab = (event: KeyboardEvent) => {\n      let step = 0\n\n      switch (event.code) {\n        case EVENT_CODE.left:\n        case EVENT_CODE.up:\n          step = -1\n          break\n        case EVENT_CODE.right:\n        case EVENT_CODE.down:\n          step = 1\n          break\n        default:\n          return\n      }\n\n      const tabList = Array.from(\n        (\n          event.currentTarget as HTMLDivElement\n        ).querySelectorAll<HTMLDivElement>('[role=tab]:not(.is-disabled)')\n      )\n      const currentIndex = tabList.indexOf(event.target as HTMLDivElement)\n      let nextIndex = currentIndex + step\n\n      if (nextIndex < 0) {\n        nextIndex = tabList.length - 1\n      } else if (nextIndex >= tabList.length) {\n        nextIndex = 0\n      }\n\n      tabList[nextIndex].focus({ preventScroll: true }) // 改变焦点元素\n      tabList[nextIndex].click() // 选中下一个tab\n      setFocus()\n    }\n\n    const setFocus = () => {\n      if (focusable.value) isFocus.value = true\n    }\n    const removeFocus = () => (isFocus.value = false)\n\n    const setRefs = (\n      el: Element | ComponentPublicInstance | null,\n      key: TabPaneName\n    ) => {\n      tabRefsMap.value[key] = el as HTMLDivElement\n    }\n\n    const focusActiveTab = async () => {\n      await nextTick()\n\n      const activeTab = tabRefsMap.value[props.currentName]\n      activeTab?.focus({ preventScroll: true })\n    }\n\n    watch(visibility, (visibility) => {\n      if (visibility === 'hidden') {\n        focusable.value = false\n      } else if (visibility === 'visible') {\n        setTimeout(() => (focusable.value = true), 50)\n      }\n    })\n    watch(focused, (focused) => {\n      if (focused) {\n        setTimeout(() => (focusable.value = true), 50)\n      } else {\n        focusable.value = false\n      }\n    })\n\n    useResizeObserver(el$, update)\n\n    onMounted(() => setTimeout(() => scrollToActiveTab(), 0))\n    onUpdated(() => update())\n\n    expose({\n      scrollToActiveTab,\n      removeFocus,\n      focusActiveTab,\n      tabListRef: nav$,\n      tabBarRef,\n      scheduleRender: () => triggerRef(tracker),\n    })\n\n    return () => {\n      const scrollBtn = scrollable.value\n        ? [\n            <span\n              class={[\n                ns.e('nav-prev'),\n                ns.is('disabled', !scrollable.value.prev),\n              ]}\n              onClick={scrollPrev}\n            >\n              <ElIcon>\n                <ArrowLeft />\n              </ElIcon>\n            </span>,\n            <span\n              class={[\n                ns.e('nav-next'),\n                ns.is('disabled', !scrollable.value.next),\n              ]}\n              onClick={scrollNext}\n            >\n              <ElIcon>\n                <ArrowRight />\n              </ElIcon>\n            </span>,\n          ]\n        : null\n\n      const tabs = props.panes.map((pane, index) => {\n        const uid = pane.uid\n        const disabled = pane.props.disabled\n        const tabName = pane.props.name ?? pane.index ?? `${index}`\n        const closable = !disabled && (pane.isClosable || props.editable)\n        pane.index = `${index}`\n\n        const btnClose = closable ? (\n          <ElIcon\n            class=\"is-icon-close\"\n            // `onClick` not exist when generate dts\n            // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n            // @ts-ignore\n            onClick={(ev: MouseEvent) => emit('tabRemove', pane, ev)}\n          >\n            <Close />\n          </ElIcon>\n        ) : null\n\n        const tabLabelContent = pane.slots.label?.() || pane.props.label\n        const tabindex = !disabled && pane.active ? 0 : -1\n\n        return (\n          <div\n            ref={(el) => setRefs(el, tabName)}\n            class={[\n              ns.e('item'),\n              ns.is(rootTabs.props.tabPosition),\n              ns.is('active', pane.active),\n              ns.is('disabled', disabled),\n              ns.is('closable', closable),\n              ns.is('focus', isFocus.value),\n            ]}\n            id={`tab-${tabName}`}\n            key={`tab-${uid}`}\n            aria-controls={`pane-${tabName}`}\n            role=\"tab\"\n            aria-selected={pane.active}\n            tabindex={tabindex}\n            onFocus={() => setFocus()}\n            onBlur={() => removeFocus()}\n            onClick={(ev: MouseEvent) => {\n              removeFocus()\n              emit('tabClick', pane, tabName, ev)\n            }}\n            onKeydown={(ev: KeyboardEvent) => {\n              if (\n                closable &&\n                (ev.code === EVENT_CODE.delete ||\n                  ev.code === EVENT_CODE.backspace)\n              ) {\n                emit('tabRemove', pane, ev)\n              }\n            }}\n          >\n            {...[tabLabelContent, btnClose]}\n          </div>\n        )\n      })\n\n      // By tracking the value property, we can schedule a job to re-render `TabNav` when needed.\n      // Unlike `instance.update`, the scheduler ensures the job is queued only once even if we trigger it multiple times.\n      tracker.value\n\n      return (\n        <div\n          ref={el$}\n          class={[\n            ns.e('nav-wrap'),\n            ns.is('scrollable', !!scrollable.value),\n            ns.is(rootTabs.props.tabPosition),\n          ]}\n        >\n          {scrollBtn}\n          <div class={ns.e('nav-scroll')} ref={navScroll$}>\n            {props.panes.length > 0 ? (\n              <div\n                class={[\n                  ns.e('nav'),\n                  ns.is(rootTabs.props.tabPosition),\n                  ns.is(\n                    'stretch',\n                    props.stretch &&\n                      ['top', 'bottom'].includes(rootTabs.props.tabPosition)\n                  ),\n                ]}\n                ref={nav$}\n                style={navStyle.value}\n                role=\"tablist\"\n                onKeydown={changeTab}\n              >\n                {...[\n                  !props.type ? (\n                    <TabBar\n                      ref={tabBarRef}\n                      tabs={[...props.panes]}\n                      tabRefs={tabRefsMap.value}\n                    />\n                  ) : null,\n                  tabs,\n                ]}\n              </div>\n            ) : null}\n          </div>\n        </div>\n      )\n    }\n  },\n})\n\nexport type TabNavInstance = InstanceType<typeof TabNav> & {\n  scrollToActiveTab: () => Promise<void>\n  removeFocus: () => void\n  focusActiveTab: () => void\n  scheduleRender: () => void\n  tabListRef: HTMLDivElement | undefined\n  tabBarRef: TabBarInstance | undefined\n}\n\nexport default TabNav\n"], "names": ["tabNavProps", "buildProps", "panes", "type", "definePropType", "Array", "default", "mutable", "currentName", "String", "Number", "editable", "Boolean", "values", "stretch", "tabNavEmits", "tabClick", "tab", "tabName", "ev", "Event", "tabRemove", "COMPONENT_NAME", "TabNav", "defineComponent", "name", "props", "emits", "expose", "emit", "inject", "tabsRootContextKey", "throwError", "ns", "useNamespace", "visibility", "useDocumentVisibility", "focused", "useWindowFocus", "navScroll$", "ref", "nav$", "el$", "tabRefsMap", "tabBarRef", "scrollable", "navOffset", "isFocus", "focusable", "tracker", "shallowRef", "sizeName", "computed", "includes", "rootTabs", "tabPosition", "navStyle", "dir", "value", "transform", "scrollPrev", "containerSize", "capitalize", "currentOffset", "newOffset", "scrollNext", "scrollToActiveTab", "activeTab", "navScroll", "navScrollBounding", "maxOffset", "isHorizontal", "offsetWidth", "activeTabBounding", "right", "top", "bottom", "max", "update", "navSize", "prev", "changeTab", "event", "step", "EVENT_CODE", "left", "currentIndex", "tabList", "nextIndex", "preventScroll", "click", "setFocus", "watch", "key", "focusActiveTab", "focus", "useResizeObserver", "triggerRef", "_createVNode", "ElIcon", "ArrowLeft", "setTimeout", "onUpdated", "ArrowRight", "tabListRef", "scheduleRender", "e", "is", "pane", "index", "uid", "disabled", "isClosable", "btnClose", "closable", "setRefs", "el", "tab<PERSON>abel<PERSON><PERSON>nt", "TabBar"], "mappings": ";;;;;;;;;;;;;;;;;AA6CaA,MAAAA,WAAW,GAAGC,kBAAU,CAAC;AACpCC,EAAAA,KAAK,EAAE;AACLC,IAAAA,IAAI,EAAEC,sBAAc,CAAoBC,KAApB,CADf;AAELC,IAAAA,OAAO,EAAE,MAAMC,kBAAO,CAAC,EAAD,CAAA;GAHY;AAKpCC,EAAAA,WAAW,EAAE;AACXL,IAAAA,IAAI,EAAE,CAACM,MAAD,EAASC,MAAT,CADK;AAEXJ,IAAAA,OAAO,EAAE,EAAA;GAPyB;AASpCK,EAAAA,QAAQ,EAAEC,OAT0B;AAUpCT,EAAAA,IAAI,EAAE;AACJA,IAAAA,IAAI,EAAEM,MADF;AAEJI,IAAAA,MAAM,EAAE,CAAC,MAAD,EAAS,aAAT,EAAwB,EAAxB,CAFJ;AAGJP,IAAAA,OAAO,EAAE,EAAA;GAbyB;AAepCQ,EAAAA,OAAO,EAAEF,OAAAA;AAf2B,CAAD,EAA9B;AAkBA,MAAMG,WAAW,GAAG;EACzBC,QAAQ,EAAE,CAACC,GAAD,EAAuBC,OAAvB,EAA6CC,EAA7C,KACRA,EAAE,YAAYC,KAFS;AAGzBC,EAAAA,SAAS,EAAE,CAACJ,GAAD,EAAuBE,EAAvB,KAAqCA,EAAE,YAAYC,KAAAA;AAHrC,EAApB;AAUP,MAAME,cAAc,GAAG,UAAvB,CAAA;AACMC,MAAAA,MAAM,GAAGC,mBAAe,CAAC;AAC7BC,EAAAA,IAAI,EAAEH,cADuB;AAE7BI,EAAAA,KAAK,EAAE1B,WAFsB;AAG7B2B,EAAAA,KAAK,EAAEZ,WAHsB;;IAIxB;IAAUa,IAAF;AAAUC,GAAAA,EAAAA;AAAV,IAAkB,MAAA,QAAA,GAAAC,UAAA,CAAAC,4BAAA,CAAA,CAAA;AAC7B,IAAA,IAAA,CAAA,QAAc;MACVC,iBAAJ,cAAyB,EAAA,CAAA;AAEzB,IAAA,MAAMC,EAAE,GAAGC,kBAAY,CAAC,MAAD,CAAvB,CAAA;IACA,MAAMC,UAAU,GAAGC,0BAAqB,EAAxC,CAAA;IACA,MAAMC,OAAO,GAAGC,mBAAc,EAA9B,CAAA;IAEA,MAAMC,UAAU,GAAGC,OAAG,EAAtB,CAAA;IACA,MAAMC,IAAI,GAAGD,OAAG,EAAhB,CAAA;IACA,MAAME,GAAG,GAAGF,OAAG,EAAf,CAAA;AACA,IAAA,MAAMG,UAAU,GAAGH,OAAG,CAAyC,EAAzC,CAAtB,CAAA;IAEA,MAAMI,SAAS,GAAGJ,OAAG,EAArB,CAAA;AAEA,IAAA,MAAMK,UAAU,GAAGL,OAAG,CAAqB,KAArB,CAAtB,CAAA;AACA,IAAA,MAAMM,SAAS,GAAGN,OAAG,CAAC,CAAD,CAArB,CAAA;AACA,IAAA,MAAMO,OAAO,GAAGP,OAAG,CAAC,KAAD,CAAnB,CAAA;AACA,IAAA,MAAMQ,SAAS,GAAGR,OAAG,CAAC,IAAD,CAArB,CAAA;IACA,MAAMS,OAAO,GAAGC,cAAU,EAA1B,CAAA;IAEA,MAAMC,QAAQ,GAAGC,YAAQ,CAAC,MACxB,CAAC,KAAD,EAAQ,QAAR,CAAA,CAAkBC,QAAlB,CAA2BC,QAAQ,CAAC5B,KAAT,CAAe6B,WAA1C,CACI,GAAA,OADJ,GAEI,QAHmB,CAAzB,CAAA;AAKA,IAAA,MAAMC,QAAQ,GAAGJ,YAAQ,CAAgB,MAAM;MAC7C,MAAMK,GAAG,GAAGN,QAAQ,CAACO,KAAT,KAAmB,OAAnB,GAA6B,GAA7B,GAAmC,GAA/C,CAAA;MACA,OAAO;AACLC,QAAAA,SAAS,EAAG,CAAWF,SAAAA,EAAAA,GAAI,CAAIX,EAAAA,EAAAA,SAAS,CAACY,KAAM,CAAA,GAAA,CAAA;OADjD,CAAA;AAGD,KALwB,CAAzB,CAAA;;MAOME,IAAAA,CAAAA,gBAAmB;AACvB,QAAA,OAAKrB;AAEL,MAAA,MAAMsB,aAAa,GACjBtB,UAAU,CAACmB,KAAX,CAAkB,CAAA,MAAA,EAAQI,kBAAU,CAACX,QAAQ,CAACO,KAAV,CAAiB,EAArD,CADF,CAAA;AAEA,MAAA,MAAMK,aAAa,GAAGjB,SAAS,CAACY,KAAhC,CAAA;MAEA,IAAI,CAACK,aAAL;QAEMC,OAAAA;MAGNlB,MAAS,SAAT,GAAkBkB,aAAlB,GAAA,aAAA,GAAA,aAAA,GAAA,aAAA,GAAA,CAAA,CAAA;MAZF,SAAA,CAAA,KAAA,GAAA,SAAA,CAAA;;IAeA,MAAMC,UAAU,GAAG,MAAM;MACvB,IAAI,CAAC1B,UAAU,CAACmB,KAAZ,IAAqB,CAACjB,IAAI,CAACiB,KAA/B;AAEA,QAAA,OAAa;AACb,MAAA,MAAMG,OAAa,GAAA,IAAA,CAAA,KACP,CAAA,CAAA,0BAAQ,CAAA,QAAkB,CAAA,KAAS,CAAA,CAAA,CAAA,CAAA,CAAA;AAC/C,MAAA,MAAME,aAAa,GAAGjB,UAAUY,CAAhC,KAAA,CAAA,CAAA,MAAA,EAAAI,kBAAA,CAAA,QAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAEA,MAAA,MAAW,aAAP,GAAA,SAA2BD;AAE/B,MAAA,IAAA,uBACYE,IAAAA,aAAgBF;QAInB,OAAA;MAfX,MAAA,SAAA,GAAA,OAAA,GAAA,aAAA,GAAA,aAAA,GAAA,CAAA,GAAA,aAAA,GAAA,aAAA,GAAA,OAAA,GAAA,aAAA,CAAA;;KAkBMK,CAAAA;AACJ,IAAA,MAAA,iBAAA,GAAA,YAAA;AACA,MAAA,MAAe,GAAA,GAAA,IAACR,CAAZ,KAAA,CAAA;AAEJ,MAAA,IAAA,CAAA,UAAA,CAAA,KAAA,IAAA,CAAA,GAAA,CAAA,KAAA,IAAA,CAAA,UAAA,CAAA,KAAA,IAAA,CAAA,GAAA;QAEMS,OAAAA;MACN,kBAAA,EAAgB,CAAA;AAEhB,MAAA,MAAMC,SAAS,GAAG7B,UAAU,CAACmB,KAA7B,CAAA,KAAA,CAAA,WAAA,CAAA,CAAA;AACA,MAAA,IAAA,CAAA,SAAkB;AAGlB,QAAA,OAAuB;AACvB,MAAA,MAAMW,SAAiB,GAAA,UAAY,CAAA,KAAA,CAAA;AACnC,MAAA,MAAMC,YAAYC,GAAAA,CAAAA,KAAAA,EAAAA,QACVC,WAAJ,QAAkBH,CAAAA,KAAAA,CAAAA,WADQ;AAG9B,MAAA,MAAMN,iBAAyB,GAAA,SAA/B,CAAA,qBAAA,EAAA,CAAA;MACA,MAAIC,iBAAJ,GAAA,SAAA,CAAA,qBAAA,EAAA,CAAA;;AAEA,MAAA,mBAAkB,GAAA,SAAA,CAAA,KAAA,CAAA;AAChB,MAAA,IAAA;UACEA,YACED,EAAa;AAChB,QAAA,IAAA,iBAAA,CAAA,IAAA,GAAA,iBAAA,CAAA,IAAA,EAAA;;AACD,SAAA;YACW,uBACM,GAAGU,iBAAiB,CAACC,KAAlC,EAAA;AACH,UAAA,SAAA,GAAA,aAAA,GAAA,iBAAA,CAAA,KAAA,GAAA,iBAAA,CAAA,KAAA,CAAA;AACF,SAAM;AACL,OAAA;YACW,iBACM,CAAA,GAAA,GAAA,iBAAqB,CAAA,GAACC;AACtC,UAAA,SAAA,GAAA,aAAA,IAAA,iBAAA,CAAA,GAAA,GAAA,iBAAA,CAAA,GAAA,CAAA,CAAA;;AACD,QAAA,IAAIF,iBAAiB,CAACG,MAAlB,GAA2BP,iBAAiB,CAACO,MAAjD,EAAyD;UACvDZ,SAAS,GACPD,aAAa,IACZU,iBAAiB,CAACG,MAAlB,GAA2BP,iBAAiB,CAACO,MADjC,CADf,CAAA;AAGD,SAAA;AACF,OAAA;;MACDZ,SAAS,CAAA,QAAQa,IAAL,CAAA,GAAA,CAAA,SAAZ,EAAA,SAAA,CAAA,CAAA;MACA/B;IACD,MA3CD,MAAA,GAAA,MAAA;;MA6CMgC,IAAAA,CAAAA,IAAAA,CAAM,KAAS,IAAA,CAAA,UAAA,CAAA,KAAA;QACf,OAAK;MAETpD,KAAK,CAACZ,OAAN,KAAiB8B,CAAAA,EAAAA,GAAAA,SAAA,CAAiBkC,UAAlC,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAA,EAAA,CAAA,CAAA;AAEA,MAAA,MAAMC,OAAO,GAAGtC,IAAI,CAACiB,KAAL,CAAY,CAAA,MAAA,EAAQI,kBAAU,CAACX,QAAQ,CAACO,KAAV,CAAiB,EAA/C,CAAhB,CAAA;AACA,MAAA,MAAMG,aAAa,GACjBtB,UAAU,CAACmB,KAAX,CAAkB,CAAA,MAAA,EAAQI,kBAAU,CAACX,QAAQ,CAACO,KAAV,CAAiB,EAArD,CADF,CAAA;AAEA,MAAA,MAAMK,aAAa,GAAGjB,SAAS,CAACY,KAAhC,CAAA;;QAEIG,UAAAA,CAAAA,KAAgBkB,GAAAA,UAAS,CAAA,KAAA,IAAA,EAAA,CAAA;AAC3BlC,QAAAA,UAAU,CAACa,KAAX,CAAA;AACAb,QAAAA,UAAU,CAACa,KAAX,CAAiBsB,IAAjB,GAAwBjB,aAAxB,GAAA,aAAA,GAAA,OAAA,CAAA;QACAlB,IAAU,OAACa,gBAA0B,GAAA,aAAb,EAAA;;AACxB,SAAA;AACEZ,OAAAA,MAAAA;AACD,QAAA,UAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AACF,QAAM,IAAA,aAAA,GAAA,CAAA,EAAA;UACK,SAACY,CAAX,KAAA,GAAA,CAAA,CAAA;;;;AAGC,IAAA,MAAA,SAAA,GAAA,CAAA,KAAA,KAAA;AACF,MAAA,IAAA,IAAA,GAAA,CAAA,CAAA;MAtBH,QAAA,KAAA,CAAA,IAAA;;QAyBMuB,KAAAA,eAAaC,CAAAA,EAAAA;UACbC,IAAI,GAAG,CAAX,CAAA,CAAA;;QAEQD,KAAAA,eAAR,CAAA,KAAA,CAAA;QACE,KAAKE,eAAU,CAACC,IAAhB;UACKD,IAAAA,GAAAA,CAAAA,CAAAA;UACHD,MAAI;AACJ,QAAA;;;YAEGC,OAAAA,GAAAA,KAAL,CAAA,IAAA,CAAA,KAAA,CAAA,aAAA,CAAA,gBAAA,CAAA,8BAAA,CAAA,CAAA,CAAA;AACED,MAAAA,MAAAA,YAAA,GAAA,OAAA,CAAA,OAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AACA,MAAA,IAAA,SAAA,GAAA,YAAA,GAAA,IAAA,CAAA;;AACF,QAAA,SAAA,GAAA,OAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AACE,OAAA,MAAA,IAAA,SAAA,IAAA,OAAA,CAAA,MAAA,EAAA;AAVJ,QAAA,SAAA,GAAA,CAAA,CAAA;;AAaA,MAAA,OAAa,CAAA,SAAQ,CAAA,CAAA;QAKfG,aAAAA,EAAAA,IAAeC;AACrB,OAAA,CAAA,CAAA;;MAEA,QAAIC,EAAAA,CAAAA;AACFA,KAAAA,CAAAA;AACD,IAAA,cAAUA,GAAAA;AACTA,MAAAA,IAAAA,UAAY,KAAZ;AACD,QAAA,OAAA,CAAA,KAAA,GAAA,IAAA,CAAA;;AAEDD,IAAAA,MAAAA,WAAQC,GAAR,MAAA,OAAyB,CAAA,KAAA,GAAA,KAAA,CAAA;AAAEC,IAAAA,MAAAA,OAAAA,GAAAA,CAAa,EAAE,EAAA,GAAA,KAAA;AAAjB,MAAA,UAAyB,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA,EAAA,CAAA;;AAClDF,IAAAA,MAAAA,cAAA,GAAmBG,YAAQ;;MAC3BC,MAAQ,SAAA,GAAA,UAAA,CAAA,KAAA,CAAA,KAAA,CAAA,WAAA,CAAA,CAAA;MAhCV,SAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,SAAA,CAAA,KAAA,CAAA;;OAmCMA,CAAAA,CAAAA;MACJ;IACDC,SAFD,CAAA,UAAA,EAAA,CAAA,WAAA,KAAA;;AAGA,QAAA,eAAoB,GAAA;;AAEpB,QAAA,UAAgB,CAAA,MAEdC,SACG,CAAA,KAAA,GAAA,IAAA,EAAA,EAAA,CAAA,CAAA;AACHlD,OAAAA;KAJF,CAAA,CAAA;;MAOMmD,IAAAA,QAAAA,EAAAA;AACJ,QAAA,iBAAA,SAAA,CAAA,KAAA,GAAA,IAAA,EAAA,EAAA,CAAA,CAAA;OAEM3B,MAAAA;QACG,SAAE4B,MAAM,GAAA,KAAA,CAAA;AAAEN,OAAAA;MAAF,CAAjB;IACDO,sBALD,CAAA,GAAA,EAAA,MAAA,CAAA,CAAA;;AAOAJ,IAAAA,aAAMzD,CAAAA,MAAD,MAAcA,EAAAA,CAAAA,CAAAA;UACbA,CAAAA;uBACF;AACD,MAAA,WAAUA;;AAEV,MAAA,UAAA,EAAA,IAAA;AACF,MAND,SAAA;AAOAyD,MAAAA,cAAgBvD,EAAAA,MAAY4D,cAAA,CAAA,OAAA,CAAA;AAC1B,KAAA,CAAA,CAAA;WACY,MAAA;AACX,MAAA,MAAM,SAAA,GAAA,UAAA,CAAA,KAAA,GAAA,CAAAC,eAAA,CAAA,MAAA,EAAA;QACLlD,OAAS,EAAA,CAACU,EAAV,CAAA,CAAA,CAAA,UAAA,CAAA,EAAA,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,CAAA,UAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AACD,QAAA,SAAA,EAAA,UAAA;AACF,OAND,EAAA,CAAAwC,eAAA,CAAAC,cAAA,EAAA,IAAA,EAAA;AAQAH,QAAAA,OAAAA,EAAAA,MAAkBtD,CAAAA,eAAlB,CAAA0D,kBAAA,EAAA,IAAA,EAAA,IAAA,CAAA,CAAA;OAES,CAAA,CAAA,CAAA,EAAAF,eAAOG,CAAAA,MAAW,EAAA;AAC3BC,QAAAA,OAAU,EAAA,CAAA,EAAMxB,CAAM,CAAA,CAAA,UAAtB,CAAA,EAAA,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,CAAA,UAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AAEAlD,QAAAA,SAAO,EAAA,UAAA;OAAA,EAAA,CAAAsE,eAAA,CAAAC,cAAA,EAAA,IAAA,EAAA;QAAA,OAAA,EAAA,MAAA,CAAAD,eAAA,CAAAK,mBAAA,EAAA,IAAA,EAAA,IAAA,CAAA,CAAA;OAAA,CAAA,CAAA,CAAA,CAAA,GAAA,IAAA,CAAA;AAILC,MAAAA,MAAAA,IAAU,GAJL,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,IAAA,EAAA,KAAA,KAAA;QAAA,IAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,CAAA;AAMLC,QAAAA,MAAAA,GAAAA,GAAc,IAAE,CAAA,GAAMR,CAAU;AAN3B,QAAP,MAAA,QAAA,GAAA,IAAA,CAAA,KAAA,CAAA,QAAA,CAAA;AASA,QAAA,MAAa,OAAA,GAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAA,CAAA,KAAA,CAAA,IAAA,KAAA,IAAA,GAAA,EAAA,GAAA,IAAA,CAAA,KAAA,KAAA,IAAA,GAAA,EAAA,GAAA,CAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AACX,QAAA,MAAe,QAAA,GAAa,CAAA,QAAA,SACxB,CAAA,UAAA,IAAA,KAAA,CAAA,QAAA,CAAA,CAAA;QAAA,IAEW,CAAA,KACLhE,GAAGyE,CAAH,EAAA,KAAA,CAAA,CAAA,CAAA;QAHN,MAMa9C,QAAAA,GAAAA,QAAAA,GAAAA,eAAAA,CAAAA,cAAAA,EAAAA;AANb,UAAA,OAAA,EAAA,eAAA;AAAA,UAAA,SAAA,EAAA,CAAA,EAAA,KAAA,IAAA,CAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA;AAAA,SAAA,EAAA;UAaW,OAAA,QACL,CAAAsC,eACE,CAACS,cAAG,EAAA,IAAA,EAAN,IAAmB9D,CAAAA,CAAAA;SAEZoB,CAAAA,GAAAA,IAAAA,CAAAA;AAjBb,QAAA,MAAA,eAAA,GAAA,CAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAA,CAAA,KAAA,EAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,KAAA,IAAA,CAAA,KAAA,CAAA,KAAA,CAAA;AAAA,QAAA,MAAA,QAAA,GAAA,CAAA,QAAA,IAAA,IAAA,CAAA,MAAA,GAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AAAA,QAAA,OADJiC,eAAA,CAAA,KAAA,EAAA;AA2BA,UAAA,KAAU,EAAA,CAAA,EAAQ,KAAChG,OAAN,CAAA,EAAgB,EAAC0G,OAAMC,CAAAA;AAClC,UAAA,OAAS,EAAA,CAAGD,EAAI,CAAA,CAAA,CAACE,MAAjB,CAAA,EAAA,EAAA,CAAA,EAAA,CAAA,QAAA,CAAA,KAAA,CAAA,WAAA,CAAA,EAAA,EAAA,CAAA,EAAA,CAAA,QAAA,EAAA,IAAA,CAAA,MAAA,CAAA,EAAA,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,QAAA,CAAA,EAAA,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,QAAA,CAAA,EAAA,EAAA,CAAA,EAAA,CAAA,OAAA,EAAA,OAAA,CAAA,KAAA,CAAA,CAAA;AACA,UAAA,IAAMC,SAAWH,OAAKlF,CAAAA,CAAAA;AACtB,UAAA,KAAa,EAAA,CAAA,IAAA,EAAGkF,GAAI,CAAA,CAAClF;AACrB,UAAA,eAAiB,EAACqF,CAAAA,KAAAA,EAAaH,OAAAA,CAAI,CAACI;AACpCJ,UAAAA,MAAKC,EAAL,KAAc;UAERI,eAAWC,EAAQ,IAAA,CAAA,MAAA;AAAA,UAAA,UAAA,EAAA,QAAA;UAAA,SAMX/F,EAAAA,MAAmBU;AANR,UAAA,QAAA,EAAA,MAAA,WAAA,EAAA;AAAA,UAAA,SAAA,EAAA,CAAA,EAAA,KAAA;AAAA,YAAA,WAAzB,EAAA,CAAA;AAYA,YAAA,IAAqB,CAAA,UAAA,EAAA,IAAO,EAAA,OAAJ,EAAA,EAAA,CAAA,CAAA;AACxB,WAAA;AAEA,UAAA,WAAA,EAAA,CAAA,EAAA,KAAA;AAAA,YAAA,IAAA,QAEiBsF,KAAAA,EAAQC,CAAD,wBAFxB,CAAA,MAAA,IAAA,EAAA,CAAA,IAAA,KAAAhC,eAAA,CAAA,SAAA,CAAA,EAAA;cAGW,IAAA,CAAA,WACL,EADK,IAEH,EAACuB,EAAGrD,CAAAA,CAAAA;aAMH;WACC;WACU,CAAA,GAAA,CAAA,eAAA,EAAA,SAAe,CAbnC,CAAA,CAAA;AAAA,OAAA,CAAA,CAAA;aAemBsD,CAAAA,KAAAA,CAAAA;AAfnB,MAAA,OAAAV,eAAA,CAAA,KAAA,EAAA;aAiBa,EAAA,GAAA;eACD,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,eAlBZ,CAAA,EAAA,CAAA,YAAA,EAAA,CAAA,CAAA,UAAA,CAAA,KAAA,CAAA,EAAA,EAAA,CAAA,EAAA,CAAA,QAAA,CAAA,KAAA,CAAA,WAAA,CAAA,CAAA;AAAA,OAAA,EAAA,CAAA,SAAA,EAmBc/E,eAAmB,CAAA,KAAA,EAAA;eAChB,EAAA,EAAA,CAAA,CAAA,CAAA,YAAA,CAAA;aACP,EAAA;UACL,KAtBL,CAAA,KAAA,CAAA,MAAA,GAAA,CAAA,GAAA+E,eAAA,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAuBgB/E,KAAsB,CAAA,EAAA,EAAA,CAAA,EAAA,CAAA,QAAA,CAAA,KAAA,CAAA,WAAA,CAAA,EAAA,EAAA,CAAA,EAAA,CAAA,SAAA,EAAA,KAAA,CAAA,OAAA,IAAA,CAAA,KAAA,EAAA,QAAA,CAAA,CAAA,QAAA,CAAA,QAAA,CAAA,KAAA,CAAA,WAAA,CAAA,CAAA,CAAA;AAChC,QAAA,KAAA,EAAA;AAKEU,QAAAA,OAAAA,EAAAA,QAAI,CAAA,KAAA;AACL,QAAA,MAAA,EAAA,SAAA;AACF,QAAA,WAAA,EAAA,SAAA;AA/BL,OAAA,EAAA,CAAA,GAAA,CAAA,CAiCQ,KAACwF,CAAAA,IAAAA,GAAAA,eAAiBJ,CAAlBK,iBAjCR,EAAA;QAtBF,KA4DA,EAAA,SAAA;AACA,QAAA,MAAA,EAAA,CAAA,GAAA,KAAA,CAAA,KAAA,CAAA;;AACArE,OAAAA,EAAAA,IAAO,CAACS,GAAR,IAAA,EAAA,IAAA,CAAA,CAAA,CAAA,GAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAEA,KAAA,CAAA;AAAA,GAAA;AAAA,CAAA;;;;;;"}