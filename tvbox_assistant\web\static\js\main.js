/**
 * TVBox助手 - 主JavaScript文件
 * 提供前端界面的交互功能
 */

// 页面加载完成后执行
document.addEventListener("DOMContentLoaded", function() {
    // 初始化提示工具
    initTooltips();
    
    // 初始化Flash消息
    initFlashMessages();
    
    // 初始化表单验证
    initFormValidation();
});

/**
 * 初始化Bootstrap提示工具
 */
function initTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * 初始化消息提示
 */
function initFlashMessages() {
    // 自动隐藏Flash消息
    setTimeout(function() {
        const flashMessages = document.querySelectorAll('.alert-dismissible.auto-close');
        flashMessages.forEach(function(message) {
            const alert = new bootstrap.Alert(message);
            alert.close();
        });
    }, 5000);
}

/**
 * 初始化表单验证
 */
function initFormValidation() {
    // 获取所有需要验证的表单
    const forms = document.querySelectorAll('.needs-validation');
    
    // 添加提交事件监听
    Array.from(forms).forEach(function (form) {
        form.addEventListener('submit', function (event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        }, false);
    });
}

/**
 * 显示加载状态
 * @param {HTMLElement} element - 要显示加载状态的元素
 * @param {string} message - 加载提示消息
 */
function showLoading(element, message = "加载中...") {
    element.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">${message}</p>
        </div>
    `;
}

/**
 * 显示错误消息
 * @param {HTMLElement} element - 要显示错误消息的元素
 * @param {string} message - 错误消息内容
 */
function showError(element, message) {
    element.innerHTML = `
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle me-2"></i>${message}
        </div>
    `;
}

/**
 * 显示成功消息
 * @param {HTMLElement} element - 要显示成功消息的元素
 * @param {string} message - 成功消息内容
 */
function showSuccess(element, message) {
    element.innerHTML = `
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>${message}
        </div>
    `;
}

/**
 * 转义HTML字符串
 * @param {string} text - 需要转义的文本
 * @returns {string} - 转义后的文本
 */
function escapeHtml(text) {
    if (typeof text !== 'string') {
        return JSON.stringify(text);
    }
    return text
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} - 格式化后的大小
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 格式化时间戳
 * @param {number} timestamp - 时间戳
 * @returns {string} - 格式化后的时间
 */
function formatTimestamp(timestamp) {
    const date = new Date(timestamp * 1000);
    return date.toLocaleString();
}

/**
 * 获取TVBox站点类型说明
 * @param {number} typeCode - 类型代码
 * @returns {string} - 类型说明
 */
function getTvboxSiteType(typeCode) {
    const typeMap = {
        0: "普通",
        1: "VIP解析",
        3: "LIVE",
        4: "本地站点"
    };
    return typeMap[typeCode] || `未知(${typeCode})`;
}

/**
 * 解析线路信息
 * @param {object} data - 线路数据
 * @param {function} callback - 回调函数
 */
function parseLineData(data, callback) {
    const result = {
        sites: data.sites || [],
        lives: data.lives || [],
        parses: data.parses || [],
        spider: data.spider || null,
        wallpaper: data.wallpaper || null,
        rules: data.rules || []
    };
    
    // 处理站点信息
    const sitesInfo = {
        count: result.sites.length,
        typeStats: {}
    };
    
    result.sites.forEach(site => {
        const typeCode = site.type || 0;
        sitesInfo.typeStats[typeCode] = (sitesInfo.typeStats[typeCode] || 0) + 1;
    });
    
    // 处理直播源信息
    const livesInfo = {
        count: result.lives.length,
        channelsCount: 0
    };
    
    result.lives.forEach(live => {
        livesInfo.channelsCount += (live.channels || []).length;
    });
    
    // 处理解析器信息
    const parsesInfo = {
        count: result.parses.length,
        typeStats: {
            0: 0,  // 普通
            1: 0   // JSON
        }
    };
    
    result.parses.forEach(parse => {
        const typeCode = parse.type || 0;
        parsesInfo.typeStats[typeCode] = (parsesInfo.typeStats[typeCode] || 0) + 1;
    });
    
    const stats = {
        sites: sitesInfo,
        lives: livesInfo,
        parses: parsesInfo,
        hasSpider: !!result.spider,
        hasWallpaper: !!result.wallpaper,
        rulesCount: result.rules.length
    };
    
    if (typeof callback === 'function') {
        callback(result, stats);
    }
    
    return { result, stats };
}

/**
 * 显示线路解析结果
 * @param {object} linesData - 线路数据
 * @param {HTMLElement} container - 显示结果的容器
 */
function displayLinesData(linesData, container) {
    // 解析并显示统计信息
    const { result, stats } = parseLineData(linesData);
    
    let html = `
        <div class="alert alert-success mb-3">
            <i class="fas fa-check-circle me-2"></i>线路解析成功
        </div>
        
        <div class="card mb-3">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">线路统计</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-primary">
                            <div class="card-body text-center">
                                <h1 class="display-4">${stats.sites.count}</h1>
                                <h5>站点</h5>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-success">
                            <div class="card-body text-center">
                                <h1 class="display-4">${stats.lives.channelsCount}</h1>
                                <h5>频道</h5>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-info">
                            <div class="card-body text-center">
                                <h1 class="display-4">${stats.parses.count}</h1>
                                <h5>解析器</h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // 添加查看详情按钮
    html += `
        <div class="text-center">
            <button class="btn btn-primary btn-lg" id="viewLinesDetail">
                <i class="fas fa-list-alt me-2"></i>查看线路详情
            </button>
        </div>
    `;
    
    // 设置HTML
    container.innerHTML = html;
    
    // 绑定详情按钮事件
    document.getElementById("viewLinesDetail").addEventListener("click", function() {
        showLinesDetailModal(result);
    });
}

/**
 * 显示线路详情模态框
 * @param {object} linesData - 线路数据
 */
function showLinesDetailModal(linesData) {
    // 检查是否已存在模态框，如果不存在则创建
    let modal = document.getElementById('linesDetailModal');
    
    if (!modal) {
        // 创建模态框HTML
        const modalHtml = `
            <div class="modal fade" id="linesDetailModal" tabindex="-1" aria-labelledby="linesDetailModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-xl modal-dialog-scrollable">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="linesDetailModalLabel">线路详细信息</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <!-- 选项卡导航 -->
                            <ul class="nav nav-tabs mb-3" id="linesTab" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="sites-tab" data-bs-toggle="tab" data-bs-target="#sites" type="button" role="tab" aria-controls="sites" aria-selected="true">
                                        站点 <span id="sitesCount" class="badge bg-primary">0</span>
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="lives-tab" data-bs-toggle="tab" data-bs-target="#lives" type="button" role="tab" aria-controls="lives" aria-selected="false">
                                        直播源 <span id="livesCount" class="badge bg-success">0</span>
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="parses-tab" data-bs-toggle="tab" data-bs-target="#parses" type="button" role="tab" aria-controls="parses" aria-selected="false">
                                        解析器 <span id="parsesCount" class="badge bg-info">0</span>
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="other-tab" data-bs-toggle="tab" data-bs-target="#other" type="button" role="tab" aria-controls="other" aria-selected="false">
                                        其他配置
                                    </button>
                                </li>
                            </ul>
                            
                            <!-- 选项卡内容 -->
                            <div class="tab-content" id="linesTabContent">
                                <div class="tab-pane fade show active" id="sites" role="tabpanel" aria-labelledby="sites-tab">
                                    <div id="sitesContainer"></div>
                                </div>
                                
                                <div class="tab-pane fade" id="lives" role="tabpanel" aria-labelledby="lives-tab">
                                    <div id="livesContainer"></div>
                                </div>
                                
                                <div class="tab-pane fade" id="parses" role="tabpanel" aria-labelledby="parses-tab">
                                    <div id="parsesContainer"></div>
                                </div>
                                
                                <div class="tab-pane fade" id="other" role="tabpanel" aria-labelledby="other-tab">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="card mb-3">
                                                <div class="card-header">爬虫规则</div>
                                                <div class="card-body" id="spiderInfo"></div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="card mb-3">
                                                <div class="card-header">壁纸配置</div>
                                                <div class="card-body" id="wallpaperInfo"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card">
                                        <div class="card-header">其他规则</div>
                                        <div class="card-body" id="rulesInfo"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 添加模态框到文档
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        modal = document.getElementById('linesDetailModal');
    }
    
    // 更新计数
    document.getElementById('sitesCount').textContent = linesData.sites ? linesData.sites.length : 0;
    document.getElementById('livesCount').textContent = linesData.lives ? linesData.lives.length : 0;
    document.getElementById('parsesCount').textContent = linesData.parses ? linesData.parses.length : 0;
    
    // 填充站点数据
    const sitesContainer = document.getElementById('sitesContainer');
    if (linesData.sites && linesData.sites.length > 0) {
        let sitesHtml = `
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>站点名称</th>
                            <th>类型</th>
                            <th>API</th>
                            <th>解密方法</th>
                            <th>扩展配置</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        linesData.sites.forEach((site, index) => {
            sitesHtml += `<tr>
                <td>${index + 1}</td>
                <td>${site.name}</td>
                <td>${getTvboxSiteType(site.type)}</td>
            `;
            
            // API列
            sitesHtml += '<td>';
            if (site.api) {
                let apiText = site.api;
                let apiDecrypted = site.api_decrypted || site.api;
                let apiMethod = site.api_method || "无需解密";
                
                if (apiDecrypted !== apiText) {
                    sitesHtml += `
                        <div class="mb-1">${escapeHtml(apiText)}</div>
                        <div class="text-success">
                            <i class="fas fa-arrow-down me-1"></i>解密后:
                            <span class="badge bg-info">${apiMethod}</span>
                        </div>
                        <div>${escapeHtml(apiDecrypted)}</div>
                    `;
                } else {
                    sitesHtml += escapeHtml(apiText);
                }
            }
            sitesHtml += '</td>';
            
            // 解密方法列
            sitesHtml += `<td>${site.api_method || "-"}</td>`;
            
            // 扩展配置列
            sitesHtml += '<td>';
            if (site.ext) {
                let extText = site.ext;
                let extDecrypted = site.ext_decrypted || site.ext;
                let extMethod = site.ext_method || "无需解密";
                
                if (extDecrypted !== extText) {
                    sitesHtml += `
                        <div class="mb-1">${escapeHtml(extText)}</div>
                        <div class="text-success">
                            <i class="fas fa-arrow-down me-1"></i>解密后:
                            <span class="badge bg-info">${extMethod}</span>
                        </div>
                        <div>${escapeHtml(extDecrypted)}</div>
                    `;
                } else {
                    sitesHtml += escapeHtml(extText || "-");
                }
            } else {
                sitesHtml += "-";
            }
            sitesHtml += '</td></tr>';
        });
        
        sitesHtml += `</tbody></table></div>`;
        sitesContainer.innerHTML = sitesHtml;
    } else {
        sitesContainer.innerHTML = '<div class="alert alert-info">无站点配置</div>';
    }
    
    // 填充直播源数据
    const livesContainer = document.getElementById('livesContainer');
    if (linesData.lives && linesData.lives.length > 0) {
        let livesHtml = '';
        
        linesData.lives.forEach((live, liveIndex) => {
            livesHtml += `
            <div class="card mb-3">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">${live.group || '未命名分组'}</h6>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped table-sm mb-0">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>频道名称</th>
                                    <th>直播URL</th>
                                </tr>
                            </thead>
                            <tbody>
            `;
            
            if (live.channels && live.channels.length > 0) {
                live.channels.forEach((channel, channelIndex) => {
                    livesHtml += `<tr><td>${channelIndex + 1}</td><td>${channel.name}</td><td>`;
                    
                    if (channel.urls && channel.urls.length > 0) {
                        channel.urls.forEach((urlInfo, urlIndex) => {
                            if (urlInfo.decrypted && urlInfo.decrypted !== urlInfo.url) {
                                livesHtml += `
                                    <div class="mb-2">
                                        <div class="mb-1">原始: ${escapeHtml(urlInfo.url)}</div>
                                        <div class="text-success">
                                            <i class="fas fa-arrow-down me-1"></i>解密后:
                                            <span class="badge bg-info">${urlInfo.method}</span>
                                        </div>
                                        <div>${escapeHtml(urlInfo.decrypted)}</div>
                                    </div>
                                `;
                            } else {
                                livesHtml += `<div class="mb-1">${escapeHtml(urlInfo.url)}</div>`;
                            }
                            
                            if (urlIndex < channel.urls.length - 1) {
                                livesHtml += '<hr class="my-1">';
                            }
                        });
                    } else {
                        livesHtml += '-';
                    }
                    
                    livesHtml += '</td></tr>';
                });
            } else {
                livesHtml += `<tr><td colspan="3" class="text-center">无频道数据</td></tr>`;
            }
            
            livesHtml += `</tbody></table></div></div></div>`;
        });
        
        livesContainer.innerHTML = livesHtml;
    } else {
        livesContainer.innerHTML = '<div class="alert alert-info">无直播源配置</div>';
    }
    
    // 填充解析器数据
    const parsesContainer = document.getElementById('parsesContainer');
    if (linesData.parses && linesData.parses.length > 0) {
        let parsesHtml = `
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>名称</th>
                            <th>类型</th>
                            <th>URL</th>
                            <th>解密方法</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        linesData.parses.forEach((parse, index) => {
            parsesHtml += `<tr>
                <td>${index + 1}</td>
                <td>${parse.name}</td>
                <td>${parse.type === 1 ? "JSON" : "普通"}</td>
            `;
            
            // URL列
            parsesHtml += '<td>';
            if (parse.url) {
                let urlText = parse.url;
                let urlDecrypted = parse.url_decrypted || parse.url;
                let urlMethod = parse.url_method || "无需解密";
                
                if (urlDecrypted !== urlText) {
                    parsesHtml += `
                        <div class="mb-1">${escapeHtml(urlText)}</div>
                        <div class="text-success">
                            <i class="fas fa-arrow-down me-1"></i>解密后:
                            <span class="badge bg-info">${urlMethod}</span>
                        </div>
                        <div>${escapeHtml(urlDecrypted)}</div>
                    `;
                } else {
                    parsesHtml += escapeHtml(urlText);
                }
            }
            parsesHtml += '</td>';
            
            // 解密方法列
            parsesHtml += `<td>${parse.url_method || "-"}</td></tr>`;
        });
        
        parsesHtml += `</tbody></table></div>`;
        parsesContainer.innerHTML = parsesHtml;
    } else {
        parsesContainer.innerHTML = '<div class="alert alert-info">无解析器配置</div>';
    }
    
    // 填充爬虫规则信息
    const spiderInfo = document.getElementById('spiderInfo');
    if (linesData.spider) {
        spiderInfo.innerHTML = `
            <div class="mb-2">
                <strong>原始URL:</strong> ${escapeHtml(linesData.spider.url)}
            </div>
            ${linesData.spider.decrypted !== linesData.spider.url ? `
            <div class="mb-2">
                <strong>解密方法:</strong> <span class="badge bg-info">${linesData.spider.method}</span>
            </div>
            <div>
                <strong>解密后:</strong> ${escapeHtml(linesData.spider.decrypted)}
            </div>
            ` : '<div class="text-muted">无需解密</div>'}
        `;
    } else {
        spiderInfo.innerHTML = '<div class="alert alert-info">无爬虫规则配置</div>';
    }
    
    // 填充壁纸配置信息
    const wallpaperInfo = document.getElementById('wallpaperInfo');
    if (linesData.wallpaper) {
        wallpaperInfo.innerHTML = `
            <div class="mb-2">
                <strong>原始URL:</strong> ${escapeHtml(linesData.wallpaper.url)}
            </div>
            ${linesData.wallpaper.decrypted !== linesData.wallpaper.url ? `
            <div class="mb-2">
                <strong>解密方法:</strong> <span class="badge bg-info">${linesData.wallpaper.method}</span>
            </div>
            <div>
                <strong>解密后:</strong> ${escapeHtml(linesData.wallpaper.decrypted)}
            </div>
            ` : '<div class="text-muted">无需解密</div>'}
        `;
    } else {
        wallpaperInfo.innerHTML = '<div class="alert alert-info">无壁纸配置</div>';
    }
    
    // 填充其他规则信息
    const rulesInfo = document.getElementById('rulesInfo');
    if (linesData.rules && linesData.rules.length > 0) {
        let rulesHtml = '<div class="list-group">';
        linesData.rules.forEach((rule, index) => {
            rulesHtml += `
            <div class="list-group-item">
                <div class="mb-2">
                    <strong>#${index + 1} 原始URL:</strong> ${escapeHtml(rule.url)}
                </div>
                ${rule.decrypted !== rule.url ? `
                <div class="mb-1">
                    <strong>解密方法:</strong> <span class="badge bg-info">${rule.method}</span>
                </div>
                <div>
                    <strong>解密后:</strong> ${escapeHtml(rule.decrypted)}
                </div>
                ` : '<div class="text-muted">无需解密</div>'}
            </div>
            `;
        });
        rulesHtml += '</div>';
        rulesInfo.innerHTML = rulesHtml;
    } else {
        rulesInfo.innerHTML = '<div class="alert alert-info">无其他规则配置</div>';
    }
    
    // 显示模态框
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();
} 

// 解密接口URL处理
$("#decryptForm").submit(function(e) {
    e.preventDefault();
    
    const url = $("#url").val().trim();
    const hintType = $("#hintType").val();
    const isConfig = $("#configMode").prop("checked");
    
    if (!url) {
        showAlert("danger", "请输入URL地址");
        return;
    }
    
    // 显示加载状态
    $("#decryptBtn").html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 解析中...').prop("disabled", true);
    $("#decryptResult").html('<div class="text-center my-5"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">正在解密处理中...</p></div>');
    
    // 发送解密请求
    $.ajax({
        url: '/api/decrypt',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            url: url,
            hint: hintType,
            is_config: isConfig
        }),
        success: function(response) {
            if (response.success) {
                if (response.data.is_config || response.data.decrypted.trim().startsWith('{') && response.data.decrypted.includes('"sites"')) {
                    // 处理配置内容显示
                    displayTVBoxConfig(response.data);
                } else {
                    // 处理普通URL解密结果
                    displayDecryptResult(response.data);
                }
            } else {
                $("#decryptResult").html(`<div class="alert alert-danger">${response.message}</div>`);
            }
        },
        error: function(xhr, status, error) {
            $("#decryptResult").html(`<div class="alert alert-danger">请求失败: ${error}</div>`);
        },
        complete: function() {
            $("#decryptBtn").html('<i class="bi bi-unlock"></i> 解密').prop("disabled", false);
        }
    });
});

// 显示普通解密结果
function displayDecryptResult(data) {
    const original = escapeHTML(data.original);
    const decrypted = escapeHTML(data.decrypted);
    const method = data.method;
    const urlInfo = data.url_info || {};
    
    let resultHTML = `
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <i class="bi bi-check-circle-fill me-2"></i>解密成功
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h5>原始内容:</h5>
                    <div class="form-control bg-light">${original}</div>
                </div>
                <div class="mb-3">
                    <h5>解密后内容: <span class="badge bg-info">${method}</span></h5>
                    <div class="form-control bg-light" style="word-break: break-all;">${decrypted}</div>
                </div>
    `;
    
    // 如果是URL，显示URL分析
    if (Object.keys(urlInfo).length > 0) {
        resultHTML += `
            <div class="mb-3">
                <h5>URL分析:</h5>
                <table class="table table-striped">
                    <tbody>
                        <tr><th>协议</th><td>${urlInfo.protocol || '-'}</td></tr>
                        <tr><th>域名</th><td>${urlInfo.domain || '-'}</td></tr>
                        <tr><th>路径</th><td>${urlInfo.path || '-'}</td></tr>
                        ${urlInfo.query ? `<tr><th>查询参数</th><td>${urlInfo.query}</td></tr>` : ''}
                    </tbody>
                </table>
            </div>
        `;
    }
    
    resultHTML += `
            <div class="d-flex justify-content-end">
                <button class="btn btn-primary me-2" onclick="copyText('${decrypted.replace(/'/g, "\\'")}')">
                    <i class="bi bi-clipboard"></i> 复制结果
                </button>
                <button class="btn btn-success" onclick="parseAsConfig('${original.replace(/'/g, "\\'")}')">
                    <i class="bi bi-braces"></i> 作为配置解析
                </button>
            </div>
        </div>
    `;
    
    $("#decryptResult").html(resultHTML);
}

// 显示配置内容解析结果
function displayTVBoxConfig(data) {
    const original = data.original;
    const decrypted = data.decrypted;
    const method = data.method;
    const configInfo = data.config_info || {};
    
    // 准备配置摘要信息
    const sitesCount = configInfo.sites_count || 0;
    const livesCount = configInfo.lives_count || 0;
    const livesGroupCount = configInfo.lives_group_count || 0;
    const parsesCount = configInfo.parses_count || 0;
    const spider = configInfo.spider || '无';
    const wallpaper = configInfo.wallpaper || '无';
    const logo = configInfo.logo || '无';
    
    // 如果没有提供config_info，尝试自己解析
    let jsonData = null;
    if (!configInfo.sites_count) {
        try {
            jsonData = JSON.parse(decrypted);
            
            // 计算站点数量
            let sites = jsonData.sites || (jsonData.video ? jsonData.video.sites : []);
            if (Array.isArray(sites)) {
                sitesCount = sites.length;
            }
            
            // 计算直播源数量
            if (jsonData.lives && Array.isArray(jsonData.lives)) {
                livesGroupCount = jsonData.lives.length;
                // 尝试计算总频道数
                let channelsCount = 0;
                for (let group of jsonData.lives) {
                    if (group.channels && Array.isArray(group.channels)) {
                        channelsCount += group.channels.length;
                    }
                }
                livesCount = channelsCount;
            }
            
            // 计算解析器数量
            if (jsonData.parses && Array.isArray(jsonData.parses)) {
                parsesCount = jsonData.parses.length;
            }
            
            // 其他字段
            spider = jsonData.spider || '无';
            wallpaper = jsonData.wallpaper || '无';
            logo = jsonData.logo || '无';
        } catch (e) {
            console.error("前端解析JSON配置失败", e);
        }
    }
    
    let resultHTML = `
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <i class="bi bi-check-circle-fill me-2"></i>TVBox配置解析成功
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h5>配置源地址:</h5>
                    <div class="form-control bg-light">${escapeHTML(original)}</div>
                </div>
                <div class="mb-3">
                    <h5>配置信息摘要: <span class="badge bg-info">${method}</span></h5>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <tbody>
                                <tr><th>站点数量</th><td>${sitesCount} 个视频源</td></tr>
                                <tr><th>直播源</th><td>${livesGroupCount} 个分组，${livesCount} 个频道</td></tr>
                                <tr><th>解析器</th><td>${parsesCount} 个解析器</td></tr>
                                <tr><th>Spider</th><td>${escapeHTML(spider)}</td></tr>
                                <tr><th>壁纸</th><td>${escapeHTML(wallpaper)}</td></tr>
                                <tr><th>Logo</th><td>${escapeHTML(logo)}</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div class="mb-3">
                    <h5>配置内容:</h5>
                    <pre class="form-control bg-light" style="max-height: 500px; overflow-y: auto;">${escapeHTML(decrypted)}</pre>
                </div>
                
                <div class="d-flex justify-content-end">
                    <button class="btn btn-primary me-2" onclick="copyText(${JSON.stringify(decrypted)})">
                        <i class="bi bi-clipboard"></i> 复制配置
                    </button>
                    <button class="btn btn-success me-2" onclick="parseLines('${original.replace(/'/g, "\\'")}')">
                        <i class="bi bi-list-ul"></i> 解析站点
                    </button>
                    <button class="btn btn-info" onclick="saveConfig('${original.replace(/'/g, "\\'")}', '${original.split('/').pop().replace(/'/g, "\\'")}')">
                        <i class="bi bi-save"></i> 保存配置
                    </button>
                </div>
            </div>
        </div>
    `;
    
    $("#decryptResult").html(resultHTML);
}

// 作为配置解析
function parseAsConfig(url) {
    $("#url").val(url);
    $("#configMode").prop("checked", true);
    $("#decryptForm").submit();
}

// 解析线路
function parseLines(url) {
    $("#parseLineModal").modal("show");
    $("#parseLineModalLabel").text("解析配置线路");
    $("#parseLineResult").html('<div class="text-center my-5"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">正在解析线路中...</p></div>');
    
    $.ajax({
        url: '/api/parse_lines',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ url: url }),
        success: function(response) {
            if (response.success) {
                displayParseResult(response.data);
            } else {
                $("#parseLineResult").html(`<div class="alert alert-danger">${response.message}</div>`);
            }
        },
        error: function(xhr, status, error) {
            $("#parseLineResult").html(`<div class="alert alert-danger">请求失败: ${error}</div>`);
        }
    });
}

// 保存配置
function saveConfig(url, filename) {
    if (!filename) {
        filename = 'config.json';
    }
    
    $.ajax({
        url: '/api/save_config',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ url: url, filename: filename }),
        success: function(response) {
            if (response.success) {
                showAlert("success", `配置已保存为: ${response.data.filename}`);
            } else {
                showAlert("danger", response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert("danger", `保存失败: ${error}`);
        }
    });
}

// 显示解析结果
function displayParseResult(data) {
    const sitesCount = data.sites ? data.sites.length : 0;
    const livesCount = data.lives ? data.lives.length : 0;
    const parsesCount = data.parses ? data.parses.length : 0;
    
    let resultHTML = `
        <div class="alert alert-success">
            解析成功: ${sitesCount} 个站点, ${livesCount} 个直播源, ${parsesCount} 个解析器
        </div>
        
        <ul class="nav nav-tabs" id="parseTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="sites-tab" data-bs-toggle="tab" data-bs-target="#sites" type="button" role="tab">
                    站点 <span class="badge bg-primary">${sitesCount}</span>
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="lives-tab" data-bs-toggle="tab" data-bs-target="#lives" type="button" role="tab">
                    直播源 <span class="badge bg-success">${livesCount}</span>
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="parses-tab" data-bs-toggle="tab" data-bs-target="#parses" type="button" role="tab">
                    解析器 <span class="badge bg-info">${parsesCount}</span>
                </button>
            </li>
        </ul>
        
        <div class="tab-content border border-top-0 rounded-bottom p-3" id="parseTabsContent">
            <div class="tab-pane fade show active" id="sites" role="tabpanel">
                ${sitesCount > 0 ? generateSitesTable(data.sites) : '<div class="alert alert-info">无站点数据</div>'}
            </div>
            <div class="tab-pane fade" id="lives" role="tabpanel">
                ${livesCount > 0 ? generateLivesContent(data.lives) : '<div class="alert alert-info">无直播源数据</div>'}
            </div>
            <div class="tab-pane fade" id="parses" role="tabpanel">
                ${parsesCount > 0 ? generateParsesTable(data.parses) : '<div class="alert alert-info">无解析器数据</div>'}
            </div>
        </div>
    `;
    
    $("#parseLineResult").html(resultHTML);
}

// 生成站点表格
function generateSitesTable(sites) {
    let html = `
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>名称</th>
                        <th>类型</th>
                        <th>API</th>
                        <th>可搜索</th>
                        <th>快速搜索</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    sites.forEach((site, index) => {
        html += `
            <tr>
                <td>${index + 1}</td>
                <td>${escapeHTML(site.name)}</td>
                <td>${getSiteTypeName(site.type)}</td>
                <td>${escapeHTML(site.api)}</td>
                <td>${site.searchable ? '✓' : '✗'}</td>
                <td>${site.quickSearch ? '✓' : '✗'}</td>
            </tr>
        `;
    });
    
    html += `
                </tbody>
            </table>
        </div>
    `;
    
    return html;
}

// 生成直播源内容
function generateLivesContent(lives) {
    let html = '';
    
    lives.forEach((live, index) => {
        const name = live.name || `分组 #${index + 1}`;
        const channels = live.channels || [];
        
        html += `
            <div class="card mb-3">
                <div class="card-header bg-success text-white">
                    ${escapeHTML(name)} - ${channels.length} 个频道
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped mb-0">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>频道名称</th>
                                    <th>URL</th>
                                </tr>
                            </thead>
                            <tbody>
        `;
        
        if (channels.length > 0) {
            channels.forEach((channel, channelIndex) => {
                html += `
                    <tr>
                        <td>${channelIndex + 1}</td>
                        <td>${escapeHTML(channel.name)}</td>
                        <td class="text-truncate" style="max-width: 300px;">${escapeHTML(channel.url)}</td>
                    </tr>
                `;
            });
        } else {
            html += `<tr><td colspan="3" class="text-center">无频道数据</td></tr>`;
        }
        
        html += `
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;
    });
    
    return html;
}

// 生成解析器表格
function generateParsesTable(parses) {
    let html = `
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>名称</th>
                        <th>类型</th>
                        <th>URL</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    parses.forEach((parse, index) => {
        html += `
            <tr>
                <td>${index + 1}</td>
                <td>${escapeHTML(parse.name)}</td>
                <td>${parse.type === 1 ? 'JSON' : '普通'}</td>
                <td class="text-truncate" style="max-width: 300px;">${escapeHTML(parse.url)}</td>
            </tr>
        `;
    });
    
    html += `
                </tbody>
            </table>
        </div>
    `;
    
    return html;
}

// 获取站点类型名称
function getSiteTypeName(type) {
    const types = {
        0: '普通',
        1: 'VIP解析',
        3: 'LIVE',
        4: '本地'
    };
    return types[type] || `未知(${type})`;
}

// HTML转义
function escapeHTML(str) {
    if (str === undefined || str === null) {
        return '';
    }
    if (typeof str !== 'string') {
        return String(str);
    }
    return str
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;');
}

// 复制文本
function copyText(text) {
    navigator.clipboard.writeText(text)
        .then(() => {
            showAlert("success", "内容已复制到剪贴板");
        })
        .catch(err => {
            showAlert("danger", "复制失败: " + err);
        });
}

// 显示提示信息
function showAlert(type, message) {
    const alertDiv = $(`<div class="alert alert-${type} alert-dismissible fade show" role="alert">
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="关闭"></button>
    </div>`);
    
    $("#alertContainer").append(alertDiv);
    
    setTimeout(() => {
        alertDiv.alert('close');
    }, 5000);
} 

// 快速解析按钮
$("#ftyButton").click(function() {
    $("#url").val("http://饭太硬.top/tv");
    $("#configMode").prop("checked", true);
    $("#decryptForm").submit();
});

$("#fmButton").click(function() {
    $("#url").val("http://肥猫.live");
    $("#configMode").prop("checked", true);
    $("#decryptForm").submit();
});

$("#werButton").click(function() {
    $("#url").val("http://tvbox.rzdpai.top/tvbox.json");
    $("#configMode").prop("checked", true);
    $("#decryptForm").submit();
});

$("#shButton").click(function() {
    $("#url").val("https://raw.liucn.cc/box/m.json");
    $("#configMode").prop("checked", true);
    $("#decryptForm").submit();
});

$("#ddButton").click(function() {
    $("#url").val("https://dxawi.github.io/0/0.json");
    $("#configMode").prop("checked", true);
    $("#decryptForm").submit();
});

$("#netflixButton").click(function() {
    $("#url").val("https://jihulab.com/clear1/tvbox/-/raw/main/0.json");
    $("#configMode").prop("checked", true);
    $("#decryptForm").submit();
});

$("#fongmiButton").click(function() {
    $("#url").val("https://raw.githubusercontent.com/FongMi/TV/release/json/config.json");
    $("#configMode").prop("checked", true);
    $("#decryptForm").submit();
});

$("#aliButton").click(function() {
    $("#url").val("http://刚刚.live/猫");
    $("#configMode").prop("checked", true);
    $("#decryptForm").submit();
});

$("#aliDriveButton").click(function() {
    $("#url").val("https://tvbox.cainisi.cf");
    $("#configMode").prop("checked", true);
    $("#decryptForm").submit();
});

$("#customButton").click(function() {
    $("#url").val("clan://localhost/tvfans/tv.json");
    $("#configMode").prop("checked", true);
    $("#decryptForm").submit();
});

$("#singleLineButton").click(function() {
    $("#url").val("https://ghproxy.net/https://raw.githubusercontent.com/habao260/zy/master/tvlive.txt");
    $("#configMode").prop("checked", false);
    $("#decryptForm").submit();
});

// 文件导入处理
$("#fileImport").change(function(e) {
    const file = e.target.files[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const content = e.target.result;
            
            // 尝试解析为JSON
            try {
                const json = JSON.parse(content);
                // 如果是TVBox配置格式
                if (json.sites || (json.video && json.video.sites)) {
                    $("#decryptResult").html(`
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle-fill me-2"></i>导入成功，检测到TVBox配置文件
                        </div>
                    `);
                    
                    // 显示配置内容
                    displayTVBoxConfig({
                        original: file.name,
                        decrypted: JSON.stringify(json, null, 2),
                        method: "本地文件导入"
                    });
                    
                    return;
                }
            } catch (e) {
                console.log("解析JSON失败，尝试作为文本处理", e);
            }
            
            // 作为普通文本处理
            $("#decryptResult").html(`
                <div class="alert alert-info">
                    <i class="bi bi-info-circle-fill me-2"></i>导入文本成功，长度: ${content.length}
                </div>
            `);
            
            // 显示内容
            displayDecryptResult({
                original: file.name,
                decrypted: content,
                method: "本地文件导入"
            });
            
        } catch (e) {
            $("#decryptResult").html(`
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>导入失败: ${e.message}
                </div>
            `);
        }
    };
    
    reader.readAsText(file);
}); 