#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库配置和连接管理
"""

from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from typing import Generator
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)

# 根据数据库类型配置引擎参数
engine_kwargs = {
    "echo": settings.DATABASE_ECHO,
}

# SQLite特殊配置
if settings.is_sqlite:
    engine_kwargs.update({
        "poolclass": StaticPool,
        "connect_args": {
            "check_same_thread": False,
            "timeout": 20
        }
    })
# MySQL配置
elif settings.is_mysql:
    engine_kwargs.update({
        "pool_size": 10,
        "max_overflow": 20,
        "pool_pre_ping": True,
        "pool_recycle": 3600,
        "connect_args": {
            "charset": "utf8mb4",
            "autocommit": False
        }
    })
# PostgreSQL配置
elif settings.is_postgresql:
    engine_kwargs.update({
        "pool_size": 10,
        "max_overflow": 20,
        "pool_pre_ping": True,
        "pool_recycle": 3600
    })

# 创建数据库引擎
engine = create_engine(settings.DATABASE_URL, **engine_kwargs)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型类
Base = declarative_base()

# 元数据配置
metadata = MetaData()

def get_db() -> Generator[Session, None, None]:
    """
    获取数据库会话
    用于依赖注入
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"数据库会话错误: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()

def create_tables():
    """创建所有数据库表"""
    try:
        Base.metadata.create_all(bind=engine)
        logger.info("数据库表创建成功")
    except Exception as e:
        logger.error(f"创建数据库表失败: {str(e)}")
        raise

def drop_tables():
    """删除所有数据库表"""
    try:
        Base.metadata.drop_all(bind=engine)
        logger.info("数据库表删除成功")
    except Exception as e:
        logger.error(f"删除数据库表失败: {str(e)}")
        raise

def check_database_connection():
    """检查数据库连接"""
    try:
        with engine.connect() as connection:
            connection.execute("SELECT 1")
        logger.info("数据库连接正常")
        return True
    except Exception as e:
        logger.error(f"数据库连接失败: {str(e)}")
        return False

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.engine = engine
        self.SessionLocal = SessionLocal
    
    def get_session(self) -> Session:
        """获取数据库会话"""
        return SessionLocal()
    
    def create_all_tables(self):
        """创建所有表"""
        create_tables()
    
    def drop_all_tables(self):
        """删除所有表"""
        drop_tables()
    
    def check_connection(self) -> bool:
        """检查连接"""
        return check_database_connection()
    
    def backup_database(self, backup_path: str):
        """备份数据库（仅SQLite）"""
        if not settings.is_sqlite:
            raise NotImplementedError("仅支持SQLite数据库备份")
        
        import shutil
        import os
        
        try:
            # 获取数据库文件路径
            db_path = settings.DATABASE_URL.replace("sqlite:///", "")
            
            # 确保备份目录存在
            backup_dir = os.path.dirname(backup_path)
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir, exist_ok=True)
            
            # 复制数据库文件
            shutil.copy2(db_path, backup_path)
            logger.info(f"数据库备份成功: {backup_path}")
            
        except Exception as e:
            logger.error(f"数据库备份失败: {str(e)}")
            raise
    
    def restore_database(self, backup_path: str):
        """恢复数据库（仅SQLite）"""
        if not settings.is_sqlite:
            raise NotImplementedError("仅支持SQLite数据库恢复")
        
        import shutil
        import os
        
        try:
            # 获取数据库文件路径
            db_path = settings.DATABASE_URL.replace("sqlite:///", "")
            
            # 检查备份文件是否存在
            if not os.path.exists(backup_path):
                raise FileNotFoundError(f"备份文件不存在: {backup_path}")
            
            # 关闭所有连接
            engine.dispose()
            
            # 恢复数据库文件
            shutil.copy2(backup_path, db_path)
            logger.info(f"数据库恢复成功: {backup_path}")
            
        except Exception as e:
            logger.error(f"数据库恢复失败: {str(e)}")
            raise

# 创建全局数据库管理器实例
db_manager = DatabaseManager()
