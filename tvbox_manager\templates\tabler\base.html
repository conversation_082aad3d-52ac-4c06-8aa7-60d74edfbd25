<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{% block title %}TVBox Manager{% endblock %}</title>
  
  <!-- Favicon -->
  <link rel="icon" href="{{ url_for('static', filename='img/favicon.ico') }}" type="image/x-icon">
  
  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@2.44.0/tabler-icons.min.css">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/tabler.min.css') }}">
  
  <!-- Custom CSS -->
  {% block extra_css %}{% endblock %}
</head>
<body class="theme-light">
  <div class="page">
    <!-- 侧边栏 -->
    <aside class="navbar navbar-vertical navbar-expand-lg" id="sidebar">
      <div class="container-fluid">
        <!-- 顶部Logo和折叠按钮 -->
        <button class="navbar-toggler" type="button">
          <span class="navbar-toggler-icon"></span>
        </button>
        <h1 class="navbar-brand navbar-brand-autodark">
          <a href="{{ url_for('interface.index') }}">
            <i class="ti ti-device-tv me-2"></i>
            <span>TVBox Manager</span>
          </a>
        </h1>
        
        <!-- 侧边栏内容 -->
        <div class="collapse navbar-collapse" id="sidebar-menu">
          <ul class="navbar-nav pt-lg-3">
            <li class="nav-item {% if active_nav == 'interface' %}active{% endif %}">
              <a class="nav-link" href="{{ url_for('interface.index') }}">
                <span class="nav-link-icon d-md-none d-lg-inline-block">
                  <i class="ti ti-link"></i>
                </span>
                <span class="nav-link-title">接口管理</span>
              </a>
            </li>
            
            <li class="nav-item {% if active_nav == 'decrypt' %}active{% endif %}">
              <a class="nav-link" href="{{ url_for('interface.decrypt') }}">
                <span class="nav-link-icon d-md-none d-lg-inline-block">
                  <i class="ti ti-lock-open"></i>
                </span>
                <span class="nav-link-title">接口解密</span>
              </a>
            </li>
            
            <li class="nav-item {% if active_nav == 'config' %}active{% endif %}">
              <a class="nav-link" href="{{ url_for('config.index') }}">
                <span class="nav-link-icon d-md-none d-lg-inline-block">
                  <i class="ti ti-settings"></i>
                </span>
                <span class="nav-link-title">配置管理</span>
              </a>
            </li>
            
            <li class="nav-item {% if active_nav == 'live' %}active{% endif %}">
              <a class="nav-link" href="{{ url_for('live.index') }}">
                <span class="nav-link-icon d-md-none d-lg-inline-block">
                  <i class="ti ti-device-tv"></i>
                </span>
                <span class="nav-link-title">直播管理</span>
              </a>
            </li>
            
            {% if current_user.has_role('admin') %}
            <li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle" href="#navbar-extra" data-bs-toggle="dropdown" data-bs-auto-close="false" role="button" aria-expanded="false">
                <span class="nav-link-icon d-md-none d-lg-inline-block">
                  <i class="ti ti-shield-lock"></i>
                </span>
                <span class="nav-link-title">管理员功能</span>
              </a>
              <div class="dropdown-menu">
                <a class="dropdown-item" href="#">
                  <span class="nav-link-icon d-md-none d-lg-inline-block">
                    <i class="ti ti-users"></i>
                  </span>
                  用户管理
                </a>
                <a class="dropdown-item" href="#">
                  <span class="nav-link-icon d-md-none d-lg-inline-block">
                    <i class="ti ti-adjustments"></i>
                  </span>
                  系统设置
                </a>
              </div>
            </li>
            {% endif %}
          </ul>
        </div>
      </div>
    </aside>
    
    <!-- 主要内容区 -->
    <div class="page-wrapper">
      <!-- 顶部导航栏 -->
      <header class="navbar navbar-expand-md d-none d-lg-flex d-print-none">
        <div class="container-xl">
          <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-menu">
            <span class="navbar-toggler-icon"></span>
          </button>
          
          <!-- 页面标题 -->
          <div class="navbar-nav flex-row order-md-last">
            <div class="nav-item dropdown">
              <a href="#" class="nav-link d-flex lh-1 text-reset p-0" data-bs-toggle="dropdown" aria-label="Open user menu">
                <div class="d-none d-xl-block ps-2">
                  <div>{{ current_user.email }}</div>
                  <div class="mt-1 small text-muted">
                    {% if current_user.has_role('admin') %}管理员{% else %}用户{% endif %}
                  </div>
                </div>
                <span class="avatar avatar-sm ms-2">{{ current_user.email[0].upper() }}</span>
              </a>
              <div class="dropdown-menu dropdown-menu-end dropdown-menu-arrow">
                <a href="{{ url_for('security.change_password') }}" class="dropdown-item">修改密码</a>
                <a href="{{ url_for('security.logout') }}" class="dropdown-item">退出登录</a>
              </div>
            </div>
          </div>
          
          <div class="collapse navbar-collapse" id="navbar-menu">
            <div>
              <h2 class="page-title">
                {% block page_title %}{% endblock %}
              </h2>
            </div>
          </div>
        </div>
      </header>
      
      <!-- 页面内容 -->
      <div class="page-body">
        <div class="container-xl">
          <!-- 提示消息 -->
          {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
              <div class="alert-container">
                {% for category, message in messages %}
                  {% set alert_class = "alert-" + category if category in ['success', 'danger', 'warning', 'info'] else "alert-info" %}
                  <div class="alert {{ alert_class }} alert-dismissible" role="alert">
                    <div class="d-flex">
                      <div>
                        {% if category == 'success' %}
                          <i class="ti ti-check icon"></i>
                        {% elif category == 'danger' or category == 'error' %}
                          <i class="ti ti-alert-circle icon"></i>
                        {% elif category == 'warning' %}
                          <i class="ti ti-alert-triangle icon"></i>
                        {% else %}
                          <i class="ti ti-info-circle icon"></i>
                        {% endif %}
                      </div>
                      <div>
                        {{ message }}
                      </div>
                    </div>
                    <a class="btn-close alert-close" data-bs-dismiss="alert" aria-label="close"></a>
                  </div>
                {% endfor %}
              </div>
            {% endif %}
          {% endwith %}
          
          <!-- 主要内容 -->
          {% block content %}{% endblock %}
        </div>
      </div>
      
      <!-- 页脚 -->
      <footer class="footer footer-transparent d-print-none">
        <div class="container-xl">
          <div class="row text-center align-items-center flex-row-reverse">
            <div class="col-lg-auto ms-lg-auto">
              <ul class="list-inline list-inline-dots mb-0">
                <li class="list-inline-item">
                  <a href="#" class="link-secondary">文档</a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="link-secondary">帮助</a>
                </li>
              </ul>
            </div>
            <div class="col-12 col-lg-auto mt-3 mt-lg-0">
              <ul class="list-inline list-inline-dots mb-0">
                <li class="list-inline-item">
                  &copy; 2025 TVBox Manager
                </li>
                <li class="list-inline-item">
                  版本 1.0.0
                </li>
              </ul>
            </div>
          </div>
        </div>
      </footer>
    </div>
  </div>
  
  <!-- JS -->
  <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
  <script src="{{ url_for('static', filename='js/tabler.min.js') }}"></script>
  
  <!-- 自定义JS -->
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      // 侧边栏切换
      const navbarToggler = document.querySelector('.navbar-toggler');
      const sidebar = document.getElementById('sidebar');
      
      if (navbarToggler && sidebar) {
        navbarToggler.addEventListener('click', () => {
          sidebar.classList.toggle('navbar-collapsed');
        });
      }
      
      // 自动关闭提示
      const alerts = document.querySelectorAll('.alert');
      alerts.forEach(alert => {
        setTimeout(() => {
          const closeBtn = alert.querySelector('.alert-close');
          if (closeBtn) {
            closeBtn.click();
          }
        }, 5000);
      });
      
      // 激活当前菜单项
      const currentPath = window.location.pathname;
      document.querySelectorAll('.nav-link').forEach(link => {
        if (link.getAttribute('href') === currentPath) {
          link.closest('.nav-item').classList.add('active');
          // 如果在下拉菜单中，打开父级
          const dropdown = link.closest('.dropdown-menu');
          if (dropdown) {
            const parentLink = dropdown.previousElementSibling;
            if (parentLink) {
              parentLink.setAttribute('aria-expanded', 'true');
              dropdown.classList.add('show');
            }
          }
        }
      });
    });
  </script>
  
  {% block extra_js %}{% endblock %}
</body>
</html> 
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{% block title %}TVBox Manager{% endblock %}</title>
  
  <!-- Favicon -->
  <link rel="icon" href="{{ url_for('static', filename='img/favicon.ico') }}" type="image/x-icon">
  
  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@2.44.0/tabler-icons.min.css">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/tabler.min.css') }}">
  
  <!-- Custom CSS -->
  {% block extra_css %}{% endblock %}
</head>
<body class="theme-light">
  <div class="page">
    <!-- 侧边栏 -->
    <aside class="navbar navbar-vertical navbar-expand-lg" id="sidebar">
      <div class="container-fluid">
        <!-- 顶部Logo和折叠按钮 -->
        <button class="navbar-toggler" type="button">
          <span class="navbar-toggler-icon"></span>
        </button>
        <h1 class="navbar-brand navbar-brand-autodark">
          <a href="{{ url_for('interface.index') }}">
            <i class="ti ti-device-tv me-2"></i>
            <span>TVBox Manager</span>
          </a>
        </h1>
        
        <!-- 侧边栏内容 -->
        <div class="collapse navbar-collapse" id="sidebar-menu">
          <ul class="navbar-nav pt-lg-3">
            <li class="nav-item {% if active_nav == 'interface' %}active{% endif %}">
              <a class="nav-link" href="{{ url_for('interface.index') }}">
                <span class="nav-link-icon d-md-none d-lg-inline-block">
                  <i class="ti ti-link"></i>
                </span>
                <span class="nav-link-title">接口管理</span>
              </a>
            </li>
            
            <li class="nav-item {% if active_nav == 'decrypt' %}active{% endif %}">
              <a class="nav-link" href="{{ url_for('interface.decrypt') }}">
                <span class="nav-link-icon d-md-none d-lg-inline-block">
                  <i class="ti ti-lock-open"></i>
                </span>
                <span class="nav-link-title">接口解密</span>
              </a>
            </li>
            
            <li class="nav-item {% if active_nav == 'config' %}active{% endif %}">
              <a class="nav-link" href="{{ url_for('config.index') }}">
                <span class="nav-link-icon d-md-none d-lg-inline-block">
                  <i class="ti ti-settings"></i>
                </span>
                <span class="nav-link-title">配置管理</span>
              </a>
            </li>
            
            <li class="nav-item {% if active_nav == 'live' %}active{% endif %}">
              <a class="nav-link" href="{{ url_for('live.index') }}">
                <span class="nav-link-icon d-md-none d-lg-inline-block">
                  <i class="ti ti-device-tv"></i>
                </span>
                <span class="nav-link-title">直播管理</span>
              </a>
            </li>
            
            {% if current_user.has_role('admin') %}
            <li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle" href="#navbar-extra" data-bs-toggle="dropdown" data-bs-auto-close="false" role="button" aria-expanded="false">
                <span class="nav-link-icon d-md-none d-lg-inline-block">
                  <i class="ti ti-shield-lock"></i>
                </span>
                <span class="nav-link-title">管理员功能</span>
              </a>
              <div class="dropdown-menu">
                <a class="dropdown-item" href="#">
                  <span class="nav-link-icon d-md-none d-lg-inline-block">
                    <i class="ti ti-users"></i>
                  </span>
                  用户管理
                </a>
                <a class="dropdown-item" href="#">
                  <span class="nav-link-icon d-md-none d-lg-inline-block">
                    <i class="ti ti-adjustments"></i>
                  </span>
                  系统设置
                </a>
              </div>
            </li>
            {% endif %}
          </ul>
        </div>
      </div>
    </aside>
    
    <!-- 主要内容区 -->
    <div class="page-wrapper">
      <!-- 顶部导航栏 -->
      <header class="navbar navbar-expand-md d-none d-lg-flex d-print-none">
        <div class="container-xl">
          <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-menu">
            <span class="navbar-toggler-icon"></span>
          </button>
          
          <!-- 页面标题 -->
          <div class="navbar-nav flex-row order-md-last">
            <div class="nav-item dropdown">
              <a href="#" class="nav-link d-flex lh-1 text-reset p-0" data-bs-toggle="dropdown" aria-label="Open user menu">
                <div class="d-none d-xl-block ps-2">
                  <div>{{ current_user.email }}</div>
                  <div class="mt-1 small text-muted">
                    {% if current_user.has_role('admin') %}管理员{% else %}用户{% endif %}
                  </div>
                </div>
                <span class="avatar avatar-sm ms-2">{{ current_user.email[0].upper() }}</span>
              </a>
              <div class="dropdown-menu dropdown-menu-end dropdown-menu-arrow">
                <a href="{{ url_for('security.change_password') }}" class="dropdown-item">修改密码</a>
                <a href="{{ url_for('security.logout') }}" class="dropdown-item">退出登录</a>
              </div>
            </div>
          </div>
          
          <div class="collapse navbar-collapse" id="navbar-menu">
            <div>
              <h2 class="page-title">
                {% block page_title %}{% endblock %}
              </h2>
            </div>
          </div>
        </div>
      </header>
      
      <!-- 页面内容 -->
      <div class="page-body">
        <div class="container-xl">
          <!-- 提示消息 -->
          {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
              <div class="alert-container">
                {% for category, message in messages %}
                  {% set alert_class = "alert-" + category if category in ['success', 'danger', 'warning', 'info'] else "alert-info" %}
                  <div class="alert {{ alert_class }} alert-dismissible" role="alert">
                    <div class="d-flex">
                      <div>
                        {% if category == 'success' %}
                          <i class="ti ti-check icon"></i>
                        {% elif category == 'danger' or category == 'error' %}
                          <i class="ti ti-alert-circle icon"></i>
                        {% elif category == 'warning' %}
                          <i class="ti ti-alert-triangle icon"></i>
                        {% else %}
                          <i class="ti ti-info-circle icon"></i>
                        {% endif %}
                      </div>
                      <div>
                        {{ message }}
                      </div>
                    </div>
                    <a class="btn-close alert-close" data-bs-dismiss="alert" aria-label="close"></a>
                  </div>
                {% endfor %}
              </div>
            {% endif %}
          {% endwith %}
          
          <!-- 主要内容 -->
          {% block content %}{% endblock %}
        </div>
      </div>
      
      <!-- 页脚 -->
      <footer class="footer footer-transparent d-print-none">
        <div class="container-xl">
          <div class="row text-center align-items-center flex-row-reverse">
            <div class="col-lg-auto ms-lg-auto">
              <ul class="list-inline list-inline-dots mb-0">
                <li class="list-inline-item">
                  <a href="#" class="link-secondary">文档</a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="link-secondary">帮助</a>
                </li>
              </ul>
            </div>
            <div class="col-12 col-lg-auto mt-3 mt-lg-0">
              <ul class="list-inline list-inline-dots mb-0">
                <li class="list-inline-item">
                  &copy; 2025 TVBox Manager
                </li>
                <li class="list-inline-item">
                  版本 1.0.0
                </li>
              </ul>
            </div>
          </div>
        </div>
      </footer>
    </div>
  </div>
  
  <!-- JS -->
  <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
  <script src="{{ url_for('static', filename='js/tabler.min.js') }}"></script>
  
  <!-- 自定义JS -->
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      // 侧边栏切换
      const navbarToggler = document.querySelector('.navbar-toggler');
      const sidebar = document.getElementById('sidebar');
      
      if (navbarToggler && sidebar) {
        navbarToggler.addEventListener('click', () => {
          sidebar.classList.toggle('navbar-collapsed');
        });
      }
      
      // 自动关闭提示
      const alerts = document.querySelectorAll('.alert');
      alerts.forEach(alert => {
        setTimeout(() => {
          const closeBtn = alert.querySelector('.alert-close');
          if (closeBtn) {
            closeBtn.click();
          }
        }, 5000);
      });
      
      // 激活当前菜单项
      const currentPath = window.location.pathname;
      document.querySelectorAll('.nav-link').forEach(link => {
        if (link.getAttribute('href') === currentPath) {
          link.closest('.nav-item').classList.add('active');
          // 如果在下拉菜单中，打开父级
          const dropdown = link.closest('.dropdown-menu');
          if (dropdown) {
            const parentLink = dropdown.previousElementSibling;
            if (parentLink) {
              parentLink.setAttribute('aria-expanded', 'true');
              dropdown.classList.add('show');
            }
          }
        }
      });
    });
  </script>
  
  {% block extra_js %}{% endblock %}
</body>
</html> 