{"version": 3, "file": "af.mjs", "sources": ["../../../../../packages/locale/lang/af.ts"], "sourcesContent": ["export default {\n  name: 'af',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'Bevestig',\n      clear: '<PERSON>ak skoon',\n    },\n    datepicker: {\n      now: 'Nou',\n      today: 'Vandag',\n      cancel: '<PERSON><PERSON><PERSON><PERSON>',\n      clear: 'Maak skoon',\n      confirm: 'Bevestig',\n      selectDate: 'Kies datum',\n      selectTime: 'Kies tyd',\n      startDate: 'Begindatum',\n      startTime: 'Begintyd',\n      endDate: 'Einddatum',\n      endTime: 'Eindtyd',\n      prevYear: 'Previous Year', // to be translated\n      nextYear: 'Next Year', // to be translated\n      prevMonth: 'Previous Month', // to be translated\n      nextMonth: 'Next Month', // to be translated\n      year: 'Jaar',\n      month1: 'Jan',\n      month2: 'Feb',\n      month3: 'Mrt',\n      month4: 'Apr',\n      month5: 'Mei',\n      month6: 'Jun',\n      month7: 'Jul',\n      month8: 'Aug',\n      month9: 'Sep',\n      month10: 'Okt',\n      month11: 'Nov',\n      month12: 'Des',\n      // week: 'week',\n      weeks: {\n        sun: 'So',\n        mon: 'Ma',\n        tue: 'Di',\n        wed: 'Wo',\n        thu: 'Do',\n        fri: 'Vr',\n        sat: 'Sa',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Feb',\n        mar: 'Mrt',\n        apr: 'Apr',\n        may: 'Mei',\n        jun: 'Jun',\n        jul: 'Jul',\n        aug: 'Aug',\n        sep: 'Sep',\n        oct: 'Okt',\n        nov: 'Nov',\n        dec: 'Des',\n      },\n    },\n    select: {\n      loading: 'Laai',\n      noMatch: 'Geen toepaslike data',\n      noData: 'Geen data',\n      placeholder: 'Kies',\n    },\n    mention: {\n      loading: 'Laai',\n    },\n    cascader: {\n      noMatch: 'Geen toepaslike data',\n      loading: 'Laai',\n      placeholder: 'Kies',\n      noData: 'Geen data',\n    },\n    pagination: {\n      goto: 'Gaan na',\n      pagesize: '/page',\n      total: 'Totaal {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Boodskap',\n      confirm: 'Bevestig',\n      cancel: 'Kanselleer',\n      error: 'Ongeldige invoer',\n    },\n    upload: {\n      deleteTip: 'press delete to remove', // to be translated\n      delete: 'Verwyder',\n      preview: 'Voorskou',\n      continue: 'Gaan voort',\n    },\n    table: {\n      emptyText: 'Geen Data',\n      confirmFilter: 'Bevestig',\n      resetFilter: 'Herstel',\n      clearFilter: 'Alles',\n      sumText: 'Som',\n    },\n    tree: {\n      emptyText: 'Geen Data',\n    },\n    transfer: {\n      noMatch: 'Geen toepaslike data',\n      noData: 'Geen data',\n      titles: ['Lys 1', 'Lys 2'],\n      filterPlaceholder: 'Voer sleutelwoord in',\n      noCheckedFormat: '{total} items',\n      hasCheckedFormat: '{checked}/{total} gekies',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,KAAK;AAChB,MAAM,KAAK,EAAE,QAAQ;AACrB,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,KAAK,EAAE,YAAY;AACzB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,UAAU,EAAE,YAAY;AAC9B,MAAM,UAAU,EAAE,UAAU;AAC5B,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,SAAS,EAAE,UAAU;AAC3B,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,QAAQ,EAAE,eAAe;AAC/B,MAAM,QAAQ,EAAE,WAAW;AAC3B,MAAM,SAAS,EAAE,gBAAgB;AACjC,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,MAAM;AACrB,MAAM,OAAO,EAAE,sBAAsB;AACrC,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,WAAW,EAAE,MAAM;AACzB,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,MAAM;AACrB,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,sBAAsB;AACrC,MAAM,OAAO,EAAE,MAAM;AACrB,MAAM,WAAW,EAAE,MAAM;AACzB,MAAM,MAAM,EAAE,WAAW;AACzB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,QAAQ,EAAE,OAAO;AACvB,MAAM,KAAK,EAAE,gBAAgB;AAC7B,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,UAAU;AACvB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,KAAK,EAAE,kBAAkB;AAC/B,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,QAAQ,EAAE,YAAY;AAC5B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,WAAW;AAC5B,MAAM,aAAa,EAAE,UAAU;AAC/B,MAAM,WAAW,EAAE,SAAS;AAC5B,MAAM,WAAW,EAAE,OAAO;AAC1B,MAAM,OAAO,EAAE,KAAK;AACpB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,WAAW;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,sBAAsB;AACrC,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,MAAM,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;AAChC,MAAM,iBAAiB,EAAE,sBAAsB;AAC/C,MAAM,eAAe,EAAE,eAAe;AACtC,MAAM,gBAAgB,EAAE,0BAA0B;AAClD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,MAAM;AACnB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}