FROM python:3.9-slim

WORKDIR /app

# 安装依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 设置环境变量
ENV FLASK_APP=run.py
ENV FLASK_ENV=production
ENV SECRET_KEY=change-me-in-production
ENV DATABASE_URI=sqlite:////app/data/tvbox_manager.db
ENV ADMIN_EMAIL=<EMAIL>
ENV ADMIN_PASSWORD=admin123

# 创建数据目录
RUN mkdir -p /app/data
RUN mkdir -p /app/instance/uploads

# 暴露端口
EXPOSE 5000

# 初始化数据库
RUN python run.py --init-only

# 启动应用
CMD ["gunicorn", "--bind", "0.0.0.0:5000", "run:app"]