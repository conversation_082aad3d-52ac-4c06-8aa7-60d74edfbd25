#!/usr/bin/env python3
"""
恢复真实配置
"""
import sqlite3
import os
import json

def restore_real_config():
    """恢复真实配置"""
    db_path = "data/tvbox.db"
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取接口2的配置（看起来是真实的）
        cursor.execute("SELECT config_content FROM interface_sources WHERE id = 2")
        result = cursor.fetchone()
        
        if result and result[0]:
            real_config = result[0]
            print("找到接口2的真实配置")
            
            # 解析并显示配置信息
            try:
                config_data = json.loads(real_config)
                spider = config_data.get('spider', 'N/A')
                sites = config_data.get('sites', [])
                
                print(f"Spider: {spider}")
                print(f"Sites数量: {len(sites)}")
                
                # 显示前几个sites
                for i, site in enumerate(sites[:3]):
                    name = site.get('name', 'N/A')
                    api = site.get('api', 'N/A')
                    jar = site.get('jar', 'N/A')
                    print(f"  Site {i+1}: {name}")
                    print(f"    API: {api}")
                    if jar != 'N/A':
                        print(f"    JAR: {jar}")
                
                # 更新接口1的配置
                cursor.execute("""
                    UPDATE interface_sources 
                    SET config_content = ?, localized_config = NULL
                    WHERE id = 1
                """, (real_config,))
                
                # 清理旧的本地化文件记录
                cursor.execute("DELETE FROM localized_files WHERE interface_id = 1")
                
                conn.commit()
                print("\n✅ 已将真实配置恢复到接口1")
                print("✅ 已清理旧的本地化记录")
                
            except json.JSONDecodeError as e:
                print(f"配置解析失败: {e}")
        else:
            print("未找到接口2的配置")
            
    except Exception as e:
        print(f"恢复失败: {str(e)}")
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    restore_real_config()
