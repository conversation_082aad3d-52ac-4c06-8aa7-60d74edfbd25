<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}TVBox Manager{% endblock %}</title>
    
    <!-- 字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- 图标 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    
    <!-- 主样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css') }}">
    
    {% block extra_head %}{% endblock %}
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        {% if current_user.is_authenticated %}
        <aside class="admin-sidebar" id="admin-sidebar">
            <div class="sidebar-header">
                <div class="header-logo">
                    <i class="fas fa-tv"></i>
                </div>
                <button id="sidebar-toggle" class="sidebar-toggle" title="折叠菜单">
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>
            
            <div class="sidebar-content">
                <div class="sidebar-section">
                    <h3 class="sidebar-title">主导航</h3>
                    <ul class="sidebar-nav">
                        <li class="sidebar-item">
                            <a href="{{ url_for('interface.index') }}" class="sidebar-link {% if active_nav == 'interface' %}active{% endif %}">
                                <span class="sidebar-icon"><i class="fas fa-link"></i></span>
                                <span class="sidebar-text">接口管理</span>
                            </a>
                        </li>
                        <li class="sidebar-item">
                            <a href="{{ url_for('interface.decrypt') }}" class="sidebar-link {% if active_nav == 'decrypt' %}active{% endif %}">
                                <span class="sidebar-icon"><i class="fas fa-unlock-alt"></i></span>
                                <span class="sidebar-text">接口解密</span>
                            </a>
                        </li>
                        <li class="sidebar-item">
                            <a href="{{ url_for('config.index') }}" class="sidebar-link {% if active_nav == 'config' %}active{% endif %}">
                                <span class="sidebar-icon"><i class="fas fa-cog"></i></span>
                                <span class="sidebar-text">配置管理</span>
                            </a>
                        </li>
                        <li class="sidebar-item">
                            <a href="{{ url_for('live.index') }}" class="sidebar-link {% if active_nav == 'live' %}active{% endif %}">
                                <span class="sidebar-icon"><i class="fas fa-tv"></i></span>
                                <span class="sidebar-text">直播管理</span>
                            </a>
                        </li>
                    </ul>
                </div>
                
                {% if current_user.has_role('admin') %}
                <div class="sidebar-section">
                    <h3 class="sidebar-title">管理功能</h3>
                    <ul class="sidebar-nav">
                        <li class="sidebar-item">
                            <a href="#" class="sidebar-link">
                                <span class="sidebar-icon"><i class="fas fa-users-cog"></i></span>
                                <span class="sidebar-text">用户管理</span>
                            </a>
                        </li>
                        <li class="sidebar-item">
                            <a href="#" class="sidebar-link">
                                <span class="sidebar-icon"><i class="fas fa-sliders-h"></i></span>
                                <span class="sidebar-text">系统设置</span>
                            </a>
                        </li>
                    </ul>
                </div>
                {% endif %}
            </div>
        </aside>
        {% endif %}
        
        <!-- 内容区域 -->
        <div class="admin-content">
            <!-- 顶部导航栏 -->
            <header class="admin-header">
                <!-- 移动菜单按钮 -->
                {% if current_user.is_authenticated %}
                <button id="mobile-menu-toggle" class="mobile-menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                {% endif %}
                
                <!-- 品牌标识 -->
                <div class="header-brand">
                    <span>TVBox Manager</span>
                </div>
                
                <!-- 导航菜单 -->
                <div class="header-actions">
                    {% if current_user.is_authenticated %}
                    <div class="user-menu">
                        <button class="user-menu-button" id="user-menu-button">
                            <div class="user-avatar">
                                {{ current_user.email[0].upper() }}
                            </div>
                            <div class="user-info">
                                <div class="user-name">{{ current_user.email.split('@')[0] }}</div>
                                <div class="user-role">
                                    {% if current_user.has_role('admin') %}管理员{% else %}用户{% endif %}
                                </div>
                            </div>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        
                        <div class="user-menu-dropdown" id="user-menu-dropdown">
                            <a href="{{ url_for('security.change_password') }}" class="user-menu-item">
                                <i class="fas fa-key user-menu-icon"></i>
                                修改密码
                            </a>
                            <a href="{{ url_for('security.logout') }}" class="user-menu-item">
                                <i class="fas fa-sign-out-alt user-menu-icon"></i>
                                退出登录
                            </a>
                        </div>
                    </div>
                    {% else %}
                    <div>
                        <a href="{{ url_for('security.login') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-sign-in-alt btn-icon"></i> 登录
                        </a>
                        {% if security.registerable %}
                        <a href="{{ url_for('security.register') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-user-plus btn-icon"></i> 注册
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </header>
            
            <!-- 主内容 -->
            <main class="admin-main">
                <!-- 闪烁消息 -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            {% set alert_class = "alert-" + category if category in ['success', 'danger', 'warning', 'info'] else "alert-info" %}
                            <div class="alert {{ alert_class }}">
                                <div class="alert-icon">
                                    {% if category == 'success' %}
                                        <i class="fas fa-check-circle"></i>
                                    {% elif category == 'danger' or category == 'error' %}
                                        <i class="fas fa-exclamation-circle"></i>
                                    {% elif category == 'warning' %}
                                        <i class="fas fa-exclamation-triangle"></i>
                                    {% else %}
                                        <i class="fas fa-info-circle"></i>
                                    {% endif %}
                                </div>
                                <div class="alert-content">
                                    {{ message }}
                                </div>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                <!-- 页面内容 -->
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    
    <!-- 移动端遮罩 -->
    <div class="mobile-overlay" id="mobile-overlay"></div>
    
    <!-- JS脚本 -->
    <script>
        // 用户下拉菜单
        const userMenuButton = document.getElementById('user-menu-button');
        const userMenuDropdown = document.getElementById('user-menu-dropdown');
        
        if (userMenuButton && userMenuDropdown) {
            userMenuButton.addEventListener('click', () => {
                userMenuDropdown.classList.toggle('show');
            });
            
            // 点击外部区域关闭下拉菜单
            document.addEventListener('click', (event) => {
                if (!userMenuButton.contains(event.target) && !userMenuDropdown.contains(event.target)) {
                    userMenuDropdown.classList.remove('show');
                }
            });
        }
        
        // 侧边栏切换
        const sidebarToggle = document.getElementById('sidebar-toggle');
        const sidebar = document.getElementById('admin-sidebar');
        
        if (sidebarToggle && sidebar) {
            // 检查本地存储中的侧边栏状态
            const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            
            // 应用初始状态
            if (sidebarCollapsed) {
                sidebar.classList.add('sidebar-collapsed');
                sidebarToggle.innerHTML = '<i class="fas fa-chevron-right"></i>';
            }
            
            // 自动适应菜单项宽度
            const calculateSidebarWidth = () => {
                // 获取所有菜单项文本
                const menuItems = document.querySelectorAll('.sidebar-text');
                let maxWidth = 240; // 默认宽度
                
                menuItems.forEach(item => {
                    const tempSpan = document.createElement('span');
                    tempSpan.style.visibility = 'hidden';
                    tempSpan.style.position = 'absolute';
                    tempSpan.style.whiteSpace = 'nowrap';
                    tempSpan.style.font = window.getComputedStyle(item).font;
                    tempSpan.textContent = item.textContent;
                    document.body.appendChild(tempSpan);
                    
                    // 菜单项文本宽度 + 图标宽度 + 内边距
                    const itemWidth = tempSpan.offsetWidth + 64;
                    if (itemWidth > maxWidth) maxWidth = itemWidth;
                    
                    document.body.removeChild(tempSpan);
                });
                
                // 限制最大宽度
                maxWidth = Math.min(maxWidth, 280);
                
                // 设置自定义属性存储宽度
                document.documentElement.style.setProperty('--sidebar-width', `${maxWidth}px`);
            };
            
            // 计算侧栏宽度
            calculateSidebarWidth();
            
            // 切换侧边栏状态
            sidebarToggle.addEventListener('click', (e) => {
                e.stopPropagation();
                const isCollapsed = sidebar.classList.toggle('sidebar-collapsed');
                
                if (isCollapsed) {
                    sidebarToggle.innerHTML = '<i class="fas fa-chevron-right"></i>';
                    localStorage.setItem('sidebarCollapsed', 'true');
                } else {
                    sidebarToggle.innerHTML = '<i class="fas fa-chevron-left"></i>';
                    localStorage.setItem('sidebarCollapsed', 'false');
                }
            });
        }
        
        // 移动端菜单
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        const mobileOverlay = document.getElementById('mobile-overlay');
        
        if (mobileMenuToggle && sidebar && mobileOverlay) {
            mobileMenuToggle.addEventListener('click', () => {
                sidebar.classList.toggle('mobile-show');
                mobileOverlay.classList.toggle('show');
            });
            
            mobileOverlay.addEventListener('click', () => {
                sidebar.classList.remove('mobile-show');
                mobileOverlay.classList.remove('show');
            });
        }
        
        // 自动隐藏提示
        const alerts = document.querySelectorAll('.alert');
        
        alerts.forEach(alert => {
            // 5秒后自动隐藏
            setTimeout(() => {
                alert.style.opacity = '0';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 300);
            }, 5000);
        });
        
        // 响应式处理
        const handleResize = () => {
            if (window.innerWidth > 768 && sidebar) {
                // PC模式
                sidebar.classList.remove('mobile-show');
                mobileOverlay.classList.remove('show');
            }
        };
        
        window.addEventListener('resize', handleResize);
        handleResize();
    </script>
    
    <!-- 自定义JS -->
    {% block extra_js %}{% endblock %}
</body>
</html> 
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}TVBox Manager{% endblock %}</title>
    
    <!-- 字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- 图标 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    
    <!-- 主样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css') }}">
    
    {% block extra_head %}{% endblock %}
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        {% if current_user.is_authenticated %}
        <aside class="admin-sidebar" id="admin-sidebar">
            <div class="sidebar-header">
                <div class="header-logo">
                    <i class="fas fa-tv"></i>
                </div>
                <button id="sidebar-toggle" class="sidebar-toggle" title="折叠菜单">
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>
            
            <div class="sidebar-content">
                <div class="sidebar-section">
                    <h3 class="sidebar-title">主导航</h3>
                    <ul class="sidebar-nav">
                        <li class="sidebar-item">
                            <a href="{{ url_for('interface.index') }}" class="sidebar-link {% if active_nav == 'interface' %}active{% endif %}">
                                <span class="sidebar-icon"><i class="fas fa-link"></i></span>
                                <span class="sidebar-text">接口管理</span>
                            </a>
                        </li>
                        <li class="sidebar-item">
                            <a href="{{ url_for('interface.decrypt') }}" class="sidebar-link {% if active_nav == 'decrypt' %}active{% endif %}">
                                <span class="sidebar-icon"><i class="fas fa-unlock-alt"></i></span>
                                <span class="sidebar-text">接口解密</span>
                            </a>
                        </li>
                        <li class="sidebar-item">
                            <a href="{{ url_for('config.index') }}" class="sidebar-link {% if active_nav == 'config' %}active{% endif %}">
                                <span class="sidebar-icon"><i class="fas fa-cog"></i></span>
                                <span class="sidebar-text">配置管理</span>
                            </a>
                        </li>
                        <li class="sidebar-item">
                            <a href="{{ url_for('live.index') }}" class="sidebar-link {% if active_nav == 'live' %}active{% endif %}">
                                <span class="sidebar-icon"><i class="fas fa-tv"></i></span>
                                <span class="sidebar-text">直播管理</span>
                            </a>
                        </li>
                    </ul>
                </div>
                
                {% if current_user.has_role('admin') %}
                <div class="sidebar-section">
                    <h3 class="sidebar-title">管理功能</h3>
                    <ul class="sidebar-nav">
                        <li class="sidebar-item">
                            <a href="#" class="sidebar-link">
                                <span class="sidebar-icon"><i class="fas fa-users-cog"></i></span>
                                <span class="sidebar-text">用户管理</span>
                            </a>
                        </li>
                        <li class="sidebar-item">
                            <a href="#" class="sidebar-link">
                                <span class="sidebar-icon"><i class="fas fa-sliders-h"></i></span>
                                <span class="sidebar-text">系统设置</span>
                            </a>
                        </li>
                    </ul>
                </div>
                {% endif %}
            </div>
        </aside>
        {% endif %}
        
        <!-- 内容区域 -->
        <div class="admin-content">
            <!-- 顶部导航栏 -->
            <header class="admin-header">
                <!-- 移动菜单按钮 -->
                {% if current_user.is_authenticated %}
                <button id="mobile-menu-toggle" class="mobile-menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                {% endif %}
                
                <!-- 品牌标识 -->
                <div class="header-brand">
                    <span>TVBox Manager</span>
                </div>
                
                <!-- 导航菜单 -->
                <div class="header-actions">
                    {% if current_user.is_authenticated %}
                    <div class="user-menu">
                        <button class="user-menu-button" id="user-menu-button">
                            <div class="user-avatar">
                                {{ current_user.email[0].upper() }}
                            </div>
                            <div class="user-info">
                                <div class="user-name">{{ current_user.email.split('@')[0] }}</div>
                                <div class="user-role">
                                    {% if current_user.has_role('admin') %}管理员{% else %}用户{% endif %}
                                </div>
                            </div>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        
                        <div class="user-menu-dropdown" id="user-menu-dropdown">
                            <a href="{{ url_for('security.change_password') }}" class="user-menu-item">
                                <i class="fas fa-key user-menu-icon"></i>
                                修改密码
                            </a>
                            <a href="{{ url_for('security.logout') }}" class="user-menu-item">
                                <i class="fas fa-sign-out-alt user-menu-icon"></i>
                                退出登录
                            </a>
                        </div>
                    </div>
                    {% else %}
                    <div>
                        <a href="{{ url_for('security.login') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-sign-in-alt btn-icon"></i> 登录
                        </a>
                        {% if security.registerable %}
                        <a href="{{ url_for('security.register') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-user-plus btn-icon"></i> 注册
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </header>
            
            <!-- 主内容 -->
            <main class="admin-main">
                <!-- 闪烁消息 -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            {% set alert_class = "alert-" + category if category in ['success', 'danger', 'warning', 'info'] else "alert-info" %}
                            <div class="alert {{ alert_class }}">
                                <div class="alert-icon">
                                    {% if category == 'success' %}
                                        <i class="fas fa-check-circle"></i>
                                    {% elif category == 'danger' or category == 'error' %}
                                        <i class="fas fa-exclamation-circle"></i>
                                    {% elif category == 'warning' %}
                                        <i class="fas fa-exclamation-triangle"></i>
                                    {% else %}
                                        <i class="fas fa-info-circle"></i>
                                    {% endif %}
                                </div>
                                <div class="alert-content">
                                    {{ message }}
                                </div>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                <!-- 页面内容 -->
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    
    <!-- 移动端遮罩 -->
    <div class="mobile-overlay" id="mobile-overlay"></div>
    
    <!-- JS脚本 -->
    <script>
        // 用户下拉菜单
        const userMenuButton = document.getElementById('user-menu-button');
        const userMenuDropdown = document.getElementById('user-menu-dropdown');
        
        if (userMenuButton && userMenuDropdown) {
            userMenuButton.addEventListener('click', () => {
                userMenuDropdown.classList.toggle('show');
            });
            
            // 点击外部区域关闭下拉菜单
            document.addEventListener('click', (event) => {
                if (!userMenuButton.contains(event.target) && !userMenuDropdown.contains(event.target)) {
                    userMenuDropdown.classList.remove('show');
                }
            });
        }
        
        // 侧边栏切换
        const sidebarToggle = document.getElementById('sidebar-toggle');
        const sidebar = document.getElementById('admin-sidebar');
        
        if (sidebarToggle && sidebar) {
            // 检查本地存储中的侧边栏状态
            const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            
            // 应用初始状态
            if (sidebarCollapsed) {
                sidebar.classList.add('sidebar-collapsed');
                sidebarToggle.innerHTML = '<i class="fas fa-chevron-right"></i>';
            }
            
            // 自动适应菜单项宽度
            const calculateSidebarWidth = () => {
                // 获取所有菜单项文本
                const menuItems = document.querySelectorAll('.sidebar-text');
                let maxWidth = 240; // 默认宽度
                
                menuItems.forEach(item => {
                    const tempSpan = document.createElement('span');
                    tempSpan.style.visibility = 'hidden';
                    tempSpan.style.position = 'absolute';
                    tempSpan.style.whiteSpace = 'nowrap';
                    tempSpan.style.font = window.getComputedStyle(item).font;
                    tempSpan.textContent = item.textContent;
                    document.body.appendChild(tempSpan);
                    
                    // 菜单项文本宽度 + 图标宽度 + 内边距
                    const itemWidth = tempSpan.offsetWidth + 64;
                    if (itemWidth > maxWidth) maxWidth = itemWidth;
                    
                    document.body.removeChild(tempSpan);
                });
                
                // 限制最大宽度
                maxWidth = Math.min(maxWidth, 280);
                
                // 设置自定义属性存储宽度
                document.documentElement.style.setProperty('--sidebar-width', `${maxWidth}px`);
            };
            
            // 计算侧栏宽度
            calculateSidebarWidth();
            
            // 切换侧边栏状态
            sidebarToggle.addEventListener('click', (e) => {
                e.stopPropagation();
                const isCollapsed = sidebar.classList.toggle('sidebar-collapsed');
                
                if (isCollapsed) {
                    sidebarToggle.innerHTML = '<i class="fas fa-chevron-right"></i>';
                    localStorage.setItem('sidebarCollapsed', 'true');
                } else {
                    sidebarToggle.innerHTML = '<i class="fas fa-chevron-left"></i>';
                    localStorage.setItem('sidebarCollapsed', 'false');
                }
            });
        }
        
        // 移动端菜单
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        const mobileOverlay = document.getElementById('mobile-overlay');
        
        if (mobileMenuToggle && sidebar && mobileOverlay) {
            mobileMenuToggle.addEventListener('click', () => {
                sidebar.classList.toggle('mobile-show');
                mobileOverlay.classList.toggle('show');
            });
            
            mobileOverlay.addEventListener('click', () => {
                sidebar.classList.remove('mobile-show');
                mobileOverlay.classList.remove('show');
            });
        }
        
        // 自动隐藏提示
        const alerts = document.querySelectorAll('.alert');
        
        alerts.forEach(alert => {
            // 5秒后自动隐藏
            setTimeout(() => {
                alert.style.opacity = '0';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 300);
            }, 5000);
        });
        
        // 响应式处理
        const handleResize = () => {
            if (window.innerWidth > 768 && sidebar) {
                // PC模式
                sidebar.classList.remove('mobile-show');
                mobileOverlay.classList.remove('show');
            }
        };
        
        window.addEventListener('resize', handleResize);
        handleResize();
    </script>
    
    <!-- 自定义JS -->
    {% block extra_js %}{% endblock %}
</body>
</html> 