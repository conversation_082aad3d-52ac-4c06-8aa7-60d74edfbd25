#!/usr/bin/env python3
import sqlite3

def check_database():
    conn = sqlite3.connect('./data/tvbox.db')
    cursor = conn.cursor()
    
    # 检查表名
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    print("Tables:", [t[0] for t in tables])
    
    # 检查接口源表的数据
    for table_name in [t[0] for t in tables if 'interface' in t[0].lower()]:
        print(f"\nTable: {table_name}")
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        print("Columns:", [c[1] for c in columns])
        
        cursor.execute(f"SELECT id, name, config_content FROM {table_name} WHERE id = 1")
        result = cursor.fetchone()
        if result:
            print(f"ID: {result[0]}, Name: {result[1]}")
            config_content = result[2]
            print(f"Config content length: {len(config_content) if config_content else 0}")
            if config_content:
                print(f"Config content preview: {config_content[:500]}...")
            else:
                print("Config content is empty or None")
    
    conn.close()

if __name__ == "__main__":
    check_database()
