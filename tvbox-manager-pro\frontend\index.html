<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>TVBox Manager Pro</title>
    <meta name="description" content="现代化的TVBox接口管理系统" />
    <meta name="keywords" content="TVBox,接口管理,解密,配置" />
    
    <!-- 预加载字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- 防止FOUC -->
    <style>
      #app {
        height: 100vh;
        background: #f5f5f5;
      }
      
      .loading {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON>e UI', <PERSON><PERSON>, sans-serif;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #409EFF;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 16px;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        color: #666;
        font-size: 14px;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <!-- 加载动画 -->
      <div class="loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载 TVBox Manager Pro...</div>
      </div>
    </div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
