{% extends 'base.html' %}

{% block title %}TVBox助手 - 配置管理{% endblock %}

{% block content %}
<div class="container py-4">
    <h1 class="mb-4">
        <i class="fas fa-cog me-2"></i> 配置管理
    </h1>
    
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">上传配置文件</h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('routes.upload_file') }}" method="post" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="formFile" class="form-label">选择TVBox配置文件</label>
                            <input class="form-control" type="file" id="formFile" name="file" accept=".json,.txt">
                            <div class="form-text">支持JSON和TXT格式的TVBox配置文件</div>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload me-2"></i>上传并解析
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">配置文件列表</h5>
                </div>
                <div class="card-body">
                    <div id="configsList">
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status"></div>
                            <div class="mt-2">加载配置文件列表...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 配置详情模态框 -->
    <div class="modal fade" id="configDetailModal" tabindex="-1" aria-labelledby="configDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="configDetailModalLabel">配置详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="configDetailContent">
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status"></div>
                        <div class="mt-2">加载配置详情...</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" id="processConfigBtn">
                        <i class="fas fa-cogs me-2"></i>处理配置
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 处理结果模态框 -->
    <div class="modal fade" id="processResultModal" tabindex="-1" aria-labelledby="processResultModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="processResultModalLabel">处理结果</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="processResultContent">
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status"></div>
                        <div class="mt-2">处理中，请稍候...</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // 加载配置文件列表
    loadConfigsList();
    
    // 当前选中的配置文件
    let currentConfigPath = '';
    
    // 加载配置文件列表
    function loadConfigsList() {
        $.ajax({
            url: '/api/configs',
            method: 'GET',
            success: function(response) {
                if (response.success && response.data.files.length > 0) {
                    // 渲染配置文件列表
                    let html = '<div class="table-responsive"><table class="table table-hover">';
                    html += '<thead><tr><th>文件名</th><th>大小</th><th>修改时间</th><th>操作</th></tr></thead>';
                    html += '<tbody>';
                    
                    response.data.files.forEach(function(file) {
                        html += `
                            <tr>
                                <td>${file.name}</td>
                                <td>${formatFileSize(file.size)}</td>
                                <td>${formatTimestamp(file.modified)}</td>
                                <td>
                                    <button class="btn btn-sm btn-primary view-config" data-filename="${file.name}">
                                        <i class="fas fa-eye"></i> 查看
                                    </button>
                                </td>
                            </tr>
                        `;
                    });
                    
                    html += '</tbody></table></div>';
                    $('#configsList').html(html);
                    
                    // 绑定查看按钮事件
                    $('.view-config').click(function() {
                        const filename = $(this).data('filename');
                        showConfigDetail(filename);
                    });
                } else {
                    $('#configsList').html('<div class="alert alert-info">暂无配置文件，请先上传配置文件</div>');
                }
            },
            error: function() {
                $('#configsList').html('<div class="alert alert-danger">加载配置文件列表失败</div>');
            }
        });
    }
    
    // 显示配置详情
    function showConfigDetail(filename) {
        // 显示模态框
        $('#configDetailModal').modal('show');
        $('#configDetailModalLabel').text('配置详情: ' + filename);
        
        // 清空内容区域并显示加载中
        $('#configDetailContent').html(`
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status"></div>
                <div class="mt-2">加载配置详情...</div>
            </div>
        `);
        
        // 请求配置详情
        $.ajax({
            url: '/api/configs/' + filename,
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    // 保存当前配置路径
                    currentConfigPath = response.data.path;
                    
                    // 渲染配置详情
                    let html = '<ul class="nav nav-tabs" role="tablist">';
                    html += '<li class="nav-item" role="presentation"><button class="nav-link active" id="info-tab" data-bs-toggle="tab" data-bs-target="#info" type="button" role="tab">基本信息</button></li>';
                    html += '<li class="nav-item" role="presentation"><button class="nav-link" id="content-tab" data-bs-toggle="tab" data-bs-target="#content" type="button" role="tab">配置内容</button></li>';
                    html += '</ul>';
                    
                    html += '<div class="tab-content mt-3">';
                    
                    // 基本信息标签页
                    html += '<div class="tab-pane fade show active" id="info" role="tabpanel">';
                    
                    // 检查配置信息是否存在
                    if (response.data.config_info) {
                        const info = response.data.config_info;
                        
                        // 站点信息
                        html += '<h5>站点信息</h5>';
                        if (info.sites && info.sites.length > 0) {
                            html += '<div class="table-responsive"><table class="table table-sm table-bordered">';
                            html += '<thead><tr><th>名称</th><th>键值</th><th>类型</th><th>API</th><th>可搜索</th><th>快速搜索</th><th>可筛选</th></tr></thead>';
                            html += '<tbody>';
                            
                            info.sites.forEach(function(site) {
                                html += `
                                    <tr>
                                        <td>${site.name || '-'}</td>
                                        <td>${site.key || '-'}</td>
                                        <td>${site.type || '-'}</td>
                                        <td>${site.api || '-'}</td>
                                        <td>${site.searchable ? '✓' : '✗'}</td>
                                        <td>${site.quickSearch ? '✓' : '✗'}</td>
                                        <td>${site.filterable ? '✓' : '✗'}</td>
                                    </tr>
                                `;
                            });
                            
                            html += '</tbody></table></div>';
                        } else {
                            html += '<div class="alert alert-info">无站点信息</div>';
                        }
                        
                        // 直播源信息
                        html += '<h5 class="mt-4">直播源信息</h5>';
                        if (info.lives && info.lives.length > 0) {
                            html += '<div class="table-responsive"><table class="table table-sm table-bordered">';
                            html += '<thead><tr><th>分组</th><th>频道数量</th></tr></thead>';
                            html += '<tbody>';
                            
                            info.lives.forEach(function(live) {
                                html += `
                                    <tr>
                                        <td>${live.group || '-'}</td>
                                        <td>${live.channels || 0}</td>
                                    </tr>
                                `;
                            });
                            
                            html += '</tbody></table></div>';
                        } else {
                            html += '<div class="alert alert-info">无直播源信息</div>';
                        }
                        
                        // 解析器信息
                        html += '<h5 class="mt-4">解析器信息</h5>';
                        if (info.parses && info.parses.length > 0) {
                            html += '<div class="table-responsive"><table class="table table-sm table-bordered">';
                            html += '<thead><tr><th>名称</th><th>类型</th><th>URL</th></tr></thead>';
                            html += '<tbody>';
                            
                            info.parses.forEach(function(parse) {
                                html += `
                                    <tr>
                                        <td>${parse.name || '-'}</td>
                                        <td>${parse.type || '-'}</td>
                                        <td>${parse.url || '-'}</td>
                                    </tr>
                                `;
                            });
                            
                            html += '</tbody></table></div>';
                        } else {
                            html += '<div class="alert alert-info">无解析器信息</div>';
                        }
                        
                        // 其他信息
                        html += '<h5 class="mt-4">其他信息</h5>';
                        html += '<div class="row">';
                        
                        html += '<div class="col-md-4"><div class="card mb-3"><div class="card-header">爬虫</div><div class="card-body">';
                        html += info.spider ? info.spider : '无爬虫信息';
                        html += '</div></div></div>';
                        
                        html += '<div class="col-md-4"><div class="card mb-3"><div class="card-header">壁纸</div><div class="card-body">';
                        html += info.wallpaper ? info.wallpaper : '无壁纸信息';
                        html += '</div></div></div>';
                        
                        html += '<div class="col-md-4"><div class="card mb-3"><div class="card-header">播放器配置</div><div class="card-body">';
                        html += info.ijk ? `${info.ijk}个配置项` : '无播放器配置';
                        html += '</div></div></div>';
                        
                        html += '</div>';
                    } else {
                        html += '<div class="alert alert-warning">无法解析配置信息</div>';
                    }
                    
                    html += '</div>';
                    
                    // 配置内容标签页
                    html += '<div class="tab-pane fade" id="content" role="tabpanel">';
                    html += '<div class="d-flex justify-content-end mb-2">';
                    html += '<button class="btn btn-sm btn-outline-secondary copy-btn" data-target="configContent"><i class="fas fa-copy"></i> 复制</button>';
                    html += '</div>';
                    html += '<pre class="p-3 border rounded bg-light"><code id="configContent">' + formatJson(response.data.content) + '</code></pre>';
                    html += '</div>';
                    
                    html += '</div>';
                    
                    $('#configDetailContent').html(html);
                } else {
                    $('#configDetailContent').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i> 加载配置详情失败: ${response.message || '未知错误'}
                        </div>
                    `);
                }
            },
            error: function(xhr) {
                $('#configDetailContent').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i> 加载配置详情失败: ${xhr.responseJSON ? xhr.responseJSON.message : '未知错误'}
                    </div>
                `);
            }
        });
    }
    
    // 处理配置按钮点击事件
    $('#processConfigBtn').click(function() {
        if (!currentConfigPath) {
            alert('未选择配置文件');
            return;
        }
        
        // 显示处理结果模态框
        $('#processResultModal').modal('show');
        
        // 发送处理请求
        $.ajax({
            url: '/api/process',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                file_path: currentConfigPath
            }),
            success: function(response) {
                if (response.success) {
                    // 渲染处理结果
                    const data = response.data;
                    let html = '<div class="alert alert-success"><i class="fas fa-check-circle me-2"></i> 处理成功!</div>';
                    
                    html += '<dl class="row">';
                    html += `<dt class="col-sm-4">输出配置文件</dt><dd class="col-sm-8">${data.output_config_path}</dd>`;
                    html += `<dt class="col-sm-4">总资源数</dt><dd class="col-sm-8">${data.resources_count}</dd>`;
                    html += `<dt class="col-sm-4">下载成功</dt><dd class="col-sm-8">${data.success_count}</dd>`;
                    html += `<dt class="col-sm-4">下载失败</dt><dd class="col-sm-8">${data.failed_count}</dd>`;
                    html += '</dl>';
                    
                    $('#processResultContent').html(html);
                } else {
                    $('#processResultContent').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i> 处理失败: ${response.message || '未知错误'}
                        </div>
                    `);
                }
            },
            error: function(xhr) {
                $('#processResultContent').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i> 处理请求失败: ${xhr.responseJSON ? xhr.responseJSON.message : '未知错误'}
                    </div>
                `);
            }
        });
    });
});
</script>
{% endblock %} 