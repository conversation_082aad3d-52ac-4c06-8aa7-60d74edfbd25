{"version": 3, "file": "ta.min.js", "sources": ["../../../../packages/locale/lang/ta.ts"], "sourcesContent": ["export default {\n  name: 'ta',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'உறுதி செய்',\n      clear: 'தெளிவாக்கு',\n    },\n    datepicker: {\n      now: 'தற்போது',\n      today: 'இன்று',\n      cancel: 'ரத்து செய்',\n      clear: 'சரி',\n      confirm: 'உறுதி செய்',\n      selectDate: 'தேதியை தேர்வு செய்',\n      selectTime: 'நேரத்தை தேர்வு செய்',\n      startDate: 'தொடங்கும் நாள்',\n      startTime: 'தொடங்கும் நேரம்',\n      endDate: 'முடியும் தேதி',\n      endTime: 'முடியும் நேரம்',\n      prevYear: 'Previous Year', // to be translated\n      nextYear: 'Next Year', // to be translated\n      prevMonth: 'Previous Month', // to be translated\n      nextMonth: 'Next Month', // to be translated\n      year: 'வருடம்',\n      month1: 'ஜனவரி',\n      month2: 'பிப்ரவரி',\n      month3: 'மார்ச்',\n      month4: 'ஏப்ரல்',\n      month5: 'மே',\n      month6: 'ஜூன்',\n      month7: 'ஜூலை',\n      month8: 'ஆகஸ்ட்',\n      month9: 'செப்டம்பர்',\n      month10: 'அக்டோபர்',\n      month11: 'நவம்பர்',\n      month12: 'டிசம்பர்',\n      weeks: {\n        sun: 'ஞாயிறு',\n        mon: 'திங்கள்',\n        tue: 'செவ்வாய்',\n        wed: 'புதன்',\n        thu: 'வியாழன்',\n        fri: 'வெள்ளி',\n        sat: 'சனி',\n      },\n      months: {\n        jan: 'ஜனவரி',\n        feb: 'பிப்ரவரி',\n        mar: 'மார்ச்',\n        apr: 'ஏப்ரல்',\n        may: 'மே',\n        jun: 'ஜூன்',\n        jul: 'ஜூலை',\n        aug: 'ஆகஸ்ட்',\n        sep: 'செப்டம்பர்',\n        oct: 'அக்டோபர்',\n        nov: 'நவம்பர்',\n        dec: 'டிசம்பர்',\n      },\n    },\n    select: {\n      loading: 'தயாராகிக்கொண்டிருக்கிறது',\n      noMatch: 'பொருத்தமான தரவு கிடைக்கவில்லை',\n      noData: 'தரவு இல்லை',\n      placeholder: 'தேர்வு செய்',\n    },\n    mention: {\n      loading: 'தயாராகிக்கொண்டிருக்கிறது',\n    },\n    cascader: {\n      noMatch: 'பொருத்தமான தரவு கிடைக்கவில்லை',\n      loading: 'தயாராகிக்கொண்டிருக்கிறது',\n      placeholder: 'தேர்வு செய்',\n      noData: 'தரவு இல்லை',\n    },\n    pagination: {\n      goto: 'தேவையான் பகுதிக்கு செல்',\n      pagesize: '/page',\n      total: 'மொத்தம் {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'செய்தி',\n      confirm: 'உறுதி செய்',\n      cancel: 'ரத்து செய்',\n      error: 'பொருத்தாமில்லாத உள்ளீடு',\n    },\n    upload: {\n      deleteTip: 'press delete to remove', // to be translated\n      delete: 'நீக்கு',\n      preview: 'முன்னோட்டம் பார்',\n      continue: 'தொடரு',\n    },\n    table: {\n      emptyText: 'தரவு இல்லை',\n      confirmFilter: 'உறுதி செய்',\n      resetFilter: 'புதுமாற்றம் செய்',\n      clearFilter: 'அனைத்தும்',\n      sumText: 'கூட்டு',\n    },\n    tree: {\n      emptyText: 'தரவு இல்லை',\n    },\n    transfer: {\n      noMatch: 'பொருத்தமான தரவு கிடைக்கவில்லை',\n      noData: 'தரவு இல்லை',\n      titles: ['பட்டியல் 1', 'பட்டியல் 2'],\n      filterPlaceholder: 'சொல்லை உள்ளீடு செய்',\n      noCheckedFormat: '{total} items', // to be translated\n      hasCheckedFormat: '{checked}/{total} தேர்வு செய்யப்பட்டவைகள்',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,WAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,yDAAyD,CAAC,KAAK,CAAC,8DAA8D,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,4CAA4C,CAAC,KAAK,CAAC,gCAAgC,CAAC,MAAM,CAAC,yDAAyD,CAAC,KAAK,CAAC,oBAAoB,CAAC,OAAO,CAAC,yDAAyD,CAAC,UAAU,CAAC,oGAAoG,CAAC,UAAU,CAAC,0GAA0G,CAAC,SAAS,CAAC,iFAAiF,CAAC,SAAS,CAAC,uFAAuF,CAAC,OAAO,CAAC,2EAA2E,CAAC,OAAO,CAAC,iFAAiF,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,sCAAsC,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,kDAAkD,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,8DAA8D,CAAC,OAAO,CAAC,kDAAkD,CAAC,OAAO,CAAC,4CAA4C,CAAC,OAAO,CAAC,kDAAkD,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,4CAA4C,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,4CAA4C,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,8DAA8D,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,4CAA4C,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,kJAAkJ,CAAC,OAAO,CAAC,sKAAsK,CAAC,MAAM,CAAC,yDAAyD,CAAC,WAAW,CAAC,+DAA+D,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,kJAAkJ,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,sKAAsK,CAAC,OAAO,CAAC,kJAAkJ,CAAC,WAAW,CAAC,+DAA+D,CAAC,MAAM,CAAC,yDAAyD,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,kIAAkI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,oDAAoD,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,sCAAsC,CAAC,OAAO,CAAC,yDAAyD,CAAC,MAAM,CAAC,yDAAyD,CAAC,KAAK,CAAC,uIAAuI,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,MAAM,CAAC,sCAAsC,CAAC,OAAO,CAAC,6FAA6F,CAAC,QAAQ,CAAC,gCAAgC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,yDAAyD,CAAC,aAAa,CAAC,yDAAyD,CAAC,WAAW,CAAC,6FAA6F,CAAC,WAAW,CAAC,wDAAwD,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,yDAAyD,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,sKAAsK,CAAC,MAAM,CAAC,yDAAyD,CAAC,MAAM,CAAC,CAAC,oDAAoD,CAAC,oDAAoD,CAAC,CAAC,iBAAiB,CAAC,0GAA0G,CAAC,eAAe,CAAC,eAAe,CAAC,gBAAgB,CAAC,yJAAyJ,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;;;;;"}