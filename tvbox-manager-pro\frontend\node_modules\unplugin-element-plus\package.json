{"name": "unplugin-element-plus", "version": "0.8.0", "packageManager": "pnpm@8.6.6", "keywords": ["element-plus", "unplugin", "vite", "webpack", "rollup", "esbuild", "plugin"], "homepage": "https://github.com/element-plus/unplugin-element-plus/tree/main/#readme", "bugs": {"url": "https://github.com/element-plus/unplugin-element-plus/issues"}, "repository": {"type": "git", "url": "https://github.com/element-plus/unplugin-element-plus"}, "files": ["dist"], "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": {"require": "./dist/index.d.ts", "import": "./dist/index.d.mts"}, "require": "./dist/index.js", "import": "./dist/index.mjs"}, "./vite": {"types": {"require": "./dist/vite.d.ts", "import": "./dist/vite.d.mts"}, "require": "./dist/vite.js", "import": "./dist/vite.mjs"}, "./webpack": {"types": {"require": "./dist/webpack.d.ts", "import": "./dist/webpack.d.mts"}, "require": "./dist/webpack.js", "import": "./dist/webpack.mjs"}, "./rollup": {"types": {"require": "./dist/rollup.d.ts", "import": "./dist/rollup.d.mts"}, "require": "./dist/rollup.js", "import": "./dist/rollup.mjs"}, "./esbuild": {"types": {"require": "./dist/esbuild.d.ts", "import": "./dist/esbuild.d.mts"}, "require": "./dist/esbuild.js", "import": "./dist/esbuild.mjs"}, "./nuxt": {"types": {"require": "./dist/nuxt.d.ts", "import": "./dist/nuxt.d.mts"}, "require": "./dist/nuxt.js", "import": "./dist/nuxt.mjs"}, "./*": "./*"}, "typesVersions": {"*": {"*": ["./dist/*", "./*"]}}, "publishConfig": {"access": "public"}, "scripts": {"lint": "eslint .", "lint:fix": "pnpm run lint --fix", "build": "tsup", "build:examples": "pnpm --filter \"./examples/*\" build", "dev": "tsup --watch", "release": "bumpp", "vite:build": "npm -C examples/vite run build", "vite:dev": "npm -C examples/vite run dev", "test": "vitest"}, "dependencies": {"@rollup/pluginutils": "^5.0.2", "es-module-lexer": "^1.3.0", "magic-string": "^0.30.1", "unplugin": "^1.3.2"}, "devDependencies": {"@sxzz/eslint-config": "^3.1.0", "@sxzz/prettier-config": "^1.0.3", "@types/node": "^20.4.0", "@vitest/ui": "^0.33.0", "bumpp": "^9.1.1", "esbuild": "~0.18.11", "escape-string-regexp": "^5.0.0", "eslint": "^8.44.0", "fast-glob": "^3.3.0", "prettier": "^3.0.0", "rollup": "^3.26.2", "rollup-plugin-esbuild": "^5.0.0", "tsup": "^7.2.0", "tsx": "^3.12.7", "typescript": "^5.1.6", "vite": "^4.4.1", "vitest": "^0.33.0"}, "engines": {"node": ">=14.19.0"}, "prettier": "@sxzz/prettier-config"}