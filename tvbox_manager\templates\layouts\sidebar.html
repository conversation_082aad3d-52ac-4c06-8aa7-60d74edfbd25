{% if current_user.is_authenticated %}
<!-- 侧边栏 -->
<aside class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <span class="sidebar-header-text">导航菜单</span>
        <button class="sidebar-toggle" id="sidebar-toggle" title="折叠侧边栏">
            <i class="fas fa-chevron-left"></i>
        </button>
    </div>
    <div class="sidebar-content">
        <div class="sidebar-section">
            <a href="{{ url_for('interface.index') }}" class="sidebar-item {% if active_nav == 'interface' %}active{% endif %}">
                <i class="fas fa-link sidebar-item-icon"></i>
                <span class="sidebar-item-text">接口管理</span>
            </a>
            
            <a href="{{ url_for('interface.decrypt') }}" class="sidebar-item {% if active_nav == 'decrypt' %}active{% endif %}">
                <i class="fas fa-unlock-alt sidebar-item-icon"></i>
                <span class="sidebar-item-text">接口解密</span>
            </a>
            
            <a href="{{ url_for('config.index') }}" class="sidebar-item {% if active_nav == 'config' %}active{% endif %}">
                <i class="fas fa-cog sidebar-item-icon"></i>
                <span class="sidebar-item-text">配置管理</span>
            </a>
            
            <a href="{{ url_for('live.index') }}" class="sidebar-item {% if active_nav == 'live' %}active{% endif %}">
                <i class="fas fa-tv sidebar-item-icon"></i>
                <span class="sidebar-item-text">直播管理</span>
            </a>
        </div>
        
        {% if current_user.has_role('admin') %}
        <div class="sidebar-section">
            <div class="sidebar-section-title">管理员功能</div>
            
            <a href="#" class="sidebar-item">
                <i class="fas fa-users-cog sidebar-item-icon"></i>
                <span class="sidebar-item-text">用户管理</span>
            </a>
            
            <a href="#" class="sidebar-item">
                <i class="fas fa-sliders-h sidebar-item-icon"></i>
                <span class="sidebar-item-text">系统设置</span>
            </a>
        </div>
        {% endif %}
    </div>
</aside>
{% endif %} 
<!-- 侧边栏 -->
<aside class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <span class="sidebar-header-text">导航菜单</span>
        <button class="sidebar-toggle" id="sidebar-toggle" title="折叠侧边栏">
            <i class="fas fa-chevron-left"></i>
        </button>
    </div>
    <div class="sidebar-content">
        <div class="sidebar-section">
            <a href="{{ url_for('interface.index') }}" class="sidebar-item {% if active_nav == 'interface' %}active{% endif %}">
                <i class="fas fa-link sidebar-item-icon"></i>
                <span class="sidebar-item-text">接口管理</span>
            </a>
            
            <a href="{{ url_for('interface.decrypt') }}" class="sidebar-item {% if active_nav == 'decrypt' %}active{% endif %}">
                <i class="fas fa-unlock-alt sidebar-item-icon"></i>
                <span class="sidebar-item-text">接口解密</span>
            </a>
            
            <a href="{{ url_for('config.index') }}" class="sidebar-item {% if active_nav == 'config' %}active{% endif %}">
                <i class="fas fa-cog sidebar-item-icon"></i>
                <span class="sidebar-item-text">配置管理</span>
            </a>
            
            <a href="{{ url_for('live.index') }}" class="sidebar-item {% if active_nav == 'live' %}active{% endif %}">
                <i class="fas fa-tv sidebar-item-icon"></i>
                <span class="sidebar-item-text">直播管理</span>
            </a>
        </div>
        
        {% if current_user.has_role('admin') %}
        <div class="sidebar-section">
            <div class="sidebar-section-title">管理员功能</div>
            
            <a href="#" class="sidebar-item">
                <i class="fas fa-users-cog sidebar-item-icon"></i>
                <span class="sidebar-item-text">用户管理</span>
            </a>
            
            <a href="#" class="sidebar-item">
                <i class="fas fa-sliders-h sidebar-item-icon"></i>
                <span class="sidebar-item-text">系统设置</span>
            </a>
        </div>
        {% endif %}
    </div>
</aside>
{% endif %} 