/**
 * 全局指令注册
 */

// 权限指令
const permission = {
  mounted(el, binding) {
    const { value } = binding
    // 这里可以添加权限检查逻辑
    // 如果没有权限，隐藏元素
    if (!value) {
      el.style.display = 'none'
    }
  },
  updated(el, binding) {
    const { value } = binding
    if (!value) {
      el.style.display = 'none'
    } else {
      el.style.display = ''
    }
  }
}

// 防抖指令
const debounce = {
  mounted(el, binding) {
    let timer
    el.addEventListener('click', () => {
      if (timer) {
        clearTimeout(timer)
      }
      timer = setTimeout(() => {
        binding.value()
      }, binding.arg || 300)
    })
  }
}

// 节流指令
const throttle = {
  mounted(el, binding) {
    let timer
    el.addEventListener('click', () => {
      if (timer) {
        return
      }
      timer = setTimeout(() => {
        binding.value()
        timer = null
      }, binding.arg || 300)
    })
  }
}

// 复制指令
const copy = {
  mounted(el, binding) {
    el.addEventListener('click', () => {
      const text = binding.value
      if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
          console.log('复制成功')
        })
      } else {
        // 兼容旧浏览器
        const textarea = document.createElement('textarea')
        textarea.value = text
        document.body.appendChild(textarea)
        textarea.select()
        document.execCommand('copy')
        document.body.removeChild(textarea)
        console.log('复制成功')
      }
    })
  }
}

export function setupDirectives(app) {
  app.directive('permission', permission)
  app.directive('debounce', debounce)
  app.directive('throttle', throttle)
  app.directive('copy', copy)
  
  console.log('全局指令注册完成')
}
