2025-07-29 14:48:11,969 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-29 14:48:11,978 [INFO] root:48 - 数据库表创建完成
2025-07-29 14:48:12,031 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-29 14:48:12,031 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-29 14:51:02,613 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-29 14:51:02,618 [INFO] root:48 - 数据库表创建完成
2025-07-29 14:51:02,679 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-29 14:51:02,679 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-29 14:51:31,134 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-29 14:51:31,139 [INFO] root:48 - 数据库表创建完成
2025-07-29 14:51:31,188 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-29 14:51:31,188 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-29 14:52:09,046 [INFO] watchfiles.main:308 - 1 change detected
2025-07-29 14:52:09,403 [INFO] watchfiles.main:308 - 1 change detected
2025-07-29 14:52:09,761 [INFO] watchfiles.main:308 - 1 change detected
2025-07-29 14:52:10,118 [INFO] watchfiles.main:308 - 1 change detected
2025-07-29 14:52:10,475 [INFO] watchfiles.main:308 - 1 change detected
2025-07-29 14:52:10,831 [INFO] watchfiles.main:308 - 1 change detected
2025-07-29 14:52:11,189 [INFO] watchfiles.main:308 - 1 change detected
2025-07-29 14:52:11,545 [INFO] watchfiles.main:308 - 1 change detected
2025-07-29 14:52:11,903 [INFO] watchfiles.main:308 - 1 change detected
2025-07-29 14:52:12,262 [INFO] watchfiles.main:308 - 1 change detected
2025-07-29 14:52:12,622 [INFO] watchfiles.main:308 - 1 change detected
2025-07-29 14:52:12,993 [INFO] watchfiles.main:308 - 1 change detected
2025-07-29 14:53:10,288 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-29 14:53:10,294 [INFO] root:48 - 数据库表创建完成
2025-07-29 14:53:10,344 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-29 14:53:10,345 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-29 14:53:17,545 [INFO] root:154 - 请求开始: GET http://localhost:8000/
2025-07-29 14:53:17,547 [INFO] root:163 - 请求完成: GET http://localhost:8000/ 状态码: 200 耗时: 0.001s
2025-07-29 14:53:17,581 [INFO] root:154 - 请求开始: GET http://localhost:8000/favicon.ico
2025-07-29 14:53:17,582 [INFO] root:163 - 请求完成: GET http://localhost:8000/favicon.ico 状态码: 404 耗时: 0.000s
2025-07-29 14:53:24,162 [INFO] root:154 - 请求开始: GET http://127.0.0.1:8000/
2025-07-29 14:53:24,165 [INFO] root:163 - 请求完成: GET http://127.0.0.1:8000/ 状态码: 200 耗时: 0.002s
2025-07-29 14:53:24,237 [INFO] root:154 - 请求开始: GET http://127.0.0.1:8000/favicon.ico
2025-07-29 14:53:24,239 [INFO] root:163 - 请求完成: GET http://127.0.0.1:8000/favicon.ico 状态码: 404 耗时: 0.003s
2025-07-29 14:53:39,448 [INFO] root:154 - 请求开始: GET http://localhost:8000/api/v1/auth/me?_t=1753772019418
2025-07-29 14:53:39,477 [INFO] root:163 - 请求完成: GET http://localhost:8000/api/v1/auth/me?_t=1753772019418 状态码: 200 耗时: 0.031s
2025-07-29 14:53:40,607 [INFO] root:154 - 请求开始: GET http://localhost:8000/api/v1/system/stats?_t=1753772020510
2025-07-29 14:53:40,611 [INFO] root:163 - 请求完成: GET http://localhost:8000/api/v1/system/stats?_t=1753772020510 状态码: 200 耗时: 0.004s
2025-07-29 14:55:10,701 [INFO] root:154 - 请求开始: GET http://localhost:8000/api/v1/auth/me?_t=1753772110689
2025-07-29 14:55:10,708 [INFO] root:163 - 请求完成: GET http://localhost:8000/api/v1/auth/me?_t=1753772110689 状态码: 200 耗时: 0.006s
2025-07-29 14:55:11,542 [INFO] root:154 - 请求开始: GET http://localhost:8000/api/v1/system/stats?_t=1753772111439
2025-07-29 14:55:11,547 [INFO] root:163 - 请求完成: GET http://localhost:8000/api/v1/system/stats?_t=1753772111439 状态码: 200 耗时: 0.006s
2025-07-29 14:55:48,454 [INFO] root:154 - 请求开始: GET http://localhost:8000/api/v1/auth/me?_t=1753772148435
2025-07-29 14:55:48,469 [INFO] root:163 - 请求完成: GET http://localhost:8000/api/v1/auth/me?_t=1753772148435 状态码: 200 耗时: 0.016s
2025-07-29 14:55:49,311 [INFO] root:154 - 请求开始: GET http://localhost:8000/api/v1/system/stats?_t=1753772149278
2025-07-29 14:55:49,317 [INFO] root:163 - 请求完成: GET http://localhost:8000/api/v1/system/stats?_t=1753772149278 状态码: 200 耗时: 0.007s
2025-07-29 14:55:49,921 [INFO] root:154 - 请求开始: GET http://localhost:8000/api/v1/auth/me?_t=1753772149894
2025-07-29 14:55:49,928 [INFO] root:163 - 请求完成: GET http://localhost:8000/api/v1/auth/me?_t=1753772149894 状态码: 200 耗时: 0.008s
2025-07-29 14:55:50,413 [INFO] root:154 - 请求开始: GET http://localhost:8000/api/v1/system/stats?_t=1753772150338
2025-07-29 14:55:50,417 [INFO] root:163 - 请求完成: GET http://localhost:8000/api/v1/system/stats?_t=1753772150338 状态码: 200 耗时: 0.003s
2025-07-29 14:56:14,780 [INFO] root:154 - 请求开始: GET http://localhost:8000/api/v1/auth/me?_t=1753772174771
2025-07-29 14:56:14,786 [INFO] root:163 - 请求完成: GET http://localhost:8000/api/v1/auth/me?_t=1753772174771 状态码: 200 耗时: 0.007s
2025-07-29 14:56:15,212 [INFO] root:154 - 请求开始: GET http://localhost:8000/api/v1/system/stats?_t=1753772175133
2025-07-29 14:56:15,215 [INFO] root:163 - 请求完成: GET http://localhost:8000/api/v1/system/stats?_t=1753772175133 状态码: 200 耗时: 0.003s
2025-07-29 15:39:31,520 [INFO] root:154 - 请求开始: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753774771509
2025-07-29 15:39:31,521 [INFO] root:154 - 请求开始: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753774771509
2025-07-29 15:39:31,521 [INFO] root:163 - 请求完成: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753774771509 状态码: 307 耗时: 0.001s
2025-07-29 15:39:31,530 [INFO] root:163 - 请求完成: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753774771509 状态码: 200 耗时: 0.009s
2025-07-29 15:39:31,860 [INFO] root:154 - 请求开始: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753774771509
2025-07-29 15:39:31,872 [INFO] root:163 - 请求完成: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753774771509 状态码: 200 耗时: 0.012s
2025-07-29 15:39:34,245 [INFO] root:154 - 请求开始: GET http://localhost:8000/api/v1/interfaces/1?_t=1753774774240
2025-07-29 15:39:34,249 [INFO] root:163 - 请求完成: GET http://localhost:8000/api/v1/interfaces/1?_t=1753774774240 状态码: 200 耗时: 0.005s
2025-07-29 15:39:34,331 [INFO] root:154 - 请求开始: GET http://localhost:8000/api/v1/interfaces/1/localization-status?_t=1753774774319
2025-07-29 15:39:34,347 [INFO] root:163 - 请求完成: GET http://localhost:8000/api/v1/interfaces/1/localization-status?_t=1753774774319 状态码: 200 耗时: 0.016s
2025-07-29 15:49:34,582 [INFO] root:154 - 请求开始: POST http://localhost:8000/api/v1/interfaces/1/parse
2025-07-29 15:49:34,988 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 15:49:34,988 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 15:49:34,989 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 15:49:34,989 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 15:49:34,992 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 15:49:34,992 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 15:49:34,992 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 15:49:34,994 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 15:49:34,997 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 15:49:34,998 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 15:49:34,998 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 15:49:35,002 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 15:49:35,010 [INFO] root:163 - 请求完成: POST http://localhost:8000/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.428s
2025-07-29 15:49:35,022 [INFO] root:154 - 请求开始: GET http://localhost:8000/api/v1/interfaces/1?_t=1753775375018
2025-07-29 15:49:35,027 [INFO] root:163 - 请求完成: GET http://localhost:8000/api/v1/interfaces/1?_t=1753775375018 状态码: 200 耗时: 0.005s
2025-07-29 15:49:58,154 [INFO] root:154 - 请求开始: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753775397840
2025-07-29 15:49:58,155 [INFO] root:154 - 请求开始: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753775397840
2025-07-29 15:49:58,159 [INFO] root:163 - 请求完成: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753775397840 状态码: 307 耗时: 0.005s
2025-07-29 15:49:58,167 [INFO] root:163 - 请求完成: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753775397840 状态码: 200 耗时: 0.012s
2025-07-29 15:49:58,478 [INFO] root:154 - 请求开始: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753775397840
2025-07-29 15:49:58,482 [INFO] root:163 - 请求完成: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753775397840 状态码: 200 耗时: 0.003s
2025-07-29 15:51:05,346 [INFO] root:154 - 请求开始: GET http://localhost:8000/api/v1/interfaces/1?_t=1753775465039
2025-07-29 15:51:05,349 [INFO] root:163 - 请求完成: GET http://localhost:8000/api/v1/interfaces/1?_t=1753775465039 状态码: 200 耗时: 0.003s
2025-07-29 15:51:05,618 [INFO] root:154 - 请求开始: GET http://localhost:8000/api/v1/interfaces/1/localization-status?_t=1753775465379
2025-07-29 15:51:05,622 [INFO] root:163 - 请求完成: GET http://localhost:8000/api/v1/interfaces/1/localization-status?_t=1753775465379 状态码: 200 耗时: 0.004s
2025-07-29 15:51:05,934 [INFO] root:154 - 请求开始: POST http://localhost:8000/api/v1/interfaces/1/parse
2025-07-29 15:51:06,346 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 15:51:06,347 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 15:51:06,347 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 15:51:06,347 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 15:51:06,349 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 15:51:06,349 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 15:51:06,349 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 15:51:06,351 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 15:51:06,353 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 15:51:06,354 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 15:51:06,354 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 15:51:06,356 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 15:51:06,363 [INFO] root:163 - 请求完成: POST http://localhost:8000/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.429s
2025-07-29 15:51:06,688 [INFO] root:154 - 请求开始: GET http://localhost:8000/api/v1/interfaces/1?_t=1753775466368
2025-07-29 15:51:06,695 [INFO] root:163 - 请求完成: GET http://localhost:8000/api/v1/interfaces/1?_t=1753775466368 状态码: 200 耗时: 0.007s
2025-07-29 15:51:22,129 [INFO] root:154 - 请求开始: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753775482119
2025-07-29 15:51:22,130 [INFO] root:163 - 请求完成: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753775482119 状态码: 307 耗时: 0.001s
2025-07-29 15:51:22,431 [INFO] root:154 - 请求开始: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753775482119
2025-07-29 15:51:22,436 [INFO] root:163 - 请求完成: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753775482119 状态码: 200 耗时: 0.005s
2025-07-29 15:51:22,461 [INFO] root:154 - 请求开始: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753775482119
2025-07-29 15:51:22,465 [INFO] root:163 - 请求完成: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753775482119 状态码: 200 耗时: 0.004s
2025-07-29 15:54:47,961 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-29 15:54:47,968 [INFO] root:48 - 数据库表创建完成
2025-07-29 15:54:48,022 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-29 15:54:48,022 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-29 15:57:13,286 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1
2025-07-29 15:57:13,299 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.013s
2025-07-29 15:57:13,437 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-29 15:57:18,299 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-29 15:57:18,306 [INFO] root:48 - 数据库表创建完成
2025-07-29 15:57:18,366 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-29 15:57:18,366 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-29 15:57:18,373 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized
2025-07-29 15:57:18,376 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized 状态码: 401 耗时: 0.003s
2025-07-29 15:57:20,420 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 15:57:21,000 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 15:57:21,001 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 15:57:21,001 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 15:57:21,002 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 15:57:21,002 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 15:57:21,003 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 15:57:21,003 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 15:57:21,006 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 15:57:21,008 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 15:57:21,008 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 15:57:21,010 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 15:57:21,013 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 15:57:21,023 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.603s
2025-07-29 15:57:23,067 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1
2025-07-29 15:57:23,070 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.003s
2025-07-29 15:57:25,118 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces
2025-07-29 15:57:25,118 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces 状态码: 307 耗时: 0.000s
2025-07-29 15:57:25,120 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/
2025-07-29 15:57:25,128 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/ 状态码: 200 耗时: 0.008s
2025-07-29 15:59:07,448 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-29 15:59:07,455 [INFO] root:48 - 数据库表创建完成
2025-07-29 15:59:07,502 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-29 15:59:07,503 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-29 16:01:37,895 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-29 16:01:42,695 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-29 16:01:42,700 [INFO] root:48 - 数据库表创建完成
2025-07-29 16:01:42,751 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-29 16:01:42,751 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-29 16:04:37,073 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/auth/login
2025-07-29 16:04:37,078 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/auth/login 状态码: 422 耗时: 0.005s
2025-07-29 16:06:21,383 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/auth/login
2025-07-29 16:06:21,703 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.320s
2025-07-29 16:09:07,621 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-29 16:09:07,627 [INFO] root:48 - 数据库表创建完成
2025-07-29 16:09:07,729 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-29 16:09:07,729 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-29 16:10:48,483 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/auth/login
2025-07-29 16:10:48,817 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.334s
2025-07-29 16:10:49,632 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/system/stats?_t=1753776649532
2025-07-29 16:10:49,638 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/system/stats?_t=1753776649532 状态码: 200 耗时: 0.006s
2025-07-29 16:11:05,985 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753776665966
2025-07-29 16:11:05,985 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753776665966
2025-07-29 16:11:05,985 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753776665966 状态码: 307 耗时: 0.001s
2025-07-29 16:11:05,995 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753776665966 状态码: 200 耗时: 0.008s
2025-07-29 16:11:06,346 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753776665966
2025-07-29 16:11:06,383 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753776665966 状态码: 200 耗时: 0.037s
2025-07-29 16:11:09,113 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753776669108
2025-07-29 16:11:09,118 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753776669108 状态码: 200 耗时: 0.005s
2025-07-29 16:11:09,203 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753776669188
2025-07-29 16:11:09,215 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753776669188 状态码: 200 耗时: 0.012s
2025-07-29 16:11:16,886 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753776676872
2025-07-29 16:11:16,887 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753776676872
2025-07-29 16:11:16,888 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753776676872 状态码: 307 耗时: 0.002s
2025-07-29 16:11:16,895 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753776676872 状态码: 200 耗时: 0.008s
2025-07-29 16:11:16,912 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753776676872
2025-07-29 16:11:16,918 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753776676872 状态码: 200 耗时: 0.006s
2025-07-29 16:11:22,671 [INFO] root:154 - 请求开始: DELETE http://localhost:8001/api/v1/interfaces/2
2025-07-29 16:11:22,687 [INFO] app.services.interface_service:405 - 接口源删除成功: 本地化测试接口
2025-07-29 16:11:22,688 [INFO] root:163 - 请求完成: DELETE http://localhost:8001/api/v1/interfaces/2 状态码: 200 耗时: 0.018s
2025-07-29 16:11:22,933 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753776682697
2025-07-29 16:11:22,934 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753776682697 状态码: 307 耗时: 0.001s
2025-07-29 16:11:23,240 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753776682697
2025-07-29 16:11:23,246 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753776682697 状态码: 200 耗时: 0.006s
2025-07-29 16:11:25,679 [INFO] root:154 - 请求开始: DELETE http://localhost:8001/api/v1/interfaces/3
2025-07-29 16:11:25,689 [INFO] app.services.interface_service:405 - 接口源删除成功: 本地化演示接口
2025-07-29 16:11:25,691 [INFO] root:163 - 请求完成: DELETE http://localhost:8001/api/v1/interfaces/3 状态码: 200 耗时: 0.011s
2025-07-29 16:11:26,012 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753776685697
2025-07-29 16:11:26,013 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753776685697 状态码: 307 耗时: 0.001s
2025-07-29 16:11:26,016 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753776685697
2025-07-29 16:11:26,019 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753776685697 状态码: 200 耗时: 0.003s
2025-07-29 16:11:28,474 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753776688466
2025-07-29 16:11:28,477 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753776688466 状态码: 200 耗时: 0.004s
2025-07-29 16:11:31,189 [INFO] root:154 - 请求开始: DELETE http://localhost:8001/api/v1/interfaces/1/localize
2025-07-29 16:11:31,204 [INFO] app.services.localization_service:743 - 接口 1 本地化已禁用
2025-07-29 16:11:31,206 [INFO] root:163 - 请求完成: DELETE http://localhost:8001/api/v1/interfaces/1/localize 状态码: 200 耗时: 0.017s
2025-07-29 16:11:32,401 [INFO] root:154 - 请求开始: PUT http://localhost:8001/api/v1/interfaces/1
2025-07-29 16:11:32,421 [INFO] app.services.interface_service:379 - 接口源更新成功: 真实TVBox配置
2025-07-29 16:11:32,423 [INFO] root:163 - 请求完成: PUT http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.022s
2025-07-29 16:11:33,029 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753776693021
2025-07-29 16:11:33,031 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753776693021 状态码: 307 耗时: 0.001s
2025-07-29 16:11:33,051 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753776693021
2025-07-29 16:11:33,057 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753776693021 状态码: 200 耗时: 0.007s
2025-07-29 16:11:33,333 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753776693021
2025-07-29 16:11:33,338 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753776693021 状态码: 200 耗时: 0.005s
2025-07-29 16:11:33,596 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20
2025-07-29 16:11:33,599 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20 状态码: 200 耗时: 0.003s
2025-07-29 16:11:35,862 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753776695552
2025-07-29 16:11:35,866 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753776695552 状态码: 200 耗时: 0.003s
2025-07-29 16:11:42,263 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753776702258
2025-07-29 16:11:42,264 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753776702258 状态码: 307 耗时: 0.001s
2025-07-29 16:11:42,569 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753776702258
2025-07-29 16:11:42,572 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753776702258 状态码: 200 耗时: 0.003s
2025-07-29 16:11:42,578 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753776702258
2025-07-29 16:11:42,581 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753776702258 状态码: 200 耗时: 0.003s
2025-07-29 16:11:55,077 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753776714771
2025-07-29 16:11:55,079 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753776714771 状态码: 200 耗时: 0.003s
2025-07-29 16:12:29,742 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-29 16:12:33,635 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-29 16:12:33,644 [INFO] root:48 - 数据库表创建完成
2025-07-29 16:12:33,694 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-29 16:12:33,694 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-29 16:15:55,649 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1
2025-07-29 16:15:55,656 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.008s
2025-07-29 16:16:38,722 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 16:16:39,149 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 16:16:39,150 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 16:16:39,150 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 16:16:39,150 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 16:16:39,150 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 16:16:39,151 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 16:16:39,151 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 16:16:39,153 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 16:16:39,156 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 16:16:39,156 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 16:16:39,156 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 16:16:39,160 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 16:16:39,169 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.447s
2025-07-29 16:16:50,254 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 16:16:50,641 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 16:16:50,641 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 16:16:50,643 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 16:16:50,643 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 16:16:50,643 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 16:16:50,644 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 16:16:50,644 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 16:16:50,646 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 16:16:50,649 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 16:16:50,649 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 16:16:50,650 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 16:16:50,651 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 16:16:50,659 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.405s
2025-07-29 16:17:20,669 [INFO] root:154 - 请求开始: DELETE http://localhost:8001/api/v1/interfaces/1/config
2025-07-29 16:17:20,670 [INFO] root:163 - 请求完成: DELETE http://localhost:8001/api/v1/interfaces/1/config 状态码: 404 耗时: 0.001s
2025-07-29 16:17:49,601 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 16:17:50,135 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 16:17:50,135 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 16:17:50,136 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 16:17:50,136 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 16:17:50,136 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 16:17:50,137 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 16:17:50,137 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 16:17:50,139 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 16:17:50,142 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 16:17:50,143 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 16:17:50,143 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 16:17:50,145 [INFO] app.services.interface_service:126 - 接口 真实TVBox配置 检测到变化: ['新建配置']
2025-07-29 16:17:50,169 [INFO] app.services.interface_service:174 - 接口解析成功: 真实TVBox配置 - 解析成功 - 创建配置，变化: ['新建配置']
2025-07-29 16:17:50,169 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.569s
2025-07-29 16:18:14,325 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1
2025-07-29 16:18:14,327 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.002s
2025-07-29 16:22:01,430 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1
2025-07-29 16:22:01,434 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.004s
2025-07-29 16:23:10,915 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/auth/login
2025-07-29 16:23:11,233 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.318s
2025-07-29 16:24:35,393 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/auth/login
2025-07-29 16:24:35,679 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.286s
2025-07-29 16:24:36,371 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/system/stats?_t=1753777476292
2025-07-29 16:24:36,379 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/system/stats?_t=1753777476292 状态码: 200 耗时: 0.008s
2025-07-29 16:24:47,718 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753777487706
2025-07-29 16:24:47,719 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753777487706
2025-07-29 16:24:47,720 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753777487706 状态码: 307 耗时: 0.002s
2025-07-29 16:24:47,727 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753777487706 状态码: 200 耗时: 0.008s
2025-07-29 16:24:48,071 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753777487706
2025-07-29 16:24:48,082 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753777487706 状态码: 200 耗时: 0.011s
2025-07-29 16:24:49,660 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/system/stats?_t=1753777489636
2025-07-29 16:24:49,665 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/system/stats?_t=1753777489636 状态码: 200 耗时: 0.004s
2025-07-29 16:24:57,997 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753777497988
2025-07-29 16:24:57,998 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753777497988
2025-07-29 16:24:58,000 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753777497988 状态码: 307 耗时: 0.003s
2025-07-29 16:24:58,003 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753777497988 状态码: 200 耗时: 0.005s
2025-07-29 16:24:58,016 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753777497988
2025-07-29 16:24:58,020 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753777497988 状态码: 200 耗时: 0.005s
2025-07-29 16:25:00,827 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753777500821
2025-07-29 16:25:00,829 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753777500821 状态码: 200 耗时: 0.003s
2025-07-29 16:25:17,494 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20
2025-07-29 16:25:17,497 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20 状态码: 200 耗时: 0.003s
2025-07-29 16:26:11,848 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753777571526
2025-07-29 16:26:11,850 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753777571526
2025-07-29 16:26:11,850 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753777571526 状态码: 307 耗时: 0.002s
2025-07-29 16:26:11,854 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753777571526 状态码: 200 耗时: 0.004s
2025-07-29 16:26:12,153 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753777571526
2025-07-29 16:26:12,157 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753777571526 状态码: 200 耗时: 0.004s
2025-07-29 16:26:13,617 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753777573613
2025-07-29 16:26:13,620 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753777573613 状态码: 200 耗时: 0.003s
2025-07-29 16:26:21,051 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753777580731
2025-07-29 16:26:21,052 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753777580731
2025-07-29 16:26:21,053 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753777580731 状态码: 307 耗时: 0.002s
2025-07-29 16:26:21,056 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753777580731
2025-07-29 16:26:21,060 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753777580731 状态码: 200 耗时: 0.008s
2025-07-29 16:26:21,062 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753777580731 状态码: 200 耗时: 0.006s
2025-07-29 16:26:25,823 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753777585816
2025-07-29 16:26:25,826 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753777585816 状态码: 200 耗时: 0.003s
2025-07-29 16:26:27,285 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/localize
2025-07-29 16:26:27,303 [INFO] app.services.localization_service:526 - 使用原始配置进行本地化，配置长度: 14966
2025-07-29 16:26:27,304 [INFO] app.services.localization_service:543 - 从原始配置中提取到 16 个URL需要下载
2025-07-29 16:26:27,317 [INFO] app.services.localization_service:587 - 跳过URL: https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg - 不支持的文件类型: .jpg
2025-07-29 16:26:27,325 [INFO] app.services.localization_service:587 - 跳过URL: https://www.zxzjhd.com/ - 网站首页
2025-07-29 16:26:27,333 [INFO] app.services.localization_service:587 - 跳过URL: https://auete.com/ - 网站首页
2025-07-29 16:26:27,340 [INFO] app.services.localization_service:587 - 跳过URL: https://www.xb6v.com/ - 网站首页
2025-07-29 16:26:28,116 [INFO] root:154 - 请求开始: PUT http://localhost:8001/api/v1/interfaces/1
2025-07-29 16:26:28,129 [INFO] app.services.interface_service:379 - 接口源更新成功: 真实TVBox配置
2025-07-29 16:26:28,130 [INFO] root:163 - 请求完成: PUT http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.015s
2025-07-29 16:26:28,859 [INFO] app.services.localization_service:641 - 文件下载成功: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/drpy2.js -> ./真实TVBox配置/js/drpy2.js
2025-07-29 16:26:29,013 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753777588702
2025-07-29 16:26:29,015 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753777588702 状态码: 307 耗时: 0.002s
2025-07-29 16:26:29,016 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753777588702
2025-07-29 16:26:29,021 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753777588702 状态码: 200 耗时: 0.006s
2025-07-29 16:26:29,324 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753777588702
2025-07-29 16:26:29,328 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753777588702 状态码: 200 耗时: 0.003s
2025-07-29 16:26:30,285 [INFO] app.services.localization_service:641 - 文件下载成功: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/huya2.js -> ./真实TVBox配置/js/huya2.js
2025-07-29 16:26:30,292 [ERROR] app.services.localization_service:655 - 处理文件失败 https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/huya2.js: UPDATE statement on table 'localized_files' expected to update 1 row(s); 0 were matched.
2025-07-29 16:26:30,293 [ERROR] app.services.localization_service:655 - 处理文件失败 https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/drpy2.min.js: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: UPDATE statement on table 'localized_files' expected to update 1 row(s); 0 were matched. (Background on this error at: https://sqlalche.me/e/20/7s2a)
2025-07-29 16:26:30,294 [ERROR] app.services.localization_service:655 - 处理文件失败 https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/斗鱼直播.js: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: UPDATE statement on table 'localized_files' expected to update 1 row(s); 0 were matched. (Background on this error at: https://sqlalche.me/e/20/7s2a)
2025-07-29 16:26:30,294 [ERROR] app.services.localization_service:655 - 处理文件失败 https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/drpy2.min.js: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: UPDATE statement on table 'localized_files' expected to update 1 row(s); 0 were matched. (Background on this error at: https://sqlalche.me/e/20/7s2a)
2025-07-29 16:26:30,295 [ERROR] app.services.localization_service:655 - 处理文件失败 https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/有声小说吧.js: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: UPDATE statement on table 'localized_files' expected to update 1 row(s); 0 were matched. (Background on this error at: https://sqlalche.me/e/20/7s2a)
2025-07-29 16:26:30,296 [ERROR] app.services.localization_service:655 - 处理文件失败 https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/drpy2.min.js: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: UPDATE statement on table 'localized_files' expected to update 1 row(s); 0 were matched. (Background on this error at: https://sqlalche.me/e/20/7s2a)
2025-07-29 16:26:30,296 [ERROR] app.services.localization_service:655 - 处理文件失败 https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/%E5%85%94%E5%B0%8F%E8%B4%9D.js: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: UPDATE statement on table 'localized_files' expected to update 1 row(s); 0 were matched. (Background on this error at: https://sqlalche.me/e/20/7s2a)
2025-07-29 16:26:30,297 [ERROR] app.services.localization_service:655 - 处理文件失败 https://scrm-community.oss-cn-shenzhen.aliyuncs.com/miniso-vendor/20250425-868403-********************************.m3u: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: UPDATE statement on table 'localized_files' expected to update 1 row(s); 0 were matched. (Background on this error at: https://sqlalche.me/e/20/7s2a)
2025-07-29 16:26:30,297 [ERROR] app.services.localization_service:655 - 处理文件失败 https://gh-proxy.net/https://raw.githubusercontent.com/wwb521/live/main/tv.m3u: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: UPDATE statement on table 'localized_files' expected to update 1 row(s); 0 were matched. (Background on this error at: https://sqlalche.me/e/20/7s2a)
2025-07-29 16:26:30,298 [ERROR] app.services.localization_service:655 - 处理文件失败 http://127.0.0.1:9978/proxy?do=饭太硬&type=liveList: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: UPDATE statement on table 'localized_files' expected to update 1 row(s); 0 were matched. (Background on this error at: https://sqlalche.me/e/20/7s2a)
2025-07-29 16:26:30,298 [ERROR] app.services.localization_service:655 - 处理文件失败 http://tv.iill.top/m3u/Live: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: UPDATE statement on table 'localized_files' expected to update 1 row(s); 0 were matched. (Background on this error at: https://sqlalche.me/e/20/7s2a)
2025-07-29 16:26:30,302 [INFO] app.services.localization_service:668 - 本地化配置文件已保存: data\localized\真实TVBox配置\api.json
2025-07-29 16:26:30,303 [INFO] app.services.localization_service:686 - 已保存接口 1 的本地化配置
2025-07-29 16:26:30,304 [ERROR] app.api.v1.interfaces:712 - 启用本地化异常: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: UPDATE statement on table 'localized_files' expected to update 1 row(s); 0 were matched. (Background on this error at: https://sqlalche.me/e/20/7s2a)
2025-07-29 16:26:30,304 [ERROR] app.core.database:75 - 数据库会话错误: 500: 启用本地化失败
2025-07-29 16:26:30,306 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/localize 状态码: 500 耗时: 3.021s
2025-07-29 16:26:31,001 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753777590689
2025-07-29 16:26:31,005 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753777590689 状态码: 200 耗时: 0.003s
2025-07-29 16:26:31,261 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753777591050
2025-07-29 16:26:31,269 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753777591050 状态码: 200 耗时: 0.008s
2025-07-29 16:26:38,415 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753777598409
2025-07-29 16:26:38,417 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753777598409 状态码: 307 耗时: 0.002s
2025-07-29 16:26:38,439 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753777598409
2025-07-29 16:26:38,447 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753777598409 状态码: 200 耗时: 0.007s
2025-07-29 16:26:38,720 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753777598409
2025-07-29 16:26:38,725 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753777598409 状态码: 200 耗时: 0.004s
2025-07-29 16:26:40,205 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753777599898
2025-07-29 16:26:40,209 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753777599898 状态码: 200 耗时: 0.005s
2025-07-29 16:26:40,468 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753777600239
2025-07-29 16:26:40,473 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753777600239 状态码: 200 耗时: 0.005s
2025-07-29 16:26:48,820 [INFO] root:154 - 请求开始: GET http://localhost:8001/localized/TVBox/drpy2.js
2025-07-29 16:26:48,824 [INFO] root:163 - 请求完成: GET http://localhost:8001/localized/TVBox/drpy2.js 状态码: 404 耗时: 0.004s
2025-07-29 16:27:10,962 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753777630644
2025-07-29 16:27:10,967 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753777630644 状态码: 200 耗时: 0.004s
2025-07-29 16:28:03,259 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753777683255
2025-07-29 16:28:03,265 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753777683255 状态码: 200 耗时: 0.005s
2025-07-29 16:28:03,734 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753777683424
2025-07-29 16:28:03,737 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753777683424 状态码: 200 耗时: 0.004s
2025-07-29 16:28:03,905 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753777683597
2025-07-29 16:28:03,911 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753777683597 状态码: 200 耗时: 0.006s
2025-07-29 16:28:03,997 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753777683757
2025-07-29 16:28:04,002 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753777683757 状态码: 200 耗时: 0.005s
2025-07-29 16:28:18,061 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753777698055
2025-07-29 16:28:18,062 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753777698055 状态码: 307 耗时: 0.001s
2025-07-29 16:28:18,364 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753777698055
2025-07-29 16:28:18,369 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753777698055 状态码: 200 耗时: 0.005s
2025-07-29 16:28:18,390 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753777698055
2025-07-29 16:28:18,395 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753777698055 状态码: 200 耗时: 0.005s
2025-07-29 16:28:20,936 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753777700631
2025-07-29 16:28:20,938 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753777700631 状态码: 200 耗时: 0.002s
2025-07-29 16:28:21,197 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753777700971
2025-07-29 16:28:21,202 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753777700971 状态码: 200 耗时: 0.005s
2025-07-29 16:28:21,618 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 16:28:22,053 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 16:28:22,055 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 16:28:22,055 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 16:28:22,055 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 16:28:22,055 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 16:28:22,056 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 16:28:22,056 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 16:28:22,058 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 16:28:22,060 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 16:28:22,061 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 16:28:22,061 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 16:28:22,062 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 16:28:22,068 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.450s
2025-07-29 16:28:22,380 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753777702074
2025-07-29 16:28:22,384 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753777702074 状态码: 200 耗时: 0.004s
2025-07-29 16:33:18,293 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753777997976
2025-07-29 16:33:18,294 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753777997976
2025-07-29 16:33:18,295 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753777997976 状态码: 307 耗时: 0.002s
2025-07-29 16:33:18,300 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753777997976 状态码: 200 耗时: 0.006s
2025-07-29 16:33:18,601 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753777997976
2025-07-29 16:33:18,605 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753777997976 状态码: 200 耗时: 0.004s
2025-07-29 16:33:19,985 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753777999981
2025-07-29 16:33:19,988 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753777999981 状态码: 200 耗时: 0.003s
2025-07-29 16:33:20,361 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753778000031
2025-07-29 16:33:20,367 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753778000031 状态码: 200 耗时: 0.006s
2025-07-29 16:38:21,273 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 16:38:21,698 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 16:38:21,698 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 16:38:21,699 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 16:38:21,699 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 16:38:21,699 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 16:38:21,699 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 16:38:21,700 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 16:38:21,701 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 16:38:21,704 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 16:38:21,705 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 16:38:21,705 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 16:38:21,707 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 16:38:21,714 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.442s
2025-07-29 16:38:21,723 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753778301719
2025-07-29 16:38:21,725 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753778301719 状态码: 200 耗时: 0.002s
2025-07-29 16:43:20,321 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 16:43:20,741 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 16:43:20,741 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 16:43:20,742 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 16:43:20,742 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 16:43:20,742 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 16:43:20,743 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 16:43:20,743 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 16:43:20,744 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 16:43:20,747 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 16:43:20,747 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 16:43:20,747 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 16:43:20,750 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 16:43:20,756 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.436s
2025-07-29 16:43:20,859 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-29 16:45:41,801 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-29 16:45:41,807 [INFO] root:48 - 数据库表创建完成
2025-07-29 16:45:41,852 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-29 16:45:41,852 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-29 16:45:41,860 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753778600762
2025-07-29 16:45:41,860 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753778632623
2025-07-29 16:45:41,862 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753778632623
2025-07-29 16:45:41,864 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753778632623 状态码: 307 耗时: 0.002s
2025-07-29 16:45:41,873 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753778600762 状态码: 200 耗时: 0.012s
2025-07-29 16:45:41,881 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753778632623 状态码: 200 耗时: 0.021s
2025-07-29 16:48:01,754 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-29 16:48:01,760 [INFO] root:48 - 数据库表创建完成
2025-07-29 16:48:01,805 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-29 16:48:01,805 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-29 16:48:21,278 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/undefined/parse
2025-07-29 16:48:21,281 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/undefined/parse 状态码: 422 耗时: 0.003s
2025-07-29 16:48:21,736 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/undefined/parse
2025-07-29 16:48:21,739 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/undefined/parse 状态码: 422 耗时: 0.002s
2025-07-29 16:48:39,921 [INFO] root:154 - 请求开始: GET http://localhost:8001/health
2025-07-29 16:48:39,922 [INFO] root:163 - 请求完成: GET http://localhost:8001/health 状态码: 200 耗时: 0.001s
2025-07-29 16:49:34,072 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753778974066
2025-07-29 16:49:34,088 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753778974066 状态码: 200 耗时: 0.016s
2025-07-29 16:49:34,448 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753778974440
2025-07-29 16:49:34,457 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753778974440 状态码: 200 耗时: 0.008s
2025-07-29 16:50:32,321 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/localize
2025-07-29 16:50:32,324 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/localize 状态码: 401 耗时: 0.002s
2025-07-29 16:50:57,326 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/auth/login
2025-07-29 16:50:57,636 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.311s
2025-07-29 16:50:57,886 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/system/stats?_t=1753779057819
2025-07-29 16:50:57,889 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/system/stats?_t=1753779057819 状态码: 200 耗时: 0.003s
2025-07-29 16:51:04,934 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753779064930
2025-07-29 16:51:04,937 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753779064930 状态码: 200 耗时: 0.004s
2025-07-29 16:51:05,116 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753779065111
2025-07-29 16:51:05,119 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753779065111 状态码: 200 耗时: 0.004s
2025-07-29 16:52:40,500 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753779160490
2025-07-29 16:52:40,504 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753779160490 状态码: 200 耗时: 0.004s
2025-07-29 16:52:59,270 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/localize
2025-07-29 16:53:00,022 [WARNING] app.services.new_localization_service:288 - 下载失败: https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5******************************* - HTTP 404
2025-07-29 16:53:13,637 [ERROR] app.services.new_localization_service:195 - 下载文件失败 http://127.0.0.1:9978/proxy?do=饭太硬&type=liveList: Cannot connect to host 127.0.0.1:9978 ssl:default [Connect call failed ('127.0.0.1', 9978)]
2025-07-29 16:53:13,638 [WARNING] app.services.new_localization_service:288 - 下载失败: http://127.0.0.1:9978/proxy?do=饭太硬&type=liveList - Cannot connect to host 127.0.0.1:9978 ssl:default [Connect call failed ('127.0.0.1', 9978)]
2025-07-29 16:53:16,705 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/localize 状态码: 200 耗时: 17.435s
2025-07-29 16:53:18,722 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753779198719
2025-07-29 16:53:18,730 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753779198719 状态码: 200 耗时: 0.008s
2025-07-29 16:53:18,761 [INFO] root:154 - 请求开始: PUT http://localhost:8001/api/v1/interfaces/1
2025-07-29 16:53:18,775 [INFO] app.services.interface_service:379 - 接口源更新成功: 真实TVBox配置
2025-07-29 16:53:18,777 [INFO] root:163 - 请求完成: PUT http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.016s
2025-07-29 16:53:20,717 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/undefined/localization-status?_t=1753779200713
2025-07-29 16:53:20,720 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/undefined/localization-status?_t=1753779200713 状态码: 422 耗时: 0.003s
2025-07-29 16:53:21,369 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753779201363
2025-07-29 16:53:21,370 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753779201363 状态码: 307 耗时: 0.001s
2025-07-29 16:53:21,371 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753779201363
2025-07-29 16:53:21,376 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753779201363 状态码: 200 耗时: 0.004s
2025-07-29 16:53:21,686 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753779201363
2025-07-29 16:53:21,696 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753779201363 状态码: 200 耗时: 0.010s
2025-07-29 16:53:40,474 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753779220469
2025-07-29 16:53:40,477 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753779220469 状态码: 200 耗时: 0.003s
2025-07-29 16:53:40,840 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753779220509
2025-07-29 16:53:40,848 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753779220509 状态码: 200 耗时: 0.009s
2025-07-29 16:53:44,836 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753779224832
2025-07-29 16:53:44,841 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753779224832 状态码: 200 耗时: 0.006s
2025-07-29 16:53:45,265 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753779225259
2025-07-29 16:53:45,267 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753779225259 状态码: 200 耗时: 0.002s
2025-07-29 16:53:45,332 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753779225305
2025-07-29 16:53:45,341 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753779225305 状态码: 200 耗时: 0.009s
2025-07-29 16:54:25,187 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753779265183
2025-07-29 16:54:25,193 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753779265183 状态码: 200 耗时: 0.006s
2025-07-29 16:54:45,744 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/config?type=localized
2025-07-29 16:54:45,746 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/config?type=localized 状态码: 404 耗时: 0.001s
2025-07-29 16:56:38,746 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized
2025-07-29 16:56:38,747 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized 状态码: 401 耗时: 0.001s
2025-07-29 16:56:55,809 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/auth/login
2025-07-29 16:56:55,812 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/auth/login 状态码: 422 耗时: 0.003s
2025-07-29 16:57:12,578 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/auth/login
2025-07-29 16:57:12,586 [ERROR] app.core.database:75 - 数据库会话错误: 401: 邮箱或密码错误
2025-07-29 16:57:12,587 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/auth/login 状态码: 401 耗时: 0.009s
2025-07-29 16:57:38,214 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/auth/login
2025-07-29 16:57:38,494 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.280s
2025-07-29 16:57:40,547 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized
2025-07-29 16:57:40,553 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized 状态码: 200 耗时: 0.006s
2025-07-29 16:59:43,415 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/auth/login
2025-07-29 16:59:43,790 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.375s
2025-07-29 16:59:45,826 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1
2025-07-29 16:59:45,831 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.005s
2025-07-29 16:59:47,879 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized
2025-07-29 16:59:47,889 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized 状态码: 200 耗时: 0.009s
2025-07-29 17:00:09,147 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753779608834
2025-07-29 17:00:09,154 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753779608834 状态码: 200 耗时: 0.008s
2025-07-29 17:00:56,732 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753779656711
2025-07-29 17:00:56,740 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753779656711 状态码: 200 耗时: 0.008s
2025-07-29 17:00:57,475 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753779657390
2025-07-29 17:00:57,514 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753779657390 状态码: 200 耗时: 0.039s
2025-07-29 17:01:12,757 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753779672749
2025-07-29 17:01:12,818 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753779672749 状态码: 200 耗时: 0.061s
2025-07-29 17:01:20,536 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753779680513
2025-07-29 17:01:20,571 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753779680513 状态码: 200 耗时: 0.034s
2025-07-29 17:01:21,736 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753779681305
2025-07-29 17:01:21,764 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753779681305 状态码: 200 耗时: 0.028s
2025-07-29 17:01:40,416 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753779700100
2025-07-29 17:01:40,425 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753779700100 状态码: 200 耗时: 0.010s
2025-07-29 17:05:23,024 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753779922704
2025-07-29 17:05:23,030 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753779922704 状态码: 200 耗时: 0.007s
2025-07-29 17:05:24,882 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753779924876
2025-07-29 17:05:24,883 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753779924876
2025-07-29 17:05:24,883 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753779924876 状态码: 307 耗时: 0.001s
2025-07-29 17:05:24,890 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753779924876 状态码: 200 耗时: 0.007s
2025-07-29 17:05:25,210 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753779924876
2025-07-29 17:05:25,214 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753779924876 状态码: 200 耗时: 0.004s
2025-07-29 17:05:27,114 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753779927108
2025-07-29 17:05:27,118 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753779927108 状态码: 200 耗时: 0.003s
2025-07-29 17:05:27,184 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753779927159
2025-07-29 17:05:27,193 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753779927159 状态码: 200 耗时: 0.008s
2025-07-29 17:05:29,899 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753779929896
2025-07-29 17:05:29,904 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753779929896 状态码: 200 耗时: 0.006s
2025-07-29 17:10:36,674 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753780236667
2025-07-29 17:10:36,685 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753780236667 状态码: 200 耗时: 0.011s
2025-07-29 17:10:36,893 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-29 17:10:41,416 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-29 17:10:41,424 [INFO] root:48 - 数据库表创建完成
2025-07-29 17:10:41,610 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-29 17:10:41,611 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-29 17:10:41,633 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753780237118
2025-07-29 17:10:41,644 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753780237118 状态码: 200 耗时: 0.011s
2025-07-29 17:10:54,978 [INFO] root:154 - 请求开始: DELETE http://localhost:8001/api/v1/interfaces/1/localize
2025-07-29 17:10:55,000 [INFO] root:163 - 请求完成: DELETE http://localhost:8001/api/v1/interfaces/1/localize 状态码: 200 耗时: 0.021s
2025-07-29 17:10:55,993 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/localize
2025-07-29 17:10:56,191 [WARNING] app.services.new_localization_service:291 - 下载失败: https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5******************************* - HTTP 404
2025-07-29 17:10:59,506 [INFO] root:154 - 请求开始: PUT http://localhost:8001/api/v1/interfaces/1
2025-07-29 17:10:59,519 [INFO] app.services.interface_service:379 - 接口源更新成功: 真实TVBox配置
2025-07-29 17:10:59,521 [INFO] root:163 - 请求完成: PUT http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.015s
2025-07-29 17:11:00,522 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753780260514
2025-07-29 17:11:00,525 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753780260514
2025-07-29 17:11:00,525 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753780260514 状态码: 307 耗时: 0.002s
2025-07-29 17:11:00,531 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753780260514 状态码: 200 耗时: 0.006s
2025-07-29 17:11:00,860 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753780260514
2025-07-29 17:11:00,885 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753780260514 状态码: 200 耗时: 0.025s
2025-07-29 17:11:02,521 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753780262517
2025-07-29 17:11:02,527 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753780262517 状态码: 200 耗时: 0.005s
2025-07-29 17:11:02,602 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753780262579
2025-07-29 17:11:02,611 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753780262579 状态码: 200 耗时: 0.009s
2025-07-29 17:11:09,481 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753780269476
2025-07-29 17:11:09,488 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753780269476 状态码: 200 耗时: 0.007s
2025-07-29 17:11:10,352 [ERROR] app.services.new_localization_service:198 - 下载文件失败 http://127.0.0.1:9978/proxy?do=饭太硬&type=liveList: Cannot connect to host 127.0.0.1:9978 ssl:default [Connect call failed ('127.0.0.1', 9978)]
2025-07-29 17:11:10,353 [WARNING] app.services.new_localization_service:291 - 下载失败: http://127.0.0.1:9978/proxy?do=饭太硬&type=liveList - Cannot connect to host 127.0.0.1:9978 ssl:default [Connect call failed ('127.0.0.1', 9978)]
2025-07-29 17:11:10,758 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753780270443
2025-07-29 17:11:10,766 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753780270443 状态码: 200 耗时: 0.007s
2025-07-29 17:11:13,236 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/localize 状态码: 200 耗时: 17.243s
2025-07-29 17:11:15,554 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753780275244
2025-07-29 17:11:15,564 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753780275244 状态码: 200 耗时: 0.009s
2025-07-29 17:11:17,251 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753780277248
2025-07-29 17:11:17,259 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753780277248 状态码: 200 耗时: 0.007s
2025-07-29 17:11:18,915 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 17:11:19,528 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 17:11:19,529 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 17:11:19,529 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 17:11:19,530 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 17:11:19,530 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 17:11:19,530 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 17:11:19,530 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 17:11:19,531 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 17:11:19,535 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 17:11:19,535 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 17:11:19,536 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 17:11:19,539 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 17:11:19,545 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753780279244
2025-07-29 17:11:19,547 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.632s
2025-07-29 17:11:19,556 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753780279244 状态码: 200 耗时: 0.011s
2025-07-29 17:11:19,863 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753780279552
2025-07-29 17:11:19,866 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753780279552 状态码: 200 耗时: 0.003s
2025-07-29 17:11:21,561 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753780281244
2025-07-29 17:11:21,569 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753780281244 状态码: 200 耗时: 0.008s
2025-07-29 17:11:23,286 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753780283281
2025-07-29 17:11:23,287 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753780283281 状态码: 307 耗时: 0.001s
2025-07-29 17:11:23,307 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753780283281
2025-07-29 17:11:23,313 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753780283281 状态码: 200 耗时: 0.005s
2025-07-29 17:11:23,595 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753780283281
2025-07-29 17:11:23,598 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753780283281 状态码: 200 耗时: 0.003s
2025-07-29 17:11:23,626 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/undefined/localization-status?_t=1753780283314
2025-07-29 17:11:23,630 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/undefined/localization-status?_t=1753780283314 状态码: 422 耗时: 0.004s
2025-07-29 17:11:36,558 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753780296552
2025-07-29 17:11:36,570 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753780296552 状态码: 200 耗时: 0.013s
2025-07-29 17:11:36,852 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753780296842
2025-07-29 17:11:36,856 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753780296842 状态码: 200 耗时: 0.004s
2025-07-29 17:13:49,267 [INFO] root:154 - 请求开始: PUT http://localhost:8001/api/v1/interfaces/1
2025-07-29 17:13:49,278 [INFO] app.services.interface_service:379 - 接口源更新成功: 真实TVBox配置
2025-07-29 17:13:49,280 [INFO] root:163 - 请求完成: PUT http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.013s
2025-07-29 17:13:50,150 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753780430145
2025-07-29 17:13:50,151 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753780430145 状态码: 307 耗时: 0.001s
2025-07-29 17:13:50,153 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753780430145
2025-07-29 17:13:50,158 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753780430145 状态码: 200 耗时: 0.005s
2025-07-29 17:13:50,488 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753780430145
2025-07-29 17:13:50,494 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753780430145 状态码: 200 耗时: 0.006s
2025-07-29 17:13:57,152 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753780437147
2025-07-29 17:13:57,154 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753780437147 状态码: 200 耗时: 0.002s
2025-07-29 17:13:57,230 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753780437209
2025-07-29 17:13:57,238 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753780437209 状态码: 200 耗时: 0.008s
2025-07-29 17:14:17,571 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753780457567
2025-07-29 17:14:17,581 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753780457567 状态码: 200 耗时: 0.010s
2025-07-29 17:19:21,760 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753780761449
2025-07-29 17:19:21,765 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753780761449 状态码: 200 耗时: 0.006s
2025-07-29 17:20:20,266 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753780820262
2025-07-29 17:20:20,273 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753780820262 状态码: 200 耗时: 0.007s
2025-07-29 17:20:20,378 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-29 17:20:25,838 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-29 17:20:25,844 [INFO] root:48 - 数据库表创建完成
2025-07-29 17:20:25,903 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-29 17:20:25,903 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-29 17:20:25,911 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753780820698
2025-07-29 17:20:25,921 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753780820698 状态码: 200 耗时: 0.009s
2025-07-29 17:20:33,370 [INFO] root:154 - 请求开始: DELETE http://localhost:8001/api/v1/interfaces/1/localize
2025-07-29 17:20:33,418 [INFO] root:163 - 请求完成: DELETE http://localhost:8001/api/v1/interfaces/1/localize 状态码: 200 耗时: 0.048s
2025-07-29 17:20:40,394 [INFO] root:154 - 请求开始: PUT http://localhost:8001/api/v1/interfaces/1
2025-07-29 17:20:40,409 [INFO] app.services.interface_service:379 - 接口源更新成功: 真实TVBox配置
2025-07-29 17:20:40,410 [INFO] root:163 - 请求完成: PUT http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.017s
2025-07-29 17:20:41,305 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753780841299
2025-07-29 17:20:41,306 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753780841299 状态码: 307 耗时: 0.001s
2025-07-29 17:20:41,308 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753780841299
2025-07-29 17:20:41,315 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753780841299 状态码: 200 耗时: 0.008s
2025-07-29 17:20:41,643 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753780841299
2025-07-29 17:20:41,651 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753780841299 状态码: 200 耗时: 0.008s
2025-07-29 17:20:49,033 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753780849030
2025-07-29 17:20:49,037 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753780849030 状态码: 200 耗时: 0.004s
2025-07-29 17:22:12,479 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753780932167
2025-07-29 17:22:12,479 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753780932167
2025-07-29 17:22:12,481 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753780932167 状态码: 307 耗时: 0.002s
2025-07-29 17:22:12,485 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753780932167 状态码: 200 耗时: 0.006s
2025-07-29 17:22:12,798 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753780932167
2025-07-29 17:22:12,801 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753780932167 状态码: 200 耗时: 0.004s
2025-07-29 17:22:14,336 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753780934332
2025-07-29 17:22:14,339 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753780934332 状态码: 200 耗时: 0.003s
2025-07-29 17:22:36,404 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753780956398
2025-07-29 17:22:36,415 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753780956398 状态码: 200 耗时: 0.011s
2025-07-29 17:22:36,658 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753780956648
2025-07-29 17:22:36,663 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753780956648 状态码: 200 耗时: 0.004s
2025-07-29 17:22:49,838 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/localize
2025-07-29 17:22:49,853 [INFO] app.services.new_localization_service:88 - 检查spider字段: https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************
2025-07-29 17:22:49,854 [INFO] app.services.new_localization_service:91 - Spider URL文件类型: spider
2025-07-29 17:22:49,854 [INFO] app.services.new_localization_service:99 - 添加spider URL到下载列表: https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************
2025-07-29 17:22:49,854 [INFO] app.services.new_localization_service:172 - 开始下载文件: https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5******************************* -> data\localized\interface_1_真实TVBox配置\spider\f0720.jpg
2025-07-29 17:22:50,063 [WARNING] app.services.new_localization_service:298 - 下载失败: https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5******************************* - HTTP 404
2025-07-29 17:22:50,065 [INFO] app.services.new_localization_service:172 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/drpy2.js -> data\localized\interface_1_真实TVBox配置\js\drpy2.js
2025-07-29 17:22:51,530 [INFO] app.services.new_localization_service:172 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/huya2.js -> data\localized\interface_1_真实TVBox配置\js\huya2.js
2025-07-29 17:22:52,911 [INFO] app.services.new_localization_service:172 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/drpy2.min.js -> data\localized\interface_1_真实TVBox配置\js\drpy2.min.js
2025-07-29 17:22:54,138 [INFO] app.services.new_localization_service:172 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/斗鱼直播.js -> data\localized\interface_1_真实TVBox配置\js\斗鱼直播.js
2025-07-29 17:22:55,341 [INFO] app.services.new_localization_service:172 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/drpy2.min.js -> data\localized\interface_1_真实TVBox配置\js\drpy2.min.js
2025-07-29 17:22:56,649 [INFO] app.services.new_localization_service:172 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/有声小说吧.js -> data\localized\interface_1_真实TVBox配置\js\有声小说吧.js
2025-07-29 17:22:57,772 [INFO] app.services.new_localization_service:172 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/drpy2.min.js -> data\localized\interface_1_真实TVBox配置\js\drpy2.min.js
2025-07-29 17:22:59,306 [INFO] app.services.new_localization_service:172 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/%E5%85%94%E5%B0%8F%E8%B4%9D.js -> data\localized\interface_1_真实TVBox配置\js\%E5%85%94%E5%B0%8F%E8%B4%9D.js
2025-07-29 17:23:00,434 [INFO] app.services.new_localization_service:172 - 开始下载文件: https://scrm-community.oss-cn-shenzhen.aliyuncs.com/miniso-vendor/20250425-868403-********************************.m3u -> data\localized\interface_1_真实TVBox配置\live\20250425-868403-********************************.m3u
2025-07-29 17:23:00,625 [INFO] app.services.new_localization_service:172 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/wwb521/live/main/tv.m3u -> data\localized\interface_1_真实TVBox配置\live\tv.m3u
2025-07-29 17:23:01,851 [INFO] app.services.new_localization_service:172 - 开始下载文件: http://127.0.0.1:9978/proxy?do=饭太硬&type=liveList -> data\localized\interface_1_真实TVBox配置\live\live_d04bc4a0.m3u
2025-07-29 17:23:03,884 [ERROR] app.services.new_localization_service:205 - 下载文件失败 http://127.0.0.1:9978/proxy?do=饭太硬&type=liveList: Cannot connect to host 127.0.0.1:9978 ssl:default [Connect call failed ('127.0.0.1', 9978)]
2025-07-29 17:23:03,886 [WARNING] app.services.new_localization_service:298 - 下载失败: http://127.0.0.1:9978/proxy?do=饭太硬&type=liveList - Cannot connect to host 127.0.0.1:9978 ssl:default [Connect call failed ('127.0.0.1', 9978)]
2025-07-29 17:23:03,886 [INFO] app.services.new_localization_service:172 - 开始下载文件: http://tv.iill.top/m3u/Live -> data\localized\interface_1_真实TVBox配置\live\live_c9b0de40.m3u
2025-07-29 17:23:06,929 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/localize 状态码: 200 耗时: 17.090s
2025-07-29 17:23:08,952 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753780988947
2025-07-29 17:23:08,960 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753780988947 状态码: 200 耗时: 0.008s
2025-07-29 17:23:09,169 [INFO] root:154 - 请求开始: PUT http://localhost:8001/api/v1/interfaces/1
2025-07-29 17:23:09,180 [INFO] app.services.interface_service:379 - 接口源更新成功: 真实TVBox配置
2025-07-29 17:23:09,183 [INFO] root:163 - 请求完成: PUT http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.014s
2025-07-29 17:23:09,845 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753780989839
2025-07-29 17:23:09,846 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753780989839
2025-07-29 17:23:09,846 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753780989839 状态码: 307 耗时: 0.001s
2025-07-29 17:23:09,851 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753780989839 状态码: 200 耗时: 0.005s
2025-07-29 17:23:09,868 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753780989839
2025-07-29 17:23:09,872 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753780989839 状态码: 200 耗时: 0.005s
2025-07-29 17:23:10,940 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/undefined/localization-status?_t=1753780990938
2025-07-29 17:23:10,943 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/undefined/localization-status?_t=1753780990938 状态码: 422 耗时: 0.003s
2025-07-29 17:28:24,900 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-29 17:28:24,905 [INFO] root:48 - 数据库表创建完成
2025-07-29 17:28:24,952 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-29 17:28:24,954 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-29 17:28:39,288 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753781319276
2025-07-29 17:28:39,304 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753781319276 状态码: 200 耗时: 0.017s
2025-07-29 17:28:39,557 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753781319551
2025-07-29 17:28:39,569 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753781319551 状态码: 200 耗时: 0.012s
2025-07-29 17:28:39,666 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781319623
2025-07-29 17:28:39,679 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781319623 状态码: 200 耗时: 0.012s
2025-07-29 17:31:37,225 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753781497221
2025-07-29 17:31:37,232 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753781497221 状态码: 200 耗时: 0.007s
2025-07-29 17:31:37,445 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753781497435
2025-07-29 17:31:37,448 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753781497435 状态码: 200 耗时: 0.003s
2025-07-29 17:31:53,306 [INFO] root:154 - 请求开始: DELETE http://localhost:8001/api/v1/interfaces/1/localize
2025-07-29 17:31:53,337 [INFO] root:163 - 请求完成: DELETE http://localhost:8001/api/v1/interfaces/1/localize 状态码: 200 耗时: 0.031s
2025-07-29 17:32:01,516 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/localize
2025-07-29 17:32:01,528 [INFO] app.services.new_localization_service:88 - 检查spider字段: https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************
2025-07-29 17:32:01,529 [INFO] app.services.new_localization_service:91 - Spider URL文件类型: spider
2025-07-29 17:32:01,529 [INFO] app.services.new_localization_service:99 - 添加spider URL到下载列表: https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************
2025-07-29 17:32:01,531 [INFO] app.services.new_localization_service:172 - 开始下载文件: https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5******************************* -> data\localized\interface_1_真实TVBox配置\spider\f0720.jpg
2025-07-29 17:32:01,717 [WARNING] app.services.new_localization_service:298 - 下载失败: https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5******************************* - HTTP 404
2025-07-29 17:32:01,717 [INFO] app.services.new_localization_service:172 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/drpy2.js -> data\localized\interface_1_真实TVBox配置\js\drpy2.js
2025-07-29 17:32:03,050 [INFO] app.services.new_localization_service:172 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/huya2.js -> data\localized\interface_1_真实TVBox配置\js\huya2.js
2025-07-29 17:32:04,409 [INFO] app.services.new_localization_service:172 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/drpy2.min.js -> data\localized\interface_1_真实TVBox配置\js\drpy2.min.js
2025-07-29 17:32:05,634 [INFO] app.services.new_localization_service:172 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/斗鱼直播.js -> data\localized\interface_1_真实TVBox配置\js\斗鱼直播.js
2025-07-29 17:32:06,950 [INFO] app.services.new_localization_service:172 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/drpy2.min.js -> data\localized\interface_1_真实TVBox配置\js\drpy2.min.js
2025-07-29 17:32:08,177 [INFO] app.services.new_localization_service:172 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/有声小说吧.js -> data\localized\interface_1_真实TVBox配置\js\有声小说吧.js
2025-07-29 17:32:09,304 [INFO] app.services.new_localization_service:172 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/drpy2.min.js -> data\localized\interface_1_真实TVBox配置\js\drpy2.min.js
2025-07-29 17:32:11,596 [INFO] app.services.new_localization_service:172 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/%E5%85%94%E5%B0%8F%E8%B4%9D.js -> data\localized\interface_1_真实TVBox配置\js\%E5%85%94%E5%B0%8F%E8%B4%9D.js
2025-07-29 17:32:12,639 [INFO] app.services.new_localization_service:172 - 开始下载文件: https://scrm-community.oss-cn-shenzhen.aliyuncs.com/miniso-vendor/20250425-868403-********************************.m3u -> data\localized\interface_1_真实TVBox配置\live\20250425-868403-********************************.m3u
2025-07-29 17:32:12,837 [INFO] app.services.new_localization_service:172 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/wwb521/live/main/tv.m3u -> data\localized\interface_1_真实TVBox配置\live\tv.m3u
2025-07-29 17:32:14,100 [INFO] app.services.new_localization_service:172 - 开始下载文件: http://127.0.0.1:9978/proxy?do=饭太硬&type=liveList -> data\localized\interface_1_真实TVBox配置\live\live_d04bc4a0.m3u
2025-07-29 17:32:16,138 [ERROR] app.services.new_localization_service:205 - 下载文件失败 http://127.0.0.1:9978/proxy?do=饭太硬&type=liveList: Cannot connect to host 127.0.0.1:9978 ssl:default [Connect call failed ('127.0.0.1', 9978)]
2025-07-29 17:32:16,138 [WARNING] app.services.new_localization_service:298 - 下载失败: http://127.0.0.1:9978/proxy?do=饭太硬&type=liveList - Cannot connect to host 127.0.0.1:9978 ssl:default [Connect call failed ('127.0.0.1', 9978)]
2025-07-29 17:32:16,139 [INFO] app.services.new_localization_service:172 - 开始下载文件: http://tv.iill.top/m3u/Live -> data\localized\interface_1_真实TVBox配置\live\live_c9b0de40.m3u
2025-07-29 17:32:19,128 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/localize 状态码: 200 耗时: 17.612s
2025-07-29 17:32:21,146 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781541141
2025-07-29 17:32:21,152 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781541141 状态码: 200 耗时: 0.007s
2025-07-29 17:32:23,149 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781543146
2025-07-29 17:32:23,157 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781543146 状态码: 200 耗时: 0.008s
2025-07-29 17:32:25,146 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781545142
2025-07-29 17:32:25,154 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781545142 状态码: 200 耗时: 0.008s
2025-07-29 17:32:27,447 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781547134
2025-07-29 17:32:27,454 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781547134 状态码: 200 耗时: 0.007s
2025-07-29 17:32:29,147 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781549143
2025-07-29 17:32:29,156 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781549143 状态码: 200 耗时: 0.009s
2025-07-29 17:32:31,459 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781551144
2025-07-29 17:32:31,467 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781551144 状态码: 200 耗时: 0.008s
2025-07-29 17:32:33,138 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781553134
2025-07-29 17:32:33,145 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781553134 状态码: 200 耗时: 0.008s
2025-07-29 17:32:35,451 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781555139
2025-07-29 17:32:35,460 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781555139 状态码: 200 耗时: 0.009s
2025-07-29 17:32:37,139 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781557137
2025-07-29 17:32:37,149 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781557137 状态码: 200 耗时: 0.009s
2025-07-29 17:32:39,454 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781559142
2025-07-29 17:32:39,463 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781559142 状态码: 200 耗时: 0.008s
2025-07-29 17:32:41,151 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781561148
2025-07-29 17:32:41,160 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781561148 状态码: 200 耗时: 0.009s
2025-07-29 17:32:43,457 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781563142
2025-07-29 17:32:43,468 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781563142 状态码: 200 耗时: 0.011s
2025-07-29 17:32:45,145 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781565142
2025-07-29 17:32:45,153 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781565142 状态码: 200 耗时: 0.008s
2025-07-29 17:32:47,457 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781567144
2025-07-29 17:32:47,467 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781567144 状态码: 200 耗时: 0.010s
2025-07-29 17:32:49,139 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781569134
2025-07-29 17:32:49,147 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781569134 状态码: 200 耗时: 0.008s
2025-07-29 17:34:18,829 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753781658824
2025-07-29 17:34:18,842 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753781658824 状态码: 200 耗时: 0.012s
2025-07-29 17:34:19,075 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753781659069
2025-07-29 17:34:19,078 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753781659069 状态码: 200 耗时: 0.003s
2025-07-29 17:34:19,175 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781659145
2025-07-29 17:34:19,188 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753781659145 状态码: 200 耗时: 0.012s
2025-07-29 17:34:49,136 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753781689133
2025-07-29 17:34:49,142 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753781689133 状态码: 200 耗时: 0.006s
2025-07-29 17:41:10,060 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753782069752
2025-07-29 17:41:10,067 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753782069752 状态码: 200 耗时: 0.007s
2025-07-29 17:41:10,533 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753782070528
2025-07-29 17:41:10,538 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753782070528 状态码: 200 耗时: 0.006s
2025-07-29 17:42:00,798 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753782120482
2025-07-29 17:42:00,803 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753782120482 状态码: 200 耗时: 0.005s
2025-07-29 17:44:19,404 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 17:44:20,138 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 17:44:20,139 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 17:44:20,140 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 17:44:20,140 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 17:44:20,140 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 17:44:20,141 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 17:44:20,141 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 17:44:20,144 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 17:44:20,146 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 17:44:20,147 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 17:44:20,147 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 17:44:20,151 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 17:44:20,158 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.754s
2025-07-29 17:44:20,174 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753782260168
2025-07-29 17:44:20,178 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753782260168 状态码: 200 耗时: 0.005s
2025-07-29 17:44:20,488 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753782260168
2025-07-29 17:44:20,493 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753782260168 状态码: 200 耗时: 0.006s
2025-07-29 17:54:19,413 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 17:54:19,873 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 17:54:19,874 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 17:54:19,874 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 17:54:19,874 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 17:54:19,874 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 17:54:19,875 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 17:54:19,875 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 17:54:19,878 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 17:54:19,882 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 17:54:19,882 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 17:54:19,883 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 17:54:19,885 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 17:54:19,890 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.477s
2025-07-29 17:54:19,900 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753782859896
2025-07-29 17:54:19,902 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753782859896 状态码: 200 耗时: 0.002s
2025-07-29 17:54:20,208 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753782859896
2025-07-29 17:54:20,213 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753782859896 状态码: 200 耗时: 0.005s
2025-07-29 17:54:20,502 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 17:54:20,744 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 17:54:20,745 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 17:54:20,745 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 17:54:20,745 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 17:54:20,745 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 17:54:20,747 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 17:54:20,747 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 17:54:20,749 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 17:54:20,751 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 17:54:20,752 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 17:54:20,752 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 17:54:20,754 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 17:54:20,759 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.256s
2025-07-29 17:54:21,081 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753782860763
2025-07-29 17:54:21,082 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753782860763
2025-07-29 17:54:21,088 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753782860763 状态码: 200 耗时: 0.007s
2025-07-29 17:54:21,089 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753782860763 状态码: 200 耗时: 0.007s
2025-07-29 18:04:19,413 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 18:04:19,870 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 18:04:19,871 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 18:04:19,871 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 18:04:19,871 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 18:04:19,873 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 18:04:19,873 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 18:04:19,873 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 18:04:19,875 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 18:04:19,878 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 18:04:19,879 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 18:04:19,879 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 18:04:19,881 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 18:04:19,887 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.475s
2025-07-29 18:04:19,896 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753783459892
2025-07-29 18:04:19,898 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753783459892 状态码: 200 耗时: 0.003s
2025-07-29 18:04:20,201 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753783459892
2025-07-29 18:04:20,207 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753783459892 状态码: 200 耗时: 0.006s
2025-07-29 18:04:20,211 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 18:04:20,434 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 18:04:20,436 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 18:04:20,436 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 18:04:20,437 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 18:04:20,437 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 18:04:20,437 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 18:04:20,438 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 18:04:20,439 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 18:04:20,442 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 18:04:20,442 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 18:04:20,444 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 18:04:20,446 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 18:04:20,453 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.242s
2025-07-29 18:04:20,496 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 18:04:20,733 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 18:04:20,734 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 18:04:20,734 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 18:04:20,735 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 18:04:20,735 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 18:04:20,736 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 18:04:20,736 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 18:04:20,738 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 18:04:20,741 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 18:04:20,742 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 18:04:20,742 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 18:04:20,745 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 18:04:20,751 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.254s
2025-07-29 18:04:20,775 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753783460458
2025-07-29 18:04:20,776 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753783460458
2025-07-29 18:04:20,782 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753783460458 状态码: 200 耗时: 0.008s
2025-07-29 18:04:20,784 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753783460458 状态码: 200 耗时: 0.008s
2025-07-29 18:04:21,069 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753783460755
2025-07-29 18:04:21,070 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753783460755
2025-07-29 18:04:21,073 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753783460755 状态码: 200 耗时: 0.005s
2025-07-29 18:04:21,079 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753783460755 状态码: 200 耗时: 0.010s
2025-07-29 18:04:21,411 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 18:04:21,825 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 18:04:21,825 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 18:04:21,826 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 18:04:21,826 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 18:04:21,826 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 18:04:21,827 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 18:04:21,827 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 18:04:21,828 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 18:04:21,831 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 18:04:21,832 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 18:04:21,832 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 18:04:21,834 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 18:04:21,840 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.429s
2025-07-29 18:04:21,849 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753783461846
2025-07-29 18:04:21,851 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753783461846 状态码: 200 耗时: 0.002s
2025-07-29 18:04:22,157 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753783461846
2025-07-29 18:04:22,163 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753783461846 状态码: 200 耗时: 0.006s
2025-07-29 18:09:13,016 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753783752699
2025-07-29 18:09:13,022 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753783752699 状态码: 200 耗时: 0.006s
2025-07-29 18:09:14,635 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753783754627
2025-07-29 18:09:14,636 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753783754627
2025-07-29 18:09:14,636 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753783754627 状态码: 307 耗时: 0.001s
2025-07-29 18:09:14,642 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753783754627 状态码: 200 耗时: 0.006s
2025-07-29 18:09:14,974 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753783754627
2025-07-29 18:09:14,987 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753783754627 状态码: 200 耗时: 0.014s
2025-07-29 18:09:17,093 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753783757089
2025-07-29 18:09:17,097 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753783757089 状态码: 200 耗时: 0.004s
2025-07-29 18:09:17,159 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753783757134
2025-07-29 18:09:17,168 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753783757134 状态码: 200 耗时: 0.010s
2025-07-29 18:09:19,245 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753783759242
2025-07-29 18:09:19,251 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753783759242 状态码: 200 耗时: 0.006s
2025-07-29 18:10:12,838 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753783812833
2025-07-29 18:10:12,846 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753783812833 状态码: 200 耗时: 0.009s
2025-07-29 18:12:54,700 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753783974385
2025-07-29 18:12:54,707 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753783974385 状态码: 200 耗时: 0.006s
2025-07-29 18:14:19,407 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 18:14:19,814 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 18:14:19,814 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 18:14:19,815 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 18:14:19,815 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 18:14:19,815 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 18:14:19,816 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 18:14:19,816 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 18:14:19,817 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 18:14:19,819 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 18:14:19,820 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 18:14:19,820 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 18:14:19,822 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 18:14:19,830 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.422s
2025-07-29 18:14:19,838 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753784059834
2025-07-29 18:14:19,840 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753784059834 状态码: 200 耗时: 0.002s
2025-07-29 18:14:20,140 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753784059834
2025-07-29 18:14:20,146 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753784059834 状态码: 200 耗时: 0.006s
2025-07-29 18:14:20,217 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 18:14:20,218 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 18:14:20,449 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 18:14:20,449 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 18:14:20,450 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 18:14:20,450 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 18:14:20,450 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 18:14:20,451 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 18:14:20,451 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 18:14:20,453 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 18:14:20,455 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 18:14:20,455 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 18:14:20,455 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 18:14:20,457 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 18:14:20,670 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 18:14:20,671 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 18:14:20,671 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 18:14:20,672 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 18:14:20,672 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 18:14:20,672 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 18:14:20,672 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 18:14:20,674 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 18:14:20,677 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 18:14:20,677 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 18:14:20,678 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 18:14:20,679 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 18:14:20,686 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.469s
2025-07-29 18:14:20,687 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 18:14:20,688 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.468s
2025-07-29 18:14:21,117 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 18:14:21,117 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 18:14:21,117 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 18:14:21,118 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 18:14:21,118 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 18:14:21,119 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 18:14:21,119 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 18:14:21,121 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 18:14:21,123 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 18:14:21,123 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 18:14:21,124 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 18:14:21,125 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 18:14:21,133 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753784060694
2025-07-29 18:14:21,134 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 18:14:21,134 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753784060690
2025-07-29 18:14:21,135 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753784060690
2025-07-29 18:14:21,135 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753784060694
2025-07-29 18:14:21,135 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.448s
2025-07-29 18:14:21,348 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 18:14:21,349 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 18:14:21,349 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 18:14:21,349 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 18:14:21,350 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 18:14:21,350 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 18:14:21,351 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 18:14:21,352 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 18:14:21,355 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 18:14:21,356 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 18:14:21,356 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 18:14:21,357 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 18:14:21,372 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753784060690 状态码: 200 耗时: 0.238s
2025-07-29 18:14:21,373 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.238s
2025-07-29 18:14:21,373 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753784060694 状态码: 200 耗时: 0.238s
2025-07-29 18:14:21,375 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753784060694 状态码: 200 耗时: 0.241s
2025-07-29 18:14:21,375 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753784060690 状态码: 200 耗时: 0.240s
2025-07-29 18:14:21,449 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 18:14:21,686 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 18:14:21,686 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 18:14:21,687 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 18:14:21,687 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 18:14:21,687 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 18:14:21,688 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 18:14:21,688 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 18:14:21,690 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 18:14:21,692 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 18:14:21,692 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 18:14:21,693 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 18:14:21,694 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 18:14:21,701 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753784061379
2025-07-29 18:14:21,701 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753784061140
2025-07-29 18:14:21,701 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753784061140
2025-07-29 18:14:21,702 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 18:14:21,702 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753784061379
2025-07-29 18:14:21,703 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.254s
2025-07-29 18:14:21,919 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 18:14:21,919 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 18:14:21,920 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 18:14:21,920 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 18:14:21,920 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 18:14:21,921 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 18:14:21,921 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 18:14:21,922 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 18:14:21,925 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 18:14:21,926 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 18:14:21,927 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 18:14:21,928 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 18:14:21,940 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753784061379 状态码: 200 耗时: 0.238s
2025-07-29 18:14:21,941 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.239s
2025-07-29 18:14:21,943 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753784061140 状态码: 200 耗时: 0.242s
2025-07-29 18:14:21,945 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753784061140 状态码: 200 耗时: 0.244s
2025-07-29 18:14:21,946 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753784061379 状态码: 200 耗时: 0.246s
2025-07-29 18:14:22,027 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753784061710
2025-07-29 18:14:22,030 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753784061710 状态码: 200 耗时: 0.003s
2025-07-29 18:14:22,262 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753784061710
2025-07-29 18:14:22,263 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753784061949
2025-07-29 18:14:22,264 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753784061949
2025-07-29 18:14:22,273 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753784061949 状态码: 200 耗时: 0.010s
2025-07-29 18:14:22,274 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753784061710 状态码: 200 耗时: 0.012s
2025-07-29 18:14:22,275 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753784061949 状态码: 200 耗时: 0.011s
2025-07-29 18:15:03,484 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753784103477
2025-07-29 18:15:03,492 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753784103477 状态码: 200 耗时: 0.009s
2025-07-29 18:15:03,835 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753784103820
2025-07-29 18:15:03,839 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753784103820 状态码: 200 耗时: 0.004s
2025-07-29 18:15:03,974 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753784103937
2025-07-29 18:15:03,989 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753784103937 状态码: 200 耗时: 0.015s
2025-07-29 18:15:06,554 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753784106551
2025-07-29 18:15:06,561 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753784106551 状态码: 200 耗时: 0.007s
2025-07-29 18:15:33,038 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753784133035
2025-07-29 18:15:33,044 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753784133035 状态码: 200 耗时: 0.006s
2025-07-29 18:15:33,478 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753784133471
2025-07-29 18:15:33,480 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753784133471 状态码: 200 耗时: 0.003s
2025-07-29 18:15:33,556 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753784133536
2025-07-29 18:15:33,574 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753784133536 状态码: 200 耗时: 0.018s
2025-07-29 18:15:52,893 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753784152887
2025-07-29 18:15:52,900 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753784152887 状态码: 200 耗时: 0.008s
2025-07-29 18:24:51,741 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753784691423
2025-07-29 18:24:51,754 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753784691423 状态码: 200 耗时: 0.013s
2025-07-29 18:24:51,863 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-29 18:24:56,330 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-29 18:24:56,335 [INFO] root:48 - 数据库表创建完成
2025-07-29 18:24:56,385 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-29 18:24:56,385 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-29 18:25:33,512 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 18:25:33,934 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 18:25:33,934 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 18:25:33,934 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 18:25:33,935 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 18:25:33,935 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 18:25:33,935 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 18:25:33,936 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 18:25:33,937 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 18:25:33,941 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 18:25:33,941 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 18:25:33,941 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 18:25:33,946 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 18:25:33,952 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.440s
2025-07-29 18:25:34,270 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753784733959
2025-07-29 18:25:34,271 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753784733959
2025-07-29 18:25:34,275 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753784733959 状态码: 200 耗时: 0.004s
2025-07-29 18:25:34,289 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753784733959 状态码: 200 耗时: 0.018s
2025-07-29 18:28:31,762 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/auth/login
2025-07-29 18:28:32,077 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.315s
2025-07-29 18:28:32,180 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-29 18:28:36,207 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-29 18:28:36,214 [INFO] root:48 - 数据库表创建完成
2025-07-29 18:28:36,260 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-29 18:28:36,260 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-29 18:28:36,267 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status
2025-07-29 18:28:36,285 [ERROR] app.services.new_localization_service:439 - 获取本地化状态失败: 'size'
2025-07-29 18:28:36,285 [ERROR] app.api.v1.interfaces:773 - 获取本地化状态异常: 'files_count'
2025-07-29 18:28:36,286 [ERROR] app.core.database:75 - 数据库会话错误: 500: 获取本地化状态失败
2025-07-29 18:28:36,286 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status 状态码: 500 耗时: 0.019s
2025-07-29 18:29:15,332 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/auth/login
2025-07-29 18:29:15,646 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.315s
2025-07-29 18:29:15,763 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-29 18:29:19,816 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-29 18:29:19,823 [INFO] root:48 - 数据库表创建完成
2025-07-29 18:29:19,870 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-29 18:29:19,870 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-29 18:29:19,879 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status
2025-07-29 18:29:19,897 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status 状态码: 200 耗时: 0.018s
2025-07-29 18:29:31,375 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753784971056
2025-07-29 18:29:31,386 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753784971056 状态码: 200 耗时: 0.011s
2025-07-29 18:31:03,684 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753785063369
2025-07-29 18:31:03,689 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753785063369 状态码: 200 耗时: 0.005s
2025-07-29 18:31:12,928 [INFO] root:154 - 请求开始: GET http://localhost:8001/localized/TVBox/live/20250425-868403-********************************.m3u
2025-07-29 18:31:12,932 [INFO] root:163 - 请求完成: GET http://localhost:8001/localized/TVBox/live/20250425-868403-********************************.m3u 状态码: 404 耗时: 0.004s
2025-07-29 18:31:16,964 [INFO] root:154 - 请求开始: GET http://localhost:8001/localized/TVBox/js/兔小贝.js
2025-07-29 18:31:16,968 [INFO] root:163 - 请求完成: GET http://localhost:8001/localized/TVBox/js/兔小贝.js 状态码: 404 耗时: 0.003s
2025-07-29 18:31:21,646 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753785081643
2025-07-29 18:31:21,655 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753785081643 状态码: 200 耗时: 0.009s
2025-07-29 18:31:31,258 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753785090939
2025-07-29 18:31:31,264 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753785090939 状态码: 200 耗时: 0.006s
2025-07-29 18:35:33,804 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 18:35:34,217 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 18:35:34,218 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 18:35:34,218 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 18:35:34,218 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 18:35:34,218 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 18:35:34,219 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 18:35:34,219 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 18:35:34,222 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 18:35:34,224 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 18:35:34,225 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 18:35:34,225 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 18:35:34,228 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 18:35:34,238 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.434s
2025-07-29 18:35:34,251 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753785334247
2025-07-29 18:35:34,254 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753785334247 状态码: 200 耗时: 0.003s
2025-07-29 18:35:34,567 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753785334247
2025-07-29 18:35:34,572 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753785334247 状态码: 200 耗时: 0.006s
2025-07-29 18:35:34,598 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 18:35:34,825 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 18:35:34,826 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 18:35:34,826 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 18:35:34,827 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 18:35:34,827 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 18:35:34,828 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 18:35:34,828 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 18:35:34,830 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 18:35:34,833 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 18:35:34,833 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 18:35:34,834 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 18:35:34,835 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 18:35:34,843 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.244s
2025-07-29 18:35:35,158 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753785334849
2025-07-29 18:35:35,159 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753785334849
2025-07-29 18:35:35,163 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753785334849 状态码: 200 耗时: 0.005s
2025-07-29 18:35:35,168 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753785334849 状态码: 200 耗时: 0.009s
2025-07-29 18:36:39,386 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753785399377
2025-07-29 18:36:39,389 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753785399377 状态码: 200 耗时: 0.005s
2025-07-29 18:36:39,495 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753785399451
2025-07-29 18:36:39,506 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753785399451 状态码: 200 耗时: 0.011s
2025-07-29 18:36:58,220 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753785418215
2025-07-29 18:36:58,229 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753785418215 状态码: 200 耗时: 0.008s
2025-07-29 18:36:58,490 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753785418484
2025-07-29 18:36:58,494 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753785418484 状态码: 200 耗时: 0.004s
2025-07-29 18:36:58,559 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753785418538
2025-07-29 18:36:58,568 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753785418538 状态码: 200 耗时: 0.009s
2025-07-29 18:37:15,956 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753785435953
2025-07-29 18:37:15,961 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753785435953 状态码: 200 耗时: 0.005s
2025-07-29 18:37:46,401 [INFO] root:154 - 请求开始: GET http://localhost:8001/localized/interface_1___TVBox__/js/兔小贝.js
2025-07-29 18:37:46,405 [INFO] root:163 - 请求完成: GET http://localhost:8001/localized/interface_1___TVBox__/js/兔小贝.js 状态码: 404 耗时: 0.004s
2025-07-29 18:38:35,229 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753785515223
2025-07-29 18:38:35,234 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753785515223 状态码: 200 耗时: 0.005s
2025-07-29 18:38:35,312 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753785515280
2025-07-29 18:38:35,322 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753785515280 状态码: 200 耗时: 0.010s
2025-07-29 18:38:40,380 [INFO] root:154 - 请求开始: GET http://localhost:8001/localized/interface_1___TVBox__/js/兔小贝.js
2025-07-29 18:38:40,384 [INFO] root:163 - 请求完成: GET http://localhost:8001/localized/interface_1___TVBox__/js/兔小贝.js 状态码: 404 耗时: 0.003s
2025-07-29 18:41:15,186 [INFO] root:154 - 请求开始: GET http://localhost:8001/localized/interface_1___TVBox__/js/兔小贝.js
2025-07-29 18:41:15,189 [INFO] root:163 - 请求完成: GET http://localhost:8001/localized/interface_1___TVBox__/js/兔小贝.js 状态码: 404 耗时: 0.003s
2025-07-29 18:41:47,159 [INFO] root:154 - 请求开始: GET http://localhost:8001/localized/interface_1_真实TVBox配置/js/兔小贝.js
2025-07-29 18:41:47,162 [INFO] root:163 - 请求完成: GET http://localhost:8001/localized/interface_1_真实TVBox配置/js/兔小贝.js 状态码: 404 耗时: 0.004s
2025-07-29 18:42:20,314 [INFO] root:154 - 请求开始: GET http://localhost:8001/localized/interface_1_真实TVBox配置/js/drpy2.js
2025-07-29 18:42:20,429 [INFO] root:163 - 请求完成: GET http://localhost:8001/localized/interface_1_真实TVBox配置/js/drpy2.js 状态码: 200 耗时: 0.114s
2025-07-29 18:43:33,115 [INFO] root:154 - 请求开始: GET http://localhost:8001/localized/interface_1_真实TVBox配置/js/斗鱼直播.js
2025-07-29 18:43:33,119 [INFO] root:163 - 请求完成: GET http://localhost:8001/localized/interface_1_真实TVBox配置/js/斗鱼直播.js 状态码: 200 耗时: 0.003s
2025-07-29 18:43:36,647 [INFO] root:154 - 请求开始: GET http://localhost:8001/localized/interface_1_真实TVBox配置/js/兔小贝.js
2025-07-29 18:43:36,649 [INFO] root:163 - 请求完成: GET http://localhost:8001/localized/interface_1_真实TVBox配置/js/兔小贝.js 状态码: 404 耗时: 0.002s
2025-07-29 18:43:44,948 [INFO] root:154 - 请求开始: GET http://localhost:8001/localized/interface_1_真实TVBox配置/live/20250425-868403-********************************.m3u
2025-07-29 18:43:44,951 [INFO] root:163 - 请求完成: GET http://localhost:8001/localized/interface_1_真实TVBox配置/live/20250425-868403-********************************.m3u 状态码: 200 耗时: 0.002s
2025-07-29 18:45:39,326 [INFO] root:154 - 请求开始: GET http://localhost:8001/localized/interface_1_真实TVBox配置/live/tv.m3u
2025-07-29 18:45:39,329 [INFO] root:163 - 请求完成: GET http://localhost:8001/localized/interface_1_真实TVBox配置/live/tv.m3u 状态码: 200 耗时: 0.003s
2025-07-29 18:46:10,724 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753785970720
2025-07-29 18:46:10,732 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753785970720 状态码: 200 耗时: 0.008s
2025-07-29 18:48:35,564 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 18:48:36,069 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 18:48:36,071 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 18:48:36,071 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 18:48:36,072 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 18:48:36,072 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 18:48:36,072 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 18:48:36,073 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 18:48:36,077 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 18:48:36,083 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 18:48:36,084 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 18:48:36,084 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 18:48:36,085 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 18:48:36,091 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.527s
2025-07-29 18:48:36,103 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753786116100
2025-07-29 18:48:36,107 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753786116100 状态码: 200 耗时: 0.003s
2025-07-29 18:48:36,416 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753786116100
2025-07-29 18:48:36,422 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753786116100 状态码: 200 耗时: 0.006s
2025-07-29 18:54:47,118 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-29 18:54:47,123 [INFO] root:48 - 数据库表创建完成
2025-07-29 18:54:47,170 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-29 18:54:47,171 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-29 18:54:57,960 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753786497946
2025-07-29 18:54:57,975 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753786497946 状态码: 200 耗时: 0.015s
2025-07-29 18:54:58,289 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753786498281
2025-07-29 18:54:58,298 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753786498281 状态码: 200 耗时: 0.008s
2025-07-29 18:54:58,389 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753786498368
2025-07-29 18:54:58,402 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753786498368 状态码: 200 耗时: 0.013s
2025-07-29 18:55:40,374 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753786540369
2025-07-29 18:55:40,381 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753786540369 状态码: 200 耗时: 0.007s
2025-07-29 18:55:40,769 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753786540759
2025-07-29 18:55:40,772 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753786540759 状态码: 200 耗时: 0.003s
2025-07-29 18:57:56,518 [INFO] root:154 - 请求开始: DELETE http://localhost:8001/api/v1/interfaces/1/localize
2025-07-29 18:57:56,540 [INFO] root:163 - 请求完成: DELETE http://localhost:8001/api/v1/interfaces/1/localize 状态码: 200 耗时: 0.022s
2025-07-29 18:58:03,801 [INFO] root:154 - 请求开始: PUT http://localhost:8001/api/v1/interfaces/1
2025-07-29 18:58:03,818 [INFO] app.services.interface_service:379 - 接口源更新成功: 真实TVBox配置
2025-07-29 18:58:03,819 [INFO] root:163 - 请求完成: PUT http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.019s
2025-07-29 18:58:04,798 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753786684788
2025-07-29 18:58:04,799 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753786684788
2025-07-29 18:58:04,800 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753786684788 状态码: 307 耗时: 0.002s
2025-07-29 18:58:04,805 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753786684788 状态码: 200 耗时: 0.007s
2025-07-29 18:58:05,130 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753786684788
2025-07-29 18:58:05,138 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753786684788 状态码: 200 耗时: 0.008s
2025-07-29 18:58:12,065 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753786692062
2025-07-29 18:58:12,068 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753786692062 状态码: 200 耗时: 0.003s
2025-07-29 18:58:39,158 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753786719155
2025-07-29 18:58:39,165 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753786719155 状态码: 200 耗时: 0.007s
2025-07-29 18:58:39,394 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753786719386
2025-07-29 18:58:39,398 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753786719386 状态码: 200 耗时: 0.004s
2025-07-29 18:58:56,944 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/localize
2025-07-29 18:58:56,958 [INFO] app.services.new_localization_service:117 - 检查spider字段: https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************
2025-07-29 18:58:56,958 [INFO] app.services.new_localization_service:124 - Spider字段解析: URL=https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg, MD5=5*******************************
2025-07-29 18:58:56,959 [INFO] app.services.new_localization_service:131 - Spider URL文件类型: spider
2025-07-29 18:58:56,960 [INFO] app.services.new_localization_service:141 - 添加spider URL到下载列表: https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg
2025-07-29 18:58:56,962 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg -> data\localized\interface_1_真实TVBox配置\spider\f0720.jpg
2025-07-29 18:58:57,486 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://www.wogg.com/ -> data\localized\interface_1_真实TVBox配置\js\script_c223c84d.js
2025-07-29 18:58:59,336 [ERROR] app.services.new_localization_service:250 - 下载文件失败 https://www.wogg.com/: Cannot connect to host www.wogg.com:443 ssl:True [SSLCertVerificationError: (1, "[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: Hostname mismatch, certificate is not valid for 'www.wogg.com'. (_ssl.c:1006)")]
2025-07-29 18:58:59,337 [WARNING] app.services.new_localization_service:344 - 下载失败: https://www.wogg.com/ - Cannot connect to host www.wogg.com:443 ssl:True [SSLCertVerificationError: (1, "[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: Hostname mismatch, certificate is not valid for 'www.wogg.com'. (_ssl.c:1006)")]
2025-07-29 18:58:59,337 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://www.zxzjhd.com/ -> data\localized\interface_1_真实TVBox配置\js\script_b458aac3.js
2025-07-29 18:59:00,636 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://auete.com/ -> data\localized\interface_1_真实TVBox配置\js\script_20bcec81.js
2025-07-29 18:59:01,931 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://www.xb6v.com/ -> data\localized\interface_1_真实TVBox配置\js\script_de8516f1.js
2025-07-29 18:59:03,060 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://im.feelec.com.cn/res/file.html?id=2c9a91099584ed490195f7bf50ac60b9 -> data\localized\interface_1_真实TVBox配置\js\file.html
2025-07-29 18:59:04,118 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/drpy2.js -> data\localized\interface_1_真实TVBox配置\js\drpy2.js
2025-07-29 18:59:05,678 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/huya2.js -> data\localized\interface_1_真实TVBox配置\js\huya2.js
2025-07-29 18:59:07,173 [ERROR] app.services.new_localization_service:250 - 下载文件失败 https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/huya2.js: Response payload is not completed: <ContentLengthError: 400, message='Not enough data for satisfy content length header.'>
2025-07-29 18:59:07,173 [WARNING] app.services.new_localization_service:344 - 下载失败: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/huya2.js - Response payload is not completed: <ContentLengthError: 400, message='Not enough data for satisfy content length header.'>
2025-07-29 18:59:07,173 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/drpy2.min.js -> data\localized\interface_1_真实TVBox配置\js\drpy2.min.js
2025-07-29 18:59:11,421 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/斗鱼直播.js -> data\localized\interface_1_真实TVBox配置\js\斗鱼直播.js
2025-07-29 18:59:12,839 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/drpy2.min.js -> data\localized\interface_1_真实TVBox配置\js\drpy2.min.js
2025-07-29 18:59:14,088 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/有声小说吧.js -> data\localized\interface_1_真实TVBox配置\js\有声小说吧.js
2025-07-29 18:59:15,330 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://im.feelec.com.cn/res/file.html?id=2c9a91099293dce40194194e5a5f7474 -> data\localized\interface_1_真实TVBox配置\js\file.html
2025-07-29 18:59:17,904 [INFO] root:154 - 请求开始: PUT http://localhost:8001/api/v1/interfaces/1
2025-07-29 18:59:17,915 [INFO] app.services.interface_service:379 - 接口源更新成功: 真实TVBox配置
2025-07-29 18:59:17,916 [INFO] root:163 - 请求完成: PUT http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.013s
2025-07-29 18:59:18,567 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753786758561
2025-07-29 18:59:18,568 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753786758561
2025-07-29 18:59:18,568 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753786758561 状态码: 307 耗时: 0.001s
2025-07-29 18:59:18,574 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753786758561 状态码: 200 耗时: 0.006s
2025-07-29 18:59:18,897 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753786758561
2025-07-29 18:59:18,902 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753786758561 状态码: 200 耗时: 0.005s
2025-07-29 18:59:19,371 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://im.feelec.com.cn/res/file.html?id=2c9a91099293dce40194194f4eb2747e -> data\localized\interface_1_真实TVBox配置\js\file.html
2025-07-29 18:59:19,510 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/drpy2.min.js -> data\localized\interface_1_真实TVBox配置\js\drpy2.min.js
2025-07-29 18:59:20,825 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/%E5%85%94%E5%B0%8F%E8%B4%9D.js -> data\localized\interface_1_真实TVBox配置\js\%E5%85%94%E5%B0%8F%E8%B4%9D.js
2025-07-29 18:59:21,997 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://im.feelec.com.cn/res/file.html?id=2c9a91099293dce4019465168f841292 -> data\localized\interface_1_真实TVBox配置\js\file.html
2025-07-29 18:59:22,606 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://im.feelec.com.cn/res/file.html?id=2c9a91099293dce401946513bb42128a -> data\localized\interface_1_真实TVBox配置\js\file.html
2025-07-29 18:59:23,434 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://im.feelec.com.cn/res/file.html?id=2c9a91099293dce401946513bb2d1289 -> data\localized\interface_1_真实TVBox配置\js\file.html
2025-07-29 18:59:24,081 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://im.feelec.com.cn/res/file.html?id=2c9a91099293dce401946513bd90128b -> data\localized\interface_1_真实TVBox配置\js\file.html
2025-07-29 18:59:25,310 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://scrm-community.oss-cn-shenzhen.aliyuncs.com/miniso-vendor/20250425-868403-********************************.m3u -> data\localized\interface_1_真实TVBox配置\live\20250425-868403-********************************.m3u
2025-07-29 18:59:25,506 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/wwb521/live/main/tv.m3u -> data\localized\interface_1_真实TVBox配置\live\tv.m3u
2025-07-29 18:59:26,797 [INFO] app.services.new_localization_service:217 - 开始下载文件: http://127.0.0.1:9978/proxy?do=饭太硬&type=liveList -> data\localized\interface_1_真实TVBox配置\live\live_d04bc4a0.m3u
2025-07-29 18:59:27,228 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753786767221
2025-07-29 18:59:27,233 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753786767221 状态码: 200 耗时: 0.006s
2025-07-29 18:59:27,320 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753786767287
2025-07-29 18:59:27,331 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753786767287 状态码: 200 耗时: 0.011s
2025-07-29 18:59:28,839 [ERROR] app.services.new_localization_service:250 - 下载文件失败 http://127.0.0.1:9978/proxy?do=饭太硬&type=liveList: Cannot connect to host 127.0.0.1:9978 ssl:default [Connect call failed ('127.0.0.1', 9978)]
2025-07-29 18:59:28,839 [WARNING] app.services.new_localization_service:344 - 下载失败: http://127.0.0.1:9978/proxy?do=饭太硬&type=liveList - Cannot connect to host 127.0.0.1:9978 ssl:default [Connect call failed ('127.0.0.1', 9978)]
2025-07-29 18:59:28,840 [INFO] app.services.new_localization_service:217 - 开始下载文件: http://tv.iill.top/m3u/Live -> data\localized\interface_1_真实TVBox配置\live\live_c9b0de40.m3u
2025-07-29 18:59:31,898 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/localize 状态码: 200 耗时: 34.954s
2025-07-29 19:02:17,452 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753786937448
2025-07-29 19:02:17,458 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753786937448 状态码: 200 耗时: 0.006s
2025-07-29 19:02:17,670 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753786937665
2025-07-29 19:02:17,673 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753786937665 状态码: 200 耗时: 0.004s
2025-07-29 19:02:17,787 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753786937760
2025-07-29 19:02:17,800 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753786937760 状态码: 200 耗时: 0.013s
2025-07-29 19:02:54,406 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753786974402
2025-07-29 19:02:54,414 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753786974402 状态码: 200 耗时: 0.008s
2025-07-29 19:12:18,052 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 19:12:18,582 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 19:12:18,583 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 19:12:18,583 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 19:12:18,584 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 19:12:18,584 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 19:12:18,584 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 19:12:18,584 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 19:12:18,586 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 19:12:18,589 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 19:12:18,590 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 19:12:18,590 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 19:12:18,593 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 19:12:18,601 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.549s
2025-07-29 19:12:18,609 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753787538607
2025-07-29 19:12:18,613 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753787538607 状态码: 200 耗时: 0.004s
2025-07-29 19:12:18,919 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753787538607
2025-07-29 19:12:18,924 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753787538607 状态码: 200 耗时: 0.005s
2025-07-29 19:22:18,052 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 19:22:18,549 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 19:22:18,550 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 19:22:18,550 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 19:22:18,550 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 19:22:18,550 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 19:22:18,551 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 19:22:18,551 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 19:22:18,553 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 19:22:18,556 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 19:22:18,556 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 19:22:18,557 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 19:22:18,558 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 19:22:18,565 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.513s
2025-07-29 19:22:18,575 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753788138571
2025-07-29 19:22:18,578 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753788138571 状态码: 200 耗时: 0.003s
2025-07-29 19:22:18,887 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753788138571
2025-07-29 19:22:18,891 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753788138571 状态码: 200 耗时: 0.005s
2025-07-29 19:22:18,931 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 19:22:19,172 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 19:22:19,172 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 19:22:19,173 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 19:22:19,173 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 19:22:19,173 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 19:22:19,173 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 19:22:19,174 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 19:22:19,176 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 19:22:19,178 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 19:22:19,179 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 19:22:19,179 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 19:22:19,180 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 19:22:19,187 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.256s
2025-07-29 19:22:19,510 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753788139191
2025-07-29 19:22:19,511 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753788139191
2025-07-29 19:22:19,518 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753788139191 状态码: 200 耗时: 0.008s
2025-07-29 19:22:19,519 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753788139191 状态码: 200 耗时: 0.008s
2025-07-29 19:32:18,054 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 19:32:18,542 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 19:32:18,543 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 19:32:18,543 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 19:32:18,543 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 19:32:18,544 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 19:32:18,544 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 19:32:18,544 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 19:32:18,547 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 19:32:18,550 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 19:32:18,550 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 19:32:18,551 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 19:32:18,552 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 19:32:18,559 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.505s
2025-07-29 19:32:18,569 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753788738565
2025-07-29 19:32:18,572 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753788738565 状态码: 200 耗时: 0.004s
2025-07-29 19:32:18,877 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753788738565
2025-07-29 19:32:18,884 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753788738565 状态码: 200 耗时: 0.007s
2025-07-29 19:32:18,888 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 19:32:19,107 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 19:32:19,109 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 19:32:19,110 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 19:32:19,110 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 19:32:19,110 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 19:32:19,110 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 19:32:19,112 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 19:32:19,114 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 19:32:19,116 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 19:32:19,118 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 19:32:19,118 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 19:32:19,120 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 19:32:19,126 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 19:32:19,126 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.239s
2025-07-29 19:32:19,354 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 19:32:19,354 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 19:32:19,355 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 19:32:19,355 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 19:32:19,355 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 19:32:19,356 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 19:32:19,356 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 19:32:19,358 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 19:32:19,360 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 19:32:19,360 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 19:32:19,361 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 19:32:19,362 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 19:32:19,369 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.244s
2025-07-29 19:32:19,453 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753788739132
2025-07-29 19:32:19,453 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753788739132
2025-07-29 19:32:19,459 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753788739132 状态码: 200 耗时: 0.006s
2025-07-29 19:32:19,460 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753788739132 状态码: 200 耗时: 0.007s
2025-07-29 19:32:19,683 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753788739373
2025-07-29 19:32:19,684 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753788739373
2025-07-29 19:32:19,687 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753788739373 状态码: 200 耗时: 0.004s
2025-07-29 19:32:19,692 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753788739373 状态码: 200 耗时: 0.007s
2025-07-29 19:32:19,838 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 19:32:20,365 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 19:32:20,366 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 19:32:20,366 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 19:32:20,366 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 19:32:20,367 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 19:32:20,367 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 19:32:20,367 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 19:32:20,370 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 19:32:20,372 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 19:32:20,372 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 19:32:20,373 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 19:32:20,374 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 19:32:20,380 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.542s
2025-07-29 19:32:20,692 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753788740386
2025-07-29 19:32:20,693 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753788740386
2025-07-29 19:32:20,697 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753788740386 状态码: 200 耗时: 0.005s
2025-07-29 19:32:20,701 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753788740386 状态码: 200 耗时: 0.008s
2025-07-29 19:42:18,050 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 19:42:18,519 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 19:42:18,520 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 19:42:18,520 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 19:42:18,520 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 19:42:18,520 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 19:42:18,521 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 19:42:18,521 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 19:42:18,523 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 19:42:18,525 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 19:42:18,526 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 19:42:18,526 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 19:42:18,527 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 19:42:18,533 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.482s
2025-07-29 19:42:18,541 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753789338538
2025-07-29 19:42:18,543 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753789338538 状态码: 200 耗时: 0.002s
2025-07-29 19:42:18,858 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753789338538
2025-07-29 19:42:18,866 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753789338538 状态码: 200 耗时: 0.007s
2025-07-29 19:42:18,888 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 19:42:19,133 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 19:42:19,134 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 19:42:19,134 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 19:42:19,134 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 19:42:19,134 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 19:42:19,136 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 19:42:19,136 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 19:42:19,138 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 19:42:19,140 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 19:42:19,140 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 19:42:19,141 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 19:42:19,142 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 19:42:19,149 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 19:42:19,150 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 19:42:19,151 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.263s
2025-07-29 19:42:19,374 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 19:42:19,375 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 19:42:19,375 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 19:42:19,375 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 19:42:19,376 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 19:42:19,376 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 19:42:19,376 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 19:42:19,378 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 19:42:19,380 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 19:42:19,381 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 19:42:19,381 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 19:42:19,383 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 19:42:19,767 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 19:42:19,768 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 19:42:19,768 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 19:42:19,768 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 19:42:19,769 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 19:42:19,769 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 19:42:19,769 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 19:42:19,771 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 19:42:19,774 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 19:42:19,774 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 19:42:19,774 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 19:42:19,776 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 19:42:19,784 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753789339156
2025-07-29 19:42:19,784 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 19:42:19,785 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753789339156
2025-07-29 19:42:19,785 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.634s
2025-07-29 19:42:19,786 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.637s
2025-07-29 19:42:20,015 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 19:42:20,016 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 19:42:20,016 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 19:42:20,017 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 19:42:20,017 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 19:42:20,017 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 19:42:20,018 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 19:42:20,019 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 19:42:20,022 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 19:42:20,022 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 19:42:20,022 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 19:42:20,025 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 19:42:20,030 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 19:42:20,034 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.250s
2025-07-29 19:42:20,035 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753789339156 状态码: 200 耗时: 0.252s
2025-07-29 19:42:20,265 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 19:42:20,266 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 19:42:20,266 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 19:42:20,266 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 19:42:20,267 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 19:42:20,267 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 19:42:20,267 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 19:42:20,269 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 19:42:20,271 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 19:42:20,271 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 19:42:20,271 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 19:42:20,273 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 19:42:20,281 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753789339156 状态码: 200 耗时: 0.496s
2025-07-29 19:42:20,282 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753789339790
2025-07-29 19:42:20,283 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 19:42:20,283 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.252s
2025-07-29 19:42:20,504 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 19:42:20,504 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 19:42:20,505 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 19:42:20,505 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 19:42:20,505 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 19:42:20,506 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 19:42:20,506 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 19:42:20,508 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 19:42:20,510 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 19:42:20,511 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 19:42:20,511 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 19:42:20,513 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 19:42:20,522 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753789339794
2025-07-29 19:42:20,522 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753789339790
2025-07-29 19:42:20,522 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.240s
2025-07-29 19:42:20,525 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753789339790 状态码: 200 耗时: 0.243s
2025-07-29 19:42:20,531 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753789339794 状态码: 200 耗时: 0.008s
2025-07-29 19:42:20,534 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753789339790 状态码: 200 耗时: 0.011s
2025-07-29 19:42:20,602 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753789339794
2025-07-29 19:42:20,603 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753789340039
2025-07-29 19:42:20,609 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753789340039 状态码: 200 耗时: 0.006s
2025-07-29 19:42:20,610 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753789339794 状态码: 200 耗时: 0.008s
2025-07-29 19:42:20,839 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753789340039
2025-07-29 19:42:20,840 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753789340288
2025-07-29 19:42:20,841 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753789340288
2025-07-29 19:42:20,843 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753789340527
2025-07-29 19:42:20,854 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753789340288 状态码: 200 耗时: 0.013s
2025-07-29 19:42:20,855 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753789340527 状态码: 200 耗时: 0.012s
2025-07-29 19:42:20,856 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753789340039 状态码: 200 耗时: 0.017s
2025-07-29 19:42:20,856 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753789340288 状态码: 200 耗时: 0.015s
2025-07-29 19:42:20,931 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753789340527
2025-07-29 19:42:20,938 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753789340527 状态码: 200 耗时: 0.007s
2025-07-29 19:42:21,024 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 19:42:21,553 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 19:42:21,554 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 19:42:21,554 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 19:42:21,556 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 19:42:21,556 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 19:42:21,556 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 19:42:21,556 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 19:42:21,558 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 19:42:21,560 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 19:42:21,561 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 19:42:21,561 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 19:42:21,564 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 19:42:21,568 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.545s
2025-07-29 19:42:21,887 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753789341574
2025-07-29 19:42:21,889 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753789341574
2025-07-29 19:42:21,891 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753789341574 状态码: 200 耗时: 0.004s
2025-07-29 19:42:21,897 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753789341574 状态码: 200 耗时: 0.008s
2025-07-29 19:49:20,704 [INFO] root:154 - 请求开始: GET http://localhost:8001/localized/interface_1_真实TVBox配置/spider/f0720.jar
2025-07-29 19:49:20,813 [INFO] root:163 - 请求完成: GET http://localhost:8001/localized/interface_1_真实TVBox配置/spider/f0720.jar 状态码: 200 耗时: 0.109s
2025-07-29 19:52:09,279 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-29 19:52:09,286 [INFO] root:48 - 数据库表创建完成
2025-07-29 19:52:09,332 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-29 19:52:09,333 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-29 19:52:38,834 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/auth/login
2025-07-29 19:52:39,171 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.337s
2025-07-29 19:52:39,437 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753789959424
2025-07-29 19:52:39,449 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753789959424 状态码: 200 耗时: 0.012s
2025-07-29 19:52:48,181 [INFO] root:154 - 请求开始: DELETE http://localhost:8001/api/v1/interfaces/1/localize
2025-07-29 19:52:48,220 [INFO] root:163 - 请求完成: DELETE http://localhost:8001/api/v1/interfaces/1/localize 状态码: 200 耗时: 0.039s
2025-07-29 19:52:56,074 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/localize
2025-07-29 19:52:56,090 [INFO] app.services.new_localization_service:117 - 检查spider字段: https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************
2025-07-29 19:52:56,091 [INFO] app.services.new_localization_service:124 - Spider字段解析: URL=https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg, MD5=5*******************************
2025-07-29 19:52:56,091 [INFO] app.services.new_localization_service:131 - Spider URL文件类型: spider
2025-07-29 19:52:56,091 [INFO] app.services.new_localization_service:141 - 添加spider URL到下载列表: https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg
2025-07-29 19:52:56,093 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg -> data\localized\interface_1_真实TVBox配置\spider\f0720.jpg
2025-07-29 19:52:57,286 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://www.wogg.com/ -> data\localized\interface_1_真实TVBox配置\js\script_c223c84d.js
2025-07-29 19:52:59,264 [ERROR] app.services.new_localization_service:250 - 下载文件失败 https://www.wogg.com/: Cannot connect to host www.wogg.com:443 ssl:True [SSLCertVerificationError: (1, "[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: Hostname mismatch, certificate is not valid for 'www.wogg.com'. (_ssl.c:1006)")]
2025-07-29 19:52:59,264 [WARNING] app.services.new_localization_service:346 - 下载失败: https://www.wogg.com/ - Cannot connect to host www.wogg.com:443 ssl:True [SSLCertVerificationError: (1, "[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: Hostname mismatch, certificate is not valid for 'www.wogg.com'. (_ssl.c:1006)")]
2025-07-29 19:52:59,266 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://www.zxzjhd.com/ -> data\localized\interface_1_真实TVBox配置\js\script_b458aac3.js
2025-07-29 19:52:59,970 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://auete.com/ -> data\localized\interface_1_真实TVBox配置\js\script_20bcec81.js
2025-07-29 19:53:06,366 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://www.xb6v.com/ -> data\localized\interface_1_真实TVBox配置\js\script_de8516f1.js
2025-07-29 19:53:09,817 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://im.feelec.com.cn/res/file.html?id=2c9a91099584ed490195f7bf50ac60b9 -> data\localized\interface_1_真实TVBox配置\js\file.html
2025-07-29 19:53:10,214 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/drpy2.js -> data\localized\interface_1_真实TVBox配置\js\drpy2.js
2025-07-29 19:53:14,107 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/huya2.js -> data\localized\interface_1_真实TVBox配置\js\huya2.js
2025-07-29 19:53:15,593 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/drpy2.min.js -> data\localized\interface_1_真实TVBox配置\js\drpy2.min.js
2025-07-29 19:53:16,926 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/斗鱼直播.js -> data\localized\interface_1_真实TVBox配置\js\斗鱼直播.js
2025-07-29 19:53:23,330 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/drpy2.min.js -> data\localized\interface_1_真实TVBox配置\js\drpy2.min.js
2025-07-29 19:53:24,618 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/有声小说吧.js -> data\localized\interface_1_真实TVBox配置\js\有声小说吧.js
2025-07-29 19:53:25,796 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://im.feelec.com.cn/res/file.html?id=2c9a91099293dce40194194e5a5f7474 -> data\localized\interface_1_真实TVBox配置\js\file.html
2025-07-29 19:53:25,866 [INFO] root:154 - 请求开始: PUT http://localhost:8001/api/v1/interfaces/1
2025-07-29 19:53:25,884 [INFO] app.services.interface_service:379 - 接口源更新成功: 真实TVBox配置
2025-07-29 19:53:25,886 [INFO] root:163 - 请求完成: PUT http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.020s
2025-07-29 19:53:26,828 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://im.feelec.com.cn/res/file.html?id=2c9a91099293dce40194194f4eb2747e -> data\localized\interface_1_真实TVBox配置\js\file.html
2025-07-29 19:53:26,830 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753790006821
2025-07-29 19:53:26,830 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753790006821
2025-07-29 19:53:26,831 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753790006821 状态码: 307 耗时: 0.001s
2025-07-29 19:53:26,836 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753790006821 状态码: 200 耗时: 0.005s
2025-07-29 19:53:27,163 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753790006821
2025-07-29 19:53:27,172 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753790006821 状态码: 200 耗时: 0.009s
2025-07-29 19:53:27,836 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/drpy2.min.js -> data\localized\interface_1_真实TVBox配置\js\drpy2.min.js
2025-07-29 19:53:29,158 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/%E5%85%94%E5%B0%8F%E8%B4%9D.js -> data\localized\interface_1_真实TVBox配置\js\%E5%85%94%E5%B0%8F%E8%B4%9D.js
2025-07-29 19:53:30,188 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://im.feelec.com.cn/res/file.html?id=2c9a91099293dce4019465168f841292 -> data\localized\interface_1_真实TVBox配置\js\file.html
2025-07-29 19:53:31,178 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://im.feelec.com.cn/res/file.html?id=2c9a91099293dce401946513bb42128a -> data\localized\interface_1_真实TVBox配置\js\file.html
2025-07-29 19:53:31,754 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://im.feelec.com.cn/res/file.html?id=2c9a91099293dce401946513bb2d1289 -> data\localized\interface_1_真实TVBox配置\js\file.html
2025-07-29 19:53:33,472 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://im.feelec.com.cn/res/file.html?id=2c9a91099293dce401946513bd90128b -> data\localized\interface_1_真实TVBox配置\js\file.html
2025-07-29 19:53:34,863 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753790014859
2025-07-29 19:53:34,867 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753790014859 状态码: 200 耗时: 0.003s
2025-07-29 19:53:34,934 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753790014907
2025-07-29 19:53:34,942 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753790014907 状态码: 200 耗时: 0.008s
2025-07-29 19:53:40,543 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://scrm-community.oss-cn-shenzhen.aliyuncs.com/miniso-vendor/20250425-868403-********************************.m3u -> data\localized\interface_1_真实TVBox配置\live\20250425-868403-********************************.m3u
2025-07-29 19:53:40,743 [INFO] app.services.new_localization_service:217 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/wwb521/live/main/tv.m3u -> data\localized\interface_1_真实TVBox配置\live\tv.m3u
2025-07-29 19:53:42,144 [INFO] app.services.new_localization_service:217 - 开始下载文件: http://127.0.0.1:9978/proxy?do=饭太硬&type=liveList -> data\localized\interface_1_真实TVBox配置\live\live_d04bc4a0.m3u
2025-07-29 19:53:44,186 [ERROR] app.services.new_localization_service:250 - 下载文件失败 http://127.0.0.1:9978/proxy?do=饭太硬&type=liveList: Cannot connect to host 127.0.0.1:9978 ssl:default [Connect call failed ('127.0.0.1', 9978)]
2025-07-29 19:53:44,186 [WARNING] app.services.new_localization_service:346 - 下载失败: http://127.0.0.1:9978/proxy?do=饭太硬&type=liveList - Cannot connect to host 127.0.0.1:9978 ssl:default [Connect call failed ('127.0.0.1', 9978)]
2025-07-29 19:53:44,187 [INFO] app.services.new_localization_service:217 - 开始下载文件: http://tv.iill.top/m3u/Live -> data\localized\interface_1_真实TVBox配置\live\live_c9b0de40.m3u
2025-07-29 19:54:03,255 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/localize 状态码: 200 耗时: 67.181s
2025-07-29 19:56:31,014 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status
2025-07-29 19:56:31,016 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status 状态码: 401 耗时: 0.001s
2025-07-29 19:56:39,249 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753790199242
2025-07-29 19:56:39,257 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753790199242 状态码: 200 耗时: 0.008s
2025-07-29 19:56:39,673 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753790199666
2025-07-29 19:56:39,675 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753790199666 状态码: 200 耗时: 0.002s
2025-07-29 19:56:39,749 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753790199725
2025-07-29 19:56:39,760 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753790199725 状态码: 200 耗时: 0.011s
2025-07-29 19:57:09,060 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753790229056
2025-07-29 19:57:09,068 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753790229056 状态码: 200 耗时: 0.007s
2025-07-29 19:58:40,124 [INFO] root:154 - 请求开始: GET http://localhost:8001/localized/interface_1_真实TVBox配置/js/兔小贝.js
2025-07-29 19:58:40,127 [INFO] root:163 - 请求完成: GET http://localhost:8001/localized/interface_1_真实TVBox配置/js/兔小贝.js 状态码: 404 耗时: 0.003s
2025-07-29 20:06:40,012 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 20:06:40,552 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 20:06:40,555 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 20:06:40,555 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 20:06:40,556 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 20:06:40,556 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 20:06:40,556 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 20:06:40,557 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 20:06:40,561 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 20:06:40,568 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 20:06:40,569 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 20:06:40,569 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 20:06:40,577 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 20:06:40,590 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.578s
2025-07-29 20:06:40,614 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753790800609
2025-07-29 20:06:40,618 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753790800609 状态码: 200 耗时: 0.003s
2025-07-29 20:13:09,497 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753791189490
2025-07-29 20:13:09,507 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753791189490 状态码: 200 耗时: 0.011s
2025-07-29 20:13:09,671 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-29 20:13:15,583 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-29 20:13:15,591 [INFO] root:48 - 数据库表创建完成
2025-07-29 20:13:15,700 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-29 20:13:15,700 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-29 20:13:15,710 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753791189856
2025-07-29 20:13:15,733 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753791189856 状态码: 200 耗时: 0.023s
2025-07-29 20:13:25,751 [INFO] root:154 - 请求开始: DELETE http://localhost:8001/api/v1/interfaces/1/localize
2025-07-29 20:13:25,801 [INFO] root:163 - 请求完成: DELETE http://localhost:8001/api/v1/interfaces/1/localize 状态码: 200 耗时: 0.050s
2025-07-29 20:13:33,967 [INFO] root:154 - 请求开始: PUT http://localhost:8001/api/v1/interfaces/1
2025-07-29 20:13:33,983 [INFO] app.services.interface_service:379 - 接口源更新成功: 真实TVBox配置
2025-07-29 20:13:33,986 [INFO] root:163 - 请求完成: PUT http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.019s
2025-07-29 20:13:34,871 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753791214862
2025-07-29 20:13:34,872 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753791214862
2025-07-29 20:13:34,873 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753791214862 状态码: 307 耗时: 0.002s
2025-07-29 20:13:34,879 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753791214862 状态码: 200 耗时: 0.006s
2025-07-29 20:13:35,207 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753791214862
2025-07-29 20:13:35,215 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753791214862 状态码: 200 耗时: 0.008s
2025-07-29 20:13:43,476 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753791223471
2025-07-29 20:13:43,482 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753791223471 状态码: 200 耗时: 0.007s
2025-07-29 20:14:01,326 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753791241010
2025-07-29 20:14:01,331 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753791241010 状态码: 200 耗时: 0.004s
2025-07-29 20:14:07,743 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/localize
2025-07-29 20:14:07,765 [INFO] app.services.new_localization_service:117 - 检查spider字段: https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************
2025-07-29 20:14:07,766 [INFO] app.services.new_localization_service:124 - Spider字段解析: URL=https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg, MD5=5*******************************
2025-07-29 20:14:07,766 [INFO] app.services.new_localization_service:131 - Spider URL文件类型: spider
2025-07-29 20:14:07,767 [INFO] app.services.new_localization_service:141 - 添加spider URL到下载列表: https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg
2025-07-29 20:14:07,768 [INFO] app.services.new_localization_service:263 - 开始下载文件: https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg -> data\localized\interface_1_真实TVBox配置\spider\f0720.jpg
2025-07-29 20:14:08,359 [INFO] app.services.new_localization_service:263 - 开始下载文件: https://www.wogg.com/ -> data\localized\interface_1_真实TVBox配置\js\script_c223c84d.js
2025-07-29 20:14:08,359 [INFO] app.services.new_localization_service:220 - URL模式检测为网站首页，跳过下载: https://www.wogg.com/
2025-07-29 20:14:08,360 [INFO] app.services.new_localization_service:402 - 跳过网页文件: https://www.wogg.com/ - Skipped: Detected as HTML webpage
2025-07-29 20:14:08,360 [INFO] app.services.new_localization_service:263 - 开始下载文件: https://www.zxzjhd.com/ -> data\localized\interface_1_真实TVBox配置\js\script_b458aac3.js
2025-07-29 20:14:08,360 [INFO] app.services.new_localization_service:220 - URL模式检测为网站首页，跳过下载: https://www.zxzjhd.com/
2025-07-29 20:14:08,361 [INFO] app.services.new_localization_service:402 - 跳过网页文件: https://www.zxzjhd.com/ - Skipped: Detected as HTML webpage
2025-07-29 20:14:08,361 [INFO] app.services.new_localization_service:263 - 开始下载文件: https://auete.com/ -> data\localized\interface_1_真实TVBox配置\js\script_20bcec81.js
2025-07-29 20:14:08,361 [INFO] app.services.new_localization_service:220 - URL模式检测为网站首页，跳过下载: https://auete.com/
2025-07-29 20:14:08,362 [INFO] app.services.new_localization_service:402 - 跳过网页文件: https://auete.com/ - Skipped: Detected as HTML webpage
2025-07-29 20:14:08,362 [INFO] app.services.new_localization_service:263 - 开始下载文件: https://www.xb6v.com/ -> data\localized\interface_1_真实TVBox配置\js\script_de8516f1.js
2025-07-29 20:14:08,362 [INFO] app.services.new_localization_service:220 - URL模式检测为网站首页，跳过下载: https://www.xb6v.com/
2025-07-29 20:14:08,363 [INFO] app.services.new_localization_service:402 - 跳过网页文件: https://www.xb6v.com/ - Skipped: Detected as HTML webpage
2025-07-29 20:14:08,363 [INFO] app.services.new_localization_service:263 - 开始下载文件: https://im.feelec.com.cn/res/file.html?id=2c9a91099584ed490195f7bf50ac60b9 -> data\localized\interface_1_真实TVBox配置\js\file.html
2025-07-29 20:14:10,356 [INFO] app.services.new_localization_service:263 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/drpy2.js -> data\localized\interface_1_真实TVBox配置\js\drpy2.js
2025-07-29 20:14:13,326 [INFO] app.services.new_localization_service:263 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/huya2.js -> data\localized\interface_1_真实TVBox配置\js\huya2.js
2025-07-29 20:14:16,026 [INFO] app.services.new_localization_service:263 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/drpy2.min.js -> data\localized\interface_1_真实TVBox配置\js\drpy2.min.js
2025-07-29 20:14:18,615 [INFO] app.services.new_localization_service:263 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/斗鱼直播.js -> data\localized\interface_1_真实TVBox配置\js\斗鱼直播.js
2025-07-29 20:14:21,298 [INFO] app.services.new_localization_service:263 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/drpy2.min.js -> data\localized\interface_1_真实TVBox配置\js\drpy2.min.js
2025-07-29 20:14:23,957 [INFO] app.services.new_localization_service:263 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/有声小说吧.js -> data\localized\interface_1_真实TVBox配置\js\有声小说吧.js
2025-07-29 20:14:26,361 [INFO] app.services.new_localization_service:263 - 开始下载文件: https://im.feelec.com.cn/res/file.html?id=2c9a91099293dce40194194e5a5f7474 -> data\localized\interface_1_真实TVBox配置\js\file.html
2025-07-29 20:14:26,482 [INFO] root:154 - 请求开始: PUT http://localhost:8001/api/v1/interfaces/1
2025-07-29 20:14:26,491 [INFO] app.services.interface_service:379 - 接口源更新成功: 真实TVBox配置
2025-07-29 20:14:26,493 [INFO] root:163 - 请求完成: PUT http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.011s
2025-07-29 20:14:27,400 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753791267087
2025-07-29 20:14:27,401 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753791267087 状态码: 307 耗时: 0.001s
2025-07-29 20:14:27,402 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753791267087
2025-07-29 20:14:27,405 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753791267087
2025-07-29 20:14:27,409 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753791267087 状态码: 200 耗时: 0.007s
2025-07-29 20:14:27,411 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753791267087 状态码: 200 耗时: 0.006s
2025-07-29 20:14:34,256 [INFO] app.services.new_localization_service:263 - 开始下载文件: https://im.feelec.com.cn/res/file.html?id=2c9a91099293dce40194194f4eb2747e -> data\localized\interface_1_真实TVBox配置\js\file.html
2025-07-29 20:14:34,967 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753791274652
2025-07-29 20:14:34,970 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753791274652 状态码: 200 耗时: 0.004s
2025-07-29 20:14:35,282 [INFO] app.services.new_localization_service:263 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/drpy2.min.js -> data\localized\interface_1_真实TVBox配置\js\drpy2.min.js
2025-07-29 20:14:35,337 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753791275005
2025-07-29 20:14:35,343 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753791275005 状态码: 200 耗时: 0.006s
2025-07-29 20:14:38,067 [INFO] app.services.new_localization_service:263 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/%E5%85%94%E5%B0%8F%E8%B4%9D.js -> data\localized\interface_1_真实TVBox配置\js\%E5%85%94%E5%B0%8F%E8%B4%9D.js
2025-07-29 20:14:40,525 [INFO] app.services.new_localization_service:263 - 开始下载文件: https://im.feelec.com.cn/res/file.html?id=2c9a91099293dce4019465168f841292 -> data\localized\interface_1_真实TVBox配置\js\file.html
2025-07-29 20:14:41,523 [INFO] app.services.new_localization_service:263 - 开始下载文件: https://im.feelec.com.cn/res/file.html?id=2c9a91099293dce401946513bb42128a -> data\localized\interface_1_真实TVBox配置\js\file.html
2025-07-29 20:14:43,351 [INFO] app.services.new_localization_service:263 - 开始下载文件: https://im.feelec.com.cn/res/file.html?id=2c9a91099293dce401946513bb2d1289 -> data\localized\interface_1_真实TVBox配置\js\file.html
2025-07-29 20:14:47,210 [INFO] app.services.new_localization_service:263 - 开始下载文件: https://im.feelec.com.cn/res/file.html?id=2c9a91099293dce401946513bd90128b -> data\localized\interface_1_真实TVBox配置\js\file.html
2025-07-29 20:14:51,339 [INFO] app.services.new_localization_service:263 - 开始下载文件: https://scrm-community.oss-cn-shenzhen.aliyuncs.com/miniso-vendor/20250425-868403-********************************.m3u -> data\localized\interface_1_真实TVBox配置\live\20250425-868403-********************************.m3u
2025-07-29 20:14:51,715 [INFO] app.services.new_localization_service:263 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/wwb521/live/main/tv.m3u -> data\localized\interface_1_真实TVBox配置\live\tv.m3u
2025-07-29 20:14:54,457 [INFO] app.services.new_localization_service:263 - 开始下载文件: http://127.0.0.1:9978/proxy?do=饭太硬&type=liveList -> data\localized\interface_1_真实TVBox配置\live\live_d04bc4a0.m3u
2025-07-29 20:14:58,540 [WARNING] app.services.new_localization_service:257 - 网页检测失败，继续下载: http://127.0.0.1:9978/proxy?do=饭太硬&type=liveList - Cannot connect to host 127.0.0.1:9978 ssl:default [Connect call failed ('127.0.0.1', 9978)]
2025-07-29 20:15:00,583 [ERROR] app.services.new_localization_service:305 - 下载文件失败 http://127.0.0.1:9978/proxy?do=饭太硬&type=liveList: Cannot connect to host 127.0.0.1:9978 ssl:default [Connect call failed ('127.0.0.1', 9978)]
2025-07-29 20:15:00,583 [WARNING] app.services.new_localization_service:413 - 下载失败: http://127.0.0.1:9978/proxy?do=饭太硬&type=liveList - Cannot connect to host 127.0.0.1:9978 ssl:default [Connect call failed ('127.0.0.1', 9978)]
2025-07-29 20:15:00,584 [INFO] app.services.new_localization_service:263 - 开始下载文件: http://tv.iill.top/m3u/Live -> data\localized\interface_1_真实TVBox配置\live\live_c9b0de40.m3u
2025-07-29 20:15:01,779 [INFO] app.services.new_localization_service:229 - Content-Type检测为HTML，跳过下载: http://tv.iill.top/m3u/Live (Content-Type: text/html)
2025-07-29 20:15:01,779 [INFO] app.services.new_localization_service:402 - 跳过网页文件: http://tv.iill.top/m3u/Live - Skipped: Detected as HTML webpage
2025-07-29 20:15:01,786 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/localize 状态码: 200 耗时: 54.043s
2025-07-29 20:18:13,791 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753791493479
2025-07-29 20:18:13,800 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localization-status?_t=1753791493479 状态码: 200 耗时: 0.010s
2025-07-29 20:18:44,960 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753791524955
2025-07-29 20:18:44,969 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753791524955 状态码: 200 耗时: 0.009s
2025-07-29 20:24:35,304 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 20:24:35,802 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 20:24:35,802 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 20:24:35,803 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 20:24:35,803 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 20:24:35,803 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 20:24:35,804 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 20:24:35,804 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 20:24:35,806 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 20:24:35,808 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 20:24:35,808 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 20:24:35,810 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 20:24:35,812 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 20:24:35,819 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.515s
2025-07-29 20:24:35,827 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753791875823
2025-07-29 20:24:35,830 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753791875823 状态码: 200 耗时: 0.003s
2025-07-29 20:24:36,141 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753791875823
2025-07-29 20:24:36,147 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753791875823 状态码: 200 耗时: 0.006s
2025-07-29 20:32:15,986 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753792335680
2025-07-29 20:32:15,992 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753792335680 状态码: 200 耗时: 0.006s
2025-07-29 20:32:19,753 [INFO] root:154 - 请求开始: GET http://localhost:8001/localized/interface_1_真实TVBox配置/js/兔小贝.js
2025-07-29 20:32:19,757 [INFO] root:163 - 请求完成: GET http://localhost:8001/localized/interface_1_真实TVBox配置/js/兔小贝.js 状态码: 404 耗时: 0.003s
2025-07-29 20:32:43,733 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753792363414
2025-07-29 20:32:43,738 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753792363414 状态码: 200 耗时: 0.005s
2025-07-29 20:34:35,287 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 20:34:35,711 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 20:34:35,711 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 20:34:35,711 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 20:34:35,712 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 20:34:35,712 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 20:34:35,713 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 20:34:35,713 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 20:34:35,714 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 20:34:35,718 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 20:34:35,718 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 20:34:35,718 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 20:34:35,721 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 20:34:35,725 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.438s
2025-07-29 20:34:35,831 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-29 20:34:40,649 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-29 20:34:40,657 [INFO] root:48 - 数据库表创建完成
2025-07-29 20:34:40,727 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-29 20:34:40,728 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-29 20:34:40,739 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753792475732
2025-07-29 20:34:40,740 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-29 20:34:40,740 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753792475732
2025-07-29 20:34:41,259 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-29 20:34:41,260 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-29 20:34:41,260 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-29 20:34:41,261 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-29 20:34:41,261 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-29 20:34:41,261 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-29 20:34:41,261 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-29 20:34:41,265 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-29 20:34:41,269 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-29 20:34:41,269 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-29 20:34:41,269 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-29 20:34:41,273 [INFO] app.services.interface_service:119 - 接口 真实TVBox配置 无变化，跳过更新: 配置内容无变化
2025-07-29 20:34:41,286 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753792475732 状态码: 200 耗时: 0.547s
2025-07-29 20:34:41,286 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.547s
2025-07-29 20:34:41,287 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753792475732 状态码: 200 耗时: 0.549s
2025-07-29 20:34:41,603 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753792481293
2025-07-29 20:34:41,604 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753792481293
2025-07-29 20:34:41,610 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753792481293 状态码: 200 耗时: 0.008s
2025-07-29 20:34:41,612 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/localized?_t=1753792481293 状态码: 200 耗时: 0.008s
2025-07-29 20:34:55,610 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-29 20:34:55,616 [INFO] root:48 - 数据库表创建完成
2025-07-29 20:34:55,663 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-29 20:34:55,664 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-29 20:35:21,380 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/
2025-07-29 20:35:21,388 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/ 状态码: 200 耗时: 0.008s
2025-07-29 20:43:53,482 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753793033477
2025-07-29 20:43:53,493 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753793033477 状态码: 200 耗时: 0.011s
2025-07-29 20:43:53,775 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753793033760
2025-07-29 20:43:53,777 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753793033760 状态码: 200 耗时: 0.002s
2025-07-29 20:44:19,476 [INFO] root:154 - 请求开始: DELETE http://localhost:8001/api/v1/interfaces/1/localize
2025-07-29 20:44:19,499 [INFO] root:163 - 请求完成: DELETE http://localhost:8001/api/v1/interfaces/1/localize 状态码: 200 耗时: 0.023s
2025-07-29 20:44:29,250 [INFO] root:154 - 请求开始: PUT http://localhost:8001/api/v1/interfaces/1
2025-07-29 20:44:29,266 [INFO] app.services.interface_service:379 - 接口源更新成功: 真实TVBox配置
2025-07-29 20:44:29,266 [INFO] root:163 - 请求完成: PUT http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.017s
2025-07-29 20:44:30,160 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753793070153
2025-07-29 20:44:30,161 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753793070153
2025-07-29 20:44:30,161 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753793070153 状态码: 307 耗时: 0.001s
2025-07-29 20:44:30,167 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753793070153 状态码: 200 耗时: 0.006s
2025-07-29 20:44:30,492 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753793070153
2025-07-29 20:44:30,495 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753793070153 状态码: 200 耗时: 0.003s
2025-07-29 20:44:39,320 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753793079316
2025-07-29 20:44:39,324 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753793079316 状态码: 200 耗时: 0.004s
2025-07-29 20:48:48,682 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-29 20:48:48,688 [INFO] root:48 - 数据库表创建完成
2025-07-29 20:48:48,745 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-29 20:48:48,745 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-29 20:49:54,030 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753793393700
2025-07-29 20:49:54,039 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753793393700 状态码: 200 耗时: 0.009s
2025-07-29 20:50:01,192 [INFO] root:154 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/localize
2025-07-29 20:50:01,218 [INFO] app.services.new_localization_service:117 - 检查spider字段: https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;5*******************************
2025-07-29 20:50:01,218 [INFO] app.services.new_localization_service:124 - Spider字段解析: URL=https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg, MD5=5*******************************
2025-07-29 20:50:01,220 [INFO] app.services.new_localization_service:131 - Spider URL文件类型: spider
2025-07-29 20:50:01,220 [INFO] app.services.new_localization_service:141 - 添加spider URL到下载列表: https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg
2025-07-29 20:50:01,220 [INFO] app.services.new_localization_service:269 - 开始下载文件: https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg -> data\localized\interface_1_真实TVBox配置\spider\f0720.jpg
2025-07-29 20:50:01,825 [INFO] app.services.new_localization_service:269 - 开始下载文件: https://www.wogg.com/ -> data\localized\interface_1_真实TVBox配置\js\script_c223c84d.js
2025-07-29 20:50:01,827 [INFO] app.services.new_localization_service:226 - URL模式检测为网站首页，跳过下载: https://www.wogg.com/
2025-07-29 20:50:01,827 [INFO] app.services.new_localization_service:408 - 跳过网页文件: https://www.wogg.com/ - Skipped: Detected as HTML webpage
2025-07-29 20:50:01,827 [INFO] app.services.new_localization_service:269 - 开始下载文件: https://www.zxzjhd.com/ -> data\localized\interface_1_真实TVBox配置\js\script_b458aac3.js
2025-07-29 20:50:01,827 [INFO] app.services.new_localization_service:226 - URL模式检测为网站首页，跳过下载: https://www.zxzjhd.com/
2025-07-29 20:50:01,827 [INFO] app.services.new_localization_service:408 - 跳过网页文件: https://www.zxzjhd.com/ - Skipped: Detected as HTML webpage
2025-07-29 20:50:01,828 [INFO] app.services.new_localization_service:269 - 开始下载文件: https://auete.com/ -> data\localized\interface_1_真实TVBox配置\js\script_20bcec81.js
2025-07-29 20:50:01,828 [INFO] app.services.new_localization_service:226 - URL模式检测为网站首页，跳过下载: https://auete.com/
2025-07-29 20:50:01,828 [INFO] app.services.new_localization_service:408 - 跳过网页文件: https://auete.com/ - Skipped: Detected as HTML webpage
2025-07-29 20:50:01,828 [INFO] app.services.new_localization_service:269 - 开始下载文件: https://www.xb6v.com/ -> data\localized\interface_1_真实TVBox配置\js\script_de8516f1.js
2025-07-29 20:50:01,830 [INFO] app.services.new_localization_service:226 - URL模式检测为网站首页，跳过下载: https://www.xb6v.com/
2025-07-29 20:50:01,830 [INFO] app.services.new_localization_service:408 - 跳过网页文件: https://www.xb6v.com/ - Skipped: Detected as HTML webpage
2025-07-29 20:50:01,830 [INFO] app.services.new_localization_service:269 - 开始下载文件: https://im.feelec.com.cn/res/file.html?id=2c9a91099584ed490195f7bf50ac60b9 -> data\localized\interface_1_真实TVBox配置\js\file.html
2025-07-29 20:50:20,582 [INFO] root:154 - 请求开始: PUT http://localhost:8001/api/v1/interfaces/1
2025-07-29 20:50:20,600 [INFO] app.services.interface_service:379 - 接口源更新成功: 真实TVBox配置
2025-07-29 20:50:20,601 [INFO] root:163 - 请求完成: PUT http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.020s
2025-07-29 20:50:21,083 [INFO] app.services.new_localization_service:269 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/drpy2.js -> data\localized\interface_1_真实TVBox配置\js\drpy2.js
2025-07-29 20:50:21,511 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753793421196
2025-07-29 20:50:21,513 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753793421196
2025-07-29 20:50:21,514 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753793421196 状态码: 307 耗时: 0.002s
2025-07-29 20:50:21,520 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753793421196 状态码: 200 耗时: 0.007s
2025-07-29 20:50:21,826 [INFO] root:154 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753793421196
2025-07-29 20:50:21,841 [INFO] root:163 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753793421196 状态码: 200 耗时: 0.015s
2025-07-29 20:50:25,303 [INFO] app.services.new_localization_service:269 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/huya2.js -> data\localized\interface_1_真实TVBox配置\js\huya2.js
2025-07-29 20:50:28,374 [INFO] app.services.new_localization_service:269 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/drpy2.min.js -> data\localized\interface_1_真实TVBox配置\js\drpy2.min.js
2025-07-29 20:50:31,022 [INFO] app.services.new_localization_service:269 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/斗鱼直播.js -> data\localized\interface_1_真实TVBox配置\js\斗鱼直播.js
2025-07-29 20:50:33,671 [INFO] app.services.new_localization_service:269 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/drpy2.min.js -> data\localized\interface_1_真实TVBox配置\js\drpy2.min.js
2025-07-29 20:50:36,274 [INFO] app.services.new_localization_service:269 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/有声小说吧.js -> data\localized\interface_1_真实TVBox配置\js\有声小说吧.js
2025-07-29 20:50:38,789 [INFO] app.services.new_localization_service:269 - 开始下载文件: https://im.feelec.com.cn/res/file.html?id=2c9a91099293dce40194194e5a5f7474 -> data\localized\interface_1_真实TVBox配置\js\file.html
2025-07-29 20:50:44,504 [INFO] app.services.new_localization_service:269 - 开始下载文件: https://im.feelec.com.cn/res/file.html?id=2c9a91099293dce40194194f4eb2747e -> data\localized\interface_1_真实TVBox配置\js\file.html
2025-07-29 20:50:45,332 [INFO] app.services.new_localization_service:269 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/drpy2.min.js -> data\localized\interface_1_真实TVBox配置\js\drpy2.min.js
2025-07-29 20:50:47,995 [INFO] app.services.new_localization_service:269 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/fantaiying7/EXT/refs/heads/main/%E5%85%94%E5%B0%8F%E8%B4%9D.js -> data\localized\interface_1_真实TVBox配置\js\%E5%85%94%E5%B0%8F%E8%B4%9D.js
2025-07-29 20:50:50,519 [INFO] app.services.new_localization_service:269 - 开始下载文件: https://im.feelec.com.cn/res/file.html?id=2c9a91099293dce4019465168f841292 -> data\localized\interface_1_真实TVBox配置\js\file.html
2025-07-29 20:50:51,094 [INFO] app.services.new_localization_service:269 - 开始下载文件: https://im.feelec.com.cn/res/file.html?id=2c9a91099293dce401946513bb42128a -> data\localized\interface_1_真实TVBox配置\js\file.html
2025-07-29 20:50:53,831 [INFO] app.services.new_localization_service:269 - 开始下载文件: https://im.feelec.com.cn/res/file.html?id=2c9a91099293dce401946513bb2d1289 -> data\localized\interface_1_真实TVBox配置\js\file.html
2025-07-29 20:50:56,811 [INFO] app.services.new_localization_service:269 - 开始下载文件: https://im.feelec.com.cn/res/file.html?id=2c9a91099293dce401946513bd90128b -> data\localized\interface_1_真实TVBox配置\js\file.html
2025-07-29 20:50:57,910 [INFO] app.services.new_localization_service:269 - 开始下载文件: https://scrm-community.oss-cn-shenzhen.aliyuncs.com/miniso-vendor/20250425-868403-********************************.m3u -> data\localized\interface_1_真实TVBox配置\live\20250425-868403-********************************.m3u
2025-07-29 20:50:58,290 [INFO] app.services.new_localization_service:269 - 开始下载文件: https://gh-proxy.net/https://raw.githubusercontent.com/wwb521/live/main/tv.m3u -> data\localized\interface_1_真实TVBox配置\live\tv.m3u
2025-07-29 20:51:00,920 [INFO] app.services.new_localization_service:269 - 开始下载文件: http://127.0.0.1:9978/proxy?do=饭太硬&type=liveList -> data\localized\interface_1_真实TVBox配置\live\live_d04bc4a0.m3u
2025-07-29 20:51:04,989 [WARNING] app.services.new_localization_service:263 - 网页检测失败，继续下载: http://127.0.0.1:9978/proxy?do=饭太硬&type=liveList - Cannot connect to host 127.0.0.1:9978 ssl:default [Connect call failed ('127.0.0.1', 9978)]
2025-07-29 20:51:07,041 [ERROR] app.services.new_localization_service:311 - 下载文件失败 http://127.0.0.1:9978/proxy?do=饭太硬&type=liveList: Cannot connect to host 127.0.0.1:9978 ssl:default [Connect call failed ('127.0.0.1', 9978)]
2025-07-29 20:51:07,041 [WARNING] app.services.new_localization_service:419 - 下载失败: http://127.0.0.1:9978/proxy?do=饭太硬&type=liveList - Cannot connect to host 127.0.0.1:9978 ssl:default [Connect call failed ('127.0.0.1', 9978)]
2025-07-29 20:51:07,043 [INFO] app.services.new_localization_service:269 - 开始下载文件: http://tv.iill.top/m3u/Live -> data\localized\interface_1_真实TVBox配置\live\live_c9b0de40.m3u
2025-07-29 20:51:08,375 [INFO] app.services.new_localization_service:235 - Content-Type检测为HTML，跳过下载: http://tv.iill.top/m3u/Live (Content-Type: text/html)
2025-07-29 20:51:08,376 [INFO] app.services.new_localization_service:408 - 跳过网页文件: http://tv.iill.top/m3u/Live - Skipped: Detected as HTML webpage
2025-07-29 20:51:08,380 [INFO] root:163 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/localize 状态码: 200 耗时: 67.188s
