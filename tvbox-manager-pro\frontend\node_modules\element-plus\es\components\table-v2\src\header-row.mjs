import { columns } from './common.mjs';
import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';

const tableV2HeaderRowProps = buildProps({
  class: String,
  columns,
  columnsStyles: {
    type: definePropType(Object),
    required: true
  },
  headerIndex: Number,
  style: { type: definePropType(Object) }
});

export { tableV2HeaderRowProps };
//# sourceMappingURL=header-row.mjs.map
