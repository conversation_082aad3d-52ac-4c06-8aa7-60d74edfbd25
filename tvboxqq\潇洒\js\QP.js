var rule = {
    title: '懒盘搜',
    host: 'https://so.yuneu.com/',
    hostJs: '',
    headers: {
        'User-Agent': 'Mozilla/5.0 (Linux; Android 11; Pixel 5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.91 Mobile Safari/537.36',
    },
    编码: 'utf-8',
    timeout: 5000,
    url: 'https://so.yuneu.com/search?q=fyclass&format=video&page=fypage',
    filter_url: '',
    detailUrl: '',
    searchUrl: 'https://so.yuneu.com/search?q=**&format=video&page=fypage',
    searchable: 1,
    quickSearch: 1,
    filterable: 1,
    class_name: '',
    class_url: '',
    proxy_rule: '',
    sniffer: false,
    isVideo: '',
    play_parse: true,
    parse_url: '',
    lazy: "js:\n        input = 'push://' + input;\n    ",
    limit: 9,
    double: false,
    // 推荐: '*',
    一级: 'js:\n        let html = fetch(input);\n        let list = pdfa(html, "body&&.semi-space-medium-vertical");\n        VODS = list.map(x => {\n            let remarks = pdfh(x, "div&&img&&alt");\n            // 过滤掉包含"迅雷云盘"的内容\n            if(remarks.includes("迅雷云盘")) return null;\n            return {\n                vod_name: pdfh(x, "div&&a&&title"),\n                vod_pic: \'https://img0.baidu.com/it/u=2841223652,2325996620&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500\',\n                vod_remarks: remarks,\n                vod_content: remarks,\n                vod_id: pdfh(x, "div&&a&&href")\n            }\n        }).filter(x => x !== null);\n    ',
    二级: {
        title: 'h1&&Text',
        img: 'img&&src',
        desc: '.card-text:eq(2)&&Text;;;;',
        content: 'body&&.semi-space-loose-vertical&&a&&href',
        tabs: "js:TABS = ['全盘搜']",
        lists: "js:\n            LISTS = [];\n            let lists1 = pdfa(html, 'body&&.semi-space-loose-vertical').map(it => {\n                let _tt = pdfh(it, 'span&&title');\n                let _uu = pdfh(it, 'a&&href');\n                return _tt + '$' + _uu;\n            });\n            LISTS.push(lists1);\n        ",
    },
    搜索: 'js:\n        let html = fetch(input);\n        let list = pdfa(html, "body&&.semi-space-medium-vertical");\n        VODS = list.map(x => {\n            let remarks = pdfh(x, "div&&img&&alt");\n            // 过滤掉包含"迅雷云盘"的内容\n            if(remarks.includes("迅雷云盘")) return null;\n            return {\n                vod_name: pdfh(x, "div&&a&&title"),\n                vod_pic: \'https://img0.baidu.com/it/u=2841223652,2325996620&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500\',\n                vod_remarks: remarks,\n                vod_content: remarks,\n                vod_id: pdfh(x, "div&&a&&href")\n            }\n        }).filter(x => x !== null);\n    ',
    cate_exclude: '首页|留言|APP|下载|资讯|新闻|动态',
    tab_exclude: '猜你|喜欢|下载|剧情|榜|评论',
    类型: '影视',
    homeUrl: 'https://so.yuneu.com/',
    二级访问前: '',
    encoding: 'utf-8',
    search_encoding: '',
    图片来源: '',
    图片替换: '',
    play_json: [],
    pagecount: {},
    tab_remove: [],
    tab_order: [],
    tab_rename: {},
}