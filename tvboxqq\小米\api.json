//资源全部免费，仅供测试交流，请勿内置传播与转卖；关注微信公众号：奇奇乐分享，发送“本地包”获取教程与更多资源！

{
    "spider": "./spider.jar",
    "wallpaper": "https://xn--4kq62z5rby2qupqba63sb41h.999888987.xyz/",
    "sites": [
        {
            "key": "豆豆",
            "name": "🌈热播┃推荐",
            "type": 3,
            "api": "csp_Douban",
            "searchable": 0
        },
        {
            "key": "config",
            "name": "⚙网盘┃配置 项目链接：https://link3.cc/lovemm",
            "type": 3,
            "api": "csp_Config",
            "searchable": 0
        },
        {
            "key": "csp_Netfixtv",
            "name": "💌至臻┃4K网盘",
            "type": 3,
            "changeable": "0",
            "quickSearch": 1,
            "api": "csp_Duopan",
            "filterable": 1,
            "ext": {
                "site_urls": [
                    "https://mihdr.top",
                    "https://www.mihdr.top",
                    "https://www.miqk.cc",
                    "https://xiaomiai.site",
                    "https://xiaomi666.fun",
                    "https://www.zhizhenpan.fun"
                ],
                "url_key": "Netfixtv",
                "token": "",
                "ucCookie": "",
                "quarkCookie": "",
                "threadinfo": {
                    "chunksize": 512,
                    "threads": 16
                }
            }
        },
        {
            "key": "csp_Netfixtv2",
            "name": "🎥二小┃4K网盘",
            "type": 3,
            "changeable": "0",
            "quickSearch": 1,
            "api": "csp_Duopan",
            "filterable": 1,
            "ext": {
                "site_urls": [
                    "https://www.2xiaopan.fun/",
                    "https://2xiaopan.fun/",
                    "https://www.2xiaoyun.fun/",
                    "https://www.2xiaoyun.fun/",
                    "https://www.xhww.net/"
                ],
                "url_key": "UC",
                "token": "",
                "ucCookie": "",
                "quarkCookie": "",
                "threadinfo": {
                    "chunksize": 512,
                    "threads": 16
                }
            }
        },
        {
            "key": "csp_Wogg",
            "name": "🧸玩偶┃4K网盘",
            "type": 3,
            "api": "csp_Wogg",
            "changeable": "0",
            "quickSearch": 1,
            "filterable": 1,
            "ext": "Yu2Y3Q8tqDTqZ+GCqfQZz8i/vsP/tl9O+/0ZjpgnBg8rYFx7ctv7/EwYIQ8UriiQKDIJuqIvlQQje6dXLxWHRiSlxkxqMyYKI0d1JGw46AuOAoB1VAvOCaqYqiTezjHHt8wMymbT/vDdiFXkMbc44kNGdjpzSyL2/kBZIAQaYMPgSkEYlv4faMeCi3MaCLYaT3C7qaUzLc56RYGLF2rmtIoYgMFOG9eSpyjO66jU/3v2f1yXnqTEEKd1/+/m8DBi8KudUm92Pa/NN8zRcY5nClAo1Eqn9bGzuO/+M4V5KG/RhdFdHyzxIpxfgoBRhn4j7GEzWBxrg4JiW2kP8Vo0iQm0S8aJUTOf1ROVp2sKW36D2Q/IgMvEqazefGXZSyNSTsjbNbVc2cHMxKrbUQIPqhD5y93kdVqr5glHjuqjrDC8Pa54/l2nFeWMZIRN7ZxMpY9O1WSazlqA1GI2A6UtmKIx6r2dX7seMOOENI+Tjl2ojAYQscbGgYpzVNL9VNf/w7xQaHqjtWLUcAOmJYweA0mZb6cGoAd8Gjcwz7iper0s53AViof2QbBffDKJxF820BsiCZAMp+Nq1Y7wrFqnnEN7HiE+YHPDxNfvBXnEgubPXu6DeZGa6edzqVbRwO52"
        },
        {
            "key": "csp_Duopan",
            "name": "🖍蜡笔┃4K网盘",
            "type": 3,
            "quickSearch": 0,
            "api": "csp_Duopan",
            "filterable": 1,
            "ext": {
                "site_urls": [
                    "https://feimao666.fun",
                    "http://feimao888.fun",
                    "http://feimaoai.site",
                    "http://www.labi88.sbs",
                    "http://fmao.site",
                    "https://fmao.shop"
                ],
                "threadinfo": {
                    "chunksize": 512,
                    "threads": 16
                },
                "url_key": "Duopan4"
            }
        },
        {
            "key": "csp_Bili",
            "name": "🏵️哔哩┃哔哩",
            "type": 3,
            "api": "csp_Bili",
            "quickSearch": 0,
            "searchable": 1,
            "ext": {
                "json": "./json/1744612665263_bili.json",
                "cookie": ""
            },
            "style": {
                "type": "rect",
                "ratio": 1.333
            }
        },
        {
            "key": "荐片",
            "name": "➖荐片┃磁力",
            "quickSearch": 0,
            "searchable": 1,
            "type": 3,
            "api": "csp_Jianpian",
            "style": {
                "type": "rect",
                "ratio": 1.333
            },
            "ext": ""
        },
        {
            "key": "娱乐",
            "name": "🎮游戏┃娱乐",
            "quickSearch": 0,
            "searchable": 1,
            "type": 3,
            "api": "./api/LIVES.py",
            "style": {
                "type": "rect",
                "ratio": 1.333
            },
            "ext": ""
        },
        {
            "key": "88看球",
            "name": "🏀看球┃直播",
            "type": 3,
            "api": "csp_Kanqiu",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "order_num": 0,
            "changeable": 0
        },
        {
            "key": "天天",
            "name": "💯天天┃影视",
            "type": 3,
            "quickSearch": 1,
            "searchable": 1,
            "api": "csp_AppRJ",
            "ext": "vxw35/hHSj07Q+maxQzOVMq1rjRCOTXpUCx8iKu5jIg="
        },
        {
            "key": "csp_nongmin",
            "name": "💯农民┃明天修",
            "type": 3,
            "api": "csp_Wwys",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "https://www.wwgz.cn"
        },
        {
            "key": "热热",
            "name": "🔥热热┃影视",
            "type": 3,
            "searchable": 1,
            "quickSearch": 1,
            "api": "csp_AppRJ",
            "ext": "jsSMEuhTZIAHjnUoLBzKdlRu5exzno6M4efF8LzwjWM="
        },
        {
            "key": "云速",
            "name": "☁️云速┃影视",
            "type": 3,
            "searchable": 1,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": "rP+4azM8YFgp3eAhyeALtUUEFpbkqmzGj2V5VZryxRbpkSjlsHbarRCZPOInvId4s3WK9rZ2YJsDy8NvMqQKQXowVmh33j+qD55VxmvMzzfiJ2fHkCRZg4+8NFWs+b1X"
        },
        {
            "key": "麻花",
            "name": "🌸麻花┃影视",
            "type": 3,
            "quickSearch": 1,
            "searchable": 1,
            "api": "csp_AppGet",
            "ext": "rP+4azM8YFgp3eAhyeALtUUEFpbkqmzGj2V5VZryxRbpkSjlsHbarRCZPOInvId4s3WK9rZ2YJsDy8NvMqQKQXowVmh33j+qD55VxmvMzzesNDM87sUul1Ii8NbP2XB5"
        },
        {
            "key": "QD4K",
            "name": "🐷猪猪┃影视",
            "type": 3,
            "searchable": 1,
            "quickSearch": 1,
            "api": "csp_AppYsV2",
            "playerType": 2,
            "ext": "./txt/cs.txt"
        },
        {
            "key": "咖啡",
            "name": "☕咖啡┃影视",
            "type": 3,
            "quickSearch": 1,
            "searchable": 1,
            "api": "csp_AppGet",
            "ext": "kzWGWFofGVJ3C/RF+lgwJ2duecy3bVUlIwiw6yr3vD1a1VenykG71Lx0iDGhqS6msfs2NS2llsxT5Z93XTR5ow=="
        },
        {
            "key": "顾我",
            "name": "🎫顾我┃影视",
            "type": 3,
            "quickSearch": 1,
            "searchable": 1,
            "api": "csp_AppGet",
            "ext": "anMxhJnIDc1c2tyG/e37xUzar6eMUg8rOlemrnxifa3xApTYq401x7vVwbCRPlgADD7zoKQvVffdAwprrKeNlQ=="
        },
        {
            "key": "大豆",
            "name": "✅大豆┃影视",
            "type": 3,
            "quickSearch": 1,
            "searchable": 1,
            "api": "csp_AppGet",
            "ext": "Qrf5S6Si5oF7dQyuv+Srh3uh0lT3z1Y7u59ip9hRVeUKFHSUUbLyGMREENFjE1N9FXjZ6Z7tiLWs6P15Ol/g5Po80zDNWJPEEoj/nv3Yelo="
        },
        {
            "key": "海豚",
            "name": "🍁海豚┃影视",
            "type": 3,
            "searchable": 1,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": "QxNFQL63IQgXoB/O+Q12SpWJNcSeybsKVx0uAk9+pBwLEYRybmr89ubfoMpO9ZMnmJwYP5eKIrLK/w5t9S+6+F2ZlfWUyUoDUP2rV9QVabZh8nM+rBV7F/sxe+7GgsdL"
        },
        {
            "key": "巧技",
            "name": "🚗聚搜┃影视",
            "type": 3,
            "searchable": 1,
            "quickSearch": 1,
            "api": "csp_qiao2",
            "playerType": 2,
            "jar": "./jars/巧技.jar",
            "ext": "7lj763gg402i79425739i7jghj118797l4hj840gi18633331l4708g2h7145403549g44l8ii56i187681hkjj3hhgh1ih3l32j250lk1k786lj20j468hk3hli4l46gig4i3g7g2722328j0136h01i7g5183k22k7gg3i72hk81gl8k9839kl7i0707"
        },
        {
            "key": "huomaoys",
            "name": "🎬️火猫┃影视",
            "type": 3,
            "searchable": 1,
            "quickSearch": 1,
            "api": "csp_Muou",
            "jar": "./jars/巧技.jar",
            "playerType": 2,
            "ext": "7lj763gg0939790i413gi484k8058896highi4414h68l7g6hk8qiaojig9k2k289l9ik807i213k5j602"
        },
        {
            "key": "金牌app",
            "name": "🎬️爆炸┃影视",
            "type": 3,
            "searchable": 1,
            "quickSearch": 1,
            "api": "csp_Muou",
            "jar": "./jars/巧技.jar",
            "playerType": 2,
            "ext": "7lj763gg402i79425i3l85i6h848i295l5hiji5l828g3l3jjhg6kg7410lhjkqiaojij3ig1lg475178k7h0il4ig3h753h7hi516758699jh2g5h433li30gk11g73l90312h4g7"
        },
        {
            "key": "公公",
            "name": "🎬️公共┃影视",
            "type": 3,
            "searchable": 1,
            "quickSearch": 1,
            "api": "csp_Muou",
            "jar": "./jars/巧技.jar",
            "playerType": 2,
            "ext": "7lj763gg402i7942463ji4qiaojijjh456889il6k6i35kj995h4j18li7kl2870klhg8hi647j5707k4ki7ig6953kj"
        },
        {
            "key": "DAY",
            "name": "🧵UPUP┃影视",
            "type": 3,
            "quickSearch": 1,
            "searchable": 1,
            "api": "./api/tt.py"
        },
        {
            "key": "步步",
            "name": "👟步步┃影视",
            "type": 3,
            "searchable": 1,
            "quickSearch": 1,
            "api": "./api/drpy2.min.js",
            "ext": "./js/测试.js"
        },
        {
            "key": "牛牛",
            "name": "🍁牛牛┃影视",
            "type": 3,
            "quickSearch": 1,
            "searchable": 1,
            "api": "./api/mioaying.py"
        },
        {
            "key": "csp_AppXY",
            "name": "🐸短剧┃影视",
            "type": 3,
            "searchable": 1,
            "quickSearch": 1,
            "api": "csp_AppXY",
            "ext": "https://xvapp.xingya.com.cn"
        },
        {
            "key": "csp_baibai",
            "name": "💯白白┃影视",
            "type": 3,
            "searchable": 1,
            "quickSearch": 1,
            "api": "csp_SuBaiBai",
            "ext": "https://www.subaibai.com"
        },
        {
            "key": "csp_LiteApple",
            "name": "🍎苹果┃影视",
            "type": 3,
            "searchable": 1,
            "quickSearch": 1,
            "api": "csp_LiteApple"
        },
        {
            "key": "csp_Gz360",
            "name": "🍉瓜子┃影视",
            "type": 3,
            "searchable": 1,
            "quickSearch": 1,
            "api": "csp_Gz360"
        },
        {
            "key": "csp_Jpys",
            "name": "🥇金牌┃影视",
            "type": 3,
            "searchable": 1,
            "quickSearch": 1,
            "api": "csp_Jpys"
        },
        {
            "key": "测试",
            "name": "🐓剧霸┃影视",
            "type": 3,
            "searchable": 1,
            "quickSearch": 1,
            "api": "./api/jubaba.py",
            "ext": ""
        },
        {
            "key": "csp_MiSou",
            "name": "🔍米搜┃盘搜",
            "type": 3,
            "searchable": 1,
            "quickSearch": 1,
            "api": "csp_MiSou"
        },
        {
            "key": "听书",
            "name": "📚听书┃听书",
            "type": 3,
            "api": "csp_TingBook",
            "searchable": 1,
            "quickSearch": 1,
            "ext": "http://www.6yueting.com"
        },
        {
            "key": "Aid",
            "name": "🚑急救┃知识",
            "type": 3,
            "api": "csp_FirstAid",
            "searchable": 1,
            "quickSearch": 1,
            "style": {
                "type": "rect",
                "ratio": 3.8
            }
        },
        {
            "key": "push_agent",
            "name": "⚠️接口开源免费，仅供测试，请测试完删除!",
            "type": 3,
            "api": "csp_Push",
            "searchable": 0,
            "ext": ""
        }
    ],
    "parses": [
        {
            "name": "Json聚合",
            "type": 3,
            "url": "Demo"
        },
        {
            "name": "影视聚合",
            "type": 3,
            "url": "影视"
        },
        {
            "name": "牛牛2",
            "type": 1,
            "url": "https://zy.qiaoji8.com/xiafan.php?url=",
            "ext": {
                "flag": [
                    "QD4K",
                    "iyf",
                    "duanju",
                    "gzcj",
                    "GTV",
                    "GZYS",
                    "weggz",
                    "Ace"
                ],
                "header": {
                    "User-Agent": "okhttp/4.9.1"
                }
            }
        },
        {
            "name": "8090g",
            "type": 0,
            "url": ""
        },
        {
            "name": "虾米",
            "type": 0,
            "url": ""
        },
        {
            "name": "夜幕",
            "type": 0,
            "url": ""
        }
    ],
    "logo": "https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1752137757797/ab67656300005f1f66fff1b2f972193c3d6433eb.jpg",
    "lives": [
        {
            "name": "平台直播",
            "type": 0,
            "url": "./lives/平台直播.txt",
            "ua": "okhttp/3.15"
        }
    ],
    "rules": [
        {
            "name": "♻️量非",
            "hosts": [
                "lz",
                "vip.lz",
                "v.cdnlz",
                "hd.lz",
                "ffzy",
                "vip.ffzy",
                "hd.ffzy"
            ],
            "regex": [
                "#EXT-X-DISCONTINUITY\r*\n*#EXTINF:6.666667,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXT-X-DISCONTINUITY\r*\n*#EXTINF:6.600000,[\\s\\S]*?#EXT-X-DISCONTINUITY"
            ]
        },
        {
            "name": "♻️索尼",
            "hosts": [
                "suonizy",
                "qrssv.com"
            ],
            "regex": [
                "15.1666",
                "15.2666"
            ]
        },
        {
            "name": "♻️乐视",
            "hosts": [
                "leshiyun"
            ],
            "regex": [
                "15.92"
            ]
        },
        {
            "name": "♻️优质",
            "hosts": [
                "yzzy",
                "playback"
            ],
            "regex": [
                "16.63",
                "18.66",
                "17.66",
                "19.13"
            ]
        },
        {
            "name": "♻️快看",
            "hosts": [
                "kuaikan",
                "vip.kuaikan"
            ],
            "regex": [
                "15.32",
                "15.231",
                "18.066"
            ]
        },
        {
            "name": "♻️360",
            "hosts": [
                "lyhuicheng"
            ],
            "regex": [
                "#EXTINF.*?\\s+.*?hrz8QcR9.*?\\.ts\\s+",
                "#EXT-X-KEY:METHOD=NONE[\\s\\S]*?#EXT-X-DISCONTINUITY"
            ]
        },
        {
            "name": "♻️开源棋牌",
            "hosts": [
                "askzycdn",
                "jkunbf",
                "bfikuncdn",
                "bfaskcdn"
            ],
            "regex": [
                "#EXT-X-KEY:METHOD=NONE\r*\n*#EXTINF:5,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXT-X-KEY:METHOD=AES-128,URI=\"[^\"]+\"\r*\n*#EXTINF:3.333,[\\s\\S]*?#EXT-X-DISCONTINUITY"
            ]
        },
        {
            "name": "♻️暴风",
            "hosts": [
                "bfengbf.com",
                "bfzy",
                "c1"
            ],
            "regex": [
                "#EXTINF.*?\\s+.*?adjump.*?\\.ts\\s+",
                "#EXT-X-DISCONTINUITY\r*\n*#EXTINF:3,[\\s\\S]*?#EXT-X-DISCONTINUITY"
            ]
        },
        {
            "name": "♻️农民",
            "hosts": [
                "toutiaovod.com"
            ],
            "regex": [
                "video/tos/cn"
            ]
        },
        {
            "name": "♻️火山",
            "hosts": [
                "huoshan.com"
            ],
            "regex": [
                "item_id="
            ]
        },
        {
            "name": "♻️抖音",
            "hosts": [
                "douyin.com"
            ],
            "regex": [
                "is_play_url="
            ]
        },
        {
            "name": "♻️磁力",
            "hosts": [
                "magnet"
            ],
            "regex": [
                "最 新",
                "直 播",
                "更 新"
            ]
        },
        {
            "name": "♻️饭团点击",
            "hosts": [
                "dadagui",
                "freeok",
                "dadagui"
            ],
            "script": [
                "document.querySelector(\"#playleft iframe\").contentWindow.document.querySelector(\"#start\").click();"
            ]
        },
        {
            "name": "♻️毛驴点击",
            "hosts": [
                "www.maolvys.com"
            ],
            "script": [
                "document.getElementsByClassName('swal-button swal-button--confirm')[0].click()"
            ]
        }
    ]
}