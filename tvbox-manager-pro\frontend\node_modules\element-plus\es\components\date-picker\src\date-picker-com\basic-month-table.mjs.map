{"version": 3, "file": "basic-month-table.mjs", "sources": ["../../../../../../../packages/components/date-picker/src/date-picker-com/basic-month-table.vue"], "sourcesContent": ["<template>\n  <table\n    role=\"grid\"\n    :aria-label=\"t('el.datepicker.monthTablePrompt')\"\n    :class=\"ns.b()\"\n    @click=\"handleMonthTableClick\"\n    @mousemove=\"handleMouseMove\"\n  >\n    <tbody ref=\"tbodyRef\">\n      <tr v-for=\"(row, key) in rows\" :key=\"key\">\n        <td\n          v-for=\"(cell, key_) in row\"\n          :key=\"key_\"\n          :ref=\"(el) => isSelectedCell(cell) && (currentCellRef = el as HTMLElement)\"\n          :class=\"getCellStyle(cell)\"\n          :aria-selected=\"`${isSelectedCell(cell)}`\"\n          :aria-label=\"t(`el.datepicker.month${+cell.text + 1}`)\"\n          :tabindex=\"isSelectedCell(cell) ? 0 : -1\"\n          @keydown.space.prevent.stop=\"handleMonthTableClick\"\n          @keydown.enter.prevent.stop=\"handleMonthTableClick\"\n        >\n          <el-date-picker-cell\n            :cell=\"{\n              ...cell,\n              renderText: t('el.datepicker.months.' + months[cell.text]),\n            }\"\n          />\n        </td>\n      </tr>\n    </tbody>\n  </table>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, nextTick, ref, watch } from 'vue'\nimport dayjs from 'dayjs'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { castArray, hasClass } from '@element-plus/utils'\nimport { basicMonthTableProps } from '../props/basic-month-table'\nimport { datesInMonth, getValidDateOfMonth } from '../utils'\nimport ElDatePickerCell from './basic-cell-render'\n\ntype MonthCell = {\n  column: number\n  row: number\n  disabled: boolean\n  start: boolean\n  end: boolean\n  text: number\n  type: 'normal' | 'today'\n  inRange: boolean\n}\n\nconst props = defineProps(basicMonthTableProps)\nconst emit = defineEmits(['changerange', 'pick', 'select'])\n\nconst ns = useNamespace('month-table')\n\nconst { t, lang } = useLocale()\nconst tbodyRef = ref<HTMLElement>()\nconst currentCellRef = ref<HTMLElement>()\nconst months = ref(\n  props.date\n    .locale('en')\n    .localeData()\n    .monthsShort()\n    .map((_) => _.toLowerCase())\n)\nconst tableRows = ref<MonthCell[][]>([\n  [] as MonthCell[],\n  [] as MonthCell[],\n  [] as MonthCell[],\n])\nconst lastRow = ref<number>()\nconst lastColumn = ref<number>()\nconst rows = computed<MonthCell[][]>(() => {\n  const rows = tableRows.value\n\n  const now = dayjs().locale(lang.value).startOf('month')\n\n  for (let i = 0; i < 3; i++) {\n    const row = rows[i]\n    for (let j = 0; j < 4; j++) {\n      const cell = (row[j] ||= {\n        row: i,\n        column: j,\n        type: 'normal',\n        inRange: false,\n        start: false,\n        end: false,\n        text: -1,\n        disabled: false,\n      })\n\n      cell.type = 'normal'\n\n      const index = i * 4 + j\n      const calTime = props.date.startOf('year').month(index)\n\n      const calEndDate =\n        props.rangeState.endDate ||\n        props.maxDate ||\n        (props.rangeState.selecting && props.minDate) ||\n        null\n\n      cell.inRange =\n        !!(\n          props.minDate &&\n          calTime.isSameOrAfter(props.minDate, 'month') &&\n          calEndDate &&\n          calTime.isSameOrBefore(calEndDate, 'month')\n        ) ||\n        !!(\n          props.minDate &&\n          calTime.isSameOrBefore(props.minDate, 'month') &&\n          calEndDate &&\n          calTime.isSameOrAfter(calEndDate, 'month')\n        )\n\n      if (props.minDate?.isSameOrAfter(calEndDate)) {\n        cell.start = !!(calEndDate && calTime.isSame(calEndDate, 'month'))\n        cell.end = props.minDate && calTime.isSame(props.minDate, 'month')\n      } else {\n        cell.start = !!(props.minDate && calTime.isSame(props.minDate, 'month'))\n        cell.end = !!(calEndDate && calTime.isSame(calEndDate, 'month'))\n      }\n\n      const isToday = now.isSame(calTime)\n      if (isToday) {\n        cell.type = 'today'\n      }\n\n      cell.text = index\n      cell.disabled = props.disabledDate?.(calTime.toDate()) || false\n    }\n  }\n  return rows\n})\n\nconst focus = () => {\n  currentCellRef.value?.focus()\n}\n\nconst getCellStyle = (cell: MonthCell) => {\n  const style = {} as any\n  const year = props.date.year()\n  const today = new Date()\n  const month = cell.text\n\n  style.disabled = props.disabledDate\n    ? datesInMonth(props.date, year, month, lang.value).every(\n        props.disabledDate\n      )\n    : false\n  style.current =\n    castArray(props.parsedValue).findIndex(\n      (date) =>\n        dayjs.isDayjs(date) && date.year() === year && date.month() === month\n    ) >= 0\n  style.today = today.getFullYear() === year && today.getMonth() === month\n\n  if (cell.inRange) {\n    style['in-range'] = true\n\n    if (cell.start) {\n      style['start-date'] = true\n    }\n\n    if (cell.end) {\n      style['end-date'] = true\n    }\n  }\n  return style\n}\n\nconst isSelectedCell = (cell: MonthCell) => {\n  const year = props.date.year()\n  const month = cell.text\n  return (\n    castArray(props.date).findIndex(\n      (date) => date.year() === year && date.month() === month\n    ) >= 0\n  )\n}\n\nconst handleMouseMove = (event: MouseEvent) => {\n  if (!props.rangeState.selecting) return\n\n  let target = event.target as HTMLElement\n  if (target.tagName === 'SPAN') {\n    target = target.parentNode?.parentNode as HTMLElement\n  }\n  if (target.tagName === 'DIV') {\n    target = target.parentNode as HTMLElement\n  }\n  if (target.tagName !== 'TD') return\n\n  const row = (target.parentNode as HTMLTableRowElement).rowIndex\n  const column = (target as HTMLTableCellElement).cellIndex\n  // can not select disabled date\n  if (rows.value[row][column].disabled) return\n\n  // only update rangeState when mouse moves to a new cell\n  // this avoids frequent Date object creation and improves performance\n  if (row !== lastRow.value || column !== lastColumn.value) {\n    lastRow.value = row\n    lastColumn.value = column\n    emit('changerange', {\n      selecting: true,\n      endDate: props.date.startOf('year').month(row * 4 + column),\n    })\n  }\n}\nconst handleMonthTableClick = (event: MouseEvent | KeyboardEvent) => {\n  const target = (event.target as HTMLElement)?.closest(\n    'td'\n  ) as HTMLTableCellElement\n  if (target?.tagName !== 'TD') return\n  if (hasClass(target, 'disabled')) return\n  const column = target.cellIndex\n  const row = (target.parentNode as HTMLTableRowElement).rowIndex\n  const month = row * 4 + column\n  const newDate = props.date.startOf('year').month(month)\n  if (props.selectionMode === 'months') {\n    if (event.type === 'keydown') {\n      emit('pick', castArray(props.parsedValue), false)\n      return\n    }\n    const newMonth = getValidDateOfMonth(\n      props.date,\n      props.date.year(),\n      month,\n      lang.value,\n      props.disabledDate\n    )\n    const newValue = hasClass(target, 'current')\n      ? castArray(props.parsedValue).filter(\n          (d) =>\n            // Filter out the selected month only when both year and month match\n            // This allows remove same months from different years #20019\n            d?.year() !== newMonth.year() || d?.month() !== newMonth.month()\n        )\n      : castArray(props.parsedValue).concat([dayjs(newMonth)])\n    emit('pick', newValue)\n  } else if (props.selectionMode === 'range') {\n    if (!props.rangeState.selecting) {\n      emit('pick', { minDate: newDate, maxDate: null })\n      emit('select', true)\n    } else {\n      if (props.minDate && newDate >= props.minDate) {\n        emit('pick', { minDate: props.minDate, maxDate: newDate })\n      } else {\n        emit('pick', { minDate: newDate, maxDate: props.minDate })\n      }\n      emit('select', false)\n    }\n  } else {\n    emit('pick', month)\n  }\n}\n\nwatch(\n  () => props.date,\n  async () => {\n    if (tbodyRef.value?.contains(document.activeElement)) {\n      await nextTick()\n      currentCellRef.value?.focus()\n    }\n  }\n)\n\ndefineExpose({\n  /**\n   * @description focus current cell\n   */\n  focus,\n})\n</script>\n"], "names": ["rows", "_openBlock", "_createElementBlock", "_unref", "_normalizeClass", "_createElementVNode", "_Fragment", "_renderList"], "mappings": ";;;;;;;;;;;;;;;;;AAwDA,IAAM,MAAA,EAAA,GAAK,aAAa,aAAa,CAAA,CAAA;AAErC,IAAA,MAAM,EAAE,CAAA,EAAG,IAAK,EAAA,GAAI,SAAU,EAAA,CAAA;AAC9B,IAAA,MAAM,WAAW,GAAiB,EAAA,CAAA;AAClC,IAAA,MAAM,iBAAiB,GAAiB,EAAA,CAAA;AACxC,IAAA,MAAM,MAAS,GAAA,GAAA,CAAA,KAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,UAAA,EAAA,CAAA,WAAA,EAAA,CAAA,GAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,WAAA,EAAA,CAAA,CAAA,CAAA;AAAA,IAAA,MACP,SACI,GAAA,GAAA,CAAA;AAGmB,MAC/B,EAAA;AACA,MAAA,EAAA;AAAqC,MACnC,EAAC;AAAA,KAAA,CACD,CAAC;AAAA,IAAA,MACA,OAAA,GAAA,GAAA,EAAA,CAAA;AAAA,IACH,MAAC,UAAA,GAAA,GAAA,EAAA,CAAA;AACD,IAAA,MAAM,eAAsB,CAAA,MAAA;AAC5B,MAAA,IAAM;AACN,MAAM,MAAA,KAAO,YAA8B,CAAA,KAAA,CAAA;AACzC,MAAA,MAAMA,WAAiB,EAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,OAAA,CAAA,OAAA,CAAA,CAAA;AAEvB,MAAM,KAAA,IAAA,CAAA,UAAc,CAAA,EAAA,CAAA,EAAA;AAEpB,QAAA,MAAA,GAAa,GAAA,KAAO,CAAA,CAAA,CAAA,CAAG;AACrB,QAAM,KAAA,IAAA,CAAA,UAAY,CAAA,EAAA,CAAA,EAAA,EAAA;AAClB,UAAA,MAAA,IAAa,GAAA,GAAO,CAAA,CAAA,CAAA,KAAQ,GAAA,CAAA,CAAA,CAAA,GAAA;AAC1B,YAAM,GAAA,EAAA,CAAA;AAAmB,YACvB,MAAK,EAAA,CAAA;AAAA,YACL,IAAQ,EAAA,QAAA;AAAA,YACR,OAAM,EAAA,KAAA;AAAA,YACN,KAAS,EAAA,KAAA;AAAA,YACT,GAAO,EAAA,KAAA;AAAA,YACP,IAAK,EAAA,CAAA,CAAA;AAAA,YACL,QAAM,EAAA,KAAA;AAAA,WAAA,CACN,CAAU;AAAA,UACZ,IAAA,CAAA,IAAA,GAAA,QAAA,CAAA;AAEA,UAAA,MAAY,KAAA,GAAA,CAAA,GAAA,CAAA,GAAA,CAAA,CAAA;AAEZ,UAAM,MAAA,OAAA,QAAgB,CAAA,IAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA;AACtB,UAAA,MAAM,UAAU,GAAM,KAAA,CAAA,UAAa,CAAM,OAAE,SAAW,CAAA,OAAA,IAAA,KAAA,CAAA,UAAA,CAAA,SAAA,IAAA,KAAA,CAAA,OAAA,IAAA,IAAA,CAAA;AAEtD,UAAM,IAAA,CAAA,OAAA,GAAA,CAAA,EAAA,KACE,CAAA,OAAA,IAAA,OACN,CAAA,2BACO,EAAA,OAAA,CAAA,IAAwB,UAAA,IAAA,OAC/B,CAAA,cAAA,CAAA,UAAA,EAAA,OAAA,CAAA,CAAA,IAAA,CAAA,EAAA,KAAA,CAAA,OAAA,IAAA,OAAA,CAAA,cAAA,CAAA,KAAA,CAAA,OAAA,EAAA,OAAA,CAAA,IAAA,UAAA,IAAA,OAAA,CAAA,aAAA,CAAA,UAAA,EAAA,OAAA,CAAA,CAAA,CAAA;AAEF,UAAA,IAAA,CAAK,EACH,GAAA,KAAA,CAAC,OACC,KACA,IAAA,GAAA,KAAA,CAAA,GAAsB,EAAA,CAAA,aAAA,CAAA,UAAe,CAAA,EAAA;AAWzC,YAAA,IAAU,CAAA,KAAA,GAAA,CAAA,EAAA,UAAuB,IAAA,OAAA,CAAA,MAAa,CAAA,UAAA,EAAA,OAAA,CAAA,CAAA,CAAA;AAC5C,YAAA,IAAA,CAAK,WAAW,mBAAsB,CAAA,MAAA,CAAA,eAA0B,OAAA,CAAA,CAAA;AAChE,WAAA,MAAK;AAA4D,YAC5D,IAAA,CAAA,KAAA,GAAA,CAAA,EAAA,KAAA,CAAA,OAAA,IAAA,OAAA,CAAA,MAAA,CAAA,KAAA,CAAA,OAAA,EAAA,OAAA,CAAA,CAAA,CAAA;AACL,YAAK,IAAA,CAAA,GAAA,GAAA,CAAA,EAAS,qBAA2B,CAAA,MAAA,CAAA,UAAa,EAAA,OAAA,CAAS,CAAO,CAAA;AACtE,WAAA;AAA8D,UAChE,MAAA,OAAA,GAAA,GAAA,CAAA,MAAA,CAAA,OAAA,CAAA,CAAA;AAEA,UAAM,IAAA,OAAA,EAAA;AACN,YAAA,IAAa,CAAA,IAAA,GAAA,OAAA,CAAA;AACX,WAAA;AAAY,UACd,IAAA,CAAA,IAAA,GAAA,KAAA,CAAA;AAEA,UAAA,IAAA,CAAK,QAAO,GAAA,CAAA,CAAA,EAAA,GAAA,KAAA,CAAA,YAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,EAAA,OAAA,CAAA,MAAA,EAAA,CAAA,KAAA,KAAA,CAAA;AACZ,SAAA;AAA0D,OAC5D;AAAA,MACF,OAAA,KAAA,CAAA;AACA,KAAOA,CAAAA,CAAAA;AAAA,IACT,MAAC,KAAA,GAAA,MAAA;AAED,MAAA,IAAM;AACJ,MAAA,CAAA,EAAA,GAAA,oBAA4B,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,EAAA,CAAA;AAAA,KAC9B,CAAA;AAEA,IAAM,MAAA,YAAA,GAAe,CAAC,IAAoB,KAAA;AACxC,MAAA,MAAM,QAAQ,EAAC,CAAA;AACf,MAAM,MAAA,IAAA,GAAO,KAAM,CAAA,IAAA,CAAK,IAAK,EAAA,CAAA;AAC7B,MAAM,MAAA,KAAA,uBAAY,IAAK,EAAA,CAAA;AACvB,MAAA,MAAM,QAAQ,IAAK,CAAA,IAAA,CAAA;AAEnB,MAAM,KAAA,CAAA,QAAA,GAAW,KAAM,CAAA,YAAA,GACnB,YAAa,CAAA,KAAA,CAAM,MAAM,IAAM,EAAA,KAAA,EAAO,IAAK,CAAA,KAAK,CAAE,CAAA,KAAA,CAAA,KAAA,CAAA,YAAA,CAAA,GAAA,KAAA,CAAA;AAAA,MAAA,KAC1C,CAAA,OAAA,GAAA,SAAA,CAAA,KAAA,CAAA,WAAA,CAAA,CAAA,SAAA,CAAA,CAAA,IAAA,KAAA,KAAA,CAAA,OAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,IAAA,EAAA,KAAA,IAAA,IAAA,IAAA,CAAA,KAAA,EAAA,KAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AAAA,MACR,KACA,CAAA,KAAA,GAAA,KAAA,CAAA,WAAA,EAAA,KAAA,IAAA,IAAA,KAAA,CAAA,QAAA,EAAA,KAAA,KAAA,CAAA;AACJ,MAAA,IAAA,IACE,CAAA,OAAA,EAAA;AAA6B,QAC3B,KAAC,CAAA,UACO,CAAA,GAAA,IAAQ,CAAI;AAA8C,QAC/D,IAAA,IAAA,CAAA,KAAA,EAAA;AACP,UAAA,kBAAoB,CAAA,GAAA,IAAA,CAAA;AAEpB,SAAA;AACE,QAAA,IAAA;AAEA,UAAA,gBAAgB,CAAA,GAAA,IAAA,CAAA;AACd,SAAA;AAAsB,OACxB;AAEA,MAAA,YAAc,CAAA;AACZ,KAAA,CAAA;AAAoB,IACtB,MAAA,cAAA,GAAA,CAAA,IAAA,KAAA;AAAA,MACF,MAAA,IAAA,GAAA,KAAA,CAAA,IAAA,CAAA,IAAA,EAAA,CAAA;AACA,MAAO,MAAA,KAAA,GAAA,IAAA,CAAA,IAAA,CAAA;AAAA,MACT,OAAA,SAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA,SAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,IAAA,EAAA,KAAA,IAAA,IAAA,IAAA,CAAA,KAAA,EAAA,KAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAM,MAAA,eAAa,GAAA,CAAA,KAAU,KAAA;AAC7B,MAAA,IAAA,EAAM;AACN,MACE,IAAA,CAAA,KAAA,CAAA,UAAgB,CAAA,SAAM;AAAA,QACpB;AAAmD,MACrD,IAAK,MAAA,GAAA,KAAA,CAAA,MAAA,CAAA;AAAA,MAET,IAAA,MAAA,CAAA,OAAA,KAAA,MAAA,EAAA;AAEA,QAAM,MAAA,GAAA,CAAA,EAAA,GAAA,MAAmB,CAAsB,UAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAA,CAAA;AAC7C,OAAI;AAEJ,MAAA,IAAI,cAAe,KAAA,KAAA,EAAA;AACnB,QAAI,MAAA,GAAO,iBAAoB,CAAA;AAC7B,OAAA;AAA4B,MAC9B,IAAA,MAAA,CAAA,OAAA,KAAA,IAAA;AACA,QAAI,OAAA;AACF,MAAA,MAAA,GAAA,GAAgB,MAAA,CAAA,UAAA,CAAA,QAAA,CAAA;AAAA,MAClB,MAAA,MAAA,GAAA,MAAA,CAAA,SAAA,CAAA;AACA,MAAI,IAAA,IAAA,CAAA,iBAAyB,CAAA,CAAA,QAAA;AAE7B,QAAM,OAAA;AACN,MAAA,IAAA,eAAgD,CAAA,KAAA,IAAA,MAAA,KAAA,UAAA,CAAA,KAAA,EAAA;AAEhD,QAAA,OAAS,CAAM,KAAA,GAAG,GAAE,CAAA;AAIpB,QAAA,UAAY,CAAA,KAAA,GAAiB,MAAA,CAAA;AAC3B,QAAA,IAAA,CAAA,aAAgB,EAAA;AAChB,UAAA,SAAW,EAAQ,IAAA;AACnB,UAAA,OAAoB,EAAA,KAAA,CAAA,IAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA,KAAA,CAAA,GAAA,GAAA,CAAA,GAAA,MAAA,CAAA;AAAA,SAAA,CAClB,CAAW;AAAA,OACX;AAA0D,KAAA,CAAA;AAC3D,IACH,MAAA,qBAAA,GAAA,CAAA,KAAA,KAAA;AAAA,MACF,IAAA,EAAA,CAAA;AACA,MAAM,MAAA,MAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,MAA+D,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA;AACnE,MAAM,IAAA,CAAA,MAAA,IAAU,OAA8B,KAAA,CAAA,GAAA,MAAA,CAAA,OAAA,MAAA,IAAA;AAAA,QAC5C,OAAA;AAAA,MACF,IAAA,QAAA,CAAA,MAAA,EAAA,UAAA,CAAA;AACA,QAAI,OAAA;AACJ,MAAI,MAAA,MAAA,GAAiB,MAAA,CAAA,SAAU,CAAG;AAClC,MAAA,MAAM,YAAgB,CAAA,UAAA,CAAA,QAAA,CAAA;AACtB,MAAM,MAAA,KAAA,SAAiD,CAAA,GAAA,MAAA,CAAA;AACvD,MAAM,MAAA,OAAA,QAAkB,CAAA,IAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA;AACxB,MAAA,IAAA,mBAAsB,aAAmB,EAAA;AACzC,QAAI,IAAA,wBAAkC,EAAA;AACpC,UAAI,IAAA,CAAA,iBAA0B,CAAA,KAAA,CAAA,WAAA,CAAA,EAAA,KAAA,CAAA,CAAA;AAC5B,UAAA,OAAa;AACb,SAAA;AAAA,QACF,MAAA,QAAA,GAAA,mBAAA,CAAA,KAAA,CAAA,IAAA,EAAA,KAAA,CAAA,IAAA,CAAA,IAAA,EAAA,EAAA,KAAA,EAAA,IAAA,CAAA,KAAA,EAAA,KAAA,CAAA,YAAA,CAAA,CAAA;AACA,QAAA,MAAM,QAAW,GAAA,QAAA,CAAA,MAAA,EAAA,SAAA,CAAA,GAAA,SAAA,CAAA,KAAA,CAAA,WAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,CAAA,CAAA,IAAA,EAAA,MAAA,QAAA,CAAA,IAAA,EAAA,IAAA,CAAA,CAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,CAAA,CAAA,KAAA,EAAA,MAAA,QAAA,CAAA,KAAA,EAAA,CAAA,GAAA,SAAA,CAAA,KAAA,CAAA,WAAA,CAAA,CAAA,MAAA,CAAA,CAAA,KAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA;AAAA,QAAA,IACT,CAAA,MAAA,EAAA,QAAA,CAAA,CAAA;AAAA,OACN,MAAA,SAAgB,CAAA,aAAA,KAAA,OAAA,EAAA;AAAA,QAChB,IAAA,CAAA,KAAA,CAAA,UAAA,CAAA,SAAA,EAAA;AAAA,UACA,IAAK,CAAA,MAAA,EAAA,EAAA,OAAA,EAAA,OAAA,EAAA,OAAA,EAAA,IAAA,EAAA,CAAA,CAAA;AAAA,UACL,IAAM,CAAA,QAAA,EAAA,IAAA,CAAA,CAAA;AAAA,SACR,MAAA;AACA,UAAM,IAAA,KAAA,CAAA,kBAA4B,IAAA,KAAA,CAAA,OAC9B,EAAU;AAAmB,YAC1B,IAAA,CAAA,MAAA,EAAA,EAAA,OAAA,EAAA,KAAA,CAAA,OAAA,EAAA,OAAA,EAAA,OAAA,EAAA,CAAA,CAAA;AAAA,WAAA,MAAA;AAAA,YAAA,IAAA,CAAA,MAAA,EAAA,EAAA,OAAA,EAAA,OAAA,EAAA,OAAA,EAAA,KAAA,CAAA,OAAA,EAAA,CAAA,CAAA;AAAA,WAGC;AAA+D,UAAA,IAAA,CAAA,QAAA,EAAA,KAAA,CAAA,CAAA;AAAA,SACnE;AAEJ,OAAA,MAAK;AAAgB,QACvB,IAAA,CAAA,MAAiB,EAAA,KAAA,CAAA,CAAA;AACf,OAAI;AACF,KAAA,CAAA;AACA,IAAA,KAAA,CAAA,gBAAmB,EAAA,YAAA;AAAA,MAAA,IACd,EAAA,EAAA,EAAA,CAAA;AACL,MAAA,IAAA,CAAA,EAAA,GAAU,QAAA,CAAA,KAAA,KAAsB,IAAA,GAAA,KAAA,CAAM,GAAS,EAAA,CAAA,QAAA,CAAA,QAAA,CAAA,aAAA,CAAA,EAAA;AAC7C,QAAA,MAAA,WAAa;AAA4C,QAAA,CAAA,EACpD,GAAA,cAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,EAAA,CAAA;AACL,OAAA;AAAyD,KAC3D,CAAA,CAAA;AACA,IAAA,MAAA,CAAA;AAAoB,MACtB,KAAA;AAAA,KAAA,CACF,CAAO;AACL,IAAA,OAAA,CAAA,YAAkB,KAAA;AAAA,MACpB,OAAAC,SAAA,EAAA,EAAAC,kBAAA,CAAA,OAAA,EAAA;AAAA,QACF,IAAA,EAAA,MAAA;AAEA,QAAA,YAAA,EAAAC,KAAA,CAAA,CAAA,CAAA,CAAA,gCAAA,CAAA;AAAA,aACc,EAAAC,cAAA,CAAAD,KAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA;AAAA,QACA,OAAA,EAAA,qBAAA;AACV,QAAA,WAAa,EAAA,eAAgB;AAC3B,OAAA,EAAA;AACA,QAAAE,0BAA4B,EAAA;AAAA,UAC9B,OAAA,EAAA,UAAA;AAAA,UACF,GAAA,EAAA,QAAA;AAAA,SACF,EAAA;AAEA,WAAaJ,SAAA,CAAA,IAAA,CAAA,EAAAC,kBAAA,CAAAI,QAAA,EAAA,IAAA,EAAAC,UAAA,CAAAJ,KAAA,CAAA,IAAA,CAAA,EAAA,CAAA,GAAA,EAAA,GAAA,KAAA;AAAA,YAAA,OAAAF,SAAA,EAAA,EAAAC,kBAAA,CAAA,IAAA,EAAA,EAAA,GAAA,EAAA,EAAA;AAAA,eAAAD,SAAA,CAAA,IAAA,CAAA,EAAAC,kBAAA,CAAAI,QAAA,EAAA,IAAA,EAAAC,UAAA,CAAA,GAAA,EAAA,CAAA,IAAA,EAAA,IAAA,KAAA;AAAA,gBAAA,OAAAN,SAAA,EAAA,EAAAC,kBAAA,CAAA,IAAA,EAAA;AAAA,kBAIX,GAAA,EAAA,IAAA;AAAA,kBACD,OAAA,EAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}