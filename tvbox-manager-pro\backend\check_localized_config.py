#!/usr/bin/env python3
"""
检查本地化配置字段
"""
import sqlite3
import os

def check_localized_config():
    """检查本地化配置"""
    db_path = "data/tvbox.db"
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查接口1的配置
        cursor.execute("""
            SELECT id, name, enable_localization, localization_status, 
                   LENGTH(config_content) as config_len,
                   LENGTH(localized_config) as localized_len
            FROM interface_sources 
            WHERE id = 1
        """)
        
        result = cursor.fetchone()
        if result:
            print(f"接口ID: {result[0]}")
            print(f"接口名称: {result[1]}")
            print(f"本地化启用: {result[2]}")
            print(f"本地化状态: {result[3]}")
            print(f"原始配置长度: {result[4]}")
            print(f"本地化配置长度: {result[5]}")
            
            # 检查本地化配置内容
            cursor.execute("SELECT localized_config FROM interface_sources WHERE id = 1")
            localized_result = cursor.fetchone()
            if localized_result and localized_result[0]:
                print(f"本地化配置前100字符: {localized_result[0][:100]}...")
            else:
                print("本地化配置为空")
        else:
            print("未找到接口1")
            
    except Exception as e:
        print(f"检查失败: {str(e)}")
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    check_localized_config()
