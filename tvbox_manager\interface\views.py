#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
接口管理视图
实现接口的解密和订阅功能
"""

import json
import logging
from datetime import datetime
from flask import (
    Blueprint, render_template, request, jsonify, 
    current_app, flash, redirect, url_for
)
from flask_security import login_required, roles_required, current_user

from tvbox_manager.utils.decryptor import TVBoxDecryptor
from tvbox_manager.config.models import db, InterfaceSource, TVBoxConfig

# 创建蓝图
interface_bp = Blueprint('interface', __name__, url_prefix='/interface')

# 获取日志记录器
logger = logging.getLogger('tvbox_manager.interface')

# 初始化解密器
decryptor = TVBoxDecryptor()

@interface_bp.route('/')
@login_required
def index():
    """接口管理首页"""
    # 获取所有接口源
    sources = InterfaceSource.query.all()
    
    return render_template(
        'tabler/interface/index.html', 
        sources=sources,
        active_nav='interface'
    )

@interface_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    """添加接口源"""
    if request.method == 'POST':
        name = request.form.get('name', '')
        url = request.form.get('url', '')
        description = request.form.get('description', '')
        type = request.form.get('type', 'vod')
        update_frequency = request.form.get('update_frequency', 24)
        
        if not name or not url:
            flash('名称和URL不能为空', 'error')
            return redirect(url_for('interface.add'))
        
        # 检查URL是否已存在
        existing = InterfaceSource.query.filter_by(url=url).first()
        if existing:
            flash('该URL已存在', 'error')
            return redirect(url_for('interface.add'))
        
        # 创建接口源
        source = InterfaceSource(
            name=name,
            url=url,
            description=description,
            type=type,
            update_frequency=update_frequency
        )
        
        # 尝试解析接口
        try:
            content, method = decryptor.decrypt_config_url(url)
            source.decrypt_method = method
            source.last_update = datetime.now()
            
            # 保存接口
            db.session.add(source)
            db.session.commit()
            
            # 尝试解析并创建配置
            if content and decryptor._is_json(content):
                config = TVBoxConfig(
                    name=f"{name} 配置",
                    content=content,
                    source_id=source.id
                )
                config.update_stats()
                
                db.session.add(config)
                db.session.commit()
                
                flash(f'接口添加成功，并创建了配置', 'success')
            else:
                flash(f'接口添加成功，但未能创建配置', 'warning')
            
            return redirect(url_for('interface.index'))
        except Exception as e:
            db.session.rollback()
            logger.error(f"添加接口失败: {str(e)}")
            flash(f'添加接口失败: {str(e)}', 'error')
            return redirect(url_for('interface.add'))
    
    return render_template('tabler/interface/add.html', active_nav='interface')

@interface_bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit(id):
    """编辑接口源"""
    source = InterfaceSource.query.get_or_404(id)
    
    if request.method == 'POST':
        name = request.form.get('name', '')
        url = request.form.get('url', '')
        description = request.form.get('description', '')
        type = request.form.get('type', 'vod')
        update_frequency = request.form.get('update_frequency', 24)
        status = request.form.get('status', 'off') == 'on'
        
        if not name or not url:
            flash('名称和URL不能为空', 'error')
            return redirect(url_for('interface.edit', id=id))
        
        # 更新接口源
        source.name = name
        source.url = url
        source.description = description
        source.type = type
        source.update_frequency = update_frequency
        source.status = status
        
        # 保存更新
        try:
            db.session.commit()
            flash('接口更新成功', 'success')
            return redirect(url_for('interface.index'))
        except Exception as e:
            db.session.rollback()
            logger.error(f"更新接口失败: {str(e)}")
            flash(f'更新接口失败: {str(e)}', 'error')
            return redirect(url_for('interface.edit', id=id))
    
    return render_template('tabler/interface/edit.html', source=source, active_nav='interface')

@interface_bp.route('/delete/<int:id>')
@login_required
@roles_required('admin')
def delete(id):
    """删除接口源"""
    source = InterfaceSource.query.get_or_404(id)
    
    try:
        # 删除关联的配置
        TVBoxConfig.query.filter_by(source_id=id).delete()
        
        # 删除接口源
        db.session.delete(source)
        db.session.commit()
        
        flash('接口删除成功', 'success')
    except Exception as e:
        db.session.rollback()
        logger.error(f"删除接口失败: {str(e)}")
        flash(f'删除接口失败: {str(e)}', 'error')
    
    return redirect(url_for('interface.index'))

@interface_bp.route('/refresh/<int:id>')
@login_required
def refresh(id):
    """刷新接口源"""
    source = InterfaceSource.query.get_or_404(id)
    
    try:
        # 解析接口
        content, method = decryptor.decrypt_config_url(source.url)
        source.decrypt_method = method
        source.last_update = datetime.now()
        
        # 更新或创建配置
        if content and decryptor._is_json(content):
            config = TVBoxConfig.query.filter_by(source_id=id).first()
            
            if config:
                config.content = content
                config.update_stats()
            else:
                config = TVBoxConfig(
                    name=f"{source.name} 配置",
                    content=content,
                    source_id=id
                )
                config.update_stats()
                db.session.add(config)
            
            db.session.commit()
            flash('接口刷新成功', 'success')
        else:
            flash('接口解析失败，未能获取有效配置', 'error')
    except Exception as e:
        db.session.rollback()
        logger.error(f"刷新接口失败: {str(e)}")
        flash(f'刷新接口失败: {str(e)}', 'error')
    
    return redirect(url_for('interface.index'))

@interface_bp.route('/decrypt', methods=['GET', 'POST'])
@login_required
def decrypt():
    """接口解密页面"""
    result = None
    method = None
    
    if request.method == 'POST':
        url = request.form.get('url', '')
        
        if url:
            try:
                content, method = decryptor.decrypt_config_url(url)
                
                if decryptor._is_json(content):
                    # 如果是JSON，格式化显示
                    try:
                        parsed = json.loads(content)
                        result = json.dumps(parsed, indent=2, ensure_ascii=False)
                        
                        # 分析配置
                        config_info = decryptor.parse_tvbox_config_content(content)
                        sites_count = len(config_info.get('sites', []))
                        lives_count = sum(len(group.get('channels', [])) for group in config_info.get('lives', []))
                        parses_count = len(config_info.get('parses', []))
                        
                        flash(f'解密成功 [方法: {method}] 站点: {sites_count}, 直播: {lives_count}, 解析: {parses_count}', 'success')
                    except json.JSONDecodeError as e:
                        result = content
                        flash(f'解密成功 [方法: {method}]，但JSON格式有误: {str(e)}', 'warning')
                else:
                    result = content
                    flash(f'解密成功 [方法: {method}]，但不是JSON格式', 'warning')
            except Exception as e:
                logger.error(f"解密失败: {str(e)}")
                flash(f'解密失败: {str(e)}', 'error')
        else:
            flash('请输入URL', 'warning')
    
    return render_template('tabler/interface/decrypt.html', result=result, method=method, active_nav='decrypt')

@interface_bp.route('/api/decrypt', methods=['POST'])
def api_decrypt():
    """接口解密API"""
    data = request.get_json()
    url = data.get('url', '')
    
    if not url:
        return jsonify({'error': '缺少URL参数'}), 400
    
    try:
        content, method = decryptor.decrypt_config_url(url)
        
        # 检查是否是有效JSON
        if decryptor._is_json(content):
            try:
                parsed = json.loads(content)
                # 分析配置
                config_info = decryptor.parse_tvbox_config_content(content)
                
                return jsonify({
                    'success': True,
                    'method': method,
                    'content': content,
                    'stats': {
                        'sites': len(config_info.get('sites', [])),
                        'lives': sum(len(group.get('channels', [])) for group in config_info.get('lives', [])),
                        'parses': len(config_info.get('parses', [])),
                        'spider': config_info.get('spider', ''),
                        'wallpaper': config_info.get('wallpaper', ''),
                    }
                })
            except json.JSONDecodeError:
                return jsonify({
                    'success': True,
                    'method': method,
                    'content': content,
                    'warning': 'JSON格式有误'
                })
        else:
            return jsonify({
                'success': True,
                'method': method,
                'content': content,
                'warning': '非JSON格式'
            })
    except Exception as e:
        logger.error(f"API解密失败: {str(e)}")
        return jsonify({'error': f'解密失败: {str(e)}'}), 500 