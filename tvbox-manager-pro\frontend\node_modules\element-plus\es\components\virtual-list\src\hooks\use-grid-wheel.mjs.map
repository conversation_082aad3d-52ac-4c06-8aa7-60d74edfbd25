{"version": 3, "file": "use-grid-wheel.mjs", "sources": ["../../../../../../../packages/components/virtual-list/src/hooks/use-grid-wheel.ts"], "sourcesContent": ["import { cAF, rAF } from '@element-plus/utils'\n\nimport type { ComputedRef } from 'vue'\n\ninterface GridWheelState {\n  atXStartEdge: ComputedRef<boolean>\n  atXEndEdge: ComputedRef<boolean>\n  atYStartEdge: ComputedRef<boolean>\n  atYEndEdge: ComputedRef<boolean>\n}\n\ntype GridWheelHandler = (x: number, y: number) => void\n\nexport const useGridWheel = (\n  { atXEndEdge, atXStartEdge, atYEndEdge, atYStartEdge }: GridWheelState,\n  onWheelDelta: GridWheelHandler\n) => {\n  let frameHandle: number | null = null\n  let xOffset = 0\n  let yOffset = 0\n\n  const hasReachedEdge = (x: number, y: number) => {\n    const xEdgeReached =\n      (x <= 0 && atXStartEdge.value) || (x >= 0 && atXEndEdge.value)\n    const yEdgeReached =\n      (y <= 0 && atYStartEdge.value) || (y >= 0 && atYEndEdge.value)\n    return xEdgeReached && yEdgeReached\n  }\n\n  const onWheel = (e: WheelEvent) => {\n    cAF(frameHandle!)\n\n    let x = e.deltaX\n    let y = e.deltaY\n    // Simulate native behavior when using touch pad/track pad for wheeling.\n    if (Math.abs(x) > Math.abs(y)) {\n      y = 0\n    } else {\n      x = 0\n    }\n\n    // Special case for windows machine with shift key + wheel scrolling\n    if (e.shiftKey && y !== 0) {\n      x = y\n      y = 0\n    }\n\n    if (\n      hasReachedEdge(xOffset, yOffset) &&\n      hasReachedEdge(xOffset + x, yOffset + y)\n    )\n      return\n\n    xOffset += x\n    yOffset += y\n\n    e.preventDefault()\n\n    frameHandle = rAF(() => {\n      onWheelDelta(xOffset, yOffset)\n      xOffset = 0\n      yOffset = 0\n    })\n  }\n\n  return {\n    hasReachedEdge,\n    onWheel,\n  }\n}\n"], "names": [], "mappings": ";;AACY,MAAC,YAAY,GAAG,CAAC,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,EAAE,YAAY,KAAK;AACtG,EAAE,IAAI,WAAW,GAAG,IAAI,CAAC;AACzB,EAAE,IAAI,OAAO,GAAG,CAAC,CAAC;AAClB,EAAE,IAAI,OAAO,GAAG,CAAC,CAAC;AAClB,EAAE,MAAM,cAAc,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK;AACnC,IAAI,MAAM,YAAY,GAAG,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC;AACpF,IAAI,MAAM,YAAY,GAAG,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC;AACpF,IAAI,OAAO,YAAY,IAAI,YAAY,CAAC;AACxC,GAAG,CAAC;AACJ,EAAE,MAAM,OAAO,GAAG,CAAC,CAAC,KAAK;AACzB,IAAI,GAAG,CAAC,WAAW,CAAC,CAAC;AACrB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;AACrB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;AACrB,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;AACnC,MAAM,CAAC,GAAG,CAAC,CAAC;AACZ,KAAK,MAAM;AACX,MAAM,CAAC,GAAG,CAAC,CAAC;AACZ,KAAK;AACL,IAAI,IAAI,CAAC,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE;AAC/B,MAAM,CAAC,GAAG,CAAC,CAAC;AACZ,MAAM,CAAC,GAAG,CAAC,CAAC;AACZ,KAAK;AACL,IAAI,IAAI,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,cAAc,CAAC,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC;AACpF,MAAM,OAAO;AACb,IAAI,OAAO,IAAI,CAAC,CAAC;AACjB,IAAI,OAAO,IAAI,CAAC,CAAC;AACjB,IAAI,CAAC,CAAC,cAAc,EAAE,CAAC;AACvB,IAAI,WAAW,GAAG,GAAG,CAAC,MAAM;AAC5B,MAAM,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AACrC,MAAM,OAAO,GAAG,CAAC,CAAC;AAClB,MAAM,OAAO,GAAG,CAAC,CAAC;AAClB,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,cAAc;AAClB,IAAI,OAAO;AACX,GAAG,CAAC;AACJ;;;;"}