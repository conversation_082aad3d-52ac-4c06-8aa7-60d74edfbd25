<template>
  <el-card class="interface-card" :class="{ 'error': interface.status === 'error' }">
    <!-- 卡片头部 -->
    <template #header>
      <div class="card-header">
        <div class="header-left">
          <h4 class="interface-name">{{ interface.name }}</h4>
          <div class="interface-tags">
            <el-tag v-if="interface.category" size="small">{{ interface.category }}</el-tag>
            <el-tag v-if="interface.is_public" size="small" type="success">公开</el-tag>
          </div>
        </div>
        <div class="header-right">
          <el-tag :type="getStatusType(interface.status)" size="small">
            {{ getStatusText(interface.status) }}
          </el-tag>
        </div>
      </div>
    </template>
    
    <!-- 卡片内容 -->
    <div class="card-content">
      <!-- 接口地址 -->
      <div class="interface-url">
        <el-text class="url-text" truncated>{{ interface.url }}</el-text>
        <el-button
          size="small"
          text
          icon="CopyDocument"
          @click="copyUrl"
        />
      </div>
      
      <!-- 描述 -->
      <div v-if="interface.description" class="interface-description">
        <el-text size="small" type="info">{{ interface.description }}</el-text>
      </div>
      
      <!-- 统计信息 -->
      <div class="stats-section">
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ interface.sites_count }}</div>
            <div class="stat-label">站点</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ interface.lives_count }}</div>
            <div class="stat-label">直播</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ interface.parses_count }}</div>
            <div class="stat-label">解析</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ interface.success_rate }}%</div>
            <div class="stat-label">成功率</div>
          </div>
        </div>
        
        <!-- 成功率进度条 -->
        <div class="success-rate">
          <el-progress
            :percentage="interface.success_rate"
            :color="getProgressColor(interface.success_rate)"
            :show-text="false"
            :stroke-width="4"
          />
        </div>
      </div>
      
      <!-- 时间信息 -->
      <div class="time-info">
        <div class="time-item">
          <el-icon><Clock /></el-icon>
          <span>创建: {{ formatDate(interface.created_at) }}</span>
        </div>
        <div v-if="interface.last_success_at" class="time-item">
          <el-icon><Check /></el-icon>
          <span>最后成功: {{ formatDate(interface.last_success_at) }}</span>
        </div>
        <div v-if="interface.last_error_at" class="time-item error">
          <el-icon><Close /></el-icon>
          <span>最后错误: {{ formatDate(interface.last_error_at) }}</span>
        </div>
      </div>
      
      <!-- 错误信息 -->
      <div v-if="interface.last_error_message" class="error-message">
        <el-alert
          :title="interface.last_error_message"
          type="error"
          :closable="false"
          show-icon
        />
      </div>
    </div>
    
    <!-- 卡片底部操作 -->
    <template #footer>
      <div class="card-actions">
        <el-button-group>
          <el-button size="small" icon="View" @click="$emit('view', interface)">
            详情
          </el-button>
          <el-button size="small" icon="Star" @click="$emit('subscribe', interface)">
            订阅
          </el-button>
        </el-button-group>
        
        <el-dropdown @command="handleCommand">
          <el-button size="small" icon="More" />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="edit" icon="Edit">编辑</el-dropdown-item>
              <el-dropdown-item command="copy" icon="CopyDocument">复制</el-dropdown-item>
              <el-dropdown-item command="delete" icon="Delete" divided>删除</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </template>
  </el-card>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { Clock, Check, Close } from '@element-plus/icons-vue'
import { formatDate } from '@/utils/date'

const props = defineProps({
  interface: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['view', 'subscribe', 'edit', 'delete'])

// 方法
const getStatusType = (status) => {
  const statusMap = {
    online: 'success',
    offline: 'info',
    error: 'danger',
    unknown: 'warning'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    online: '在线',
    offline: '离线',
    error: '错误',
    unknown: '未知'
  }
  return statusMap[status] || '未知'
}

const getProgressColor = (percentage) => {
  if (percentage >= 80) return '#67c23a'
  if (percentage >= 60) return '#e6a23c'
  return '#f56c6c'
}

const copyUrl = async () => {
  try {
    await navigator.clipboard.writeText(props.interface.url)
    ElMessage.success('URL已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const handleCommand = (command) => {
  switch (command) {
    case 'edit':
      emit('edit', props.interface)
      break
    case 'copy':
      // 复制整个接口配置
      ElMessage.info('复制功能开发中')
      break
    case 'delete':
      emit('delete', props.interface)
      break
  }
}
</script>

<style lang="scss" scoped>
.interface-card {
  height: 100%;
  margin-bottom: 20px;
  transition: all 0.3s;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
  
  &.error {
    border-color: var(--el-color-danger-light-7);
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    
    .header-left {
      flex: 1;
      
      .interface-name {
        margin: 0 0 8px 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        line-height: 1.2;
      }
      
      .interface-tags {
        display: flex;
        gap: 4px;
        flex-wrap: wrap;
      }
    }
    
    .header-right {
      margin-left: 12px;
    }
  }
  
  .card-content {
    .interface-url {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
      padding: 8px;
      background: var(--el-bg-color-page);
      border-radius: 4px;
      
      .url-text {
        flex: 1;
        font-family: monospace;
        font-size: 12px;
        color: var(--el-text-color-regular);
      }
    }
    
    .interface-description {
      margin-bottom: 16px;
      padding: 8px;
      background: var(--el-color-info-light-9);
      border-radius: 4px;
      border-left: 3px solid var(--el-color-info);
    }
    
    .stats-section {
      margin-bottom: 16px;
      
      .stats-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 8px;
        margin-bottom: 8px;
        
        .stat-item {
          text-align: center;
          
          .stat-value {
            font-size: 18px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            line-height: 1;
          }
          
          .stat-label {
            font-size: 12px;
            color: var(--el-text-color-regular);
            margin-top: 2px;
          }
        }
      }
    }
    
    .time-info {
      margin-bottom: 12px;
      
      .time-item {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        color: var(--el-text-color-regular);
        margin-bottom: 4px;
        
        &.error {
          color: var(--el-color-danger);
        }
        
        .el-icon {
          font-size: 14px;
        }
      }
    }
    
    .error-message {
      margin-bottom: 12px;
      
      :deep(.el-alert) {
        padding: 8px 12px;
        
        .el-alert__title {
          font-size: 12px;
          line-height: 1.4;
        }
      }
    }
  }
  
  .card-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .interface-card {
    .card-header {
      flex-direction: column;
      gap: 8px;
      
      .header-right {
        margin-left: 0;
        align-self: flex-start;
      }
    }
    
    .stats-section .stats-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
    }
  }
}
</style>
