<template>
  <div class="create-interface-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button icon="ArrowLeft" @click="$router.back()">返回</el-button>
        <div class="header-info">
          <h2>添加接口源</h2>
          <p>添加新的TVBox接口源到系统中</p>
        </div>
      </div>
    </div>
    
    <el-row :gutter="20">
      <!-- 表单区域 -->
      <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
        <el-card class="form-card">
          <template #header>
            <span>基本信息</span>
          </template>
          
          <el-form
            ref="formRef"
            :model="form"
            :rules="rules"
            label-width="100px"
            size="large"
          >
            <el-form-item label="接口名称" prop="name">
              <el-input
                v-model="form.name"
                placeholder="请输入接口名称"
                clearable
              />
            </el-form-item>
            
            <el-form-item label="接口地址" prop="url">
              <el-input
                v-model="form.url"
                placeholder="请输入接口URL地址"
                clearable
              >
                <template #append>
                  <el-button
                    :loading="testLoading"
                    @click="testInterface"
                  >
                    测试
                  </el-button>
                </template>
              </el-input>
            </el-form-item>
            
            <el-form-item label="接口分类" prop="category">
              <el-select
                v-model="form.category"
                placeholder="请选择或输入分类"
                filterable
                allow-create
                clearable
              >
                <el-option
                  v-for="category in categories"
                  :key="category"
                  :label="category"
                  :value="category"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="标签">
              <el-input
                v-model="form.tags"
                placeholder="请输入标签，多个标签用逗号分隔"
                clearable
              />
            </el-form-item>
            
            <el-form-item label="描述">
              <el-input
                v-model="form.description"
                type="textarea"
                :rows="3"
                placeholder="请输入接口描述"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
            
            <el-form-item label="更新间隔">
              <el-select v-model="form.update_interval" placeholder="请选择更新间隔">
                <el-option label="1小时" :value="3600" />
                <el-option label="6小时" :value="21600" />
                <el-option label="12小时" :value="43200" />
                <el-option label="24小时" :value="86400" />
                <el-option label="手动更新" :value="0" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="公开设置">
              <el-switch
                v-model="form.is_public"
                active-text="公开"
                inactive-text="私有"
              />
              <div class="form-tip">
                公开的接口源可以被其他用户查看和订阅
              </div>
            </el-form-item>

            <el-form-item label="本地化设置">
              <el-switch
                v-model="form.enable_localization"
                active-text="启用"
                inactive-text="禁用"
              />
              <div class="form-tip">
                启用本地化将自动下载配置中的远程文件到本地，提高访问速度
              </div>
            </el-form-item>
            
            <el-form-item>
              <el-button
                type="primary"
                :loading="submitLoading"
                @click="handleSubmit"
              >
                {{ submitLoading ? '创建中...' : '创建接口' }}
              </el-button>
              <el-button @click="resetForm">重置</el-button>
              <el-button @click="$router.back()">取消</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      
      <!-- 预览区域 -->
      <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
        <el-card class="preview-card">
          <template #header>
            <span>接口预览</span>
          </template>
          
          <div v-if="!testResult" class="preview-placeholder">
            <el-empty description="请先测试接口以查看预览" />
          </div>
          
          <div v-else class="preview-content">
            <!-- 测试结果 -->
            <div class="test-result">
              <el-alert
                :title="testResult.success ? '接口测试成功' : '接口测试失败'"
                :type="testResult.success ? 'success' : 'error'"
                :closable="false"
                show-icon
              />
            </div>
            
            <!-- 解密信息 -->
            <div v-if="testResult.success" class="decrypt-info">
              <h4>解密信息</h4>
              <el-descriptions :column="1" size="small" border>
                <el-descriptions-item label="解密方法">
                  {{ testResult.method || '无需解密' }}
                </el-descriptions-item>
                <el-descriptions-item label="内容大小">
                  {{ getContentSize(testResult.content) }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
            
            <!-- 配置统计 -->
            <div v-if="configStats" class="config-stats">
              <h4>配置统计</h4>
              <div class="stats-grid">
                <div class="stat-item">
                  <div class="stat-value">{{ configStats.sites }}</div>
                  <div class="stat-label">站点数</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ configStats.lives }}</div>
                  <div class="stat-label">直播源</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ configStats.parses }}</div>
                  <div class="stat-label">解析器</div>
                </div>
              </div>
            </div>
            
            <!-- 配置内容预览 -->
            <div v-if="testResult.content" class="content-preview">
              <h4>配置内容</h4>
              <el-input
                :model-value="testResult.content"
                type="textarea"
                :rows="10"
                readonly
                class="content-textarea"
              />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { interfaceApi } from '@/api/interfaces'
import { ElMessage } from 'element-plus'

const router = useRouter()

// 响应式数据
const formRef = ref()
const submitLoading = ref(false)
const testLoading = ref(false)
const testResult = ref(null)
const categories = ref([])

const form = reactive({
  name: '',
  url: '',
  category: '',
  tags: '',
  description: '',
  update_interval: 3600,
  is_public: false,
  enable_localization: false
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入接口名称', trigger: 'blur' },
    { min: 2, max: 50, message: '名称长度在2到50个字符', trigger: 'blur' }
  ],
  url: [
    { required: true, message: '请输入接口地址', trigger: 'blur' },
    { type: 'url', message: '请输入正确的URL地址', trigger: 'blur' }
  ]
}

// 计算属性
const configStats = computed(() => {
  if (!testResult.value?.content) return null
  
  try {
    const config = JSON.parse(testResult.value.content)
    return {
      sites: config.sites?.length || 0,
      lives: config.lives?.reduce((total, group) => total + (group.channels?.length || 0), 0) || 0,
      parses: config.parses?.length || 0
    }
  } catch {
    return null
  }
})

// 方法
const loadCategories = async () => {
  try {
    const response = await interfaceApi.getInterfaceCategories()
    categories.value = response.data.categories
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

const testInterface = async () => {
  if (!form.url) {
    ElMessage.warning('请先输入接口地址')
    return
  }
  
  try {
    testLoading.value = true
    const response = await interfaceApi.decryptInterfaceUrl({ url: form.url })
    testResult.value = response.data
    
    if (response.data.success) {
      ElMessage.success('接口测试成功')
      
      // 如果没有填写名称，尝试从URL中提取
      if (!form.name) {
        const urlObj = new URL(form.url)
        form.name = urlObj.hostname
      }
    } else {
      ElMessage.error(`接口测试失败: ${response.data.error}`)
    }
  } catch (error) {
    ElMessage.error('接口测试失败')
    testResult.value = { success: false, error: error.message }
  } finally {
    testLoading.value = false
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitLoading.value = true

    const response = await interfaceApi.createInterfaceSource(form)

    if (response.data) {
      ElMessage.success('接口创建成功')
      if (form.enable_localization) {
        ElMessage.info('本地化任务已启动，将在后台执行')
      }
      router.push('/interfaces')
    } else {
      ElMessage.error('创建接口失败：响应数据异常')
    }

  } catch (error) {
    console.error('创建接口失败:', error)

    // 更详细的错误处理
    if (error.response) {
      const errorMsg = error.response.data?.detail || error.response.data?.message || '创建接口失败'
      ElMessage.error(errorMsg)
    } else if (error.request) {
      ElMessage.error('网络请求失败，请检查网络连接')
    } else {
      ElMessage.error('创建接口失败：' + error.message)
    }
  } finally {
    submitLoading.value = false
  }
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  testResult.value = null
}

const getContentSize = (content) => {
  if (!content) return '0 B'
  
  const bytes = new Blob([content]).size
  const sizes = ['B', 'KB', 'MB', 'GB']
  
  if (bytes === 0) return '0 B'
  
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

// 生命周期
onMounted(() => {
  loadCategories()
})
</script>

<style lang="scss" scoped>
.create-interface-page {
  .page-header {
    margin-bottom: 20px;
    
    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .header-info {
        h2 {
          margin: 0 0 4px 0;
          color: var(--el-text-color-primary);
        }
        
        p {
          margin: 0;
          color: var(--el-text-color-regular);
          font-size: 14px;
        }
      }
    }
  }
  
  .form-card {
    .form-tip {
      font-size: 12px;
      color: var(--el-text-color-regular);
      margin-top: 4px;
    }
  }
  
  .preview-card {
    .preview-placeholder {
      text-align: center;
      padding: 40px 0;
    }
    
    .preview-content {
      .test-result {
        margin-bottom: 16px;
      }
      
      .decrypt-info,
      .config-stats,
      .content-preview {
        margin-bottom: 16px;
        
        h4 {
          margin: 0 0 12px 0;
          font-size: 14px;
          color: var(--el-text-color-primary);
        }
      }
      
      .stats-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 8px;
        
        .stat-item {
          text-align: center;
          padding: 8px;
          background: var(--el-bg-color-page);
          border-radius: 4px;
          
          .stat-value {
            font-size: 18px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            line-height: 1;
          }
          
          .stat-label {
            font-size: 12px;
            color: var(--el-text-color-regular);
            margin-top: 4px;
          }
        }
      }
      
      .content-textarea {
        :deep(.el-textarea__inner) {
          font-family: monospace;
          font-size: 12px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .create-interface-page {
    .page-header .header-left {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }
  }
}
</style>
