#!/usr/bin/env python3
"""
恢复真正的原始配置
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.tvbox_decryptor import TVBoxDecryptor
import sqlite3
import json

def restore_original_config():
    """恢复真正的原始配置"""
    db_path = "data/tvbox.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取接口1的URL
        cursor.execute("SELECT id, name, url FROM interface_sources WHERE id = 1")
        result = cursor.fetchone()
        
        if result:
            interface_id, name, url = result
            print(f"接口ID: {interface_id}")
            print(f"接口名称: {name}")
            print(f"接口URL: {url}")
            
            # 使用解密器获取真实配置
            decryptor = TVBoxDecryptor()
            print(f"\n正在从URL获取真实配置...")
            
            try:
                content, method = decryptor.decrypt_config_url(url)
                print(f"解密方法: {method}")
                
                if content:
                    print(f"获取成功，内容长度: {len(content)}")
                    
                    # 尝试解析配置
                    try:
                        config_data = json.loads(content)
                        spider = config_data.get('spider', 'N/A')
                        sites = config_data.get('sites', [])
                        lives = config_data.get('lives', [])
                        parses = config_data.get('parses', [])
                        
                        print(f"\n配置解析成功:")
                        print(f"Spider: {spider}")
                        print(f"Sites数量: {len(sites)}")
                        print(f"Lives数量: {len(lives)}")
                        print(f"Parses数量: {len(parses)}")
                        
                        # 检查是否是真实配置
                        if len(sites) > 0:
                            print(f"\n前3个Sites:")
                            for i, site in enumerate(sites[:3]):
                                name = site.get('name', 'N/A')
                                api = site.get('api', 'N/A')
                                jar = site.get('jar', 'N/A')
                                ext = site.get('ext', 'N/A')
                                print(f"  Site {i+1}: {name}")
                                print(f"    API: {api}")
                                if jar != 'N/A':
                                    print(f"    JAR: {jar}")
                                if ext != 'N/A':
                                    print(f"    EXT: {ext}")
                        
                        # 统计需要下载的URL
                        download_urls = []
                        
                        # Spider
                        if spider and isinstance(spider, str) and spider.startswith('http'):
                            download_urls.append(f"Spider: {spider}")
                        
                        # Sites中的URL
                        for i, site in enumerate(sites):
                            api = site.get('api', '')
                            if isinstance(api, str) and api.startswith('http'):
                                download_urls.append(f"Site {i+1} API: {api}")
                            
                            jar = site.get('jar', '')
                            if isinstance(jar, str) and jar.startswith('http'):
                                download_urls.append(f"Site {i+1} JAR: {jar}")
                            
                            ext = site.get('ext', '')
                            if isinstance(ext, str) and ext.startswith('http'):
                                download_urls.append(f"Site {i+1} EXT: {ext}")
                        
                        # Lives中的URL
                        for i, live in enumerate(lives):
                            url_field = live.get('url', '')
                            if isinstance(url_field, str) and url_field.startswith('http'):
                                download_urls.append(f"Live {i+1}: {url_field}")
                        
                        print(f"\n需要下载的文件 ({len(download_urls)} 个):")
                        for url in download_urls:
                            print(f"  {url}")
                        
                        # 更新数据库
                        cursor.execute("""
                            UPDATE interface_sources 
                            SET config_content = ?, localized_config = NULL
                            WHERE id = 1
                        """, (content,))
                        
                        # 清理旧的本地化文件记录
                        cursor.execute("DELETE FROM localized_files WHERE interface_id = 1")
                        
                        conn.commit()
                        
                        print(f"\n✅ 已恢复真实原始配置")
                        print(f"✅ 配置长度: {len(content)}")
                        print(f"✅ 预计可下载 {len(download_urls)} 个文件")
                        
                        return True
                        
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON解析失败: {e}")
                        print(f"内容前500字符: {content[:500]}")
                        return False
                else:
                    print(f"❌ 未获取到内容")
                    return False
                    
            except Exception as e:
                print(f"❌ 获取配置失败: {e}")
                return False
        else:
            print("❌ 未找到接口1")
            return False
            
    except Exception as e:
        print(f"❌ 操作失败: {str(e)}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    restore_original_config()
