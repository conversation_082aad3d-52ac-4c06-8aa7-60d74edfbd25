#!/usr/bin/env python3
"""
更新本地化状态的脚本
"""
import sqlite3

def update_status():
    """更新本地化状态为completed"""
    conn = sqlite3.connect('data/tvbox.db')
    cursor = conn.cursor()
    
    cursor.execute('UPDATE interface_sources SET localization_status = ? WHERE id = ?', ('completed', 1))
    conn.commit()
    
    # 验证更新
    cursor.execute('SELECT localization_status FROM interface_sources WHERE id = 1')
    result = cursor.fetchone()
    
    print(f'状态已更新为: {result[0]}')
    conn.close()

if __name__ == "__main__":
    update_status()
