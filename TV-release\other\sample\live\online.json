{"lives": [{"name": "M3U", "url": "https://github.com/live.m3u"}, {"name": "TXT", "url": "https://github.com/live.txt", "epg": "https://epg.112114.xyz/?ch={name}&date={date}", "logo": "https://epg.112114.xyz/logo/{name}.png"}, {"name": "UA", "url": "https://github.com/live.txt", "epg": "https://epg.112114.xyz/?ch={name}&date={date}", "logo": "https://epg.112114.xyz/logo/{name}.png", "ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "referer": "https://github.com/"}, {"name": "Custom", "boot": false, "pass": true, "url": "https://github.com/live.txt", "epg": "https://epg.112114.xyz/?ch={name}&date={date}&serverTimeZone=Asia/Shanghai", "logo": "https://epg.112114.xyz/logo/{name}.png", "header": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Referer": "https://github.com/"}, "catchup": {"days": "7", "type": "append", "regex": "/PLTV/", "replace": "/PLTV/,/TVOD/", "source": "?playseek=${(b)yyyyMMddHHmmss}-${(e)yyyyMMddHHmmss}"}}, {"name": "JSON", "type": 1, "url": "https://github.com/live.json"}, {"name": "Spider-JS", "type": 3, "api": "https://github.com/live.js", "ext": ""}, {"name": "Spider-Python", "type": 3, "api": "https://github.com/live.py", "ext": ""}], "headers": [{"host": "gslbserv.itv.cmvideo.cn", "header": {"User-Agent": "okhttp/3.12.13"}}], "proxy": ["raw.githubusercontent.com"], "hosts": ["cache.ott.ystenlive.itv.cmvideo.cn=base-v4-free-mghy.e.cdn.chinamobile.com"], "ads": ["static-mozai.4gtv.tv"]}