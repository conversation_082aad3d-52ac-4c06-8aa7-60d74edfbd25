{% extends "layouts/admin.html" %}
{% set active_nav = 'interface' %}

{% block title %}接口管理 - TVBox Manager{% endblock %}

{% block content %}
<!-- 页面标题 -->
<div class="page-header">
    <h1 class="page-title">接口管理</h1>
    <div class="page-actions">
        <a href="{{ url_for('interface.add') }}" class="btn btn-primary">
            <i class="fas fa-plus btn-icon"></i> 添加接口
        </a>
    </div>
</div>

<!-- 接口列表卡片 -->
<div class="card">
    <div class="card-header">
        <h2 class="card-title">接口列表</h2>
    </div>
    
    {% if sources %}
    <div class="table-container">
        <table class="table">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>URL</th>
                    <th>类型</th>
                    <th>解密方法</th>
                    <th>状态</th>
                    <th>更新时间</th>
                    <th width="120">操作</th>
                </tr>
            </thead>
            <tbody>
                {% for source in sources %}
                <tr>
                    <td class="font-semibold">{{ source.name }}</td>
                    <td>
                        <div class="truncate" style="max-width: 200px;" title="{{ source.url }}">
                            {{ source.url }}
                        </div>
                    </td>
                    <td>{{ source.type }}</td>
                    <td>{{ source.decrypt_method or '未知' }}</td>
                    <td>
                        {% if source.status %}
                        <span class="status-badge active">
                            <i class="fas fa-check-circle status-icon"></i> 正常
                        </span>
                        {% else %}
                        <span class="status-badge inactive">
                            <i class="fas fa-times-circle status-icon"></i> 停用
                        </span>
                        {% endif %}
                    </td>
                    <td>
                        {% if source.last_update %}
                        {{ source.last_update.strftime('%Y-%m-%d %H:%M') }}
                        {% else %}
                        <span class="text-tertiary">从未更新</span>
                        {% endif %}
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="action-btn edit" title="编辑" onclick="location.href='{{ url_for('interface.edit', id=source.id) }}'; return false;">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="action-btn refresh" title="刷新" onclick="location.href='{{ url_for('interface.refresh', id=source.id) }}'; return false;">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                            <button class="action-btn delete" title="删除" onclick="if(confirm('确认删除此接口?')) location.href='{{ url_for('interface.delete', id=source.id) }}'; return false;">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div class="empty-state">
        <div class="empty-icon">
            <i class="fas fa-link"></i>
        </div>
        <h3 class="empty-title">暂无接口数据</h3>
        <p class="empty-description">
            您还没有添加任何接口。接口是获取视频源的重要渠道，添加后可获取丰富的视频资源。
        </p>
        <a href="{{ url_for('interface.add') }}" class="btn btn-primary">
            <i class="fas fa-plus btn-icon"></i> 添加第一个接口
        </a>
    </div>
    {% endif %}
</div>

<!-- 接口说明卡片 -->
<div class="card">
    <div class="card-header">
        <h2 class="card-title">接口说明</h2>
    </div>
    <div class="card-body">
        <p>接口是TVBox获取视频资源的主要途径，通常包含以下类型：</p>
        <ul style="list-style-type: disc; padding-left: 20px; margin-top: 10px;">
            <li>直接接口 - 无需解密可直接使用</li>
            <li>加密接口 - 需要先解密后使用</li>
            <li>本地接口 - 从本地文件加载</li>
        </ul>
        
        <div style="margin-top: 16px;">
            <h4 style="margin-bottom: 8px; font-weight: 600;">接口状态说明:</h4>
            <div style="display: flex; gap: 16px;">
                <div>
                    <span class="status-badge active">
                        <i class="fas fa-check-circle status-icon"></i> 正常
                    </span>
                    - 接口可正常访问和使用
                </div>
                <div>
                    <span class="status-badge inactive">
                        <i class="fas fa-times-circle status-icon"></i> 停用
                    </span>
                    - 接口暂时无法访问或已停用
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 
            <i class="fas fa-plus btn-icon"></i> 添加第一个接口
        </a>
    </div>
    {% endif %}
</div>

<!-- 接口说明卡片 -->
<div class="card">
    <div class="card-header">
        <h2 class="card-title">接口说明</h2>
    </div>
    <div class="card-body">
        <p>接口是TVBox获取视频资源的主要途径，通常包含以下类型：</p>
        <ul style="list-style-type: disc; padding-left: 20px; margin-top: 10px;">
            <li>直接接口 - 无需解密可直接使用</li>
            <li>加密接口 - 需要先解密后使用</li>
            <li>本地接口 - 从本地文件加载</li>
        </ul>
        
        <div style="margin-top: 16px;">
            <h4 style="margin-bottom: 8px; font-weight: 600;">接口状态说明:</h4>
            <div style="display: flex; gap: 16px;">
                <div>
                    <span class="status-badge active">
                        <i class="fas fa-check-circle status-icon"></i> 正常
                    </span>
                    - 接口可正常访问和使用
                </div>
                <div>
                    <span class="status-badge inactive">
                        <i class="fas fa-times-circle status-icon"></i> 停用
                    </span>
                    - 接口暂时无法访问或已停用
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 