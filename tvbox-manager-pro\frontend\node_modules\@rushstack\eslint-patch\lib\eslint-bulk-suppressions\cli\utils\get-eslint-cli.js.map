{"version": 3, "file": "get-eslint-cli.js", "sourceRoot": "", "sources": ["../../../../src/eslint-bulk-suppressions/cli/utils/get-eslint-cli.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;AAkB3D,0DA8CC;AA9DD,gDAAwB;AACxB,+CAA4E;AAE5E,+EAA+E;AAC/E,mFAAmF;AACnF,MAAM,eAAe,GAAgB,IAAI,GAAG,CAAC;IAC3C,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;CACT,CAAC,CAAC;AAEH,SAAgB,uBAAuB,CAAC,WAAmB;IACzD,6GAA6G;IAC7G,gCAAgC;IAChC,IAAI,CAAC;QACH,MAAM,kBAAkB,GAAW,OAAO,CAAC,OAAO,CAAC,qDAAyC,EAAE;YAC5F,KAAK,EAAE,CAAC,WAAW,CAAC;SACrB,CAAC,CAAC;QACH,MAAM,eAAe,GAAW,cAAI,CAAC,OAAO,CAAC,cAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC;QAC/E,MAAM,EAAE,OAAO,EAAE,kBAAkB,EAAE,GAAG,OAAO,CAAC,GAAG,eAAe,eAAe,CAAC,CAAC;QAEnF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,kBAAkB,CAAC,EAAE,CAAC;YAC7C,OAAO,CAAC,IAAI,CACV,wGAAwG,CACzG,CAAC;QACJ,CAAC;QAED,OAAO,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,CAAC;IAClD,CAAC;IAAC,OAAO,EAAE,EAAE,CAAC;QACZ,IAAI,CAAC;YACH,MAAM,EACJ,YAAY,EACZ,eAAe,EAChB,GAGG,OAAO,CAAC,GAAG,WAAW,eAAe,CAAC,CAAC;YAE3C,IAAI,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,MAAM,EAAE,CAAC;gBAC5B,MAAM,IAAI,KAAK,CACb,mFAAmF;oBACjF,iDAAiD,CACpD,CAAC;YACJ,CAAC;iBAAM,IAAI,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,MAAM,EAAE,CAAC;gBAChC,MAAM,IAAI,KAAK,CACb,+EAA+E;oBAC7E,iDAAiD,CACpD,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,kFAAkF,CAAC,CAAC;YACtG,CAAC;QACH,CAAC;QAAC,OAAO,EAAE,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CACb,uGAAuG,CACxG,CAAC;QACJ,CAAC;IACH,CAAC;AACH,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICEN<PERSON> in the project root for license information.\n\nimport path from 'path';\nimport { BULK_SUPPRESSIONS_CLI_ESLINT_PACKAGE_NAME } from '../../constants';\n\n// When this list is updated, update the `eslint-bulk-suppressions-newest-test`\n// and/or the `eslint-bulk-suppressions-newest-test` projects' eslint dependencies.\nconst TESTED_VERSIONS: Set<string> = new Set([\n  '8.6.0',\n  '8.7.0',\n  '8.21.0',\n  '8.22.0',\n  '8.23.0',\n  '8.23.1',\n  '8.57.0',\n  '9.25.1'\n]);\n\nexport function getEslintPathAndVersion(packagePath: string): [string, string] {\n  // Try to find a local ESLint installation, the one that should be listed as a dev dependency in package.json\n  // and installed in node_modules\n  try {\n    const localEslintApiPath: string = require.resolve(BULK_SUPPRESSIONS_CLI_ESLINT_PACKAGE_NAME, {\n      paths: [packagePath]\n    });\n    const localEslintPath: string = path.dirname(path.dirname(localEslintApiPath));\n    const { version: localEslintVersion } = require(`${localEslintPath}/package.json`);\n\n    if (!TESTED_VERSIONS.has(localEslintVersion)) {\n      console.warn(\n        '@rushstack/eslint-bulk: Be careful, the installed ESLint version has not been tested with eslint-bulk.'\n      );\n    }\n\n    return [localEslintApiPath, localEslintVersion];\n  } catch (e1) {\n    try {\n      const {\n        dependencies,\n        devDependencies\n      }: {\n        dependencies: Record<string, string> | undefined;\n        devDependencies: Record<string, string> | undefined;\n      } = require(`${packagePath}/package.json`);\n\n      if (devDependencies?.eslint) {\n        throw new Error(\n          '@rushstack/eslint-bulk: eslint is specified as a dev dependency in package.json, ' +\n            'but eslint-bulk cannot find it in node_modules.'\n        );\n      } else if (dependencies?.eslint) {\n        throw new Error(\n          '@rushstack/eslint-bulk: eslint is specified as a dependency in package.json, ' +\n            'but eslint-bulk cannot find it in node_modules.'\n        );\n      } else {\n        throw new Error('@rushstack/eslint-bulk: eslint is not specified as a dependency in package.json.');\n      }\n    } catch (e2) {\n      throw new Error(\n        \"@rushstack/eslint-bulk: This command must be run in the same folder as a project's package.json file.\"\n      );\n    }\n  }\n}\n"]}