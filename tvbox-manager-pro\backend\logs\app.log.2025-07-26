2025-07-26 11:37:47,469 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753501067463
2025-07-26 11:37:47,499 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753501067463 状态码: 200 耗时: 0.031s
2025-07-26 11:37:47,806 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753501067794
2025-07-26 11:37:47,808 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753501067794
2025-07-26 11:37:47,810 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753501067794 状态码: 307 耗时: 0.004s
2025-07-26 11:37:47,825 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753501067794 状态码: 200 耗时: 0.017s
2025-07-26 11:37:48,178 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753501067794
2025-07-26 11:37:48,198 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753501067794 状态码: 200 耗时: 0.019s
2025-07-26 11:37:50,568 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753501070564
2025-07-26 11:37:50,574 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753501070564 状态码: 200 耗时: 0.006s
2025-07-26 11:37:52,484 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753501072477
2025-07-26 11:37:52,485 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753501072477
2025-07-26 11:37:52,486 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753501072477 状态码: 307 耗时: 0.003s
2025-07-26 11:37:52,492 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753501072477 状态码: 200 耗时: 0.007s
2025-07-26 11:37:52,510 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753501072477
2025-07-26 11:37:52,516 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753501072477 状态码: 200 耗时: 0.006s
2025-07-26 11:37:54,031 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 11:37:54,033 [ERROR] app.api.v1.interfaces:474 - 解析接口源异常: name 'interface_service' is not defined
2025-07-26 11:37:54,033 [ERROR] app.core.database:75 - 数据库会话错误: 500: 解析接口源失败
2025-07-26 11:37:54,034 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 500 耗时: 0.003s
2025-07-26 11:37:55,461 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 11:37:55,461 [ERROR] app.api.v1.interfaces:474 - 解析接口源异常: name 'interface_service' is not defined
2025-07-26 11:37:55,461 [ERROR] app.core.database:75 - 数据库会话错误: 500: 解析接口源失败
2025-07-26 11:37:55,463 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 500 耗时: 0.003s
2025-07-26 11:37:55,651 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 11:37:55,653 [ERROR] app.api.v1.interfaces:474 - 解析接口源异常: name 'interface_service' is not defined
2025-07-26 11:37:55,654 [ERROR] app.core.database:75 - 数据库会话错误: 500: 解析接口源失败
2025-07-26 11:37:55,654 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 500 耗时: 0.003s
2025-07-26 11:37:55,728 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 11:37:55,730 [ERROR] app.api.v1.interfaces:474 - 解析接口源异常: name 'interface_service' is not defined
2025-07-26 11:37:55,730 [ERROR] app.core.database:75 - 数据库会话错误: 500: 解析接口源失败
2025-07-26 11:37:55,731 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 500 耗时: 0.003s
2025-07-26 11:37:55,837 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 11:37:55,839 [ERROR] app.api.v1.interfaces:474 - 解析接口源异常: name 'interface_service' is not defined
2025-07-26 11:37:55,839 [ERROR] app.core.database:75 - 数据库会话错误: 500: 解析接口源失败
2025-07-26 11:37:55,841 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 500 耗时: 0.004s
2025-07-26 11:37:56,041 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 11:37:56,044 [ERROR] app.api.v1.interfaces:474 - 解析接口源异常: name 'interface_service' is not defined
2025-07-26 11:37:56,044 [ERROR] app.core.database:75 - 数据库会话错误: 500: 解析接口源失败
2025-07-26 11:37:56,045 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 500 耗时: 0.004s
2025-07-26 11:37:56,213 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 11:37:56,214 [ERROR] app.api.v1.interfaces:474 - 解析接口源异常: name 'interface_service' is not defined
2025-07-26 11:37:56,214 [ERROR] app.core.database:75 - 数据库会话错误: 500: 解析接口源失败
2025-07-26 11:37:56,215 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 500 耗时: 0.002s
2025-07-26 11:39:33,005 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 11:39:33,007 [ERROR] app.api.v1.interfaces:474 - 解析接口源异常: name 'interface_service' is not defined
2025-07-26 11:39:33,007 [ERROR] app.core.database:75 - 数据库会话错误: 500: 解析接口源失败
2025-07-26 11:39:33,008 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 500 耗时: 0.003s
2025-07-26 11:40:00,921 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 11:40:00,928 [INFO] root:48 - 数据库表创建完成
2025-07-26 11:40:00,978 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 11:40:00,979 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 11:40:46,944 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 11:40:46,954 [INFO] app.services.interface_service:90 - 使用模拟数据进行测试
2025-07-26 11:40:46,954 [INFO] app.core.tvbox_decryptor:600 - 验证通过: 找到2个有效站点
2025-07-26 11:40:46,968 [ERROR] app.services.interface_service:157 - 解析接口失败: 'TVBoxDecryptor' object has no attribute 'generate_config_checksum'
2025-07-26 11:40:46,971 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.027s
2025-07-26 11:40:47,198 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753501246980
2025-07-26 11:40:47,199 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753501246980 状态码: 307 耗时: 0.001s
2025-07-26 11:40:47,507 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753501246980
2025-07-26 11:40:47,524 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753501246980 状态码: 200 耗时: 0.017s
2025-07-26 11:41:29,400 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753501289396
2025-07-26 11:41:29,402 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753501289396 状态码: 307 耗时: 0.001s
2025-07-26 11:41:29,457 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753501289396
2025-07-26 11:41:29,461 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753501289396 状态码: 200 耗时: 0.003s
2025-07-26 12:12:45,138 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 12:12:45,141 [INFO] app.services.interface_service:90 - 使用模拟数据进行测试
2025-07-26 12:12:45,141 [INFO] app.core.tvbox_decryptor:600 - 验证通过: 找到2个有效站点
2025-07-26 12:12:45,147 [ERROR] app.services.interface_service:157 - 解析接口失败: 'TVBoxDecryptor' object has no attribute 'generate_config_checksum'
2025-07-26 12:12:45,148 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.010s
2025-07-26 12:12:45,389 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753503165156
2025-07-26 12:12:45,391 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753503165156 状态码: 307 耗时: 0.002s
2025-07-26 12:12:45,697 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753503165156
2025-07-26 12:12:45,701 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753503165156 状态码: 200 耗时: 0.004s
2025-07-26 12:12:50,501 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753503170496
2025-07-26 12:12:50,509 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753503170496 状态码: 200 耗时: 0.008s
2025-07-26 12:12:57,967 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 12:12:57,969 [INFO] app.services.interface_service:90 - 使用模拟数据进行测试
2025-07-26 12:12:57,969 [INFO] app.core.tvbox_decryptor:600 - 验证通过: 找到2个有效站点
2025-07-26 12:12:57,976 [ERROR] app.services.interface_service:157 - 解析接口失败: 'TVBoxDecryptor' object has no attribute 'generate_config_checksum'
2025-07-26 12:12:57,977 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.010s
2025-07-26 12:12:58,214 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753503177982
2025-07-26 12:12:58,216 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753503177982 状态码: 200 耗时: 0.002s
2025-07-26 12:13:00,351 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753503180344
2025-07-26 12:13:00,351 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753503180344 状态码: 307 耗时: 0.000s
2025-07-26 12:13:00,379 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753503180344
2025-07-26 12:13:00,383 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753503180344 状态码: 200 耗时: 0.005s
2025-07-26 12:13:00,661 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753503180344
2025-07-26 12:13:00,675 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753503180344 状态码: 200 耗时: 0.014s
2025-07-26 12:13:09,348 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753503189344
2025-07-26 12:13:09,350 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753503189344 状态码: 307 耗时: 0.002s
2025-07-26 12:13:09,350 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753503189344
2025-07-26 12:13:09,356 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753503189344 状态码: 200 耗时: 0.006s
2025-07-26 12:13:09,683 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753503189344
2025-07-26 12:13:09,688 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753503189344 状态码: 200 耗时: 0.005s
2025-07-26 12:13:12,732 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753503192413
2025-07-26 12:13:12,732 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753503192413
2025-07-26 12:13:12,733 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753503192413 状态码: 307 耗时: 0.002s
2025-07-26 12:13:12,736 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753503192413
2025-07-26 12:13:12,741 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753503192413 状态码: 200 耗时: 0.005s
2025-07-26 12:13:12,741 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753503192413 状态码: 200 耗时: 0.010s
2025-07-26 12:13:14,191 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753503194188
2025-07-26 12:13:14,195 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753503194188 状态码: 200 耗时: 0.003s
2025-07-26 12:39:47,788 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 12:39:47,794 [INFO] root:48 - 数据库表创建完成
2025-07-26 12:39:47,843 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 12:39:47,843 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 12:40:28,472 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753504828140
2025-07-26 12:40:28,472 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753504828140
2025-07-26 12:40:28,473 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753504828140 状态码: 307 耗时: 0.002s
2025-07-26 12:40:28,490 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753504828140 状态码: 200 耗时: 0.018s
2025-07-26 12:40:28,786 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753504828140
2025-07-26 12:40:28,798 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753504828140 状态码: 200 耗时: 0.011s
2025-07-26 12:41:29,237 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 12:41:29,370 [INFO] app.services.interface_service:90 - 解密结果: method=直接内容, content_length=18367
2025-07-26 12:41:29,380 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.143s
2025-07-26 12:41:29,503 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753504889387
2025-07-26 12:41:29,505 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753504889387 状态码: 307 耗时: 0.002s
2025-07-26 12:41:29,825 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753504889387
2025-07-26 12:41:29,829 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753504889387 状态码: 200 耗时: 0.005s
2025-07-26 12:41:34,484 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753504894480
2025-07-26 12:41:34,488 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753504894480 状态码: 200 耗时: 0.003s
2025-07-26 12:44:52,580 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 12:44:52,586 [INFO] root:48 - 数据库表创建完成
2025-07-26 12:44:52,633 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 12:44:52,633 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 12:46:44,551 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 12:46:44,558 [INFO] root:48 - 数据库表创建完成
2025-07-26 12:46:44,607 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 12:46:44,607 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 12:49:44,864 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 12:49:44,869 [INFO] root:48 - 数据库表创建完成
2025-07-26 12:49:44,917 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 12:49:44,918 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 12:50:03,378 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753505403366
2025-07-26 12:50:03,394 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753505403366 状态码: 200 耗时: 0.016s
2025-07-26 12:50:03,718 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753505403707
2025-07-26 12:50:03,719 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753505403707
2025-07-26 12:50:03,720 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753505403707 状态码: 307 耗时: 0.002s
2025-07-26 12:50:03,725 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753505403707 状态码: 200 耗时: 0.006s
2025-07-26 12:50:04,066 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753505403707
2025-07-26 12:50:04,080 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753505403707 状态码: 200 耗时: 0.014s
2025-07-26 12:50:41,209 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 12:50:41,389 [INFO] app.services.interface_service:90 - 解密结果: method=直接内容, content_length=18367
2025-07-26 12:50:41,398 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.189s
2025-07-26 12:50:41,725 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753505441406
2025-07-26 12:50:41,725 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753505441406 状态码: 307 耗时: 0.000s
2025-07-26 12:50:41,728 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753505441406
2025-07-26 12:50:41,730 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753505441406 状态码: 200 耗时: 0.003s
2025-07-26 12:51:11,661 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753505471656
2025-07-26 12:51:11,662 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753505471656 状态码: 307 耗时: 0.001s
2025-07-26 12:51:11,972 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753505471656
2025-07-26 12:51:11,976 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753505471656 状态码: 200 耗时: 0.004s
2025-07-26 12:55:24,768 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-26 12:55:29,082 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 12:55:29,088 [INFO] root:48 - 数据库表创建完成
2025-07-26 12:55:29,139 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 12:55:29,140 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 12:57:24,869 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 12:57:24,877 [INFO] root:48 - 数据库表创建完成
2025-07-26 12:57:24,932 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 12:57:24,932 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 12:57:39,686 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 12:57:39,825 [WARNING] app.core.tvbox_decryptor:231 - 获取到疑似图片内容，长度: 18367
2025-07-26 12:57:39,825 [INFO] app.core.tvbox_decryptor:233 - 重试获取JSON内容 (1/3)
2025-07-26 12:57:42,384 [WARNING] app.core.tvbox_decryptor:231 - 获取到疑似图片内容，长度: 18367
2025-07-26 12:57:42,384 [INFO] app.core.tvbox_decryptor:233 - 重试获取JSON内容 (2/3)
2025-07-26 12:57:45,450 [WARNING] app.core.tvbox_decryptor:231 - 获取到疑似图片内容，长度: 18367
2025-07-26 12:57:45,451 [INFO] app.core.tvbox_decryptor:233 - 重试获取JSON内容 (3/3)
2025-07-26 12:57:47,930 [WARNING] app.core.tvbox_decryptor:231 - 获取到疑似图片内容，长度: 18367
2025-07-26 12:57:47,930 [ERROR] app.core.tvbox_decryptor:236 - 重试3次后仍获取到非JSON内容
2025-07-26 12:57:47,931 [INFO] app.services.interface_service:90 - 解密结果: method=直接内容, content_length=18367
2025-07-26 12:57:47,940 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 8.254s
2025-07-26 12:57:47,950 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753505867946
2025-07-26 12:57:47,950 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753505867946 状态码: 307 耗时: 0.000s
2025-07-26 12:57:48,272 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753505867946
2025-07-26 12:57:48,280 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753505867946 状态码: 200 耗时: 0.009s
2025-07-26 13:05:04,281 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-26 13:05:08,274 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 13:05:08,280 [INFO] root:48 - 数据库表创建完成
2025-07-26 13:05:08,327 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 13:05:08,328 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 13:05:24,303 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 13:05:24,309 [INFO] root:48 - 数据库表创建完成
2025-07-26 13:05:24,435 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 13:05:24,437 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 13:05:39,237 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 13:05:39,245 [INFO] app.core.tvbox_decryptor:143 - 使用缓存的成功数据
2025-07-26 13:05:39,246 [INFO] app.services.interface_service:90 - 解密结果: method=缓存数据, content_length=14449
2025-07-26 13:05:39,246 [INFO] app.core.tvbox_decryptor:1112 - 验证通过: 找到50个有效站点
2025-07-26 13:05:39,261 [ERROR] app.services.interface_service:160 - 解析接口失败: 'TVBoxDecryptor' object has no attribute 'generate_config_checksum'
2025-07-26 13:05:39,262 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.025s
2025-07-26 13:05:39,475 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753506339267
2025-07-26 13:05:39,476 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753506339267 状态码: 307 耗时: 0.001s
2025-07-26 13:05:39,784 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753506339267
2025-07-26 13:05:39,795 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753506339267 状态码: 200 耗时: 0.011s
2025-07-26 13:06:20,168 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753506380162
2025-07-26 13:06:20,173 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753506380162 状态码: 200 耗时: 0.005s
2025-07-26 13:17:15,847 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753507035530
2025-07-26 13:17:15,848 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753507035530
2025-07-26 13:17:15,848 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753507035530 状态码: 307 耗时: 0.002s
2025-07-26 13:17:15,861 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753507035530 状态码: 200 耗时: 0.013s
2025-07-26 13:17:16,155 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753507035530
2025-07-26 13:17:16,159 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753507035530 状态码: 200 耗时: 0.003s
2025-07-26 13:17:19,112 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753507039108
2025-07-26 13:17:19,118 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753507039108 状态码: 200 耗时: 0.006s
2025-07-26 13:17:22,633 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 13:17:22,637 [INFO] app.core.tvbox_decryptor:143 - 使用缓存的成功数据
2025-07-26 13:17:22,637 [INFO] app.services.interface_service:90 - 解密结果: method=缓存数据, content_length=14449
2025-07-26 13:17:22,639 [INFO] app.core.tvbox_decryptor:1112 - 验证通过: 找到50个有效站点
2025-07-26 13:17:22,646 [ERROR] app.services.interface_service:160 - 解析接口失败: 'TVBoxDecryptor' object has no attribute 'generate_config_checksum'
2025-07-26 13:17:22,647 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.014s
2025-07-26 13:17:22,899 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753507042654
2025-07-26 13:17:22,902 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753507042654 状态码: 200 耗时: 0.004s
2025-07-26 13:18:50,095 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753507129775
2025-07-26 13:18:50,096 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753507129775
2025-07-26 13:18:50,096 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753507129775 状态码: 307 耗时: 0.001s
2025-07-26 13:18:50,102 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753507129775 状态码: 200 耗时: 0.006s
2025-07-26 13:18:50,402 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753507129775
2025-07-26 13:18:50,406 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753507129775 状态码: 200 耗时: 0.005s
2025-07-26 13:18:54,964 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/subscribe
2025-07-26 13:18:54,982 [INFO] app.services.interface_service:346 - 订阅创建成功: 用户1 订阅接口1
2025-07-26 13:18:54,983 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/subscribe 状态码: 200 耗时: 0.019s
2025-07-26 13:19:01,238 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753507141230
2025-07-26 13:19:01,239 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753507141230
2025-07-26 13:19:01,239 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753507141230 状态码: 307 耗时: 0.001s
2025-07-26 13:19:01,244 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753507141230 状态码: 200 耗时: 0.005s
2025-07-26 13:19:01,257 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753507141230
2025-07-26 13:19:01,260 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753507141230 状态码: 200 耗时: 0.003s
2025-07-26 13:19:08,028 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753507147720
2025-07-26 13:19:08,032 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753507147720 状态码: 200 耗时: 0.004s
2025-07-26 13:23:58,747 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-26 13:24:03,259 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 13:24:03,265 [INFO] root:48 - 数据库表创建完成
2025-07-26 13:24:03,313 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 13:24:03,313 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 13:24:39,544 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 13:24:39,551 [INFO] root:48 - 数据库表创建完成
2025-07-26 13:24:39,610 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 13:24:39,610 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 13:29:25,850 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753507765835
2025-07-26 13:29:25,862 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753507765835 状态码: 200 耗时: 0.012s
2025-07-26 13:29:46,468 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753507786459
2025-07-26 13:29:46,474 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753507786459 状态码: 200 耗时: 0.006s
2025-07-26 13:30:09,647 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753507809640
2025-07-26 13:30:09,650 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753507809640 状态码: 200 耗时: 0.004s
2025-07-26 13:30:31,559 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753507831552
2025-07-26 13:30:31,562 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753507831552 状态码: 200 耗时: 0.003s
2025-07-26 13:30:50,530 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753507850525
2025-07-26 13:30:50,536 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753507850525 状态码: 200 耗时: 0.006s
2025-07-26 13:31:07,430 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753507867422
2025-07-26 13:31:07,434 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753507867422 状态码: 200 耗时: 0.004s
2025-07-26 13:31:23,764 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753507883759
2025-07-26 13:31:23,786 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753507883759 状态码: 200 耗时: 0.022s
2025-07-26 13:31:24,109 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753507884099
2025-07-26 13:31:24,111 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753507884099
2025-07-26 13:31:24,112 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753507884099 状态码: 307 耗时: 0.002s
2025-07-26 13:31:24,119 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753507884099 状态码: 200 耗时: 0.008s
2025-07-26 13:31:24,460 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753507884099
2025-07-26 13:31:24,470 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753507884099 状态码: 200 耗时: 0.009s
2025-07-26 13:31:24,495 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753507884489
2025-07-26 13:31:24,496 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753507884489 状态码: 307 耗时: 0.001s
2025-07-26 13:31:24,499 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753507884489
2025-07-26 13:31:24,504 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753507884489 状态码: 200 耗时: 0.005s
2025-07-26 13:32:39,738 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753507959732
2025-07-26 13:32:39,746 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753507959732 状态码: 200 耗时: 0.008s
2025-07-26 13:32:40,079 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753507959984
2025-07-26 13:32:40,123 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753507959984 状态码: 200 耗时: 0.044s
2025-07-26 13:33:50,251 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753508030034
2025-07-26 13:33:50,256 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753508030034 状态码: 200 耗时: 0.005s
2025-07-26 13:33:50,559 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753508030549
2025-07-26 13:33:50,561 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753508030549 状态码: 307 耗时: 0.001s
2025-07-26 13:33:50,562 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753508030549
2025-07-26 13:33:50,570 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753508030549 状态码: 200 耗时: 0.006s
2025-07-26 13:33:50,920 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753508030549
2025-07-26 13:33:50,924 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753508030549 状态码: 200 耗时: 0.004s
2025-07-26 13:33:50,948 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753508030944
2025-07-26 13:33:50,949 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753508030944 状态码: 307 耗时: 0.001s
2025-07-26 13:33:50,958 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753508030944
2025-07-26 13:33:50,961 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753508030944 状态码: 200 耗时: 0.003s
2025-07-26 13:33:55,395 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753508035388
2025-07-26 13:33:55,396 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753508035388
2025-07-26 13:33:55,397 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753508035388 状态码: 307 耗时: 0.002s
2025-07-26 13:33:55,404 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753508035388 状态码: 200 耗时: 0.008s
2025-07-26 13:33:55,419 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753508035388
2025-07-26 13:33:55,424 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753508035388 状态码: 200 耗时: 0.005s
2025-07-26 13:33:55,447 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753508035442
2025-07-26 13:33:55,447 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753508035442 状态码: 307 耗时: 0.000s
2025-07-26 13:33:55,454 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753508035442
2025-07-26 13:33:55,463 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753508035442 状态码: 200 耗时: 0.009s
2025-07-26 13:34:30,537 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753508070530
2025-07-26 13:34:30,543 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753508070530 状态码: 200 耗时: 0.005s
2025-07-26 13:34:30,794 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753508070785
2025-07-26 13:34:30,796 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753508070785 状态码: 200 耗时: 0.003s
2025-07-26 13:34:58,244 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 13:34:58,380 [INFO] app.services.interface_service:90 - 解密结果: method=直接内容, content_length=18367
2025-07-26 13:34:58,390 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.146s
2025-07-26 13:34:58,399 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753508098396
2025-07-26 13:34:58,403 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753508098396 状态码: 200 耗时: 0.003s
2025-07-26 13:41:25,372 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753508485139
2025-07-26 13:41:25,378 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753508485139 状态码: 200 耗时: 0.005s
2025-07-26 13:41:25,624 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753508485615
2025-07-26 13:41:25,626 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753508485615 状态码: 307 耗时: 0.001s
2025-07-26 13:41:25,626 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753508485615
2025-07-26 13:41:25,630 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753508485615 状态码: 200 耗时: 0.004s
2025-07-26 13:41:25,961 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753508485615
2025-07-26 13:41:25,964 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753508485615 状态码: 200 耗时: 0.004s
2025-07-26 13:41:25,985 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753508485980
2025-07-26 13:41:25,987 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753508485980 状态码: 307 耗时: 0.000s
2025-07-26 13:41:25,992 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753508485980
2025-07-26 13:41:25,996 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753508485980 状态码: 200 耗时: 0.004s
2025-07-26 13:41:29,651 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753508489645
2025-07-26 13:41:29,652 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753508489645
2025-07-26 13:41:29,653 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753508489645 状态码: 307 耗时: 0.002s
2025-07-26 13:41:29,659 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753508489645 状态码: 200 耗时: 0.007s
2025-07-26 13:41:29,676 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753508489645
2025-07-26 13:41:29,679 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753508489645 状态码: 200 耗时: 0.003s
2025-07-26 13:41:29,704 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753508489699
2025-07-26 13:41:29,706 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753508489699 状态码: 307 耗时: 0.001s
2025-07-26 13:41:29,711 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753508489699
2025-07-26 13:41:29,721 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753508489699 状态码: 200 耗时: 0.010s
2025-07-26 13:43:27,677 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753508607666
2025-07-26 13:43:27,678 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753508607666 状态码: 307 耗时: 0.001s
2025-07-26 13:43:27,708 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753508607667
2025-07-26 13:43:27,714 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753508607667 状态码: 200 耗时: 0.006s
2025-07-26 13:43:28,042 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753508607666
2025-07-26 13:43:28,048 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753508607666 状态码: 200 耗时: 0.006s
2025-07-26 13:43:41,872 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753508621867
2025-07-26 13:43:41,878 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753508621867 状态码: 200 耗时: 0.006s
2025-07-26 13:43:42,154 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753508622143
2025-07-26 13:43:42,155 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753508622143
2025-07-26 13:43:42,156 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753508622143 状态码: 307 耗时: 0.002s
2025-07-26 13:43:42,161 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753508622143 状态码: 200 耗时: 0.006s
2025-07-26 13:43:42,219 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753508622143
2025-07-26 13:43:42,222 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753508622143 状态码: 200 耗时: 0.003s
2025-07-26 13:44:42,246 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753508682238
2025-07-26 13:44:42,253 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753508682238 状态码: 200 耗时: 0.006s
2025-07-26 13:50:34,734 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753509034418
2025-07-26 13:50:34,735 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753509034418 状态码: 307 耗时: 0.001s
2025-07-26 13:50:34,736 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753509034418
2025-07-26 13:50:34,741 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753509034418 状态码: 200 耗时: 0.006s
2025-07-26 13:50:35,043 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753509034418
2025-07-26 13:50:35,045 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753509034418 状态码: 200 耗时: 0.003s
2025-07-26 13:50:38,141 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753509038136
2025-07-26 13:50:38,144 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753509038136 状态码: 200 耗时: 0.003s
2025-07-26 13:50:39,548 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 13:50:39,692 [INFO] app.services.interface_service:90 - 解密结果: method=直接内容, content_length=18367
2025-07-26 13:50:39,699 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.151s
2025-07-26 13:50:39,809 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753509039705
2025-07-26 13:50:39,812 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753509039705 状态码: 200 耗时: 0.003s
2025-07-26 13:53:32,574 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 13:53:32,755 [INFO] app.services.interface_service:90 - 解密结果: method=直接内容, content_length=18367
2025-07-26 13:53:32,765 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.191s
2025-07-26 13:53:32,881 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-26 13:53:35,992 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 13:53:35,998 [INFO] root:48 - 数据库表创建完成
2025-07-26 13:53:36,100 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 13:53:36,100 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 13:53:36,110 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753509212773
2025-07-26 13:53:36,119 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753509212773 状态码: 200 耗时: 0.010s
2025-07-26 13:55:39,676 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 13:55:39,871 [INFO] app.services.interface_service:90 - 解密结果: method=直接内容, content_length=18367
2025-07-26 13:55:39,881 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.205s
2025-07-26 13:55:39,986 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-26 13:55:43,022 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 13:55:43,027 [INFO] root:48 - 数据库表创建完成
2025-07-26 13:55:43,079 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 13:55:43,079 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 13:55:43,087 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753509339886
2025-07-26 13:55:43,093 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753509339886 状态码: 200 耗时: 0.006s
2025-07-26 13:56:34,358 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 13:56:34,517 [INFO] app.services.interface_service:90 - 解密结果: method=直接内容, content_length=18367
2025-07-26 13:56:34,518 [INFO] app.services.interface_service:101 - 配置验证结果: False
2025-07-26 13:56:34,526 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.168s
2025-07-26 13:56:34,836 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753509394530
2025-07-26 13:56:34,839 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753509394530 状态码: 200 耗时: 0.003s
2025-07-26 14:02:16,986 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 14:02:16,991 [INFO] root:48 - 数据库表创建完成
2025-07-26 14:02:17,048 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 14:02:17,048 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 14:02:31,686 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753509751670
2025-07-26 14:02:31,705 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753509751670 状态码: 200 耗时: 0.019s
2025-07-26 14:02:31,989 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753509751976
2025-07-26 14:02:31,997 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753509751976 状态码: 200 耗时: 0.008s
2025-07-26 14:03:05,260 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 14:03:05,451 [INFO] app.services.interface_service:90 - 解密结果: method=直接内容, content_length=18367
2025-07-26 14:03:05,451 [INFO] app.services.interface_service:101 - 配置验证结果: False
2025-07-26 14:03:05,461 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.201s
2025-07-26 14:03:05,782 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753509785467
2025-07-26 14:03:05,784 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753509785467 状态码: 200 耗时: 0.002s
2025-07-26 14:05:45,421 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 14:05:45,428 [INFO] root:48 - 数据库表创建完成
2025-07-26 14:05:45,474 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 14:05:45,475 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 14:06:33,256 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/login
2025-07-26 14:06:33,578 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.322s
2025-07-26 14:06:33,758 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753509993748
2025-07-26 14:06:33,767 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753509993748 状态码: 200 耗时: 0.010s
2025-07-26 14:09:39,866 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753510179861
2025-07-26 14:09:39,873 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753510179861 状态码: 200 耗时: 0.007s
2025-07-26 14:09:40,030 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-26 14:09:43,944 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 14:09:43,949 [INFO] root:48 - 数据库表创建完成
2025-07-26 14:09:43,995 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 14:09:43,995 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 14:09:44,002 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753510180248
2025-07-26 14:09:44,009 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753510180248 状态码: 200 耗时: 0.008s
2025-07-26 16:49:44,060 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats?_t=1753519783990
2025-07-26 16:49:44,070 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats?_t=1753519783990 状态码: 200 耗时: 0.010s
2025-07-26 16:49:47,282 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753519787276
2025-07-26 16:49:47,282 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753519787276 状态码: 307 耗时: 0.001s
2025-07-26 16:49:47,283 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753519787276
2025-07-26 16:49:47,288 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753519787276 状态码: 200 耗时: 0.005s
2025-07-26 16:49:47,609 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753519787276
2025-07-26 16:49:47,618 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753519787276 状态码: 200 耗时: 0.008s
2025-07-26 16:49:50,804 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753519790495
2025-07-26 16:49:50,807 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753519790495 状态码: 200 耗时: 0.003s
2025-07-26 16:50:00,485 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 16:50:00,613 [INFO] app.services.interface_service:90 - 解密结果: method=直接内容, content_length=18367
2025-07-26 16:50:00,613 [INFO] app.services.interface_service:101 - 配置验证结果: False
2025-07-26 16:50:00,623 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.138s
2025-07-26 16:50:00,975 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753519800629
2025-07-26 16:50:00,977 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753519800629 状态码: 200 耗时: 0.002s
2025-07-26 16:52:01,227 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753519920920
2025-07-26 16:52:01,228 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753519920920 状态码: 307 耗时: 0.001s
2025-07-26 16:52:01,229 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753519920920
2025-07-26 16:52:01,234 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753519920920 状态码: 200 耗时: 0.005s
2025-07-26 16:52:01,538 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753519920920
2025-07-26 16:52:01,541 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753519920920 状态码: 200 耗时: 0.003s
2025-07-26 16:52:03,389 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753519923384
2025-07-26 16:52:03,392 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753519923384 状态码: 200 耗时: 0.003s
2025-07-26 16:52:06,872 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753519926563
2025-07-26 16:52:06,872 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753519926563 状态码: 307 耗时: 0.000s
2025-07-26 16:52:06,874 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753519926563
2025-07-26 16:52:06,876 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753519926563
2025-07-26 16:52:06,884 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753519926563 状态码: 200 耗时: 0.008s
2025-07-26 16:52:06,884 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753519926563 状态码: 200 耗时: 0.009s
2025-07-26 16:55:13,135 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 16:55:13,143 [INFO] root:48 - 数据库表创建完成
2025-07-26 16:55:13,188 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 16:55:13,188 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 16:56:15,820 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753520175498
2025-07-26 16:56:15,826 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753520175498 状态码: 200 耗时: 0.006s
2025-07-26 16:56:15,987 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-26 16:56:20,237 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 16:56:20,242 [INFO] root:48 - 数据库表创建完成
2025-07-26 16:56:20,288 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 16:56:20,288 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 16:56:40,159 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 16:56:40,275 [WARNING] app.core.tvbox_decryptor:1050 - 未能从混合内容中提取有效的JSON配置，返回空模板
2025-07-26 16:56:40,275 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=83
2025-07-26 16:56:40,275 [INFO] app.core.tvbox_decryptor:1108 - 配置验证通过: 包含有效字段但无站点
2025-07-26 16:56:40,276 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-26 16:56:40,293 [INFO] app.services.interface_service:153 - 接口解析成功: 123
2025-07-26 16:56:40,294 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.136s
2025-07-26 16:56:40,655 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753520200302
2025-07-26 16:56:40,659 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753520200302 状态码: 200 耗时: 0.004s
2025-07-26 16:57:55,873 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753520275866
2025-07-26 16:57:55,877 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753520275866 状态码: 200 耗时: 0.003s
2025-07-26 16:58:11,798 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753520291792
2025-07-26 16:58:11,818 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753520291792 状态码: 200 耗时: 0.019s
2025-07-26 16:58:12,093 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753520292083
2025-07-26 16:58:12,096 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753520292083 状态码: 200 耗时: 0.003s
2025-07-26 17:00:57,166 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 17:00:57,172 [INFO] root:48 - 数据库表创建完成
2025-07-26 17:00:57,214 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 17:00:57,215 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 17:01:45,744 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 17:01:45,935 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 17:01:45,935 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 17:01:45,936 [WARNING] app.core.tvbox_decryptor:1061 - 未能从混合内容中提取有效的JSON配置，返回空模板
2025-07-26 17:01:45,936 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=83
2025-07-26 17:01:45,936 [INFO] app.core.tvbox_decryptor:1119 - 配置验证通过: 包含有效字段但无站点
2025-07-26 17:01:45,937 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-26 17:01:45,951 [INFO] app.services.interface_service:153 - 接口解析成功: 123
2025-07-26 17:01:45,951 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.208s
2025-07-26 17:01:46,002 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753520505960
2025-07-26 17:01:46,005 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753520505960 状态码: 200 耗时: 0.003s
2025-07-26 17:02:31,043 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 17:02:31,050 [INFO] root:48 - 数据库表创建完成
2025-07-26 17:02:31,097 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 17:02:31,097 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 17:02:47,692 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 17:02:47,816 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 17:02:47,816 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 17:02:47,817 [WARNING] app.core.tvbox_decryptor:1061 - 未能从混合内容中提取有效的JSON配置，返回空模板
2025-07-26 17:02:47,817 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=83
2025-07-26 17:02:47,817 [INFO] app.core.tvbox_decryptor:1119 - 配置验证通过: 包含有效字段但无站点
2025-07-26 17:02:47,817 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-26 17:02:47,831 [INFO] app.services.interface_service:153 - 接口解析成功: 123
2025-07-26 17:02:47,832 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.139s
2025-07-26 17:02:47,952 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753520567837
2025-07-26 17:02:47,956 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753520567837 状态码: 200 耗时: 0.004s
2025-07-26 17:03:41,210 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 17:03:41,324 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 17:03:41,325 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 17:03:41,325 [WARNING] app.core.tvbox_decryptor:1061 - 未能从混合内容中提取有效的JSON配置，返回空模板
2025-07-26 17:03:41,325 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=83
2025-07-26 17:03:41,326 [INFO] app.core.tvbox_decryptor:1119 - 配置验证通过: 包含有效字段但无站点
2025-07-26 17:03:41,326 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-26 17:03:41,335 [INFO] app.services.interface_service:153 - 接口解析成功: 123
2025-07-26 17:03:41,336 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.126s
2025-07-26 17:03:41,448 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-26 17:03:44,744 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 17:03:44,750 [INFO] root:48 - 数据库表创建完成
2025-07-26 17:03:44,803 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 17:03:44,803 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 17:03:44,810 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753520621342
2025-07-26 17:03:44,818 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753520621342 状态码: 200 耗时: 0.008s
2025-07-26 17:04:11,557 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 17:04:11,722 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 17:04:11,722 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 17:04:11,724 [WARNING] app.core.tvbox_decryptor:1057 - 第 1 个Base64字符串解码失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 17:04:11,724 [WARNING] app.core.tvbox_decryptor:1061 - 未能从混合内容中提取有效的JSON配置，返回空模板
2025-07-26 17:04:11,724 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=83
2025-07-26 17:04:11,724 [INFO] app.core.tvbox_decryptor:1119 - 配置验证通过: 包含有效字段但无站点
2025-07-26 17:04:11,724 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-26 17:04:11,736 [INFO] app.services.interface_service:153 - 接口解析成功: 123
2025-07-26 17:04:11,737 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.180s
2025-07-26 17:04:12,054 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753520651743
2025-07-26 17:04:12,058 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753520651743 状态码: 200 耗时: 0.003s
2025-07-26 17:05:01,848 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-26 17:05:04,876 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 17:05:04,882 [INFO] root:48 - 数据库表创建完成
2025-07-26 17:05:04,929 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 17:05:04,929 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 17:05:08,603 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 17:05:08,754 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 17:05:08,754 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 17:05:08,754 [WARNING] app.core.tvbox_decryptor:1069 - 第 1 个Base64字符串解码失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 17:05:08,754 [WARNING] app.core.tvbox_decryptor:1073 - 未能从混合内容中提取有效的JSON配置，返回空模板
2025-07-26 17:05:08,754 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=83
2025-07-26 17:05:08,756 [INFO] app.core.tvbox_decryptor:1131 - 配置验证通过: 包含有效字段但无站点
2025-07-26 17:05:08,756 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-26 17:05:08,769 [INFO] app.services.interface_service:153 - 接口解析成功: 123
2025-07-26 17:05:08,769 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.165s
2025-07-26 17:05:09,097 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753520708774
2025-07-26 17:05:09,112 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753520708774 状态码: 200 耗时: 0.015s
2025-07-26 17:06:05,329 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 17:06:05,432 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 17:06:05,432 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 17:06:05,434 [WARNING] app.core.tvbox_decryptor:1069 - 第 1 个Base64字符串解码失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 17:06:05,434 [WARNING] app.core.tvbox_decryptor:1073 - 未能从混合内容中提取有效的JSON配置，返回空模板
2025-07-26 17:06:05,434 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=83
2025-07-26 17:06:05,434 [INFO] app.core.tvbox_decryptor:1131 - 配置验证通过: 包含有效字段但无站点
2025-07-26 17:06:05,435 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-26 17:06:05,442 [INFO] app.services.interface_service:153 - 接口解析成功: 123
2025-07-26 17:06:05,443 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.114s
2025-07-26 17:06:05,552 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-26 17:06:08,617 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 17:06:08,624 [INFO] root:48 - 数据库表创建完成
2025-07-26 17:06:08,675 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 17:06:08,675 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 17:06:08,684 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753520765448
2025-07-26 17:06:08,692 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753520765448 状态码: 200 耗时: 0.008s
2025-07-26 17:06:36,024 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 17:06:36,172 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 17:06:36,172 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 17:06:36,173 [WARNING] app.core.tvbox_decryptor:1075 - 第 1 个Base64字符串解码失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 17:06:36,173 [WARNING] app.core.tvbox_decryptor:1079 - 未能从混合内容中提取有效的JSON配置，返回空模板
2025-07-26 17:06:36,173 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=83
2025-07-26 17:06:36,174 [INFO] app.core.tvbox_decryptor:1137 - 配置验证通过: 包含有效字段但无站点
2025-07-26 17:06:36,174 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-26 17:06:36,187 [INFO] app.services.interface_service:153 - 接口解析成功: 123
2025-07-26 17:06:36,188 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.164s
2025-07-26 17:06:36,510 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753520796193
2025-07-26 17:06:36,514 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753520796193 状态码: 200 耗时: 0.003s
2025-07-26 17:07:27,943 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-26 17:07:31,314 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 17:07:31,320 [INFO] root:48 - 数据库表创建完成
2025-07-26 17:07:31,370 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 17:07:31,371 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 17:07:43,064 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 17:07:43,236 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 17:07:43,236 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 17:07:43,237 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;53ad14bb641d6ad5b1a76ea48f35cb7d",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-26 17:07:43,239 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-26 17:07:43,239 [WARNING] app.core.tvbox_decryptor:1075 - 第 1 个Base64字符串解码失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 17:07:43,239 [WARNING] app.core.tvbox_decryptor:1079 - 未能从混合内容中提取有效的JSON配置，返回空模板
2025-07-26 17:07:43,240 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=83
2025-07-26 17:07:43,240 [INFO] app.core.tvbox_decryptor:1137 - 配置验证通过: 包含有效字段但无站点
2025-07-26 17:07:43,240 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-26 17:07:43,251 [INFO] app.services.interface_service:153 - 接口解析成功: 123
2025-07-26 17:07:43,252 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.188s
2025-07-26 17:07:43,327 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753520863257
2025-07-26 17:07:43,331 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753520863257 状态码: 200 耗时: 0.002s
2025-07-26 17:08:44,054 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 17:08:44,210 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 17:08:44,220 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 17:08:44,248 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;53ad14bb641d6ad5b1a76ea48f35cb7d",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-26 17:08:44,252 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-26 17:08:44,256 [WARNING] app.core.tvbox_decryptor:1075 - 第 1 个Base64字符串解码失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 17:08:44,259 [WARNING] app.core.tvbox_decryptor:1079 - 未能从混合内容中提取有效的JSON配置，返回空模板
2025-07-26 17:08:44,259 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=83
2025-07-26 17:08:44,261 [INFO] app.core.tvbox_decryptor:1137 - 配置验证通过: 包含有效字段但无站点
2025-07-26 17:08:44,262 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-26 17:08:44,285 [INFO] app.services.interface_service:153 - 接口解析成功: 123
2025-07-26 17:08:44,293 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.239s
2025-07-26 17:08:44,410 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-26 17:08:47,549 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 17:08:47,554 [INFO] root:48 - 数据库表创建完成
2025-07-26 17:08:47,601 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 17:08:47,602 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 17:08:47,609 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753520924302
2025-07-26 17:08:47,615 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753520924302 状态码: 200 耗时: 0.007s
2025-07-26 17:09:49,845 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 17:09:50,027 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 17:09:50,027 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 17:09:50,027 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;53ad14bb641d6ad5b1a76ea48f35cb7d",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-26 17:09:50,027 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-26 17:09:50,029 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-26 17:09:50,029 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 17:09:50,030 [WARNING] app.core.tvbox_decryptor:1085 - JSON修复失败
2025-07-26 17:09:50,030 [WARNING] app.core.tvbox_decryptor:1094 - 未能从混合内容中提取有效的JSON配置，返回空模板
2025-07-26 17:09:50,031 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=83
2025-07-26 17:09:50,031 [INFO] app.core.tvbox_decryptor:1152 - 配置验证通过: 包含有效字段但无站点
2025-07-26 17:09:50,031 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-26 17:09:50,043 [INFO] app.services.interface_service:153 - 接口解析成功: 123
2025-07-26 17:09:50,044 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.199s
2025-07-26 17:09:50,155 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-26 17:09:53,257 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 17:09:53,263 [INFO] root:48 - 数据库表创建完成
2025-07-26 17:09:53,307 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 17:09:53,307 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 17:09:53,315 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753520990049
2025-07-26 17:09:53,322 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753520990049 状态码: 200 耗时: 0.007s
2025-07-26 17:10:37,147 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 17:10:37,308 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 17:10:37,308 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 17:10:37,310 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;53ad14bb641d6ad5b1a76ea48f35cb7d",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-26 17:10:37,310 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-26 17:10:37,311 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-26 17:10:37,311 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 17:10:37,311 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-26 17:10:37,311 [WARNING] app.core.tvbox_decryptor:1088 - JSON修复失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 17:10:37,312 [INFO] app.core.tvbox_decryptor:1090 - 返回原始JSON内容的前2000字符作为预览
2025-07-26 17:10:37,312 [INFO] app.services.interface_service:90 - 解密结果: method=直接内容, content_length=2028
2025-07-26 17:10:37,312 [INFO] app.services.interface_service:101 - 配置验证结果: False
2025-07-26 17:10:37,322 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.175s
2025-07-26 17:10:37,637 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753521037328
2025-07-26 17:10:37,639 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753521037328 状态码: 200 耗时: 0.002s
2025-07-26 17:11:19,360 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 17:11:19,509 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 17:11:19,510 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 17:11:19,510 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;53ad14bb641d6ad5b1a76ea48f35cb7d",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-26 17:11:19,511 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-26 17:11:19,511 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-26 17:11:19,511 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 17:11:19,512 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-26 17:11:19,512 [WARNING] app.core.tvbox_decryptor:1088 - JSON修复失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 17:11:19,513 [INFO] app.core.tvbox_decryptor:1090 - 返回原始JSON内容的前2000字符作为预览
2025-07-26 17:11:19,513 [INFO] app.services.interface_service:90 - 解密结果: method=直接内容, content_length=2028
2025-07-26 17:11:19,513 [INFO] app.services.interface_service:101 - 配置验证结果: False
2025-07-26 17:11:19,523 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.163s
2025-07-26 17:11:19,899 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753521079533
2025-07-26 17:11:19,904 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753521079533 状态码: 200 耗时: 0.005s
2025-07-26 17:12:24,930 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-26 17:12:28,079 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 17:12:28,085 [INFO] root:48 - 数据库表创建完成
2025-07-26 17:12:28,136 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 17:12:28,137 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 17:12:31,215 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 17:12:31,451 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 17:12:31,451 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 17:12:31,452 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;53ad14bb641d6ad5b1a76ea48f35cb7d",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-26 17:12:31,452 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-26 17:12:31,452 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-26 17:12:31,453 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 17:12:31,453 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-26 17:12:31,454 [WARNING] app.core.tvbox_decryptor:1090 - JSON修复失败: Invalid control character at: line 2 column 17 (char 19)
2025-07-26 17:12:31,454 [INFO] app.core.tvbox_decryptor:1092 - 返回原始JSON内容的前2000字符作为预览
2025-07-26 17:12:31,454 [INFO] app.services.interface_service:90 - 解密结果: method=直接内容, content_length=2028
2025-07-26 17:12:31,454 [INFO] app.services.interface_service:101 - 配置验证结果: False
2025-07-26 17:12:31,466 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.252s
2025-07-26 17:12:31,476 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753521151472
2025-07-26 17:12:31,481 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753521151472 状态码: 200 耗时: 0.005s
2025-07-26 17:13:36,752 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 17:13:36,876 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 17:13:36,877 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 17:13:36,877 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;53ad14bb641d6ad5b1a76ea48f35cb7d",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-26 17:13:36,877 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-26 17:13:36,878 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-26 17:13:36,878 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 17:13:36,878 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-26 17:13:36,879 [WARNING] app.core.tvbox_decryptor:1090 - JSON修复失败: Invalid control character at: line 2 column 17 (char 19)
2025-07-26 17:13:36,879 [INFO] app.core.tvbox_decryptor:1092 - 返回原始JSON内容的前2000字符作为预览
2025-07-26 17:13:36,879 [INFO] app.services.interface_service:90 - 解密结果: method=直接内容, content_length=2028
2025-07-26 17:13:36,879 [INFO] app.services.interface_service:101 - 配置验证结果: False
2025-07-26 17:13:36,886 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.134s
2025-07-26 17:13:36,997 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-26 17:13:40,136 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 17:13:40,141 [INFO] root:48 - 数据库表创建完成
2025-07-26 17:13:40,192 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 17:13:40,192 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 17:13:40,199 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753521216890
2025-07-26 17:13:40,205 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753521216890 状态码: 200 耗时: 0.007s
2025-07-26 17:14:42,906 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 17:14:43,024 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 17:14:43,024 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 17:14:43,025 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;53ad14bb641d6ad5b1a76ea48f35cb7d",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-26 17:14:43,025 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-26 17:14:43,025 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-26 17:14:43,025 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 17:14:43,026 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-26 17:14:43,026 [WARNING] app.core.tvbox_decryptor:1092 - JSON修复失败: Invalid control character at: line 2 column 17 (char 19)
2025-07-26 17:14:43,026 [INFO] app.core.tvbox_decryptor:1094 - 返回原始JSON内容的前2000字符作为预览
2025-07-26 17:14:43,027 [INFO] app.services.interface_service:90 - 解密结果: method=直接内容, content_length=2028
2025-07-26 17:14:43,027 [INFO] app.services.interface_service:101 - 配置验证结果: False
2025-07-26 17:14:43,035 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.129s
2025-07-26 17:14:43,139 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-26 17:14:46,194 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 17:14:46,200 [INFO] root:48 - 数据库表创建完成
2025-07-26 17:14:46,244 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 17:14:46,245 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 17:14:46,253 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753521283039
2025-07-26 17:14:46,259 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753521283039 状态码: 200 耗时: 0.006s
2025-07-26 17:15:57,234 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-26 17:16:00,674 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 17:16:00,679 [INFO] root:48 - 数据库表创建完成
2025-07-26 17:16:00,727 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 17:16:00,727 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 17:16:00,735 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 17:16:00,830 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 17:16:00,830 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 17:16:00,830 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;53ad14bb641d6ad5b1a76ea48f35cb7d",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-26 17:16:00,831 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-26 17:16:00,831 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-26 17:16:00,831 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 17:16:00,832 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-26 17:16:00,833 [WARNING] app.core.tvbox_decryptor:1092 - JSON修复失败: Invalid control character at: line 2 column 17 (char 19)
2025-07-26 17:16:00,833 [INFO] app.core.tvbox_decryptor:1094 - 返回原始JSON内容的前2000字符作为预览
2025-07-26 17:16:00,833 [INFO] app.services.interface_service:90 - 解密结果: method=直接内容, content_length=2028
2025-07-26 17:16:00,834 [INFO] app.services.interface_service:101 - 配置验证结果: False
2025-07-26 17:16:00,841 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.107s
2025-07-26 17:16:00,849 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753521360847
2025-07-26 17:16:00,854 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753521360847 状态码: 200 耗时: 0.004s
2025-07-26 17:19:48,521 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 17:19:48,670 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 17:19:48,670 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 17:19:48,670 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;53ad14bb641d6ad5b1a76ea48f35cb7d",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-26 17:19:48,671 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-26 17:19:48,671 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-26 17:19:48,671 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 17:19:48,672 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-26 17:19:48,673 [WARNING] app.core.tvbox_decryptor:1092 - JSON修复失败: Invalid control character at: line 2 column 17 (char 19)
2025-07-26 17:19:48,673 [INFO] app.core.tvbox_decryptor:1094 - 返回原始JSON内容的前2000字符作为预览
2025-07-26 17:19:48,673 [INFO] app.services.interface_service:90 - 解密结果: method=直接内容, content_length=2028
2025-07-26 17:19:48,674 [INFO] app.services.interface_service:101 - 配置验证结果: False
2025-07-26 17:19:48,679 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.157s
2025-07-26 17:19:48,773 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753521588684
2025-07-26 17:19:48,776 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753521588684 状态码: 200 耗时: 0.003s
2025-07-26 17:21:13,668 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 17:21:13,828 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 17:21:13,828 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 17:21:13,828 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;53ad14bb641d6ad5b1a76ea48f35cb7d",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-26 17:21:13,829 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-26 17:21:13,829 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-26 17:21:13,829 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 17:21:13,829 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-26 17:21:13,830 [WARNING] app.core.tvbox_decryptor:1092 - JSON修复失败: Invalid control character at: line 2 column 17 (char 19)
2025-07-26 17:21:13,832 [INFO] app.core.tvbox_decryptor:1094 - 返回原始JSON内容的前2000字符作为预览
2025-07-26 17:21:13,832 [INFO] app.services.interface_service:90 - 解密结果: method=直接内容, content_length=2028
2025-07-26 17:21:13,832 [INFO] app.services.interface_service:101 - 配置验证结果: False
2025-07-26 17:21:13,838 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.170s
2025-07-26 17:21:13,945 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-26 17:21:17,024 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 17:21:17,031 [INFO] root:48 - 数据库表创建完成
2025-07-26 17:21:17,079 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 17:21:17,079 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 17:21:17,087 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753521673843
2025-07-26 17:21:17,094 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753521673843 状态码: 200 耗时: 0.008s
2025-07-26 17:22:42,116 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 17:22:42,294 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 17:22:42,296 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 17:22:42,296 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;53ad14bb641d6ad5b1a76ea48f35cb7d",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-26 17:22:42,296 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-26 17:22:42,296 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-26 17:22:42,297 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 17:22:42,297 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-26 17:22:42,298 [WARNING] app.core.tvbox_decryptor:1104 - JSON修复失败: Invalid control character at: line 2 column 17 (char 19)
2025-07-26 17:22:42,299 [INFO] app.core.tvbox_decryptor:1117 - 返回清理注释后的完整内容，长度: 7493
2025-07-26 17:22:42,299 [INFO] app.services.interface_service:90 - 解密结果: method=直接内容, content_length=7493
2025-07-26 17:22:42,299 [INFO] app.services.interface_service:101 - 配置验证结果: False
2025-07-26 17:22:42,308 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.190s
2025-07-26 17:22:42,413 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-26 17:22:45,507 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 17:22:45,513 [INFO] root:48 - 数据库表创建完成
2025-07-26 17:22:45,565 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 17:22:45,566 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 17:22:45,572 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753521762311
2025-07-26 17:22:45,578 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753521762311 状态码: 200 耗时: 0.006s
2025-07-26 18:12:54,677 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753524774670
2025-07-26 18:12:54,678 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753524774670
2025-07-26 18:12:54,679 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753524774670 状态码: 307 耗时: 0.002s
2025-07-26 18:12:54,689 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753524774670 状态码: 200 耗时: 0.011s
2025-07-26 18:12:55,016 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753524774670
2025-07-26 18:12:55,025 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753524774670 状态码: 200 耗时: 0.009s
2025-07-26 18:12:57,471 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753524777467
2025-07-26 18:12:57,474 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753524777467 状态码: 200 耗时: 0.003s
2025-07-26 18:12:59,838 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 18:13:00,016 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 18:13:00,017 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 18:13:00,017 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;53ad14bb641d6ad5b1a76ea48f35cb7d",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-26 18:13:00,017 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-26 18:13:00,017 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-26 18:13:00,018 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 18:13:00,018 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-26 18:13:00,020 [WARNING] app.core.tvbox_decryptor:1104 - JSON修复失败: Invalid control character at: line 2 column 17 (char 19)
2025-07-26 18:13:00,020 [INFO] app.core.tvbox_decryptor:1107 - 开始清理注释...
2025-07-26 18:13:00,020 [INFO] app.core.tvbox_decryptor:1118 - 返回清理注释后的完整内容，长度: 7493
2025-07-26 18:13:00,021 [INFO] app.services.interface_service:90 - 解密结果: method=直接内容, content_length=7493
2025-07-26 18:13:00,021 [INFO] app.services.interface_service:101 - 配置验证结果: False
2025-07-26 18:13:00,032 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.194s
2025-07-26 18:13:00,051 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753524780038
2025-07-26 18:13:00,056 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753524780038 状态码: 200 耗时: 0.004s
2025-07-26 18:27:24,238 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 18:27:24,392 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 18:27:24,392 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 18:27:24,393 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;53ad14bb641d6ad5b1a76ea48f35cb7d",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-26 18:27:24,393 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-26 18:27:24,393 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-26 18:27:24,393 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 18:27:24,394 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-26 18:27:24,395 [WARNING] app.core.tvbox_decryptor:1104 - JSON修复失败: Invalid control character at: line 2 column 17 (char 19)
2025-07-26 18:27:24,395 [INFO] app.core.tvbox_decryptor:1107 - 开始清理注释...
2025-07-26 18:27:24,395 [INFO] app.core.tvbox_decryptor:1118 - 返回清理注释后的完整内容，长度: 7493
2025-07-26 18:27:24,396 [INFO] app.services.interface_service:90 - 解密结果: method=直接内容, content_length=7493
2025-07-26 18:27:24,396 [INFO] app.services.interface_service:101 - 配置验证结果: False
2025-07-26 18:27:24,403 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.165s
2025-07-26 18:27:24,514 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-26 18:27:28,377 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 18:27:28,385 [INFO] root:48 - 数据库表创建完成
2025-07-26 18:27:28,429 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 18:27:28,429 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 18:27:28,437 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753525644408
2025-07-26 18:27:28,444 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753525644408 状态码: 200 耗时: 0.007s
2025-07-26 18:28:48,078 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 18:28:48,240 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 18:28:48,240 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 18:28:48,241 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;53ad14bb641d6ad5b1a76ea48f35cb7d",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-26 18:28:48,241 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-26 18:28:48,241 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-26 18:28:48,242 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 18:28:48,242 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-26 18:28:48,243 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-26 18:28:48,246 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-26 18:28:48,247 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-26 18:28:48,247 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-26 18:28:48,277 [INFO] app.services.interface_service:153 - 接口解析成功: 123
2025-07-26 18:28:48,278 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.201s
2025-07-26 18:28:48,343 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753525728285
2025-07-26 18:28:48,345 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753525728285 状态码: 200 耗时: 0.003s
2025-07-26 19:15:37,127 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753528536807
2025-07-26 19:15:37,128 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753528536807 状态码: 307 耗时: 0.001s
2025-07-26 19:15:37,129 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753528536807
2025-07-26 19:15:37,140 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753528536807 状态码: 200 耗时: 0.011s
2025-07-26 19:15:37,438 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753528536807
2025-07-26 19:15:37,445 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753528536807 状态码: 200 耗时: 0.007s
2025-07-26 19:15:40,622 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753528540617
2025-07-26 19:15:40,625 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753528540617 状态码: 200 耗时: 0.003s
2025-07-26 19:15:43,196 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 19:15:43,295 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 19:15:43,295 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 19:15:43,297 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;53ad14bb641d6ad5b1a76ea48f35cb7d",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-26 19:15:43,297 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-26 19:15:43,297 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-26 19:15:43,297 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 19:15:43,297 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-26 19:15:43,300 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-26 19:15:43,302 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-26 19:15:43,302 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-26 19:15:43,303 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-26 19:15:43,311 [INFO] app.services.interface_service:153 - 接口解析成功: 123
2025-07-26 19:15:43,312 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.115s
2025-07-26 19:15:43,461 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753528543320
2025-07-26 19:15:43,464 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753528543320 状态码: 200 耗时: 0.003s
2025-07-26 19:16:20,358 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753528580352
2025-07-26 19:16:20,359 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753528580352 状态码: 307 耗时: 0.001s
2025-07-26 19:16:20,383 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753528580352
2025-07-26 19:16:20,389 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753528580352 状态码: 200 耗时: 0.005s
2025-07-26 19:16:20,662 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753528580352
2025-07-26 19:16:20,668 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753528580352 状态码: 200 耗时: 0.005s
2025-07-26 20:10:41,186 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 20:10:41,199 [INFO] root:48 - 数据库表创建完成
2025-07-26 20:10:41,255 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 20:10:41,255 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 20:11:22,801 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 20:11:22,808 [INFO] root:48 - 数据库表创建完成
2025-07-26 20:11:22,859 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 20:11:22,860 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 20:13:06,654 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 20:13:06,661 [INFO] root:48 - 数据库表创建完成
2025-07-26 20:13:06,703 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 20:13:06,705 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 20:13:46,206 [INFO] root:149 - 请求开始: GET http://localhost:8001/docs
2025-07-26 20:13:46,209 [INFO] root:158 - 请求完成: GET http://localhost:8001/docs 状态码: 200 耗时: 0.002s
2025-07-26 20:13:46,809 [INFO] root:149 - 请求开始: GET http://localhost:8001/openapi.json
2025-07-26 20:13:46,879 [INFO] root:158 - 请求完成: GET http://localhost:8001/openapi.json 状态码: 200 耗时: 0.070s
2025-07-26 20:14:17,881 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 20:14:17,883 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1/parse 状态码: 405 耗时: 0.002s
2025-07-26 20:14:17,933 [INFO] root:149 - 请求开始: GET http://localhost:8001/favicon.ico
2025-07-26 20:14:17,934 [INFO] root:158 - 请求完成: GET http://localhost:8001/favicon.ico 状态码: 404 耗时: 0.001s
2025-07-26 20:14:31,198 [INFO] root:149 - 请求开始: GET http://localhost:8001/docs
2025-07-26 20:14:31,199 [INFO] root:158 - 请求完成: GET http://localhost:8001/docs 状态码: 200 耗时: 0.001s
2025-07-26 20:14:31,295 [INFO] root:149 - 请求开始: GET http://localhost:8001/openapi.json
2025-07-26 20:14:31,297 [INFO] root:158 - 请求完成: GET http://localhost:8001/openapi.json 状态码: 200 耗时: 0.002s
2025-07-26 20:15:46,452 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse?force_update=false
2025-07-26 20:15:46,693 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 20:15:46,694 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 20:15:46,694 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;53ad14bb641d6ad5b1a76ea48f35cb7d",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-26 20:15:46,696 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-26 20:15:46,696 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-26 20:15:46,696 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 20:15:46,697 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-26 20:15:46,699 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-26 20:15:46,704 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-26 20:15:46,704 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-26 20:15:46,704 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-26 20:15:46,710 [INFO] app.services.interface_service:119 - 接口 123 无变化，跳过更新: 配置内容无变化
2025-07-26 20:15:46,719 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse?force_update=false 状态码: 200 耗时: 0.267s
2025-07-26 20:17:20,309 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse?force_update=true
2025-07-26 20:17:20,503 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 20:17:20,504 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 20:17:20,504 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;53ad14bb641d6ad5b1a76ea48f35cb7d",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-26 20:17:20,504 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-26 20:17:20,505 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-26 20:17:20,505 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 20:17:20,505 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-26 20:17:20,507 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-26 20:17:20,511 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-26 20:17:20,512 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-26 20:17:20,512 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-26 20:17:20,533 [INFO] app.services.interface_service:171 - 接口解析成功: 123 - 解析成功 - 更新配置
2025-07-26 20:17:20,534 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse?force_update=true 状态码: 200 耗时: 0.225s
2025-07-26 20:18:39,308 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/batch-update?force_update=false
2025-07-26 20:18:39,310 [INFO] app.api.v1.interfaces:512 - 开始批量更新 1 个接口，强制更新: False
2025-07-26 20:18:39,524 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 20:18:39,524 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 20:18:39,525 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;53ad14bb641d6ad5b1a76ea48f35cb7d",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-26 20:18:39,525 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-26 20:18:39,525 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-26 20:18:39,526 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 20:18:39,526 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-26 20:18:39,527 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-26 20:18:39,530 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-26 20:18:39,530 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-26 20:18:39,530 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-26 20:18:39,533 [INFO] app.services.interface_service:119 - 接口 123 无变化，跳过更新: 配置内容无变化
2025-07-26 20:18:39,540 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/batch-update?force_update=false 状态码: 200 耗时: 0.234s
2025-07-26 20:19:38,797 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/batch-update?force_update=true
2025-07-26 20:19:38,800 [INFO] app.api.v1.interfaces:512 - 开始批量更新 1 个接口，强制更新: True
2025-07-26 20:19:38,964 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 20:19:38,965 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 20:19:38,965 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;53ad14bb641d6ad5b1a76ea48f35cb7d",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-26 20:19:38,966 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-26 20:19:38,966 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-26 20:19:38,966 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 20:19:38,966 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-26 20:19:38,969 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-26 20:19:38,971 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-26 20:19:38,972 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-26 20:19:38,973 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-26 20:19:38,991 [INFO] app.services.interface_service:171 - 接口解析成功: 123 - 解析成功 - 更新配置
2025-07-26 20:19:38,992 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/batch-update?force_update=true 状态码: 200 耗时: 0.195s
2025-07-26 20:30:01,629 [INFO] root:149 - 请求开始: GET http://localhost:8001/docs
2025-07-26 20:30:01,630 [INFO] root:158 - 请求完成: GET http://localhost:8001/docs 状态码: 200 耗时: 0.002s
2025-07-26 20:30:01,698 [INFO] root:149 - 请求开始: GET http://localhost:8001/openapi.json
2025-07-26 20:30:01,701 [INFO] root:158 - 请求完成: GET http://localhost:8001/openapi.json 状态码: 200 耗时: 0.003s
2025-07-26 20:31:22,396 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/batch-update?force_update=false
2025-07-26 20:31:22,399 [INFO] app.api.v1.interfaces:512 - 开始批量更新 1 个接口，强制更新: False
2025-07-26 20:31:24,829 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 20:31:24,829 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 20:31:24,830 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;53ad14bb641d6ad5b1a76ea48f35cb7d",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-26 20:31:24,830 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-26 20:31:24,830 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-26 20:31:24,831 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 20:31:24,831 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-26 20:31:24,833 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-26 20:31:24,835 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-26 20:31:24,836 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-26 20:31:24,836 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-26 20:31:24,839 [INFO] app.services.interface_service:119 - 接口 123 无变化，跳过更新: 配置内容无变化
2025-07-26 20:31:24,846 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/batch-update?force_update=false 状态码: 200 耗时: 2.450s
2025-07-26 20:41:43,081 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 20:41:43,088 [INFO] root:48 - 数据库表创建完成
2025-07-26 20:41:43,167 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 20:41:43,168 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 20:42:19,077 [INFO] root:149 - 请求开始: GET http://localhost:8001/health
2025-07-26 20:42:19,079 [INFO] root:158 - 请求完成: GET http://localhost:8001/health 状态码: 200 耗时: 0.002s
2025-07-26 20:42:19,117 [INFO] root:149 - 请求开始: GET http://localhost:8001/favicon.ico
2025-07-26 20:42:19,118 [INFO] root:158 - 请求完成: GET http://localhost:8001/favicon.ico 状态码: 404 耗时: 0.002s
2025-07-26 20:42:25,874 [INFO] root:149 - 请求开始: GET http://localhost:8001/health
2025-07-26 20:42:25,875 [INFO] root:158 - 请求完成: GET http://localhost:8001/health 状态码: 200 耗时: 0.002s
2025-07-26 20:43:16,872 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/login
2025-07-26 20:43:17,231 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.358s
2025-07-26 20:43:18,029 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats?_t=1753533797922
2025-07-26 20:43:18,038 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats?_t=1753533797922 状态码: 200 耗时: 0.010s
2025-07-26 20:43:45,801 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753533825788
2025-07-26 20:43:45,802 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753533825789
2025-07-26 20:43:45,803 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753533825788 状态码: 307 耗时: 0.002s
2025-07-26 20:43:45,810 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753533825789 状态码: 200 耗时: 0.008s
2025-07-26 20:43:46,161 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753533825788
2025-07-26 20:43:46,181 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753533825788 状态码: 200 耗时: 0.019s
2025-07-26 20:46:06,883 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/batch-update?force_update=false
2025-07-26 20:46:06,888 [INFO] app.api.v1.interfaces:512 - 开始批量更新 1 个接口，强制更新: False
2025-07-26 20:46:07,042 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 20:46:07,043 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 20:46:07,043 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;53ad14bb641d6ad5b1a76ea48f35cb7d",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-26 20:46:07,043 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-26 20:46:07,044 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-26 20:46:07,044 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 20:46:07,044 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-26 20:46:07,047 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-26 20:46:07,049 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-26 20:46:07,050 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-26 20:46:07,050 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-26 20:46:07,055 [INFO] app.services.interface_service:119 - 接口 123 无变化，跳过更新: 配置内容无变化
2025-07-26 20:46:07,067 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/batch-update?force_update=false 状态码: 200 耗时: 0.184s
2025-07-26 20:46:07,149 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753533967077
2025-07-26 20:46:07,150 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753533967077 状态码: 307 耗时: 0.001s
2025-07-26 20:46:07,458 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753533967077
2025-07-26 20:46:07,463 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753533967077 状态码: 200 耗时: 0.005s
2025-07-26 20:47:35,080 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/batch-update?force_update=true
2025-07-26 20:47:35,082 [INFO] app.api.v1.interfaces:512 - 开始批量更新 1 个接口，强制更新: True
2025-07-26 20:47:35,239 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 20:47:35,239 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 20:47:35,240 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;53ad14bb641d6ad5b1a76ea48f35cb7d",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-26 20:47:35,240 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-26 20:47:35,240 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-26 20:47:35,241 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 20:47:35,241 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-26 20:47:35,243 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-26 20:47:35,246 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-26 20:47:35,247 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-26 20:47:35,247 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-26 20:47:35,270 [INFO] app.services.interface_service:171 - 接口解析成功: 123 - 解析成功 - 更新配置
2025-07-26 20:47:35,271 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/batch-update?force_update=true 状态码: 200 耗时: 0.190s
2025-07-26 20:47:35,331 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753534055279
2025-07-26 20:47:35,331 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753534055279 状态码: 307 耗时: 0.000s
2025-07-26 20:47:35,640 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753534055279
2025-07-26 20:47:35,644 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753534055279 状态码: 200 耗时: 0.004s
2025-07-26 20:54:27,624 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753534467612
2025-07-26 20:54:27,631 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753534467612 状态码: 200 耗时: 0.007s
2025-07-26 20:54:32,697 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 20:54:32,807 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 20:54:32,809 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 20:54:32,809 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;53ad14bb641d6ad5b1a76ea48f35cb7d",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-26 20:54:32,810 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-26 20:54:32,810 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-26 20:54:32,811 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 20:54:32,811 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-26 20:54:32,812 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-26 20:54:32,817 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-26 20:54:32,817 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-26 20:54:32,818 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-26 20:54:32,820 [INFO] app.services.interface_service:119 - 接口 123 无变化，跳过更新: 配置内容无变化
2025-07-26 20:54:32,826 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.128s
2025-07-26 20:54:32,982 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753534472837
2025-07-26 20:54:32,984 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753534472837 状态码: 200 耗时: 0.002s
2025-07-26 20:54:38,477 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753534478470
2025-07-26 20:54:38,478 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753534478470
2025-07-26 20:54:38,478 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753534478470 状态码: 307 耗时: 0.001s
2025-07-26 20:54:38,483 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753534478470 状态码: 200 耗时: 0.005s
2025-07-26 20:54:38,804 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753534478470
2025-07-26 20:54:38,807 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753534478470 状态码: 200 耗时: 0.003s
2025-07-26 20:54:41,006 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753534480684
2025-07-26 20:54:41,010 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753534480684 状态码: 200 耗时: 0.004s
2025-07-26 20:57:53,517 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-26 20:57:53,524 [INFO] root:48 - 数据库表创建完成
2025-07-26 20:57:53,577 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-26 20:57:53,577 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-26 20:59:14,906 [INFO] root:149 - 请求开始: GET http://localhost:8001/health
2025-07-26 20:59:14,906 [INFO] root:158 - 请求完成: GET http://localhost:8001/health 状态码: 200 耗时: 0.001s
2025-07-26 20:59:31,147 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753534771139
2025-07-26 20:59:31,166 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753534771139 状态码: 200 耗时: 0.021s
2025-07-26 20:59:31,505 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753534771494
2025-07-26 20:59:31,505 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753534771494
2025-07-26 20:59:31,507 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753534771494 状态码: 307 耗时: 0.003s
2025-07-26 20:59:31,513 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753534771494 状态码: 200 耗时: 0.008s
2025-07-26 20:59:31,833 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753534771494
2025-07-26 20:59:31,845 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753534771494 状态码: 200 耗时: 0.012s
2025-07-26 21:00:59,701 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/batch-update?force_update=true
2025-07-26 21:00:59,704 [INFO] app.api.v1.interfaces:512 - 开始批量更新 1 个接口，强制更新: True
2025-07-26 21:00:59,892 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 21:00:59,892 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 21:00:59,892 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;53ad14bb641d6ad5b1a76ea48f35cb7d",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-26 21:00:59,893 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-26 21:00:59,893 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-26 21:00:59,894 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 21:00:59,894 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-26 21:00:59,897 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-26 21:00:59,901 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-26 21:00:59,903 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-26 21:00:59,903 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-26 21:00:59,931 [INFO] app.services.interface_service:174 - 接口解析成功: 123 - 解析成功 - 更新配置
2025-07-26 21:00:59,932 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/batch-update?force_update=true 状态码: 200 耗时: 0.232s
2025-07-26 21:00:59,950 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753534859946
2025-07-26 21:00:59,952 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753534859946 状态码: 307 耗时: 0.001s
2025-07-26 21:01:00,271 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753534859946
2025-07-26 21:01:00,274 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753534859946 状态码: 200 耗时: 0.004s
2025-07-26 21:01:32,836 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753534892830
2025-07-26 21:01:32,839 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753534892830 状态码: 200 耗时: 0.003s
2025-07-26 21:02:23,210 [INFO] root:149 - 请求开始: GET http://localhost:8001/docs
2025-07-26 21:02:23,211 [INFO] root:158 - 请求完成: GET http://localhost:8001/docs 状态码: 200 耗时: 0.001s
2025-07-26 21:02:23,289 [INFO] root:149 - 请求开始: GET http://localhost:8001/openapi.json
2025-07-26 21:02:23,339 [INFO] root:158 - 请求完成: GET http://localhost:8001/openapi.json 状态码: 200 耗时: 0.048s
2025-07-26 21:03:57,021 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/batch-update?force_update=true
2025-07-26 21:03:57,023 [INFO] app.api.v1.interfaces:512 - 开始批量更新 1 个接口，强制更新: True
2025-07-26 21:03:57,204 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 21:03:57,204 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 21:03:57,204 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;53ad14bb641d6ad5b1a76ea48f35cb7d",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-26 21:03:57,205 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-26 21:03:57,206 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-26 21:03:57,206 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 21:03:57,206 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-26 21:03:57,209 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-26 21:03:57,211 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-26 21:03:57,212 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-26 21:03:57,212 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-26 21:03:57,232 [INFO] app.services.interface_service:174 - 接口解析成功: 123 - 解析成功 - 更新配置
2025-07-26 21:03:57,233 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/batch-update?force_update=true 状态码: 200 耗时: 0.213s
2025-07-26 21:04:52,370 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753535092363
2025-07-26 21:04:52,377 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753535092363 状态码: 200 耗时: 0.007s
2025-07-26 21:04:52,695 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753535092686
2025-07-26 21:04:52,700 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753535092686 状态码: 200 耗时: 0.005s
2025-07-26 21:05:45,676 [INFO] root:149 - 请求开始: GET http://localhost:8001/docs
2025-07-26 21:05:45,678 [INFO] root:158 - 请求完成: GET http://localhost:8001/docs 状态码: 200 耗时: 0.001s
2025-07-26 21:05:45,751 [INFO] root:149 - 请求开始: GET http://localhost:8001/openapi.json
2025-07-26 21:05:45,753 [INFO] root:158 - 请求完成: GET http://localhost:8001/openapi.json 状态码: 200 耗时: 0.002s
2025-07-26 21:06:21,037 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/batch-update?force_update=false
2025-07-26 21:06:21,038 [INFO] app.api.v1.interfaces:512 - 开始批量更新 1 个接口，强制更新: False
2025-07-26 21:06:21,139 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 21:06:21,140 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 21:06:21,140 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;53ad14bb641d6ad5b1a76ea48f35cb7d",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-26 21:06:21,141 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-26 21:06:21,141 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-26 21:06:21,141 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 21:06:21,142 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-26 21:06:21,144 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-26 21:06:21,148 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-26 21:06:21,148 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-26 21:06:21,149 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-26 21:06:21,150 [INFO] app.services.interface_service:119 - 接口 123 无变化，跳过更新: 配置内容无变化
2025-07-26 21:06:21,159 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/batch-update?force_update=false 状态码: 200 耗时: 0.122s
2025-07-26 21:15:26,361 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/login
2025-07-26 21:15:26,673 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.313s
2025-07-26 21:15:26,990 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats?_t=1753535726897
2025-07-26 21:15:26,994 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats?_t=1753535726897 状态码: 200 耗时: 0.004s
2025-07-26 21:15:29,003 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753535728994
2025-07-26 21:15:29,003 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753535728994
2025-07-26 21:15:29,004 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753535728994 状态码: 307 耗时: 0.002s
2025-07-26 21:15:29,008 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753535728994 状态码: 200 耗时: 0.006s
2025-07-26 21:15:29,346 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753535728994
2025-07-26 21:15:29,350 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753535728994 状态码: 200 耗时: 0.005s
2025-07-26 21:15:31,549 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753535731541
2025-07-26 21:15:31,552 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753535731541 状态码: 200 耗时: 0.003s
2025-07-26 21:15:52,695 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-26 21:15:52,834 [INFO] app.core.tvbox_decryptor:1030 - 开始从混合内容中提取JSON，内容长度: 18367
2025-07-26 21:15:52,836 [INFO] app.core.tvbox_decryptor:1036 - 找到 1 个可能的Base64字符串
2025-07-26 21:15:52,836 [INFO] app.core.tvbox_decryptor:1046 - 解码后内容前500字符: {

"spider":"https://fs-im-kefu.7moor-fs1.com/ly/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1753018609748/f0720.jpg;md5;53ad14bb641d6ad5b1a76ea48f35cb7d",

"wallpaper":"https://深色壁纸.xxooo.cf/",



"sites":[

{"key":"豆","name":"修复926看球【神秘的哥哥们】","type": 3, "api": "csp_DouDouGuard","indexs":1,"searchable": 0,"quickSearch": 0,"changeable":0},

{"key":"玩偶","name":"👽玩偶哥哥┃4K弹幕","type":3,"api":"csp_WoGGGuard","timeout":30,"searchable":1,"quickSearch":1,"changeable":0, "ext": {"Cloud-drive":"tvfan/Cloud-drive.
2025-07-26 21:15:52,836 [INFO] app.core.tvbox_decryptor:1053 - JSON起始位置: 0, 结束位置: 11017
2025-07-26 21:15:52,836 [INFO] app.core.tvbox_decryptor:1057 - 提取JSON部分，长度: 11018
2025-07-26 21:15:52,837 [WARNING] app.core.tvbox_decryptor:1073 - JSON解析失败: Expecting value: line 18 column 1 (char 1957)
2025-07-26 21:15:52,837 [INFO] app.core.tvbox_decryptor:1075 - 错误位置附近的内容: ,"searchable":1,"quickSearch":1,"changeable":1},

//{"key":"溢彩","name":"💡溢彩┃秒播","type":3,"api":"csp_
2025-07-26 21:15:52,838 [INFO] app.core.tvbox_decryptor:1118 - 修复JSON后解析成功，长度: 10116
2025-07-26 21:15:52,841 [INFO] app.services.interface_service:90 - 解密结果: method=配置解析, content_length=14966
2025-07-26 21:15:52,841 [INFO] app.core.tvbox_decryptor:1251 - 验证通过: 找到50个有效站点
2025-07-26 21:15:52,841 [INFO] app.services.interface_service:101 - 配置验证结果: True
2025-07-26 21:15:52,843 [INFO] app.services.interface_service:119 - 接口 123 无变化，跳过更新: 配置内容无变化
2025-07-26 21:15:52,851 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 200 耗时: 0.156s
2025-07-26 21:15:53,283 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753535752859
2025-07-26 21:15:53,286 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753535752859 状态码: 200 耗时: 0.003s
2025-07-26 21:15:59,830 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753535759820
2025-07-26 21:15:59,831 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753535759820 状态码: 307 耗时: 0.001s
2025-07-26 21:15:59,850 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753535759820
2025-07-26 21:15:59,856 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753535759820 状态码: 200 耗时: 0.006s
2025-07-26 21:16:00,134 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753535759820
2025-07-26 21:16:00,138 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753535759820 状态码: 200 耗时: 0.003s
2025-07-26 21:16:02,454 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753535762146
2025-07-26 21:16:02,457 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753535762146 状态码: 200 耗时: 0.003s
2025-07-26 21:22:01,062 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1
2025-07-26 21:22:01,065 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.004s
2025-07-26 22:23:06,701 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753539786385
2025-07-26 22:23:06,703 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753539786385
2025-07-26 22:23:06,703 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753539786385 状态码: 307 耗时: 0.002s
2025-07-26 22:23:06,707 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753539786385 状态码: 200 耗时: 0.005s
2025-07-26 22:23:07,022 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753539786385
2025-07-26 22:23:07,025 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753539786385 状态码: 200 耗时: 0.002s
2025-07-26 22:23:10,211 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753539790206
2025-07-26 22:23:10,215 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753539790206 状态码: 200 耗时: 0.004s
2025-07-26 22:25:44,170 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753539944163
2025-07-26 22:25:44,172 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753539944163 状态码: 200 耗时: 0.002s
2025-07-26 22:26:02,412 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753539962407
2025-07-26 22:26:02,420 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753539962407 状态码: 200 耗时: 0.008s
2025-07-26 22:26:02,657 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753539962650
2025-07-26 22:26:02,661 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753539962650 状态码: 200 耗时: 0.005s
2025-07-26 22:29:35,947 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753540175941
2025-07-26 22:29:35,949 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753540175941
2025-07-26 22:29:35,949 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753540175941 状态码: 307 耗时: 0.002s
2025-07-26 22:29:35,953 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753540175941 状态码: 200 耗时: 0.004s
2025-07-26 22:29:36,273 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753540175941
2025-07-26 22:29:36,277 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753540175941 状态码: 200 耗时: 0.004s
2025-07-26 22:29:37,944 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753540177939
2025-07-26 22:29:37,947 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753540177939 状态码: 200 耗时: 0.004s
2025-07-26 22:30:04,209 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753540204202
2025-07-26 22:30:04,210 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753540204202
2025-07-26 22:30:04,210 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753540204202 状态码: 307 耗时: 0.001s
2025-07-26 22:30:04,216 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753540204202 状态码: 200 耗时: 0.006s
2025-07-26 22:30:04,230 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753540204202
2025-07-26 22:30:04,236 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753540204202 状态码: 200 耗时: 0.006s
2025-07-26 22:30:05,981 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753540205976
2025-07-26 22:30:05,988 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753540205976 状态码: 200 耗时: 0.007s
2025-07-26 22:30:19,542 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753540219231
2025-07-26 22:30:19,543 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753540219231
2025-07-26 22:30:19,543 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753540219231 状态码: 307 耗时: 0.001s
2025-07-26 22:30:19,549 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753540219231 状态码: 200 耗时: 0.006s
2025-07-26 22:30:19,848 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753540219231
2025-07-26 22:30:19,854 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753540219231 状态码: 200 耗时: 0.005s
2025-07-26 22:30:21,545 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753540221541
2025-07-26 22:30:21,552 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753540221541 状态码: 200 耗时: 0.007s
2025-07-26 22:30:24,607 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753540224290
2025-07-26 22:30:24,610 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753540224290 状态码: 307 耗时: 0.003s
2025-07-26 22:30:24,611 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753540224290
2025-07-26 22:30:24,613 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753540224290
2025-07-26 22:30:24,617 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753540224290 状态码: 200 耗时: 0.004s
2025-07-26 22:30:24,619 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753540224290 状态码: 200 耗时: 0.008s
2025-07-26 22:30:27,483 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753540227477
2025-07-26 22:30:27,483 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753540227477 状态码: 307 耗时: 0.000s
2025-07-26 22:30:27,499 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753540227477
2025-07-26 22:30:27,502 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753540227477 状态码: 200 耗时: 0.003s
2025-07-26 22:30:27,792 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753540227477
2025-07-26 22:30:27,795 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753540227477 状态码: 200 耗时: 0.003s
2025-07-26 22:30:33,382 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753540233067
2025-07-26 22:30:33,385 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753540233067 状态码: 200 耗时: 0.003s
