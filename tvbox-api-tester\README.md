# TVBox API Web 测试工具

一个独立的Web界面API测试工具，专门用于测试TVBox Manager Pro后端API。

## 🚀 功能特性

### 核心功能
- **Web界面** - 现代化的浏览器测试界面
- **手动测试** - 可视化表单输入，实时测试API
- **自动测试** - 一键批量测试所有API接口
- **实时日志** - 详细的请求/响应日志显示
- **结果导出** - 测试结果可导出为JSON/HTML

### 测试覆盖
- **认证API** - 登录、注册、令牌管理
- **接口管理** - CRUD操作、测试、刷新
- **订阅管理** - 订阅创建、配置生成
- **用户管理** - 用户CRUD、权限管理
- **系统管理** - 统计、设置、日志
- **解密API** - URL解密、内容解密

## 🛠 技术栈

- **前端**: HTML5 + CSS3 + JavaScript (ES6+)
- **后端**: Python 3.8+ HTTP服务器
- **UI框架**: Bootstrap 5
- **HTTP客户端**: Axios
- **图表库**: Chart.js
- **代码高亮**: Prism.js
- **认证方式**: Basic Authentication (账号密码)

## 📦 使用方法

### 1. 快速启动 (推荐)
```bash
# 一键启动
python start.py
```

### 2. 手动启动
```bash
# 安装依赖
pip install -r requirements.txt

# 启动服务器
python server.py
```

### 3. 其他方式
```bash
# 方式1: 直接打开HTML文件 (功能受限)
双击 index.html

# 方式2: 使用Python内置服务器 (无API代理)
python -m http.server 8080
```

### 2. 配置API服务器
1. 打开测试工具页面
2. 在"服务器配置"中设置API地址 (默认: http://localhost:8001)
3. 输入用户邮箱和密码 (默认: <EMAIL> / admin123)
4. 点击"测试连接"验证服务器状态

### 3. 开始测试
- **手动测试**: 选择API接口，填写参数，点击"发送请求"
- **自动测试**: 点击"运行所有测试"进行批量测试
- **查看结果**: 在"测试结果"面板查看详细信息

## 📖 使用指南

### 手动测试流程
1. 选择要测试的API分类
2. 选择具体的API接口
3. 填写请求参数
4. 点击"发送请求"
5. 查看响应结果

### 自动测试流程
1. 配置测试参数
2. 点击"运行所有测试"
3. 等待测试完成
4. 查看测试报告
5. 导出测试结果

## 🔧 配置说明

### 服务器配置
- **API地址**: 后端API服务器地址
- **超时时间**: 请求超时时间 (秒)
- **重试次数**: 失败重试次数

### 测试配置
- **并发数**: 自动测试并发请求数
- **延迟**: 请求间隔时间 (毫秒)
- **日志级别**: 日志详细程度

## 📊 测试报告

测试完成后会生成详细报告，包含：
- 测试概览统计
- 成功/失败接口列表
- 响应时间分析
- 错误详情
- 性能图表

## 🎯 特色功能

### 智能参数填充
- 自动记住上次输入的参数
- 提供常用参数模板
- 支持参数变量替换

### 实时监控
- 实时显示请求状态
- 响应时间监控
- 错误率统计

### 结果管理
- 测试历史记录
- 结果对比分析
- 批量导出功能

## 🔐 安全说明

- 本工具仅用于开发测试环境
- 请勿在生产环境使用
- 敏感信息会在本地存储，请注意安全

## 📄 许可证

MIT License
