{"version": 3, "file": "total2.mjs", "sources": ["../../../../../../../packages/components/pagination/src/components/total.vue"], "sourcesContent": ["<template>\n  <span :class=\"ns.e('total')\" :disabled=\"disabled\">\n    {{\n      t('el.pagination.total', {\n        total,\n      })\n    }}\n  </span>\n</template>\n\n<script lang=\"ts\" setup>\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { usePagination } from '../usePagination'\nimport { paginationTotalProps } from './total'\n\nconst { t } = useLocale()\nconst ns = useNamespace('pagination')\nconst { disabled } = usePagination()\n\ndefineOptions({\n  name: 'ElPaginationTotal',\n})\n\ndefineProps(paginationTotalProps)\n</script>\n"], "names": [], "mappings": ";;;;;;;mCAmBc,CAAA;AAAA,EACZ,IAAM,EAAA,mBAAA;AACR,CAAA,CAAA,CAAA;;;;;AANA,IAAM,MAAA,EAAE,CAAE,EAAA,GAAI,SAAU,EAAA,CAAA;AACxB,IAAM,MAAA,EAAA,GAAK,aAAa,YAAY,CAAA,CAAA;AACpC,IAAM,MAAA,EAAE,QAAS,EAAA,GAAI,aAAc,EAAA,CAAA;;;;;;;;;;;;;;;"}