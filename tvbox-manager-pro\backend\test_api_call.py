import requests
import json

def test_localization_api():
    print("测试本地化API调用...")
    
    # API端点
    base_url = "http://localhost:8001"
    
    # 首先登录获取token
    login_data = {
        "email": "<EMAIL>",
        "password": "admin123"
    }
    
    try:
        print("1. 尝试登录...")
        login_response = requests.post(f"{base_url}/api/v1/auth/login", json=login_data)
        print(f"登录响应状态: {login_response.status_code}")
        
        if login_response.status_code == 200:
            token_data = login_response.json()
            token = token_data.get("access_token")
            print(f"登录成功，获取到token")
            
            # 设置认证头
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            # 调用本地化API
            print("2. 调用本地化API...")
            localize_url = f"{base_url}/api/v1/interfaces/1/localize"
            localize_response = requests.post(localize_url, headers=headers)
            print(f"本地化API响应状态: {localize_response.status_code}")
            print(f"本地化API响应内容: {localize_response.text}")
            
        else:
            print(f"登录失败: {login_response.text}")
            
    except requests.exceptions.ConnectionError:
        print("无法连接到后端服务，请确保服务正在运行")
    except Exception as e:
        print(f"API调用失败: {e}")

if __name__ == "__main__":
    test_localization_api()
