        <!-- 页脚 -->
        <footer class="footer">
            <div class="footer-content">
                <div class="footer-copyright">
                    &copy; {{ now.year }} TVBox Manager. All rights reserved.
                </div>
                <div class="footer-info">
                    由 <a href="#">TVBox Manager</a> 强力驱动
                </div>
            </div>
        </footer>
    </div>
    
    <!-- 核心JS -->
    <script>
        // 用户下拉菜单
        const userMenuButton = document.getElementById('user-menu-button');
        const userMenuDropdown = document.getElementById('user-menu-dropdown');
        
        if (userMenuButton && userMenuDropdown) {
            userMenuButton.addEventListener('click', () => {
                userMenuDropdown.classList.toggle('show');
            });
            
            // 点击外部区域关闭下拉菜单
            document.addEventListener('click', (event) => {
                if (!userMenuButton.contains(event.target) && !userMenuDropdown.contains(event.target)) {
                    userMenuDropdown.classList.remove('show');
                }
            });
        }
        
        // 侧边栏切换
        const sidebarToggle = document.getElementById('sidebar-toggle');
        const sidebar = document.getElementById('sidebar');
        const workspace = document.getElementById('workspace');
        
        if (sidebarToggle && sidebar && workspace) {
            // 检查本地存储中的侧边栏状态
            const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            
            // 计算侧边栏宽度 - 自动适应最长菜单项
            const calculateSidebarWidth = () => {
                // 临时展开侧边栏以计算宽度
                const wasCollapsed = sidebar.classList.contains('sidebar-collapsed');
                if (wasCollapsed) sidebar.classList.remove('sidebar-collapsed');
                
                // 获取所有菜单项文本
                const menuItems = document.querySelectorAll('.sidebar-item-text');
                let maxWidth = 180; // 默认最小宽度
                
                menuItems.forEach(item => {
                    const tempSpan = document.createElement('span');
                    tempSpan.style.visibility = 'hidden';
                    tempSpan.style.position = 'absolute';
                    tempSpan.style.whiteSpace = 'nowrap';
                    tempSpan.style.font = window.getComputedStyle(item).font;
                    tempSpan.textContent = item.textContent;
                    document.body.appendChild(tempSpan);
                    
                    // 菜单项文本宽度 + 图标宽度 + 内边距
                    const itemWidth = tempSpan.offsetWidth + 40;
                    if (itemWidth > maxWidth) maxWidth = itemWidth;
                    
                    document.body.removeChild(tempSpan);
                });
                
                // 还原折叠状态
                if (wasCollapsed) sidebar.classList.add('sidebar-collapsed');
                
                // 限制最大宽度
                maxWidth = Math.min(maxWidth, 240);
                maxWidth = Math.max(maxWidth, 180);
                
                // 设置自定义属性存储宽度
                document.documentElement.style.setProperty('--sidebar-width', `${maxWidth}px`);
                document.documentElement.style.setProperty('--sidebar-collapsed-width', '50px');
                
                return maxWidth;
            };
            
            // 计算并应用侧边栏宽度
            const sidebarWidth = calculateSidebarWidth();
            
            // 应用初始状态
            if (sidebarCollapsed) {
                sidebar.classList.add('sidebar-collapsed');
                workspace.style.marginLeft = 'var(--sidebar-collapsed-width)';
                sidebarToggle.innerHTML = '<i class="fas fa-chevron-right"></i>';
            } else {
                workspace.style.marginLeft = `var(--sidebar-width)`;
                sidebarToggle.innerHTML = '<i class="fas fa-chevron-left"></i>';
            }
            
            // 切换侧边栏状态
            sidebarToggle.addEventListener('click', (e) => {
                e.stopPropagation(); // 防止事件冒泡
                const isCollapsed = sidebar.classList.toggle('sidebar-collapsed');
                
                if (isCollapsed) {
                    workspace.style.marginLeft = 'var(--sidebar-collapsed-width)';
                    sidebarToggle.innerHTML = '<i class="fas fa-chevron-right"></i>';
                    localStorage.setItem('sidebarCollapsed', 'true');
                } else {
                    workspace.style.marginLeft = `var(--sidebar-width)`;
                    sidebarToggle.innerHTML = '<i class="fas fa-chevron-left"></i>';
                    localStorage.setItem('sidebarCollapsed', 'false');
                }
            });
        }
        
        // 移动端菜单
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        
        if (mobileMenuToggle && sidebar) {
            mobileMenuToggle.addEventListener('click', () => {
                sidebar.classList.toggle('show');
                
                // 创建遮罩层
                let overlay = document.getElementById('mobile-overlay');
                
                if (!overlay) {
                    overlay = document.createElement('div');
                    overlay.id = 'mobile-overlay';
                    overlay.classList.add('mobile-overlay');
                    document.body.appendChild(overlay);
                    
                    // 点击遮罩关闭菜单
                    overlay.addEventListener('click', () => {
                        sidebar.classList.remove('show');
                        overlay.classList.remove('show');
                    });
                }
                
                overlay.classList.toggle('show');
            });
        }
        
        // 确保移动端遮罩不会覆盖侧边栏上的交互元素
        const handleResize = () => {
            if (window.innerWidth > 768) {
                // PC模式
                const sidebarCollapsed = sidebar && sidebar.classList.contains('sidebar-collapsed');
                if (workspace && sidebar) {
                    workspace.style.marginLeft = sidebarCollapsed ? 
                        'var(--sidebar-collapsed-width)' : 
                        'var(--sidebar-width)';
                }
                
                // 移除可能存在的移动端样式
                if (sidebar) {
                    sidebar.classList.remove('show');
                }
                const overlay = document.getElementById('mobile-overlay');
                if (overlay) {
                    overlay.classList.remove('show');
                }
            } else {
                // 移动模式
                if (workspace) {
                    workspace.style.marginLeft = '0';
                }
            }
        };
        
        window.addEventListener('resize', handleResize);
        // 初始调用一次
        handleResize();
        
        // 自动隐藏提示
        const alerts = document.querySelectorAll('.alert');
        
        alerts.forEach(alert => {
            // 关闭按钮
            const closeBtn = alert.querySelector('.alert-close');
            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    alert.style.opacity = '0';
                    setTimeout(() => {
                        alert.style.display = 'none';
                    }, 300);
                });
            }
            
            // 5秒后自动隐藏
            setTimeout(() => {
                alert.style.opacity = '0';
                setTimeout(() => {
                    alert.style.display = 'none';
                }, 300);
            }, 5000);
        });
    </script>
    
    <!-- 自定义JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html> 
        <footer class="footer">
            <div class="footer-content">
                <div class="footer-copyright">
                    &copy; {{ now.year }} TVBox Manager. All rights reserved.
                </div>
                <div class="footer-info">
                    由 <a href="#">TVBox Manager</a> 强力驱动
                </div>
            </div>
        </footer>
    </div>
    
    <!-- 核心JS -->
    <script>
        // 用户下拉菜单
        const userMenuButton = document.getElementById('user-menu-button');
        const userMenuDropdown = document.getElementById('user-menu-dropdown');
        
        if (userMenuButton && userMenuDropdown) {
            userMenuButton.addEventListener('click', () => {
                userMenuDropdown.classList.toggle('show');
            });
            
            // 点击外部区域关闭下拉菜单
            document.addEventListener('click', (event) => {
                if (!userMenuButton.contains(event.target) && !userMenuDropdown.contains(event.target)) {
                    userMenuDropdown.classList.remove('show');
                }
            });
        }
        
        // 侧边栏切换
        const sidebarToggle = document.getElementById('sidebar-toggle');
        const sidebar = document.getElementById('sidebar');
        const workspace = document.getElementById('workspace');
        
        if (sidebarToggle && sidebar && workspace) {
            // 检查本地存储中的侧边栏状态
            const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            
            // 计算侧边栏宽度 - 自动适应最长菜单项
            const calculateSidebarWidth = () => {
                // 临时展开侧边栏以计算宽度
                const wasCollapsed = sidebar.classList.contains('sidebar-collapsed');
                if (wasCollapsed) sidebar.classList.remove('sidebar-collapsed');
                
                // 获取所有菜单项文本
                const menuItems = document.querySelectorAll('.sidebar-item-text');
                let maxWidth = 180; // 默认最小宽度
                
                menuItems.forEach(item => {
                    const tempSpan = document.createElement('span');
                    tempSpan.style.visibility = 'hidden';
                    tempSpan.style.position = 'absolute';
                    tempSpan.style.whiteSpace = 'nowrap';
                    tempSpan.style.font = window.getComputedStyle(item).font;
                    tempSpan.textContent = item.textContent;
                    document.body.appendChild(tempSpan);
                    
                    // 菜单项文本宽度 + 图标宽度 + 内边距
                    const itemWidth = tempSpan.offsetWidth + 40;
                    if (itemWidth > maxWidth) maxWidth = itemWidth;
                    
                    document.body.removeChild(tempSpan);
                });
                
                // 还原折叠状态
                if (wasCollapsed) sidebar.classList.add('sidebar-collapsed');
                
                // 限制最大宽度
                maxWidth = Math.min(maxWidth, 240);
                maxWidth = Math.max(maxWidth, 180);
                
                // 设置自定义属性存储宽度
                document.documentElement.style.setProperty('--sidebar-width', `${maxWidth}px`);
                document.documentElement.style.setProperty('--sidebar-collapsed-width', '50px');
                
                return maxWidth;
            };
            
            // 计算并应用侧边栏宽度
            const sidebarWidth = calculateSidebarWidth();
            
            // 应用初始状态
            if (sidebarCollapsed) {
                sidebar.classList.add('sidebar-collapsed');
                workspace.style.marginLeft = 'var(--sidebar-collapsed-width)';
                sidebarToggle.innerHTML = '<i class="fas fa-chevron-right"></i>';
            } else {
                workspace.style.marginLeft = `var(--sidebar-width)`;
                sidebarToggle.innerHTML = '<i class="fas fa-chevron-left"></i>';
            }
            
            // 切换侧边栏状态
            sidebarToggle.addEventListener('click', (e) => {
                e.stopPropagation(); // 防止事件冒泡
                const isCollapsed = sidebar.classList.toggle('sidebar-collapsed');
                
                if (isCollapsed) {
                    workspace.style.marginLeft = 'var(--sidebar-collapsed-width)';
                    sidebarToggle.innerHTML = '<i class="fas fa-chevron-right"></i>';
                    localStorage.setItem('sidebarCollapsed', 'true');
                } else {
                    workspace.style.marginLeft = `var(--sidebar-width)`;
                    sidebarToggle.innerHTML = '<i class="fas fa-chevron-left"></i>';
                    localStorage.setItem('sidebarCollapsed', 'false');
                }
            });
        }
        
        // 移动端菜单
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        
        if (mobileMenuToggle && sidebar) {
            mobileMenuToggle.addEventListener('click', () => {
                sidebar.classList.toggle('show');
                
                // 创建遮罩层
                let overlay = document.getElementById('mobile-overlay');
                
                if (!overlay) {
                    overlay = document.createElement('div');
                    overlay.id = 'mobile-overlay';
                    overlay.classList.add('mobile-overlay');
                    document.body.appendChild(overlay);
                    
                    // 点击遮罩关闭菜单
                    overlay.addEventListener('click', () => {
                        sidebar.classList.remove('show');
                        overlay.classList.remove('show');
                    });
                }
                
                overlay.classList.toggle('show');
            });
        }
        
        // 确保移动端遮罩不会覆盖侧边栏上的交互元素
        const handleResize = () => {
            if (window.innerWidth > 768) {
                // PC模式
                const sidebarCollapsed = sidebar && sidebar.classList.contains('sidebar-collapsed');
                if (workspace && sidebar) {
                    workspace.style.marginLeft = sidebarCollapsed ? 
                        'var(--sidebar-collapsed-width)' : 
                        'var(--sidebar-width)';
                }
                
                // 移除可能存在的移动端样式
                if (sidebar) {
                    sidebar.classList.remove('show');
                }
                const overlay = document.getElementById('mobile-overlay');
                if (overlay) {
                    overlay.classList.remove('show');
                }
            } else {
                // 移动模式
                if (workspace) {
                    workspace.style.marginLeft = '0';
                }
            }
        };
        
        window.addEventListener('resize', handleResize);
        // 初始调用一次
        handleResize();
        
        // 自动隐藏提示
        const alerts = document.querySelectorAll('.alert');
        
        alerts.forEach(alert => {
            // 关闭按钮
            const closeBtn = alert.querySelector('.alert-close');
            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    alert.style.opacity = '0';
                    setTimeout(() => {
                        alert.style.display = 'none';
                    }, 300);
                });
            }
            
            // 5秒后自动隐藏
            setTimeout(() => {
                alert.style.opacity = '0';
                setTimeout(() => {
                    alert.style.display = 'none';
                }, 300);
            }, 5000);
        });
    </script>
    
    <!-- 自定义JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html> 