#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
用户管理API路由
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel, EmailStr
from typing import Optional, List
import logging

from app.core.database import get_db
from app.core.security import get_current_user_id
from app.models.user import User
from app.services.user_service import UserService
from app.models.system import OperationLog

logger = logging.getLogger(__name__)
router = APIRouter()
user_service = UserService()

# Pydantic模型
class UserCreate(BaseModel):
    email: EmailStr
    username: str
    password: str
    nickname: Optional[str] = ""
    role: Optional[str] = "user"

class UserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    username: Optional[str] = None
    nickname: Optional[str] = None
    is_active: Optional[bool] = None
    role: Optional[str] = None

class UserResponse(BaseModel):
    id: int
    email: str
    username: str
    nickname: str
    role: str
    is_active: bool
    created_at: str
    updated_at: str
    last_login_at: Optional[str] = None
    
    class Config:
        from_attributes = True

class UserListResponse(BaseModel):
    items: List[UserResponse]
    total: int
    page: int
    limit: int
    total_pages: int

@router.get("/", response_model=UserListResponse, summary="获取用户列表")
async def get_users(
    request: Request,
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    role: Optional[str] = Query(None, description="角色筛选"),
    is_active: Optional[bool] = Query(None, description="状态筛选"),
    db: Session = Depends(get_db)
):
    """获取用户列表（需要管理员权限）"""
    try:
        # 获取当前用户信息（支持Basic认证）
        from app.core.security import get_current_user
        current_user = get_current_user(request)

        # 检查权限：只有管理员可以获取用户列表
        if current_user.get("role") != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="需要管理员权限"
            )

        skip = (page - 1) * limit

        # 构建筛选条件
        filters = {}
        if search:
            filters['search'] = search
        if role:
            filters['role'] = role
        if is_active is not None:
            filters['is_active'] = is_active

        # 使用真实的用户服务获取数据
        users, total = user_service.get_users(db, skip=skip, limit=limit, **filters)

        user_list = []
        for user in users:
            user_list.append(UserResponse(
                id=user.id,
                email=user.email,
                username=user.username,
                nickname=user.nickname or "",
                role=user.role,
                is_active=user.is_active,
                created_at=user.created_at.isoformat(),
                updated_at=user.updated_at.isoformat(),
                last_login_at=user.last_login_at.isoformat() if user.last_login_at else None
            ))

        return UserListResponse(
            items=user_list,
            total=total,
            page=page,
            limit=limit,
            total_pages=(total + limit - 1) // limit
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户列表异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户列表失败"
        )

@router.post("/", response_model=UserResponse, summary="创建用户")
async def create_user(
    user_data: UserCreate,
    request: Request,
    db: Session = Depends(get_db)
):
    """创建新用户（需要管理员权限）"""
    try:
        # 获取当前用户信息（支持Basic认证）
        from app.core.security import get_current_user
        current_user = get_current_user(request)

        # 检查权限：只有管理员可以创建用户
        if current_user.get("role") != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="需要管理员权限"
            )

        # 检查邮箱是否已存在
        if user_service.get_user_by_email(db, user_data.email):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已存在"
            )

        # 检查用户名是否已存在
        if user_service.get_user_by_username(db, user_data.username):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在"
            )

        # 使用真实的用户服务创建用户
        user = user_service.create_user(db, user_data.dict())

        # 记录操作日志
        log = OperationLog(
            user_id=current_user["id"],
            action="create_user",
            description=f"创建用户: {user.username}",
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent", ""),
            status_code=201
        )
        db.add(log)
        db.commit()

        return UserResponse(
            id=user.id,
            email=user.email,
            username=user.username,
            nickname=user.nickname or "",
            role=user.role,
            is_active=user.is_active,
            created_at=user.created_at.isoformat(),
            updated_at=user.updated_at.isoformat(),
            last_login_at=user.last_login_at.isoformat() if user.last_login_at else None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建用户异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建用户失败"
        )

@router.get("/{user_id}", response_model=UserResponse, summary="获取用户详情")
async def get_user(
    user_id: int,
    request: Request,
    db: Session = Depends(get_db)
):
    """获取用户详情"""
    try:
        # 获取当前用户ID
        current_user_id = get_current_user_id(request)
        current_user = db.query(User).filter(User.id == current_user_id).first()

        # 检查权限：管理员可以查看所有用户，普通用户只能查看自己
        if not current_user or (current_user.role != "admin" and current_user.id != user_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此用户信息"
            )
        
        user = user_service.get_user_by_id(db, user_id)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        return UserResponse(
            id=user.id,
            email=user.email,
            username=user.username,
            nickname=user.nickname or "",
            role=user.role,
            is_active=user.is_active,
            created_at=user.created_at.isoformat(),
            updated_at=user.updated_at.isoformat(),
            last_login_at=user.last_login_at.isoformat() if user.last_login_at else None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户详情异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户详情失败"
        )

@router.put("/{user_id}", response_model=UserResponse, summary="更新用户")
async def update_user(
    user_id: int,
    user_data: UserUpdate,
    request: Request,
    db: Session = Depends(get_db)
):
    """更新用户信息"""
    try:
        # 获取当前用户ID
        current_user_id = get_current_user_id(request)
        current_user = db.query(User).filter(User.id == current_user_id).first()

        # 检查权限：管理员可以更新所有用户，普通用户只能更新自己的部分信息
        if not current_user or (current_user.role != "admin" and current_user.id != user_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权修改此用户信息"
            )
        
        # 普通用户不能修改角色和状态
        if current_user.role != "admin":
            user_data.role = None
            user_data.is_active = None
        
        update_data = user_data.dict(exclude_unset=True)
        
        # 检查邮箱和用户名唯一性
        if 'email' in update_data:
            existing_user = user_service.get_user_by_email(db, update_data['email'])
            if existing_user and existing_user.id != user_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="邮箱已存在"
                )
        
        if 'username' in update_data:
            existing_user = user_service.get_user_by_username(db, update_data['username'])
            if existing_user and existing_user.id != user_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="用户名已存在"
                )
        
        user = user_service.update_user(db, user_id, update_data)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 记录操作日志
        log = OperationLog(
            user_id=current_user.id,
            action="update_user",
            description=f"更新用户: {user.username}",
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent", ""),
            status_code=200
        )
        db.add(log)
        db.commit()
        
        return UserResponse(
            id=user.id,
            email=user.email,
            username=user.username,
            nickname=user.nickname or "",
            role=user.role,
            is_active=user.is_active,
            created_at=user.created_at.isoformat(),
            updated_at=user.updated_at.isoformat(),
            last_login_at=user.last_login_at.isoformat() if user.last_login_at else None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新用户异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新用户失败"
        )

@router.delete("/{user_id}", summary="删除用户")
async def delete_user(
    user_id: int,
    request: Request,
    db: Session = Depends(get_db)
):
    """删除用户（需要管理员权限）"""
    try:
        # 获取当前用户ID
        current_user_id = get_current_user_id(request)
        current_user = db.query(User).filter(User.id == current_user_id).first()

        # 检查权限
        if not current_user or current_user.role != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="需要管理员权限"
            )
        
        # 不能删除自己
        if current_user.id == user_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能删除自己"
            )
        
        success = user_service.delete_user(db, user_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 记录操作日志
        log = OperationLog(
            user_id=current_user.id,
            action="delete_user",
            description=f"删除用户: {user_id}",
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent", ""),
            status_code=200
        )
        db.add(log)
        db.commit()
        
        return {"message": "用户删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除用户异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除用户失败"
        )
