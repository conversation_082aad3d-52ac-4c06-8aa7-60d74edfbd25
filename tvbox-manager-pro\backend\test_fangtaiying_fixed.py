#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def test_fangtaiying_fixed():
    """测试修复后的饭太硬接口"""
    
    from app.services.interface_service import InterfaceService
    import json
    
    service = InterfaceService()
    url = "http://www.饭太硬.com/tv"
    
    print(f"测试修复后的饭太硬接口: {url}")
    
    try:
        # 完整解密流程
        print("=== 完整解密流程 ===")
        full_content, full_method = service.decryptor.decrypt_config_url(url)
        print(f"解密方法: {full_method}")
        print(f"内容长度: {len(full_content)}")
        print(f"内容前100字符: {repr(full_content[:100])}")
        
        # 检查是否是有效JSON
        try:
            config = json.loads(full_content)
            print(f"✅ JSON解析成功")
            if isinstance(config, dict):
                sites_count = len(config.get('sites', []))
                lives_count = len(config.get('lives', []))
                parses_count = len(config.get('parses', []))
                print(f"配置统计: {sites_count}个站点, {lives_count}个直播源, {parses_count}个解析器")
                
                if sites_count > 0:
                    print("✅ 成功！加密接口解析出了有效配置")
                    # 显示前几个站点名称
                    sites = config.get('sites', [])
                    print("前5个站点:")
                    for i, site in enumerate(sites[:5]):
                        print(f"  {i+1}. {site.get('name', '未知')}")
                else:
                    print("⚠️ 解析成功但没有站点数据")
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            print("这可能意味着解密没有成功")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fangtaiying_fixed()
