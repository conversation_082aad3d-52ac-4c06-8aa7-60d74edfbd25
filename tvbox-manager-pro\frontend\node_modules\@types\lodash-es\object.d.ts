import { default as assign } from "./assign";
import { default as assignIn } from "./assignIn";
import { default as assignInWith } from "./assignInWith";
import { default as assignWith } from "./assignWith";
import { default as at } from "./at";
import { default as create } from "./create";
import { default as defaults } from "./defaults";
import { default as defaultsDeep } from "./defaultsDeep";
import { default as entries } from "./entries";
import { default as entriesIn } from "./entriesIn";
import { default as extend } from "./extend";
import { default as extendWith } from "./extendWith";
import { default as findKey } from "./findKey";
import { default as findLastKey } from "./findLastKey";
import { default as forIn } from "./forIn";
import { default as forInRight } from "./forInRight";
import { default as forOwn } from "./forOwn";
import { default as forOwnRight } from "./forOwnRight";
import { default as functions } from "./functions";
import { default as functionsIn } from "./functionsIn";
import { default as get } from "./get";
import { default as has } from "./has";
import { default as hasIn } from "./hasIn";
import { default as invert } from "./invert";
import { default as invertBy } from "./invertBy";
import { default as invoke } from "./invoke";
import { default as keys } from "./keys";
import { default as keysIn } from "./keysIn";
import { default as mapKeys } from "./mapKeys";
import { default as mapValues } from "./mapValues";
import { default as merge } from "./merge";
import { default as mergeWith } from "./mergeWith";
import { default as omit } from "./omit";
import { default as omitBy } from "./omitBy";
import { default as pick } from "./pick";
import { default as pickBy } from "./pickBy";
import { default as result } from "./result";
import { default as set } from "./set";
import { default as setWith } from "./setWith";
import { default as toPairs } from "./toPairs";
import { default as toPairsIn } from "./toPairsIn";
import { default as transform } from "./transform";
import { default as unset } from "./unset";
import { default as update } from "./update";
import { default as updateWith } from "./updateWith";
import { default as values } from "./values";
import { default as valuesIn } from "./valuesIn";

export { default } from "./object.default";
