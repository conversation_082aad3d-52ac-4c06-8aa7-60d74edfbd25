#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库迁移脚本
添加缺失的字段和表
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text, inspect
from app.core.database import engine, SessionLocal
from app.models import user, tvbox, system
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_column_exists(table_name: str, column_name: str) -> bool:
    """检查列是否存在"""
    inspector = inspect(engine)
    columns = inspector.get_columns(table_name)
    return any(col['name'] == column_name for col in columns)

def check_table_exists(table_name: str) -> bool:
    """检查表是否存在"""
    inspector = inspect(engine)
    return table_name in inspector.get_table_names()

def migrate_database():
    """执行数据库迁移"""
    db = SessionLocal()
    
    try:
        logger.info("开始数据库迁移...")
        
        # 1. 检查并添加用户表的role字段
        if check_table_exists('users'):
            if not check_column_exists('users', 'role'):
                logger.info("添加users表的role字段...")
                db.execute(text("ALTER TABLE users ADD COLUMN role VARCHAR(20) DEFAULT 'user'"))
                db.commit()
                logger.info("✅ 添加role字段成功")
            else:
                logger.info("✅ users.role字段已存在")
        
        # 2. 检查并创建系统设置表
        if not check_table_exists('system_settings'):
            logger.info("创建system_settings表...")
            db.execute(text("""
                CREATE TABLE system_settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key VARCHAR(100) NOT NULL UNIQUE,
                    value TEXT,
                    description TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """))
            db.commit()
            logger.info("✅ 创建system_settings表成功")
        else:
            logger.info("✅ system_settings表已存在")
        
        # 3. 检查并创建操作日志表
        if not check_table_exists('operation_logs'):
            logger.info("创建operation_logs表...")
            db.execute(text("""
                CREATE TABLE operation_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    action VARCHAR(100) NOT NULL,
                    description TEXT,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    status_code INTEGER DEFAULT 200,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """))
            db.commit()
            logger.info("✅ 创建operation_logs表成功")
        else:
            logger.info("✅ operation_logs表已存在")
        
        # 4. 检查并创建订阅表
        if not check_table_exists('subscriptions'):
            logger.info("创建subscriptions表...")
            db.execute(text("""
                CREATE TABLE subscriptions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name VARCHAR(100) NOT NULL,
                    description TEXT,
                    user_id INTEGER NOT NULL,
                    access_key VARCHAR(100) UNIQUE NOT NULL,
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """))
            db.commit()
            logger.info("✅ 创建subscriptions表成功")
        else:
            logger.info("✅ subscriptions表已存在")
        
        # 5. 检查并创建订阅项表
        if not check_table_exists('subscription_items'):
            logger.info("创建subscription_items表...")
            db.execute(text("""
                CREATE TABLE subscription_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    subscription_id INTEGER NOT NULL,
                    interface_id INTEGER NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (subscription_id) REFERENCES subscriptions (id) ON DELETE CASCADE,
                    FOREIGN KEY (interface_id) REFERENCES interface_sources (id) ON DELETE CASCADE,
                    UNIQUE(subscription_id, interface_id)
                )
            """))
            db.commit()
            logger.info("✅ 创建subscription_items表成功")
        else:
            logger.info("✅ subscription_items表已存在")
        
        # 6. 插入默认系统设置
        if check_table_exists('system_settings'):
            logger.info("插入默认系统设置...")
            default_settings = [
                ('app_name', 'TVBox Manager Pro', '应用名称'),
                ('max_interfaces_per_user', '100', '每个用户最大接口数'),
                ('enable_registration', 'true', '是否允许注册'),
                ('default_user_role', 'user', '默认用户角色'),
                ('session_timeout', '3600', '会话超时时间（秒）')
            ]
            
            for key, value, description in default_settings:
                # 检查设置是否已存在
                result = db.execute(text("SELECT COUNT(*) FROM system_settings WHERE key = :key"), {"key": key}).scalar()
                if result == 0:
                    db.execute(text("""
                        INSERT INTO system_settings (key, value, description) 
                        VALUES (:key, :value, :description)
                    """), {"key": key, "value": value, "description": description})
            
            db.commit()
            logger.info("✅ 插入默认系统设置成功")
        
        # 7. 创建默认管理员用户
        if check_table_exists('users'):
            logger.info("检查默认管理员用户...")
            result = db.execute(text("SELECT COUNT(*) FROM users WHERE email = '<EMAIL>'")).scalar()
            if result == 0:
                from app.core.security import create_password_hash
                hashed_password = create_password_hash("admin123")
                
                db.execute(text("""
                    INSERT INTO users (username, email, password_hash, role, is_active, is_verified, is_superuser) 
                    VALUES (:username, :email, :password_hash, :role, :is_active, :is_verified, :is_superuser)
                """), {
                    "username": "admin",
                    "email": "<EMAIL>",
                    "password_hash": hashed_password,
                    "role": "admin",
                    "is_active": True,
                    "is_verified": True,
                    "is_superuser": True
                })
                db.commit()
                logger.info("✅ 创建默认管理员用户成功")
            else:
                logger.info("✅ 默认管理员用户已存在")
        
        logger.info("🎉 数据库迁移完成！")
        
    except Exception as e:
        logger.error(f"❌ 数据库迁移失败: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()

def main():
    """主函数"""
    try:
        migrate_database()
        print("\n" + "="*50)
        print("🎉 数据库迁移成功完成！")
        print("📝 默认管理员账号:")
        print("   邮箱: <EMAIL>")
        print("   密码: admin123")
        print("="*50)
    except Exception as e:
        print(f"\n❌ 迁移失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
