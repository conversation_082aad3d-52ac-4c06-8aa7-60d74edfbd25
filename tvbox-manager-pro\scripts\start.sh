#!/bin/bash

# TVBox Manager Pro 启动脚本

set -e

echo "🚀 启动 TVBox Manager Pro..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 检查端口是否被占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo "⚠️  端口 $port 已被占用"
        return 1
    fi
    return 0
}

echo "🔍 检查端口占用情况..."
if ! check_port 3000; then
    echo "请停止占用端口 3000 的进程后重试"
    exit 1
fi

if ! check_port 8000; then
    echo "请停止占用端口 8000 的进程后重试"
    exit 1
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p data/uploads
mkdir -p data/backups
mkdir -p data/cache
mkdir -p logs

# 设置环境变量
export COMPOSE_PROJECT_NAME=tvbox-manager-pro

# 构建并启动服务
echo "🔨 构建并启动服务..."
docker-compose up --build -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 显示访问信息
echo ""
echo "✅ TVBox Manager Pro 启动成功！"
echo ""
echo "📱 前端访问地址: http://localhost:3000"
echo "🔧 后端API地址: http://localhost:8000"
echo "📚 API文档地址: http://localhost:8000/docs"
echo ""
echo "👤 默认管理员账号:"
echo "   邮箱: <EMAIL>"
echo "   密码: admin123"
echo ""
echo "🛠️  常用命令:"
echo "   查看日志: docker-compose logs -f"
echo "   停止服务: docker-compose down"
echo "   重启服务: docker-compose restart"
echo ""
echo "🎉 享受使用 TVBox Manager Pro！"
