#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def test_xhztv_debug():
    """调试xhztv.top/4k.json接口问题"""
    
    from app.services.interface_service import InterfaceService
    import json
    
    service = InterfaceService()
    url = "http://xhztv.top/4k.json"
    
    print(f"调试xhztv接口: {url}")
    
    try:
        # 1. 获取原始内容
        normalized_url = service.decryptor._normalize_url(url)
        content, final_url = service.decryptor._get_json(normalized_url)
        print(f"原始内容长度: {len(content)}")
        print(f"原始内容前200字符: {repr(content[:200])}")
        
        # 2. 检查是否被识别为直接配置文件
        is_direct = service.decryptor._is_direct_config_file(normalized_url, content)
        print(f"是否识别为直接配置文件: {is_direct}")
        
        # 3. 如果是直接配置文件，测试处理逻辑
        if is_direct:
            print("=== 直接配置文件处理 ===")
            try:
                result_content, method = service.decryptor._handle_direct_config_file(content, normalized_url)
                print(f"处理方法: {method}")
                print(f"处理后内容长度: {len(result_content)}")
                print(f"处理后内容前200字符: {repr(result_content[:200])}")
                
                # 检查是否是有效JSON
                try:
                    config = json.loads(result_content)
                    print(f"✅ 处理后JSON解析成功")
                    if isinstance(config, dict):
                        sites_count = len(config.get('sites', []))
                        print(f"站点数: {sites_count}")
                except json.JSONDecodeError as e:
                    print(f"❌ 处理后JSON解析失败: {e}")
                    
            except Exception as handle_e:
                print(f"❌ 直接配置文件处理失败: {handle_e}")
                import traceback
                traceback.print_exc()
        
        # 4. 完整解密流程
        print(f"\n=== 完整解密流程 ===")
        full_content, full_method = service.decryptor.decrypt_config_url(url)
        print(f"完整解密方法: {full_method}")
        print(f"完整解密内容长度: {len(full_content)}")
        print(f"完整解密内容前200字符: {repr(full_content[:200])}")
        
        # 5. 检查最终结果
        try:
            final_config = json.loads(full_content)
            print(f"✅ 最终JSON解析成功")
            if isinstance(final_config, dict):
                sites_count = len(final_config.get('sites', []))
                lives_count = len(final_config.get('lives', []))
                parses_count = len(final_config.get('parses', []))
                print(f"最终配置统计: {sites_count}个站点, {lives_count}个直播源, {parses_count}个解析器")
                
                if sites_count == 0:
                    print("⚠️ 警告：没有解析出任何站点！")
                    # 检查是否有其他字段
                    print(f"配置包含的字段: {list(final_config.keys())}")
                else:
                    print("✅ 成功解析出站点数据")
        except json.JSONDecodeError as e:
            print(f"❌ 最终JSON解析失败: {e}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_xhztv_debug()
