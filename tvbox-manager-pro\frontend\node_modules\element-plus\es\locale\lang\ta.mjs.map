{"version": 3, "file": "ta.mjs", "sources": ["../../../../../packages/locale/lang/ta.ts"], "sourcesContent": ["export default {\n  name: 'ta',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'உறுதி செய்',\n      clear: 'தெளிவாக்கு',\n    },\n    datepicker: {\n      now: 'தற்போது',\n      today: 'இன்று',\n      cancel: 'ரத்து செய்',\n      clear: 'சரி',\n      confirm: 'உறுதி செய்',\n      selectDate: 'தேதியை தேர்வு செய்',\n      selectTime: 'நேரத்தை தேர்வு செய்',\n      startDate: 'தொடங்கும் நாள்',\n      startTime: 'தொடங்கும் நேரம்',\n      endDate: 'முடியும் தேதி',\n      endTime: 'முடியும் நேரம்',\n      prevYear: 'Previous Year', // to be translated\n      nextYear: 'Next Year', // to be translated\n      prevMonth: 'Previous Month', // to be translated\n      nextMonth: 'Next Month', // to be translated\n      year: 'வருடம்',\n      month1: 'ஜனவரி',\n      month2: 'பிப்ரவரி',\n      month3: 'மார்ச்',\n      month4: 'ஏப்ரல்',\n      month5: 'மே',\n      month6: 'ஜூன்',\n      month7: 'ஜூலை',\n      month8: 'ஆகஸ்ட்',\n      month9: 'செப்டம்பர்',\n      month10: 'அக்டோபர்',\n      month11: 'நவம்பர்',\n      month12: 'டிசம்பர்',\n      weeks: {\n        sun: 'ஞாயிறு',\n        mon: 'திங்கள்',\n        tue: 'செவ்வாய்',\n        wed: 'புதன்',\n        thu: 'வியாழன்',\n        fri: 'வெள்ளி',\n        sat: 'சனி',\n      },\n      months: {\n        jan: 'ஜனவரி',\n        feb: 'பிப்ரவரி',\n        mar: 'மார்ச்',\n        apr: 'ஏப்ரல்',\n        may: 'மே',\n        jun: 'ஜூன்',\n        jul: 'ஜூலை',\n        aug: 'ஆகஸ்ட்',\n        sep: 'செப்டம்பர்',\n        oct: 'அக்டோபர்',\n        nov: 'நவம்பர்',\n        dec: 'டிசம்பர்',\n      },\n    },\n    select: {\n      loading: 'தயாராகிக்கொண்டிருக்கிறது',\n      noMatch: 'பொருத்தமான தரவு கிடைக்கவில்லை',\n      noData: 'தரவு இல்லை',\n      placeholder: 'தேர்வு செய்',\n    },\n    mention: {\n      loading: 'தயாராகிக்கொண்டிருக்கிறது',\n    },\n    cascader: {\n      noMatch: 'பொருத்தமான தரவு கிடைக்கவில்லை',\n      loading: 'தயாராகிக்கொண்டிருக்கிறது',\n      placeholder: 'தேர்வு செய்',\n      noData: 'தரவு இல்லை',\n    },\n    pagination: {\n      goto: 'தேவையான் பகுதிக்கு செல்',\n      pagesize: '/page',\n      total: 'மொத்தம் {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'செய்தி',\n      confirm: 'உறுதி செய்',\n      cancel: 'ரத்து செய்',\n      error: 'பொருத்தாமில்லாத உள்ளீடு',\n    },\n    upload: {\n      deleteTip: 'press delete to remove', // to be translated\n      delete: 'நீக்கு',\n      preview: 'முன்னோட்டம் பார்',\n      continue: 'தொடரு',\n    },\n    table: {\n      emptyText: 'தரவு இல்லை',\n      confirmFilter: 'உறுதி செய்',\n      resetFilter: 'புதுமாற்றம் செய்',\n      clearFilter: 'அனைத்தும்',\n      sumText: 'கூட்டு',\n    },\n    tree: {\n      emptyText: 'தரவு இல்லை',\n    },\n    transfer: {\n      noMatch: 'பொருத்தமான தரவு கிடைக்கவில்லை',\n      noData: 'தரவு இல்லை',\n      titles: ['பட்டியல் 1', 'பட்டியல் 2'],\n      filterPlaceholder: 'சொல்லை உள்ளீடு செய்',\n      noCheckedFormat: '{total} items', // to be translated\n      hasCheckedFormat: '{checked}/{total} தேர்வு செய்யப்பட்டவைகள்',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,yDAAyD;AACxE,MAAM,KAAK,EAAE,8DAA8D;AAC3E,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,4CAA4C;AACvD,MAAM,KAAK,EAAE,gCAAgC;AAC7C,MAAM,MAAM,EAAE,yDAAyD;AACvE,MAAM,KAAK,EAAE,oBAAoB;AACjC,MAAM,OAAO,EAAE,yDAAyD;AACxE,MAAM,UAAU,EAAE,oGAAoG;AACtH,MAAM,UAAU,EAAE,0GAA0G;AAC5H,MAAM,SAAS,EAAE,iFAAiF;AAClG,MAAM,SAAS,EAAE,uFAAuF;AACxG,MAAM,OAAO,EAAE,2EAA2E;AAC1F,MAAM,OAAO,EAAE,iFAAiF;AAChG,MAAM,QAAQ,EAAE,eAAe;AAC/B,MAAM,QAAQ,EAAE,WAAW;AAC3B,MAAM,SAAS,EAAE,gBAAgB;AACjC,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,IAAI,EAAE,sCAAsC;AAClD,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,kDAAkD;AAChE,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,8DAA8D;AAC5E,MAAM,OAAO,EAAE,kDAAkD;AACjE,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,OAAO,EAAE,kDAAkD;AACjE,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,4CAA4C;AACzD,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,4CAA4C;AACzD,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,oBAAoB;AACjC,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,8DAA8D;AAC3E,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,4CAA4C;AACzD,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,kJAAkJ;AACjK,MAAM,OAAO,EAAE,sKAAsK;AACrL,MAAM,MAAM,EAAE,yDAAyD;AACvE,MAAM,WAAW,EAAE,+DAA+D;AAClF,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,kJAAkJ;AACjK,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,sKAAsK;AACrL,MAAM,OAAO,EAAE,kJAAkJ;AACjK,MAAM,WAAW,EAAE,+DAA+D;AAClF,MAAM,MAAM,EAAE,yDAAyD;AACvE,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,kIAAkI;AAC9I,MAAM,QAAQ,EAAE,OAAO;AACvB,MAAM,KAAK,EAAE,oDAAoD;AACjE,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,sCAAsC;AACnD,MAAM,OAAO,EAAE,yDAAyD;AACxE,MAAM,MAAM,EAAE,yDAAyD;AACvE,MAAM,KAAK,EAAE,uIAAuI;AACpJ,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,OAAO,EAAE,6FAA6F;AAC5G,MAAM,QAAQ,EAAE,gCAAgC;AAChD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,yDAAyD;AAC1E,MAAM,aAAa,EAAE,yDAAyD;AAC9E,MAAM,WAAW,EAAE,6FAA6F;AAChH,MAAM,WAAW,EAAE,wDAAwD;AAC3E,MAAM,OAAO,EAAE,sCAAsC;AACrD,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,yDAAyD;AAC1E,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,sKAAsK;AACrL,MAAM,MAAM,EAAE,yDAAyD;AACvE,MAAM,MAAM,EAAE,CAAC,oDAAoD,EAAE,oDAAoD,CAAC;AAC1H,MAAM,iBAAiB,EAAE,0GAA0G;AACnI,MAAM,eAAe,EAAE,eAAe;AACtC,MAAM,gBAAgB,EAAE,yJAAyJ;AACjL,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,MAAM;AACnB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}