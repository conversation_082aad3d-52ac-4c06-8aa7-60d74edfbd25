{"version": 3, "file": "fa.min.mjs", "sources": ["../../../../packages/locale/lang/fa.ts"], "sourcesContent": ["export default {\n  name: 'fa',\n  el: {\n    breadcrumb: {\n      label: 'مسیر راهنما',\n    },\n    colorpicker: {\n      confirm: 'تأیید',\n      clear: 'پاک کردن',\n      defaultLabel: 'انتخاب‌گر رنگ',\n      description:\n        'رنگ فعلی {color} است. برای انتخاب رنگ جدید، اینتر را فشار دهید.',\n      alphaLabel: 'مقدار آلفا را انتخاب کنید',\n    },\n    datepicker: {\n      now: 'اکنون',\n      today: 'امروز',\n      cancel: 'لغو',\n      clear: 'پاک کردن',\n      confirm: 'تأیید',\n      dateTablePrompt:\n        'از کلیدهای جهت‌دار و اینتر برای انتخاب روز ماه استفاده کنید',\n      monthTablePrompt:\n        'از کلیدهای جهت‌دار و اینتر برای انتخاب ماه استفاده کنید',\n      yearTablePrompt:\n        'از کلیدهای جهت‌دار و اینتر برای انتخاب سال استفاده کنید',\n      selectedDate: 'تاریخ انتخاب‌شده',\n      selectDate: 'انتخاب تاریخ',\n      selectTime: 'انتخاب زمان',\n      startDate: 'تاریخ شروع',\n      startTime: 'زمان شروع',\n      endDate: 'تاریخ پایان',\n      endTime: 'زمان پایان',\n      prevYear: 'سال قبل',\n      nextYear: 'سال بعد',\n      prevMonth: 'ماه قبل',\n      nextMonth: 'ماه بعد',\n      year: '',\n      month1: 'ژانویه',\n      month2: 'فوریه',\n      month3: 'مارس',\n      month4: 'آوریل',\n      month5: 'مه',\n      month6: 'ژوئن',\n      month7: 'ژوئیه',\n      month8: 'اوت',\n      month9: 'سپتامبر',\n      month10: 'اکتبر',\n      month11: 'نوامبر',\n      month12: 'دسامبر',\n      week: 'هفته',\n      weeks: {\n        sun: 'یک‌شنبه',\n        mon: 'دوشنبه',\n        tue: 'سه‌شنبه',\n        wed: 'چهارشنبه',\n        thu: 'پنج‌شنبه',\n        fri: 'جمعه',\n        sat: 'شنبه',\n      },\n      weeksFull: {\n        sun: 'یک‌شنبه',\n        mon: 'دوشنبه',\n        tue: 'سه‌شنبه',\n        wed: 'چهارشنبه',\n        thu: 'پنج‌شنبه',\n        fri: 'جمعه',\n        sat: 'شنبه',\n      },\n      months: {\n        jan: 'ژانویه',\n        feb: 'فوریه',\n        mar: 'مارچ',\n        apr: 'آوریل',\n        may: 'مه',\n        jun: 'ژوئن',\n        jul: 'ژوئیه',\n        aug: 'اوت',\n        sep: 'سپتامبر',\n        oct: 'اکتبر',\n        nov: 'نوامبر',\n        dec: 'دسامبر',\n      },\n    },\n    inputNumber: {\n      decrease: 'کاهش عدد',\n      increase: 'افزایش عدد',\n    },\n    select: {\n      loading: 'در حال بارگذاری',\n      noMatch: 'هیچ داده منطبقی وجود ندارد',\n      noData: 'داده‌ای موجود نیست',\n      placeholder: 'انتخاب کنید',\n    },\n    mention: {\n      loading: 'در حال بارگذاری',\n    },\n    dropdown: {\n      toggleDropdown: 'باز و بسته کردن منوی کشویی',\n    },\n    cascader: {\n      noMatch: 'هیچ داده منطبقی وجود ندارد',\n      loading: 'در حال بارگذاری',\n      placeholder: 'انتخاب کنید',\n      noData: 'داده‌ای موجود نیست',\n    },\n    pagination: {\n      goto: 'برو به',\n      pagesize: '/صفحه',\n      total: 'مجموع {total}',\n      pageClassifier: '',\n      page: 'صفحه',\n      prev: 'برو به صفحه قبلی',\n      next: 'برو به صفحه بعدی',\n      currentPage: 'صفحه {pager}',\n      prevPages: '{pager} صفحات قبلی',\n      nextPages: '{pager} صفحات بعدی',\n      deprecationWarning:\n        'استفاده‌های منسوخ شناسایی شد، لطفاً به مستندات el-pagination مراجعه کنید',\n    },\n    dialog: {\n      close: 'بستن این دیالوگ',\n    },\n    drawer: {\n      close: 'بستن این دیالوگ',\n    },\n    messagebox: {\n      title: 'پیام',\n      confirm: 'تأیید',\n      cancel: 'لغو',\n      error: 'ورودی نامعتبر',\n      close: 'بستن این دیالوگ',\n    },\n    upload: {\n      deleteTip: 'برای حذف، کلید delete را فشار دهید',\n      delete: 'حذف',\n      preview: 'پیش‌نمایش',\n      continue: 'ادامه',\n    },\n    slider: {\n      defaultLabel: 'لغزنده بین {min} و {max}',\n      defaultRangeStartLabel: 'انتخاب مقدار شروع',\n      defaultRangeEndLabel: 'انتخاب مقدار پایان',\n    },\n    table: {\n      emptyText: 'داده‌ای موجود نیست',\n      confirmFilter: 'تأیید',\n      resetFilter: 'بازنشانی',\n      clearFilter: 'همه',\n      sumText: 'مجموع',\n    },\n    tour: {\n      next: 'بعدی',\n      previous: 'قبلی',\n      finish: 'پایان',\n    },\n    tree: {\n      emptyText: 'داده‌ای موجود نیست',\n    },\n    transfer: {\n      noMatch: 'داده‌ای مطابقت ندارد',\n      noData: 'داده‌ای موجود نیست',\n      titles: ['فهرست ۱', 'فهرست ۲'],\n      filterPlaceholder: 'کلمه کلیدی را وارد کنید',\n      noCheckedFormat: '{total} آیتم',\n      hasCheckedFormat: '{checked}/{total} انتخاب‌شده',\n    },\n    image: {\n      error: 'ناموفق',\n    },\n    pageHeader: {\n      title: 'بازگشت',\n    },\n    popconfirm: {\n      confirmButtonText: 'بله',\n      cancelButtonText: 'خیر',\n    },\n    carousel: {\n      leftArrow: 'پیکان به جهت چپ',\n      rightArrow: 'پیکان چرخان به جهت راست',\n      indicator: 'سوئیچ چرخان به شاخص {index}',\n    },\n  },\n}\n"], "names": [], "mappings": ";;AAAA,SAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,+DAA+D,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,KAAK,CAAC,6CAA6C,CAAC,YAAY,CAAC,2EAA2E,CAAC,WAAW,CAAC,wRAAwR,CAAC,UAAU,CAAC,oIAAoI,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,gCAAgC,CAAC,KAAK,CAAC,gCAAgC,CAAC,MAAM,CAAC,oBAAoB,CAAC,KAAK,CAAC,6CAA6C,CAAC,OAAO,CAAC,gCAAgC,CAAC,eAAe,CAAC,kTAAkT,CAAC,gBAAgB,CAAC,+RAA+R,CAAC,eAAe,CAAC,+RAA+R,CAAC,YAAY,CAAC,6FAA6F,CAAC,UAAU,CAAC,qEAAqE,CAAC,UAAU,CAAC,+DAA+D,CAAC,SAAS,CAAC,yDAAyD,CAAC,SAAS,CAAC,mDAAmD,CAAC,OAAO,CAAC,+DAA+D,CAAC,OAAO,CAAC,yDAAyD,CAAC,QAAQ,CAAC,uCAAuC,CAAC,QAAQ,CAAC,uCAAuC,CAAC,SAAS,CAAC,uCAAuC,CAAC,SAAS,CAAC,uCAAuC,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,4CAA4C,CAAC,OAAO,CAAC,gCAAgC,CAAC,OAAO,CAAC,sCAAsC,CAAC,OAAO,CAAC,sCAAsC,CAAC,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,4CAA4C,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,4CAA4C,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,4CAA4C,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,4CAA4C,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,4CAA4C,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,6CAA6C,CAAC,QAAQ,CAAC,yDAAyD,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,kFAAkF,CAAC,OAAO,CAAC,0IAA0I,CAAC,MAAM,CAAC,oGAAoG,CAAC,WAAW,CAAC,+DAA+D,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,kFAAkF,CAAC,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,qIAAqI,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,0IAA0I,CAAC,OAAO,CAAC,kFAAkF,CAAC,WAAW,CAAC,+DAA+D,CAAC,MAAM,CAAC,oGAAoG,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,iCAAiC,CAAC,QAAQ,CAAC,2BAA2B,CAAC,KAAK,CAAC,wCAAwC,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,mFAAmF,CAAC,IAAI,CAAC,mFAAmF,CAAC,WAAW,CAAC,kCAAkC,CAAC,SAAS,CAAC,iEAAiE,CAAC,SAAS,CAAC,iEAAiE,CAAC,kBAAkB,CAAC,oUAAoU,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,kFAAkF,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,kFAAkF,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,0BAA0B,CAAC,OAAO,CAAC,gCAAgC,CAAC,MAAM,CAAC,oBAAoB,CAAC,KAAK,CAAC,2EAA2E,CAAC,KAAK,CAAC,kFAAkF,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,kJAAkJ,CAAC,MAAM,CAAC,oBAAoB,CAAC,OAAO,CAAC,wDAAwD,CAAC,QAAQ,CAAC,gCAAgC,CAAC,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,4EAA4E,CAAC,sBAAsB,CAAC,8FAA8F,CAAC,oBAAoB,CAAC,oGAAoG,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,oGAAoG,CAAC,aAAa,CAAC,gCAAgC,CAAC,WAAW,CAAC,kDAAkD,CAAC,WAAW,CAAC,oBAAoB,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,0BAA0B,CAAC,MAAM,CAAC,gCAAgC,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,oGAAoG,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,gHAAgH,CAAC,MAAM,CAAC,oGAAoG,CAAC,MAAM,CAAC,CAAC,uCAAuC,CAAC,uCAAuC,CAAC,CAAC,iBAAiB,CAAC,wHAAwH,CAAC,eAAe,CAAC,kCAAkC,CAAC,gBAAgB,CAAC,gFAAgF,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,6EAA6E,CAAC,UAAU,CAAC,wHAAwH,CAAC,SAAS,CAAC,6GAA6G,CAAC,CAAC,CAAC;;;;"}