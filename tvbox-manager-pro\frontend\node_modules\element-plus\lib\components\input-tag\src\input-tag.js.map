{"version": 3, "file": "input-tag.js", "sources": ["../../../../../../packages/components/input-tag/src/input-tag.ts"], "sourcesContent": ["import {\n  buildProps,\n  definePropType,\n  isArray,\n  isString,\n  isUndefined,\n} from '@element-plus/utils'\nimport { useSizeProp } from '@element-plus/hooks'\nimport {\n  CHANGE_EVENT,\n  EVENT_CODE,\n  INPUT_EVENT,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { tagProps } from '@element-plus/components/tag/src/tag'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\n\nexport const inputTagProps = buildProps({\n  /**\n   * @description binding value\n   */\n  modelValue: {\n    type: definePropType<string[]>(Array),\n  },\n  /**\n   * @description max number tags that can be enter\n   */\n  max: Number,\n  /**\n   * @description tag type\n   */\n  tagType: { ...tagProps.type, default: 'info' },\n  /**\n   * @description tag effect\n   */\n  tagEffect: tagProps.effect,\n  /**\n   * @description the key to trigger input tag\n   */\n  trigger: {\n    type: definePropType<'Enter' | 'Space'>(String),\n    default: EVENT_CODE.enter,\n  },\n  /**\n   * @description whether tags can be dragged\n   */\n  draggable: Boolean,\n  /**\n   * @description add a tag when a delimiter is matched\n   */\n  delimiter: {\n    type: [String, RegExp],\n    default: '',\n  },\n  /**\n   * @description input box size\n   */\n  size: useSizeProp,\n  /**\n   * @description whether to show clear button\n   */\n  clearable: Boolean,\n  /**\n   * @description whether to disable input-tag\n   */\n  disabled: {\n    type: Boolean,\n    default: undefined,\n  },\n  /**\n   * @description whether to trigger form validation\n   */\n  validateEvent: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description native input readonly\n   */\n  readonly: Boolean,\n  /**\n   * @description native input autofocus\n   */\n  autofocus: Boolean,\n  /**\n   * @description same as `id` in native input\n   */\n  id: {\n    type: String,\n    default: undefined,\n  },\n  /**\n   * @description same as `tabindex` in native input\n   */\n  tabindex: {\n    type: [String, Number],\n    default: 0,\n  },\n  /**\n   * @description same as `maxlength` in native input\n   */\n  maxlength: {\n    type: [String, Number],\n  },\n  /**\n   * @description same as `minlength` in native input\n   */\n  minlength: {\n    type: [String, Number],\n  },\n  /**\n   * @description placeholder of input\n   */\n  placeholder: String,\n  /**\n   * @description native input autocomplete\n   */\n  autocomplete: {\n    type: String,\n    default: 'off',\n  },\n  /**\n   * @description whether to save the input value when the input loses focus\n   */\n  saveOnBlur: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description native `aria-label` attribute\n   */\n  ariaLabel: String,\n} as const)\nexport type InputTagProps = ExtractPropTypes<typeof inputTagProps>\nexport type InputTagPropsPublic = __ExtractPublicPropTypes<typeof inputTagProps>\n\nexport const inputTagEmits = {\n  [UPDATE_MODEL_EVENT]: (value?: string[]) =>\n    isArray(value) || isUndefined(value),\n  [CHANGE_EVENT]: (value?: string[]) => isArray(value) || isUndefined(value),\n  [INPUT_EVENT]: (value: string) => isString(value),\n  'add-tag': (value: string | string[]) => isString(value) || isArray(value),\n  'remove-tag': (value: string) => isString(value),\n  focus: (evt: FocusEvent) => evt instanceof FocusEvent,\n  blur: (evt: FocusEvent) => evt instanceof FocusEvent,\n  clear: () => true,\n}\nexport type InputTagEmits = typeof inputTagEmits\n"], "names": ["buildProps", "definePropType", "tagProps", "EVENT_CODE", "useSizeProp", "UPDATE_MODEL_EVENT", "isArray", "isUndefined", "CHANGE_EVENT", "INPUT_EVENT", "isString"], "mappings": ";;;;;;;;;;;;AAeY,MAAC,aAAa,GAAGA,kBAAU,CAAC;AACxC,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAEC,sBAAc,CAAC,KAAK,CAAC;AAC/B,GAAG;AACH,EAAE,GAAG,EAAE,MAAM;AACb,EAAE,OAAO,EAAE,EAAE,GAAGC,YAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE;AAChD,EAAE,SAAS,EAAEA,YAAQ,CAAC,MAAM;AAC5B,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAED,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAEE,eAAU,CAAC,KAAK;AAC7B,GAAG;AACH,EAAE,SAAS,EAAE,OAAO;AACpB,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,IAAI,EAAEC,iBAAW;AACnB,EAAE,SAAS,EAAE,OAAO;AACpB,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,SAAS,EAAE,OAAO;AACpB,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,GAAG;AACH,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,SAAS,EAAE,MAAM;AACnB,CAAC,EAAE;AACS,MAAC,aAAa,GAAG;AAC7B,EAAE,CAACC,wBAAkB,GAAG,CAAC,KAAK,KAAKC,cAAO,CAAC,KAAK,CAAC,IAAIC,iBAAW,CAAC,KAAK,CAAC;AACvE,EAAE,CAACC,kBAAY,GAAG,CAAC,KAAK,KAAKF,cAAO,CAAC,KAAK,CAAC,IAAIC,iBAAW,CAAC,KAAK,CAAC;AACjE,EAAE,CAACE,iBAAW,GAAG,CAAC,KAAK,KAAKC,eAAQ,CAAC,KAAK,CAAC;AAC3C,EAAE,SAAS,EAAE,CAAC,KAAK,KAAKA,eAAQ,CAAC,KAAK,CAAC,IAAIJ,cAAO,CAAC,KAAK,CAAC;AACzD,EAAE,YAAY,EAAE,CAAC,KAAK,KAAKI,eAAQ,CAAC,KAAK,CAAC;AAC1C,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,UAAU;AAC3C,EAAE,IAAI,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,UAAU;AAC1C,EAAE,KAAK,EAAE,MAAM,IAAI;AACnB;;;;;"}