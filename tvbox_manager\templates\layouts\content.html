<!-- 工作区 -->
<main class="workspace" id="workspace">
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="flash-messages">
                {% for category, message in messages %}
                    {% set alert_class = "alert-" + category if category in ['success', 'danger', 'warning', 'info'] else "alert-info" %}
                    <div class="alert {{ alert_class }} fade-in">
                        <div class="alert-icon">
                            {% if category == 'success' %}
                                <i class="fas fa-check-circle"></i>
                            {% elif category == 'danger' or category == 'error' %}
                                <i class="fas fa-exclamation-circle"></i>
                            {% elif category == 'warning' %}
                                <i class="fas fa-exclamation-triangle"></i>
                            {% else %}
                                <i class="fas fa-info-circle"></i>
                            {% endif %}
                        </div>
                        <div class="alert-content">
                            {{ message }}
                        </div>
                        <button class="alert-close" type="button">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}
    
    <!-- 内容区域 -->
    {% block content %}{% endblock %}
</main> 
<main class="workspace" id="workspace">
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="flash-messages">
                {% for category, message in messages %}
                    {% set alert_class = "alert-" + category if category in ['success', 'danger', 'warning', 'info'] else "alert-info" %}
                    <div class="alert {{ alert_class }} fade-in">
                        <div class="alert-icon">
                            {% if category == 'success' %}
                                <i class="fas fa-check-circle"></i>
                            {% elif category == 'danger' or category == 'error' %}
                                <i class="fas fa-exclamation-circle"></i>
                            {% elif category == 'warning' %}
                                <i class="fas fa-exclamation-triangle"></i>
                            {% else %}
                                <i class="fas fa-info-circle"></i>
                            {% endif %}
                        </div>
                        <div class="alert-content">
                            {{ message }}
                        </div>
                        <button class="alert-close" type="button">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}
    
    <!-- 内容区域 -->
    {% block content %}{% endblock %}
</main> 