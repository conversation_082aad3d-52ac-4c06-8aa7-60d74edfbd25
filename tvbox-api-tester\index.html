<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TVBox API Web 测试工具</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Prism.js CSS -->
    <link href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism-tomorrow.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-gear-fill me-2"></i>
                TVBox API 测试工具
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item">
                    <span class="navbar-text me-3">
                        服务器状态: <span id="serverStatus" class="badge bg-secondary">未连接</span>
                    </span>
                </div>
                <button class="btn btn-outline-light btn-sm" onclick="testConnection()">
                    <i class="bi bi-arrow-clockwise"></i> 测试连接
                </button>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-3">
        <div class="row">
            <!-- 左侧边栏 -->
            <div class="col-md-3">
                <!-- 服务器配置 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <i class="bi bi-server"></i> 服务器配置
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">API地址</label>
                            <input type="text" class="form-control" id="apiBaseUrl" 
                                   value="http://localhost:8001" placeholder="http://localhost:8001">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">超时时间 (秒)</label>
                            <input type="number" class="form-control" id="timeout" value="30" min="5" max="300">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">用户邮箱</label>
                            <input type="email" class="form-control" id="userEmail"
                                   value="<EMAIL>" placeholder="<EMAIL>">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">用户密码</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="userPassword"
                                       value="admin123" placeholder="admin123">
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility()">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                        </div>
                        <button class="btn btn-primary w-100" onclick="saveConfig()">
                            <i class="bi bi-check-lg"></i> 保存配置
                        </button>
                    </div>
                </div>

                <!-- API分类 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <i class="bi bi-list-ul"></i> API分类
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush">
                            <a href="#" class="list-group-item list-group-item-action active" 
                               onclick="selectCategory('auth')">
                                <i class="bi bi-shield-lock"></i> 认证API
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" 
                               onclick="selectCategory('interfaces')">
                                <i class="bi bi-link-45deg"></i> 接口管理
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" 
                               onclick="selectCategory('subscriptions')">
                                <i class="bi bi-star"></i> 订阅管理
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" 
                               onclick="selectCategory('users')">
                                <i class="bi bi-people"></i> 用户管理
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" 
                               onclick="selectCategory('system')">
                                <i class="bi bi-gear"></i> 系统管理
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" 
                               onclick="selectCategory('decrypt')">
                                <i class="bi bi-unlock"></i> 解密API
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="card">
                    <div class="card-header">
                        <i class="bi bi-lightning"></i> 快速操作
                    </div>
                    <div class="card-body">
                        <button class="btn btn-success w-100 mb-2" onclick="runAllTests()">
                            <i class="bi bi-play-fill"></i> 运行所有测试
                        </button>
                        <button class="btn btn-info w-100 mb-2" onclick="exportResults()">
                            <i class="bi bi-download"></i> 导出结果
                        </button>
                        <button class="btn btn-warning w-100 mb-2" onclick="clearResults()">
                            <i class="bi bi-trash"></i> 清空结果
                        </button>
                        <button class="btn btn-secondary w-100" onclick="loadApiDocs()">
                            <i class="bi bi-book"></i> API文档
                        </button>
                    </div>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-9">
                <!-- 标签页 -->
                <ul class="nav nav-tabs" id="mainTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="manual-tab" data-bs-toggle="tab" 
                                data-bs-target="#manual" type="button" role="tab">
                            <i class="bi bi-hand-index"></i> 手动测试
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="auto-tab" data-bs-toggle="tab" 
                                data-bs-target="#auto" type="button" role="tab">
                            <i class="bi bi-robot"></i> 自动测试
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="results-tab" data-bs-toggle="tab" 
                                data-bs-target="#results" type="button" role="tab">
                            <i class="bi bi-clipboard-data"></i> 测试结果
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="logs-tab" data-bs-toggle="tab" 
                                data-bs-target="#logs" type="button" role="tab">
                            <i class="bi bi-journal-text"></i> 日志
                        </button>
                    </li>
                </ul>

                <!-- 标签页内容 -->
                <div class="tab-content" id="mainTabContent">
                    <!-- 手动测试 -->
                    <div class="tab-pane fade show active" id="manual" role="tabpanel">
                        <div class="card mt-3">
                            <div class="card-header">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <h5 class="mb-0">
                                            <i class="bi bi-hand-index"></i> 手动测试
                                        </h5>
                                    </div>
                                    <div class="col-auto">
                                        <select class="form-select" id="apiSelect" onchange="selectApi()">
                                            <option value="">选择API接口</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="manualTestForm">
                                    <div class="alert alert-info">
                                        <i class="bi bi-info-circle"></i> 
                                        请先选择左侧的API分类，然后选择具体的API接口进行测试。
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 自动测试 -->
                    <div class="tab-pane fade" id="auto" role="tabpanel">
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-robot"></i> 自动测试
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label class="form-label">并发数</label>
                                        <input type="number" class="form-control" id="concurrency" value="1" min="1" max="10">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">延迟 (毫秒)</label>
                                        <input type="number" class="form-control" id="delay" value="1000" min="0" max="10000">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">重试次数</label>
                                        <input type="number" class="form-control" id="retries" value="1" min="0" max="5">
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <h6>选择测试范围</h6>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="testAuth" checked>
                                        <label class="form-check-label" for="testAuth">认证API</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="testInterfaces" checked>
                                        <label class="form-check-label" for="testInterfaces">接口管理</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="testSubscriptions" checked>
                                        <label class="form-check-label" for="testSubscriptions">订阅管理</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="testUsers">
                                        <label class="form-check-label" for="testUsers">用户管理</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="testSystem">
                                        <label class="form-check-label" for="testSystem">系统管理</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="testDecrypt" checked>
                                        <label class="form-check-label" for="testDecrypt">解密API</label>
                                    </div>
                                </div>

                                <div class="d-grid gap-2">
                                    <button class="btn btn-success btn-lg" id="startAutoTest" onclick="startAutoTest()">
                                        <i class="bi bi-play-fill"></i> 开始自动测试
                                    </button>
                                    <button class="btn btn-danger" id="stopAutoTest" onclick="stopAutoTest()" style="display: none;">
                                        <i class="bi bi-stop-fill"></i> 停止测试
                                    </button>
                                </div>

                                <div class="mt-3" id="autoTestProgress" style="display: none;">
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                    </div>
                                    <div class="mt-2 text-center">
                                        <span id="progressText">准备中...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 测试结果 -->
                    <div class="tab-pane fade" id="results" role="tabpanel">
                        <div class="card mt-3">
                            <div class="card-header">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <h5 class="mb-0">
                                            <i class="bi bi-clipboard-data"></i> 测试结果
                                        </h5>
                                    </div>
                                    <div class="col-auto">
                                        <button class="btn btn-outline-primary btn-sm" onclick="refreshResults()">
                                            <i class="bi bi-arrow-clockwise"></i> 刷新
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="resultsContent">
                                    <div class="alert alert-info">
                                        <i class="bi bi-info-circle"></i> 
                                        暂无测试结果，请先运行测试。
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 日志 -->
                    <div class="tab-pane fade" id="logs" role="tabpanel">
                        <div class="card mt-3">
                            <div class="card-header">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <h5 class="mb-0">
                                            <i class="bi bi-journal-text"></i> 实时日志
                                        </h5>
                                    </div>
                                    <div class="col-auto">
                                        <button class="btn btn-outline-danger btn-sm" onclick="clearLogs()">
                                            <i class="bi bi-trash"></i> 清空日志
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="logsContent" class="logs-container">
                                    <!-- 日志内容将在这里显示 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 响应模态框 -->
    <div class="modal fade" id="responseModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">API响应详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="responseContent"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="copyResponse()">复制响应</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript 库 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios@1.6.0/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    
    <!-- 自定义脚本 -->
    <script src="js/config.js"></script>
    <script src="js/api-definitions.js"></script>
    <script src="js/manual-test.js"></script>
    <script src="js/auto-test.js"></script>
    <script src="js/results.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
