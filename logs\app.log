2025-07-25 16:07:46,600 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 16:07:46,765 [INFO] root:48 - 数据库表创建完成
2025-07-25 16:07:47,157 [INFO] app.services.user_service:78 - 默认管理员用户创建成功: <EMAIL>
2025-07-25 16:07:47,159 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 16:08:00,184 [INFO] root:149 - 请求开始: GET http://127.0.0.1:8000/docs
2025-07-25 16:08:00,185 [INFO] root:158 - 请求完成: GET http://127.0.0.1:8000/docs 状态码: 200 耗时: 0.001s
2025-07-25 16:08:02,117 [INFO] root:149 - 请求开始: GET http://127.0.0.1:8000/openapi.json
2025-07-25 16:08:02,159 [INFO] root:158 - 请求完成: GET http://127.0.0.1:8000/openapi.json 状态码: 200 耗时: 0.041s
2025-07-25 16:08:17,090 [INFO] root:149 - 请求开始: POST http://127.0.0.1:8000/api/v1/auth/login
2025-07-25 16:08:17,109 [ERROR] app.core.database:75 - 数据库会话错误: 401: 邮箱或密码错误
2025-07-25 16:08:17,110 [INFO] root:158 - 请求完成: POST http://127.0.0.1:8000/api/v1/auth/login 状态码: 401 耗时: 0.020s
2025-07-25 16:08:29,025 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/auth/login
2025-07-25 16:08:29,361 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/auth/login 状态码: 200 耗时: 0.336s
2025-07-25 16:08:37,291 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/auth/me?_t=1753430917286
2025-07-25 16:08:37,292 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/auth/me?_t=1753430917286 状态码: 200 耗时: 0.001s
2025-07-25 16:08:37,295 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/auth/me?_t=1753430917286
2025-07-25 16:08:37,308 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/auth/me?_t=1753430917286 状态码: 200 耗时: 0.012s
2025-07-25 16:08:48,503 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/auth/me?_t=1753430928180
2025-07-25 16:08:48,505 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/auth/me?_t=1753430928180 状态码: 200 耗时: 0.002s
2025-07-25 16:08:48,526 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/auth/me?_t=1753430928180
2025-07-25 16:08:48,540 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/auth/me?_t=1753430928180 状态码: 200 耗时: 0.014s
2025-07-25 16:10:02,274 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/auth/me?_t=1753431001962
2025-07-25 16:10:02,274 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/auth/me?_t=1753431001962 状态码: 200 耗时: 0.000s
2025-07-25 16:10:02,277 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/auth/me?_t=1753431001962
2025-07-25 16:10:02,282 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/auth/me?_t=1753431001962 状态码: 200 耗时: 0.005s
2025-07-25 16:10:08,409 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/auth/me?_t=1753431008402
2025-07-25 16:10:08,410 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/auth/me?_t=1753431008402 状态码: 200 耗时: 0.001s
2025-07-25 16:10:08,444 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/auth/me?_t=1753431008402
2025-07-25 16:10:08,454 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/auth/me?_t=1753431008402 状态码: 200 耗时: 0.010s
2025-07-25 16:10:58,105 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/auth/logout
2025-07-25 16:10:58,125 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/auth/logout 状态码: 200 耗时: 0.020s
2025-07-25 16:10:58,154 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/auth/logout
2025-07-25 16:10:58,316 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/auth/logout 状态码: 200 耗时: 0.162s
2025-07-25 16:11:09,457 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/auth/login
2025-07-25 16:11:12,657 [ERROR] app.core.database:75 - 数据库会话错误: 401: 邮箱或密码错误
2025-07-25 16:11:12,663 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/auth/login 状态码: 401 耗时: 3.206s
2025-07-25 16:11:37,854 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/auth/login
2025-07-25 16:11:38,141 [ERROR] app.core.database:75 - 数据库会话错误: 401: 邮箱或密码错误
2025-07-25 16:11:38,142 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/auth/login 状态码: 401 耗时: 0.288s
2025-07-25 16:11:45,429 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/auth/login
2025-07-25 16:11:45,715 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/auth/login 状态码: 200 耗时: 0.287s
2025-07-25 16:12:21,169 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/auth/me?_t=1753431140863
2025-07-25 16:12:21,170 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/auth/me?_t=1753431140863 状态码: 200 耗时: 0.001s
2025-07-25 16:12:21,172 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/auth/me?_t=1753431140863
2025-07-25 16:12:21,176 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/auth/me?_t=1753431140863 状态码: 200 耗时: 0.004s
2025-07-25 16:20:47,374 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/interfaces?_t=1753431647058
2025-07-25 16:20:47,375 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/interfaces?_t=1753431647058 状态码: 200 耗时: 0.001s
2025-07-25 16:20:47,377 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/interfaces?_t=1753431647058
2025-07-25 16:20:47,379 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/interfaces?_t=1753431647058 状态码: 422 耗时: 0.002s
2025-07-25 16:20:49,804 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/interfaces?_t=1753431649800
2025-07-25 16:20:49,804 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/interfaces?_t=1753431649800 状态码: 200 耗时: 0.000s
2025-07-25 16:20:49,807 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/interfaces?_t=1753431649800
2025-07-25 16:20:49,809 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/interfaces?_t=1753431649800 状态码: 422 耗时: 0.003s
2025-07-25 16:20:53,647 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/auth/logout
2025-07-25 16:20:53,654 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/auth/logout 状态码: 200 耗时: 0.007s
2025-07-25 16:21:32,707 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/auth/login
2025-07-25 16:21:32,709 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/auth/login 状态码: 200 耗时: 0.002s
2025-07-25 16:21:32,711 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/auth/login
2025-07-25 16:21:33,019 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/auth/login 状态码: 200 耗时: 0.308s
2025-07-25 16:25:53,928 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/auth/me?_t=1753431953619
2025-07-25 16:25:53,929 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/auth/me?_t=1753431953619 状态码: 200 耗时: 0.002s
2025-07-25 16:25:53,931 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/auth/me?_t=1753431953619
2025-07-25 16:25:53,936 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/auth/me?_t=1753431953619 状态码: 200 耗时: 0.005s
2025-07-25 16:26:19,288 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/auth/me?_t=1753431979277
2025-07-25 16:26:19,290 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/auth/me?_t=1753431979277 状态码: 200 耗时: 0.002s
2025-07-25 16:26:19,304 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/auth/me?_t=1753431979277
2025-07-25 16:26:19,316 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/auth/me?_t=1753431979277 状态码: 200 耗时: 0.012s
2025-07-25 16:32:53,893 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/auth/me?_t=1753432373574
2025-07-25 16:32:53,894 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/auth/me?_t=1753432373574 状态码: 200 耗时: 0.001s
2025-07-25 16:32:53,896 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/auth/me?_t=1753432373574
2025-07-25 16:32:53,903 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/auth/me?_t=1753432373574 状态码: 200 耗时: 0.006s
2025-07-25 16:32:57,499 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753432377494
2025-07-25 16:32:57,500 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432377494
2025-07-25 16:32:57,500 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753432377494 状态码: 200 耗时: 0.001s
2025-07-25 16:32:57,503 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432377494 状态码: 200 耗时: 0.003s
2025-07-25 16:32:57,504 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753432377494
2025-07-25 16:32:57,504 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753432377494 状态码: 307 耗时: 0.000s
2025-07-25 16:32:57,505 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432377494
2025-07-25 16:32:57,509 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432377494 状态码: 200 耗时: 0.004s
2025-07-25 16:32:57,536 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753432377494
2025-07-25 16:32:57,536 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753432377494 状态码: 200 耗时: 0.000s
2025-07-25 16:32:57,539 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753432377494
2025-07-25 16:32:57,546 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753432377494 状态码: 200 耗时: 0.007s
2025-07-25 16:33:04,641 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753432384329
2025-07-25 16:33:04,642 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432384329
2025-07-25 16:33:04,642 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753432384329 状态码: 200 耗时: 0.001s
2025-07-25 16:33:04,643 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432384329 状态码: 200 耗时: 0.001s
2025-07-25 16:33:04,646 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753432384329
2025-07-25 16:33:04,647 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432384329
2025-07-25 16:33:04,647 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753432384329 状态码: 307 耗时: 0.001s
2025-07-25 16:33:04,656 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753432384329
2025-07-25 16:33:04,656 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432384329 状态码: 200 耗时: 0.009s
2025-07-25 16:33:04,658 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753432384329 状态码: 200 耗时: 0.002s
2025-07-25 16:33:04,661 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753432384329
2025-07-25 16:33:04,665 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753432384329 状态码: 200 耗时: 0.003s
2025-07-25 16:33:14,678 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753432394673
2025-07-25 16:33:14,679 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753432394673 状态码: 200 耗时: 0.002s
2025-07-25 16:33:14,681 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432394673
2025-07-25 16:33:14,683 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432394673 状态码: 200 耗时: 0.001s
2025-07-25 16:33:14,685 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753432394673
2025-07-25 16:33:14,685 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753432394673 状态码: 307 耗时: 0.000s
2025-07-25 16:33:14,686 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432394673
2025-07-25 16:33:14,690 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432394673 状态码: 200 耗时: 0.004s
2025-07-25 16:33:14,705 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753432394673
2025-07-25 16:33:14,705 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753432394673 状态码: 200 耗时: 0.000s
2025-07-25 16:33:14,708 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753432394673
2025-07-25 16:33:14,711 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753432394673 状态码: 200 耗时: 0.003s
2025-07-25 16:33:16,660 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432396651
2025-07-25 16:33:16,660 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432396651 状态码: 200 耗时: 0.000s
2025-07-25 16:33:16,664 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432396651
2025-07-25 16:33:16,669 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432396651 状态码: 200 耗时: 0.004s
2025-07-25 16:33:29,796 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753432409792
2025-07-25 16:33:29,797 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432409792
2025-07-25 16:33:29,797 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753432409792 状态码: 200 耗时: 0.001s
2025-07-25 16:33:29,798 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432409792 状态码: 200 耗时: 0.001s
2025-07-25 16:33:29,801 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753432409792
2025-07-25 16:33:29,802 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432409792
2025-07-25 16:33:29,802 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753432409792 状态码: 307 耗时: 0.001s
2025-07-25 16:33:29,807 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432409792 状态码: 200 耗时: 0.004s
2025-07-25 16:33:29,824 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753432409792
2025-07-25 16:33:29,824 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753432409792 状态码: 200 耗时: 0.000s
2025-07-25 16:33:29,836 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753432409792
2025-07-25 16:33:29,847 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753432409792 状态码: 200 耗时: 0.010s
2025-07-25 16:33:35,866 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753432415863
2025-07-25 16:33:35,867 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753432415863 状态码: 200 耗时: 0.001s
2025-07-25 16:33:35,869 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432415863
2025-07-25 16:33:35,870 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432415863 状态码: 200 耗时: 0.001s
2025-07-25 16:33:35,872 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753432415863
2025-07-25 16:33:35,872 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753432415863 状态码: 307 耗时: 0.000s
2025-07-25 16:33:35,874 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432415863
2025-07-25 16:33:35,878 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432415863 状态码: 200 耗时: 0.003s
2025-07-25 16:33:35,894 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753432415863
2025-07-25 16:33:35,894 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753432415863 状态码: 200 耗时: 0.000s
2025-07-25 16:33:35,899 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753432415863
2025-07-25 16:33:35,910 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753432415863 状态码: 200 耗时: 0.011s
2025-07-25 16:33:38,921 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753432418917
2025-07-25 16:33:38,921 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432418917
2025-07-25 16:33:38,923 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753432418917 状态码: 200 耗时: 0.002s
2025-07-25 16:33:38,923 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432418917 状态码: 200 耗时: 0.002s
2025-07-25 16:33:38,925 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753432418917
2025-07-25 16:33:38,926 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432418917
2025-07-25 16:33:38,926 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753432418917 状态码: 307 耗时: 0.001s
2025-07-25 16:33:38,928 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432418917 状态码: 200 耗时: 0.002s
2025-07-25 16:33:38,944 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753432418917
2025-07-25 16:33:38,944 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753432418917 状态码: 200 耗时: 0.000s
2025-07-25 16:33:38,946 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753432418917
2025-07-25 16:33:38,952 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753432418917 状态码: 200 耗时: 0.005s
2025-07-25 16:33:42,929 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753432422926
2025-07-25 16:33:42,930 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432422926
2025-07-25 16:33:42,930 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753432422926 状态码: 200 耗时: 0.001s
2025-07-25 16:33:42,931 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432422926 状态码: 200 耗时: 0.001s
2025-07-25 16:33:42,934 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753432422926
2025-07-25 16:33:42,935 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432422926
2025-07-25 16:33:42,935 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753432422926 状态码: 307 耗时: 0.001s
2025-07-25 16:33:42,938 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753432422926 状态码: 200 耗时: 0.003s
2025-07-25 16:33:42,955 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753432422926
2025-07-25 16:33:42,955 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753432422926 状态码: 200 耗时: 0.000s
2025-07-25 16:33:42,958 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753432422926
2025-07-25 16:33:42,961 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753432422926 状态码: 200 耗时: 0.004s
2025-07-25 16:36:06,871 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/auth/me?_t=1753432566559
2025-07-25 16:36:06,871 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/auth/me?_t=1753432566559 状态码: 200 耗时: 0.000s
2025-07-25 16:36:06,873 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/auth/me?_t=1753432566559
2025-07-25 16:36:06,877 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/auth/me?_t=1753432566559 状态码: 200 耗时: 0.004s
2025-07-25 16:42:49,820 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/auth/me?_t=1753432969510
2025-07-25 16:42:49,821 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/auth/me?_t=1753432969510 状态码: 200 耗时: 0.001s
2025-07-25 16:42:49,827 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/auth/me?_t=1753432969510
2025-07-25 16:42:49,834 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/auth/me?_t=1753432969510 状态码: 200 耗时: 0.007s
2025-07-25 16:43:43,821 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/auth/me?_t=1753433023820
2025-07-25 16:43:43,823 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/auth/me?_t=1753433023820 状态码: 200 耗时: 0.001s
2025-07-25 16:43:43,825 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/auth/me?_t=1753433023820
2025-07-25 16:43:43,829 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/auth/me?_t=1753433023820 状态码: 200 耗时: 0.004s
2025-07-25 16:47:01,294 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753433220979
2025-07-25 16:47:01,295 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753433220979
2025-07-25 16:47:01,295 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753433220979 状态码: 200 耗时: 0.001s
2025-07-25 16:47:01,296 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753433220979 状态码: 200 耗时: 0.001s
2025-07-25 16:47:01,299 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753433220979
2025-07-25 16:47:01,302 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753433220979
2025-07-25 16:47:01,303 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753433220979 状态码: 307 耗时: 0.003s
2025-07-25 16:47:01,309 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753433220979 状态码: 200 耗时: 0.008s
2025-07-25 16:47:01,312 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753433220979
2025-07-25 16:47:01,312 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753433220979 状态码: 200 耗时: 0.000s
2025-07-25 16:47:01,317 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753433220979
2025-07-25 16:47:01,323 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753433220979 状态码: 200 耗时: 0.005s
2025-07-25 16:47:17,512 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/upload/avatar
2025-07-25 16:47:17,513 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/upload/avatar 状态码: 404 耗时: 0.001s
2025-07-25 16:47:23,939 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/upload/avatar
2025-07-25 16:47:23,940 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/upload/avatar 状态码: 404 耗时: 0.001s
2025-07-25 16:48:00,605 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/auth/me?_t=1753433280584
2025-07-25 16:48:00,606 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/auth/me?_t=1753433280584 状态码: 200 耗时: 0.001s
2025-07-25 16:48:00,630 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/auth/me?_t=1753433280584
2025-07-25 16:48:00,655 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/auth/me?_t=1753433280584 状态码: 200 耗时: 0.028s
2025-07-25 16:48:04,826 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/auth/logout
2025-07-25 16:48:04,826 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/auth/logout 状态码: 200 耗时: 0.000s
2025-07-25 16:48:04,829 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/auth/logout
2025-07-25 16:48:04,839 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/auth/logout 状态码: 200 耗时: 0.011s
2025-07-25 16:48:09,123 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/auth/login
2025-07-25 16:48:09,123 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/auth/login 状态码: 200 耗时: 0.000s
2025-07-25 16:48:09,125 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/auth/login
2025-07-25 16:48:09,403 [ERROR] app.core.database:75 - 数据库会话错误: 401: 邮箱或密码错误
2025-07-25 16:48:09,404 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/auth/login 状态码: 401 耗时: 0.278s
2025-07-25 16:48:14,917 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/auth/login
2025-07-25 16:48:15,218 [ERROR] app.core.database:75 - 数据库会话错误: 401: 邮箱或密码错误
2025-07-25 16:48:15,218 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/auth/login 状态码: 401 耗时: 0.301s
2025-07-25 16:48:18,846 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/auth/login
2025-07-25 16:48:19,126 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/auth/login 状态码: 200 耗时: 0.281s
2025-07-25 16:48:44,928 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/upload/avatar
2025-07-25 16:48:44,929 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/upload/avatar 状态码: 404 耗时: 0.002s
2025-07-25 16:49:21,106 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753433360787
2025-07-25 16:49:21,107 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753433360787
2025-07-25 16:49:21,107 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753433360787 状态码: 200 耗时: 0.001s
2025-07-25 16:49:21,108 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753433360787 状态码: 200 耗时: 0.001s
2025-07-25 16:49:21,111 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753433360787
2025-07-25 16:49:21,112 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753433360787 状态码: 307 耗时: 0.001s
2025-07-25 16:49:21,113 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753433360787
2025-07-25 16:49:21,120 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753433360787 状态码: 200 耗时: 0.008s
2025-07-25 16:49:21,125 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753433360787
2025-07-25 16:49:21,126 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753433360787 状态码: 200 耗时: 0.001s
2025-07-25 16:49:21,129 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753433360787
2025-07-25 16:49:21,135 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753433360787 状态码: 200 耗时: 0.005s
2025-07-25 16:49:22,873 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753433362869
2025-07-25 16:49:22,873 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753433362869 状态码: 200 耗时: 0.000s
2025-07-25 16:49:22,875 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753433362869
2025-07-25 16:49:22,878 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753433362869 状态码: 200 耗时: 0.003s
2025-07-25 16:50:01,585 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/decrypt
2025-07-25 16:50:01,586 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/decrypt 状态码: 200 耗时: 0.001s
2025-07-25 16:50:01,587 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/interfaces/decrypt
2025-07-25 16:50:01,821 [ERROR] app.api.v1.interfaces:272 - 解密接口URL异常: 1 validation error for DecryptResponse
error
  Field required [type=missing, input_value={'success': True, 'conten...method': '直接内容'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-07-25 16:50:01,823 [ERROR] app.core.database:75 - 数据库会话错误: 2 validation errors for DecryptResponse
content
  Field required [type=missing, input_value={'success': False, 'error...tic.dev/2.10/v/missing"}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
method
  Field required [type=missing, input_value={'success': False, 'error...tic.dev/2.10/v/missing"}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-07-25 16:50:01,824 [ERROR] root:111 - 未处理的异常: 2 validation errors for DecryptResponse
content
  Field required [type=missing, input_value={'success': False, 'error...tic.dev/2.10/v/missing"}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
method
  Field required [type=missing, input_value={'success': False, 'error...tic.dev/2.10/v/missing"}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
  + Exception Group Traceback (most recent call last):
  |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\_utils.py", line 76, in collapse_excgroups
  |     yield
  |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 186, in __call__
  |     async with anyio.create_task_group() as task_group:
  |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_backends\_asyncio.py", line 767, in __aexit__
  |     raise BaseExceptionGroup(
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    |     await self.app(scope, receive, _send)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 185, in __call__
    |     with collapse_excgroups():
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 155, in __exit__
    |     self.gen.throw(typ, value, traceback)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    |     raise exc
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 187, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "g:\项目区\tvbox1\tvbox-manager-pro\backend\app\main.py", line 152, in log_requests
    |     response = await call_next(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 163, in call_next
    |     raise app_exc
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 149, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\trustedhost.py", line 36, in __call__
    |     await self.app(scope, receive, send)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    |     await self.simple_response(scope, receive, send, request_headers=headers)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    |     await self.app(scope, receive, send)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    |     await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    |     raise exc
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    |     await app(scope, receive, sender)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    |     await route.handle(scope, receive, send)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    |     await self.app(scope, receive, send)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    |     await wrap_app_handling_exceptions(app, request)(scope, receive, send)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    |     raise exc
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    |     await app(scope, receive, sender)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    |     response = await f(request)
    |                ^^^^^^^^^^^^^^^^
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    |     raw_response = await run_endpoint_function(
    |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    |     return await dependant.call(**values)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "g:\项目区\tvbox1\tvbox-manager-pro\backend\app\api\v1\interfaces.py", line 273, in decrypt_interface_url
    |     return DecryptResponse(
    |            ^^^^^^^^^^^^^^^^
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pydantic\main.py", line 214, in __init__
    |     validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    | pydantic_core._pydantic_core.ValidationError: 2 validation errors for DecryptResponse
    | content
    |   Field required [type=missing, input_value={'success': False, 'error...tic.dev/2.10/v/missing"}, input_type=dict]
    |     For further information visit https://errors.pydantic.dev/2.10/v/missing
    | method
    |   Field required [type=missing, input_value={'success': False, 'error...tic.dev/2.10/v/missing"}, input_type=dict]
    |     For further information visit https://errors.pydantic.dev/2.10/v/missing
    +------------------------------------

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 185, in __call__
    with collapse_excgroups():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 155, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 187, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "g:\项目区\tvbox1\tvbox-manager-pro\backend\app\main.py", line 152, in log_requests
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 163, in call_next
    raise app_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 149, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\trustedhost.py", line 36, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "g:\项目区\tvbox1\tvbox-manager-pro\backend\app\api\v1\interfaces.py", line 273, in decrypt_interface_url
    return DecryptResponse(
           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pydantic\main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pydantic_core._pydantic_core.ValidationError: 2 validation errors for DecryptResponse
content
  Field required [type=missing, input_value={'success': False, 'error...tic.dev/2.10/v/missing"}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
method
  Field required [type=missing, input_value={'success': False, 'error...tic.dev/2.10/v/missing"}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-07-25 16:50:18,320 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces
2025-07-25 16:50:18,321 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces 状态码: 200 耗时: 0.001s
2025-07-25 16:50:18,323 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/interfaces
2025-07-25 16:50:18,323 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/interfaces 状态码: 307 耗时: 0.000s
2025-07-25 16:50:18,325 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/
2025-07-25 16:50:18,327 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/ 状态码: 200 耗时: 0.001s
2025-07-25 16:50:18,329 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/interfaces/
2025-07-25 16:50:18,424 [ERROR] app.services.tvbox_decryptor:268 - 解析配置内容失败: Expecting value: line 1 column 1 (char 0)
2025-07-25 16:50:18,434 [INFO] app.services.interface_service:64 - 接口源创建成功: 123
2025-07-25 16:50:18,436 [ERROR] app.api.v1.interfaces:99 - 创建接口源异常: 3 validation errors for InterfaceSourceResponse
last_error_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 7, 25, 8, 50, 18, 425210), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.10/v/string_type
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 7, 25, 8, 50, 18), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.10/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 7, 25, 8, 50, 18), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.10/v/string_type
2025-07-25 16:50:18,437 [ERROR] app.core.database:75 - 数据库会话错误: 500: 创建接口源失败
2025-07-25 16:50:18,438 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/interfaces/ 状态码: 500 耗时: 0.109s
2025-07-25 17:04:58,991 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 17:04:58,996 [INFO] root:48 - 数据库表创建完成
2025-07-25 17:04:59,039 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 17:04:59,040 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 17:05:07,284 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/auth/me?_t=1753434306969
2025-07-25 17:05:07,285 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/auth/me?_t=1753434306969 状态码: 200 耗时: 0.001s
2025-07-25 17:05:07,287 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/auth/me?_t=1753434306969
2025-07-25 17:05:07,302 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/auth/me?_t=1753434306969 状态码: 200 耗时: 0.016s
2025-07-25 17:05:07,607 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753434307595
2025-07-25 17:05:07,607 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753434307595 状态码: 200 耗时: 0.000s
2025-07-25 17:05:07,610 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753434307595
2025-07-25 17:05:07,615 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753434307595 状态码: 200 耗时: 0.005s
2025-07-25 17:05:13,421 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434313416
2025-07-25 17:05:13,422 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434313416 状态码: 200 耗时: 0.001s
2025-07-25 17:05:13,423 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753434313416
2025-07-25 17:05:13,424 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753434313416 状态码: 200 耗时: 0.001s
2025-07-25 17:05:13,426 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434313416
2025-07-25 17:05:13,426 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434313416 状态码: 307 耗时: 0.000s
2025-07-25 17:05:13,428 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753434313416
2025-07-25 17:05:13,433 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753434313416 状态码: 200 耗时: 0.005s
2025-07-25 17:05:13,455 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434313416
2025-07-25 17:05:13,455 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434313416 状态码: 200 耗时: 0.000s
2025-07-25 17:05:13,458 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434313416
2025-07-25 17:05:13,477 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434313416 状态码: 200 耗时: 0.019s
2025-07-25 17:05:13,498 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434313494
2025-07-25 17:05:13,500 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434313494 状态码: 200 耗时: 0.002s
2025-07-25 17:05:13,508 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434313494
2025-07-25 17:05:13,508 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434313494 状态码: 307 耗时: 0.000s
2025-07-25 17:05:13,521 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434313494
2025-07-25 17:05:13,521 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434313494 状态码: 200 耗时: 0.000s
2025-07-25 17:05:13,526 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434313494
2025-07-25 17:05:13,531 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434313494 状态码: 200 耗时: 0.005s
2025-07-25 17:05:17,093 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/auth/me?_t=1753434317091
2025-07-25 17:05:17,094 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/auth/me?_t=1753434317091 状态码: 200 耗时: 0.001s
2025-07-25 17:05:17,096 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/auth/me?_t=1753434317091
2025-07-25 17:05:17,103 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/auth/me?_t=1753434317091 状态码: 200 耗时: 0.006s
2025-07-25 17:05:17,438 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434317427
2025-07-25 17:05:17,439 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753434317427
2025-07-25 17:05:17,439 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434317427 状态码: 200 耗时: 0.001s
2025-07-25 17:05:17,440 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753434317427 状态码: 200 耗时: 0.001s
2025-07-25 17:05:17,442 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434317427
2025-07-25 17:05:17,442 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434317427 状态码: 307 耗时: 0.000s
2025-07-25 17:05:17,444 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753434317427
2025-07-25 17:05:17,449 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753434317427 状态码: 200 耗时: 0.005s
2025-07-25 17:05:17,482 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434317427
2025-07-25 17:05:17,485 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434317427 状态码: 200 耗时: 0.002s
2025-07-25 17:05:17,488 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434317427
2025-07-25 17:05:17,493 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434317427 状态码: 200 耗时: 0.005s
2025-07-25 17:05:17,520 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434317515
2025-07-25 17:05:17,520 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434317515 状态码: 200 耗时: 0.000s
2025-07-25 17:05:17,540 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434317515
2025-07-25 17:05:17,541 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434317515 状态码: 307 耗时: 0.001s
2025-07-25 17:05:17,578 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434317515
2025-07-25 17:05:17,578 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434317515 状态码: 200 耗时: 0.000s
2025-07-25 17:05:17,582 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434317515
2025-07-25 17:05:17,590 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434317515 状态码: 200 耗时: 0.007s
2025-07-25 17:05:39,646 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434339641
2025-07-25 17:05:39,647 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753434339641
2025-07-25 17:05:39,647 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434339641 状态码: 200 耗时: 0.001s
2025-07-25 17:05:39,648 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753434339641 状态码: 200 耗时: 0.001s
2025-07-25 17:05:39,650 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434339641
2025-07-25 17:05:39,650 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753434339641
2025-07-25 17:05:39,652 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434339641 状态码: 307 耗时: 0.002s
2025-07-25 17:05:39,655 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753434339641 状态码: 200 耗时: 0.005s
2025-07-25 17:05:39,675 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434339641
2025-07-25 17:05:39,676 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434339641 状态码: 200 耗时: 0.001s
2025-07-25 17:05:39,678 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434339641
2025-07-25 17:05:39,691 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434339641 状态码: 200 耗时: 0.013s
2025-07-25 17:05:39,711 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434339709
2025-07-25 17:05:39,711 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434339709 状态码: 200 耗时: 0.000s
2025-07-25 17:05:39,715 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434339709
2025-07-25 17:05:39,716 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434339709 状态码: 307 耗时: 0.001s
2025-07-25 17:05:39,721 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434339709
2025-07-25 17:05:39,722 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434339709 状态码: 200 耗时: 0.001s
2025-07-25 17:05:39,723 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434339709
2025-07-25 17:05:39,726 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434339709 状态码: 200 耗时: 0.003s
2025-07-25 17:05:44,735 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753434344727
2025-07-25 17:05:44,736 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753434344727 状态码: 200 耗时: 0.001s
2025-07-25 17:05:44,740 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753434344727
2025-07-25 17:05:44,743 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753434344727 状态码: 200 耗时: 0.003s
2025-07-25 17:06:20,038 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434379707
2025-07-25 17:06:20,039 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753434379707
2025-07-25 17:06:20,039 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434379707 状态码: 200 耗时: 0.001s
2025-07-25 17:06:20,040 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/categories/list?_t=1753434379707 状态码: 200 耗时: 0.002s
2025-07-25 17:06:20,042 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434379707
2025-07-25 17:06:20,042 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434379707 状态码: 307 耗时: 0.001s
2025-07-25 17:06:20,043 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753434379707
2025-07-25 17:06:20,046 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434379707
2025-07-25 17:06:20,047 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434379707 状态码: 200 耗时: 0.001s
2025-07-25 17:06:20,047 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/categories/list?_t=1753434379707 状态码: 200 耗时: 0.004s
2025-07-25 17:06:20,048 [ERROR] asyncio:1771 - Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-07-25 17:06:20,054 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434379707
2025-07-25 17:06:20,058 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434379707 状态码: 200 耗时: 0.003s
2025-07-25 17:06:20,073 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434380070
2025-07-25 17:06:20,073 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434380070 状态码: 200 耗时: 0.000s
2025-07-25 17:06:20,076 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434380070
2025-07-25 17:06:20,077 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753434380070 状态码: 307 耗时: 0.001s
2025-07-25 17:06:20,081 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434380070
2025-07-25 17:06:20,082 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434380070 状态码: 200 耗时: 0.001s
2025-07-25 17:06:20,126 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434380070
2025-07-25 17:06:20,136 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753434380070 状态码: 200 耗时: 0.010s
2025-07-25 17:13:01,029 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 17:13:01,035 [INFO] root:48 - 数据库表创建完成
2025-07-25 17:13:01,076 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 17:13:01,076 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 17:17:29,170 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 17:17:33,473 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 17:17:33,478 [INFO] root:48 - 数据库表创建完成
2025-07-25 17:17:33,522 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 17:17:33,522 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 17:22:30,543 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 17:22:33,992 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 17:22:33,997 [INFO] root:48 - 数据库表创建完成
2025-07-25 17:22:34,043 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 17:22:34,043 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 17:28:52,722 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 17:28:56,612 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 17:28:56,618 [INFO] root:48 - 数据库表创建完成
2025-07-25 17:28:56,662 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 17:28:56,662 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 17:28:58,947 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 17:36:15,404 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 17:36:15,409 [INFO] root:48 - 数据库表创建完成
2025-07-25 17:36:15,453 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 17:36:15,453 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 17:36:18,329 [INFO] root:149 - 请求开始: GET http://localhost:8000/docs
2025-07-25 17:36:18,329 [INFO] root:158 - 请求完成: GET http://localhost:8000/docs 状态码: 200 耗时: 0.000s
2025-07-25 17:36:20,279 [INFO] root:149 - 请求开始: GET http://localhost:8000/openapi.json
2025-07-25 17:36:20,309 [INFO] root:158 - 请求完成: GET http://localhost:8000/openapi.json 状态码: 200 耗时: 0.030s
2025-07-25 17:36:49,539 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/auth/login
2025-07-25 17:36:49,555 [ERROR] app.core.database:75 - 数据库会话错误: 401: 邮箱或密码错误
2025-07-25 17:36:49,556 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/auth/login 状态码: 401 耗时: 0.018s
2025-07-25 17:37:10,463 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/interfaces/
2025-07-25 17:37:13,193 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 1/3): HTTPConnectionPool(host='string', port=80): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000199DCB1C150>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-25 17:37:16,897 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 2/3): HTTPConnectionPool(host='string', port=80): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000199DCB1C210>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-25 17:37:20,592 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 3/3): HTTPConnectionPool(host='string', port=80): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000199DCB1D990>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-25 17:37:20,592 [ERROR] app.services.tvbox_decryptor:86 - 解密配置URL失败: HTTPConnectionPool(host='string', port=80): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000199DCB1D990>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-25 17:37:20,598 [ERROR] app.services.interface_service:155 - 解析接口失败: HTTPConnectionPool(host='string', port=80): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000199DCB1D990>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-25 17:37:20,601 [INFO] app.services.interface_service:64 - 接口源创建成功: string
2025-07-25 17:37:20,602 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/interfaces/ 状态码: 200 耗时: 10.139s
2025-07-25 17:38:08,907 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 17:38:08,912 [INFO] root:48 - 数据库表创建完成
2025-07-25 17:38:08,955 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 17:38:08,955 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 17:43:01,970 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 17:43:05,879 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 17:43:05,887 [INFO] root:48 - 数据库表创建完成
2025-07-25 17:43:05,941 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 17:43:05,942 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 17:48:01,989 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 17:48:05,814 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 17:48:05,819 [INFO] root:48 - 数据库表创建完成
2025-07-25 17:48:05,860 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 17:48:05,860 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 17:55:24,951 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 17:55:34,737 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 17:55:34,744 [INFO] root:48 - 数据库表创建完成
2025-07-25 17:55:34,803 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 17:55:34,803 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 17:55:36,432 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 18:16:12,819 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 18:16:12,824 [INFO] root:48 - 数据库表创建完成
2025-07-25 18:16:12,876 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 18:16:12,876 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 18:26:08,916 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 18:26:18,727 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 18:26:18,735 [INFO] root:48 - 数据库表创建完成
2025-07-25 18:26:18,808 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 18:26:18,809 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 18:31:11,071 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 18:31:14,522 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 18:31:14,527 [INFO] root:48 - 数据库表创建完成
2025-07-25 18:31:14,572 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 18:31:14,573 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 18:31:47,427 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 19:14:50,196 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:14:50,220 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:14:50,271 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 19:18:19,347 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:18:19,353 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:18:19,407 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 19:20:38,810 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:20:38,816 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:20:38,869 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 19:30:35,007 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:30:35,013 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:30:35,064 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 19:30:52,578 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:30:52,585 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:30:52,632 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 19:31:10,320 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:31:10,325 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:31:10,373 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 19:31:32,156 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:31:32,162 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:31:32,210 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 19:31:48,352 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:31:48,358 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:31:48,402 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 19:32:05,357 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:32:05,364 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:32:05,413 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 19:32:22,211 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:32:22,217 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:32:22,280 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 19:37:07,260 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:37:07,267 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:37:07,310 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 19:38:58,408 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:38:58,414 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:38:58,467 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 19:42:38,154 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:42:38,161 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:42:38,213 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 19:43:26,635 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:43:26,641 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:43:26,693 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 19:44:10,384 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:44:10,389 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:44:10,433 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 19:44:53,991 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:44:53,997 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:44:54,049 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 19:45:38,406 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:45:38,413 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:45:38,462 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 19:48:17,405 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:48:17,411 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:48:17,466 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 19:48:51,806 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:48:51,812 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:48:51,856 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 19:49:06,840 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:49:06,903 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:49:07,035 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 19:50:01,355 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:50:01,361 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:50:01,414 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 19:50:25,127 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:50:25,134 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:50:25,181 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 19:50:39,471 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:50:39,478 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:50:39,524 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 19:50:54,480 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:50:54,487 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:50:54,557 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 20:02:09,297 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:02:09,303 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:02:09,352 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 20:02:35,111 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:02:35,125 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:02:35,207 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 20:03:16,623 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:03:16,630 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:03:16,687 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 20:05:51,224 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:05:51,234 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:05:51,281 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 20:06:28,521 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:06:28,527 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:06:28,573 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 20:15:03,216 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:15:03,223 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:15:03,280 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 20:15:28,547 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:15:28,552 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:15:28,625 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 20:15:42,915 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:15:42,921 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:15:42,973 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 20:18:50,099 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:18:50,105 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:18:50,166 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 20:19:08,311 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:19:08,318 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:19:08,376 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 20:19:50,297 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:19:50,302 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:19:50,361 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 20:27:43,270 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:27:43,277 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:27:43,334 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 20:28:06,329 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:28:06,337 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:28:06,387 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 20:38:25,969 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:38:25,976 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:38:26,027 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 20:38:48,399 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:38:48,404 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:38:48,455 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 20:39:19,548 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:39:19,555 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:39:19,609 [ERROR] app.services.user_service:82 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-25 20:41:52,972 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:41:52,979 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:41:53,026 [ERROR] app.services.user_service:83 - 创建默认管理员失败: (sqlite3.OperationalError) no such column: users.role
[SQL: SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.avatar AS users_avatar, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.is_superuser AS users_is_superuser, users.role AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at, users.login_count AS users_login_count, users.last_login_ip AS users_last_login_ip, users.failed_login_attempts AS users_failed_login_attempts, users.locked_until AS users_locked_until, users.password_reset_token AS users_password_reset_token, users.password_reset_expires AS users_password_reset_expires, users.email_verification_token AS users_email_verification_token, users.settings AS users_settings 
FROM users 
WHERE users.email = ?
 LIMIT ? OFFSET ?]
[parameters: ('<EMAIL>', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
