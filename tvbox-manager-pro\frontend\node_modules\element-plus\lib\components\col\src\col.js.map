{"version": 3, "file": "col.js", "sources": ["../../../../../../packages/components/col/src/col.ts"], "sourcesContent": ["import { buildProps, definePropType, mutable } from '@element-plus/utils'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type Col from './col.vue'\n\nexport type ColSizeObject = {\n  span?: number\n  offset?: number\n  pull?: number\n  push?: number\n}\nexport type ColSize = number | ColSizeObject\n\nexport const colProps = buildProps({\n  /**\n   * @description custom element tag\n   */\n  tag: {\n    type: String,\n    default: 'div',\n  },\n  /**\n   * @description number of column the grid spans\n   */\n  span: {\n    type: Number,\n    default: 24,\n  },\n  /**\n   * @description number of spacing on the left side of the grid\n   */\n  offset: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description number of columns that grid moves to the left\n   */\n  pull: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description number of columns that grid moves to the right\n   */\n  push: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description `<768px` Responsive columns or column props object\n   */\n  xs: {\n    type: definePropType<ColSize>([Number, Object]),\n    default: () => mutable({} as const),\n  },\n  /**\n   * @description `≥768px` Responsive columns or column props object\n   */\n  sm: {\n    type: definePropType<ColSize>([Number, Object]),\n    default: () => mutable({} as const),\n  },\n  /**\n   * @description `≥992px` Responsive columns or column props object\n   */\n  md: {\n    type: definePropType<ColSize>([Number, Object]),\n    default: () => mutable({} as const),\n  },\n  /**\n   * @description `≥1200px` Responsive columns or column props object\n   */\n  lg: {\n    type: definePropType<ColSize>([Number, Object]),\n    default: () => mutable({} as const),\n  },\n  /**\n   * @description `≥1920px` Responsive columns or column props object\n   */\n  xl: {\n    type: definePropType<ColSize>([Number, Object]),\n    default: () => mutable({} as const),\n  },\n} as const)\nexport type ColProps = ExtractPropTypes<typeof colProps>\nexport type ColPropsPublic = __ExtractPublicPropTypes<typeof colProps>\nexport type ColInstance = InstanceType<typeof Col> & unknown\n"], "names": ["buildProps", "definePropType", "mutable"], "mappings": ";;;;;;;AACY,MAAC,QAAQ,GAAGA,kBAAU,CAAC;AACnC,EAAE,GAAG,EAAE;AACP,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAEC,sBAAc,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1C,IAAI,OAAO,EAAE,MAAMC,kBAAO,CAAC,EAAE,CAAC;AAC9B,GAAG;AACH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAED,sBAAc,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1C,IAAI,OAAO,EAAE,MAAMC,kBAAO,CAAC,EAAE,CAAC;AAC9B,GAAG;AACH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAED,sBAAc,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1C,IAAI,OAAO,EAAE,MAAMC,kBAAO,CAAC,EAAE,CAAC;AAC9B,GAAG;AACH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAED,sBAAc,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1C,IAAI,OAAO,EAAE,MAAMC,kBAAO,CAAC,EAAE,CAAC;AAC9B,GAAG;AACH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAED,sBAAc,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1C,IAAI,OAAO,EAAE,MAAMC,kBAAO,CAAC,EAAE,CAAC;AAC9B,GAAG;AACH,CAAC;;;;"}