#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import logging
import time
import os
import json

# 添加项目路径
sys.path.append('.')

from app.core.tvbox_decryptor import TVBoxDecryptor

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s [%(levelname)s] %(name)s:%(lineno)d - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger('test_our_decryptor')

def test_our_decryptor():
    """测试我们的解密器"""
    url = "http://www.饭太硬.com/tv"
    logger.info(f"测试我们的解密器: {url}")

    try:
        decryptor = TVBoxDecryptor()
        logger.info("解密器创建成功")

        # 使用原始解密器保存的内容进行测试
        logger.info("=== 使用原始解密器的内容测试 ===")
        try:
            with open(r'g:\项目区\tvbox1\response_logs\formatted_1753455100.json', 'r', encoding='utf-8') as f:
                test_content = f.read()

            logger.info(f"测试内容长度: {len(test_content)}")
            logger.info(f"测试内容前200字符: {repr(test_content[:200])}")

            # 验证配置
            is_valid = decryptor.validate_tvbox_config(test_content)
            logger.info(f"配置验证: {is_valid}")

            if is_valid:
                # 解析配置
                config_info = decryptor.parse_tvbox_config_content(test_content)
                sites_count = len(config_info.get('sites', []))
                lives_count = len(config_info.get('lives', []))
                parses_count = len(config_info.get('parses', []))

                logger.info(f"站点数量: {sites_count}")
                logger.info(f"直播分组: {lives_count}")
                logger.info(f"解析器数量: {parses_count}")

                return True
            else:
                logger.error("配置验证失败")

                # 尝试移除注释后再验证
                logger.info("=== 尝试移除注释后验证 ===")
                cleaned_content = decryptor._remove_comments_preserve_url(test_content)
                logger.info(f"清理后内容长度: {len(cleaned_content)}")
                logger.info(f"清理后内容前200字符: {repr(cleaned_content[:200])}")

                is_valid_cleaned = decryptor.validate_tvbox_config(cleaned_content)
                logger.info(f"清理后配置验证: {is_valid_cleaned}")

                if is_valid_cleaned:
                    config_info = decryptor.parse_tvbox_config_content(cleaned_content)
                    sites_count = len(config_info.get('sites', []))
                    lives_count = len(config_info.get('lives', []))
                    parses_count = len(config_info.get('parses', []))

                    logger.info(f"站点数量: {sites_count}")
                    logger.info(f"直播分组: {lives_count}")
                    logger.info(f"解析器数量: {parses_count}")

                    return True

                return False

        except Exception as e:
            logger.error(f"测试原始内容失败: {e}")
            return False

    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    logger.info("=== 测试我们的解密器 ===")
    success = test_our_decryptor()
    logger.info(f"测试结果: {'成功' if success else '失败'}")
