@echo off
chcp 65001 >nul
title TVBox Manager Pro - 快速启动

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    TVBox Manager Pro                         ║
echo ║                      快速启动                                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python 未安装，请先安装 Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo 📁 进入后端目录...
cd /d "%~dp0backend"

echo 📦 安装最小化依赖...
pip install -r requirements-minimal.txt

if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败，尝试使用国内镜像源...
    pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements-minimal.txt
)

echo 📁 创建必要的目录...
if not exist "data" mkdir data
if not exist "logs" mkdir logs

echo 🚀 启动服务...
echo.
python app/main.py

echo.
echo 服务已停止，按任意键退出...
pause >nul
