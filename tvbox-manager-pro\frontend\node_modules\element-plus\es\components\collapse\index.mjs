import Collapse from './src/collapse2.mjs';
import CollapseItem from './src/collapse-item2.mjs';
export { collapseEmits, collapseProps, emitChangeFn } from './src/collapse.mjs';
export { collapseItemProps } from './src/collapse-item.mjs';
export { collapseContextKey } from './src/constants.mjs';
import { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';

const ElCollapse = withInstall(Collapse, {
  CollapseItem
});
const ElCollapseItem = withNoopInstall(CollapseItem);

export { ElCollapse, ElCollapseItem, ElCollapse as default };
//# sourceMappingURL=index.mjs.map
