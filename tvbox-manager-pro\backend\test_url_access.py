#!/usr/bin/env python3
"""
测试URL访问
"""
import requests
import urllib3
from urllib.parse import quote, unquote
import json

def test_url_access():
    """测试URL访问"""
    
    # 原始URL
    original_url = "http://www.饭太硬.com/tv"
    print(f"原始URL: {original_url}")
    
    # 尝试不同的编码方式
    urls_to_try = [
        original_url,
        "http://www.xn--4bra.xn--vuq861b.com/tv",  # punycode编码
        quote(original_url, safe=':/?#[]@!$&\'()*+,;='),  # URL编码
    ]
    
    # 禁用SSL警告
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    
    for i, url in enumerate(urls_to_try):
        print(f"\n尝试 {i+1}: {url}")
        
        try:
            # 设置请求头
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
            
            # 尝试访问
            response = requests.get(url, headers=headers, timeout=30, verify=False)
            print(f"状态码: {response.status_code}")
            print(f"Content-Type: {response.headers.get('content-type', 'unknown')}")
            print(f"内容长度: {len(response.text)}")
            print(f"内容前200字符: {response.text[:200]}")
            
            if response.status_code == 200:
                # 尝试解析为JSON
                try:
                    config_data = json.loads(response.text)
                    print(f"✅ JSON解析成功!")
                    
                    spider = config_data.get('spider', 'N/A')
                    sites = config_data.get('sites', [])
                    lives = config_data.get('lives', [])
                    
                    print(f"Spider: {spider}")
                    print(f"Sites数量: {len(sites)}")
                    print(f"Lives数量: {len(lives)}")
                    
                    if len(sites) > 0:
                        print(f"前3个Sites:")
                        for j, site in enumerate(sites[:3]):
                            name = site.get('name', 'N/A')
                            api = site.get('api', 'N/A')
                            print(f"  {j+1}. {name} - {api}")
                    
                    return response.text, url
                    
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                    
                    # 检查是否是Base64编码
                    try:
                        import base64
                        decoded = base64.b64decode(response.text).decode('utf-8')
                        config_data = json.loads(decoded)
                        print(f"✅ Base64解码+JSON解析成功!")
                        return decoded, url
                    except:
                        print(f"❌ Base64解码也失败")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 访问失败: {e}")
    
    print(f"\n❌ 所有尝试都失败了")
    return None, None

if __name__ == "__main__":
    content, successful_url = test_url_access()
    if content:
        print(f"\n🎉 成功获取配置!")
        print(f"成功的URL: {successful_url}")
        print(f"配置长度: {len(content)}")
    else:
        print(f"\n❌ 无法获取配置")
