#!/usr/bin/env python3
"""
修复本地化状态脚本
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models import InterfaceSource, LocalizedFile
from datetime import datetime

def fix_localization_status():
    """修复本地化状态"""
    db = SessionLocal()
    
    try:
        print("🔧 修复本地化状态...")
        print("=" * 80)
        
        # 获取所有启用本地化的接口
        interfaces = db.query(InterfaceSource).filter(
            InterfaceSource.enable_localization == True
        ).all()
        
        for interface in interfaces:
            print(f"\n📋 检查接口 ID: {interface.id} - {interface.name}")
            
            # 获取本地化文件
            localized_files = db.query(LocalizedFile).filter(
                LocalizedFile.interface_id == interface.id
            ).all()
            
            if not localized_files:
                print(f"   ⚠️  没有本地化文件，跳过")
                continue
            
            total_files = len(localized_files)
            completed_files = len([f for f in localized_files if f.download_status == 'completed'])
            error_files = len([f for f in localized_files if f.download_status == 'error'])
            
            print(f"   📊 文件统计: 总计 {total_files}, 完成 {completed_files}, 错误 {error_files}")
            
            # 计算进度
            if total_files > 0:
                progress = int((completed_files / total_files) * 100)
            else:
                progress = 0
            
            # 确定状态
            if completed_files == total_files:
                # 全部完成
                new_status = 'completed'
                new_progress = 100
            elif completed_files > 0:
                # 部分完成
                new_status = 'completed'  # 即使有错误，只要有成功的就算完成
                new_progress = progress
            else:
                # 全部失败
                new_status = 'error'
                new_progress = 0
            
            # 更新状态
            old_status = interface.localization_status
            old_progress = interface.localization_progress
            
            interface.localization_status = new_status
            interface.localization_progress = new_progress
            
            # 如果状态变为完成，更新完成时间
            if new_status == 'completed' and not interface.last_localization_at:
                interface.last_localization_at = datetime.utcnow()
            
            # 清除错误信息（如果有成功的文件）
            if completed_files > 0:
                interface.localization_error_message = None
            
            db.commit()
            
            print(f"   🔄 状态更新: {old_status} ({old_progress}%) → {new_status} ({new_progress}%)")
            
            if new_status == 'completed':
                print(f"   ✅ 本地化完成！成功下载 {completed_files}/{total_files} 个文件")
            elif error_files > 0:
                print(f"   ⚠️  部分文件下载失败: {error_files} 个")
        
        print("\n" + "=" * 80)
        print("✅ 状态修复完成！")
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    fix_localization_status()
