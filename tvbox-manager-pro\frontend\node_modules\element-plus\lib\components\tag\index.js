'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var tag$1 = require('./src/tag2.js');
var tag = require('./src/tag.js');
var install = require('../../utils/vue/install.js');

const ElTag = install.withInstall(tag$1["default"]);

exports.tagEmits = tag.tagEmits;
exports.tagProps = tag.tagProps;
exports.ElTag = ElTag;
exports["default"] = ElTag;
//# sourceMappingURL=index.js.map
