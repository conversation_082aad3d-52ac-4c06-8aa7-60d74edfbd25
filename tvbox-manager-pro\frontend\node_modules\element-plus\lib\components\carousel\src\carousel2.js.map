{"version": 3, "file": "carousel2.js", "sources": ["../../../../../../packages/components/carousel/src/carousel.vue"], "sourcesContent": ["<template>\n  <div\n    ref=\"root\"\n    :class=\"carouselClasses\"\n    @mouseenter.stop=\"handleMouseEnter\"\n    @mouseleave.stop=\"handleMouseLeave\"\n  >\n    <transition v-if=\"arrowDisplay\" name=\"carousel-arrow-left\">\n      <button\n        v-show=\"\n          (arrow === 'always' || hover) && (props.loop || activeIndex > 0)\n        \"\n        type=\"button\"\n        :class=\"[ns.e('arrow'), ns.em('arrow', 'left')]\"\n        :aria-label=\"t('el.carousel.leftArrow')\"\n        @mouseenter=\"handleButtonEnter('left')\"\n        @mouseleave=\"handleButtonLeave\"\n        @click.stop=\"throttledArrowClick(activeIndex - 1)\"\n      >\n        <ElIcon>\n          <ArrowLeft />\n        </ElIcon>\n      </button>\n    </transition>\n    <transition v-if=\"arrowDisplay\" name=\"carousel-arrow-right\">\n      <button\n        v-show=\"\n          (arrow === 'always' || hover) &&\n          (props.loop || activeIndex < items.length - 1)\n        \"\n        type=\"button\"\n        :class=\"[ns.e('arrow'), ns.em('arrow', 'right')]\"\n        :aria-label=\"t('el.carousel.rightArrow')\"\n        @mouseenter=\"handleButtonEnter('right')\"\n        @mouseleave=\"handleButtonLeave\"\n        @click.stop=\"throttledArrowClick(activeIndex + 1)\"\n      >\n        <ElIcon>\n          <ArrowRight />\n        </ElIcon>\n      </button>\n    </transition>\n    <div\n      :class=\"ns.e('container')\"\n      :style=\"containerStyle\"\n      @transitionstart=\"handleTransitionStart\"\n      @transitionend=\"handleTransitionEnd\"\n    >\n      <PlaceholderItem />\n      <slot />\n    </div>\n    <items-sorter>\n      <ul v-if=\"indicatorPosition !== 'none'\" :class=\"indicatorsClasses\">\n        <li\n          v-for=\"(item, index) in items\"\n          v-show=\"isTwoLengthShow(index)\"\n          :key=\"index\"\n          :class=\"[\n            ns.e('indicator'),\n            ns.em('indicator', direction),\n            ns.is('active', index === activeIndex),\n          ]\"\n          @mouseenter=\"throttledIndicatorHover(index)\"\n          @click.stop=\"handleIndicatorClick(index)\"\n        >\n          <button\n            :class=\"ns.e('button')\"\n            :aria-label=\"t('el.carousel.indicator', { index: index + 1 })\"\n          >\n            <span v-if=\"hasLabel\">{{ item.props.label }}</span>\n          </button>\n        </li>\n      </ul>\n    </items-sorter>\n    <svg\n      v-if=\"props.motionBlur\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      version=\"1.1\"\n      style=\"display: none\"\n    >\n      <defs>\n        <filter id=\"elCarouselHorizontal\">\n          <feGaussianBlur in=\"SourceGraphic\" stdDeviation=\"12,0\" />\n        </filter>\n        <filter id=\"elCarouselVertical\">\n          <feGaussianBlur in=\"SourceGraphic\" stdDeviation=\"0,10\" />\n        </filter>\n      </defs>\n    </svg>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, unref } from 'vue'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { carouselEmits, carouselProps } from './carousel'\nimport { useCarousel } from './use-carousel'\n\nconst COMPONENT_NAME = 'ElCarousel'\ndefineOptions({\n  name: COMPONENT_NAME,\n})\n\nconst props = defineProps(carouselProps)\nconst emit = defineEmits(carouselEmits)\nconst {\n  root,\n  activeIndex,\n  arrowDisplay,\n  hasLabel,\n  hover,\n  isCardType,\n  items,\n  isVertical,\n  containerStyle,\n  handleButtonEnter,\n  handleButtonLeave,\n  handleIndicatorClick,\n  handleMouseEnter,\n  handleMouseLeave,\n  setActiveItem,\n  prev,\n  next,\n  PlaceholderItem,\n  isTwoLengthShow,\n  ItemsSorter,\n  throttledArrowClick,\n  throttledIndicatorHover,\n} = useCarousel(props, emit, COMPONENT_NAME)\nconst ns = useNamespace('carousel')\n\nconst { t } = useLocale()\n\nconst carouselClasses = computed(() => {\n  const classes = [ns.b(), ns.m(props.direction)]\n  if (unref(isCardType)) {\n    classes.push(ns.m('card'))\n  }\n  return classes\n})\n\nconst indicatorsClasses = computed(() => {\n  const classes = [ns.e('indicators'), ns.em('indicators', props.direction)]\n  if (unref(hasLabel)) {\n    classes.push(ns.em('indicators', 'labels'))\n  }\n  if (props.indicatorPosition === 'outside') {\n    classes.push(ns.em('indicators', 'outside'))\n  }\n  if (unref(isVertical)) {\n    classes.push(ns.em('indicators', 'right'))\n  }\n  return classes\n})\n\nfunction handleTransitionStart(e: TransitionEvent) {\n  if (!props.motionBlur) return\n\n  const kls = unref(isVertical)\n    ? `${ns.namespace.value}-transitioning-vertical`\n    : `${ns.namespace.value}-transitioning`\n  ;(e.currentTarget as HTMLDivElement).classList.add(kls)\n}\n\nfunction handleTransitionEnd(e: TransitionEvent) {\n  if (!props.motionBlur) return\n\n  const kls = unref(isVertical)\n    ? `${ns.namespace.value}-transitioning-vertical`\n    : `${ns.namespace.value}-transitioning`\n  ;(e.currentTarget as HTMLDivElement).classList.remove(kls)\n}\n\ndefineExpose({\n  /** @description active slide index */\n  activeIndex,\n  /** @description manually switch slide, index of the slide to be switched to, starting from 0; or the `name` of corresponding `el-carousel-item` */\n  setActiveItem,\n  /** @description switch to the previous slide */\n  prev,\n  /** @description switch to the next slide */\n  next,\n})\n</script>\n"], "names": ["useCarousel", "useNamespace", "useLocale", "computed", "unref", "_openBlock", "_createElementBlock"], "mappings": ";;;;;;;;;;;;;;uCAqGc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR;;;;;;;AAIA,IAAM,MAAA;AAAA,MACJ,IAAA;AAAA,MACA,WAAA;AAAA,MACA,YAAA;AAAA,MACA,QAAA;AAAA,MACA,KAAA;AAAA,MACA,UAAA;AAAA,MACA,KAAA;AAAA,MACA,UAAA;AAAA,MACA,cAAA;AAAA,MACA,iBAAA;AAAA,MACA,iBAAA;AAAA,MACA,oBAAA;AAAA,MACA,gBAAA;AAAA,MACA,gBAAA;AAAA,MACA,aAAA;AAAA,MACA,IAAA;AAAA,MACA,IAAA;AAAA,MACA,eAAA;AAAA,MACA,eAAA;AAAA,MACA,WAAA;AAAA,MACA,mBAAA;AAAA,MACA,uBAAA;AAAA,KACE,GAAAA,uBAAA,CAAY,KAAO,EAAA,IAAA,EAAM,cAAc,CAAA,CAAA;AAC3C,IAAM,MAAA,EAAA,GAAKC,mBAAa,UAAU,CAAA,CAAA;AAElC,IAAM,MAAA,EAAE,CAAE,EAAA,GAAIC,iBAAU,EAAA,CAAA;AAExB,IAAM,MAAA,eAAA,GAAkBC,aAAS,MAAM;AACrC,MAAM,MAAA,OAAA,GAAU,CAAC,EAAG,CAAA,CAAA,IAAK,EAAG,CAAA,CAAA,CAAE,KAAM,CAAA,SAAS,CAAC,CAAA,CAAA;AAC9C,MAAI,IAAAC,SAAA,CAAM,UAAU,CAAG,EAAA;AACrB,QAAA,OAAA,CAAQ,IAAK,CAAA,EAAA,CAAG,CAAE,CAAA,MAAM,CAAC,CAAA,CAAA;AAAA,OAC3B;AACA,MAAO,OAAA,OAAA,CAAA;AAAA,KACR,CAAA,CAAA;AAED,IAAM,MAAA,iBAAA,GAAoBD,aAAS,MAAM;AACvC,MAAM,MAAA,OAAA,GAAU,CAAC,EAAA,CAAG,CAAE,CAAA,YAAY,CAAG,EAAA,EAAA,CAAG,EAAG,CAAA,YAAA,EAAc,KAAM,CAAA,SAAS,CAAC,CAAA,CAAA;AACzE,MAAI,IAAAC,SAAA,CAAM,QAAQ,CAAG,EAAA;AACnB,QAAA,OAAA,CAAQ,IAAK,CAAA,EAAA,CAAG,EAAG,CAAA,YAAA,EAAc,QAAQ,CAAC,CAAA,CAAA;AAAA,OAC5C;AACA,MAAI,IAAA,KAAA,CAAM,sBAAsB,SAAW,EAAA;AACzC,QAAA,OAAA,CAAQ,IAAK,CAAA,EAAA,CAAG,EAAG,CAAA,YAAA,EAAc,SAAS,CAAC,CAAA,CAAA;AAAA,OAC7C;AACA,MAAI,IAAAA,SAAA,CAAM,UAAU,CAAG,EAAA;AACrB,QAAA,OAAA,CAAQ,IAAK,CAAA,EAAA,CAAG,EAAG,CAAA,YAAA,EAAc,OAAO,CAAC,CAAA,CAAA;AAAA,OAC3C;AACA,MAAO,OAAA,OAAA,CAAA;AAAA,KACR,CAAA,CAAA;AAED,IAAA,SAAS,sBAAsB,CAAoB,EAAA;AACjD,MAAI,IAAA,CAAC,MAAM,UAAY;AAEvB,QAAA,OAAY;AAGX,MAAC,MAAE,GAAA,GAAAA,SAA2C,CAAA,UAAA,CAAA,GAAO,CAAA,EAAA,EAAA,CAAA,SAAA,CAAA,KAAA,CAAA,uBAAA,CAAA,GAAA,CAAA,EAAA,EAAA,CAAA,SAAA,CAAA,KAAA,CAAA,cAAA,CAAA,CAAA;AAAA,MACxD,CAAA,CAAA,aAAA,CAAA,SAAA,CAAA,GAAA,CAAA,GAAA,CAAA,CAAA;AAEA,KAAA;AACE,IAAI,4BAAmB,CAAA,CAAA,EAAA;AAEvB,MAAA,IAAA,CAAA,KAAY,CAAA,UAAgB;AAG3B,QAAG,OAAA;AAAqD,MAC3D,MAAA,GAAA,GAAAA,SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,EAAA,EAAA,CAAA,SAAA,CAAA,KAAA,CAAA,uBAAA,CAAA,GAAA,CAAA,EAAA,EAAA,CAAA,SAAA,CAAA,KAAA,CAAA,cAAA,CAAA,CAAA;AAEA,MAAa,CAAA,CAAA,aAAA,CAAA,SAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA;AAAA,KAAA;AAAA,IAEX,MAAA,CAAA;AAAA,MAAA,WAAA;AAAA,MAEA,aAAA;AAAA,MAAA,IAAA;AAAA,MAEA,IAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAEA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MACD,OAAAC,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}