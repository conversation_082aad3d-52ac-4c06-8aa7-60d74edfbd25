{% extends "base.html" %}

{% block title %}修改密码 - TVBox Manager{% endblock %}

{% block content %}
<div class="flex items-center justify-center py-6">
    <div class="w-full max-w-md">
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="bg-gradient-to-r from-primary-600 to-primary-800 px-6 py-8 text-center text-white">
                <h2 class="text-2xl font-bold mb-2">修改密码</h2>
                <p class="text-primary-100 text-sm">更新您的账户密码</p>
            </div>
            
            <div class="px-6 py-8">
                <form action="{{ url_for('security.change_password') }}" method="POST" name="change_password_form" id="change-password-form" class="custom-form">
                    {{ change_password_form.hidden_tag() }}
                    
                    <div class="form-group">
                        <label for="password" class="form-label flex items-center">
                            <i class="fas fa-lock mr-2 text-primary-600"></i> 当前密码
                        </label>
                        {{ change_password_form.password(class="form-input", placeholder="请输入当前密码") }}
                        {% if change_password_form.password.errors %}
                        <div class="text-red-600 text-sm mt-1">
                            {% for error in change_password_form.password.errors %}
                            <p class="flex items-center"><i class="fas fa-exclamation-circle mr-1"></i> {{ error }}</p>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        <label for="new_password" class="form-label flex items-center">
                            <i class="fas fa-key mr-2 text-primary-600"></i> 新密码
                        </label>
                        {{ change_password_form.new_password(class="form-input", placeholder="请输入新密码") }}
                        {% if change_password_form.new_password.errors %}
                        <div class="text-red-600 text-sm mt-1">
                            {% for error in change_password_form.new_password.errors %}
                            <p class="flex items-center"><i class="fas fa-exclamation-circle mr-1"></i> {{ error }}</p>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        <label for="new_password_confirm" class="form-label flex items-center">
                            <i class="fas fa-check-double mr-2 text-primary-600"></i> 确认新密码
                        </label>
                        {{ change_password_form.new_password_confirm(class="form-input", placeholder="请再次输入新密码") }}
                        {% if change_password_form.new_password_confirm.errors %}
                        <div class="text-red-600 text-sm mt-1">
                            {% for error in change_password_form.new_password_confirm.errors %}
                            <p class="flex items-center"><i class="fas fa-exclamation-circle mr-1"></i> {{ error }}</p>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="flex items-center justify-between mt-8 mb-4">
                        <button type="submit" class="btn btn-primary w-full flex items-center justify-center">
                            <i class="fas fa-save mr-2"></i> 更新密码
                        </button>
                    </div>
                </form>
                
                <div class="mt-6 pt-6 border-t border-gray-200 text-center text-sm text-gray-600">
                    <a href="{{ url_for('interface.index') }}" class="text-primary-600 font-medium hover:text-primary-800 hover:underline">
                        <i class="fas fa-arrow-left mr-1"></i> 返回主页
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 移除Flask-Security额外的页面元素
    const securityContainers = document.querySelectorAll('.container');
    securityContainers.forEach(container => {
        if (container !== document.getElementById('change-password-form').closest('.container')) {
            container.style.display = 'none';
        }
    });
    
    // 移除额外的标题
    const titles = document.querySelectorAll('h1');
    titles.forEach(title => {
        if (title.textContent.includes('Change') || title.textContent.includes('修改')) {
            title.style.display = 'none';
        }
    });
});
</script>
{% endblock %} 