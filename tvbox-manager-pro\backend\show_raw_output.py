#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests

def show_raw_config():
    """显示原始配置文件内容"""
    
    url = 'http://xhztv.top/4k.json'
    headers = {
        'User-Agent': 'okhttp/3.12.11',
        'Accept': '*/*'
    }

    try:
        response = requests.get(url, headers=headers, timeout=10)
        content = response.text
        
        print(f'状态码: {response.status_code}')
        print(f'Content-Type: {response.headers.get("Content-Type", "未知")}')
        print(f'内容长度: {len(content)}')
        print(f'=' * 80)
        print('完整原始内容:')
        print(f'=' * 80)
        print(content)
        print(f'=' * 80)
        print('原始内容结束')
        
        # 保存到文件
        with open('raw_config_output.txt', 'w', encoding='utf-8') as f:
            f.write(content)
        print(f'\n✅ 原始内容已保存到 raw_config_output.txt')
        
        # 尝试用系统解析，显示错误
        print(f'\n' + '=' * 80)
        print('系统解析测试:')
        print(f'=' * 80)
        
        try:
            from app.services.tvbox_decryptor import TVBoxDecryptor
            decryptor = TVBoxDecryptor()
            
            # 直接调用解密方法
            decrypted_content, method = decryptor.decrypt_config_url(url)
            print(f'解密方法: {method}')
            print(f'解密后内容长度: {len(decrypted_content)}')
            
            # 尝试解析配置
            config_info = decryptor.parse_tvbox_config_content(decrypted_content)
            print(f'解析结果:')
            print(f'  站点数: {len(config_info.get("sites", []))}')
            print(f'  直播源数: {len(config_info.get("lives", []))}')
            print(f'  解析器数: {len(config_info.get("parses", []))}')
            
        except Exception as system_e:
            print(f'系统解析错误: {system_e}')
            import traceback
            traceback.print_exc()
        
    except Exception as e:
        print(f'请求失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    show_raw_config()
