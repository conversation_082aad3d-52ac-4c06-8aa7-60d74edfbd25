{"version": 3, "file": "steps.js", "sources": ["../../../../../../packages/components/tour/src/steps.ts"], "sourcesContent": ["import { defineComponent } from 'vue'\nimport { flattedChildren, isArray } from '@element-plus/utils'\n\nimport type { FlattenVNodes } from '@element-plus/utils'\nimport type { Component, VNode } from 'vue'\n\nexport default defineComponent({\n  name: 'ElTourSteps',\n  props: {\n    current: {\n      type: Number,\n      default: 0,\n    },\n  },\n  emits: ['update-total'],\n  setup(props, { slots, emit }) {\n    let cacheTotal = 0\n\n    return () => {\n      const children = slots.default?.()!\n      const result: VNode[] = []\n      let total = 0\n\n      function filterSteps(children?: FlattenVNodes) {\n        if (!isArray(children)) return\n        ;(children as VNode[]).forEach((item) => {\n          const name = ((item?.type || {}) as Component)?.name\n\n          if (name === 'ElTourStep') {\n            result.push(item)\n            total += 1\n          }\n        })\n      }\n\n      if (children.length) {\n        filterSteps(flattedChildren(children![0]?.children))\n      }\n\n      if (cacheTotal !== total) {\n        cacheTotal = total\n        emit('update-total', total)\n      }\n\n      if (result.length) {\n        return result[props.current]\n      }\n      return null\n    }\n  },\n})\n"], "names": ["defineComponent", "isArray", "flatted<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;AAEA,kBAAeA,mBAAe,CAAC;AAC/B,EAAE,IAAI,EAAE,aAAa;AACrB,EAAE,KAAK,EAAE;AACT,IAAI,OAAO,EAAE;AACb,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,OAAO,EAAE,CAAC;AAChB,KAAK;AACL,GAAG;AACH,EAAE,KAAK,EAAE,CAAC,cAAc,CAAC;AACzB,EAAE,KAAK,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;AAChC,IAAI,IAAI,UAAU,GAAG,CAAC,CAAC;AACvB,IAAI,OAAO,MAAM;AACjB,MAAM,IAAI,EAAE,EAAE,EAAE,CAAC;AACjB,MAAM,MAAM,QAAQ,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC9E,MAAM,MAAM,MAAM,GAAG,EAAE,CAAC;AACxB,MAAM,IAAI,KAAK,GAAG,CAAC,CAAC;AACpB,MAAM,SAAS,WAAW,CAAC,SAAS,EAAE;AACtC,QAAQ,IAAI,CAACC,cAAO,CAAC,SAAS,CAAC;AAC/B,UAAU,OAAO;AACjB,QAAQ,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AACpC,UAAU,IAAI,GAAG,CAAC;AAClB,UAAU,MAAM,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,KAAK,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC;AACrG,UAAU,IAAI,IAAI,KAAK,YAAY,EAAE;AACrC,YAAY,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC9B,YAAY,KAAK,IAAI,CAAC,CAAC;AACvB,WAAW;AACX,SAAS,CAAC,CAAC;AACX,OAAO;AACP,MAAM,IAAI,QAAQ,CAAC,MAAM,EAAE;AAC3B,QAAQ,WAAW,CAACC,qBAAe,CAAC,CAAC,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;AACxF,OAAO;AACP,MAAM,IAAI,UAAU,KAAK,KAAK,EAAE;AAChC,QAAQ,UAAU,GAAG,KAAK,CAAC;AAC3B,QAAQ,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;AACpC,OAAO;AACP,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE;AACzB,QAAQ,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACrC,OAAO;AACP,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK,CAAC;AACN,GAAG;AACH,CAAC,CAAC;;;;"}