import Cookies from 'js-cookie'

const TOKEN_KEY = 'tvbox_access_token'
const REFRESH_TOKEN_KEY = 'tvbox_refresh_token'

// 令牌管理
export function getToken() {
  return Cookies.get(TOKEN_KEY)
}

export function setToken(token) {
  return Cookies.set(TOKEN_KEY, token, { expires: 7 }) // 7天过期
}

export function removeToken() {
  Cookies.remove(TOKEN_KEY)
  Cookies.remove(REFRESH_TOKEN_KEY)
}

export function getRefreshToken() {
  return Cookies.get(REFRESH_TOKEN_KEY)
}

export function setRefreshToken(token) {
  return Cookies.set(REFRESH_TOKEN_KEY, token, { expires: 30 }) // 30天过期
}

// 权限检查
export function hasPermission(permission, userPermissions = []) {
  if (!permission) return true
  return userPermissions.includes(permission)
}

export function hasRole(role, userRoles = []) {
  if (!role) return true
  return userRoles.includes(role)
}

export function hasAnyRole(roles = [], userRoles = []) {
  if (!roles.length) return true
  return roles.some(role => userRoles.includes(role))
}

export function hasAllRoles(roles = [], userRoles = []) {
  if (!roles.length) return true
  return roles.every(role => userRoles.includes(role))
}

// 用户信息本地存储
const USER_INFO_KEY = 'tvbox_user_info'

export function getUserInfo() {
  const userInfo = localStorage.getItem(USER_INFO_KEY)
  return userInfo ? JSON.parse(userInfo) : null
}

export function setUserInfo(userInfo) {
  localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo))
}

export function removeUserInfo() {
  localStorage.removeItem(USER_INFO_KEY)
}

// 清除所有认证信息
export function clearAuth() {
  removeToken()
  removeUserInfo()
}
