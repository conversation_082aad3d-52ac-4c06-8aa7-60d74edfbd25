#!/usr/bin/env python3
"""
检查接口3的配置
"""
import sqlite3
import os
import json

def check_interface3():
    """检查接口3的配置"""
    db_path = "data/tvbox.db"
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取接口3的完整信息
        cursor.execute("""
            SELECT id, name, url, config_content
            FROM interface_sources 
            WHERE id = 3
        """)
        
        result = cursor.fetchone()
        if result:
            interface_id, name, url, config_content = result
            print(f"接口ID: {interface_id}")
            print(f"接口名称: {name}")
            print(f"接口URL: {url}")
            
            if config_content:
                try:
                    config_data = json.loads(config_content)
                    
                    # 显示配置的基本信息
                    spider = config_data.get('spider', 'N/A')
                    sites = config_data.get('sites', [])
                    lives = config_data.get('lives', [])
                    parses = config_data.get('parses', [])
                    
                    print(f"\nSpider: {spider}")
                    print(f"Sites数量: {len(sites)}")
                    print(f"Lives数量: {len(lives)}")
                    print(f"Parses数量: {len(parses)}")
                    
                    # 显示前几个sites的详细信息
                    print(f"\n前3个Sites:")
                    for i, site in enumerate(sites[:3]):
                        name = site.get('name', 'N/A')
                        api = site.get('api', 'N/A')
                        jar = site.get('jar', 'N/A')
                        ext = site.get('ext', 'N/A')
                        print(f"  Site {i+1}: {name}")
                        print(f"    API: {api}")
                        if jar != 'N/A':
                            print(f"    JAR: {jar}")
                        if ext != 'N/A':
                            print(f"    EXT: {ext}")
                    
                    # 检查是否看起来像真实配置
                    real_indicators = 0
                    if len(sites) > 5:
                        real_indicators += 1
                        print(f"\n✅ 有 {len(sites)} 个站点，看起来像真实配置")
                    
                    if len(lives) > 0:
                        real_indicators += 1
                        print(f"✅ 有 {len(lives)} 个直播源")
                    
                    if len(parses) > 0:
                        real_indicators += 1
                        print(f"✅ 有 {len(parses)} 个解析器")
                    
                    # 检查是否有多样化的API类型
                    api_types = set()
                    for site in sites:
                        api = site.get('api', '')
                        if api.startswith('csp_'):
                            api_types.add('csp')
                        elif api.startswith('http'):
                            api_types.add('http')
                        else:
                            api_types.add('other')
                    
                    if len(api_types) > 1:
                        real_indicators += 1
                        print(f"✅ 有多种API类型: {api_types}")
                    
                    if real_indicators >= 3:
                        print(f"\n🎯 这看起来是一个真实的TVBox配置！")
                        return True
                    else:
                        print(f"\n❓ 这可能不是完整的真实配置")
                        return False
                        
                except Exception as e:
                    print(f"配置解析失败: {e}")
                    return False
        else:
            print("未找到接口3")
            return False
            
    except Exception as e:
        print(f"检查失败: {str(e)}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    check_interface3()
