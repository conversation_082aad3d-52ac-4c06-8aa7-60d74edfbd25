{"version": 3, "file": "collection-item.mjs", "sources": ["../../../../../../packages/components/collection/src/collection-item.vue"], "sourcesContent": ["<template>\n  <slot />\n</template>\n\n<script lang=\"ts\" setup>\ndefineOptions({\n  name: 'ElCollectionItem',\n  inheritAttrs: false,\n})\n</script>\n"], "names": ["DO_defineComponent", "_renderSlot"], "mappings": ";;;kCAKcA,eAAA,CAAA;AAAA,EACZ,IAAM,EAAA,kBAAA;AAAA,EACN,YAAc,EAAA,KAAA;AAChB,CAAA,CAAA,CAAA;;SAPEC,UAAQ,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA;;;;;;"}