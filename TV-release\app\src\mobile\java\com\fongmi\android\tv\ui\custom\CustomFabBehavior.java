package com.fongmi.android.tv.ui.custom;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.core.view.ViewCompat;

import com.google.android.material.floatingactionbutton.FloatingActionButton;

public class CustomFabBehavior extends FloatingActionButton.Behavior {

    public CustomFabBehavior() {
        super();
    }

    public CustomFabBehavior(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    public boolean onStartNestedScroll(@NonNull CoordinatorLayout coordinatorLayout, @NonNull FloatingActionButton child, @NonNull View directTargetChild, @NonNull View target, int axes, int type) {
        return axes == ViewCompat.SCROLL_AXIS_VERTICAL || super.onStartNestedScroll(coordinatorLayout, child, directTargetChild, target, axes, type);
    }

    @Override
    public void onNestedScroll(@NonNull CoordinatorLayout coordinatorLayout, @NonNull FloatingActionButton child, @NonNull View target, int dxConsumed, int dyConsumed, int dxUnconsumed, int dyUnconsumed, int type, @NonNull int[] consumed) {
        super.onNestedScroll(coordinatorLayout, child, target, dxConsumed, dyConsumed, dxUnconsumed, dyUnconsumed, type, consumed);
        if ("top".equals(child.getTag())) {
            if (dyConsumed > 0 && child.getVisibility() == View.INVISIBLE) {
                child.show();
            } else if (dyConsumed < 0 && child.getVisibility() == View.VISIBLE) {
                onHide(child);
            }
        } else {
            if (dyConsumed > 0 && child.getVisibility() == View.VISIBLE) {
                onHide(child);
            } else if (dyConsumed < 0 && child.getVisibility() == View.INVISIBLE) {
                child.show();
            }
        }
    }

    private void onHide(FloatingActionButton child) {
        child.hide(new FloatingActionButton.OnVisibilityChangedListener() {
            @Override
            public void onHidden(FloatingActionButton fab) {
                child.setVisibility(View.INVISIBLE);
            }
        });
    }
}