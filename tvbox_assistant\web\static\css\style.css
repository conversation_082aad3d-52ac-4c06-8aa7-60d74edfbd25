/**
 * TVBox助手自定义样式
 */

/* 页面全局样式 */
body {
    background-color: #f8f9fa;
}

/* 顶部导航栏样式 */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: bold;
}

/* 卡片样式 */
.card {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: none;
    margin-bottom: 20px;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
    font-weight: 500;
}

/* 表单元素样式 */
.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 按钮增强样式 */
.btn {
    border-radius: 0.25rem;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

/* 提示框样式 */
.alert {
    border-radius: 0.25rem;
    border: none;
}

/* 代码块样式 */
pre {
    background-color: #f8f9fa;
    border-radius: 0.25rem;
    padding: 15px;
    font-size: 0.9rem;
    max-height: 500px;
    overflow: auto;
}

code {
    font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
}

/* 资源卡片样式 */
.resource-card {
    transition: transform 0.2s;
    cursor: pointer;
}

.resource-card:hover {
    transform: translateY(-2px);
}

/* 表格样式美化 */
.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table th {
    border-top: none;
}

/* 加载指示器样式 */
.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px 0;
}

/* 标签页样式 */
.nav-tabs .nav-link {
    border: none;
    color: #495057;
    padding: 0.75rem 1rem;
    border-bottom: 2px solid transparent;
}

.nav-tabs .nav-link.active {
    color: #0d6efd;
    border-bottom: 2px solid #0d6efd;
    font-weight: 500;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .container {
        padding-left: 10px;
        padding-right: 10px;
    }
} 