<template>
  <div v-if="!item.meta?.hideInMenu">
    <!-- 有子菜单的情况 -->
    <el-sub-menu
      v-if="hasChildren"
      :index="item.path"
      :popper-append-to-body="true"
    >
      <template #title>
        <el-icon v-if="item.meta?.icon">
          <component :is="item.meta.icon" />
        </el-icon>
        <span>{{ item.meta?.title }}</span>
      </template>
      
      <SidebarItem
        v-for="child in item.children"
        :key="child.path"
        :item="child"
        :base-path="resolvePath(child.path)"
      />
    </el-sub-menu>
    
    <!-- 单个菜单项 -->
    <el-menu-item
      v-else
      :index="resolvePath(item.path)"
      @click="handleClick"
    >
      <el-icon v-if="item.meta?.icon">
        <component :is="item.meta.icon" />
      </el-icon>
      <template #title>
        <span>{{ item.meta?.title }}</span>
      </template>
    </el-menu-item>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { isExternal } from '@/utils/validate'

const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  basePath: {
    type: String,
    default: ''
  }
})

const router = useRouter()

// 计算属性
const hasChildren = computed(() => {
  return props.item.children && props.item.children.length > 0
})

// 方法
const resolvePath = (routePath) => {
  if (isExternal(routePath)) {
    return routePath
  }

  if (isExternal(props.basePath)) {
    return props.basePath
  }

  // 如果路径已经是绝对路径，直接返回
  if (routePath.startsWith('/')) {
    return routePath
  }

  return props.basePath ? `${props.basePath}/${routePath}` : `/${routePath}`
}

const handleClick = () => {
  const path = resolvePath(props.item.path)
  
  if (isExternal(path)) {
    window.open(path, '_blank')
  } else {
    router.push(path)
  }
}
</script>
