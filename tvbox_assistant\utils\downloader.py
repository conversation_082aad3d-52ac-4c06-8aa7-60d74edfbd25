#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
资源下载器
用于下载TVBox配置文件中引用的远程资源
"""

import os
import time
import logging
import hashlib
import aiohttp
import asyncio
from urllib.parse import urlparse
from .decryptor import TVBoxDecryptor

class ResourceDownloader:
    """资源下载器"""
    
    def __init__(self, output_dir="./downloads", max_concurrency=5):
        """
        初始化下载器
        
        Args:
            output_dir: 输出目录
            max_concurrency: 最大并发下载数
        """
        self.output_dir = output_dir
        self.download_map = {}  # URL到本地路径的映射
        self.logger = logging.getLogger('tvbox_downloader')
        self.decryptor = TVBoxDecryptor()
        self.semaphore = asyncio.Semaphore(max_concurrency)
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
    
    async def download_resources(self, resources):
        """
        下载资源
        
        Args:
            resources: 资源字典 {url: {type, path, field, encrypted}}
            
        Returns:
            dict: URL到本地路径的映射
        """
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 按资源类型创建子目录
        resource_types = set(info['type'] for info in resources.values())
        for res_type in resource_types:
            os.makedirs(os.path.join(self.output_dir, res_type), exist_ok=True)
        
        self.logger.info(f"开始下载 {len(resources)} 个远程资源...")
        
        # 创建下载任务
        tasks = []
        for url, info in resources.items():
            # 解密URL(如果需要)
            decrypt_url = url
            if info.get('encrypted', False):
                decrypt_url, method = self.decryptor.decrypt(url, info.get('field'))
                if decrypt_url != url:
                    self.logger.info(f"成功解密URL: {url} -> {decrypt_url}，使用方法: {method}")
                    # 更新资源URL
                    resources[url]['decrypted_url'] = decrypt_url
                else:
                    self.logger.warning(f"无法解密URL: {url}")
            
            # 只下载有效URL
            if decrypt_url.startswith(('http://', 'https://')):
                task = self.download_resource(decrypt_url, info)
                tasks.append(task)
            else:
                self.logger.warning(f"跳过无效URL: {decrypt_url}")
                self.download_map[url] = url  # 保持原始URL
        
        # 并发下载
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            success_count = 0
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    self.logger.error(f"下载任务 {i+1} 失败: {str(result)}")
                else:
                    success_count += 1
            
            self.logger.info(f"下载完成! 成功: {success_count}/{len(tasks)}")
        else:
            self.logger.info("没有需要下载的有效资源")
        
        return self.download_map
    
    async def download_resource(self, url, info):
        """
        下载单个资源
        
        Args:
            url: 资源URL
            info: 资源信息
            
        Returns:
            str: 本地保存路径
        """
        async with self.semaphore:
            original_url = info.get('original_url', url)
            
            # 如果已经下载过，直接返回本地路径
            if original_url in self.download_map:
                return self.download_map[original_url]
            
            try:
                self.logger.info(f"下载资源: {url}")
                
                # 生成文件名
                filename = self.generate_filename(url, info)
                output_path = os.path.join(self.output_dir, info['type'], filename)
                
                # 设置超时和重试
                max_retries = 3
                retry_count = 0
                timeout = aiohttp.ClientTimeout(total=30)  # 30秒超时
                
                while retry_count < max_retries:
                    try:
                        async with aiohttp.ClientSession(timeout=timeout) as session:
                            async with session.get(url, headers=self.headers) as response:
                                if response.status == 200:
                                    # 获取内容类型
                                    content_type = response.headers.get('Content-Type', '')
                                    
                                    # 读取文件内容
                                    content = await response.read()
                                    
                                    # 保存文件
                                    os.makedirs(os.path.dirname(output_path), exist_ok=True)
                                    with open(output_path, 'wb') as f:
                                        f.write(content)
                                    
                                    # 记录文件信息
                                    file_size = len(content)
                                    file_info = {
                                        'path': output_path,
                                        'size': file_size,
                                        'content_type': content_type,
                                        'downloaded_at': time.time()
                                    }
                                    
                                    # 相对路径，用于更新配置文件
                                    rel_path = os.path.relpath(output_path, os.getcwd()).replace('\\', '/')
                                    if not rel_path.startswith('./'):
                                        rel_path = f"./{rel_path}"
                                    
                                    self.logger.info(f"资源已保存到: {rel_path}, 大小: {file_size} 字节")
                                    
                                    # 保存映射关系
                                    self.download_map[original_url] = rel_path
                                    return rel_path
                                else:
                                    self.logger.warning(f"下载失败，状态码: {response.status}, URL: {url}")
                                    retry_count += 1
                                    await asyncio.sleep(1)  # 重试前等待
                    except (aiohttp.ClientError, asyncio.TimeoutError) as e:
                        self.logger.warning(f"下载出错: {str(e)}, 重试({retry_count + 1}/{max_retries})")
                        retry_count += 1
                        await asyncio.sleep(2)  # 重试前等待
                
                # 重试次数用完，仍然失败
                self.logger.error(f"下载资源失败 {url}: 重试次数已用完")
                self.download_map[original_url] = original_url  # 保持原始URL
                return original_url
                
            except Exception as e:
                self.logger.error(f"下载资源异常 {url}: {str(e)}")
                self.download_map[original_url] = original_url
                raise e
    
    def generate_filename(self, url, info):
        """
        根据URL生成文件名
        
        Args:
            url: 资源URL
            info: 资源信息
            
        Returns:
            str: 生成的文件名
        """
        try:
            parsed = urlparse(url)
            path = parsed.path
            
            # 获取原始文件名
            basename = os.path.basename(path)
            
            # 如果URL没有文件名部分，生成一个基于URL的唯一名称
            if not basename or basename == '/' or '.' not in basename:
                # 生成URL的哈希作为文件名
                hash_obj = hashlib.md5(url.encode())
                url_hash = hash_obj.hexdigest()[:8]
                
                # 根据资源类型决定扩展名
                ext = self._guess_extension(info)
                
                basename = f"{info['type']}_{url_hash}{ext}"
            
            # 确保文件名安全
            safe_name = self._sanitize_filename(basename)
            
            # 如果文件名已经存在，添加哈希值避免冲突
            target_path = os.path.join(self.output_dir, info['type'], safe_name)
            if os.path.exists(target_path):
                name, ext = os.path.splitext(safe_name)
                timestamp = int(time.time())
                safe_name = f"{name}_{timestamp}{ext}"
            
            return safe_name
        except Exception as e:
            self.logger.error(f"生成文件名失败: {str(e)}")
            # 生成一个基于时间戳的默认名称
            timestamp = int(time.time())
            hash_obj = hashlib.md5(url.encode())
            url_hash = hash_obj.hexdigest()[:8]
            return f"resource_{info['type']}_{timestamp}_{url_hash}.bin"
    
    def _guess_extension(self, info):
        """
        根据资源类型猜测文件扩展名
        
        Args:
            info: 资源信息
            
        Returns:
            str: 文件扩展名
        """
        res_type = info['type']
        field = info.get('field')
        
        # 根据资源类型和字段判断可能的扩展名
        if res_type == 'spider' or field == 'jar':
            return '.jar'
        elif res_type == 'lives' or field == 'url' and 'lives' in info.get('path', []):
            return '.txt'
        elif field == 'api' and 'js' in info.get('path', []):
            return '.js'
        elif field in ['api', 'ext', 'parse', 'json']:
            return '.json'
        elif res_type == 'wallpaper':
            return '.jpg'
        else:
            return '.txt'  # 默认扩展名
    
    def _sanitize_filename(self, filename):
        """
        清理文件名，移除不安全字符
        
        Args:
            filename: 原始文件名
            
        Returns:
            str: 安全的文件名
        """
        # 移除文件名中不安全的字符
        unsafe_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|', '%']
        for char in unsafe_chars:
            filename = filename.replace(char, '_')
        
        # 处理查询参数
        if '?' in filename:
            filename = filename.split('?')[0]
        
        return filename 