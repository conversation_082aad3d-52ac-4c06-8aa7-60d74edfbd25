#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
API v1 路由汇总
"""

from fastapi import APIRouter

from app.api.v1 import auth, interfaces, subscriptions, users, system, decrypt

api_router = APIRouter()

# 注册各模块路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
api_router.include_router(interfaces.router, prefix="/interfaces", tags=["接口管理"])
api_router.include_router(subscriptions.router, prefix="/subscriptions", tags=["订阅管理"])
api_router.include_router(users.router, prefix="/users", tags=["用户管理"])
api_router.include_router(system.router, prefix="/system", tags=["系统管理"])
api_router.include_router(decrypt.router, prefix="/decrypt", tags=["解密API"])

# 健康检查
@api_router.get("/health", tags=["系统"])
async def health_check():
    """API健康检查"""
    return {
        "status": "healthy",
        "version": "1.0.0",
        "api_version": "v1"
    }
