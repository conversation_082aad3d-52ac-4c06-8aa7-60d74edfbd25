<template>
  <div class="sidebar">
    <!-- Logo区域 -->
    <div class="sidebar-logo" :class="{ 'collapsed': appStore.isCollapsed }">
      <router-link to="/" class="logo-link">
        <el-icon size="32" color="#409EFF">
          <Monitor />
        </el-icon>
        <transition name="fade">
          <span v-show="!appStore.isCollapsed" class="logo-text">
            TVBox Manager Pro
          </span>
        </transition>
      </router-link>
    </div>
    
    <!-- 菜单区域 -->
    <el-scrollbar class="sidebar-menu-container">
      <el-menu
        :default-active="activeMenu"
        :collapse="appStore.isCollapsed"
        :unique-opened="true"
        :collapse-transition="false"
        mode="vertical"
        background-color="var(--el-bg-color)"
        text-color="var(--el-text-color-primary)"
        active-text-color="var(--el-color-primary)"
        @select="handleMenuSelect"
      >
        <SidebarItem
          v-for="route in menuRoutes"
          :key="route.path"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import { Monitor } from '@element-plus/icons-vue'
import SidebarItem from './SidebarItem.vue'

const route = useRoute()
const router = useRouter()
const appStore = useAppStore()
const userStore = useUserStore()

// 计算属性
const activeMenu = computed(() => {
  const { meta, path } = route
  if (meta.activeMenu) {
    return meta.activeMenu
  }
  return path
})

const menuRoutes = computed(() => {
  // 直接定义菜单项，确保路径正确
  const menuItems = [
    {
      path: '/dashboard',
      name: 'Dashboard',
      meta: {
        title: '控制台',
        icon: 'Monitor'
      }
    },
    {
      path: '/interfaces',
      name: 'Interfaces',
      meta: {
        title: '接口管理',
        icon: 'Link'
      }
    },
    {
      path: '/configs',
      name: 'Configs',
      meta: {
        title: '配置管理',
        icon: 'Document'
      }
    },
    {
      path: '/subscriptions',
      name: 'Subscriptions',
      meta: {
        title: '我的订阅',
        icon: 'Star'
      }
    }
  ]

  // 根据用户权限过滤菜单
  return menuItems.filter(item => hasPermission(item))
})

// 方法
const hasPermission = (route) => {
  // 暂时返回true，后续可以根据用户角色进行权限控制
  return true
}

const handleMenuSelect = (index) => {
  if (appStore.isMobile) {
    appStore.toggleSidebar()
  }
}
</script>

<style lang="scss" scoped>
.sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
}

.sidebar-logo {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid var(--el-border-color-light);
  transition: all 0.28s;
  
  &.collapsed {
    padding: 0;
  }
  
  .logo-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--el-text-color-primary);
    font-weight: 600;
    font-size: 18px;
    
    .logo-text {
      margin-left: 12px;
      white-space: nowrap;
    }
  }
}

.sidebar-menu-container {
  flex: 1;
  
  :deep(.el-scrollbar__view) {
    height: 100%;
  }
  
  :deep(.el-menu) {
    border: none;
    height: 100%;
    width: 100% !important;
  }
  
  :deep(.el-menu-item) {
    height: 48px;
    line-height: 48px;
    
    &.is-active {
      background-color: var(--el-color-primary-light-9);
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 3px;
        background-color: var(--el-color-primary);
      }
    }
    
    &:hover {
      background-color: var(--el-color-primary-light-9);
    }
  }
  
  :deep(.el-sub-menu__title) {
    height: 48px;
    line-height: 48px;
    
    &:hover {
      background-color: var(--el-color-primary-light-9);
    }
  }
}

// 过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.28s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 暗色模式适配
.dark {
  .sidebar-logo {
    border-bottom-color: var(--el-border-color-darker);
  }
}
</style>
