#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
直播管理视图
实现直播源的管理功能
"""

import json
import logging
from datetime import datetime
from flask import (
    Blueprint, render_template, request, jsonify, 
    current_app, flash, redirect, url_for
)
from flask_security import login_required, roles_required, current_user

from tvbox_manager.config.models import db, TVBoxConfig, LiveGroup, LiveChannel

# 创建蓝图
live_bp = Blueprint('live', __name__, url_prefix='/live')

# 获取日志记录器
logger = logging.getLogger('tvbox_manager.live')

@live_bp.route('/')
@login_required
def index():
    """直播管理首页"""
    # 获取所有直播分组
    groups = LiveGroup.query.all()
    
    # 获取关联配置信息
    configs = TVBoxConfig.query.all()
    
    return render_template(
        'tabler/live/index.html', 
        groups=groups,
        configs=configs,
        active_nav='live'
    )

@live_bp.route('/group/add', methods=['GET', 'POST'])
@login_required
def add_group():
    """添加直播分组"""
    if request.method == 'POST':
        name = request.form.get('name', '')
        config_id = request.form.get('config_id')
        
        if not name:
            flash('分组名称不能为空', 'error')
            return redirect(url_for('live.add_group'))
        
        try:
            # 创建直播分组
            group = LiveGroup(
                name=name,
                config_id=config_id
            )
            
            db.session.add(group)
            db.session.commit()
            
            flash('直播分组添加成功', 'success')
            return redirect(url_for('live.index'))
        except Exception as e:
            db.session.rollback()
            logger.error(f"添加直播分组失败: {str(e)}")
            flash(f'添加直播分组失败: {str(e)}', 'error')
            return redirect(url_for('live.add_group'))
    
    # 获取配置列表
    configs = TVBoxConfig.query.all()
    
    return render_template('tabler/live/add_group.html', configs=configs, active_nav='live')

@live_bp.route('/group/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_group(id):
    """编辑直播分组"""
    group = LiveGroup.query.get_or_404(id)
    
    if request.method == 'POST':
        name = request.form.get('name', '')
        config_id = request.form.get('config_id')
        
        if not name:
            flash('分组名称不能为空', 'error')
            return redirect(url_for('live.edit_group', id=id))
        
        try:
            # 更新分组信息
            group.name = name
            group.config_id = config_id
            
            db.session.commit()
            
            flash('直播分组更新成功', 'success')
            return redirect(url_for('live.index'))
        except Exception as e:
            db.session.rollback()
            logger.error(f"更新直播分组失败: {str(e)}")
            flash(f'更新直播分组失败: {str(e)}', 'error')
            return redirect(url_for('live.edit_group', id=id))
    
    # 获取配置列表
    configs = TVBoxConfig.query.all()
    
    return render_template('tabler/live/edit_group.html', group=group, configs=configs, active_nav='live')

@live_bp.route('/group/delete/<int:id>')
@login_required
@roles_required('admin')
def delete_group(id):
    """删除直播分组"""
    group = LiveGroup.query.get_or_404(id)
    
    try:
        # 删除关联的频道
        LiveChannel.query.filter_by(group_id=id).delete()
        
        # 删除分组
        db.session.delete(group)
        db.session.commit()
        
        flash('直播分组删除成功', 'success')
    except Exception as e:
        db.session.rollback()
        logger.error(f"删除直播分组失败: {str(e)}")
        flash(f'删除直播分组失败: {str(e)}', 'error')
    
    return redirect(url_for('live.index'))

@live_bp.route('/channel/<int:group_id>')
@login_required
def channel_list(group_id):
    """直播频道列表"""
    group = LiveGroup.query.get_or_404(group_id)
    channels = LiveChannel.query.filter_by(group_id=group_id).all()
    
    return render_template(
        'tabler/live/channel_list.html',
        group=group,
        channels=channels,
        active_nav='live'
    )

@live_bp.route('/channel/add/<int:group_id>', methods=['GET', 'POST'])
@login_required
def add_channel(group_id):
    """添加直播频道"""
    group = LiveGroup.query.get_or_404(group_id)
    
    if request.method == 'POST':
        name = request.form.get('name', '')
        urls = request.form.get('urls', '')
        
        if not name or not urls:
            flash('频道名称和URL不能为空', 'error')
            return redirect(url_for('live.add_channel', group_id=group_id))
        
        try:
            # 创建直播频道
            channel = LiveChannel(
                name=name,
                urls=urls,
                group_id=group_id
            )
            
            db.session.add(channel)
            db.session.commit()
            
            flash('直播频道添加成功', 'success')
            return redirect(url_for('live.channel_list', group_id=group_id))
        except Exception as e:
            db.session.rollback()
            logger.error(f"添加直播频道失败: {str(e)}")
            flash(f'添加直播频道失败: {str(e)}', 'error')
            return redirect(url_for('live.add_channel', group_id=group_id))
    
    return render_template('tabler/live/add_channel.html', group=group, active_nav='live')

@live_bp.route('/channel/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_channel(id):
    """编辑直播频道"""
    channel = LiveChannel.query.get_or_404(id)
    group = LiveGroup.query.get(channel.group_id)
    
    if request.method == 'POST':
        name = request.form.get('name', '')
        urls = request.form.get('urls', '')
        
        if not name or not urls:
            flash('频道名称和URL不能为空', 'error')
            return redirect(url_for('live.edit_channel', id=id))
        
        try:
            # 更新频道信息
            channel.name = name
            channel.urls = urls
            
            db.session.commit()
            
            flash('直播频道更新成功', 'success')
            return redirect(url_for('live.channel_list', group_id=channel.group_id))
        except Exception as e:
            db.session.rollback()
            logger.error(f"更新直播频道失败: {str(e)}")
            flash(f'更新直播频道失败: {str(e)}', 'error')
            return redirect(url_for('live.edit_channel', id=id))
    
    return render_template('tabler/live/edit_channel.html', channel=channel, group=group, active_nav='live')

@live_bp.route('/channel/delete/<int:id>')
@login_required
def delete_channel(id):
    """删除直播频道"""
    channel = LiveChannel.query.get_or_404(id)
    group_id = channel.group_id
    
    try:
        # 删除频道
        db.session.delete(channel)
        db.session.commit()
        
        flash('直播频道删除成功', 'success')
    except Exception as e:
        db.session.rollback()
        logger.error(f"删除直播频道失败: {str(e)}")
        flash(f'删除直播频道失败: {str(e)}', 'error')
    
    return redirect(url_for('live.channel_list', group_id=group_id))

@live_bp.route('/import/<int:group_id>', methods=['GET', 'POST'])
@login_required
def import_channels(group_id):
    """批量导入直播频道"""
    group = LiveGroup.query.get_or_404(group_id)
    
    if request.method == 'POST':
        content = request.form.get('content', '')
        
        if not content:
            flash('导入内容不能为空', 'error')
            return redirect(url_for('live.import_channels', group_id=group_id))
        
        try:
            # 解析频道内容
            imported = 0
            lines = content.strip().split('\n')
            
            for line in lines:
                if line.strip() and ',' in line:
                    parts = line.strip().split(',', 1)
                    if len(parts) == 2:
                        name = parts[0].strip()
                        url = parts[1].strip()
                        
                        if name and url:
                            channel = LiveChannel(
                                name=name,
                                urls=url,
                                group_id=group_id
                            )
                            db.session.add(channel)
                            imported += 1
            
            if imported > 0:
                db.session.commit()
                flash(f'成功导入 {imported} 个直播频道', 'success')
            else:
                flash('未导入任何频道，请检查格式', 'warning')
                
            return redirect(url_for('live.channel_list', group_id=group_id))
        except Exception as e:
            db.session.rollback()
            logger.error(f"导入直播频道失败: {str(e)}")
            flash(f'导入直播频道失败: {str(e)}', 'error')
            return redirect(url_for('live.import_channels', group_id=group_id))
    
    return render_template('tabler/live/import_channels.html', group=group, active_nav='live')

@live_bp.route('/export/<int:group_id>')
@login_required
def export_channels(group_id):
    """导出直播频道"""
    group = LiveGroup.query.get_or_404(group_id)
    channels = LiveChannel.query.filter_by(group_id=group_id).all()
    
    content = ""
    for channel in channels:
        content += f"{channel.name},{channel.urls}\n"
    
    return render_template('tabler/live/export_channels.html', group=group, content=content, active_nav='live')

@live_bp.route('/api/groups')
def api_groups():
    """获取直播分组API"""
    groups = LiveGroup.query.all()
    
    result = []
    for group in groups:
        channels = []
        for channel in group.channels:
            channels.append({
                'name': channel.name,
                'urls': channel.urls.split(',')
            })
        
        result.append({
            'group': group.name,
            'channels': channels
        })
    
    return jsonify(result) 