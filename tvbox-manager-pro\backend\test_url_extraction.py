#!/usr/bin/env python3
"""
测试URL提取逻辑
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.localization_service import LocalizationService
import json

def test_url_extraction():
    """测试URL提取"""
    service = LocalizationService()
    
    # 读取测试配置
    with open('test_config.json', 'r', encoding='utf-8') as f:
        config_data = json.load(f)
    
    print("测试配置:")
    print(json.dumps(config_data, ensure_ascii=False, indent=2))
    
    # 提取URL
    base_url = "https://example.com/config"
    urls = service.extract_urls_from_config(config_data, base_url)
    
    print(f"\n提取到 {len(urls)} 个URL:")
    for i, url_info in enumerate(urls):
        print(f"{i+1}. {url_info['file_type']}: {url_info['url']}")
        print(f"   字段: {url_info['field_name']}")
        print(f"   路径: {url_info['config_path']}")
        
        # 测试URL过滤
        should_skip, skip_reason = service.should_skip_url(url_info['url'])
        if should_skip:
            print(f"   状态: 跳过 - {skip_reason}")
        else:
            print(f"   状态: 需要下载")
        print()

if __name__ == "__main__":
    test_url_extraction()
