import { request } from '@/utils/request'

export const systemApi = {
  // 获取系统统计
  getSystemStats() {
    return request.get('/v1/system/stats')
  },

  // 获取系统设置
  getSystemSettings() {
    return request.get('/v1/system/settings')
  },

  // 更新系统设置
  updateSystemSettings(data) {
    return request.put('/v1/system/settings', data)
  },

  // 获取系统信息
  getSystemInfo() {
    return request.get('/v1/system/info')
  },

  // 获取系统日志
  getSystemLogs(params = {}) {
    return request.get('/v1/system/logs', params)
  },

  // 清理系统日志
  clearSystemLogs() {
    return request.delete('/v1/system/logs')
  },

  // 系统备份
  createBackup() {
    return request.post('/v1/system/backup')
  },

  // 获取备份列表
  getBackups() {
    return request.get('/v1/system/backups')
  },

  // 恢复备份
  restoreBackup(backupId) {
    return request.post(`/v1/system/backups/${backupId}/restore`)
  },

  // 删除备份
  deleteBackup(backupId) {
    return request.delete(`/v1/system/backups/${backupId}`)
  },

  // 系统健康检查
  healthCheck() {
    return request.get('/v1/system/health')
  },

  // 获取系统性能指标
  getPerformanceMetrics() {
    return request.get('/v1/system/metrics')
  },

  // 重启系统服务
  restartService(serviceName) {
    return request.post(`/v1/system/services/${serviceName}/restart`)
  },

  // 获取服务状态
  getServiceStatus() {
    return request.get('/v1/system/services/status')
  }
}
