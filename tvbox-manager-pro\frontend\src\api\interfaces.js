import { request } from '@/utils/request'

export const interfaceApi = {
  // 获取接口源列表
  getInterfaceSources(params = {}) {
    return request.get('/v1/interfaces', params)
  },

  // 获取接口源详情
  getInterfaceSource(id) {
    return request.get(`/v1/interfaces/${id}`)
  },

  // 创建接口源
  createInterfaceSource(data) {
    return request.post('/v1/interfaces', data)
  },

  // 更新接口源
  updateInterfaceSource(id, data) {
    return request.put(`/v1/interfaces/${id}`, data)
  },

  // 删除接口源
  deleteInterfaceSource(id) {
    return request.delete(`/v1/interfaces/${id}`)
  },

  // 解析接口源
  parseInterfaceSource(id) {
    return request.post(`/v1/interfaces/${id}/parse`)
  },

  // 订阅接口源
  subscribeInterfaceSource(id, data) {
    return request.post(`/v1/interfaces/${id}/subscribe`, data)
  },

  // 解密接口URL
  decryptInterfaceUrl(data) {
    return request.post('/v1/interfaces/decrypt', data)
  },

  // 获取接口分类列表
  getInterfaceCategories() {
    return request.get('/v1/interfaces/categories/list')
  },

  // 批量操作接口源
  batchOperateInterfaceSources(data) {
    return request.post('/v1/interfaces/batch', data)
  },

  // 批量更新接口源
  batchUpdateInterfaceSources(params = {}) {
    return request.post('/v1/interfaces/batch-update', null, { params })
  },

  // 本地化相关API

  // 启用接口本地化
  enableLocalization(id) {
    return request.post(`/v1/interfaces/${id}/localize`)
  },

  // 禁用接口本地化
  disableLocalization(id) {
    return request.delete(`/v1/interfaces/${id}/localize`)
  },

  // 获取本地化状态
  getLocalizationStatus(id) {
    return request.get(`/v1/interfaces/${id}/localization-status`)
  },

  // 获取本地化配置
  getLocalizedConfig(id) {
    return request.get(`/v1/interfaces/${id}/localized`)
  }
}
