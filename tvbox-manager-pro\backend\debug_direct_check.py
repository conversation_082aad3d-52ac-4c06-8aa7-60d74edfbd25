#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def debug_direct_check():
    """调试直接配置文件识别逻辑"""
    
    from app.services.tvbox_decryptor import TVBoxDecryptor
    import json
    
    decryptor = TVBoxDecryptor()
    url = "http://xhztv.top/4k.json"
    
    print(f"调试直接配置文件识别: {url}")
    
    try:
        # 1. 获取原始内容
        normalized_url = decryptor._normalize_url(url)
        print(f"标准化URL: {normalized_url}")
        
        content, final_url = decryptor._get_json(normalized_url)
        print(f"原始内容长度: {len(content)}")
        print(f"原始内容前100字符: {repr(content[:100])}")
        
        # 2. 检查URL后缀
        url_ends_with_json = normalized_url.lower().endswith('.json')
        print(f"URL是否以.json结尾: {url_ends_with_json}")
        
        # 3. 检查内容格式
        content_starts_with_brace = content.strip().startswith('{')
        print(f"内容是否以{{开头: {content_starts_with_brace}")
        
        # 4. 检查是否包含配置字段
        has_sites = '"sites"' in content
        has_lives = '"lives"' in content
        has_parses = '"parses"' in content
        has_name = '"name"' in content
        print(f"包含sites: {has_sites}")
        print(f"包含lives: {has_lives}")
        print(f"包含parses: {has_parses}")
        print(f"包含name: {has_name}")
        
        # 5. 测试_is_direct_config_file方法
        is_direct = decryptor._is_direct_config_file(normalized_url, content)
        print(f"_is_direct_config_file结果: {is_direct}")
        
        # 6. 测试JSON解析
        try:
            json_data = json.loads(content)
            print(f"✅ 原始内容可以直接JSON解析")
            sites_count = len(json_data.get('sites', []))
            print(f"原始内容站点数: {sites_count}")
        except json.JSONDecodeError as e:
            print(f"❌ 原始内容无法直接JSON解析: {e}")
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_direct_check()
