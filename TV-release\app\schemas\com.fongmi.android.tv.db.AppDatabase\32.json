{"formatVersion": 1, "database": {"version": 32, "identityHash": "23710113f88894310d2acbc92c9e5ef1", "entities": [{"tableName": "Keep", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`key` TEXT NOT NULL, `siteName` TEXT, `vodName` TEXT, `vodPic` TEXT, `createTime` INTEGER NOT NULL, `type` INTEGER NOT NULL, `cid` INTEGER NOT NULL, PRIMARY KEY(`key`))", "fields": [{"fieldPath": "key", "columnName": "key", "affinity": "TEXT", "notNull": true}, {"fieldPath": "siteName", "columnName": "siteName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "vodName", "columnName": "vodName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "vodPic", "columnName": "vodPic", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createTime", "columnName": "createTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "cid", "columnName": "cid", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["key"]}, "indices": [], "foreignKeys": []}, {"tableName": "Site", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`key` TEXT NOT NULL, `searchable` INTEGER, `changeable` INTEGER, PRIMARY KEY(`key`))", "fields": [{"fieldPath": "key", "columnName": "key", "affinity": "TEXT", "notNull": true}, {"fieldPath": "searchable", "columnName": "searchable", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "changeable", "columnName": "changeable", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["key"]}, "indices": [], "foreignKeys": []}, {"tableName": "Live", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`name` TEXT NOT NULL, `boot` INTEGER NOT NULL, `pass` INTEGER NOT NULL, PRIMARY KEY(`name`))", "fields": [{"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "boot", "columnName": "boot", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "pass", "columnName": "pass", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["name"]}, "indices": [], "foreignKeys": []}, {"tableName": "Track", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `type` INTEGER NOT NULL, `group` INTEGER NOT NULL, `track` INTEGER NOT NULL, `key` TEXT, `name` TEXT, `selected` INTEGER NOT NULL, `adaptive` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "group", "columnName": "group", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "track", "columnName": "track", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "key", "columnName": "key", "affinity": "TEXT", "notNull": false}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "selected", "columnName": "selected", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "adaptive", "columnName": "adaptive", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_Track_key_type", "unique": true, "columnNames": ["key", "type"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_Track_key_type` ON `${TABLE_NAME}` (`key`, `type`)"}], "foreignKeys": []}, {"tableName": "Config", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `type` INTEGER NOT NULL, `time` INTEGER NOT NULL, `url` TEXT, `json` TEXT, `name` TEXT, `logo` TEXT, `home` TEXT, `parse` TEXT)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "time", "columnName": "time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "url", "columnName": "url", "affinity": "TEXT", "notNull": false}, {"fieldPath": "json", "columnName": "json", "affinity": "TEXT", "notNull": false}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "logo", "columnName": "logo", "affinity": "TEXT", "notNull": false}, {"fieldPath": "home", "columnName": "home", "affinity": "TEXT", "notNull": false}, {"fieldPath": "parse", "columnName": "parse", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_Config_url_type", "unique": true, "columnNames": ["url", "type"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_Config_url_type` ON `${TABLE_NAME}` (`url`, `type`)"}], "foreignKeys": []}, {"tableName": "<PERSON><PERSON>", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT, `uuid` TEXT, `name` TEXT, `ip` TEXT, `type` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "uuid", "columnName": "uuid", "affinity": "TEXT", "notNull": false}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "ip", "columnName": "ip", "affinity": "TEXT", "notNull": false}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_Device_uuid_name", "unique": true, "columnNames": ["uuid", "name"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_Device_uuid_name` ON `${TABLE_NAME}` (`uuid`, `name`)"}], "foreignKeys": []}, {"tableName": "History", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`key` TEXT NOT NULL, `vodPic` TEXT, `vodName` TEXT, `vodFlag` TEXT, `vodRemarks` TEXT, `episodeUrl` TEXT, `revSort` INTEGER NOT NULL, `revPlay` INTEGER NOT NULL, `createTime` INTEGER NOT NULL, `opening` INTEGER NOT NULL, `ending` INTEGER NOT NULL, `position` INTEGER NOT NULL, `duration` INTEGER NOT NULL, `speed` REAL NOT NULL, `scale` INTEGER NOT NULL, `cid` INTEGER NOT NULL, PRIMARY KEY(`key`))", "fields": [{"fieldPath": "key", "columnName": "key", "affinity": "TEXT", "notNull": true}, {"fieldPath": "vodPic", "columnName": "vodPic", "affinity": "TEXT", "notNull": false}, {"fieldPath": "vodName", "columnName": "vodName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "vodFlag", "columnName": "vodFlag", "affinity": "TEXT", "notNull": false}, {"fieldPath": "vodRemarks", "columnName": "vodRemarks", "affinity": "TEXT", "notNull": false}, {"fieldPath": "episodeUrl", "columnName": "episodeUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "revSort", "columnName": "revSort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "revPlay", "columnName": "revPlay", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createTime", "columnName": "createTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "opening", "columnName": "opening", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "ending", "columnName": "ending", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "position", "columnName": "position", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "duration", "columnName": "duration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "speed", "columnName": "speed", "affinity": "REAL", "notNull": true}, {"fieldPath": "scale", "columnName": "scale", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "cid", "columnName": "cid", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["key"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '23710113f88894310d2acbc92c9e5ef1')"]}}