{"version": 3, "file": "sort-icon.js", "sources": ["../../../../../../../packages/components/table-v2/src/components/sort-icon.tsx"], "sourcesContent": ["import ElIcon from '@element-plus/components/icon'\nimport { SortDown, SortUp } from '@element-plus/icons-vue'\nimport { SortOrder } from '../constants'\n\nimport type { FunctionalComponent } from 'vue'\n\nexport type SortIconProps = {\n  sortOrder: SortOrder\n  class?: JSX.IntrinsicAttributes['class']\n}\n\nconst SortIcon: FunctionalComponent<SortIconProps> = (props) => {\n  const { sortOrder } = props\n\n  return (\n    <ElIcon size={14} class={props.class}>\n      {sortOrder === SortOrder.ASC ? <SortUp /> : <SortDown />}\n    </ElIcon>\n  )\n}\n\nexport default SortIcon\n"], "names": ["SortIcon", "sortOrder", "_createVNode", "ElIcon", "SortOrder", "SortUp", "SortDown"], "mappings": ";;;;;;;;;;AAWA,EAAMA,MAAAA;IACE,SAAA;AAAEC,GAAAA,GAAAA,KAAAA,CAAAA;AAAF,EAAA,OAANC,eAAA,CAAAC,YAAA,EAAA;AAEA,IAAA,MAAA,EAAA,EAAA;AAAA,IAAA,OAAA,EAAA,KAAA,CAAA,KAAA;AAAA,GAAA,EAAA;AAAA,IAAA,OAAA,EAAA,MAAA,CAAA,SAAA,KAAAC,mBAAA,CAAA,GAAA,GAAAF,eAAA,CAAAG,eAAA,EAAA,IAAA,EAAA,IAAA,CAAA,GAAAH,eAAA,CAAAI,iBAAA,EAAA,IAAA,EAAA,IAAA,CAAA,CAAA;AAAA,GAAA,CAAA,CAAA;AAAA,CAAA,CAAA;AAKD,iBARD,QAAA;;;;"}