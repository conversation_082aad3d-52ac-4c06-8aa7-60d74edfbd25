plugins {
    id 'com.android.library'
    id 'com.chaquo.python'
}

android {
    namespace 'com.fongmi.chaquo'

    compileSdk 35
    flavorDimensions = ["abi"]

    defaultConfig {
        minSdk 21
        targetSdk 28
        python {
            version "3.8"
            pip {
                install("-r", "requirements.txt")
            }
        }
    }

    productFlavors {
        arm64_v8a {
            dimension "abi"
            ndk { abiFilters "arm64-v8a" }
        }
        armeabi_v7a {
            dimension "abi"
            ndk { abiFilters "armeabi-v7a" }
        }
    }

    sourceSets {
        main {
            python.srcDirs = ["src/main/python"]
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
}

dependencies {
    implementation project(':catvod')
}