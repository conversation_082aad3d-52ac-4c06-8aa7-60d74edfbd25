{"version": 3, "file": "tree-select-option.js", "sources": ["../../../../../../packages/components/tree-select/src/tree-select-option.ts"], "sourcesContent": ["import { defineComponent, getCurrentInstance, nextTick, watch } from 'vue'\nimport { ElOption } from '@element-plus/components/select'\n\nconst component = defineComponent({\n  extends: ElOption,\n  setup(props, ctx) {\n    const result = (ElOption.setup as NonNullable<any>)(props, ctx)\n\n    // use methods.selectOptionClick\n    delete result.selectOptionClick\n\n    const vm = (getCurrentInstance() as NonNullable<any>).proxy\n\n    // Fix: https://github.com/element-plus/element-plus/issues/7917\n    // `el-option` will delete the cache before unmount,\n    // This is normal for flat arrays `<el-select><el-option v-for=\"3\"></el-select>`,\n    // Because the same node key does not create a difference node,\n    // But in tree data, the same key at different levels will create diff nodes,\n    // So the destruction of `el-option` in `nextTick` will be slower than\n    // the creation of new `el-option`, which will delete the new node,\n    // here restore the deleted node.\n    // @link https://github.com/element-plus/element-plus/blob/6df6e49db07b38d6cc3b5e9a960782bd30879c11/packages/components/select/src/option.vue#L78\n    nextTick(() => {\n      if (!result.select.states.cachedOptions.get(vm.value)) {\n        result.select.onOptionCreate(vm)\n      }\n    })\n\n    watch(\n      () => ctx.attrs.visible,\n      (val) => {\n        nextTick(() => {\n          result.states.visible = val\n        })\n      },\n      {\n        immediate: true,\n      }\n    )\n\n    return result\n  },\n  methods: {\n    selectOptionClick() {\n      // $el.parentElement => el-tree-node__content\n      this.$el.parentElement.click()\n    },\n  },\n})\n\nexport default component\n"], "names": ["defineComponent", "ElOption", "getCurrentInstance", "nextTick", "watch"], "mappings": ";;;;;;;AAEK,MAAC,SAAS,GAAGA,mBAAe,CAAC;AAClC,EAAE,OAAO,EAAEC,cAAQ;AACnB,EAAE,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE;AACpB,IAAI,MAAM,MAAM,GAAGA,cAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAC9C,IAAI,OAAO,MAAM,CAAC,iBAAiB,CAAC;AACpC,IAAI,MAAM,EAAE,GAAGC,sBAAkB,EAAE,CAAC,KAAK,CAAC;AAC1C,IAAIC,YAAQ,CAAC,MAAM;AACnB,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE;AAC7D,QAAQ,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;AACzC,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAIC,SAAK,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK;AAC5C,MAAMD,YAAQ,CAAC,MAAM;AACrB,QAAQ,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC;AACpC,OAAO,CAAC,CAAC;AACT,KAAK,EAAE;AACP,MAAM,SAAS,EAAE,IAAI;AACrB,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,iBAAiB,GAAG;AACxB,MAAM,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;AACrC,KAAK;AACL,GAAG;AACH,CAAC;;;;"}