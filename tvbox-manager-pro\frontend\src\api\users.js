import { request } from '@/utils/request'

export const usersApi = {
  // 获取用户列表
  getUsers(params = {}) {
    return request.get('/v1/users', params)
  },

  // 创建用户
  createUser(data) {
    return request.post('/v1/users', data)
  },

  // 获取用户详情
  getUserById(id) {
    return request.get(`/v1/users/${id}`)
  },

  // 更新用户信息
  updateUser(id, data) {
    return request.put(`/v1/users/${id}`, data)
  },

  // 删除用户
  deleteUser(id) {
    return request.delete(`/v1/users/${id}`)
  },

  // 批量删除用户
  batchDeleteUsers(ids) {
    return request.post('/v1/users/batch-delete', { ids })
  },

  // 启用/禁用用户
  toggleUserStatus(id, isActive) {
    return request.patch(`/v1/users/${id}/status`, { is_active: isActive })
  },

  // 重置用户密码
  resetUserPassword(id, newPassword) {
    return request.post(`/v1/users/${id}/reset-password`, { password: newPassword })
  },

  // 获取用户角色列表
  getUserRoles() {
    return request.get('/v1/users/roles')
  },

  // 分配用户角色
  assignUserRole(id, roleId) {
    return request.post(`/v1/users/${id}/roles`, { role_id: roleId })
  }
}
