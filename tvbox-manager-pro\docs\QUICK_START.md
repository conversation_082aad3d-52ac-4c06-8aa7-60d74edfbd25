# TVBox Manager Pro 快速开始指南

## 🎯 项目简介

TVBox Manager Pro 是一个现代化的TVBox接口管理系统，基于FastAPI + Vue.js 3构建，提供完整的接口解密、配置管理、用户权限控制等功能。

## ✨ 主要特性

- 🔐 **安全认证**: JWT + OAuth2认证，基于角色的权限控制
- 📡 **智能解密**: 支持Base64、AES/CBC等多种TVBox接口解密方式
- 📊 **可视化控制台**: 实时数据统计和系统监控
- 🔄 **自动订阅**: 智能订阅管理和自动更新机制
- 📱 **响应式设计**: 支持桌面、平板、手机多种设备
- 🐳 **容器化部署**: Docker + Docker Compose一键部署
- 💾 **多数据库支持**: SQLite(默认) / MySQL / PostgreSQL

## 🚀 快速启动

### 方式一：Docker Compose（推荐）

1. **克隆项目**
```bash
git clone <repository-url>
cd tvbox-manager-pro
```

2. **一键启动**
```bash
chmod +x scripts/start.sh
./scripts/start.sh
```

3. **访问应用**
- 前端: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

### 方式二：手动启动

#### 后端启动

1. **安装依赖**
```bash
cd backend
pip install -r requirements.txt
```

2. **启动服务**
```bash
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### 前端启动

1. **安装依赖**
```bash
cd frontend
npm install
```

2. **启动开发服务器**
```bash
npm run dev
```

## 👤 默认账号

- **邮箱**: <EMAIL>
- **密码**: admin123

## 📁 项目结构

```
tvbox-manager-pro/
├── backend/                    # 后端FastAPI服务
│   ├── app/
│   │   ├── api/               # API路由
│   │   ├── core/              # 核心配置
│   │   ├── models/            # 数据模型
│   │   ├── services/          # 业务逻辑
│   │   ├── utils/             # 工具函数
│   │   └── main.py            # 应用入口
│   ├── requirements.txt       # Python依赖
│   └── Dockerfile             # Docker配置
├── frontend/                   # 前端Vue.js应用
│   ├── src/
│   │   ├── components/        # Vue组件
│   │   ├── views/             # 页面视图
│   │   ├── router/            # 路由配置
│   │   ├── stores/            # 状态管理
│   │   ├── utils/             # 工具函数
│   │   └── main.js            # 应用入口
│   ├── package.json           # 前端依赖
│   └── Dockerfile             # Docker配置
├── scripts/                    # 部署脚本
├── docs/                       # 项目文档
└── docker-compose.yml          # Docker编排配置
```

## 🔧 配置说明

### 环境变量

#### 后端配置 (.env)
```bash
# 数据库配置
DATABASE_URL=sqlite:///./data/tvbox.db

# 安全配置
SECRET_KEY=your-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=10080

# CORS配置
CORS_ORIGINS=http://localhost:3000,http://localhost:8080
```

#### 前端配置 (.env.development)
```bash
# API基础URL
VITE_API_BASE_URL=http://localhost:8000/api

# 应用端口
VITE_PORT=3000
```

## 📚 功能模块

### 1. 用户管理
- 用户注册/登录
- 角色权限管理
- 个人资料设置

### 2. 控制台Dashboard
- 系统状态监控
- 数据统计图表
- 快速操作面板

### 3. 接口订阅管理
- 接口源添加/编辑
- 自动解密和解析
- 订阅状态监控
- 定时更新机制

### 4. 配置管理
- TVBox配置解析
- 配置文件管理
- 版本控制
- 批量操作

### 5. 数据管理
- 站点信息管理
- 直播源管理
- 解析器管理
- 数据导入导出

## 🛠️ 开发指南

### 后端开发

1. **添加新的API接口**
```python
# 在 app/api/v1/ 目录下创建新的路由文件
# 在 app/services/ 目录下创建对应的服务类
# 在 app/models/ 目录下创建数据模型
```

2. **数据库迁移**
```bash
# 生成迁移文件
alembic revision --autogenerate -m "描述"

# 执行迁移
alembic upgrade head
```

### 前端开发

1. **添加新页面**
```bash
# 在 src/views/ 目录下创建页面组件
# 在 src/router/index.js 中添加路由配置
```

2. **添加新的API接口**
```javascript
// 在 src/api/ 目录下创建或修改API文件
// 在组件中使用API服务
```

## 🔍 常见问题

### Q: 如何修改默认端口？
A: 修改 docker-compose.yml 中的端口映射，或修改环境变量文件。

### Q: 如何使用MySQL数据库？
A: 修改后端的 DATABASE_URL 环境变量，并启用 docker-compose.yml 中的 mysql 服务。

### Q: 如何备份数据？
A: 使用系统内置的备份功能，或直接备份 data 目录。

## 📞 技术支持

如有问题，请查看：
- [API文档](http://localhost:8000/docs)
- [项目文档](./docs/)
- [GitHub Issues](https://github.com/your-repo/issues)

## 📄 许可证

MIT License
