'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var iconsVue = require('@element-plus/icons-vue');
var runtime = require('./props/runtime.js');

const iconPropType = runtime.definePropType([
  String,
  Object,
  Function
]);
const CloseComponents = {
  Close: iconsVue.Close
};
const TypeComponents = {
  Close: iconsVue.Close,
  SuccessFilled: iconsVue.SuccessFilled,
  InfoFilled: iconsVue.InfoFilled,
  WarningFilled: iconsVue.WarningFilled,
  CircleCloseFilled: iconsVue.CircleCloseFilled
};
const TypeComponentsMap = {
  primary: iconsVue.InfoFilled,
  success: iconsVue.SuccessFilled,
  warning: iconsVue.WarningFilled,
  error: iconsVue.CircleCloseFilled,
  info: iconsVue.InfoFilled
};
const ValidateComponentsMap = {
  validating: iconsVue.Loading,
  success: iconsVue.CircleCheck,
  error: iconsVue.CircleClose
};

exports.CloseComponents = CloseComponents;
exports.TypeComponents = TypeComponents;
exports.TypeComponentsMap = TypeComponentsMap;
exports.ValidateComponentsMap = ValidateComponentsMap;
exports.iconPropType = iconPropType;
//# sourceMappingURL=icon.js.map
