plugins {
    id 'com.android.library'
}

android {
    namespace 'com.github.catvod.crawler'

    compileSdk 35

    defaultConfig {
        minSdk 21
        targetSdk 28
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
}

dependencies {
    api 'androidx.annotation:annotation:1.6.0'
    api 'androidx.preference:preference:1.2.1'
    api 'com.google.code.gson:gson:' + gsonVersion
    api 'com.google.net.cronet:cronet-okhttp:0.1.0'
    api 'com.googlecode.juniversalchardet:juniversalchardet:1.0.3'
    api 'com.orhanobut:logger:2.2.0'
    api 'com.squareup.okhttp3:okhttp:' + okhttpVersion
    api 'com.squareup.okhttp3:okhttp-dnsoverhttps:' + okhttpVersion
    api 'com.squareup.okhttp3:logging-interceptor:' + okhttpVersion
    api 'org.chromium.net:cronet-embedded:76.3809.111'
    api('com.google.guava:guava:33.3.1-android') {
        exclude group: 'com.google.code.findbugs', module: 'jsr305'
        exclude group: 'org.checkerframework', module: 'checker-compat-qual'
        exclude group: 'org.checkerframework', module: 'checker-qual'
        exclude group: 'com.google.errorprone', module: 'error_prone_annotations'
        exclude group: 'com.google.j2objc', module: 'j2objc-annotations'
        exclude group: 'org.codehaus.mojo', module: 'animal-sniffer-annotations'
    }
}