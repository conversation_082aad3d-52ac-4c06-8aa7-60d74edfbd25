#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
安全认证模块
基于Flask-Security-Too实现用户认证和授权
"""

import os
import logging
from flask import current_app
from flask_security import Security, SQLAlchemyUserDatastore
from flask_security.forms import LoginForm, RegisterForm
from flask_security.utils import hash_password
from sqlalchemy.exc import IntegrityError
from tvbox_manager.config.models import db, User, Role

# 创建全局Security实例
security = Security()

def init_security(app):
    """初始化Flask-Security配置

    Args:
        app: Flask应用实例
    
    Returns:
        Security: Flask-Security实例
    """
    # 获取管理员邮箱和密码
    admin_email = os.environ.get('TVBOX_ADMIN_EMAIL', '<EMAIL>')
    admin_password = os.environ.get('TVBOX_ADMIN_PASSWORD', 'admin123456')

    # 创建UserDatastore
    user_datastore = SQLAlchemyUserDatastore(db, User, Role)
    
    # 配置安全选项
    app.config.update(
        # 安全密钥
        SECRET_KEY=os.environ.get('SECRET_KEY', 'tvbox-manager-secret-key'),
        
        # 密码哈希选项
        SECURITY_PASSWORD_SALT=os.environ.get('SECURITY_PASSWORD_SALT', 'tvbox-manager-salt'),
        SECURITY_PASSWORD_HASH='bcrypt',
        
        # 登录设置
        SECURITY_LOGIN_URL='/login',
        SECURITY_LOGOUT_URL='/logout',
        SECURITY_POST_LOGIN_VIEW='/interface/',
        SECURITY_POST_LOGOUT_VIEW='/login',
        
        # 注册设置
        SECURITY_REGISTERABLE=True,
        SECURITY_REGISTER_URL='/register',
        SECURITY_POST_REGISTER_VIEW='/login',
        
        # 密码重置
        SECURITY_RECOVERABLE=True,
        SECURITY_RESET_URL='/reset',
        SECURITY_POST_RESET_VIEW='/login',
        
        # 更改密码
        SECURITY_CHANGEABLE=True,
        SECURITY_CHANGE_URL='/change',

        # 跟踪登录
        SECURITY_TRACKABLE=True,

        # 关闭CDN使用
        SECURITY_SEND_REGISTER_EMAIL=False,
        SECURITY_CONFIRM_EMAIL_WITHIN="5 days",
        SECURITY_RESET_PASSWORD_WITHIN="5 days",
        SECURITY_LOGIN_WITHOUT_CONFIRMATION=True,
        
        # 使用本地资源，不使用CDN
        SECURITY_BOOTSTRAP_FORM_FIELD_ERRORS=False,
        SECURITY_FLASH_MESSAGES=True,
        SECURITY_I18N_DIRNAME=None,
        SECURITY_I18N_DOMAIN="flask_security",
        
        # 禁用API相关
        SECURITY_WANT_JSON=False,
        SECURITY_CSRF_IGNORE_UNAUTH_ENDPOINTS=False,
        SECURITY_CSRF_COOKIE_NAME='csrf_token',
        
        # 表单设置
        SECURITY_LOGIN_USER_TEMPLATE='tabler/login.html',
        SECURITY_REGISTER_USER_TEMPLATE='tabler/register.html',
        SECURITY_RESET_PASSWORD_TEMPLATE='tabler/reset.html',
        SECURITY_CHANGE_PASSWORD_TEMPLATE='tabler/change.html',
        SECURITY_SEND_CONFIRMATION_TEMPLATE='tabler/send_confirmation.html',
        SECURITY_SEND_LOGIN_TEMPLATE='tabler/send_login.html',

        # 邮件主题
        SECURITY_EMAIL_SUBJECT_REGISTER='欢迎使用TVBox Manager',
        SECURITY_EMAIL_SUBJECT_PASSWORD_NOTICE='TVBox Manager密码通知',
        SECURITY_EMAIL_SUBJECT_PASSWORD_RESET='TVBox Manager密码重置',
        SECURITY_EMAIL_SUBJECT_PASSWORD_CHANGE_NOTICE='TVBox Manager密码已更改',
        SECURITY_EMAIL_SUBJECT_CONFIRM='TVBox Manager邮箱确认',
    )

    # 初始化Security实例
    global security
    security.init_app(app, user_datastore)

    # 创建管理员账户
    @app.before_first_request
    def create_admin_user():
        """创建默认管理员账户"""
        logger = logging.getLogger(__name__)
        logger.info("正在初始化数据库和用户角色...")
        
        try:
            # 确保此函数在应用上下文中运行
            with app.app_context():
                # 创建所有表
                db.create_all()
    
                # 开始事务
                with db.session.begin_nested():
                    
                    # 如果没有管理员角色，创建一个
                    admin_role = user_datastore.find_role('admin')
                    if not admin_role:
                        logger.info("创建管理员角色")
                        admin_role = user_datastore.create_role(name='admin', description='管理员角色')
                    
                    # 如果没有用户角色，创建一个
                    user_role = user_datastore.find_role('user')
                    if not user_role:
                        logger.info("创建普通用户角色")
                        user_role = user_datastore.create_role(name='user', description='普通用户角色')
                
                db.session.commit()
                
                # 检查管理员用户是否已存在
                admin_user = user_datastore.find_user(email=admin_email)
                if not admin_user:
                    # 检查用户名是否已存在
                    username_check = user_datastore.find_user(username='admin')
                    if username_check:
                        logger.warning(f"用户名'admin'已被使用，使用备用用户名")
                        username = 'admin_user'
                    else:
                        username = 'admin'
                    
                    # 如果没有管理员用户，创建一个
                    logger.info(f"创建管理员用户: {admin_email}")
                    with db.session.no_autoflush:
                        admin_user = user_datastore.create_user(
                            email=admin_email,
                            username=username,
                            password=hash_password(admin_password),
                            active=True
                        )
                        user_datastore.add_role_to_user(admin_user, 'admin')
                        db.session.commit()
                else:
                    # 确保管理员用户有admin角色
                    logger.info(f"管理员用户已存在，确保拥有管理员权限")
                    if not any(role.name == 'admin' for role in admin_user.roles):
                        user_datastore.add_role_to_user(admin_user, 'admin')
                        db.session.commit()
                
                logger.info("数据库初始化完成")
        except IntegrityError as e:
            db.session.rollback()
            logger.error(f"数据库完整性错误: {str(e)}")
        except Exception as e:
            db.session.rollback()
            logger.error(f"初始化数据库时出错: {str(e)}")

    return security 