@echo off
chcp 65001 >nul
echo 🚀 启动 TVBox Manager Pro...

REM 检查Docker是否安装
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker 未安装，请先安装 Docker Desktop
    echo 下载地址: https://www.docker.com/products/docker-desktop/
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose 未安装，请先安装 Docker Desktop
    pause
    exit /b 1
)

REM 检查Docker是否运行
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker 未运行，请启动 Docker Desktop
    pause
    exit /b 1
)

echo 🔍 检查端口占用情况...
netstat -an | findstr ":3000" >nul
if %errorlevel% equ 0 (
    echo ⚠️  端口 3000 已被占用，请停止占用该端口的进程
    pause
    exit /b 1
)

netstat -an | findstr ":8001" >nul
if %errorlevel% equ 0 (
    echo ⚠️  端口 8001 已被占用，请停止占用该端口的进程
    pause
    exit /b 1
)

echo 📁 创建必要的目录...
if not exist "data" mkdir data
if not exist "data\uploads" mkdir data\uploads
if not exist "data\backups" mkdir data\backups
if not exist "data\cache" mkdir data\cache
if not exist "logs" mkdir logs

echo 🔨 构建并启动服务...
set COMPOSE_PROJECT_NAME=tvbox-manager-pro
docker-compose up --build -d

if %errorlevel% neq 0 (
    echo ❌ 启动失败，请检查错误信息
    pause
    exit /b 1
)

echo ⏳ 等待服务启动...
timeout /t 10 /nobreak >nul

echo 🔍 检查服务状态...
docker-compose ps

echo.
echo ✅ TVBox Manager Pro 启动成功！
echo.
echo 📱 前端访问地址: http://localhost:5173
echo 🔧 后端API地址: http://localhost:8001
echo 📚 API文档地址: http://localhost:8001/docs
echo.
echo 👤 默认管理员账号:
echo    邮箱: <EMAIL>
echo    密码: admin123
echo.
echo 🛠️  常用命令:
echo    查看日志: docker-compose logs -f
echo    停止服务: docker-compose down
echo    重启服务: docker-compose restart
echo.
echo 🎉 享受使用 TVBox Manager Pro！
echo.
echo 按任意键打开浏览器...
pause >nul

REM 打开浏览器
start http://localhost:5173
