#!/usr/bin/env python3
"""
更新为真实配置
"""
import sqlite3
import os
import json

def update_real_config():
    """更新为真实配置"""
    db_path = "data/tvbox.db"
    
    # 读取真实配置
    with open('real_tvbox_config.json', 'r', encoding='utf-8') as f:
        real_config = f.read()
    
    print("读取真实配置成功")
    
    try:
        # 验证配置格式
        config_data = json.loads(real_config)
        spider = config_data.get('spider', 'N/A')
        sites = config_data.get('sites', [])
        lives = config_data.get('lives', [])
        parses = config_data.get('parses', [])
        
        print(f"Spider: {spider}")
        print(f"Sites数量: {len(sites)}")
        print(f"Lives数量: {len(lives)}")
        print(f"Parses数量: {len(parses)}")
        
        # 统计需要下载的文件
        download_urls = []
        
        # Spider
        if spider and spider.startswith('http'):
            download_urls.append(f"Spider: {spider}")
        
        # Sites中的URL
        for i, site in enumerate(sites):
            api = site.get('api', '')
            if api.startswith('http'):
                download_urls.append(f"Site {i+1} API: {api}")
            
            jar = site.get('jar', '')
            if jar and jar.startswith('http'):
                download_urls.append(f"Site {i+1} JAR: {jar}")
            
            ext = site.get('ext', '')
            if ext and ext.startswith('http'):
                download_urls.append(f"Site {i+1} EXT: {ext}")
        
        # Lives中的URL
        for i, live in enumerate(lives):
            url = live.get('url', '')
            if url and url.startswith('http'):
                download_urls.append(f"Live {i+1}: {url}")
        
        print(f"\n需要下载的文件 ({len(download_urls)} 个):")
        for url in download_urls:
            print(f"  {url}")
        
        # 更新数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            UPDATE interface_sources 
            SET config_content = ?, localized_config = NULL, name = ?
            WHERE id = 1
        """, (real_config, "真实TVBox配置"))
        
        # 清理旧的本地化文件记录
        cursor.execute("DELETE FROM localized_files WHERE interface_id = 1")
        
        conn.commit()
        conn.close()
        
        print(f"\n✅ 已更新接口1为真实TVBox配置")
        print(f"✅ 配置长度: {len(real_config)}")
        print(f"✅ 预计可下载 {len(download_urls)} 个文件")
        
    except Exception as e:
        print(f"更新失败: {str(e)}")

if __name__ == "__main__":
    update_real_config()
