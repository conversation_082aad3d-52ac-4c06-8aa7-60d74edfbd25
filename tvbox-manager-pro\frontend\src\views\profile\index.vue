<template>
  <div class="profile-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>个人中心</h2>
      <p>管理您的账号信息和个性化设置</p>
    </div>

    <!-- 用户信息概览 -->
    <div class="user-overview">
      <el-card>
        <div class="user-header">
          <div class="user-avatar-section">
            <el-upload
              class="avatar-uploader"
              :action="uploadUrl"
              :headers="uploadHeaders"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
            >
              <el-avatar
                :size="80"
                :src="userInfo.avatar"
                :icon="UserFilled"
                class="user-avatar"
              />
              <div class="avatar-overlay">
                <el-icon><Camera /></el-icon>
              </div>
            </el-upload>
          </div>

          <div class="user-info">
            <h2>{{ userInfo.username }}</h2>
            <p class="user-email">{{ userInfo.email }}</p>
            <div class="user-tags">
              <el-tag :type="userInfo.is_active ? 'success' : 'danger'" size="small">
                {{ userInfo.is_active ? '已激活' : '未激活' }}
              </el-tag>
              <el-tag :type="userInfo.email_verified ? 'success' : 'warning'" size="small">
                {{ userInfo.email_verified ? '邮箱已验证' : '邮箱未验证' }}
              </el-tag>
            </div>
          </div>

          <div class="user-stats">
            <div class="stat-item">
              <div class="stat-value">{{ userStats.loginCount }}</div>
              <div class="stat-label">登录次数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ userStats.interfaceCount }}</div>
              <div class="stat-label">接口数量</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ formatDate(userInfo.created_at, 'MM-DD') }}</div>
              <div class="stat-label">注册时间</div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 设置区域 -->
    <div class="settings-section">
      <el-row :gutter="20">
        <!-- 账号设置 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <el-card class="setting-card">
            <template #header>
              <div class="card-header">
                <el-icon><User /></el-icon>
                <span>账号设置</span>
              </div>
            </template>

            <el-form
              ref="basicFormRef"
              :model="basicForm"
              :rules="basicRules"
              label-width="80px"
              size="default"
            >
              <el-form-item label="用户名">
                <el-input
                  v-model="basicForm.username"
                  :disabled="true"
                >
                  <template #suffix>
                    <el-tooltip content="用户名不可修改" placement="top">
                      <el-icon><Lock /></el-icon>
                    </el-tooltip>
                  </template>
                </el-input>
              </el-form-item>

              <el-form-item label="显示名称" prop="display_name">
                <el-input
                  v-model="basicForm.display_name"
                  placeholder="设置显示名称"
                  clearable
                />
                <div class="form-tip">用于在界面中显示的名称</div>
              </el-form-item>

              <el-form-item>
                <el-button
                  type="primary"
                  :loading="basicLoading"
                  @click="updateBasicInfo"
                >
                  保存修改
                </el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>

        <!-- 密码修改 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <el-card class="setting-card">
            <template #header>
              <div class="card-header">
                <el-icon><Lock /></el-icon>
                <span>密码修改</span>
              </div>
            </template>

            <el-form
              ref="passwordFormRef"
              :model="passwordForm"
              :rules="passwordRules"
              label-width="80px"
              size="default"
            >
              <el-form-item label="当前密码" prop="current_password">
                <el-input
                  v-model="passwordForm.current_password"
                  type="password"
                  placeholder="请输入当前密码"
                  show-password
                  clearable
                />
              </el-form-item>

              <el-form-item label="新密码" prop="new_password">
                <el-input
                  v-model="passwordForm.new_password"
                  type="password"
                  placeholder="请输入新密码"
                  show-password
                  clearable
                />
                <div class="password-strength">
                  <div class="strength-bar">
                    <div
                      class="strength-fill"
                      :class="passwordStrength.class"
                      :style="{ width: passwordStrength.width }"
                    ></div>
                  </div>
                  <span class="strength-text">{{ passwordStrength.text }}</span>
                </div>
              </el-form-item>

              <el-form-item label="确认密码" prop="confirm_password">
                <el-input
                  v-model="passwordForm.confirm_password"
                  type="password"
                  placeholder="请再次输入新密码"
                  show-password
                  clearable
                />
              </el-form-item>

              <el-form-item>
                <el-button
                  type="primary"
                  :loading="passwordLoading"
                  @click="changePassword"
                >
                  修改密码
                </el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px;">
        <!-- 邮箱设置 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <el-card class="setting-card">
            <template #header>
              <div class="card-header">
                <el-icon><Message /></el-icon>
                <span>邮箱设置</span>
              </div>
            </template>

            <div class="current-email-info">
              <div class="email-display">
                <span class="email-text">{{ userInfo.email }}</span>
                <el-tag :type="userInfo.email_verified ? 'success' : 'warning'" size="small">
                  {{ userInfo.email_verified ? '已验证' : '未验证' }}
                </el-tag>
              </div>

              <el-button
                v-if="!userInfo.email_verified"
                type="primary"
                size="small"
                :loading="verifyLoading"
                @click="sendVerificationEmail"
              >
                发送验证邮件
              </el-button>
            </div>

            <el-divider />

            <el-form
              ref="emailFormRef"
              :model="emailForm"
              :rules="emailRules"
              label-width="80px"
              size="default"
            >
              <el-form-item label="新邮箱" prop="new_email">
                <el-input
                  v-model="emailForm.new_email"
                  placeholder="请输入新的邮箱地址"
                  clearable
                />
              </el-form-item>

              <el-form-item label="验证码" prop="verification_code">
                <div class="verification-input">
                  <el-input
                    v-model="emailForm.verification_code"
                    placeholder="验证码"
                    clearable
                  />
                  <el-button
                    :disabled="!emailForm.new_email || codeCountdown > 0"
                    :loading="codeLoading"
                    @click="sendEmailCode"
                    size="default"
                  >
                    {{ codeCountdown > 0 ? `${codeCountdown}s` : '发送' }}
                  </el-button>
                </div>
              </el-form-item>

              <el-form-item label="登录密码" prop="password">
                <el-input
                  v-model="emailForm.password"
                  type="password"
                  placeholder="请输入当前登录密码"
                  show-password
                  clearable
                />
              </el-form-item>

              <el-form-item>
                <el-button
                  type="primary"
                  :loading="emailLoading"
                  @click="changeEmail"
                >
                  更换邮箱
                </el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>

        <!-- 个性化设置 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <el-card class="setting-card">
            <template #header>
              <div class="card-header">
                <el-icon><Setting /></el-icon>
                <span>个性化设置</span>
              </div>
            </template>

            <el-form
              ref="preferencesFormRef"
              :model="preferencesForm"
              label-width="80px"
              size="default"
            >
              <el-form-item label="主题模式">
                <el-radio-group v-model="preferencesForm.theme">
                  <el-radio label="light">浅色</el-radio>
                  <el-radio label="dark">深色</el-radio>
                  <el-radio label="auto">自动</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="语言">
                <el-select v-model="preferencesForm.language" placeholder="选择语言">
                  <el-option label="简体中文" value="zh-cn" />
                  <el-option label="English" value="en" />
                </el-select>
              </el-form-item>

              <el-form-item label="邮件通知">
                <el-switch
                  v-model="preferencesForm.email_notifications"
                  active-text="开启"
                  inactive-text="关闭"
                />
              </el-form-item>

              <el-form-item label="自动保存">
                <el-switch
                  v-model="preferencesForm.auto_save"
                  active-text="开启"
                  inactive-text="关闭"
                />
              </el-form-item>

              <el-form-item>
                <el-button
                  type="primary"
                  :loading="preferencesLoading"
                  @click="updatePreferences"
                >
                  保存设置
                </el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
      </el-row>
    </div>

  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatDate } from '@/utils/date'
import { validatePassword } from '@/utils/validate'
import {
  UserFilled,
  Camera,
  Lock,
  Message,
  User,
  Setting
} from '@element-plus/icons-vue'

const userStore = useUserStore()
const appStore = useAppStore()

// 响应式数据
const activeTab = ref('basic')
const basicFormRef = ref()
const passwordFormRef = ref()
const emailFormRef = ref()
const preferencesFormRef = ref()

const basicLoading = ref(false)
const passwordLoading = ref(false)
const emailLoading = ref(false)
const preferencesLoading = ref(false)
const verifyLoading = ref(false)
const codeLoading = ref(false)
const codeCountdown = ref(0)

// 用户信息
const userInfo = ref({
  username: 'admin',
  email: '<EMAIL>',
  display_name: '管理员',
  avatar: '',
  is_active: true,
  email_verified: true,
  created_at: new Date().toISOString()
})

// 用户统计
const userStats = ref({
  loginCount: 156,
  interfaceCount: 23
})

// 基本信息表单
const basicForm = reactive({
  username: '',
  display_name: ''
})

// 密码修改表单
const passwordForm = reactive({
  current_password: '',
  new_password: '',
  confirm_password: ''
})

// 邮箱修改表单
const emailForm = reactive({
  new_email: '',
  verification_code: '',
  password: ''
})

// 个性化设置表单
const preferencesForm = reactive({
  theme: 'light',
  language: 'zh-cn',
  email_notifications: true,
  auto_save: true
})

// 表单验证规则
const basicRules = {
  display_name: [
    { max: 50, message: '显示名称长度不能超过50个字符', trigger: 'blur' }
  ]
}

const passwordRules = {
  current_password: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  new_password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirm_password: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.new_password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

const emailRules = {
  new_email: [
    { required: true, message: '请输入新邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  verification_code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 6, message: '验证码为6位数字', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ]
}

// 计算属性
const uploadUrl = computed(() => '/api/v1/upload/avatar')
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${userStore.token}`
}))

const passwordStrength = computed(() => {
  const password = passwordForm.new_password
  if (!password) {
    return { width: '0%', class: '', text: '' }
  }

  const result = validatePassword(password)
  const strengthMap = {
    0: { width: '20%', class: 'weak', text: '很弱' },
    1: { width: '40%', class: 'weak', text: '较弱' },
    2: { width: '60%', class: 'medium', text: '中等' },
    3: { width: '80%', class: 'strong', text: '较强' },
    4: { width: '100%', class: 'very-strong', text: '很强' },
    5: { width: '100%', class: 'very-strong', text: '极强' }
  }

  return strengthMap[result.strength] || strengthMap[0]
})

// 方法
const loadUserInfo = async () => {
  try {
    // 这里应该调用API获取用户信息
    // const response = await userApi.getCurrentUser()
    // userInfo.value = response.data

    // 暂时使用模拟数据
    Object.assign(basicForm, {
      username: userInfo.value.username,
      display_name: userInfo.value.display_name
    })

    // 加载个性化设置
    Object.assign(preferencesForm, {
      theme: appStore.isDark ? 'dark' : 'light',
      language: appStore.language || 'zh-cn'
    })
  } catch (error) {
    ElMessage.error('加载用户信息失败')
  }
}

const updateBasicInfo = async () => {
  if (!basicFormRef.value) return

  try {
    const valid = await basicFormRef.value.validate()
    if (!valid) return

    basicLoading.value = true

    // 这里应该调用API更新用户信息
    // await userApi.updateProfile(basicForm)

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    Object.assign(userInfo.value, basicForm)
    ElMessage.success('基本信息更新成功')
  } catch (error) {
    ElMessage.error('更新基本信息失败')
  } finally {
    basicLoading.value = false
  }
}

const changePassword = async () => {
  if (!passwordFormRef.value) return

  try {
    const valid = await passwordFormRef.value.validate()
    if (!valid) return

    passwordLoading.value = true

    // 这里应该调用API修改密码
    // await authApi.changePassword(passwordForm)

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('密码修改成功，请重新登录')
    resetPasswordForm()

    // 可以选择自动退出登录
    // userStore.logout()
  } catch (error) {
    ElMessage.error('密码修改失败')
  } finally {
    passwordLoading.value = false
  }
}

const changeEmail = async () => {
  if (!emailFormRef.value) return

  try {
    const valid = await emailFormRef.value.validate()
    if (!valid) return

    emailLoading.value = true

    // 这里应该调用API更换邮箱
    // await userApi.changeEmail(emailForm)

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    userInfo.value.email = emailForm.new_email
    userInfo.value.email_verified = false

    ElMessage.success('邮箱更换成功，请查收验证邮件')
    resetEmailForm()
  } catch (error) {
    ElMessage.error('邮箱更换失败')
  } finally {
    emailLoading.value = false
  }
}

const sendVerificationEmail = async () => {
  try {
    verifyLoading.value = true

    // 这里应该调用API发送验证邮件
    // await userApi.sendVerificationEmail()

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('验证邮件已发送，请查收')
  } catch (error) {
    ElMessage.error('发送验证邮件失败')
  } finally {
    verifyLoading.value = false
  }
}

const sendEmailCode = async () => {
  if (!emailForm.new_email) {
    ElMessage.warning('请先输入新邮箱地址')
    return
  }

  try {
    codeLoading.value = true

    // 这里应该调用API发送验证码
    // await userApi.sendEmailCode({ email: emailForm.new_email })

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('验证码已发送')
    startCountdown()
  } catch (error) {
    ElMessage.error('发送验证码失败')
  } finally {
    codeLoading.value = false
  }
}

const startCountdown = () => {
  codeCountdown.value = 60
  const timer = setInterval(() => {
    codeCountdown.value--
    if (codeCountdown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}

const updatePreferences = async () => {
  try {
    preferencesLoading.value = true

    // 这里应该调用API保存个性化设置
    // await userApi.updatePreferences(preferencesForm)

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 应用设置到应用状态
    if (preferencesForm.theme === 'dark') {
      appStore.isDark = true
    } else if (preferencesForm.theme === 'light') {
      appStore.isDark = false
    }

    if (appStore.setLanguage) {
      appStore.setLanguage(preferencesForm.language)
    }

    ElMessage.success('个性化设置保存成功')
  } catch (error) {
    ElMessage.error('保存设置失败')
  } finally {
    preferencesLoading.value = false
  }
}

const handleAvatarSuccess = (response) => {
  if (response.success) {
    userInfo.value.avatar = response.data.url
    ElMessage.success('头像更新成功')
  } else {
    ElMessage.error('头像上传失败')
  }
}

const beforeAvatarUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过2MB')
    return false
  }
  return true
}

// 重置表单方法
const resetBasicForm = () => {
  if (basicFormRef.value) {
    basicFormRef.value.resetFields()
  }
  loadUserInfo()
}

const resetPasswordForm = () => {
  if (passwordFormRef.value) {
    passwordFormRef.value.resetFields()
  }
  Object.assign(passwordForm, {
    current_password: '',
    new_password: '',
    confirm_password: ''
  })
}

const resetEmailForm = () => {
  if (emailFormRef.value) {
    emailFormRef.value.resetFields()
  }
  Object.assign(emailForm, {
    new_email: '',
    verification_code: '',
    password: ''
  })
}

const resetPreferencesForm = () => {
  loadUserInfo()
}

// 生命周期
onMounted(() => {
  loadUserInfo()
})

// 监听密码强度
watch(() => passwordForm.new_password, () => {
  // 密码强度会自动通过计算属性更新
})
</script>

<style lang="scss" scoped>
.profile-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;

  h2 {
    margin: 0 0 8px 0;
    color: var(--el-text-color-primary);
    font-size: 24px;
    font-weight: 600;
  }

  p {
    margin: 0;
    color: var(--el-text-color-regular);
    font-size: 14px;
  }
}

.user-overview {
  margin-bottom: 24px;

  .user-header {
    display: flex;
    align-items: center;
    gap: 24px;

    .user-avatar-section {
      .avatar-uploader {
        position: relative;
        display: inline-block;
        cursor: pointer;

        .user-avatar {
          border: 3px solid var(--el-border-color-light);
          transition: all 0.3s;
        }

        .avatar-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.6);
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s;
          border-radius: 50%;

          .el-icon {
            font-size: 20px;
          }
        }

        &:hover {
          .user-avatar {
            border-color: var(--el-color-primary);
          }

          .avatar-overlay {
            opacity: 1;
          }
        }
      }
    }

    .user-info {
      flex: 1;

      h2 {
        margin: 0 0 8px 0;
        color: var(--el-text-color-primary);
        font-size: 20px;
        font-weight: 600;
      }

      .user-email {
        margin: 0 0 12px 0;
        color: var(--el-text-color-regular);
        font-size: 14px;
      }

      .user-tags {
        display: flex;
        gap: 8px;
      }
    }

    .user-stats {
      display: flex;
      gap: 32px;

      .stat-item {
        text-align: center;

        .stat-value {
          font-size: 18px;
          font-weight: 600;
          color: var(--el-color-primary);
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 12px;
          color: var(--el-text-color-regular);
        }
      }
    }
  }
}

.settings-section {
  .setting-card {
    height: 100%;

    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;

      .el-icon {
        color: var(--el-color-primary);
      }
    }
  }
}

.current-email-info {
  margin-bottom: 20px;

  .email-display {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;

    .email-text {
      font-size: 14px;
      color: var(--el-text-color-primary);
    }
  }
}

.password-strength {
  margin-top: 8px;

  .strength-bar {
    height: 4px;
    background: var(--el-border-color-lighter);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 4px;

    .strength-fill {
      height: 100%;
      transition: all 0.3s;

      &.weak {
        background: var(--el-color-danger);
      }

      &.medium {
        background: var(--el-color-warning);
      }

      &.strong {
        background: var(--el-color-success);
      }

      &.very-strong {
        background: var(--el-color-primary);
      }
    }
  }

  .strength-text {
    font-size: 12px;
    color: var(--el-text-color-regular);
  }
}

.verification-input {
  display: flex;
  gap: 12px;

  .el-input {
    flex: 1;
  }

  .el-button {
    white-space: nowrap;
  }
}

.form-tip {
  font-size: 12px;
  color: var(--el-text-color-regular);
  margin-top: 4px;
  line-height: 1.4;
}

// 响应式设计
@media (max-width: 768px) {
  .profile-page {
    padding: 16px;
  }

  .user-header {
    flex-direction: column;
    text-align: center;
    gap: 16px;

    .user-stats {
      justify-content: center;
      gap: 24px;
    }
  }

  .verification-input {
    flex-direction: column;

    .el-button {
      width: 100%;
    }
  }

  .setting-card {
    margin-bottom: 16px;
  }
}
</style>
