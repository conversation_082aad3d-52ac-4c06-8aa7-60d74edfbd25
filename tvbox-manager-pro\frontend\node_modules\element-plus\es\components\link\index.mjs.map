{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/link/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Link from './src/link.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElLink: SFCWithInstall<typeof Link> = withInstall(Link)\nexport default ElLink\n\nexport * from './src/link'\n"], "names": [], "mappings": ";;;;AAEY,MAAC,MAAM,GAAG,WAAW,CAAC,IAAI;;;;"}