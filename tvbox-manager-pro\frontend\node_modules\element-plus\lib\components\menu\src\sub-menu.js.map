{"version": 3, "file": "sub-menu.js", "sources": ["../../../../../../packages/components/menu/src/sub-menu.ts"], "sourcesContent": ["import {\n  Fragment,\n  computed,\n  defineComponent,\n  getCurrentInstance,\n  h,\n  inject,\n  onBeforeUnmount,\n  onMounted,\n  provide,\n  reactive,\n  ref,\n  vShow,\n  watch,\n  withDirectives,\n} from 'vue'\nimport { useTimeoutFn } from '@vueuse/core'\nimport ElCollapseTransition from '@element-plus/components/collapse-transition'\nimport ElTooltip from '@element-plus/components/tooltip'\nimport {\n  buildProps,\n  iconPropType,\n  isString,\n  isUndefined,\n  throwError,\n} from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { ArrowDown, ArrowRight } from '@element-plus/icons-vue'\nimport { ElIcon } from '@element-plus/components/icon'\nimport useMenu from './use-menu'\nimport { useMenuCssVar } from './use-menu-css-var'\nimport { MENU_INJECTION_KEY, SUB_MENU_INJECTION_KEY } from './tokens'\n\nimport type { Placement } from '@element-plus/components/popper'\nimport type { TooltipInstance } from '@element-plus/components/tooltip'\nimport type {\n  ExtractPropTypes,\n  VNodeArrayChildren,\n  __ExtractPublicPropTypes,\n} from 'vue'\nimport type { MenuProvider, SubMenuProvider } from './types'\n\nexport const subMenuProps = buildProps({\n  /**\n   * @description unique identification\n   */\n  index: {\n    type: String,\n    required: true,\n  },\n  /**\n   * @description timeout before showing a sub-menu(inherit `show-timeout` of the menu by default.)\n   */\n  showTimeout: Number,\n  /**\n   * @description timeout before hiding a sub-menu(inherit `hide-timeout` of the menu by default.)\n   */\n  hideTimeout: Number,\n  /**\n   * @description custom class name for the popup menu\n   */\n  popperClass: String,\n  /**\n   * @description whether the sub-menu is disabled\n   */\n  disabled: Boolean,\n  /**\n   * @description whether popup menu is teleported to the body\n   */\n  teleported: {\n    type: Boolean,\n    default: undefined,\n  },\n  /**\n   * @description offset of the popper (overrides the `popper` of menu)\n   */\n  popperOffset: Number,\n  /**\n   * @description Icon when menu are expanded and submenu are closed, `expand-close-icon` and `expand-open-icon` need to be passed together to take effect\n   */\n  expandCloseIcon: {\n    type: iconPropType,\n  },\n  /**\n   * @description Icon when menu are expanded and submenu are opened, `expand-open-icon` and `expand-close-icon` need to be passed together to take effect\n   */\n  expandOpenIcon: {\n    type: iconPropType,\n  },\n  /**\n   * @description Icon when menu are collapsed and submenu are closed, `collapse-close-icon` and `collapse-open-icon` need to be passed together to take effect\n   */\n  collapseCloseIcon: {\n    type: iconPropType,\n  },\n  /**\n   * @description Icon when menu are collapsed and submenu are opened, `collapse-open-icon` and `collapse-close-icon` need to be passed together to take effect\n   */\n  collapseOpenIcon: {\n    type: iconPropType,\n  },\n} as const)\nexport type SubMenuProps = ExtractPropTypes<typeof subMenuProps>\nexport type SubMenuPropsPublic = __ExtractPublicPropTypes<typeof subMenuProps>\n\nconst COMPONENT_NAME = 'ElSubMenu'\nexport default defineComponent({\n  name: COMPONENT_NAME,\n  props: subMenuProps,\n\n  setup(props, { slots, expose }) {\n    const instance = getCurrentInstance()!\n    const { indexPath, parentMenu } = useMenu(\n      instance,\n      computed(() => props.index)\n    )\n    const nsMenu = useNamespace('menu')\n    const nsSubMenu = useNamespace('sub-menu')\n\n    // inject\n    const rootMenu = inject<MenuProvider>(MENU_INJECTION_KEY)\n    if (!rootMenu) throwError(COMPONENT_NAME, 'can not inject root menu')\n\n    const subMenu = inject<SubMenuProvider>(\n      `${SUB_MENU_INJECTION_KEY}${parentMenu.value!.uid}`\n    )\n    if (!subMenu) throwError(COMPONENT_NAME, 'can not inject sub menu')\n\n    const items = ref<MenuProvider['items']>({})\n    const subMenus = ref<MenuProvider['subMenus']>({})\n\n    let timeout: (() => void) | undefined\n    const mouseInChild = ref(false)\n    const verticalTitleRef = ref<HTMLDivElement>()\n    const vPopper = ref<TooltipInstance>()\n\n    // computed\n    const currentPlacement = computed<Placement>(() =>\n      mode.value === 'horizontal' && isFirstLevel.value\n        ? 'bottom-start'\n        : 'right-start'\n    )\n    const subMenuTitleIcon = computed(() => {\n      return (mode.value === 'horizontal' && isFirstLevel.value) ||\n        (mode.value === 'vertical' && !rootMenu.props.collapse)\n        ? props.expandCloseIcon && props.expandOpenIcon\n          ? opened.value\n            ? props.expandOpenIcon\n            : props.expandCloseIcon\n          : ArrowDown\n        : props.collapseCloseIcon && props.collapseOpenIcon\n        ? opened.value\n          ? props.collapseOpenIcon\n          : props.collapseCloseIcon\n        : ArrowRight\n    })\n    const isFirstLevel = computed(() => subMenu.level === 0)\n    const appendToBody = computed(() => {\n      const value = props.teleported\n      return isUndefined(value) ? isFirstLevel.value : value\n    })\n    const menuTransitionName = computed(() =>\n      rootMenu.props.collapse\n        ? `${nsMenu.namespace.value}-zoom-in-left`\n        : `${nsMenu.namespace.value}-zoom-in-top`\n    )\n    const fallbackPlacements = computed<Placement[]>(() =>\n      mode.value === 'horizontal' && isFirstLevel.value\n        ? [\n            'bottom-start',\n            'bottom-end',\n            'top-start',\n            'top-end',\n            'right-start',\n            'left-start',\n          ]\n        : [\n            'right-start',\n            'right',\n            'right-end',\n            'left-start',\n            'bottom-start',\n            'bottom-end',\n            'top-start',\n            'top-end',\n          ]\n    )\n    const opened = computed(() => rootMenu.openedMenus.includes(props.index))\n    const active = computed(() =>\n      [...Object.values(items.value), ...Object.values(subMenus.value)].some(\n        ({ active }) => active\n      )\n    )\n\n    const mode = computed(() => rootMenu.props.mode)\n    const persistent = computed(() => rootMenu.props.persistent)\n    const item = reactive({\n      index: props.index,\n      indexPath,\n      active,\n    })\n\n    const ulStyle = useMenuCssVar(rootMenu.props, subMenu.level + 1)\n\n    const subMenuPopperOffset = computed(\n      () => props.popperOffset ?? rootMenu.props.popperOffset\n    )\n\n    const subMenuPopperClass = computed(\n      () => props.popperClass ?? rootMenu.props.popperClass\n    )\n\n    const subMenuShowTimeout = computed(\n      () => props.showTimeout ?? rootMenu.props.showTimeout\n    )\n\n    const subMenuHideTimeout = computed(\n      () => props.hideTimeout ?? rootMenu.props.hideTimeout\n    )\n\n    // methods\n    const doDestroy = () =>\n      vPopper.value?.popperRef?.popperInstanceRef?.destroy()\n\n    const handleCollapseToggle = (value: boolean) => {\n      if (!value) {\n        doDestroy()\n      }\n    }\n\n    const handleClick = () => {\n      if (\n        (rootMenu.props.menuTrigger === 'hover' &&\n          rootMenu.props.mode === 'horizontal') ||\n        (rootMenu.props.collapse && rootMenu.props.mode === 'vertical') ||\n        props.disabled\n      )\n        return\n\n      rootMenu.handleSubMenuClick({\n        index: props.index,\n        indexPath: indexPath.value,\n        active: active.value,\n      })\n    }\n\n    const handleMouseenter = (\n      event: MouseEvent | FocusEvent,\n      showTimeout = subMenuShowTimeout.value\n    ) => {\n      if (event.type === 'focus') return\n\n      if (\n        (rootMenu.props.menuTrigger === 'click' &&\n          rootMenu.props.mode === 'horizontal') ||\n        (!rootMenu.props.collapse && rootMenu.props.mode === 'vertical') ||\n        props.disabled\n      ) {\n        subMenu.mouseInChild.value = true\n        return\n      }\n      subMenu.mouseInChild.value = true\n\n      timeout?.()\n      ;({ stop: timeout } = useTimeoutFn(() => {\n        rootMenu.openMenu(props.index, indexPath.value)\n      }, showTimeout))\n\n      if (appendToBody.value) {\n        parentMenu.value.vnode.el?.dispatchEvent(new MouseEvent('mouseenter'))\n      }\n    }\n\n    const handleMouseleave = (deepDispatch = false) => {\n      if (\n        (rootMenu.props.menuTrigger === 'click' &&\n          rootMenu.props.mode === 'horizontal') ||\n        (!rootMenu.props.collapse && rootMenu.props.mode === 'vertical')\n      ) {\n        subMenu.mouseInChild.value = false\n        return\n      }\n      timeout?.()\n      subMenu.mouseInChild.value = false\n      ;({ stop: timeout } = useTimeoutFn(\n        () =>\n          !mouseInChild.value &&\n          rootMenu.closeMenu(props.index, indexPath.value),\n        subMenuHideTimeout.value\n      ))\n\n      if (appendToBody.value && deepDispatch) {\n        subMenu.handleMouseleave?.(true)\n      }\n    }\n\n    watch(\n      () => rootMenu.props.collapse,\n      (value) => handleCollapseToggle(Boolean(value))\n    )\n\n    // provide\n    {\n      const addSubMenu: SubMenuProvider['addSubMenu'] = (item) => {\n        subMenus.value[item.index] = item\n      }\n      const removeSubMenu: SubMenuProvider['removeSubMenu'] = (item) => {\n        delete subMenus.value[item.index]\n      }\n      provide<SubMenuProvider>(`${SUB_MENU_INJECTION_KEY}${instance.uid}`, {\n        addSubMenu,\n        removeSubMenu,\n        handleMouseleave,\n        mouseInChild,\n        level: subMenu.level + 1,\n      })\n    }\n\n    // expose\n    expose({\n      opened,\n    })\n\n    // lifecycle\n    onMounted(() => {\n      rootMenu.addSubMenu(item)\n      subMenu.addSubMenu(item)\n    })\n\n    onBeforeUnmount(() => {\n      subMenu.removeSubMenu(item)\n      rootMenu.removeSubMenu(item)\n    })\n\n    return () => {\n      const titleTag: VNodeArrayChildren = [\n        slots.title?.(),\n        h(\n          ElIcon,\n          {\n            class: nsSubMenu.e('icon-arrow'),\n            style: {\n              transform: opened.value\n                ? (props.expandCloseIcon && props.expandOpenIcon) ||\n                  (props.collapseCloseIcon &&\n                    props.collapseOpenIcon &&\n                    rootMenu.props.collapse)\n                  ? 'none'\n                  : 'rotateZ(180deg)'\n                : 'none',\n            },\n          },\n          {\n            default: () =>\n              isString(subMenuTitleIcon.value)\n                ? h(instance.appContext.components[subMenuTitleIcon.value])\n                : h(subMenuTitleIcon.value),\n          }\n        ),\n      ]\n\n      // this render function is only used for bypass `Vue`'s compiler caused patching issue.\n      const child = rootMenu.isMenuPopup\n        ? h(\n            ElTooltip,\n            {\n              ref: vPopper,\n              visible: opened.value,\n              effect: 'light',\n              pure: true,\n              offset: subMenuPopperOffset.value,\n              showArrow: false,\n              persistent: persistent.value,\n              popperClass: subMenuPopperClass.value,\n              placement: currentPlacement.value,\n              teleported: appendToBody.value,\n              fallbackPlacements: fallbackPlacements.value,\n              transition: menuTransitionName.value,\n              gpuAcceleration: false,\n            },\n            {\n              content: () =>\n                h(\n                  'div',\n                  {\n                    class: [\n                      nsMenu.m(mode.value),\n                      nsMenu.m('popup-container'),\n                      subMenuPopperClass.value,\n                    ],\n                    onMouseenter: (evt: MouseEvent) =>\n                      handleMouseenter(evt, 100),\n                    onMouseleave: () => handleMouseleave(true),\n                    onFocus: (evt: FocusEvent) => handleMouseenter(evt, 100),\n                  },\n                  [\n                    h(\n                      'ul',\n                      {\n                        class: [\n                          nsMenu.b(),\n                          nsMenu.m('popup'),\n                          nsMenu.m(`popup-${currentPlacement.value}`),\n                        ],\n                        style: ulStyle.value,\n                      },\n                      [slots.default?.()]\n                    ),\n                  ]\n                ),\n              default: () =>\n                h(\n                  'div',\n                  {\n                    class: nsSubMenu.e('title'),\n                    onClick: handleClick,\n                  },\n                  titleTag\n                ),\n            }\n          )\n        : h(Fragment, {}, [\n            h(\n              'div',\n              {\n                class: nsSubMenu.e('title'),\n                ref: verticalTitleRef,\n                onClick: handleClick,\n              },\n              titleTag\n            ),\n            h(\n              ElCollapseTransition,\n              {},\n              {\n                default: () =>\n                  withDirectives(\n                    h(\n                      'ul',\n                      {\n                        role: 'menu',\n                        class: [nsMenu.b(), nsMenu.m('inline')],\n                        style: ulStyle.value,\n                      },\n                      [slots.default?.()]\n                    ),\n                    [[vShow, opened.value]]\n                  ),\n              }\n            ),\n          ])\n\n      return h(\n        'li',\n        {\n          class: [\n            nsSubMenu.b(),\n            nsSubMenu.is('active', active.value),\n            nsSubMenu.is('opened', opened.value),\n            nsSubMenu.is('disabled', props.disabled),\n          ],\n          role: 'menuitem',\n          ariaHaspopup: true,\n          ariaExpanded: opened.value,\n          onMouseenter: handleMouseenter,\n          onMouseleave: () => handleMouseleave(),\n          onFocus: handleMouseenter,\n        },\n        [child]\n      )\n    }\n  },\n})\n"], "names": ["buildProps", "iconPropType", "defineComponent", "getCurrentInstance", "useMenu", "computed", "useNamespace", "inject", "MENU_INJECTION_KEY", "throwError", "SUB_MENU_INJECTION_KEY", "ref", "ArrowDown", "ArrowRight", "isUndefined", "reactive", "useMenuCssVar", "useTimeoutFn", "watch", "provide", "onMounted", "onBeforeUnmount", "h", "ElIcon", "isString", "ElTooltip", "Fragment", "ElCollapseTransition", "withDirectives", "vShow"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAgCY,MAAC,YAAY,GAAGA,kBAAU,CAAC;AACvC,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,YAAY,EAAE,MAAM;AACtB,EAAE,eAAe,EAAE;AACnB,IAAI,IAAI,EAAEC,iBAAY;AACtB,GAAG;AACH,EAAE,cAAc,EAAE;AAClB,IAAI,IAAI,EAAEA,iBAAY;AACtB,GAAG;AACH,EAAE,iBAAiB,EAAE;AACrB,IAAI,IAAI,EAAEA,iBAAY;AACtB,GAAG;AACH,EAAE,gBAAgB,EAAE;AACpB,IAAI,IAAI,EAAEA,iBAAY;AACtB,GAAG;AACH,CAAC,EAAE;AACH,MAAM,cAAc,GAAG,WAAW,CAAC;AACnC,cAAeC,mBAAe,CAAC;AAC/B,EAAE,IAAI,EAAE,cAAc;AACtB,EAAE,KAAK,EAAE,YAAY;AACrB,EAAE,KAAK,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;AAClC,IAAI,MAAM,QAAQ,GAAGC,sBAAkB,EAAE,CAAC;AAC1C,IAAI,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,GAAGC,kBAAO,CAAC,QAAQ,EAAEC,YAAQ,CAAC,MAAM,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AACrF,IAAI,MAAM,MAAM,GAAGC,kBAAY,CAAC,MAAM,CAAC,CAAC;AACxC,IAAI,MAAM,SAAS,GAAGA,kBAAY,CAAC,UAAU,CAAC,CAAC;AAC/C,IAAI,MAAM,QAAQ,GAAGC,UAAM,CAACC,yBAAkB,CAAC,CAAC;AAChD,IAAI,IAAI,CAAC,QAAQ;AACjB,MAAMC,gBAAU,CAAC,cAAc,EAAE,0BAA0B,CAAC,CAAC;AAC7D,IAAI,MAAM,OAAO,GAAGF,UAAM,CAAC,CAAC,EAAEG,6BAAsB,CAAC,EAAE,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/E,IAAI,IAAI,CAAC,OAAO;AAChB,MAAMD,gBAAU,CAAC,cAAc,EAAE,yBAAyB,CAAC,CAAC;AAC5D,IAAI,MAAM,KAAK,GAAGE,OAAG,CAAC,EAAE,CAAC,CAAC;AAC1B,IAAI,MAAM,QAAQ,GAAGA,OAAG,CAAC,EAAE,CAAC,CAAC;AAC7B,IAAI,IAAI,OAAO,CAAC;AAChB,IAAI,MAAM,YAAY,GAAGA,OAAG,CAAC,KAAK,CAAC,CAAC;AACpC,IAAI,MAAM,gBAAgB,GAAGA,OAAG,EAAE,CAAC;AACnC,IAAI,MAAM,OAAO,GAAGA,OAAG,EAAE,CAAC;AAC1B,IAAI,MAAM,gBAAgB,GAAGN,YAAQ,CAAC,MAAM,IAAI,CAAC,KAAK,KAAK,YAAY,IAAI,YAAY,CAAC,KAAK,GAAG,cAAc,GAAG,aAAa,CAAC,CAAC;AAChI,IAAI,MAAM,gBAAgB,GAAGA,YAAQ,CAAC,MAAM;AAC5C,MAAM,OAAO,IAAI,CAAC,KAAK,KAAK,YAAY,IAAI,YAAY,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,UAAU,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,eAAe,IAAI,KAAK,CAAC,cAAc,GAAG,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,cAAc,GAAG,KAAK,CAAC,eAAe,GAAGO,kBAAS,GAAG,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,gBAAgB,GAAG,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,gBAAgB,GAAG,KAAK,CAAC,iBAAiB,GAAGC,mBAAU,CAAC;AACrX,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,YAAY,GAAGR,YAAQ,CAAC,MAAM,OAAO,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC;AAC7D,IAAI,MAAM,YAAY,GAAGA,YAAQ,CAAC,MAAM;AACxC,MAAM,MAAM,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC;AACrC,MAAM,OAAOS,iBAAW,CAAC,KAAK,CAAC,GAAG,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;AAC7D,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,kBAAkB,GAAGT,YAAQ,CAAC,MAAM,QAAQ,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,EAAE,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;AAC5J,IAAI,MAAM,kBAAkB,GAAGA,YAAQ,CAAC,MAAM,IAAI,CAAC,KAAK,KAAK,YAAY,IAAI,YAAY,CAAC,KAAK,GAAG;AAClG,MAAM,cAAc;AACpB,MAAM,YAAY;AAClB,MAAM,WAAW;AACjB,MAAM,SAAS;AACf,MAAM,aAAa;AACnB,MAAM,YAAY;AAClB,KAAK,GAAG;AACR,MAAM,aAAa;AACnB,MAAM,OAAO;AACb,MAAM,WAAW;AACjB,MAAM,YAAY;AAClB,MAAM,cAAc;AACpB,MAAM,YAAY;AAClB,MAAM,WAAW;AACjB,MAAM,SAAS;AACf,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,MAAM,GAAGA,YAAQ,CAAC,MAAM,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AAC9E,IAAI,MAAM,MAAM,GAAGA,YAAQ,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,OAAO,CAAC,CAAC,CAAC;AAC5I,IAAI,MAAM,IAAI,GAAGA,YAAQ,CAAC,MAAM,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACrD,IAAI,MAAM,UAAU,GAAGA,YAAQ,CAAC,MAAM,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AACjE,IAAI,MAAM,IAAI,GAAGU,YAAQ,CAAC;AAC1B,MAAM,KAAK,EAAE,KAAK,CAAC,KAAK;AACxB,MAAM,SAAS;AACf,MAAM,MAAM;AACZ,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,OAAO,GAAGC,2BAAa,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACrE,IAAI,MAAM,mBAAmB,GAAGX,YAAQ,CAAC,MAAM;AAC/C,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,OAAO,CAAC,EAAE,GAAG,KAAK,CAAC,YAAY,KAAK,IAAI,GAAG,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC;AAClF,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,kBAAkB,GAAGA,YAAQ,CAAC,MAAM;AAC9C,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,OAAO,CAAC,EAAE,GAAG,KAAK,CAAC,WAAW,KAAK,IAAI,GAAG,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC;AAChF,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,kBAAkB,GAAGA,YAAQ,CAAC,MAAM;AAC9C,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,OAAO,CAAC,EAAE,GAAG,KAAK,CAAC,WAAW,KAAK,IAAI,GAAG,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC;AAChF,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,kBAAkB,GAAGA,YAAQ,CAAC,MAAM;AAC9C,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,OAAO,CAAC,EAAE,GAAG,KAAK,CAAC,WAAW,KAAK,IAAI,GAAG,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC;AAChF,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,SAAS,GAAG,MAAM;AAC5B,MAAM,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACrB,MAAM,OAAO,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,iBAAiB,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC;AACxJ,KAAK,CAAC;AACN,IAAI,MAAM,oBAAoB,GAAG,CAAC,KAAK,KAAK;AAC5C,MAAM,IAAI,CAAC,KAAK,EAAE;AAClB,QAAQ,SAAS,EAAE,CAAC;AACpB,OAAO;AACP,KAAK,CAAC;AACN,IAAI,MAAM,WAAW,GAAG,MAAM;AAC9B,MAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,WAAW,KAAK,OAAO,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,YAAY,IAAI,QAAQ,CAAC,KAAK,CAAC,QAAQ,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,UAAU,IAAI,KAAK,CAAC,QAAQ;AAC3K,QAAQ,OAAO;AACf,MAAM,QAAQ,CAAC,kBAAkB,CAAC;AAClC,QAAQ,KAAK,EAAE,KAAK,CAAC,KAAK;AAC1B,QAAQ,SAAS,EAAE,SAAS,CAAC,KAAK;AAClC,QAAQ,MAAM,EAAE,MAAM,CAAC,KAAK;AAC5B,OAAO,CAAC,CAAC;AACT,KAAK,CAAC;AACN,IAAI,MAAM,gBAAgB,GAAG,CAAC,KAAK,EAAE,WAAW,GAAG,kBAAkB,CAAC,KAAK,KAAK;AAChF,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO;AAChC,QAAQ,OAAO;AACf,MAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,WAAW,KAAK,OAAO,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,YAAY,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,UAAU,IAAI,KAAK,CAAC,QAAQ,EAAE;AAC9K,QAAQ,OAAO,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC;AAC1C,QAAQ,OAAO;AACf,OAAO;AACP,MAAM,OAAO,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC;AACxC,MAAM,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,OAAO,EAAE,CAAC;AAC3C,MAAM,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,GAAGY,iBAAY,CAAC,MAAM;AAC9C,QAAQ,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;AACxD,OAAO,EAAE,WAAW,CAAC,EAAE;AACvB,MAAM,IAAI,YAAY,CAAC,KAAK,EAAE;AAC9B,QAAQ,CAAC,EAAE,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,aAAa,CAAC,IAAI,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC;AAC3G,OAAO;AACP,KAAK,CAAC;AACN,IAAI,MAAM,gBAAgB,GAAG,CAAC,YAAY,GAAG,KAAK,KAAK;AACvD,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,WAAW,KAAK,OAAO,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,YAAY,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE;AAC5J,QAAQ,OAAO,CAAC,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;AAC3C,QAAQ,OAAO;AACf,OAAO;AACP,MAAM,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,OAAO,EAAE,CAAC;AAC3C,MAAM,OAAO,CAAC,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;AACzC,MAAM,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,GAAGA,iBAAY,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,IAAI,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,SAAS,CAAC,KAAK,CAAC,EAAE,kBAAkB,CAAC,KAAK,CAAC,EAAE;AAClJ,MAAM,IAAI,YAAY,CAAC,KAAK,IAAI,YAAY,EAAE;AAC9C,QAAQ,CAAC,EAAE,GAAG,OAAO,CAAC,gBAAgB,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AAClF,OAAO;AACP,KAAK,CAAC;AACN,IAAIC,SAAK,CAAC,MAAM,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,KAAK,KAAK,oBAAoB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC1F,IAAI;AACJ,MAAM,MAAM,UAAU,GAAG,CAAC,KAAK,KAAK;AACpC,QAAQ,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;AAC5C,OAAO,CAAC;AACR,MAAM,MAAM,aAAa,GAAG,CAAC,KAAK,KAAK;AACvC,QAAQ,OAAO,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC3C,OAAO,CAAC;AACR,MAAMC,WAAO,CAAC,CAAC,EAAET,6BAAsB,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;AAC1D,QAAQ,UAAU;AAClB,QAAQ,aAAa;AACrB,QAAQ,gBAAgB;AACxB,QAAQ,YAAY;AACpB,QAAQ,KAAK,EAAE,OAAO,CAAC,KAAK,GAAG,CAAC;AAChC,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,MAAM,CAAC;AACX,MAAM,MAAM;AACZ,KAAK,CAAC,CAAC;AACP,IAAIU,aAAS,CAAC,MAAM;AACpB,MAAM,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AAChC,MAAM,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AAC/B,KAAK,CAAC,CAAC;AACP,IAAIC,mBAAe,CAAC,MAAM;AAC1B,MAAM,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AAClC,MAAM,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AACnC,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,MAAM;AACjB,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,MAAM,QAAQ,GAAG;AACvB,QAAQ,CAAC,EAAE,GAAG,KAAK,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;AAC5D,QAAQC,KAAC,CAACC,cAAM,EAAE;AAClB,UAAU,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,YAAY,CAAC;AAC1C,UAAU,KAAK,EAAE;AACjB,YAAY,SAAS,EAAE,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,eAAe,IAAI,KAAK,CAAC,cAAc,IAAI,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,gBAAgB,IAAI,QAAQ,CAAC,KAAK,CAAC,QAAQ,GAAG,MAAM,GAAG,iBAAiB,GAAG,MAAM;AACzM,WAAW;AACX,SAAS,EAAE;AACX,UAAU,OAAO,EAAE,MAAMC,eAAQ,CAAC,gBAAgB,CAAC,KAAK,CAAC,GAAGF,KAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,GAAGA,KAAC,CAAC,gBAAgB,CAAC,KAAK,CAAC;AACjJ,SAAS,CAAC;AACV,OAAO,CAAC;AACR,MAAM,MAAM,KAAK,GAAG,QAAQ,CAAC,WAAW,GAAGA,KAAC,CAACG,iBAAS,EAAE;AACxD,QAAQ,GAAG,EAAE,OAAO;AACpB,QAAQ,OAAO,EAAE,MAAM,CAAC,KAAK;AAC7B,QAAQ,MAAM,EAAE,OAAO;AACvB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,MAAM,EAAE,mBAAmB,CAAC,KAAK;AACzC,QAAQ,SAAS,EAAE,KAAK;AACxB,QAAQ,UAAU,EAAE,UAAU,CAAC,KAAK;AACpC,QAAQ,WAAW,EAAE,kBAAkB,CAAC,KAAK;AAC7C,QAAQ,SAAS,EAAE,gBAAgB,CAAC,KAAK;AACzC,QAAQ,UAAU,EAAE,YAAY,CAAC,KAAK;AACtC,QAAQ,kBAAkB,EAAE,kBAAkB,CAAC,KAAK;AACpD,QAAQ,UAAU,EAAE,kBAAkB,CAAC,KAAK;AAC5C,QAAQ,eAAe,EAAE,KAAK;AAC9B,OAAO,EAAE;AACT,QAAQ,OAAO,EAAE,MAAM;AACvB,UAAU,IAAI,GAAG,CAAC;AAClB,UAAU,OAAOH,KAAC,CAAC,KAAK,EAAE;AAC1B,YAAY,KAAK,EAAE;AACnB,cAAc,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;AAClC,cAAc,MAAM,CAAC,CAAC,CAAC,iBAAiB,CAAC;AACzC,cAAc,kBAAkB,CAAC,KAAK;AACtC,aAAa;AACb,YAAY,YAAY,EAAE,CAAC,GAAG,KAAK,gBAAgB,CAAC,GAAG,EAAE,GAAG,CAAC;AAC7D,YAAY,YAAY,EAAE,MAAM,gBAAgB,CAAC,IAAI,CAAC;AACtD,YAAY,OAAO,EAAE,CAAC,GAAG,KAAK,gBAAgB,CAAC,GAAG,EAAE,GAAG,CAAC;AACxD,WAAW,EAAE;AACb,YAAYA,KAAC,CAAC,IAAI,EAAE;AACpB,cAAc,KAAK,EAAE;AACrB,gBAAgB,MAAM,CAAC,CAAC,EAAE;AAC1B,gBAAgB,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC;AACjC,gBAAgB,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;AAC3D,eAAe;AACf,cAAc,KAAK,EAAE,OAAO,CAAC,KAAK;AAClC,aAAa,EAAE,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AAC1E,WAAW,CAAC,CAAC;AACb,SAAS;AACT,QAAQ,OAAO,EAAE,MAAMA,KAAC,CAAC,KAAK,EAAE;AAChC,UAAU,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC;AACrC,UAAU,OAAO,EAAE,WAAW;AAC9B,SAAS,EAAE,QAAQ,CAAC;AACpB,OAAO,CAAC,GAAGA,KAAC,CAACI,YAAQ,EAAE,EAAE,EAAE;AAC3B,QAAQJ,KAAC,CAAC,KAAK,EAAE;AACjB,UAAU,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC;AACrC,UAAU,GAAG,EAAE,gBAAgB;AAC/B,UAAU,OAAO,EAAE,WAAW;AAC9B,SAAS,EAAE,QAAQ,CAAC;AACpB,QAAQA,KAAC,CAACK,4BAAoB,EAAE,EAAE,EAAE;AACpC,UAAU,OAAO,EAAE,MAAM;AACzB,YAAY,IAAI,GAAG,CAAC;AACpB,YAAY,OAAOC,kBAAc,CAACN,KAAC,CAAC,IAAI,EAAE;AAC1C,cAAc,IAAI,EAAE,MAAM;AAC1B,cAAc,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;AACrD,cAAc,KAAK,EAAE,OAAO,CAAC,KAAK;AAClC,aAAa,EAAE,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAACO,SAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACrG,WAAW;AACX,SAAS,CAAC;AACV,OAAO,CAAC,CAAC;AACT,MAAM,OAAOP,KAAC,CAAC,IAAI,EAAE;AACrB,QAAQ,KAAK,EAAE;AACf,UAAU,SAAS,CAAC,CAAC,EAAE;AACvB,UAAU,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC;AAC9C,UAAU,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC;AAC9C,UAAU,SAAS,CAAC,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC,QAAQ,CAAC;AAClD,SAAS;AACT,QAAQ,IAAI,EAAE,UAAU;AACxB,QAAQ,YAAY,EAAE,IAAI;AAC1B,QAAQ,YAAY,EAAE,MAAM,CAAC,KAAK;AAClC,QAAQ,YAAY,EAAE,gBAAgB;AACtC,QAAQ,YAAY,EAAE,MAAM,gBAAgB,EAAE;AAC9C,QAAQ,OAAO,EAAE,gBAAgB;AACjC,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;AAClB,KAAK,CAAC;AACN,GAAG;AACH,CAAC,CAAC;;;;;"}