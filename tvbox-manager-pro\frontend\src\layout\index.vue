<template>
  <div class="app-wrapper" :class="{ 'mobile': appStore.isMobile }">
    <!-- 侧边栏 -->
    <div
      v-if="!appStore.isMobile"
      class="sidebar-container"
      :class="{ 'collapsed': appStore.isCollapsed }"
    >
      <Sidebar />
    </div>
    
    <!-- 移动端侧边栏遮罩 -->
    <div
      v-if="appStore.isMobile && !appStore.isCollapsed"
      class="sidebar-mask"
      @click="appStore.toggleSidebar"
    />
    
    <!-- 移动端侧边栏 -->
    <transition name="sidebar">
      <div
        v-if="appStore.isMobile && !appStore.isCollapsed"
        class="mobile-sidebar"
      >
        <Sidebar />
      </div>
    </transition>
    
    <!-- 主内容区域 -->
    <div class="main-container" :class="{ 'collapsed': appStore.isCollapsed && !appStore.isMobile }">
      <!-- 顶部导航栏 -->
      <div class="navbar-container">
        <Navbar />
      </div>
      
      <!-- 标签页导航 -->
      <!-- <div v-if="showTags" class="tags-container">
        <TagsView />
      </div> -->
      
      <!-- 页面内容 -->
      <div class="app-main">
        <router-view v-slot="{ Component, route }">
          <transition name="fade-transform" mode="out-in">
            <keep-alive :include="cachedViews">
              <component :is="Component" :key="route.path" />
            </keep-alive>
          </transition>
        </router-view>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useAppStore } from '@/stores/app'
import Sidebar from './components/Sidebar/index.vue'
import Navbar from './components/Navbar.vue'
// import TagsView from './components/TagsView.vue'

const appStore = useAppStore()

// 计算属性
// const showTags = computed(() => true) // 可以根据设置控制是否显示标签页
const cachedViews = computed(() => []) // 缓存的视图组件名称列表
</script>

<style lang="scss" scoped>
.app-wrapper {
  position: relative;
  height: 100vh;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  display: flex;

  &.mobile {
    .main-container {
      margin-left: 0;
      width: 100%;
      max-width: 100vw;
    }
  }
}

.sidebar-container {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  width: 210px;
  background: var(--el-bg-color);
  border-right: 1px solid var(--el-border-color-light);
  transition: width 0.28s;
  z-index: 1001;
  
  &.collapsed {
    width: 64px;
  }
}

.sidebar-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1000;
}

.mobile-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  width: 210px;
  background: var(--el-bg-color);
  border-right: 1px solid var(--el-border-color-light);
  z-index: 1001;
}

.main-container {
  flex: 1;
  margin-left: 210px;
  transition: margin-left 0.28s;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  max-width: calc(100vw - 210px);
  overflow-x: hidden;

  &.collapsed {
    margin-left: 64px;
    max-width: calc(100vw - 64px);
  }
}

.navbar-container {
  height: 50px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

.tags-container {
  height: 34px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
}

.app-main {
  flex: 1;
  padding: 20px;
  background: var(--el-bg-color-page);
  overflow: auto;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

// 过渡动画
.sidebar-enter-active,
.sidebar-leave-active {
  transition: transform 0.28s;
}

.sidebar-enter-from {
  transform: translateX(-100%);
}

.sidebar-leave-to {
  transform: translateX(-100%);
}

.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.5s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

// 响应式设计
@media (max-width: 768px) {
  .app-main {
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .app-main {
    padding: 10px;
  }
}
</style>
