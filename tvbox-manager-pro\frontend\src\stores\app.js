import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import Cookies from 'js-cookie'

export const useAppStore = defineStore('app', () => {
  // 状态
  const isDark = ref(false)
  const isCollapsed = ref(false)
  const language = ref('zh-cn')
  const size = ref('default')
  const loading = ref(false)
  const device = ref('desktop')
  
  // 计算属性
  const theme = computed(() => isDark.value ? 'dark' : 'light')
  const isMobile = computed(() => device.value === 'mobile')
  const isTablet = computed(() => device.value === 'tablet')
  const isDesktop = computed(() => device.value === 'desktop')
  
  // 方法
  const initApp = () => {
    // 从本地存储恢复设置
    const savedTheme = Cookies.get('theme')
    if (savedTheme) {
      isDark.value = savedTheme === 'dark'
    } else {
      // 检查系统主题偏好
      isDark.value = window.matchMedia('(prefers-color-scheme: dark)').matches
    }
    
    const savedCollapsed = Cookies.get('sidebar-collapsed')
    if (savedCollapsed) {
      isCollapsed.value = savedCollapsed === 'true'
    }
    
    const savedLanguage = Cookies.get('language')
    if (savedLanguage) {
      language.value = savedLanguage
    }
    
    const savedSize = Cookies.get('element-size')
    if (savedSize) {
      size.value = savedSize
    }
    
    // 检测设备类型
    detectDevice()
    
    // 监听窗口大小变化
    window.addEventListener('resize', detectDevice)
    
    // 监听系统主题变化
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      if (!Cookies.get('theme')) {
        isDark.value = e.matches
        applyTheme()
      }
    })
    
    // 应用主题
    applyTheme()
  }
  
  const detectDevice = () => {
    const width = window.innerWidth
    if (width < 768) {
      device.value = 'mobile'
      isCollapsed.value = true
    } else if (width < 1024) {
      device.value = 'tablet'
    } else {
      device.value = 'desktop'
    }
  }
  
  const toggleTheme = () => {
    isDark.value = !isDark.value
    Cookies.set('theme', theme.value, { expires: 365 })
    applyTheme()
  }
  
  const applyTheme = () => {
    const html = document.documentElement
    if (isDark.value) {
      html.classList.add('dark')
    } else {
      html.classList.remove('dark')
    }
  }
  
  const toggleSidebar = () => {
    isCollapsed.value = !isCollapsed.value
    Cookies.set('sidebar-collapsed', isCollapsed.value.toString(), { expires: 365 })
  }
  
  const setLanguage = (lang) => {
    language.value = lang
    Cookies.set('language', lang, { expires: 365 })
  }
  
  const setSize = (newSize) => {
    size.value = newSize
    Cookies.set('element-size', newSize, { expires: 365 })
  }
  
  const setLoading = (status) => {
    loading.value = status
  }
  
  const showLoading = () => {
    loading.value = true
  }
  
  const hideLoading = () => {
    loading.value = false
  }
  
  return {
    // 状态
    isDark,
    isCollapsed,
    language,
    size,
    loading,
    device,
    
    // 计算属性
    theme,
    isMobile,
    isTablet,
    isDesktop,
    
    // 方法
    initApp,
    detectDevice,
    toggleTheme,
    applyTheme,
    toggleSidebar,
    setLanguage,
    setSize,
    setLoading,
    showLoading,
    hideLoading
  }
})
