{"spider": "/localized/interface_1_真实TVBox配置/spider/f0729.jar", "wallpaper": "https://深色壁纸.xxooo.cf/", "sites": [{"key": "豆", "name": "修复更新所有【神秘的哥哥们】", "type": 3, "api": "csp_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indexs": 1, "searchable": 0, "quickSearch": 0, "changeable": 0}, {"key": "玩偶", "name": "👽玩偶哥哥┃4K弹幕", "type": 3, "api": "csp_WoGGGuard", "timeout": 30, "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": {"Cloud-drive": "tvfan/Cloud-drive.txt", "from": "4k|auto", "siteUrl": "https://www.wogg.com/", "danMu": "弹"}}, {"key": "YGP", "name": "🚀叨观荐影┃预告片", "type": 3, "api": "csp_YGPGuard", "searchable": 0, "quickSearch": 0, "changeable": 0}, {"key": "alllive", "name": "📽️一直播┃直播", "type": 3, "api": "csp_AllliveGuard", "playerType": 2, "searchable": 0, "quickSearch": 0, "changeable": 0}, {"key": "seed", "name": "💡聚剧剧┃三盘", "type": 3, "api": "csp_<PERSON>dhub<PERSON>uard", "changeable": 0, "ext": "5++kwLhNYm9UrO9wh7Dl7eKamTee4s/5", "searchable": 1, "quickSearch": 1, "indexs": 0, "style": {"type": "list"}}, {"key": "白白", "name": "🐟白白┃秒播", "type": 3, "api": "csp_SbaibaiGuard", "timeout": 15, "playerType": 2, "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "厂长", "name": "📔厂长┃不卡", "type": 3, "api": "csp_NewCzGuard", "timeout": 15, "playerType": 2, "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "南瓜", "name": "🎃南瓜┃不卡", "type": 3, "api": "csp_<PERSON>", "timeout": 15, "playerType": 2, "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "原创", "name": "☀原创┃不卡", "type": 3, "api": "csp_YCyzGuard", "timeout": 30, "playerType": 1, "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "苹果", "name": "🍎苹果┃不卡", "type": 3, "api": "csp_LiteAppleGuard", "timeout": 15, "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "糯米", "name": "🍓糯米┃秒播", "type": 3, "api": "csp_NmyswvGuard", "timeout": 15, "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "文采", "name": "💮文采┃秒播", "type": 3, "api": "csp_JpysGuard", "timeout": 15, "playerType": 2, "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "Lib", "name": "🌟立播┃秒播", "type": 3, "api": "csp_LibvioGuard", "timeout": 15, "searchable": 1, "quickSearch": 1, "changeable": 1, "ext": {"Cloud-drive": "tvfan/Cloud-drive.txt", "from": "4k|auto"}}, {"key": "zxzj", "name": "🍊在线┃秒播", "type": 3, "api": "csp_ZxzjGuard", "timeout": 15, "searchable": 1, "quickSearch": 1, "changeable": 1, "ext": "https://www.zxzjhd.com/"}, {"key": "比特", "name": "🍄比特┃手机", "type": 3, "api": "csp_BttwooGuard", "timeout": 15, "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "低端", "name": "⏮️低端┃外剧", "type": 3, "api": "csp_DdrkGuard", "timeout": 15, "playerType": "2", "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "萌米", "name": "👀萌米┃多线", "type": 3, "api": "csp_AppTTGuard", "timeout": 15, "playerType": 2, "searchable": 1, "quickSearch": 1, "changeable": 1, "ext": "uqGL1bNENEIVq+dC1p/Y9uWjuA=="}, {"key": "热播", "name": "📺热播┃多线", "type": 3, "api": "csp_AppTTGuard", "timeout": 15, "playerType": 2, "searchable": 1, "quickSearch": 1, "changeable": 1, "ext": "uqGL1bNENExT7/hGxpSE5qU="}, {"key": "欢视", "name": "👓欢视┃多线", "type": 3, "api": "csp_AppTTGuard", "timeout": 15, "playerType": 2, "searchable": 1, "quickSearch": 1, "changeable": 1, "ext": "uqGL1bNENExT9fFAy5mE5qU="}, {"key": "奥特", "name": "🏝奥特┃多线", "type": 3, "api": "csp_<PERSON><PERSON>Guard", "timeout": 15, "searchable": 1, "quickSearch": 1, "changeable": 1, "ext": "https://auete.com/"}, {"key": "荐片", "name": "🐭荐片┃P2P", "type": 3, "api": "csp_<PERSON><PERSON><PERSON><PERSON>", "timeout": 15, "playerType": 2, "searchable": 1, "quickSearch": 1, "changeable": 0}, {"key": "新6V", "name": "🧲新6V┃磁力", "type": 3, "api": "csp_SixVGuard", "timeout": 15, "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": "https://www.xb6v.com/"}, {"key": "Dm84", "name": "🚌巴士┃动漫", "type": 3, "api": "csp_Dm84Guard", "timeout": 15, "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "Ysj", "name": "🎀异界┃动漫", "type": 3, "api": "csp_<PERSON><PERSON>j<PERSON><PERSON>", "timeout": 15, "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "Anime1", "name": "🐾日本┃动漫", "type": 3, "api": "csp_Anime1Guard", "timeout": 15, "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "926", "name": "⚽926┃看球", "type": 3, "api": "csp_kanqiu926Guard", "timeout": 15, "searchable": 0, "changeable": 0, "style": {"type": "list"}, "ext": "uqGL1fpJNAUa4uUHi9iMsef1+C/R"}, {"key": "88", "name": "⚽88┃看球", "type": 3, "api": "csp_Sir88Guard", "timeout": 15, "searchable": 0, "changeable": 0, "style": {"type": "list"}}, {"key": "看球", "name": "⚽看球┃直播", "type": 3, "api": "csp_<PERSON><PERSON><PERSON><PERSON>", "timeout": 15, "searchable": 0, "changeable": 0, "style": {"type": "list"}}, {"key": "MTV", "name": "🎧明星┃MV", "type": 3, "api": "csp_<PERSON><PERSON><PERSON><PERSON>", "style": {"type": "rect", "ratio": 1.597}, "searchable": 0, "quickSearch": 0, "changeable": 0, "ext": {"json": "/localized/interface_1_真实TVBox配置/json/file.json"}}, {"key": "虎牙直播js", "name": "🐯虎牙┃直播", "type": 3, "api": "/localized/interface_1_真实TVBox配置/js/drpy2.js", "ext": "/localized/interface_1_真实TVBox配置/js/huya2.js", "style": {"type": "rect", "ratio": 1.755}, "timeout": 15, "playerType": "2", "searchable": 0, "quickSearch": 0, "changeable": 0}, {"key": "斗鱼js", "name": "🐟斗鱼┃直播", "type": 3, "api": "/localized/interface_1_真实TVBox配置/js/drpy2.min.js", "ext": "/localized/interface_1_真实TVBox配置/js/斗鱼直播.js", "style": {"type": "rect", "ratio": 1.755}, "timeout": 15, "playerType": "2", "searchable": 0, "quickSearch": 0, "changeable": 0}, {"key": "有声小说js", "name": "🎧有声┃小说", "type": 3, "api": "/localized/interface_1_真实TVBox配置/js/drpy2.min.js", "ext": "/localized/interface_1_真实TVBox配置/js/有声小说吧.js", "style": {"type": "rect", "ratio": 1}, "timeout": 15, "searchable": 0, "quickSearch": 0, "changeable": 0}, {"key": "Aid", "name": "🚑急救┃教学", "type": 3, "api": "csp_FirstAidGuard", "searchable": 0, "quickSearch": 0, "changeable": 0, "style": {"type": "rect", "ratio": 3.8}}, {"key": "抠搜", "name": "🍄抠抠┃搜搜", "type": 3, "api": "csp_KkSsGuard", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": {"Cloud-drive": "tvfan/Cloud-drive.txt", "from": "4k|auto"}}, {"key": "UC", "name": "🌈优汐┃搜搜", "type": 3, "api": "csp_UuSsGuard", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": {"Cloud-drive": "tvfan/Cloud-drive.txt", "from": "4k|auto"}}, {"key": "YpanSo", "name": "🐟盘她┃三盘", "type": 3, "api": "csp_YpanSoGuard", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": {"Cloud-drive": "tvfan/Cloud-drive.txt", "from": "4k|auto"}}, {"key": "xzso", "name": "👻盘它┃三盘", "type": 3, "api": "csp_<PERSON>zsoGuard", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": {"Cloud-drive": "tvfan/Cloud-drive.txt", "from": "4k|auto"}}, {"key": "米搜", "name": "🦋米搜┃夸父", "type": 3, "api": "csp_MIPanSoGuard", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": {"Cloud-drive": "tvfan/Cloud-drive.txt", "from": "4k|auto"}}, {"key": "夸搜", "name": "😻夸搜┃夸父", "type": 3, "api": "csp_PanSearchGuard", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": {"pan": "quark", "Cloud-drive": "tvfan/Cloud-drive.txt", "from": "4k|auto"}}, {"key": "<PERSON><PERSON>", "name": "🙀盘搜┃阿狸", "type": 3, "api": "csp_PanSearchGuard", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": {"Cloud-drive": "tvfan/Cloud-drive.txt", "from": "4k|auto"}}, {"key": "YiSo", "name": "😹易搜┃阿狸", "type": 3, "api": "csp_<PERSON><PERSON><PERSON>", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": {"Cloud-drive": "tvfan/Cloud-drive.txt", "from": "4k|auto"}}, {"key": "push_agent", "name": "🛴手机┃推送", "type": 3, "api": "csp_<PERSON><PERSON><PERSON><PERSON>", "searchable": 0, "quickSearch": 0, "ext": {"Cloud-drive": "tvfan/Cloud-drive.txt", "from": "4k|auto"}}, {"key": "Bili", "name": "🅱哔哔合集┃弹幕", "type": 3, "api": "csp_<PERSON><PERSON><PERSON><PERSON>", "style": {"type": "rect", "ratio": 1.597}, "searchable": 1, "quickSearch": 0, "changeable": 0, "ext": {"json": "/localized/interface_1_真实TVBox配置/json/file.json"}}, {"key": "<PERSON><PERSON><PERSON>", "name": "🅱哔哔演唱会┃弹幕", "type": 3, "api": "csp_<PERSON><PERSON><PERSON><PERSON>", "style": {"type": "rect", "ratio": 1.597}, "searchable": 1, "quickSearch": 0, "changeable": 0, "ext": {"json": "/localized/interface_1_真实TVBox配置/json/file.json"}}, {"key": "dr_兔小贝", "name": "📚儿童┃启蒙", "type": 3, "api": "/localized/interface_1_真实TVBox配置/js/drpy2.min.js", "ext": "/localized/interface_1_真实TVBox配置/js/%E5%85%94%E5%B0%8F%E8%B4%9D.js", "style": {"type": "rect", "ratio": 1.597}, "searchable": 0, "quickSearch": 0, "changeable": 0}, {"key": "少儿教育", "name": "📚少儿┃教育", "type": 3, "api": "csp_<PERSON><PERSON><PERSON><PERSON>", "style": {"type": "rect", "ratio": 1.597}, "searchable": 0, "quickSearch": 0, "changeable": 0, "ext": {"json": "/localized/interface_1_真实TVBox配置/json/file.json"}}, {"key": "小学课堂", "name": "📚小学┃课堂", "type": 3, "api": "csp_<PERSON><PERSON><PERSON><PERSON>", "style": {"type": "rect", "ratio": 1.597}, "searchable": 0, "quickSearch": 0, "changeable": 0, "ext": {"json": "/localized/interface_1_真实TVBox配置/json/file.json"}}, {"key": "初中课堂", "name": "📚初中┃课堂", "type": 3, "api": "csp_<PERSON><PERSON><PERSON><PERSON>", "style": {"type": "rect", "ratio": 1.597}, "searchable": 0, "quickSearch": 0, "changeable": 0, "ext": {"json": "/localized/interface_1_真实TVBox配置/json/file.json"}}, {"key": "高中教育", "name": "📚高中┃课堂", "type": 3, "api": "csp_<PERSON><PERSON><PERSON><PERSON>", "style": {"type": "rect", "ratio": 1.597}, "searchable": 0, "quickSearch": 0, "changeable": 0, "ext": {"json": "/localized/interface_1_真实TVBox配置/json/file.json"}}, {"key": "fan", "name": "导航 www.饭太硬.com", "type": 3, "api": "csp_XPathGuard", "searchable": 1, "quickSearch": 0, "changeable": 0}, {"key": "cc", "name": "请勿相信视频中广告", "type": 3, "api": "csp_XPathGuard", "searchable": 1, "quickSearch": 0, "changeable": 0}], "logo": "https://cdn.wmpvp.com/steamWeb/B96F868DE45B45D690B5F9F490D0BC60-1745866248445.gif", "lives": [{"name": "V6范明明（需开启V6网络）", "type": 0, "url": "/localized/interface_1_真实TVBox配置/live/20250425-868403-********************************.m3u", "playerType": 2}, {"name": "V4聚合（卡顿请按左┃右键换线）", "type": 0, "url": "/localized/interface_1_真实TVBox配置/live/tv.m3u", "playerType": 2, "epg": "http://epg.51zmt.top:8000/api/diyp/?ch={name}&date={date}"}, {"name": "牛播一", "type": 0, "url": "http://127.0.0.1:9978/proxy?do=饭太硬&type=liveList", "playerType": 2, "epg": "http://epg.51zmt.top:8000/api/diyp/?ch={name}&date={date}"}, {"name": "平台直播", "type": 0, "url": "http://tv.iill.top/m3u/Live", "ua": "okhttp/3.15", "playerType": 2}]}