#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import logging
import time
import os
import re
import json
from tvbox_assistant.utils.decryptor import TVBoxDecryptor

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s [%(levelname)s] %(name)s:%(lineno)d - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger('test_decryptor')

def show_error_context(content, error_position, context_lines=5):
    """显示JSON错误周围的上下文"""
    lines = content.splitlines()
    
    # 找出错误位置所在的行
    line_count = 0
    char_count = 0
    error_line = 0
    for i, line in enumerate(lines):
        char_count += len(line) + 1  # +1 for newline
        if char_count >= error_position:
            error_line = i
            break
        line_count += 1
    
    # 计算要显示的行范围
    start_line = max(0, error_line - context_lines)
    end_line = min(len(lines), error_line + context_lines + 1)
    
    # 构建显示内容
    error_context = []
    for i in range(start_line, end_line):
        prefix = ">> " if i == error_line else "   "
        error_context.append(f"{prefix}{i+1:4d}: {lines[i]}")
    
    return "\n".join(error_context)

def remove_comments_preserve_url(content):
    """移除注释但保留URL结构"""
    # 分析内容的行
    lines = content.splitlines()
    cleaned_lines = []
    
    for line in lines:
        # 过滤掉整行注释 (以//开头的行，前面可能有空白)
        if re.match(r'^\s*//.*$', line):
            continue
        
        # 过滤掉行内注释，但保护URL中的 '//'
        # 先标记所有URL中的 '//' 为特殊标记
        line = re.sub(r'(https?:)//', r'\1URLSLASHSLASH', line)
        # 移除注释
        line = re.sub(r'//.*$', '', line)
        # 恢复URL
        line = re.sub(r'URLSLASHSLASH', '//', line)
        
        cleaned_lines.append(line)
    
    # 过滤掉多行注释，但保留格式
    content = '\n'.join(cleaned_lines)
    content = re.sub(r'/\*.*?\*/', '', content, flags=re.DOTALL)
    
    return content

def test_url(url, save_raw_content=True):
    """测试解密URL"""
    logger.info(f"测试URL解密: {url}")
    try:
        decryptor = TVBoxDecryptor()
        start_time = time.time()
        result, method = decryptor.decrypt_config_url(url)
        elapsed = time.time() - start_time
        
        logger.info(f"解密方法: {method}")
        logger.info(f"耗时: {elapsed:.2f}秒")
        
        # 保存完整响应内容到文件便于分析
        if save_raw_content and result and result != url:
            save_dir = "response_logs"
            os.makedirs(save_dir, exist_ok=True)
            save_path = os.path.join(save_dir, f"raw_response_{int(time.time())}.txt")
            with open(save_path, "w", encoding="utf-8") as f:
                f.write(result)
            logger.info(f"已保存完整响应内容到: {save_path}")
        
        # 打印结果摘要
        if len(result) > 500:
            logger.info(f"解密结果 (前500字符): {result[:500]}...")
        else:
            logger.info(f"解密结果: {result}")
            
        # 分析配置
        config_parsed = False
        if result.strip().startswith('{'):
            try:
                # 只移除注释，保留URL格式
                cleaned_content = remove_comments_preserve_url(result)
                
                # 保存处理后的内容
                if save_raw_content and cleaned_content != result:
                    cleaned_path = os.path.join(save_dir, f"cleaned_{int(time.time())}.txt")
                    with open(cleaned_path, "w", encoding="utf-8") as f:
                        f.write(cleaned_content)
                    logger.info(f"已保存处理后内容到: {cleaned_path}")
                
                try:
                    # 尝试解析JSON
                    json_data = json.loads(cleaned_content)
                    # 格式化输出但不添加空行，使格式与ysys.json一致
                    formatted_json = json.dumps(json_data, ensure_ascii=False, indent=2, separators=(',', ':'))
                    
                    # 保存格式化后的JSON
                    if save_raw_content:
                        formatted_path = os.path.join(save_dir, f"formatted_{int(time.time())}.json")
                        with open(formatted_path, "w", encoding="utf-8") as f:
                            f.write(formatted_json)
                        logger.info(f"已保存格式化后JSON到: {formatted_path}")
                    
                    # 使用格式化后的内容进行解析
                    config_info = decryptor.parse_tvbox_config_content(formatted_json)
                    sites_count = len(config_info.get('sites', []))
                    lives_count = sum(len(group.get('channels', [])) for group in config_info.get('lives', []))
                    lives_group_count = len(config_info.get('lives', []))
                    parses_count = len(config_info.get('parses', []))
                    
                    logger.info(f"配置分析:")
                    logger.info(f"站点数量: {sites_count}")
                    logger.info(f"直播分组: {lives_group_count}, 频道: {lives_count}")
                    logger.info(f"解析器数量: {parses_count}")
                    logger.info(f"Spider: {config_info.get('spider', '')}")
                    logger.info(f"壁纸: {config_info.get('wallpaper', '')}")
                    logger.info(f"Logo: {config_info.get('logo', '')}")
                    
                    # 至少有一项配置数据不为空，才算解析成功
                    if sites_count > 0 or lives_count > 0 or parses_count > 0 or config_info.get('spider'):
                        config_parsed = True
                    else:
                        logger.warning("配置数据为空，可能解析不完整")
                except json.JSONDecodeError as json_err:
                    # 如果仍然解析失败，显示错误上下文
                    error_position = json_err.pos
                    error_line = json_err.lineno
                    error_col = json_err.colno
                    logger.error(f"JSON解析错误: {json_err} (行 {error_line}, 列 {error_col}, 位置 {error_position})")
                    
                    # 显示错误周围的内容
                    error_context = show_error_context(cleaned_content, error_position)
                    logger.error(f"错误上下文:\n{error_context}")
                    
                    # 尝试识别问题字符
                    if error_position < len(cleaned_content):
                        problem_char = cleaned_content[error_position]
                        problem_char_code = ord(problem_char)
                        logger.error(f"错误位置字符: '{problem_char}' (Unicode: U+{problem_char_code:04X})")
                    
                    # 尝试使用直接移除注释后的内容
                    logger.info("尝试使用原始内容解析...")
                    config_info = decryptor.parse_tvbox_config_content(cleaned_content)
                    sites_count = len(config_info.get('sites', []))
                    if sites_count > 0:
                        logger.info(f"原始内容解析成功，站点数量: {sites_count}")
                        config_parsed = True
            except Exception as e:
                logger.error(f"分析配置失败: {e}")
                import traceback
                logger.error(traceback.format_exc())
        else:
            if result != url:  # 如果返回内容不是原始URL
                logger.error(f"返回内容不是有效的JSON格式")
        
        # 只有当获取到内容并成功解析配置时，才算测试成功
        return config_parsed or (method != "无需解密" and result and result != url)
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    logger.info("=== TVBox解密器测试 ===")
    
    # 测试常见TVBox接口
    test_urls = [
        "http://www.饭太硬.com/tv",            # 中文域名接口          # 需要解密的接口
    ]
    
    success = 0
    failed = 0
    for url in test_urls:
        logger.info("=" * 50)
        if test_url(url):
            success += 1
            logger.info(f"✅ 测试成功: {url}")
        else:
            failed += 1
            logger.info(f"❌ 测试失败: {url}")
    
    logger.info("=" * 50)
    logger.info(f"测试完成: {success}/{len(test_urls)} 成功, {failed}/{len(test_urls)} 失败") 