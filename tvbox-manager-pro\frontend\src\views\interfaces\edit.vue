<template>
  <div class="edit-interface-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button icon="ArrowLeft" @click="$router.back()">返回</el-button>
        <div class="header-info">
          <h2>编辑接口源</h2>
          <p>修改接口源的基本信息和配置</p>
        </div>
      </div>
    </div>
    
    <el-row :gutter="20">
      <!-- 表单区域 -->
      <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
        <el-card class="form-card" v-loading="loading">
          <template #header>
            <span>基本信息</span>
          </template>
          
          <el-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="100px"
            label-position="left"
          >
            <el-form-item label="接口名称" prop="name">
              <el-input
                v-model="formData.name"
                placeholder="请输入接口名称"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
            
            <el-form-item label="接口地址" prop="url">
              <el-input
                v-model="formData.url"
                placeholder="请输入接口URL"
                type="textarea"
                :rows="3"
              />
            </el-form-item>
            
            <el-form-item label="接口描述" prop="description">
              <el-input
                v-model="formData.description"
                placeholder="请输入接口描述"
                type="textarea"
                :rows="4"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
            
            <el-form-item label="分类" prop="category">
              <el-select
                v-model="formData.category"
                placeholder="请选择分类"
                style="width: 100%"
              >
                <el-option label="影视" value="影视" />
                <el-option label="直播" value="直播" />
                <el-option label="综合" value="综合" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="标签" prop="tags">
              <el-input
                v-model="formData.tags"
                placeholder="请输入标签，多个标签用逗号分隔"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
            
            <el-form-item label="公开状态" prop="is_public">
              <el-switch
                v-model="formData.is_public"
                active-text="公开"
                inactive-text="私有"
              />
            </el-form-item>
            
            <el-form-item label="自动刷新" prop="update_interval">
              <el-select
                v-model="formData.update_interval"
                placeholder="请选择自动刷新间隔"
                style="width: 200px"
              >
                <el-option label="关闭" :value="0" />
                <el-option label="5分钟" :value="300" />
                <el-option label="10分钟" :value="600" />
                <el-option label="30分钟" :value="1800" />
                <el-option label="1小时" :value="3600" />
                <el-option label="6小时" :value="21600" />
                <el-option label="12小时" :value="43200" />
                <el-option label="24小时" :value="86400" />
              </el-select>
            </el-form-item>

            <el-form-item label="本地化" prop="enable_localization">
              <div style="display: flex; align-items: center; gap: 12px;">
                <el-switch
                  v-model="formData.enable_localization"
                  active-text="启用"
                  inactive-text="禁用"
                  :disabled="localizationLoading"
                  @change="handleLocalizationToggle"
                />
                <el-tag
                  v-if="formData.localization_status && formData.localization_status !== 'disabled'"
                  :type="getLocalizationStatusType(formData.localization_status)"
                  size="small"
                >
                  {{ getLocalizationStatusText(formData.localization_status) }}
                </el-tag>
                <el-progress
                  v-if="formData.localization_status === 'downloading' && formData.localization_progress > 0"
                  :percentage="formData.localization_progress"
                  :width="80"
                  type="circle"
                  :stroke-width="4"
                />

              </div>
              <div v-if="formData.enable_localization" style="margin-top: 8px;">
                <el-text size="small" type="info">
                  启用后将自动下载远程文件到本地，提高访问速度
                </el-text>
              </div>
              <div v-if="formData.localization_error_message" style="margin-top: 8px;">
                <el-text size="small" type="danger">
                  错误：{{ formData.localization_error_message }}
                </el-text>
              </div>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="handleSubmit" :loading="submitting">
                保存修改
              </el-button>
              <el-button @click="handleReset">重置</el-button>
              <el-button @click="$router.back()">取消</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      
      <!-- 帮助信息 -->
      <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
        <el-card class="help-card">
          <template #header>
            <span>编辑说明</span>
          </template>
          
          <div class="help-content">
            <h4>接口地址格式</h4>
            <p>支持以下格式的接口地址：</p>
            <ul>
              <li>直接JSON配置URL</li>
              <li>Base64编码的配置</li>
              <li>加密的配置链接</li>
            </ul>
            
            <h4>自动刷新</h4>
            <p>设置接口配置的自动更新频率，系统会定期检查并更新配置内容。</p>
            
            <h4>公开状态</h4>
            <p>公开的接口可以被其他用户订阅和使用，私有接口仅自己可见。</p>

            <h4>本地化功能</h4>
            <p>启用后将自动下载接口中的远程文件到本地，包括：</p>
            <ul>
              <li>Spider文件（转换为jar格式）</li>
              <li>JavaScript API文件</li>
              <li>JSON配置文件</li>
              <li>直播源文件</li>
            </ul>
            <p>本地化可以提高访问速度，减少网络依赖。</p>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { interfaceApi } from '@/api/interfaces'
import { ElMessage } from 'element-plus'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const localizationLoading = ref(false)
const formRef = ref()

const formData = reactive({
  name: '',
  url: '',
  description: '',
  category: '',
  tags: '',
  is_public: false,
  update_interval: 3600,
  enable_localization: false,
  localization_status: 'disabled',
  localization_progress: 0
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入接口名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  url: [
    { required: true, message: '请输入接口地址', trigger: 'blur' },
    { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ]
}

// 本地化状态相关方法
const getLocalizationStatusType = (status) => {
  const statusMap = {
    'disabled': 'info',
    'pending': 'warning',
    'downloading': 'warning',
    'completed': 'success',
    'error': 'danger'
  }
  return statusMap[status] || 'info'
}

const getLocalizationStatusText = (status) => {
  const statusMap = {
    'disabled': '已禁用',
    'pending': '等待中',
    'downloading': '下载中',
    'completed': '已完成',
    'error': '错误'
  }
  return statusMap[status] || '未知'
}

// 方法
const loadInterfaceData = async () => {
  try {
    loading.value = true
    const response = await interfaceApi.getInterfaceSource(route.params.id)
    const data = response.data
    
    // 填充表单数据
    Object.assign(formData, {
      name: data.name || '',
      url: data.url || '',
      description: data.description || '',
      category: data.category || '',
      tags: data.tags || '',
      is_public: data.is_public || false,
      update_interval: data.update_interval || 3600,
      enable_localization: data.enable_localization || false,
      localization_status: data.localization_status || 'disabled',
      localization_progress: data.localization_progress || 0,
      localization_error_message: data.localization_error_message || null
    })
  } catch (error) {
    ElMessage.error('加载接口数据失败')
    router.back()
  } finally {
    loading.value = false
  }
}

const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value.validate()

    submitting.value = true

    // 准备提交数据，排除本地化相关字段（这些字段通过专门的API管理）
    const submitData = {
      name: formData.name,
      description: formData.description,
      category: formData.category,
      tags: formData.tags,
      is_public: formData.is_public,
      update_interval: formData.update_interval
      // 注意：不包含 enable_localization 等本地化字段
    }

    // 提交更新
    await interfaceApi.updateInterfaceSource(route.params.id, submitData)

    ElMessage.success('接口更新成功')
    router.push('/interfaces')
  } catch (error) {
    if (error.errors) {
      // 表单验证错误
      return
    }
    ElMessage.error('更新接口失败')
  } finally {
    submitting.value = false
  }
}

const handleReset = () => {
  formRef.value.resetFields()
  loadInterfaceData()
}

// 本地化相关方法
const handleLocalizationToggle = async (enabled) => {
  try {
    localizationLoading.value = true

    if (enabled) {
      // 启用本地化
      const response = await interfaceApi.enableLocalization(route.params.id)
      if (response.data.success) {
        ElMessage.success('本地化启用成功')
        // 更新状态
        formData.localization_status = 'downloading'
        formData.localization_progress = 0

        // 开始轮询状态
        startStatusPolling()
      } else {
        ElMessage.error(response.data.error || '启用本地化失败')
        formData.enable_localization = false
      }
    } else {
      // 禁用本地化
      const response = await interfaceApi.disableLocalization(route.params.id)
      if (response.data.success) {
        ElMessage.success('本地化已禁用')
        formData.localization_status = 'disabled'
        formData.localization_progress = 0
        formData.localization_error_message = null
      } else {
        ElMessage.error(response.data.error || '禁用本地化失败')
        formData.enable_localization = true
      }
    }
  } catch (error) {
    ElMessage.error('操作失败')
    formData.enable_localization = !enabled
  } finally {
    localizationLoading.value = false
  }
}

const startStatusPolling = () => {
  const pollInterval = setInterval(async () => {
    try {
      const response = await interfaceApi.getLocalizationStatus(route.params.id)
      if (response.data.success) {
        const status = response.data
        formData.localization_status = status.localization_status
        formData.localization_progress = status.localization_progress
        formData.localization_error_message = status.error_message

        // 如果完成或出错，停止轮询
        if (status.localization_status === 'completed' || status.localization_status === 'error') {
          clearInterval(pollInterval)
          if (status.localization_status === 'completed') {
            ElMessage.success('本地化完成')
          } else {
            ElMessage.error('本地化失败')
          }
        }
      }
    } catch (error) {
      clearInterval(pollInterval)
    }
  }, 2000) // 每2秒轮询一次

  // 30秒后停止轮询
  setTimeout(() => {
    clearInterval(pollInterval)
  }, 30000)
}



// 生命周期
onMounted(() => {
  loadInterfaceData()
})
</script>

<style lang="scss" scoped>
.edit-interface-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .header-info {
      h2 {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
        color: #1f2937;
      }
      
      p {
        margin: 4px 0 0 0;
        color: #6b7280;
        font-size: 14px;
      }
    }
  }
}

.form-card {
  margin-bottom: 20px;
}

.help-card {
  .help-content {
    h4 {
      margin: 0 0 8px 0;
      font-size: 14px;
      font-weight: 600;
      color: #374151;
    }
    
    p {
      margin: 0 0 12px 0;
      font-size: 13px;
      color: #6b7280;
      line-height: 1.5;
    }
    
    ul {
      margin: 0 0 16px 0;
      padding-left: 16px;
      
      li {
        font-size: 13px;
        color: #6b7280;
        line-height: 1.5;
        margin-bottom: 4px;
      }
    }
  }
}
</style>
