var rule = {
    title: '奇珍异兽[官]',
    host: 'https://www.iqiyi.com',
    homeUrl: '',
    // detailUrl:'https://pcw-api.iqiyi.com/albums/album/avlistinfo?aid=fyid&size=2000&page=1',
    detailUrl: 'https://pcw-api.iqiyi.com/video/video/videoinfowithuser/fyid?agent_type=1&authcookie=&subkey=fyid&subscribe=1',
    searchUrl: 'https://search.video.iqiyi.com/o?if=html5&key=**&pageNum=fypage&pos=1&pageSize=24&site=iqiyi',
    searchable: 2,
    multi: 1,
    filterable: 1,
    filter: {
        '1': [{
            'key': 'mode',
            'name': '综合排序',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '热播榜',
                'v': 11
            }, {
                'n': '好评榜',
                'v': 8
            }, {
                'n': '新上线',
                'v': 4
            }]
        }, {
            'key': 'year',
            'name': '全部年份',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                "n": "2025",
                "v": 2025
            }, {
                "n": "2024",
                "v": 2024
            }, {
                'n': '2023',
                'v': 2023
            }, {
                'n': '2022',
                'v': 2022
            }, {
                'n': '2021',
                'v': 2021
            }, {
                'n': '2020',
                'v': 2020
            }, {
                'n': '2019',
                'v': 2019
            }, {
                'n': '2018',
                'v': 2018
            }, {
                'n': '2017',
                'v': 2017
            }, {
                'n': '2016-2011',
                'v': '2011_2016'
            }, {
                'n': '2010-2000',
                'v': '2000_2010'
            }, {
                'n': '90年代',
                'v': '1990_1999'
            }, {
                'n': '80年代',
                'v': '1980_1989'
            }, {
                'n': '更早',
                'v': '1964_1979'
            }]
        }, {
            'key': 'is_purchase',
            'name': '全部资费',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '免费',
                'v': 0
            }, {
                'n': '会员',
                'v': 1
            }, {
                'n': '付费',
                'v': 2
            }]
        }, {
            'key': 'three_category_id',
            'name': '地区',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '华语',
                'v': 1
            }, {
                'n': '香港地区',
                'v': 28997
            }, {
                'n': '美国',
                'v': 2
            }, {
                'n': '欧洲',
                'v': 3
            }, {
                'n': '韩国',
                'v': 4
            }, {
                'n': '日本',
                'v': 308
            }, {
                'n': '泰国',
                'v': 1115
            }, {
                'n': '印度',
                'v': 28999
            }, {
                'n': '其它',
                'v': 5
            }]
        }, {
            'key': '18001',
            'name': '类型',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '喜剧',
                'v': 8
            }, {
                'n': '爱情',
                'v': 6
            }, {
                'n': '动作',
                'v': 11
            }, {
                'n': '枪战',
                'v': 131
            }, {
                'n': '犯罪',
                'v': 291
            }, {
                'n': '惊悚',
                'v': 128
            }, {
                'n': '恐怖',
                'v': 10
            }, {
                'n': '悬疑',
                'v': 289
            }, {
                'n': '动画',
                'v': 12
            }, {
                'n': '家庭',
                'v': 27356
            }, {
                'n': '奇幻',
                'v': 1284
            }, {
                'n': '魔幻',
                'v': 129
            }, {
                'n': '科幻',
                'v': 9
            }, {
                'n': '战争',
                'v': 7
            }, {
                'n': '青春',
                'v': 130
            }]
        }, {
            'key': '27396',
            'name': '规格',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '巨制',
                'v': 27397
            }, {
                'n': '院线',
                'v': 27815
            }, {
                'n': '独播',
                'v': 30149
            }, {
                'n': '网络电影',
                'v': 27401
            }]
        }],
        '2': [{
            'key': 'mode',
            'name': '综合排序',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '热播榜',
                'v': 11
            }, {
                'n': '好评榜',
                'v': 8
            }, {
                'n': '新上线',
                'v': 4
            }]
        }, {
            'key': 'year',
            'name': '全部年份',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '2023',
                'v': 2023
            }, {
                'n': '2022',
                'v': 2022
            }, {
                'n': '2021',
                'v': 2021
            }, {
                'n': '2020',
                'v': 2020
            }, {
                'n': '2019',
                'v': 2019
            }, {
                'n': '2018',
                'v': 2018
            }, {
                'n': '2017',
                'v': 2017
            }, {
                'n': '2016-2011',
                'v': '2011_2016'
            }, {
                'n': '2010-2000',
                'v': '2000_2010'
            }, {
                'n': '90年代',
                'v': '1990_1999'
            }, {
                'n': '80年代',
                'v': '1980_1989'
            }, {
                'n': '更早',
                'v': '1964_1979'
            }]
        }, {
            'key': 'is_purchase',
            'name': '全部资费',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '免费',
                'v': 0
            }, {
                'n': '会员',
                'v': 1
            }, {
                'n': '付费',
                'v': 2
            }]
        }, {
            'key': 'three_category_id',
            'name': '地区',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '内地',
                'v': 15
            }, {
                'n': '港剧',
                'v': 16
            }, {
                'n': '韩剧',
                'v': 17
            }, {
                'n': '美剧',
                'v': 18
            }, {
                'n': '日剧',
                'v': 309
            }, {
                'n': '泰剧',
                'v': 1114
            }, {
                'n': '台湾地区',
                'v': 1117
            }, {
                'n': '英剧',
                'v': 28916
            }, {
                'n': '其它',
                'v': 19
            }]
        }, {
            'key': '18003',
            'name': '类型',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '自制',
                'v': 11992
            }, {
                'n': '古装',
                'v': 24
            }, {
                'n': '言情',
                'v': 20
            }, {
                'n': '武侠',
                'v': 23
            }, {
                'n': '偶像',
                'v': 30
            }, {
                'n': '家庭',
                'v': 1654
            }, {
                'n': '青春',
                'v': 1653
            }, {
                'n': '都市',
                'v': 24064
            }, {
                'n': '喜剧',
                'v': 135
            }, {
                'n': '战争',
                'v': 27916
            }, {
                'n': '军旅',
                'v': 1655
            }, {
                'n': '谍战',
                'v': 290
            }, {
                'n': '悬疑',
                'v': 32
            }, {
                'n': '罪案',
                'v': 149
            }, {
                'n': '穿越',
                'v': 148
            }, {
                'n': '宫廷',
                'v': 139
            }, {
                'n': '历史',
                'v': 21
            }, {
                'n': '神话',
                'v': 145
            }, {
                'n': '科幻',
                'v': 34
            }, {
                'n': '年代',
                'v': 27
            }, {
                'n': '农村',
                'v': 29
            }, {
                'n': '商战',
                'v': 140
            }, {
                'n': '剧情',
                'v': 24063
            }, {
                'n': '奇幻',
                'v': 27881
            }, {
                'n': '网剧',
                'v': 24065
            }, {
                'n': '竖短片',
                'v': 32839
            }]
        }],
        '3': [{
            'key': 'mode',
            'name': '综合排序',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '热播榜',
                'v': 11
            }, {
                'n': '好评榜',
                'v': 8
            }, {
                'n': '新上线',
                'v': 4
            }]
        }, {
            'key': 'year',
            'name': '全部年份',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '2023',
                'v': 2023
            }, {
                'n': '2022',
                'v': 2022
            }, {
                'n': '2021',
                'v': 2021
            }, {
                'n': '2020',
                'v': 2020
            }, {
                'n': '2019',
                'v': 2019
            }, {
                'n': '2018',
                'v': 2018
            }, {
                'n': '2017',
                'v': 2017
            }, {
                'n': '2016-2011',
                'v': '2011_2016'
            }, {
                'n': '2010-2000',
                'v': '2000_2010'
            }, {
                'n': '90年代',
                'v': '1990_1999'
            }, {
                'n': '80年代',
                'v': '1980_1989'
            }, {
                'n': '更早',
                'v': '1964_1979'
            }]
        }, {
            'key': 'is_purchase',
            'name': '全部资费',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '免费',
                'v': 0
            }, {
                'n': '会员',
                'v': 1
            }, {
                'n': '付费',
                'v': 2
            }]
        }, {
            'key': 'three_category_id',
            'name': '地区',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '国内',
                'v': 20323
            }, {
                'n': '国外',
                'v': 20324
            }]
        }, {
            'key': '18004',
            'name': '类型',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '人文',
                'v': 70
            }, {
                'n': '美食',
                'v': 33908
            }, {
                'n': '医疗',
                'v': 33924
            }, {
                'n': '自然',
                'v': 33933
            }, {
                'n': '萌宠',
                'v': 33945
            }, {
                'n': '财经',
                'v': 33953
            }, {
                'n': '罪案',
                'v': 33960
            }, {
                'n': '竞技',
                'v': 33967
            }, {
                'n': '灾难',
                'v': 33974
            }, {
                'n': '军事',
                'v': 72
            }, {
                'n': '历史',
                'v': 74
            }, {
                'n': '探险',
                'v': 73
            }, {
                'n': '社会',
                'v': 71
            }, {
                'n': '科技',
                'v': 28119
            }, {
                'n': '旅游',
                'v': 310
            }]
        }, {
            'key': '28467',
            'name': '出品方',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': 'BBC',
                'v': 28468
            }, {
                'n': '美国历史频道',
                'v': 28470
            }, {
                'n': '探索频道',
                'v': 28471
            }, {
                'n': '央视记录',
                'v': 28472
            }, {
                'n': '北京纪实频道',
                'v': 28473
            }, {
                'n': '上海纪实频道',
                'v': 28474
            }, {
                'n': '朗思文化',
                'v': 28476
            }, {
                'n': 'CNEX',
                'v': 28477
            }, {
                'n': '五星传奇',
                'v': 28478
            }, {
                'n': 'IMG',
                'v': 28479
            }, {
                'n': 'NHK',
                'v': 28480
            }, {
                'n': '爱奇艺出品',
                'v': 31283
            }, {
                'n': 'Netflix',
                'v': 31286
            }]
        }, {
            'key': '29076',
            'name': '片种',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '纪录电影',
                'v': 29077
            }, {
                'n': '系列纪录片',
                'v': 29078
            }, {
                'n': '网络纪录片',
                'v': 29082
            }, {
                'n': '纪实栏目',
                'v': 29083
            }]
        }, {
            'key': '31294',
            'name': '时长',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '微纪录',
                'v': 29079
            }, {
                'n': '长纪录',
                'v': 29080
            }, {
                'n': '短纪录',
                'v': 29081
            }]
        }],
        '4': [{
            'key': 'mode',
            'name': '综合排序',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '热播榜',
                'v': 11
            }, {
                'n': '好评榜',
                'v': 8
            }, {
                'n': '新上线',
                'v': 4
            }]
        }, {
            'key': 'year',
            'name': '全部年份',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '2023',
                'v': 2023
            }, {
                'n': '2022',
                'v': 2022
            }, {
                'n': '2021',
                'v': 2021
            }, {
                'n': '2020',
                'v': 2020
            }, {
                'n': '2019',
                'v': 2019
            }, {
                'n': '2018',
                'v': 2018
            }, {
                'n': '2017',
                'v': 2017
            }, {
                'n': '2016-2011',
                'v': '2011_2016'
            }, {
                'n': '2010-2000',
                'v': '2000_2010'
            }, {
                'n': '90年代',
                'v': '1990_1999'
            }, {
                'n': '80年代',
                'v': '1980_1989'
            }, {
                'n': '更早',
                'v': '1964_1979'
            }]
        }, {
            'key': 'is_purchase',
            'name': '全部资费',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '免费',
                'v': 0
            }, {
                'n': '会员',
                'v': 1
            }, {
                'n': '付费',
                'v': 2
            }]
        }, {
            'key': 'three_category_id',
            'name': '地区',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '中国大陆',
                'v': 37
            }, {
                'n': '日本',
                'v': 38
            }, {
                'n': '韩国',
                'v': 1106
            }, {
                'n': '欧美',
                'v': 30218
            }, {
                'n': '其它',
                'v': 40
            }]
        }, {
            'key': '30219',
            'name': '版本',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '动画',
                'v': 30220
            }, {
                'n': '特摄',
                'v': 30223
            }, {
                'n': '布袋戏',
                'v': 30224
            }, {
                'n': '特别篇',
                'v': 32782
            }, {
                'n': '动态漫画',
                'v': 32783
            }, {
                'n': '动画电影',
                'v': 32784
            }, {
                'n': '竖版视频',
                'v': 32785
            }, {
                'n': '轻动画',
                'v': 33482
            }, {
                'n': '短剧',
                'v': 33483
            }]
        }, {
            'key': '30225',
            'name': '风格',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '搞笑',
                'v': 30230
            }, {
                'n': '热血',
                'v': 30232
            }, {
                'n': '催泪',
                'v': 30233
            }, {
                'n': '治愈',
                'v': 30234
            }, {
                'n': '励志',
                'v': 30237
            }, {
                'n': '机战',
                'v': 30241
            }, {
                'n': '恋爱',
                'v': 30243
            }, {
                'n': '科幻',
                'v': 30245
            }, {
                'n': '奇幻',
                'v': 30247
            }, {
                'n': '推理',
                'v': 30248
            }, {
                'n': '校园',
                'v': 30249
            }, {
                'n': '日常',
                'v': 30252
            }, {
                'n': '历史',
                'v': 30254
            }, {
                'n': '美食',
                'v': 30255
            }, {
                'n': '职场',
                'v': 30256
            }, {
                'n': '偶像',
                'v': 30258
            }, {
                'n': '泡面',
                'v': 30265
            }, {
                'n': '冒险',
                'v': 30267
            }, {
                'n': '竞技',
                'v': 30268
            }, {
                'n': '合家欢',
                'v': 30270
            }, {
                'n': '武侠',
                'v': 32792
            }, {
                'n': '玄幻',
                'v': 32793
            }]
        }, {
            'key': '32795',
            'name': '新类型',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '轻小说改编',
                'v': 32796
            }, {
                'n': '漫画改编',
                'v': 32797
            }, {
                'n': '游戏改编',
                'v': 32798
            }, {
                'n': '原创',
                'v': 32799
            }]
        }],
        '6': [{
            'key': 'mode',
            'name': '综合排序',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '热播榜',
                'v': 11
            }, {
                'n': '好评榜',
                'v': 8
            }, {
                'n': '新上线',
                'v': 4
            }]
        }, {
            'key': 'year',
            'name': '全部年份',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '2023',
                'v': 2023
            }, {
                'n': '2022',
                'v': 2022
            }, {
                'n': '2021',
                'v': 2021
            }, {
                'n': '2020',
                'v': 2020
            }, {
                'n': '2019',
                'v': 2019
            }, {
                'n': '2018',
                'v': 2018
            }, {
                'n': '2017',
                'v': 2017
            }, {
                'n': '2016-2011',
                'v': '2011_2016'
            }, {
                'n': '2010-2000',
                'v': '2000_2010'
            }, {
                'n': '90年代',
                'v': '1990_1999'
            }, {
                'n': '80年代',
                'v': '1980_1989'
            }, {
                'n': '更早',
                'v': '1964_1979'
            }]
        }, {
            'key': 'is_purchase',
            'name': '全部资费',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '免费',
                'v': 0
            }, {
                'n': '会员',
                'v': 1
            }, {
                'n': '付费',
                'v': 2
            }]
        }, {
            'key': 'three_category_id',
            'name': '地区',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '内地',
                'v': 151
            }, {
                'n': '港台',
                'v': 152
            }, {
                'n': '韩国',
                'v': 33306
            }, {
                'n': '欧美',
                'v': 154
            }, {
                'n': '其它',
                'v': 1113
            }]
        }, {
            'key': '18014',
            'name': '类型',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '播报',
                'v': 155
            }, {
                'n': '访谈',
                'v': 156
            }, {
                'n': '游戏',
                'v': 158
            }, {
                'n': '晚会',
                'v': 292
            }, {
                'n': '曲艺',
                'v': 293
            }, {
                'n': '脱口秀',
                'v': 2118
            }, {
                'n': '真人秀',
                'v': 2224
            }, {
                'n': '竞技',
                'v': 30278
            }, {
                'n': '爱奇艺出品',
                'v': 30279
            }, {
                'n': '竞演',
                'v': 33860
            }]
        }, {
            'key': '33162',
            'name': '题材',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '音乐',
                'v': 33163
            }, {
                'n': '舞蹈',
                'v': 33172
            }, {
                'n': '文化',
                'v': 33173
            }, {
                'n': '美食',
                'v': 33182
            }, {
                'n': '伦理',
                'v': 33184
            }, {
                'n': '相亲',
                'v': 33193
            }, {
                'n': '纪实',
                'v': 33195
            }, {
                'n': '生活',
                'v': 33196
            }, {
                'n': '亲子',
                'v': 33197
            }, {
                'n': '少儿',
                'v': 33198
            }, {
                'n': '财经',
                'v': 33199
            }, {
                'n': '健康',
                'v': 33200
            }, {
                'n': '时尚',
                'v': 33203
            }, {
                'n': '旅游',
                'v': 33205
            }, {
                'n': '教育',
                'v': 33206
            }, {
                'n': '其它',
                'v': 33220
            }, {
                'n': '爱情',
                'v': 33316
            }, {
                'n': '搞笑',
                'v': 33317
            }, {
                'n': '益智',
                'v': 33318
            }, {
                'n': '职场',
                'v': 33319
            }, {
                'n': '语言',
                'v': 33530
            }, {
                'n': '辩论',
                'v': 33531
            }, {
                'n': '演讲',
                'v': 33843
            }]
        }],
        '5': [{
            'key': 'mode',
            'name': '综合排序',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '热播榜',
                'v': 11
            }, {
                'n': '好评榜',
                'v': 8
            }, {
                'n': '新上线',
                'v': 4
            }]
        }, {
            'key': 'year',
            'name': '全部年份',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '2023',
                'v': 2023
            }, {
                'n': '2022',
                'v': 2022
            }, {
                'n': '2021',
                'v': 2021
            }, {
                'n': '2020',
                'v': 2020
            }, {
                'n': '2019',
                'v': 2019
            }, {
                'n': '2018',
                'v': 2018
            }, {
                'n': '2017',
                'v': 2017
            }, {
                'n': '2016-2011',
                'v': '2011_2016'
            }, {
                'n': '2010-2000',
                'v': '2000_2010'
            }, {
                'n': '90年代',
                'v': '1990_1999'
            }, {
                'n': '80年代',
                'v': '1980_1989'
            }, {
                'n': '更早',
                'v': '1964_1979'
            }]
        }, {
            'key': 'is_purchase',
            'name': '全部资费',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '免费',
                'v': 0
            }, {
                'n': '会员',
                'v': 1
            }, {
                'n': '付费',
                'v': 2
            }]
        }, {
            'key': 'three_category_id',
            'name': '地区',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '内地',
                'v': 221
            }, {
                'n': '港台',
                'v': 220
            }, {
                'n': '欧美',
                'v': 219
            }, {
                'n': '日韩',
                'v': 218
            }, {
                'n': '拉美',
                'v': 217
            }, {
                'n': '亚洲地区',
                'v': 216
            }, {
                'n': '非洲',
                'v': 215
            }]
        }, {
            'key': '18009',
            'name': '语种',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '普通话',
                'v': 91
            }, {
                'n': '粤语',
                'v': 92
            }, {
                'n': '闽南语',
                'v': 93
            }, {
                'n': '英语',
                'v': 94
            }, {
                'n': '日语',
                'v': 95
            }, {
                'n': '韩语',
                'v': 96
            }, {
                'n': '其它',
                'v': 97
            }]
        }, {
            'key': '23554',
            'name': '音乐类型',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '单曲MV',
                'v': 23556
            }, {
                'n': '单曲现场',
                'v': 23557
            }, {
                'n': '演唱会',
                'v': 23558
            }, {
                'n': '新闻',
                'v': 23559
            }, {
                'n': '访谈',
                'v': 23560
            }, {
                'n': '音乐记录',
                'v': 23561
            }, {
                'n': '音乐周边',
                'v': 23562
            }, {
                'n': '音乐节目',
                'v': 23563
            }, {
                'n': '影视原声',
                'v': 23564
            }, {
                'n': '音乐短片',
                'v': 23565
            }, {
                'n': '其他',
                'v': 23566
            }]
        }],
        '16': [{
            'key': 'mode',
            'name': '综合排序',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '热播榜',
                'v': 11
            }, {
                'n': '好评榜',
                'v': 8
            }, {
                'n': '新上线',
                'v': 4
            }]
        }, {
            'key': 'year',
            'name': '全部年份',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '2023',
                'v': 2023
            }, {
                'n': '2022',
                'v': 2022
            }, {
                'n': '2021',
                'v': 2021
            }, {
                'n': '2020',
                'v': 2020
            }, {
                'n': '2019',
                'v': 2019
            }, {
                'n': '2018',
                'v': 2018
            }, {
                'n': '2017',
                'v': 2017
            }, {
                'n': '2016-2011',
                'v': '2011_2016'
            }, {
                'n': '2010-2000',
                'v': '2000_2010'
            }, {
                'n': '90年代',
                'v': '1990_1999'
            }, {
                'n': '80年代',
                'v': '1980_1989'
            }, {
                'n': '更早',
                'v': '1964_1979'
            }]
        }, {
            'key': 'is_purchase',
            'name': '全部资费',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '免费',
                'v': 0
            }, {
                'n': '会员',
                'v': 1
            }, {
                'n': '付费',
                'v': 2
            }]
        }, {
            'key': '18061',
            'name': '类型',
            'value': [{
                'n': '全部',
                'v': ''
            }, {
                'n': '喜剧',
                'v': 1296
            }, {
                'n': '爱情',
                'v': 1297
            }, {
                'n': '动作',
                'v': 1298
            }, {
                'n': '奇幻',
                'v': 1299
            }, {
                'n': '惊悚',
                'v': 1300
            }, {
                'n': '悬疑',
                'v': 1301
            }, {
                'n': '青春',
                'v': 1302
            }]
        }]
    },
    // url:'https://pcw-api.iqiyi.com/search/recommend/list?channel_id=fyclass&data_type=1&is_purchase=&mode=24&page_id=fypage&ret_num=48&three_category_id=',
    url: 'https://pcw-api.iqiyi.com/search/recommend/list?channel_id=fyclass&data_type=1&page_id=fypage&ret_num=48',
    filter_url: 'is_purchase={{fl.is_purchase}}&mode={{fl.mode}}&three_category_id={{fl.three_category_id}}&market_release_date_level={{fl.year}}',
    // url:'https://pcw-api.iqiyi.com/search/video/videolists?channel_id=fyclass&pageNum=fypage&pageSize=24&data_type=1&site=iqiyi',
    headers: {
        'User-Agent': 'MOBILE_UA'
    },
    timeout: 5000,
    class_name: '电影&电视剧&纪录片&动漫&综艺&音乐&网络电影',
    class_url: '1&2&3&4&6&5&16',
    limit: 20,
    play_parse: true,
    lazy: $js.toString(() => {
        try {
            let api = "" + input.split("?")[0];
            console.log(api);
            let response = fetch(api, {
                method: 'get',
                headers: {
                    'User-Agent': 'okhttp/3.14.9',
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            });
            let bata = JSON.parse(response);
            if (bata.url.includes("qiyi")) {
                input = {
                    parse: 0,
                    url: bata.url,
                    jx: 0,
                    danmaku: "http://127.0.0.1:9978/proxy?do=danmu&site=js&url=http://dm.qxq6.com/zy/api.php?url=" + input.split("?")[0]
                };
            } else {
                input = {
                    parse: 0,
                    url: input.split("?")[0],
                    jx: 1,
                    danmaku: "http://127.0.0.1:9978/proxy?do=danmu&site=js&url=http://dm.qxq6.com/zy/api.php?url=" + input.split("?")[0]
                };
            }
        } catch {
            input = {
                parse: 0,
                url: input.split("?")[0],
                jx: 1,
                danmaku: "http://127.0.0.1:9978/proxy?do=danmu&site=js&url=http://dm.qxq6.com/zy/api.php?url=" + input.split("?")[0]
            };
        }
    }),
    // play_parse:true,
    // 手动调用解析请求json的url,此lazy不方便
    // lazy:'js:input="https://cache.json.icu/home/api?type=ys&uid=292796&key=fnoryABDEFJNPQV269&url="+input.split("?")[0];log(input);let html=JSON.parse(request(input));log(html);input=html.url||input',
    // 推荐:'.list_item;img&&alt;img&&src;a&&Text;a&&data-float',
    // 一级:'json:.data.list;.name;.imageUrl;.latestOrder;.albumId',
    推荐: '',
    // 推荐:'js:let d=[];fetch_params.headers["user-agent"]=PC_UA;pdfh=jsp.pdfh;pdfa=jsp.pdfa;pd=jsp.pd;let html=fetch(HOST,fetch_params);let lists=pdfa(html,".qy-mod-li");lists.forEach(function(it){try{let title=pdfh(it,"p.sub&&title");let desc=pdfh(it,".qy-mod-label&&Text");let pic_url=pd(it,"img&&src");d.push({title:title,desc:desc,img:pic_url})}catch(e){}});res=setResult(d);',
    一级: 'js:let d=[];if(MY_CATE==="16"){input=input.replace("channel_id=16","channel_id=1").split("three_category_id")[0];input+="three_category_id=27401"}else if(MY_CATE==="5"){input=input.replace("data_type=1","data_type=2")}let html=request(input);let json=JSON.parse(html);if(json.code==="A00003"){fetch_params.headers["user-agent"]=PC_UA;json=JSON.parse(fetch(input,fetch_params))}json.data.list.forEach(function(data){if(data.channelId===1){desc=data.hasOwnProperty("score")?data.score+"分\\t":""}else if(data.channelId===2||data.channelId===4){if(data.latestOrder===data.videoCount){desc=(data.hasOwnProperty("score")?data.score+"分\\t":"")+data.latestOrder+"集全"}else{if(data.videoCount){desc=(data.hasOwnProperty("score")?data.score+"分\\t":"")+data.latestOrder+"/"+data.videoCount+"集"}else{desc="更新至 "+data.latestOrder+"集"}}}else if(data.channelId===6){desc=data.period+"期"}else if(data.channelId===5){desc=data.focus}else{if(data.latestOrder){desc="更新至 第"+data.latestOrder+"期"}else if(data.period){desc=data.period}else{desc=data.focus}}url=MY_CATE+"$"+data.albumId;d.push({url:url,title:data.name,desc:desc,pic_url:data.imageUrl.replace(".jpg","_390_520.jpg?caplist=jpg,webp")})});setResult(d);',
    // 一级:'js:let d=[];if(MY_CATE==="16"){input=input.replace("channel_id=16","channel_id=1").split("three_category_id")[0];input+="three_category_id=27401"}else if(MY_CATE==="5"){input=input.replace("data_type=1","data_type=2")}let html=fetch(input,fetch_params);let json=JSON.parse(html);if(json.code==="A00003"){fetch_params.headers["user-agent"]=PC_UA;json=JSON.parse(fetch(input,fetch_params))}json.data.list.forEach(function(data){if(data.channelId===1){desc=data.hasOwnProperty("score")?data.score+"分\\t":""}else if(data.channelId===2||data.channelId===4){if(data.latestOrder===data.videoCount){desc=(data.hasOwnProperty("score")?data.score+"分\\t":"")+data.latestOrder+"集全"}else{if(data.videoCount){desc=(data.hasOwnProperty("score")?data.score+"分\\t":"")+data.latestOrder+"/"+data.videoCount+"集"}else{desc="更新至 "+data.latestOrder+"集"}}}else if(data.channelId===6){desc=data.period+"期"}else if(data.channelId===5){desc=data.focus}else{if(data.latestOrder){desc="更新至 第"+data.latestOrder+"期"}else if(data.period){desc=data.period}else{desc=data.focus}}url=MY_CATE+"$"+data.albumId;d.push({url:url,title:data.name,desc:desc,pic_url:data.imageUrl.replace(".jpg","_390_520.jpg?caplist=jpg,webp")})});setResult(d);',
    // 一级:'json:.data.list;.name;.imageUrl;.playUrl;.latestOrder',
    // 二级:{is_json:1,"title":"data.title;data.moviecategory[0]+data.moviecategory[1]","img":"data.cdncover","desc":"data.area[0];data.director[0]","content":"data.description","tabs":"data.playlink_sites;data.playlinksdetail.#idv.quality","lists":"data.playlinksdetail.#idv.default_url"},
    // 二级:{is_json:1,"title":"data.name+data.subtitle;data.latestOrder","img":"data.imageUrl","desc":"data.categories;data.areas","content":"data.description","tabs":"data.name","lists":"data.playlinksdetail.#idv.default_url"},
    二级: '',
    二级: 'js:let d=[];let html=request(input);let json=JSON.parse(html).data;VOD={vod_id:"",vod_url:input,vod_name:"",type_name:"",vod_actor:"",vod_year:"",vod_director:"",vod_area:"",vod_content:"",vod_remarks:"",vod_pic:""};VOD.vod_name=json.name;try{if(json.latestOrder){VOD.vod_remarks="类型: "+(json.categories[0].name||"")+"\\t"+(json.categories[1].name||"")+"\\t"+(json.categories[2].name||"")+"\\t"+"评分："+(json.score||"")+"\\n更新至：第"+json.latestOrder+"集(期)/共"+json.videoCount+"集(期)"}else{VOD.vod_remarks="类型: "+(json.categories[0].name||"")+"\\t"+(json.categories[1].name||"")+"\\t"+(json.categories[2].name||"")+"\\t"+"评分："+(json.score||"")+json.period}}catch(e){VOD.vod_remarks=json.subtitle}VOD.vod_area=(json.focus||"")+"\\n资费："+(json.payMark===1?"VIP":"免费")+"\\n地区："+(json.areas||"");let vsize="579_772";try{vsize=json.imageSize[12]}catch(e){}VOD.vod_pic=json.imageUrl.replace(".jpg","_"+vsize+".jpg?caplist=jpg,webp");VOD.type_name=json.categories.map(function(it){return it.name}).join(",");if(json.people.main_charactor){let vod_actors=[];json.people.main_charactor.forEach(function(it){vod_actors.push(it.name)});VOD.vod_actor=vod_actors.join(",")}VOD.vod_content=json.description;let playlists=[];if(json.channelId===1||json.channelId===5){playlists=[{playUrl:json.playUrl,imageUrl:json.imageUrl,shortTitle:json.shortTitle,focus:json.focus,period:json.period}]}else{if(json.channelId===6){let qs=json.period.split("-")[0];let listUrl="https://pcw-api.iqiyi.com/album/source/svlistinfo?cid=6&sourceid="+json.albumId+"&timelist="+qs;let playData=JSON.parse(request(listUrl)).data[qs];playData.forEach(function(it){playlists.push({playUrl:it.playUrl,imageUrl:it.imageUrl,shortTitle:it.shortTitle,focus:it.focus,period:it.period})})}else{let listUrl="https://pcw-api.iqiyi.com/albums/album/avlistinfo?aid="+json.albumId+"&size=200&page=1";let data=JSON.parse(request(listUrl)).data;let total=data.total;playlists=data.epsodelist;if(total>200){for(let i=2;i<total/200+1;i++){let listUrl="https://pcw-api.iqiyi.com/albums/album/avlistinfo?aid="+json.albumId+"&size=200&page="+i;let data=JSON.parse(request(listUrl)).data;playlists=playlists.concat(data.epsodelist)}}}}playlists.forEach(function(it){d.push({title:it.shortTitle||"第"+it.order+"集",desc:it.subtitle||it.focus||it.period,img:it.imageUrl.replace(".jpg","_480_270.jpg?caplist=jpg,webp"),url:it.playUrl})});VOD.vod_play_from="qiyi";VOD.vod_play_url=d.map(function(it){return it.title+"$"+it.url}).join("#");',
    // 二级:'js:let d=[];let html=request(input);let json=JSON.parse(html).data;vod={vod_id:"",vod_url:input,vod_name:"",type_name:"",vod_actor:"",vod_year:"",vod_director:"",vod_area:"",vod_content:"",vod_remarks:"",vod_pic:""};vod.vod_name=json.name;try{if(json.latestOrder){vod.vod_remarks="类型: "+(json.categories[0].name||"")+"\\t"+(json.categories[1].name||"")+"\\t"+(json.categories[2].name||"")+"\\t"+"评分："+(json.score||"")+"\\n更新至：第"+json.latestOrder+"集(期)/共"+json.videoCount+"集(期)"}else{vod.vod_remarks="类型: "+(json.categories[0].name||"")+"\\t"+(json.categories[1].name||"")+"\\t"+(json.categories[2].name||"")+"\\t"+"评分："+(json.score||"")+json.period}}catch(e){vod.vod_remarks=json.subtitle}vod.vod_area=(json.focus||"")+"\\n资费："+(json.payMark===1?"VIP":"免费")+"\\n地区："+(json.areas||"");let vsize="579_772";try{vsize=json.imageSize[12]}catch(e){}vod.vod_pic=json.imageUrl.replace(".jpg","_"+vsize+".jpg?caplist=jpg,webp");vod.type_name=json.categories.map(function(it){return it.name}).join(",");if(json.people.main_charactor){vod_actors=[];json.people.main_charactor.forEach(function(it){vod_actors.push(it.name)});vod.vod_actor=vod_actors.join(",")}vod.vod_content=json.description;let playlists=[];if(json.channelId===1||json.channelId===5){playlists=[{playUrl:json.playUrl,imageUrl:json.imageUrl,shortTitle:json.shortTitle,focus:json.focus,period:json.period}]}else{if(json.channelId===6){let qs=json.period.split("-")[0];let listUrl="https://pcw-api.iqiyi.com/album/source/svlistinfo?cid=6&sourceid="+json.albumId+"&timelist="+qs;let playData=JSON.parse(request(listUrl)).data[qs];playData.forEach(function(it){playlists.push({playUrl:it.playUrl,imageUrl:it.imageUrl,shortTitle:it.shortTitle,focus:it.focus,period:it.period})})}else{let listUrl="https://pcw-api.iqiyi.com/albums/album/avlistinfo?aid="+json.albumId+"&size=200&page=1";let data=JSON.parse(request(listUrl)).data;let total=data.total;playlists=data.epsodelist;if(total>200){for(let i=2;i<total/200+1;i++){let listUrl="https://pcw-api.iqiyi.com/albums/album/avlistinfo?aid="+json.albumId+"&size=200&page="+i;let data=JSON.parse(request(listUrl)).data;playlists=playlists.concat(data.epsodelist)}}}}playlists.forEach(function(it){d.push({title:it.shortTitle||"第"+it.order+"集",desc:it.subtitle||it.focus||it.period,img:it.imageUrl.replace(".jpg","_480_270.jpg?caplist=jpg,webp"),url:it.playUrl})});vod.vod_play_from="qiyi";vod.vod_play_url=d.map(function(it){return it.title+"$"+it.url}).join("#");',
    搜索: 'json:.data.docinfos;.albumDocInfo.albumTitle;.albumDocInfo.albumVImage;.albumDocInfo.channel;.albumDocInfo.albumId;.albumDocInfo.tvFocus',
}