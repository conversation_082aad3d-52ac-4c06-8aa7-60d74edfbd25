{"version": 3, "file": "roving-focus-group.mjs", "sources": ["../../../../../../packages/components/roving-focus-group/src/roving-focus-group.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { createCollectionWithScope } from '@element-plus/components/collection'\n\nimport type {\n  ExtractPropTypes,\n  HTMLAttributes,\n  StyleValue,\n  __ExtractPublicPropTypes,\n} from 'vue'\n\nexport const rovingFocusGroupProps = buildProps({\n  style: { type: definePropType<StyleValue>([String, Array, Object]) },\n  currentTabId: {\n    type: definePropType<string | null>(String),\n  },\n  defaultCurrentTabId: String,\n  loop: Boolean,\n  dir: {\n    type: String, // left for direction support\n    values: ['ltr', 'rtl'],\n    default: 'ltr',\n  },\n  orientation: {\n    // left for orientation support\n    type: definePropType<HTMLAttributes['aria-orientation']>(String),\n  },\n\n  onBlur: Function,\n  onFocus: Function,\n  onMousedown: Function,\n})\n\nexport type ElRovingFocusGroupProps = ExtractPropTypes<\n  typeof rovingFocusGroupProps\n>\n\nexport type ElRovingFocusGroupPropsPublic = __ExtractPublicPropTypes<\n  typeof rovingFocusGroupProps\n>\n\nconst {\n  ElCollection,\n  ElCollectionItem,\n  COLLECTION_INJECTION_KEY,\n  COLLECTION_ITEM_INJECTION_KEY,\n} = createCollectionWithScope('RovingFocusGroup')\n\nexport {\n  ElCollection,\n  ElCollectionItem,\n  COLLECTION_INJECTION_KEY as ROVING_FOCUS_COLLECTION_INJECTION_KEY,\n  COLLECTION_ITEM_INJECTION_KEY as ROVING_FOCUS_ITEM_COLLECTION_INJECTION_KEY,\n}\n"], "names": [], "mappings": ";;;AAEY,MAAC,qBAAqB,GAAG,UAAU,CAAC;AAChD,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,EAAE;AAC1D,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,EAAE,mBAAmB,EAAE,MAAM;AAC7B,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,GAAG,EAAE;AACP,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;AAC1B,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,EAAE,MAAM,EAAE,QAAQ;AAClB,EAAE,OAAO,EAAE,QAAQ;AACnB,EAAE,WAAW,EAAE,QAAQ;AACvB,CAAC,EAAE;AACE,MAAC;AACN,EAAE,YAAY;AACd,EAAE,gBAAgB;AAClB,EAAE,wBAAwB;AAC1B,EAAE,6BAA6B;AAC/B,CAAC,GAAG,yBAAyB,CAAC,kBAAkB;;;;"}