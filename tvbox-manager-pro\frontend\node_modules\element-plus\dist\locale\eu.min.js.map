{"version": 3, "file": "eu.min.js", "sources": ["../../../../packages/locale/lang/eu.ts"], "sourcesContent": ["export default {\n  name: 'eu',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: '<PERSON><PERSON>',\n      clear: 'Garbitu',\n    },\n    datepicker: {\n      now: '<PERSON><PERSON>',\n      today: '<PERSON><PERSON>r',\n      cancel: 'Utzi',\n      clear: 'Garbitu',\n      confirm: '<PERSON><PERSON>',\n      selectDate: 'Hautatu data',\n      selectTime: 'Hautatu ordua',\n      startDate: 'Hasierako data',\n      startTime: 'Hasierako ordua',\n      endDate: 'Amaierako data',\n      endTime: 'Amaierako ordua',\n      prevYear: 'Aurreko urtea',\n      nextYear: 'Hurrengo urtea',\n      prevMonth: 'Aurreko hilabetea',\n      nextMonth: 'Hurrengo hilabetea',\n      year: '',\n      month1: 'Urtarrila',\n      month2: 'Otsaila',\n      month3: 'Martxoa',\n      month4: 'Apirila',\n      month5: 'Maiatza',\n      month6: 'Ekaina',\n      month7: 'Uztaila',\n      month8: 'Abuztua',\n      month9: '<PERSON><PERSON>',\n      month10: 'Urria',\n      month11: 'Azaroa',\n      month12: 'Abendua',\n      // week: 'astea',\n      weeks: {\n        sun: 'ig.',\n        mon: 'al.',\n        tue: 'ar.',\n        wed: 'az.',\n        thu: 'og.',\n        fri: 'ol.',\n        sat: 'lr.',\n      },\n      months: {\n        jan: 'urt',\n        feb: 'ots',\n        mar: 'mar',\n        apr: 'api',\n        may: 'mai',\n        jun: 'eka',\n        jul: 'uzt',\n        aug: 'abu',\n        sep: 'ira',\n        oct: 'urr',\n        nov: 'aza',\n        dec: 'abe',\n      },\n    },\n    select: {\n      loading: 'Kargatzen',\n      noMatch: 'Bat datorren daturik ez',\n      noData: 'Daturik ez',\n      placeholder: 'Hautatu',\n    },\n    mention: {\n      loading: 'Kargatzen',\n    },\n    cascader: {\n      noMatch: 'Bat datorren daturik ez',\n      loading: 'Kargatzen',\n      placeholder: 'Hautatu',\n      noData: 'Daturik ez',\n    },\n    pagination: {\n      goto: 'Joan',\n      pagesize: '/orria',\n      total: 'Guztira {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Mezua',\n      confirm: 'Ados',\n      cancel: 'Utzi',\n      error: 'Sarrera baliogabea',\n    },\n    upload: {\n      deleteTip: 'sakatu Ezabatu kentzeko',\n      delete: 'Ezabatu',\n      preview: 'Aurrebista',\n      continue: 'Jarraitu',\n    },\n    table: {\n      emptyText: 'Daturik ez',\n      confirmFilter: 'Baieztatu',\n      resetFilter: 'Berrezarri',\n      clearFilter: 'Guztia',\n      sumText: 'Batura',\n    },\n    tour: {\n      next: 'Hurrengoa',\n      previous: 'Aurrekoa',\n      finish: 'Bukatu',\n    },\n    tree: {\n      emptyText: 'Daturik ez',\n    },\n    transfer: {\n      noMatch: 'Bat datorren daturik ez',\n      noData: 'Daturik ez',\n      titles: ['Zerrenda 1', 'Zerrenda 2'], // to be translated\n      filterPlaceholder: 'Sartu gako-hitza', // to be translated\n      noCheckedFormat: '{total} elementu', // to be translated\n      hasCheckedFormat: '{checked}/{total} hautatuta', // to be translated\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,WAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,eAAe,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,iBAAiB,CAAC,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC,gBAAgB,CAAC,SAAS,CAAC,mBAAmB,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,yBAAyB,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,yBAAyB,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,iBAAiB,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,aAAa,CAAC,WAAW,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,yBAAyB,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,eAAe,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,6BAA6B,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;;;;;"}