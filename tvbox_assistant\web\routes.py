#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
TVBox助手Web应用路由
处理Web页面路由和模板渲染
"""

import os
import logging
from flask import (
    Blueprint, flash, redirect, render_template, 
    request, url_for, current_app, jsonify, send_from_directory
)
from werkzeug.utils import secure_filename

# 创建日志记录器
logger = logging.getLogger('tvbox_routes')

# 创建蓝图
bp = Blueprint('routes', __name__)

def allowed_file(filename):
    """检查文件是否允许上传"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']

@bp.route('/')
def index():
    """首页"""
    logger.debug("访问首页")
    return render_template('index.html')

@bp.route('/configs')
def configs():
    """配置管理页面"""
    logger.debug("访问配置管理页面")
    return render_template('configs.html')

@bp.route('/decrypt')
def decrypt():
    """接口解密页面"""
    logger.debug("访问接口解密页面")
    return render_template('decrypt.html')

@bp.route('/resources')
def resources():
    """资源管理页面"""
    logger.debug("访问资源管理页面")
    return render_template('resources.html')

@bp.route('/upload', methods=['POST'])
def upload_file():
    """处理文件上传"""
    logger.debug("处理文件上传请求")
    if 'file' not in request.files:
        flash('未找到文件', 'danger')
        return redirect(request.url)
    
    file = request.files['file']
    if file.filename == '':
        flash('未选择文件', 'danger')
        return redirect(request.url)
    
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)
        file.save(file_path)
        
        logger.info(f"文件上传成功: {filename}")
        flash(f'文件 {filename} 上传成功!', 'success')
        return redirect(url_for('routes.configs', filename=filename))
    
    flash('不支持的文件类型', 'danger')
    return redirect(request.url)

@bp.route('/uploads/<filename>')
def uploaded_file(filename):
    """获取上传的文件"""
    return send_from_directory(current_app.config['UPLOAD_FOLDER'], filename) 