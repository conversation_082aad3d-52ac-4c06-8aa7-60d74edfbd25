#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
TVBox接口解密工具
支持各种加密方式的解密操作
完全基于 tvbox_assistant 的成功实现
"""

import json
import base64
import re
import traceback
import logging
import requests
import ipaddress
import time
import hashlib
from urllib.parse import urlparse, urljoin, parse_qs, unquote
from pathlib import Path
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
import binascii
from typing import Optional, Tuple, List, Dict, Any

class TVBoxDecryptor:
    """TVBox配置解密工具类，完全模拟TVBox客户端行为"""

    def __init__(self):
        """初始化解密器"""
        self.logger = logging.getLogger('app.core.tvbox_decryptor')
        # 定义JS路径模式，匹配相对路径JS
        self.js_uri_pattern = re.compile(r'"(\.|\.\.)/(.?|.+?)\.js\?(.?|.+?)"')
    
    def decrypt(self, content, hint_type=None):
        """
        尝试使用多种方法解密TVBox配置或地址
        完全对标TVBox客户端Decoder类实现

        Args:
            content: 配置内容或地址
            hint_type: 提示的类型

        Returns:
            tuple: (解密后的内容, 使用的解密方法)
        """
        self.logger.debug(f"尝试解密内容: {content[:100] if content else 'None'}...")

        # 处理空内容
        if not content or not content.strip():
            return content, "无内容"

        # 尝试进行URL编码解码
        if '%' in content:
            try:
                decoded = unquote(content)
                if decoded != content:
                    return decoded, "URL解码"
            except Exception as e:
                self.logger.debug(f"URL解码失败: {str(e)}")

        # 尝试JSON解析
        if content.strip().startswith('{') and self._is_json(content):
            return content, "JSON格式"

        # 尝试Base64解码 (对应TVBox中的base64方法)
        if "**" in content:
            try:
                extracted = self._extract_base64(content)
                if extracted:
                    decoded = base64.b64decode(extracted).decode('utf-8')
                    return decoded, "Base64解码"
            except Exception as e:
                self.logger.debug(f"Base64解码失败: {str(e)}")

        # 尝试AES/CBC解密 (对应TVBox中的cbc方法)
        if content.startswith("2423"):
            try:
                decoded = self._decrypt_cbc(content)
                return decoded, "AES/CBC解密"
            except Exception as e:
                self.logger.debug(f"AES/CBC解密失败: {str(e)}")

        # 特殊情况: clan://协议处理
        if content.startswith('clan://'):
            try:
                # clan://localhost/文件路径 转换为 http://127.0.0.1:9978/file/文件路径
                clan_path = content.replace('clan://', '')
                parts = clan_path.split('/', 1)
                if len(parts) == 2:
                    host, path = parts
                    if host.lower() == 'localhost':
                        decoded = f"http://127.0.0.1:9978/file/{path}"
                        return decoded, "clan协议转换"
            except Exception as e:
                self.logger.debug(f"clan协议转换失败: {str(e)}")

        # 特殊情况：字符串反转
        if content.strip() and not content.startswith(('http://', 'https://')):
            reversed_content = content[::-1]
            # 如果反转后是一个看起来像URL的字符串
            if reversed_content.startswith(('http://', 'https://')) or 'www.' in reversed_content:
                return reversed_content, "字符串反转"

        # 特殊情况：直接替换内容
        replacements = {
            'fongmi': 'https://raw.githubusercontent.com/FongMi/TV/release/json/config.json',
            'fm': 'https://raw.githubusercontent.com/FongMi/TV/release/json/config.json',
            'ali': 'http://饭太硬.top/tv',
            'xo': 'https://jihulab.com/clear1/tvbox/-/raw/main/0.json',
            'xiaoguazi': 'https://dxawi.github.io/0/0.json',
        }

        for key, value in replacements.items():
            if content.strip().lower() == key:
                return value, f"关键词替换[{key}]"

        # 默认返回原内容
        return content, "无需解密"

    def decrypt_config_url(self, url, config_type=None):
        """
        专门用于解密TVBox配置URL的方法
        完全模拟TVBox客户端行为，对任意URL进行解析获取配置
        对标VodConfig和LiveConfig的loadConfig方法

        Args:
            url: 配置URL
            config_type: 配置类型提示

        Returns:
            tuple: (配置内容, 解密方法)
        """
        self.logger.debug(f"尝试解析配置URL: {url}")

        try:
            # 1. 标准化URL
            normalized_url = self._normalize_url(url)

            # 2. 获取JSON内容 (对应TVBox的Decoder.getJson方法)
            content, final_url = self._get_json(normalized_url)

            # 3. 验证并处理内容 (对应TVBox的verify方法)
            verified_content = self._verify_content(final_url, content)

            # 4. 检查是否是有效的JSON (对应VodConfig的checkJson方法)
            if self._is_json(verified_content):
                json_data = json.loads(verified_content)

                # 5. 检查是否包含消息
                if isinstance(json_data, dict) and "msg" in json_data:
                    return json_data["msg"], "错误消息"

                # 6. 检查是否是仓库配置
                if isinstance(json_data, dict) and "urls" in json_data and isinstance(json_data["urls"], list):
                    # 解析仓库配置，获取第一个URL
                    try:
                        first_url = json_data["urls"][0].get("url", "")
                        if first_url:
                            return self.decrypt_config_url(first_url, config_type)
                    except Exception as e:
                        self.logger.error(f"解析仓库配置失败: {str(e)}")

                # 7. 返回处理后的JSON配置
                return json.dumps(json_data, ensure_ascii=False, indent=2), "配置解析"

            return verified_content, "直接内容"

        except Exception as e:
            self.logger.error(f"解析配置URL失败: {str(e)}")
            self.logger.debug(traceback.format_exc())

            # 8. 如果失败且内容非空，尝试常规解密
            if url:
                return self.decrypt(url, config_type)

            return url, "解析失败"

    def _get_json(self, url, tag="vod"):
        """
        获取JSON内容，对标TVBox的Decoder.getJson方法

        Args:
            url: 请求URL
            tag: 请求标签

        Returns:
            tuple: (内容, 最终URL)
        """
        # 设置TVBox客户端请求头
        headers = {
            'User-Agent': 'okhttp/3.12.11',
            'Connection': 'keep-alive',
            'Accept': '*/*',
            'Accept-Encoding': 'gzip, deflate'
        }

        # 不自动处理重定向，与TVBox一致
        response = requests.get(url, headers=headers, timeout=10, allow_redirects=False)

        # 处理重定向
        if response.status_code in (301, 302, 303, 307, 308):
            redirect_url = response.headers.get('Location')
            if redirect_url:
                self.logger.debug(f"请求被重定向到: {redirect_url}")
                # 如果重定向URL不是绝对URL，转换为绝对URL
                if not redirect_url.startswith(('http://', 'https://')):
                    redirect_url = urljoin(url, redirect_url)

                # 请求重定向URL
                return self._get_json(redirect_url, tag)

        # 获取内容
        if response.ok:
            # 使用相同的URL参数数量检查，与TVBox一致
            try:
                parsed = urlparse(url)
                request_url = response.request.url
                parsed_request = urlparse(request_url)

                query_size = len(parse_qs(parsed.query))
                request_query_size = len(parse_qs(parsed_request.query))

                if request_query_size == query_size:
                    url = request_url
            except Exception as e:
                self.logger.debug(f"URL参数检查失败: {str(e)}")

            return response.text, url

        # 请求失败，抛出异常
        response.raise_for_status()

    def _verify_content(self, url, data):
        """
        验证内容，对标TVBox的verify方法

        Args:
            url: 请求URL
            data: 响应内容

        Returns:
            str: 处理后的内容
        """
        if not data:
            raise Exception("响应内容为空")

        # 移除注释但保留URL结构
        data = self._remove_comments_preserve_url(data)

        # 如果已经是JSON对象，直接修正并返回
        if self._is_json(data):
            return self._fix_urls(url, data)

        # 检查是否包含Base64标记
        if "**" in data:
            data = self._extract_and_decode_base64(data)

        # 检查是否需要AES/CBC解密
        if data.startswith("2423"):
            data = self._decrypt_cbc(data)

        # 修正URL
        data = self._fix_urls(url, data)

        # 尝试从混合内容中提取JSON配置
        data = self.extract_json_from_mixed_content(data)

        return data

    def _remove_comments_preserve_url(self, content):
        """移除注释但保留URL结构"""
        # 分析内容的行
        lines = content.splitlines()
        cleaned_lines = []

        for line in lines:
            # 过滤掉整行注释 (以//开头的行，前面可能有空白)
            if re.match(r'^\s*//.*$', line):
                continue

            # 过滤掉行内注释，但保护URL中的 '//'
            # 先标记所有URL中的 '//' 为特殊标记
            line = re.sub(r'(https?:)//', r'\1URLSLASHSLASH', line)
            # 移除注释
            line = re.sub(r'//.*$', '', line)
            # 恢复URL
            line = re.sub(r'URLSLASHSLASH', '//', line)

            cleaned_lines.append(line)

        # 过滤掉多行注释，但保留格式
        content = '\n'.join(cleaned_lines)
        content = re.sub(r'/\*.*?\*/', '', content, flags=re.DOTALL)

        return content

    def _is_json(self, text):
        """
        检查文本是否是有效的JSON

        Args:
            text: 要检查的文本

        Returns:
            bool: 是否是JSON
        """
        try:
            if not isinstance(text, str):
                return False
            json.loads(text)
            return True
        except:
            return False

    def _extract_base64(self, data):
        """
        提取Base64编码部分，对标TVBox的extract方法

        Args:
            data: 包含Base64的数据

        Returns:
            str: 提取的Base64部分
        """
        # 匹配Base64特征
        matcher = re.compile(r"[A-Za-z0-9]{8}\*\*").search(data)
        if matcher:
            return data[data.index(matcher.group()) + 10:]
        return ""

    def _extract_and_decode_base64(self, data):
        """
        提取并解码Base64内容，对标TVBox的base64方法

        Args:
            data: 包含Base64的数据

        Returns:
            str: 解码后的内容
        """
        extracted = self._extract_base64(data)
        if not extracted:
            return data
        return base64.b64decode(extracted).decode('utf-8')

    def _decrypt_cbc(self, data):
        """
        AES/CBC解密，对标TVBox的cbc方法

        Args:
            data: 要解密的数据

        Returns:
            str: 解密后的内容
        """
        # 将16进制字符串转换为字节
        hex_bytes = binascii.unhexlify(data)
        decode = hex_bytes.decode('latin1').lower()

        # 提取密钥和IV
        key = self._pad_end(decode[decode.index("$#") + 2:decode.index("#$")])
        iv = self._pad_end(decode[-13:])

        # 准备解密
        cipher = AES.new(key.encode(), AES.MODE_CBC, iv.encode())

        # 提取加密数据
        encrypted_data = data[data.index("2324") + 4:len(data) - 26]
        encrypted_bytes = binascii.unhexlify(encrypted_data)

        # 解密
        decrypted_bytes = cipher.decrypt(encrypted_bytes)

        # 去除填充并返回
        try:
            return unpad(decrypted_bytes, 16).decode('utf-8')
        except:
            # 如果去除填充失败，直接返回解密结果
            return decrypted_bytes.decode('utf-8', errors='ignore')

    def _pad_end(self, key):
        """
        填充字符串到16字节，对标TVBox的padEnd方法

        Args:
            key: 要填充的字符串

        Returns:
            str: 填充后的字符串
        """
        return key + "0000000000000000"[len(key):]

    def _normalize_url(self, url):
        """
        标准化URL，处理中文域名、协议前缀等

        Args:
            url: 原始URL

        Returns:
            str: 标准化后的URL
        """
        # 添加协议前缀
        if not url.startswith(('http://', 'https://')):
            if url.startswith('//'):
                url = 'https:' + url
            else:
                url = 'https://' + url

        # 标准化域名中的中文
        try:
            parsed = urlparse(url)
            if any(ord(c) > 127 for c in parsed.netloc):
                # 使用Punycode转换中文域名
                ascii_domain = parsed.netloc.encode('idna').decode('ascii')
                url = url.replace(parsed.netloc, ascii_domain)
        except Exception as e:
            self.logger.debug(f"标准化域名失败: {str(e)}")

        return url

    def _fix_urls(self, url, data):
        """
        修正相对URL，对标TVBox的fix方法

        Args:
            url: 基础URL
            data: 要修正的内容

        Returns:
            str: 修正后的内容
        """
        # 替换JS URI
        for match in self.js_uri_pattern.finditer(data):
            data = self._replace_js_uri(url, data, match.group())

        # 替换相对路径
        base_url = self._get_base_url(url)
        if "../" in data:
            data = data.replace("../", urljoin(base_url, "../"))
        if "./" in data:
            data = data.replace("./", urljoin(base_url, "./"))
        if "__JS1__" in data:
            data = data.replace("__JS1__", "./")
        if "__JS2__" in data:
            data = data.replace("__JS2__", "../")

        return data

    def _replace_js_uri(self, url, data, ext):
        """
        替换JS URI，对标TVBox的replace方法

        Args:
            url: 基础URL
            data: 要修正的内容
            ext: 要替换的JS URI

        Returns:
            str: 修正后的内容
        """
        base_url = self._get_base_url(url)
        t = ext.replace("\"./", "\"" + urljoin(base_url, "./"))
        t = t.replace("\"../", "\"" + urljoin(base_url, "../"))
        t = t.replace("./", "__JS1__").replace("../", "__JS2__")
        return data.replace(ext, t)

    def _get_base_url(self, url):
        """
        获取基础URL

        Args:
            url: 完整URL

        Returns:
            str: 基础URL
        """
        parsed = urlparse(url)
        path_parts = parsed.path.split("/")
        if len(path_parts) > 1:
            path = "/".join(path_parts[:-1]) + "/"
        else:
            path = "/"
        return f"{parsed.scheme}://{parsed.netloc}{path}"

    def validate_tvbox_config(self, content):
        """
        验证配置内容是否是有效的TVBox配置

        Args:
            content: 配置内容

        Returns:
            bool: 是否是有效的TVBox配置
        """
        try:
            # 尝试解析JSON
            if not content or not isinstance(content, str):
                return False

            json_data = json.loads(content)

            # 必须包含sites字段
            if 'sites' not in json_data and ('video' not in json_data or 'sites' not in json_data.get('video', {})):
                return False

            # 检查sites字段是否是数组
            sites = json_data.get('sites', json_data.get('video', {}).get('sites', []))
            if not isinstance(sites, list) or len(sites) == 0:
                return False

            # 检查至少一个站点是否包含基本的必要字段
            valid_sites = 0
            for site in sites:
                if isinstance(site, dict) and 'key' in site and 'name' in site and 'api' in site:
                    valid_sites += 1

            if valid_sites == 0:
                return False

            # 检查可选但常见的字段格式是否正确
            if 'lives' in json_data and not isinstance(json_data['lives'], list):
                return False

            if 'parses' in json_data and not isinstance(json_data['parses'], list):
                return False

            # 通过所有检查，认为是有效的TVBox配置
            self.logger.info(f"验证通过: 找到{valid_sites}个有效站点")
            return True

        except Exception as e:
            self.logger.debug(f"配置验证失败: {str(e)}")
            return False

    def parse_tvbox_config_content(self, content):
        """
        解析TVBox配置内容，提取关键信息

        Args:
            content: JSON配置内容

        Returns:
            dict: 配置信息对象
        """
        try:
            # 解析JSON
            if isinstance(content, str):
                json_data = json.loads(content)
            else:
                json_data = content

            # 提取基本信息
            result = {
                'spider': json_data.get('spider', ''),
                'wallpaper': json_data.get('wallpaper', ''),
                'logo': json_data.get('logo', ''),
                'sites': [],
                'lives': [],
                'parses': []
            }

            # 提取站点信息
            sites = json_data.get('sites', json_data.get('video', {}).get('sites', []))
            for site in sites:
                if isinstance(site, dict):
                    site_info = {
                        'key': site.get('key', ''),
                        'name': site.get('name', ''),
                        'type': site.get('type', 0),
                        'api': site.get('api', ''),
                        'searchable': site.get('searchable', 1),
                        'quickSearch': site.get('quickSearch', 1),
                        'filterable': site.get('filterable', 1),
                        'hide': site.get('hide', 0),
                        'jar': site.get('jar', '')
                    }
                    result['sites'].append(site_info)

            # 提取解析器信息
            parses = json_data.get('parses', [])
            for parse in parses:
                if isinstance(parse, dict):
                    parse_info = {
                        'name': parse.get('name', ''),
                        'url': parse.get('url', ''),
                        'type': parse.get('type', 0)
                    }
                    result['parses'].append(parse_info)

            # 提取直播分组信息
            lives = json_data.get('lives', [])
            if lives and len(lives) > 0:
                # 检查第一个元素是否包含特殊处理字段
                if 'proxy' in str(lives[0]):
                    result['lives'].append({
                        'group': '默认分组',
                        'channels': [],
                        'proxy': True
                    })
                else:
                    # 常规直播源处理
                    for live in lives:
                        if isinstance(live, dict) and 'group' in live:
                            live_info = {
                                'group': live.get('group', ''),
                                'channels': []
                            }

                            # 处理频道
                            channels = live.get('channels', [])
                            for channel in channels:
                                if isinstance(channel, dict):
                                    channel_info = {
                                        'name': channel.get('name', ''),
                                        'urls': channel.get('urls', [])
                                    }
                                    live_info['channels'].append(channel_info)

                            result['lives'].append(live_info)

            return result

        except Exception as e:
            self.logger.error(f"解析配置内容失败: {str(e)}")
            self.logger.debug(traceback.format_exc())
            return {
                'spider': '',
                'wallpaper': '',
                'logo': '',
                'sites': [],
                'lives': [],
                'parses': []
            }

    def generate_config_checksum(self, content):
        """
        生成配置内容的校验和

        Args:
            content: 配置内容

        Returns:
            str: MD5校验和
        """
        if not content:
            return ""
        return hashlib.md5(content.encode('utf-8')).hexdigest()
        """检查是否是有效的JSON"""
        if not content or not content.strip():
            return False

        content = content.strip()
        if not (content.startswith('{') or content.startswith('[')):
            return False

        try:
            json.loads(content)
            return True
        except:
            return False

    def auto_decrypt(self, content: str) -> Tuple[Optional[str], Optional[str]]:
        """自动检测解密方法"""
        # 去除首尾空白
        content = content.strip()
        
        # 1. 尝试直接解析JSON
        if self._is_valid_json(content):
            return content, "json"
        
        # 2. 尝试URL解码
        try:
            url_decoded = unquote(content)
            if url_decoded != content and self._is_valid_json(url_decoded):
                return url_decoded, "url_decode"
        except:
            pass
        
        # 3. 尝试Base64解码
        try:
            base64_decoded = base64.b64decode(content).decode('utf-8')
            if self._is_valid_json(base64_decoded):
                return base64_decoded, "base64"
        except:
            pass
        
        # 4. 尝试Base64 + Gzip解码
        try:
            base64_decoded = base64.b64decode(content)
            gzip_decoded = gzip.decompress(base64_decoded).decode('utf-8')
            if self._is_valid_json(gzip_decoded):
                return gzip_decoded, "gzip"
        except:
            pass
        
        # 5. 尝试Base64 + Zlib解码
        try:
            base64_decoded = base64.b64decode(content)
            zlib_decoded = zlib.decompress(base64_decoded).decode('utf-8')
            if self._is_valid_json(zlib_decoded):
                return zlib_decoded, "zlib"
        except:
            pass
        
        # 6. 尝试自定义解密方法
        try:
            custom_decoded, method = self.custom_decrypt(content)
            if custom_decoded:
                return custom_decoded, method
        except:
            pass
        
        return None, None

    def decrypt(self, content, hint_type=None):
        """
        尝试使用多种方法解密TVBox配置或地址
        完全对标TVBox客户端Decoder类实现
        """
        self.logger.debug(f"尝试解密内容: {content[:100] if content else 'None'}...")

        # 处理空内容
        if not content or not content.strip():
            return content, "无内容"

        # 尝试进行URL编码解码
        if '%' in content:
            try:
                decoded = unquote(content)
                if decoded != content:
                    return decoded, "URL解码"
            except Exception as e:
                self.logger.debug(f"URL解码失败: {str(e)}")

        # 尝试JSON解析
        if content.strip().startswith('{') and self._is_json(content):
            return content, "JSON格式"

        # 尝试Base64解码 (对应TVBox中的base64方法)
        if "**" in content:
            try:
                extracted = self._extract_base64(content)
                if extracted:
                    decoded = base64.b64decode(extracted).decode('utf-8')
                    return decoded, "Base64解码"
            except Exception as e:
                self.logger.debug(f"Base64解码失败: {str(e)}")

        # 默认返回原内容
        return content, "无需解密"

    def _extract_base64(self, content):
        """提取Base64内容"""
        if "**" in content:
            # 提取 ** 之间的内容
            start = content.find("**")
            end = content.rfind("**")
            if start != -1 and end != -1 and start < end:
                return content[start+2:end]
        return None

    def _extract_and_decode_base64(self, data):
        """提取并解码Base64内容"""
        try:
            # 提取 ** 之间的内容
            start = data.find("**")
            end = data.rfind("**")
            if start != -1 and end != -1 and start < end:
                base64_content = data[start+2:end]
                decoded = base64.b64decode(base64_content).decode('utf-8')
                return decoded
        except Exception as e:
            self.logger.debug(f"Base64解码失败: {str(e)}")
        return data

    def _decrypt_cbc(self, data):
        """AES/CBC解密"""
        try:
            # 移除前缀
            if data.startswith("2423"):
                data = data[4:]

            # 解码hex
            encrypted_data = bytes.fromhex(data)

            # 使用默认密钥和IV
            key = b"VFBfMjAyMl9WaXA="[:16]  # 截取16字节
            iv = b"VFBfMjAyMl9WaXA="[:16]   # 截取16字节

            cipher = AES.new(key, AES.MODE_CBC, iv)
            decrypted = cipher.decrypt(encrypted_data)

            # 移除padding
            decrypted = unpad(decrypted, AES.block_size)

            return decrypted.decode('utf-8')
        except Exception as e:
            self.logger.debug(f"AES/CBC解密失败: {str(e)}")
            return data

    def custom_decrypt(self, content: str) -> Tuple[Optional[str], Optional[str]]:
        """自定义解密方法"""
        try:
            # 检查是否是特殊格式的加密内容
            
            # 方法1: 检查是否是十六进制编码
            if re.match(r'^[0-9a-fA-F]+$', content) and len(content) % 2 == 0:
                try:
                    hex_decoded = bytes.fromhex(content).decode('utf-8')
                    if self._is_valid_json(hex_decoded):
                        return hex_decoded, "hex"
                except:
                    pass
            
            # 方法2: 检查是否是反转的Base64
            try:
                reversed_content = content[::-1]
                base64_decoded = base64.b64decode(reversed_content).decode('utf-8')
                if self._is_valid_json(base64_decoded):
                    return base64_decoded, "reverse_base64"
            except:
                pass
            
            # 方法3: 检查是否是简单的字符替换
            try:
                # 常见的字符替换模式
                replacements = [
                    ('_', '/'),
                    ('-', '+'),
                    ('*', '=')
                ]
                
                modified_content = content
                for old, new in replacements:
                    modified_content = modified_content.replace(old, new)
                
                base64_decoded = base64.b64decode(modified_content).decode('utf-8')
                if self._is_valid_json(base64_decoded):
                    return base64_decoded, "custom_base64"
            except:
                pass
            
            return None, None
            
        except Exception as e:
            logger.error(f"自定义解密失败: {str(e)}")
            return None, None
    
    def analyze_config(self, config_content: str) -> Dict[str, Any]:
        """分析配置内容"""
        try:
            config_data = json.loads(config_content)
            
            analysis = {
                "valid": True,
                "sites_count": len(config_data.get("sites", [])),
                "lives_count": len(config_data.get("lives", [])),
                "parses_count": len(config_data.get("parses", [])),
                "wallpaper": config_data.get("wallpaper", ""),
                "spider": config_data.get("spider", ""),
                "total_size": len(config_content),
                "format": "json"
            }
            
            # 分析站点信息
            sites = config_data.get("sites", [])
            if sites:
                site_types = {}
                for site in sites:
                    site_type = site.get("type", "unknown")
                    site_types[site_type] = site_types.get(site_type, 0) + 1
                analysis["site_types"] = site_types
            
            # 分析直播源
            lives = config_data.get("lives", [])
            if lives:
                live_types = {}
                for live in lives:
                    live_type = live.get("type", "unknown")
                    live_types[live_type] = live_types.get(live_type, 0) + 1
                analysis["live_types"] = live_types
            
            return analysis
            
        except json.JSONDecodeError as e:
            return {
                "valid": False,
                "error": f"JSON格式错误: {str(e)}",
                "total_size": len(config_content)
            }
        except Exception as e:
            return {
                "valid": False,
                "error": f"分析失败: {str(e)}",
                "total_size": len(config_content)
            }
    
    def merge_configs(self, configs: List[str]) -> Optional[str]:
        """合并多个配置"""
        try:
            merged_data = {
                "sites": [],
                "lives": [],
                "parses": [],
                "wallpaper": "",
                "spider": ""
            }
            
            for config_content in configs:
                try:
                    config_data = json.loads(config_content)
                    
                    # 合并站点
                    if "sites" in config_data:
                        merged_data["sites"].extend(config_data["sites"])
                    
                    # 合并直播源
                    if "lives" in config_data:
                        merged_data["lives"].extend(config_data["lives"])
                    
                    # 合并解析器
                    if "parses" in config_data:
                        merged_data["parses"].extend(config_data["parses"])
                    
                    # 使用第一个非空的壁纸和爬虫
                    if not merged_data["wallpaper"] and config_data.get("wallpaper"):
                        merged_data["wallpaper"] = config_data["wallpaper"]
                    
                    if not merged_data["spider"] and config_data.get("spider"):
                        merged_data["spider"] = config_data["spider"]
                        
                except json.JSONDecodeError:
                    logger.warning(f"跳过无效的配置内容")
                    continue
            
            # 去重
            merged_data["sites"] = self._deduplicate_list(merged_data["sites"], "name")
            merged_data["lives"] = self._deduplicate_list(merged_data["lives"], "name")
            merged_data["parses"] = self._deduplicate_list(merged_data["parses"], "name")
            
            return json.dumps(merged_data, ensure_ascii=False, indent=2)
            
        except Exception as e:
            logger.error(f"合并配置失败: {str(e)}")
            return None
    
    def _is_valid_json(self, content: str) -> bool:
        """检查是否是有效的JSON"""
        try:
            json.loads(content)
            return True
        except:
            return False
    
    def _deduplicate_list(self, items: List[Dict], key: str) -> List[Dict]:
        """根据指定键去重"""
        seen = set()
        result = []
        
        for item in items:
            if isinstance(item, dict) and key in item:
                if item[key] not in seen:
                    seen.add(item[key])
                    result.append(item)
            else:
                result.append(item)
        
        return result
    
    def validate_config(self, config_content: str) -> Dict[str, Any]:
        """验证配置格式"""
        try:
            config_data = json.loads(config_content)
            
            # 检查必需字段
            required_fields = ["sites", "lives", "parses"]
            missing_fields = []
            
            for field in required_fields:
                if field not in config_data:
                    missing_fields.append(field)
            
            if missing_fields:
                return {
                    "valid": False,
                    "error": f"缺少必需字段: {', '.join(missing_fields)}"
                }
            
            # 检查字段类型
            if not isinstance(config_data["sites"], list):
                return {"valid": False, "error": "sites字段必须是数组"}
            
            if not isinstance(config_data["lives"], list):
                return {"valid": False, "error": "lives字段必须是数组"}
            
            if not isinstance(config_data["parses"], list):
                return {"valid": False, "error": "parses字段必须是数组"}
            
            return {"valid": True, "message": "配置格式验证通过"}
            
        except json.JSONDecodeError as e:
            return {"valid": False, "error": f"JSON格式错误: {str(e)}"}
        except Exception as e:
            return {"valid": False, "error": f"验证失败: {str(e)}"}

    def extract_json_from_mixed_content(self, content):
        """
        从混合内容中提取JSON配置

        Args:
            content: 混合内容（可能包含图片数据和Base64编码的JSON）

        Returns:
            str: 提取的JSON字符串，如果失败返回原内容
        """
        try:
            # 尝试直接解析为JSON
            json.loads(content)
            return content
        except:
            pass

        # 查找Base64编码的JSON数据
        try:
            import re
            import base64

            self.logger.info(f"开始从混合内容中提取JSON，内容长度: {len(content)}")

            # 查找长的Base64字符串（通常JSON配置会很长）
            base64_pattern = r'[A-Za-z0-9+/]{100,}={0,2}'
            matches = re.findall(base64_pattern, content)

            self.logger.info(f"找到 {len(matches)} 个可能的Base64字符串")

            for i, match in enumerate(matches):
                try:
                    self.logger.debug(f"尝试解码第 {i+1} 个Base64字符串，长度: {len(match)}")
                    # 尝试解码Base64
                    decoded = base64.b64decode(match).decode('utf-8')
                    self.logger.debug(f"Base64解码成功，解码后长度: {len(decoded)}")

                    # 打印解码后内容的前500个字符用于调试
                    self.logger.info(f"解码后内容前500字符: {decoded[:500]}")

                    # 清理解码后的数据，查找JSON部分
                    # 查找第一个 { 和最后一个 }
                    start_idx = decoded.find('{')
                    end_idx = decoded.rfind('}')

                    self.logger.info(f"JSON起始位置: {start_idx}, 结束位置: {end_idx}")

                    if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                        json_part = decoded[start_idx:end_idx+1]
                        self.logger.info(f"提取JSON部分，长度: {len(json_part)}")

                        # 尝试修复常见的JSON语法错误
                        try:
                            # 验证是否为有效JSON
                            parsed = json.loads(json_part)
                            self.logger.info(f"JSON解析成功，类型: {type(parsed)}")

                            # 确保包含TVBox配置的关键字段
                            if isinstance(parsed, dict) and ('sites' in parsed or 'spider' in parsed):
                                self.logger.info(f"成功从Base64提取JSON配置，长度: {len(json_part)}")
                                # 返回格式化的JSON
                                return json.dumps(parsed, ensure_ascii=False, indent=2)
                            else:
                                self.logger.info(f"JSON不包含TVBox配置字段，keys: {list(parsed.keys()) if isinstance(parsed, dict) else 'not dict'}")
                        except json.JSONDecodeError as e:
                            self.logger.warning(f"JSON解析失败: {e}")
                            # 先保存原始内容，然后尝试修复
                            self.logger.info(f"错误位置附近的内容: {json_part[max(0, 1957-50):1957+50]}")

                            # 尝试修复常见的JSON错误
                            try:
                                # 移除JSON注释（// 开头的行注释），但保留URL中的//
                                lines = json_part.split('\n')
                                cleaned_lines = []
                                for line in lines:
                                    # 智能查找注释位置，排除URL中的//
                                    original_line = line
                                    # 查找所有//的位置
                                    comment_pos = -1
                                    pos = 0
                                    while True:
                                        pos = line.find('//', pos)
                                        if pos == -1:
                                            break
                                        # 检查//前面的字符，如果是:则不是注释
                                        if pos > 0 and line[pos-1] == ':':
                                            pos += 2
                                            continue
                                        # 检查是否是真正的注释（前面应该是空格、逗号、}等）
                                        if pos == 0 or line[pos-1] in ' \t,}]':
                                            comment_pos = pos
                                            break
                                        pos += 2

                                    if comment_pos != -1:
                                        # 移除注释部分，保留注释前的内容
                                        line = line[:comment_pos].rstrip()
                                    if line.strip():  # 只保留非空行
                                        cleaned_lines.append(line)

                                fixed_json = '\n'.join(cleaned_lines)

                                # 移除可能的尾随逗号
                                fixed_json = re.sub(r',\s*}', '}', fixed_json)
                                fixed_json = re.sub(r',\s*]', ']', fixed_json)
                                # 移除所有控制字符，只保留可打印字符和基本空白字符
                                fixed_json = ''.join(char for char in fixed_json if ord(char) >= 32 or char in '\t\n\r')

                                parsed = json.loads(fixed_json)
                                if isinstance(parsed, dict) and ('sites' in parsed or 'spider' in parsed):
                                    self.logger.info(f"修复JSON后解析成功，长度: {len(fixed_json)}")
                                    return json.dumps(parsed, ensure_ascii=False, indent=2)
                            except Exception as fix_error:
                                self.logger.warning(f"JSON修复失败: {fix_error}")
                                # 如果修复失败，尝试返回清理后的内容（移除注释）
                                try:
                                    self.logger.info("开始清理注释...")
                                    lines = json_part.split('\n')
                                    cleaned_lines = []
                                    for line in lines:
                                        # 智能查找注释位置，排除URL中的//
                                        comment_pos = -1
                                        pos = 0
                                        while True:
                                            pos = line.find('//', pos)
                                            if pos == -1:
                                                break
                                            # 检查//前面的字符，如果是:则不是注释
                                            if pos > 0 and line[pos-1] == ':':
                                                pos += 2
                                                continue
                                            # 检查是否是真正的注释（前面应该是空格、逗号、}等）
                                            if pos == 0 or line[pos-1] in ' \t,}]':
                                                comment_pos = pos
                                                break
                                            pos += 2

                                        if comment_pos != -1:
                                            line = line[:comment_pos].rstrip()
                                        if line.strip():
                                            cleaned_lines.append(line)

                                    cleaned_content = '\n'.join(cleaned_lines)
                                    self.logger.info(f"返回清理注释后的完整内容，长度: {len(cleaned_content)}")
                                    return cleaned_content
                                except Exception as clean_error:
                                    self.logger.warning(f"清理注释失败: {clean_error}")
                                    self.logger.info("返回原始JSON内容的前2000字符作为预览")
                                    return json_part[:2000] + "\n\n... (内容被截断，原始长度: " + str(len(json_part)) + " 字符)"
                    else:
                        self.logger.info(f"未找到完整的JSON结构")

                except Exception as decode_error:
                    self.logger.warning(f"第 {i+1} 个Base64字符串解码失败: {decode_error}")
                    continue

            # 如果没有找到有效的Base64 JSON，返回空的TVBox配置模板
            self.logger.warning("未能从混合内容中提取有效的JSON配置，返回空模板")
            empty_config = {
                "spider": "",
                "wallpaper": "",
                "sites": [],
                "lives": [],
                "parses": []
            }
            return json.dumps(empty_config, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"提取JSON时发生错误: {e}")
            # 返回空模板
            empty_config = {
                "spider": "",
                "wallpaper": "",
                "sites": [],
                "lives": [],
                "parses": []
            }
            return json.dumps(empty_config, ensure_ascii=False, indent=2)

    def validate_tvbox_config(self, content):
        """
        验证配置内容是否是有效的TVBox配置

        Args:
            content: 配置内容

        Returns:
            bool: 是否是有效的TVBox配置
        """
        try:
            # 尝试解析JSON
            if not content or not isinstance(content, str):
                self.logger.debug("内容为空或不是字符串")
                return False

            json_data = json.loads(content)
            self.logger.debug(f"JSON解析成功，包含字段: {list(json_data.keys())}")

            # 检查是否包含sites字段（更宽松的检查）
            sites = []
            if 'sites' in json_data:
                sites = json_data['sites']
            elif 'video' in json_data and isinstance(json_data['video'], dict) and 'sites' in json_data['video']:
                sites = json_data['video']['sites']

            if not isinstance(sites, list):
                self.logger.debug(f"sites字段不是数组: {type(sites)}")
                return False

            # 如果没有站点，但有其他有效字段，也认为是有效配置
            if len(sites) == 0:
                # 检查是否有其他有效字段
                valid_fields = ['lives', 'parses', 'wallpaper', 'spider']
                has_valid_field = any(field in json_data for field in valid_fields)
                if has_valid_field:
                    self.logger.info("配置验证通过: 包含有效字段但无站点")
                    return True
                else:
                    self.logger.debug("配置无有效内容")
                    return False

            # 检查站点是否包含基本字段（更宽松的检查）
            valid_sites = 0
            for site in sites:
                if isinstance(site, dict):
                    # 只要包含name或key字段就认为是有效站点
                    if 'name' in site or 'key' in site:
                        valid_sites += 1

            if valid_sites == 0:
                self.logger.debug("没有找到有效站点")
                return False

            # 检查可选字段格式（更宽松）
            if 'lives' in json_data and json_data['lives'] is not None and not isinstance(json_data['lives'], list):
                self.logger.debug("lives字段格式错误")
                return False

            if 'parses' in json_data and json_data['parses'] is not None and not isinstance(json_data['parses'], list):
                self.logger.debug("parses字段格式错误")
                return False

            # 通过所有检查，认为是有效的TVBox配置
            self.logger.info(f"验证通过: 找到{valid_sites}个有效站点")
            return True

        except json.JSONDecodeError as e:
            self.logger.debug(f"JSON解析失败: {str(e)}")
            return False
        except Exception as e:
            self.logger.debug(f"配置验证失败: {str(e)}")
            return False

    def parse_tvbox_config_content(self, content):
        """
        解析TVBox配置内容，提取关键信息

        Args:
            content: JSON配置内容

        Returns:
            dict: 配置信息对象
        """
        try:
            # 解析JSON
            if isinstance(content, str):
                json_data = json.loads(content)
            else:
                json_data = content

            # 提取基本信息
            result = {
                'spider': json_data.get('spider', ''),
                'wallpaper': json_data.get('wallpaper', ''),
                'logo': json_data.get('logo', ''),
                'sites': [],
                'lives': [],
                'parses': []
            }

            # 提取站点信息
            sites = json_data.get('sites', json_data.get('video', {}).get('sites', []))
            for site in sites:
                if isinstance(site, dict):
                    site_info = {
                        'key': site.get('key', ''),
                        'name': site.get('name', ''),
                        'type': site.get('type', 0),
                        'api': site.get('api', ''),
                        'searchable': site.get('searchable', 1),
                        'quickSearch': site.get('quickSearch', 1),
                        'filterable': site.get('filterable', 1),
                        'hide': site.get('hide', 0),
                        'jar': site.get('jar', '')
                    }
                    result['sites'].append(site_info)

            # 提取解析器信息
            parses = json_data.get('parses', [])
            for parse in parses:
                if isinstance(parse, dict):
                    parse_info = {
                        'name': parse.get('name', ''),
                        'url': parse.get('url', ''),
                        'type': parse.get('type', 0)
                    }
                    result['parses'].append(parse_info)

            # 提取直播分组信息
            lives = json_data.get('lives', [])
            if lives and len(lives) > 0:
                # 检查第一个元素是否包含特殊处理字段
                if 'proxy' in str(lives[0]):
                    result['lives'].append({
                        'group': '默认分组',
                        'channels': [],
                        'proxy': True
                    })
                else:
                    # 常规直播源处理
                    for live in lives:
                        if isinstance(live, dict) and 'group' in live:
                            live_info = {
                                'group': live.get('group', ''),
                                'channels': []
                            }

                            # 处理频道
                            channels = live.get('channels', [])
                            for channel in channels:
                                if isinstance(channel, dict):
                                    channel_info = {
                                        'name': channel.get('name', ''),
                                        'urls': channel.get('urls', [])
                                    }
                                    live_info['channels'].append(channel_info)

                            result['lives'].append(live_info)

            return result

        except Exception as e:
            self.logger.error(f"解析配置内容失败: {str(e)}")
            return {
                'spider': '',
                'wallpaper': '',
                'logo': '',
                'sites': [],
                'lives': [],
                'parses': []
            }

    def _fix_urls(self, url, data):
        """修正相对URL，对标TVBox的fix方法"""
        # 替换JS URI
        for match in self.js_uri_pattern.finditer(data):
            data = self._replace_js_uri(url, data, match.group())

        # 替换相对路径
        base_url = self._get_base_url(url)
        if "../" in data:
            data = data.replace("../", urljoin(base_url, "../"))
        if "./" in data:
            data = data.replace("./", urljoin(base_url, "./"))
        if "__JS1__" in data:
            data = data.replace("__JS1__", "./")
        if "__JS2__" in data:
            data = data.replace("__JS2__", "../")

        return data

    def _replace_js_uri(self, url, data, ext):
        """替换JS URI"""
        base_url = self._get_base_url(url)
        t = ext.replace("\"./", "\"" + urljoin(base_url, "./"))
        t = t.replace("\"../", "\"" + urljoin(base_url, "../"))
        t = t.replace("./", "__JS1__").replace("../", "__JS2__")
        return data.replace(ext, t)

    def _get_base_url(self, url):
        """获取基础URL"""
        parsed = urlparse(url)
        path_parts = parsed.path.split("/")
        if len(path_parts) > 1:
            path = "/".join(path_parts[:-1]) + "/"
        else:
            path = "/"
        return f"{parsed.scheme}://{parsed.netloc}{path}"
