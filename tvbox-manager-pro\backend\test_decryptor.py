#!/usr/bin/env python3
"""
测试解密器获取原始配置
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.tvbox_decryptor import TVBoxDecryptor
import json

def test_decryptor():
    """测试解密器"""
    decryptor = TVBoxDecryptor()
    
    url = "http://www.饭太硬.com/tv"
    print(f"测试URL: {url}")
    
    try:
        content, method = decryptor.decrypt_config_url(url)
        print(f"解密方法: {method}")
        print(f"内容长度: {len(content) if content else 0}")
        
        if content:
            try:
                config_data = json.loads(content)
                print(f"配置中的spider: {config_data.get('spider', 'N/A')}")
                
                # 检查sites中的api字段
                sites = config_data.get('sites', [])
                if sites:
                    print(f"配置中第一个site的api: {sites[0].get('api', 'N/A')}")
                    
                    # 查找包含HTTP URL的字段
                    http_urls = []
                    for i, site in enumerate(sites):
                        api = site.get('api', '')
                        if isinstance(api, str) and api.startswith(('http://', 'https://')):
                            http_urls.append(f"sites[{i}].api: {api}")
                        
                        jar = site.get('jar', '')
                        if isinstance(jar, str) and jar.startswith(('http://', 'https://')):
                            http_urls.append(f"sites[{i}].jar: {jar}")
                        
                        ext = site.get('ext', '')
                        if isinstance(ext, str) and ext.startswith(('http://', 'https://')):
                            http_urls.append(f"sites[{i}].ext: {ext}")
                    
                    print(f"\n找到 {len(http_urls)} 个HTTP URL:")
                    for url in http_urls[:10]:  # 只显示前10个
                        print(f"  {url}")
                
                # 检查spider字段
                spider = config_data.get('spider', '')
                if isinstance(spider, str) and spider.startswith(('http://', 'https://')):
                    print(f"\nSpider HTTP URL: {spider}")
                else:
                    print(f"\nSpider不是HTTP URL: {spider}")
                    
                # 检查lives字段
                lives = config_data.get('lives', [])
                live_urls = []
                for i, live in enumerate(lives):
                    url_field = live.get('url', '')
                    if isinstance(url_field, str) and url_field.startswith(('http://', 'https://')):
                        live_urls.append(f"lives[{i}].url: {url_field}")
                
                print(f"\n找到 {len(live_urls)} 个直播源HTTP URL:")
                for url in live_urls[:5]:  # 只显示前5个
                    print(f"  {url}")
                    
            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")
                print(f"内容前500字符: {content[:500]}")
        else:
            print("未获取到内容")
            
    except Exception as e:
        print(f"解密失败: {e}")

if __name__ == "__main__":
    test_decryptor()
