// 手动测试功能
class ManualTester {
    constructor(app) {
        this.app = app;
    }
    
    async sendRequest() {
        const apiSelect = document.getElementById('apiSelect');
        const selectedApi = apiSelect.value;
        
        if (!selectedApi) {
            this.app.showToast('请先选择API接口', 'warning');
            return;
        }
        
        const apiDef = getApiDefinition(this.app.currentCategory, selectedApi);
        if (!apiDef) {
            this.app.showToast('API定义不存在', 'error');
            return;
        }
        
        try {
            // 收集表单数据
            const formData = this.collectFormData(apiDef);
            
            // 构建请求
            const request = this.buildRequest(apiDef, formData);
            
            // 显示加载状态
            this.showLoading(true);
            
            // 发送请求
            const startTime = Date.now();
            const response = await this.executeRequest(request);
            const endTime = Date.now();
            const responseTime = endTime - startTime;
            
            // 显示结果
            this.showResult(request, response, responseTime, true);
            
            // 记录日志
            this.app.log(`请求成功: ${apiDef.method} ${request.url} (${responseTime}ms)`, 'success');
            
            // 保存到结果历史
            this.saveToHistory(request, response, responseTime, true);
            
        } catch (error) {
            const endTime = Date.now();
            const responseTime = endTime - (error.startTime || Date.now());
            
            // 显示错误
            this.showResult(error.request || {}, error.response || {}, responseTime, false, error.message);
            
            // 记录错误日志
            this.app.log(`请求失败: ${error.message}`, 'error');
            
            // 保存到结果历史
            this.saveToHistory(error.request || {}, error.response || {}, responseTime, false, error.message);
            
        } finally {
            this.showLoading(false);
        }
    }
    
    collectFormData(apiDef) {
        const form = document.getElementById('apiTestForm');
        const formData = new FormData(form);
        const data = {};
        
        // 收集表单数据
        for (const [key, value] of formData.entries()) {
            const param = apiDef.parameters[key];
            if (param) {
                if (param.type === 'number') {
                    data[key] = value ? Number(value) : undefined;
                } else if (param.type === 'checkbox') {
                    data[key] = true; // 如果checkbox被选中，FormData会包含它
                } else {
                    data[key] = value || undefined;
                }
            }
        }
        
        // 处理未选中的checkbox
        for (const [key, param] of Object.entries(apiDef.parameters)) {
            if (param.type === 'checkbox' && !(key in data)) {
                data[key] = false;
            }
        }
        
        // 处理JSON字符串参数
        for (const [key, value] of Object.entries(data)) {
            if (typeof value === 'string' && (value.startsWith('[') || value.startsWith('{'))) {
                try {
                    data[key] = JSON.parse(value);
                } catch (e) {
                    // 保持原始字符串
                }
            }
        }
        
        return data;
    }
    
    buildRequest(apiDef, formData) {
        let url = this.app.config.apiBaseUrl + apiDef.path;
        const headers = {
            'Content-Type': 'application/json'
        };
        
        // 处理认证
        if (apiDef.requiresAuth && this.app.config.userEmail && this.app.config.userPassword) {
            const credentials = btoa(`${this.app.config.userEmail}:${this.app.config.userPassword}`);
            headers['Authorization'] = `Basic ${credentials}`;
        }
        
        // 处理路径参数
        const pathParams = {};
        const bodyData = {};
        const queryParams = {};
        
        for (const [key, value] of Object.entries(formData)) {
            const param = apiDef.parameters[key];
            if (param && param.isPathParam) {
                pathParams[key] = value;
                url = url.replace(`{${key}}`, encodeURIComponent(value));
            } else if (apiDef.method === 'GET' && value !== undefined && value !== '') {
                queryParams[key] = value;
            } else if (value !== undefined && value !== '') {
                bodyData[key] = value;
            }
        }
        
        // 构建查询字符串
        if (Object.keys(queryParams).length > 0) {
            const searchParams = new URLSearchParams();
            for (const [key, value] of Object.entries(queryParams)) {
                searchParams.append(key, value);
            }
            url += '?' + searchParams.toString();
        }
        
        return {
            method: apiDef.method,
            url: url,
            headers: headers,
            data: Object.keys(bodyData).length > 0 ? bodyData : undefined,
            timeout: this.app.config.timeout
        };
    }
    
    async executeRequest(request) {
        const config = {
            method: request.method.toLowerCase(),
            url: request.url,
            headers: request.headers,
            timeout: request.timeout
        };
        
        if (request.data && request.method !== 'GET') {
            config.data = request.data;
        }
        
        return await axios(config);
    }
    
    showLoading(show) {
        const resultContainer = document.getElementById('manualTestResult');
        if (show) {
            resultContainer.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">发送中...</span>
                    </div>
                    <div class="mt-2">发送请求中...</div>
                </div>
            `;
        }
    }
    
    showResult(request, response, responseTime, success, errorMessage = null) {
        const resultContainer = document.getElementById('manualTestResult');
        
        const statusClass = success ? 'success' : 'error';
        const statusText = success ? '成功' : '失败';
        const statusCode = response.status || 0;
        
        let resultHtml = `
            <div class="response-container">
                <div class="response-header">
                    <div>
                        <span class="response-status ${statusClass}">
                            ${statusCode} ${statusText}
                        </span>
                        <span class="response-time">${responseTime}ms</span>
                    </div>
                    <div>
                        <button class="btn btn-sm btn-outline-primary" onclick="copyResponse('manual')">
                            <i class="bi bi-clipboard"></i> 复制
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="showResponseModal('manual')">
                            <i class="bi bi-arrows-fullscreen"></i> 详情
                        </button>
                    </div>
                </div>
        `;
        
        // 请求信息
        resultHtml += `
            <div class="mb-3">
                <h6><i class="bi bi-arrow-up-right"></i> 请求信息</h6>
                <div class="row">
                    <div class="col-md-6">
                        <strong>方法:</strong> ${request.method}<br>
                        <strong>URL:</strong> <code>${request.url}</code>
                    </div>
                    <div class="col-md-6">
                        <strong>请求头:</strong>
                        <pre class="mt-1"><code>${JSON.stringify(request.headers, null, 2)}</code></pre>
                    </div>
                </div>
        `;
        
        if (request.data) {
            resultHtml += `
                <div class="mt-2">
                    <strong>请求体:</strong>
                    <pre class="mt-1"><code class="language-json">${JSON.stringify(request.data, null, 2)}</code></pre>
                </div>
            `;
        }
        
        resultHtml += '</div>';
        
        // 响应信息
        resultHtml += `
            <div>
                <h6><i class="bi bi-arrow-down-left"></i> 响应信息</h6>
        `;
        
        if (errorMessage) {
            resultHtml += `
                <div class="alert alert-danger">
                    <strong>错误:</strong> ${errorMessage}
                </div>
            `;
        }
        
        if (response.headers) {
            resultHtml += `
                <div class="mb-2">
                    <strong>响应头:</strong>
                    <pre><code>${JSON.stringify(response.headers, null, 2)}</code></pre>
                </div>
            `;
        }
        
        if (response.data) {
            const responseData = typeof response.data === 'string' ? response.data : JSON.stringify(response.data, null, 2);
            const language = typeof response.data === 'object' ? 'json' : 'text';
            
            resultHtml += `
                <div class="response-body">
                    <strong>响应体:</strong>
                    <pre class="mt-1"><code class="language-${language}" id="manualResponseData">${responseData}</code></pre>
                </div>
            `;
        }
        
        resultHtml += '</div></div>';
        
        resultContainer.innerHTML = resultHtml;
        
        // 高亮代码
        if (window.Prism) {
            Prism.highlightAll();
        }
        
        // 保存响应数据用于复制
        window.lastManualResponse = {
            request: request,
            response: response,
            responseTime: responseTime,
            success: success,
            errorMessage: errorMessage
        };
    }
    
    saveToHistory(request, response, responseTime, success, errorMessage = null) {
        const historyItem = {
            timestamp: new Date().toISOString(),
            request: request,
            response: response,
            responseTime: responseTime,
            success: success,
            errorMessage: errorMessage
        };
        
        this.app.testResults.unshift(historyItem);
        
        // 限制历史记录数量
        if (this.app.testResults.length > 100) {
            this.app.testResults = this.app.testResults.slice(0, 100);
        }
        
        // 更新结果页面
        if (window.resultsManager) {
            window.resultsManager.updateResults();
        }
    }
    
    fillExampleData() {
        const apiSelect = document.getElementById('apiSelect');
        const selectedApi = apiSelect.value;
        
        if (!selectedApi) {
            this.app.showToast('请先选择API接口', 'warning');
            return;
        }
        
        const apiDef = getApiDefinition(this.app.currentCategory, selectedApi);
        if (!apiDef || !apiDef.example) {
            this.app.showToast('该API没有示例数据', 'info');
            return;
        }
        
        const form = document.getElementById('apiTestForm');
        for (const [key, value] of Object.entries(apiDef.example)) {
            const input = form.querySelector(`[name="${key}"]`);
            if (input) {
                if (input.type === 'checkbox') {
                    input.checked = !!value;
                } else {
                    input.value = typeof value === 'object' ? JSON.stringify(value) : value;
                }
            }
        }
        
        this.app.showToast('示例数据已填充', 'success');
    }
    
    clearForm() {
        const form = document.getElementById('apiTestForm');
        if (form) {
            form.reset();
            this.app.showToast('表单已清空', 'info');
        }
    }
}

// 全局函数
function sendManualRequest() {
    if (!window.manualTester) {
        window.manualTester = new ManualTester(app);
    }
    window.manualTester.sendRequest();
}

function fillExampleData() {
    if (!window.manualTester) {
        window.manualTester = new ManualTester(app);
    }
    window.manualTester.fillExampleData();
}

function clearForm() {
    if (!window.manualTester) {
        window.manualTester = new ManualTester(app);
    }
    window.manualTester.clearForm();
}

function copyResponse(type) {
    const responseData = type === 'manual' ? window.lastManualResponse : null;
    if (!responseData) {
        app.showToast('没有可复制的响应数据', 'warning');
        return;
    }
    
    const textToCopy = JSON.stringify(responseData, null, 2);
    
    if (navigator.clipboard) {
        navigator.clipboard.writeText(textToCopy).then(() => {
            app.showToast('响应数据已复制到剪贴板', 'success');
        }).catch(() => {
            app.showToast('复制失败', 'error');
        });
    } else {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = textToCopy;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand('copy');
            app.showToast('响应数据已复制到剪贴板', 'success');
        } catch (err) {
            app.showToast('复制失败', 'error');
        }
        document.body.removeChild(textArea);
    }
}

function showResponseModal(type) {
    const responseData = type === 'manual' ? window.lastManualResponse : null;
    if (!responseData) {
        app.showToast('没有可显示的响应数据', 'warning');
        return;
    }
    
    const modal = document.getElementById('responseModal');
    const modalContent = document.getElementById('responseContent');
    
    const formattedData = JSON.stringify(responseData, null, 2);
    modalContent.innerHTML = `
        <pre><code class="language-json">${formattedData}</code></pre>
    `;
    
    // 高亮代码
    if (window.Prism) {
        Prism.highlightAll();
    }
    
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}
