import { defineComponent } from 'vue';
import { COMPONENT_NAME } from './constants.mjs';
import { columnAlignment } from '../../../constants/column-alignment.mjs';
import { buildProps } from '../../../utils/vue/props/runtime.mjs';

const descriptionItemProps = buildProps({
  label: {
    type: String,
    default: ""
  },
  span: {
    type: Number,
    default: 1
  },
  rowspan: {
    type: Number,
    default: 1
  },
  width: {
    type: [String, Number],
    default: ""
  },
  minWidth: {
    type: [String, Number],
    default: ""
  },
  labelWidth: {
    type: [String, Number],
    default: ""
  },
  align: {
    type: String,
    values: columnAlignment,
    default: "left"
  },
  labelAlign: {
    type: String,
    values: columnAlignment
  },
  className: {
    type: String,
    default: ""
  },
  labelClassName: {
    type: String,
    default: ""
  }
});
const DescriptionItem = defineComponent({
  name: COMPONENT_NAME,
  props: descriptionItemProps
});

export { DescriptionItem as default, descriptionItemProps };
//# sourceMappingURL=description-item.mjs.map
