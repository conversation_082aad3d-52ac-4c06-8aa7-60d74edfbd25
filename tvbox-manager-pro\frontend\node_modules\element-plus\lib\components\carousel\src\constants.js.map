{"version": 3, "file": "constants.js", "sources": ["../../../../../../packages/components/carousel/src/constants.ts"], "sourcesContent": ["import type { Injection<PERSON><PERSON>, Ref, VNode } from 'vue'\nimport type { CarouselItemProps } from './carousel-item'\n\nexport type CarouselItemStates = {\n  hover: boolean\n  translate: number\n  scale: number\n  active: boolean\n  ready: boolean\n  inStage: boolean\n  animating: boolean\n}\n\nexport type CarouselItemContext = {\n  props: CarouselItemProps\n  states: CarouselItemStates\n  uid: number\n  getVnode: () => VNode\n  translateItem: (index: number, activeIndex: number, oldIndex?: number) => void\n}\n\nexport type CarouselContext = {\n  root: Ref<HTMLElement | undefined>\n  items: Ref<CarouselItemContext[]>\n  isCardType: Ref<boolean>\n  isVertical: Ref<boolean>\n  loop: boolean\n  cardScale: number\n  addItem: (item: CarouselItemContext) => void\n  removeItem: (item: CarouselItemContext) => void\n  setActiveItem: (index: number) => void\n  setContainerHeight: (height: number) => void\n}\n\nexport const carouselContextKey: InjectionKey<CarouselContext> =\n  Symbol('carouselContextKey')\n\nexport const CAROUSEL_ITEM_NAME = 'ElCarouselItem'\n"], "names": [], "mappings": ";;;;AAAY,MAAC,kBAAkB,GAAG,MAAM,CAAC,oBAAoB,EAAE;AACnD,MAAC,kBAAkB,GAAG;;;;;"}