//资源全部免费，仅供测试交流，请勿内置传播与转卖；关注微信公众号：奇奇乐分享，发送“本地包”获取教程与更多资源！

{
    "spider": "./spider.jar",
    "wallpaper": "http://ppic/feimao.php",
    "logo": "http://panda/0262.png",
    "sites": [
        {
            "key": "豆瓣",
            "name": "🐼┃备用公众号┃我不是肥猫",
            "type": 3,
            "api": "csp_Douban",
            "searchable": 0
        },
        {
            "key": "豆瓣预告",
            "name": "🐼┃豆瓣┃预告",
            "type": 3,
            "api": "csp_YGP",
            "playerType": 2,
            "searchable": 0
        },
        {
            "key": "config",
            "name": "🐼┃配置┃中心",
            "type": 3,
            "api": "csp_Config",
            "playerType": 2
        },
        {
            "key": "csp_FeiMaoUC",
            "name": "⚡┃闪电┃优汐",
            "type": 3,
            "api": "csp_Duopan",
            "filterable": 1,
            "ext": {
                "site_urls": [
                    "http://1.95.79.193/"
                ],
                "url_key": "FeiMaoUC"
            }
        },
        {
            "key": "csp_Duopan",
            "name": "🖍︎┃蜡笔┃影视",
            "type": 3,
            "api": "csp_Duopan",
            "filterable": 1,
            "ext": {
                "site_urls": [
                    "https://feimao666.fun",
                    "http://feimao888.fun",
                    "http://feimaoai.site",
                    "http://www.labi88.sbs",
                    "http://fmao.site",
                    "http://fmao.shop",
                    "http://xiaocge.fun"
                ],
                "threadinfo": {
                    "chunksize": 512,
                    "threads": 16
                },
                "url_key": "Duopan2"
            }
        },
        {
            "key": "csp_Netfixtv",
            "name": "💌┃️至臻┃影视",
            "type": 3,
            "api": "csp_Duopan",
            "filterable": 1,
            "ext": {
                "site_urls": [
                    "http://xiaomi666.fun",
                    "https://xiaomiai.site",
                    "https://mihdr.top",
                    "https://www.mihdr.top",
                    "http://www.miqk.cc",
                    "https://www.zhizhenpan.fun"
                ],
                "url_key": "Netfixtv2",
                "threadinfo": {
                    "chunksize": 512,
                    "threads": 16
                }
            }
        },
        {
            "key": "csp_Panda_玩偶哥哥",
            "name": "👲┃玩偶┃影视",
            "api": "csp_Wogg",
            "type": 3,
            "searchable": 1,
            "playerType": 2,
            "timeout": 30,
            "changeable": 0,
            "ext": "https://www.wogg.one/"
        },
        {
            "key": "潮流",
            "name": "🏜┃潮流┃APP",
            "type": 3,
            "api": "csp_AppRJ",
            "ext": "vxw35/hHSj07Q+maxQzOVMq1rjRCOTXpUCx8iKu5jIg="
        },
        {
            "key": "梦回",
            "name": "🏈┃梦回┃APP",
            "type": 3,
            "api": "csp_AppRJ",
            "ext": "Z2MNziRBwjQxqaBuUxNkDRyvTntIkN1cOXhjyWgjCTc="
        },
        {
            "key": "星辉",
            "name": "🦢┃星辉┃APP",
            "type": 3,
            "api": "csp_AppRJ",
            "ext": "jsSMEuhTZIAHjnUoLBzKdlRu5exzno6M4efF8LzwjWM="
        },
        {
            "key": "肥猫",
            "name": "🐼┃肥猫┃APP",
            "type": 3,
            "api": "csp_AppGet",
            "ext": "4wSF77WNzVYxpDQF3Vf/eAoVqKf1nKdv5MpKtrhokKJN288M3lP16ZeoM36I71sJ"
        },
        {
            "key": "干饭",
            "name": "🍚┃干饭┃APP",
            "type": 3,
            "api": "csp_AppGet",
            "ext": "Ygx7wpu8Zcm70i88iO9WMRvxQZdMqIapMqvlBKPmjCP60xrvslJlJFIwHepviTkKbgCK9nn25i0k7xnWVX0Y7g=="
        },
        {
            "key": "光盘",
            "name": "📀┃光盘┃APP",
            "type": 3,
            "api": "csp_AppGet",
            "ext": "Qrf5S6Si5oF7dQyuv+Srh3uh0lT3z1Y7u59ip9hRVeUKFHSUUbLyGMREENFjE1N9FXjZ6Z7tiLWs6P15Ol/g5GJXlo8gIlG7ul8a9uA76+A="
        },
        {
            "key": "行动",
            "name": "😌┃行动┃APP",
            "type": 3,
            "api": "csp_AppGet",
            "ext": "Koog6uPyOjHNZh8eY8CgUL+XGp1zR8d/K2NE9Ls6xomS8zNeZA6OEFwcZ27Zy0ZH9i0JczXR1zmcv9qIihz837BdnQrvJfE8QEyUmS3qKxs="
        },
        {
            "key": "再来",
            "name": "✌️┃再来┃APP",
            "type": 3,
            "api": "csp_AppGet",
            "ext": "QxNFQL63IQgXoB/O+Q12SpWJNcSeybsKVx0uAk9+pBwLEYRybmr89ubfoMpO9ZMnmJwYP5eKIrLK/w5t9S+6+F2ZlfWUyUoDUP2rV9QVabZh8nM+rBV7F/sxe+7GgsdL"
        },
        {
            "key": "一碗",
            "name": "🥣┃一碗┃APP",
            "type": 3,
            "api": "csp_AppGet",
            "ext": "ALuKLnutJ05qjASk0Chn7a1ibR0W0XHqemfuZwhL7IuMdagHkHIMn3C5V/5JLCsoeZGsBV/Lw0xVXwTkIlgq0Q=="
        },
        {
            "key": "猫抓",
            "name": "🐱┃猫抓┃APP",
            "type": 3,
            "api": "csp_AppGet",
            "ext": "uOx4WoHPKe5LbDsNX+u7yPZ/33148sHvWON6kZmflkJm9N9mwXRjT6S/UJau4nVckbCH+h6lvztfl/Ag6BM8Ng=="
        },
        {
            "key": "仓鼠",
            "name": "🐹┃耗子┃APP",
            "type": 3,
            "api": "csp_AppGet",
            "ext": "uOx4WoHPKe5LbDsNX+u7yPwU6Ups0duUFWxZbQqPo2oF+jkgV7jbW6aOO1kS4+9PdDFoRHhRxKKvcUOgzloTKw=="
        },
        {
            "key": "蔬菜",
            "name": "🫛┃蔬菜┃APP",
            "type": 3,
            "api": "csp_AppGet",
            "ext": "/eCbNvmrG2960Ybun3e0F4Nv6y2xkgJD5jOna5SVI+QW4PXMkizOh5qQPL132WcH"
        },
        {
            "key": "黑猫",
            "name": "🐼┃黑猫┃APP",
            "type": 3,
            "api": "csp_AppGet",
            "ext": "cLeOXZXsq65z+2s8qkWsD+GYhlTxoLn2cNnZiAXyiQPBfOCR3nVfgmZWdkvjSaqJFD9Bmb+U0RVCI9wv8BMVIg=="
        },
        {
            "key": "囧次元",
            "name": "🤡┃次元┃APP",
            "type": 3,
            "api": "csp_AppGet",
            "ext": "DUFPNFXbc0nhQ9S/akUS1016tumg5c0CXsmywMwiuf27+XAPWObdQRDR22Lgih/6189f+1INKiaNm4cQ+AY7CA=="
        },
        {
            "key": "csp_Jpys",
            "name": "🥇️┃金牌┃影视",
            "type": 3,
            "api": "csp_Jpys"
        },
        {
            "key": "csp_Wwys",
            "name": "🌾️┃农民┃影视",
            "type": 3,
            "api": "csp_Wwys",
            "ext": "https://www.wwgz.cn"
        },
        {
            "key": "csp_KmeiJu",
            "name": "🍐┃鸭梨┃影视",
            "type": 3,
            "api": "csp_KmeiJu"
        },
        {
            "key": "csp_Lkdy",
            "name": "🏔️┃来看┃影视",
            "type": 3,
            "api": "csp_Lkdy"
        },
        {
            "key": "csp_Tvyb",
            "name": "☁️️┃云播┃影视",
            "type": 3,
            "api": "csp_Tvyb"
        },
        {
            "key": "csp_Qiyou",
            "name": "🥝┃奇优┃影视",
            "type": 3,
            "api": "csp_Qiyou"
        },
        {
            "key": "荐片",
            "name": "🎬┃荐片┃影视",
            "api": "csp_Jianpian",
            "type": 3,
            "playerType": "2"
        },
        {
            "key": "csp_Quanwk",
            "name": "👀┃全看┃影视",
            "type": 3,
            "api": "csp_Quanwk",
            "ext": "https://www.91qkw.com"
        },
        {
            "key": "csp_J",
            "name": "🌎┃饺子┃外剧",
            "type": 3,
            "api": "csp_Jiaozi",
            "playerType": 2
        },
        {
            "key": "csp_SaoHuo",
            "name": "🔥┃火火┃影视",
            "type": 3,
            "api": "csp_SaoHuo",
            "playerType": 2,
            "ext": "https://shdy5.us"
        },
        {
            "key": "csp_Gz360",
            "name": "🍉┃瓜子┃影视",
            "type": 3,
            "api": "csp_Gz360",
            "playerType": 2
        },
        {
            "key": "csp_LiteApple",
            "name": "🍎┃苹果┃影视",
            "type": 3,
            "api": "csp_LiteApple",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "厂长",
            "name": "🏭┃厂长┃影视",
            "type": 3,
            "playerType": "2",
            "api": "csp_Czsapp",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "https://www.czzymovie.com"
        },
        {
            "key": "csp_AppXY",
            "name": "🎀️┃星牙┃短剧",
            "type": 3,
            "api": "csp_AppXY"
        },
        {
            "key": "csp_SP360",
            "name": "📺┃360┃官源",
            "type": 3,
            "api": "csp_SP360"
        },
        {
            "key": "csp_Bili",
            "name": "🅱┃️哔哩┃合集",
            "type": 3,
            "api": "csp_Bili",
            "playerType": 2,
            "ext": {
                "json": "./json/bili.json",
                "cookie": ""
            },
            "style": {
                "type": "rect",
                "ratio": 1.333
            }
        },
        {
            "key": "csp_Dm84",
            "name": "🤣┃动漫┃巴士",
            "type": 3,
            "playerType": "2",
            "api": "csp_Dm84",
            "ext": "https://dm84.net"
        },
        {
            "key": "csp_FourK",
            "name": "🌋┃绝对┃影视",
            "type": 3,
            "playerType": "2",
            "api": "csp_FourK",
            "ext": "https://www.4kvm.tv"
        },
        {
            "key": "csp_FirstAid",
            "name": "🚑┃急救┃教学",
            "type": 3,
            "api": "csp_FirstAid",
            "searchable": 0,
            "quickSearch": 0,
            "style": {
                "type": "rect",
                "ratio": 3.8
            },
            "gridview": "0-0-4.1"
        },
        {
            "key": "酷狗",
            "name": "🐾┃酷狗┃音乐",
            "type": 3,
            "api": "csp_Kugou",
            "playerType": 2,
            "ext": {
                "classes": [
                    {
                        "type_name": "酷狗",
                        "type_id": "kugou"
                    }
                ]
            }
        },
        {
            "key": "MTV",
            "name": "🎧┃明星┃MV",
            "type": 3,
            "api": "csp_Bili",
            "playerType": 2,
            "ext": {
                "json": "https://img2.gelonghui.com/library/2b4eb-8fb08a6b-f9f1-48d8-816a-1bc712a85fefnull",
                "cookie": ""
            },
            "style": {
                "type": "rect",
                "ratio": 1.333
            }
        },
        {
            "key": "娱乐",
            "name": "🎮┃游戏┃娱乐",
            "type": 3,
            "api": "./api/LIVES.py",
            "style": {
                "type": "rect",
                "ratio": 1.333
            },
            "ext": ""
        },
        {
            "key": "看球",
            "name": "⚾┃看球┃直播",
            "type": 3,
            "api": "csp_Kanqiu",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 0,
            "gridview": 3,
            "style": {
                "type": "list"
            }
        },
        {
            "key": "drpy_js_310直播",
            "name": "🏀┃体育┃直播",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "changeable": 0,
            "gridview": 3,
            "style": {
                "type": "list"
            },
            "ext": "https://img2.gelonghui.com/library/d6014-7a5d5b19-3fe5-456a-8335-925bc845606fnull"
        },
        {
            "key": "米搜",
            "name": "🌖┃米搜┃网盘",
            "type": 3,
            "api": "csp_MiSou",
            "ext": "http://127.0.0.1:9978/file/fatcat/kk.txt"
        },
        {
            "key": "csp_PanSearch",
            "name": "🚃┃盘搜┃网盘",
            "type": 3,
            "api": "csp_PanSearch",
            "ext": "http://127.0.0.1:9978/file/fatcat/token.txt"
        },
        {
            "key": "儿童",
            "name": "📚┃儿童┃启蒙",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "searchable": 0,
            "quickSearch": 0,
            "changeable": 0,
            "style": {
                "type": "rect",
                "ratio": 1.597
            },
            "ext": "./js/兔小贝.js"
        },
        {
            "key": "csp_少儿",
            "name": "📚┃少儿┃教育",
            "type": 3,
            "api": "csp_Bili",
            "playerType": 2,
            "ext": {
                "json": "https://img2.gelonghui.com/library/1903b-eb0f1675-2437-4e72-bcf0-427b1626d79fnull",
                "cookie": ""
            },
            "style": {
                "type": "rect",
                "ratio": 1.333
            }
        },
        {
            "key": "csp_小学",
            "name": "📚┃小学┃课堂",
            "type": 3,
            "api": "csp_Bili",
            "playerType": 2,
            "ext": {
                "json": "https://img2.gelonghui.com/library/a1140-144855fe-3eaa-44f3-b689-6812c233de54null",
                "cookie": ""
            },
            "style": {
                "type": "rect",
                "ratio": 1.333
            }
        },
        {
            "key": "csp_初中",
            "name": "📚┃初中┃课堂",
            "type": 3,
            "api": "csp_Bili",
            "playerType": 2,
            "ext": {
                "json": "https://img2.gelonghui.com/library/ba156-73e16cad-8257-4f33-b8c0-e051e72e546dnull",
                "cookie": ""
            },
            "style": {
                "type": "rect",
                "ratio": 1.333
            }
        },
        {
            "key": "csp_高中",
            "name": "📚┃高中┃课堂",
            "type": 3,
            "api": "csp_Bili",
            "playerType": 2,
            "ext": {
                "json": "https://img2.gelonghui.com/library/e44b3-4a82ab48-e014-49b2-bb66-ee5207e8f195null",
                "cookie": ""
            },
            "style": {
                "type": "rect",
                "ratio": 1.333
            }
        },
        {
            "key": "push_agent",
            "name": "关注公众号：肥猫宝贝",
            "type": 3,
            "api": "csp_Push",
            "playerType": "1",
            "searchable": 0,
            "ext": ".json/txt/ken.txt"
        }
    ],
    "parses": [
        {
            "name": "Json聚合",
            "type": 3,
            "url": "Demo"
        },
        {
            "name": "Web聚合",
            "type": 3,
            "url": "Web"
        },
        {
            "name": "qiyi[官源]",
            "type": 1,
            "url": "http://39.104.230.177:1122/lxjx/myyk.php?url="
        },
        {
            "name": "肥猫最可爱",
            "type": 1,
            "url": "http://xn--ihqu10cn4c.xn--z7x900a.live/jx.php?id=2&url=",
            "ext": {
                "flag": [
                    "qq",
                    "腾讯",
                    "qiyi",
                    "爱奇艺",
                    "奇艺",
                    "youku",
                    "优酷",
                    "tucheng",
                    "sohu",
                    "搜狐",
                    "letv",
                    "乐视",
                    "mgtv",
                    "芒果",
                    "tnmb",
                    "seven",
                    "yzm",
                    "aliyun",
                    "RJuMao",
                    "bilibili",
                    "1905",
                    "xinvip",
                    "XAL",
                    "qiqi",
                    "XALS",
                    "YuMi-vip"
                ]
            }
        }
    ],
    "lives": [
        {
            "name": "live",
            "type": 0,
            "url": "./lives/live.txt",
            "epg": "http://epg.112114.xyz/?ch={name}&date={date}"
        }
    ],
    "rules": [
        {
            "name": "♻️量非",
            "hosts": [
                "lz",
                "vip.lz",
                "v.cdnlz",
                "hd.lz",
                "ffzy",
                "vip.ffzy",
                "hd.ffzy"
            ],
            "regex": [
                "#EXT-X-DISCONTINUITY\r*\n*#EXTINF:6.666667,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXT-X-DISCONTINUITY\r*\n*#EXTINF:6.600000,[\\s\\S]*?#EXT-X-DISCONTINUITY"
            ]
        },
        {
            "name": "♻️索尼",
            "hosts": [
                "suonizy",
                "qrssv.com"
            ],
            "regex": [
                "15.1666",
                "15.2666"
            ]
        },
        {
            "name": "♻️乐视",
            "hosts": [
                "leshiyun"
            ],
            "regex": [
                "15.92"
            ]
        },
        {
            "name": "♻️优质",
            "hosts": [
                "yzzy",
                "playback"
            ],
            "regex": [
                "16.63",
                "18.66",
                "17.66",
                "19.13"
            ]
        },
        {
            "name": "♻️快看",
            "hosts": [
                "kuaikan",
                "vip.kuaikan"
            ],
            "regex": [
                "15.32",
                "15.231",
                "18.066"
            ]
        },
        {
            "name": "♻️360",
            "hosts": [
                "lyhuicheng"
            ],
            "regex": [
                "#EXTINF.*?\\s+.*?hrz8QcR9.*?\\.ts\\s+",
                "#EXT-X-KEY:METHOD=NONE[\\s\\S]*?#EXT-X-DISCONTINUITY"
            ]
        },
        {
            "name": "♻️开源棋牌",
            "hosts": [
                "askzycdn",
                "jkunbf",
                "bfikuncdn",
                "bfaskcdn"
            ],
            "regex": [
                "#EXT-X-KEY:METHOD=NONE\r*\n*#EXTINF:5,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXT-X-KEY:METHOD=AES-128,URI=\"[^\"]+\"\r*\n*#EXTINF:3.333,[\\s\\S]*?#EXT-X-DISCONTINUITY"
            ]
        },
        {
            "name": "♻️暴风",
            "hosts": [
                "bfengbf.com",
                "bfzy",
                "c1"
            ],
            "regex": [
                "#EXTINF.*?\\s+.*?adjump.*?\\.ts\\s+",
                "#EXT-X-DISCONTINUITY\r*\n*#EXTINF:3,[\\s\\S]*?#EXT-X-DISCONTINUITY"
            ]
        },
        {
            "name": "♻️农民",
            "hosts": [
                "toutiaovod.com"
            ],
            "regex": [
                "video/tos/cn"
            ]
        },
        {
            "name": "♻️火山",
            "hosts": [
                "huoshan.com"
            ],
            "regex": [
                "item_id="
            ]
        },
        {
            "name": "♻️抖音",
            "hosts": [
                "douyin.com"
            ],
            "regex": [
                "is_play_url="
            ]
        },
        {
            "name": "♻️磁力",
            "hosts": [
                "magnet"
            ],
            "regex": [
                "最 新",
                "直 播",
                "更 新"
            ]
        },
        {
            "name": "♻️饭团点击",
            "hosts": [
                "dadagui",
                "freeok",
                "dadagui"
            ],
            "script": [
                "document.querySelector(\"#playleft iframe\").contentWindow.document.querySelector(\"#start\").click();"
            ]
        },
        {
            "name": "♻️毛驴点击",
            "hosts": [
                "www.maolvys.com"
            ],
            "script": [
                "document.getElementsByClassName('swal-button swal-button--confirm')[0].click()"
            ]
        }
    ]
}