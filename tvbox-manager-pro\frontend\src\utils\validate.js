/**
 * 验证工具函数
 */

/**
 * 验证邮箱格式
 * @param {string} email 邮箱地址
 * @returns {boolean} 是否有效
 */
export function isEmail(email) {
  const reg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  return reg.test(email)
}

/**
 * 验证手机号格式
 * @param {string} phone 手机号
 * @returns {boolean} 是否有效
 */
export function isPhone(phone) {
  const reg = /^1[3-9]\d{9}$/
  return reg.test(phone)
}

/**
 * 验证URL格式
 * @param {string} url URL地址
 * @returns {boolean} 是否有效
 */
export function isUrl(url) {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

/**
 * 验证是否为外部链接
 * @param {string} path 路径
 * @returns {boolean} 是否为外部链接
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

/**
 * 验证IP地址格式
 * @param {string} ip IP地址
 * @returns {boolean} 是否有效
 */
export function isIP(ip) {
  const reg = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
  return reg.test(ip)
}

/**
 * 验证端口号
 * @param {number|string} port 端口号
 * @returns {boolean} 是否有效
 */
export function isPort(port) {
  const num = Number(port)
  return Number.isInteger(num) && num >= 1 && num <= 65535
}

/**
 * 验证用户名格式
 * @param {string} username 用户名
 * @returns {boolean} 是否有效
 */
export function isUsername(username) {
  // 3-20位，字母、数字、下划线
  const reg = /^[a-zA-Z0-9_]{3,20}$/
  return reg.test(username)
}

/**
 * 验证密码强度
 * @param {string} password 密码
 * @returns {object} 验证结果
 */
export function validatePassword(password) {
  const result = {
    valid: false,
    strength: 0,
    message: '',
    checks: {
      length: false,
      lowercase: false,
      uppercase: false,
      number: false,
      special: false
    }
  }
  
  if (!password) {
    result.message = '密码不能为空'
    return result
  }
  
  // 长度检查
  if (password.length >= 8) {
    result.checks.length = true
    result.strength += 1
  }
  
  // 小写字母
  if (/[a-z]/.test(password)) {
    result.checks.lowercase = true
    result.strength += 1
  }
  
  // 大写字母
  if (/[A-Z]/.test(password)) {
    result.checks.uppercase = true
    result.strength += 1
  }
  
  // 数字
  if (/\d/.test(password)) {
    result.checks.number = true
    result.strength += 1
  }
  
  // 特殊字符
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    result.checks.special = true
    result.strength += 1
  }
  
  // 判断强度
  if (result.strength < 3) {
    result.message = '密码强度较弱'
  } else if (result.strength < 4) {
    result.message = '密码强度中等'
  } else {
    result.message = '密码强度较强'
    result.valid = true
  }
  
  return result
}

/**
 * 验证身份证号码
 * @param {string} idCard 身份证号码
 * @returns {boolean} 是否有效
 */
export function isIdCard(idCard) {
  const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  return reg.test(idCard)
}

/**
 * 验证银行卡号
 * @param {string} cardNumber 银行卡号
 * @returns {boolean} 是否有效
 */
export function isBankCard(cardNumber) {
  const reg = /^[1-9]\d{12,18}$/
  return reg.test(cardNumber)
}

/**
 * 验证中文姓名
 * @param {string} name 姓名
 * @returns {boolean} 是否有效
 */
export function isChineseName(name) {
  const reg = /^[\u4e00-\u9fa5]{2,8}$/
  return reg.test(name)
}

/**
 * 验证数字范围
 * @param {number} value 值
 * @param {number} min 最小值
 * @param {number} max 最大值
 * @returns {boolean} 是否在范围内
 */
export function isInRange(value, min, max) {
  const num = Number(value)
  return !isNaN(num) && num >= min && num <= max
}

/**
 * 验证文件大小
 * @param {File} file 文件对象
 * @param {number} maxSize 最大大小（字节）
 * @returns {boolean} 是否符合要求
 */
export function isValidFileSize(file, maxSize) {
  return file && file.size <= maxSize
}

/**
 * 验证文件类型
 * @param {File} file 文件对象
 * @param {Array} allowedTypes 允许的类型
 * @returns {boolean} 是否符合要求
 */
export function isValidFileType(file, allowedTypes) {
  if (!file) return false
  return allowedTypes.includes(file.type)
}

/**
 * 验证图片文件
 * @param {File} file 文件对象
 * @returns {boolean} 是否为图片
 */
export function isImageFile(file) {
  const imageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
  return isValidFileType(file, imageTypes)
}

/**
 * 验证JSON格式
 * @param {string} str JSON字符串
 * @returns {boolean} 是否为有效JSON
 */
export function isValidJSON(str) {
  try {
    JSON.parse(str)
    return true
  } catch {
    return false
  }
}

/**
 * 验证颜色值（十六进制）
 * @param {string} color 颜色值
 * @returns {boolean} 是否有效
 */
export function isValidColor(color) {
  const reg = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
  return reg.test(color)
}

/**
 * 验证版本号格式
 * @param {string} version 版本号
 * @returns {boolean} 是否有效
 */
export function isValidVersion(version) {
  const reg = /^\d+\.\d+\.\d+$/
  return reg.test(version)
}
