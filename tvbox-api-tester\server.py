#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
TVBox API Web 测试工具 - Python启动服务器
提供Web界面的HTTP服务和后端API代理
"""

import os
import sys
import json
import time
import threading
import webbrowser
from pathlib import Path
from http.server import HTTPServer, SimpleHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import requests
import base64

class TVBoxAPITestServer(SimpleHTTPRequestHandler):
    """自定义HTTP请求处理器"""
    
    def __init__(self, *args, backend_url="http://localhost:8001", **kwargs):
        self.backend_url = backend_url
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        if self.path.startswith('/api/'):
            self.handle_api_request()
        elif self.path.startswith('/proxy/'):
            self.handle_proxy_request()
        else:
            # 处理静态文件
            super().do_GET()
    
    def do_POST(self):
        """处理POST请求"""
        if self.path.startswith('/api/'):
            self.handle_api_request()
        elif self.path.startswith('/proxy/'):
            self.handle_proxy_request()
        else:
            self.send_error(404, "Not Found")
    
    def do_PUT(self):
        """处理PUT请求"""
        if self.path.startswith('/api/') or self.path.startswith('/proxy/'):
            self.handle_api_request()
        else:
            self.send_error(404, "Not Found")
    
    def do_DELETE(self):
        """处理DELETE请求"""
        if self.path.startswith('/api/') or self.path.startswith('/proxy/'):
            self.handle_api_request()
        else:
            self.send_error(404, "Not Found")
    
    def handle_api_request(self):
        """处理API请求代理"""
        try:
            # 构建目标URL
            if self.path.startswith('/proxy/'):
                target_path = self.path[7:]  # 移除 /proxy/ 前缀
            else:
                target_path = self.path
            
            target_url = f"{self.backend_url}{target_path}"
            
            # 获取请求体
            content_length = int(self.headers.get('Content-Length', 0))
            request_body = self.rfile.read(content_length) if content_length > 0 else None
            
            # 构建请求头
            headers = {}
            for key, value in self.headers.items():
                if key.lower() not in ['host', 'content-length']:
                    headers[key] = value
            
            # 处理账号密码认证
            auth_header = self.headers.get('Authorization')
            if auth_header and auth_header.startswith('Basic '):
                # 保持Basic认证
                headers['Authorization'] = auth_header
            elif auth_header and auth_header.startswith('Bearer '):
                # 保持Bearer认证
                headers['Authorization'] = auth_header
            
            # 发送请求到后端
            response = requests.request(
                method=self.command,
                url=target_url,
                headers=headers,
                data=request_body,
                timeout=30,
                verify=False
            )
            
            # 返回响应
            self.send_response(response.status_code)
            
            # 设置响应头
            for key, value in response.headers.items():
                if key.lower() not in ['content-encoding', 'transfer-encoding', 'connection']:
                    self.send_header(key, value)
            
            # 添加CORS头
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
            
            self.end_headers()
            
            # 发送响应体
            if response.content:
                self.wfile.write(response.content)
                
        except requests.exceptions.RequestException as e:
            self.send_error_response(502, f"后端服务连接失败: {str(e)}")
        except Exception as e:
            self.send_error_response(500, f"服务器内部错误: {str(e)}")
    
    def handle_proxy_request(self):
        """处理代理请求"""
        self.handle_api_request()
    
    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
        self.send_header('Access-Control-Max-Age', '86400')
        self.end_headers()
    
    def send_error_response(self, code, message):
        """发送错误响应"""
        self.send_response(code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        error_response = {
            "success": False,
            "error": message,
            "code": code
        }
        
        self.wfile.write(json.dumps(error_response).encode('utf-8'))
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {format % args}")

class TVBoxTestServerManager:
    """测试服务器管理器"""
    
    def __init__(self):
        self.server = None
        self.server_thread = None
        self.port = 8080
        self.backend_url = "http://localhost:8001"
        self.running = False
    
    def check_backend_status(self):
        """检查后端服务状态"""
        try:
            response = requests.get(f"{self.backend_url}/docs", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def find_available_port(self, start_port=8080):
        """查找可用端口"""
        import socket
        
        for port in range(start_port, start_port + 100):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('localhost', port))
                    return port
            except OSError:
                continue
        
        raise RuntimeError("无法找到可用端口")
    
    def create_handler_class(self):
        """创建请求处理器类"""
        backend_url = self.backend_url
        
        class CustomHandler(TVBoxAPITestServer):
            def __init__(self, *args, **kwargs):
                super().__init__(*args, backend_url=backend_url, **kwargs)
        
        return CustomHandler
    
    def start_server(self):
        """启动服务器"""
        try:
            # 查找可用端口
            self.port = self.find_available_port(8080)
            
            # 创建服务器
            handler_class = self.create_handler_class()
            self.server = HTTPServer(('localhost', self.port), handler_class)
            
            # 在单独线程中运行服务器
            self.server_thread = threading.Thread(target=self.server.serve_forever)
            self.server_thread.daemon = True
            self.server_thread.start()
            
            self.running = True
            
            print(f"✅ 测试服务器已启动")
            print(f"📍 服务器地址: http://localhost:{self.port}")
            print(f"🔗 后端代理: {self.backend_url}")
            
            return True
            
        except Exception as e:
            print(f"❌ 启动服务器失败: {str(e)}")
            return False
    
    def stop_server(self):
        """停止服务器"""
        if self.server:
            self.server.shutdown()
            self.server.server_close()
            self.running = False
            print("🛑 服务器已停止")
    
    def open_browser(self):
        """打开浏览器"""
        if self.running:
            url = f"http://localhost:{self.port}"
            print(f"🌐 正在打开浏览器: {url}")
            webbrowser.open(url)
    
    def show_status(self):
        """显示状态信息"""
        print("\n" + "="*60)
        print("📊 TVBox API 测试工具状态")
        print("="*60)
        
        # 检查后端状态
        backend_status = self.check_backend_status()
        backend_status_text = "✅ 在线" if backend_status else "❌ 离线"
        
        print(f"🔧 后端服务: {self.backend_url} - {backend_status_text}")
        print(f"🌐 测试服务器: http://localhost:{self.port} - {'✅ 运行中' if self.running else '❌ 未启动'}")
        
        if not backend_status:
            print("\n⚠️  后端服务未启动，请先启动TVBox Manager Pro后端:")
            print("   cd tvbox-manager-pro/backend")
            print("   python app/main.py")
        
        print("="*60)

def main():
    """主函数"""
    print("🚀 TVBox API Web 测试工具")
    print("基于Python的独立测试服务器")
    print()
    
    # 切换到脚本目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # 创建服务器管理器
    server_manager = TVBoxTestServerManager()
    
    try:
        while True:
            print("\n请选择操作:")
            print("1. 🚀 启动测试服务器")
            print("2. 🌐 打开浏览器")
            print("3. 📊 查看状态")
            print("4. ⚙️  设置后端地址")
            print("5. 🛑 停止服务器")
            print("0. 🚪 退出程序")
            
            choice = input("\n请输入选择 (0-5): ").strip()
            
            if choice == '0':
                break
            elif choice == '1':
                if server_manager.running:
                    print("⚠️  服务器已在运行中")
                else:
                    if server_manager.start_server():
                        # 自动打开浏览器
                        time.sleep(1)
                        server_manager.open_browser()
            elif choice == '2':
                server_manager.open_browser()
            elif choice == '3':
                server_manager.show_status()
            elif choice == '4':
                new_url = input(f"请输入后端地址 (当前: {server_manager.backend_url}): ").strip()
                if new_url:
                    server_manager.backend_url = new_url
                    print(f"✅ 后端地址已更新为: {new_url}")
            elif choice == '5':
                server_manager.stop_server()
            else:
                print("❌ 无效选择，请重新输入")
    
    except KeyboardInterrupt:
        print("\n\n🛑 程序被用户中断")
    
    finally:
        # 清理资源
        server_manager.stop_server()
        print("👋 再见！")

if __name__ == "__main__":
    main()
