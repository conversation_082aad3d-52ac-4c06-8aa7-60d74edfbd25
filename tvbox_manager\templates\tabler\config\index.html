{% extends "tabler/base.html" %}

{% set active_nav = 'config' %}
{% block title %}配置管理 - TVBox Manager{% endblock %}
{% block page_title %}配置管理{% endblock %}

{% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <h2 class="page-title">
          配置管理
        </h2>
        <div class="text-muted mt-1">管理TVBox的配置文件</div>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <a href="{{ url_for('config.add') }}" class="btn btn-primary d-none d-sm-inline-block">
            <i class="ti ti-plus"></i>
            添加配置
          </a>
          <a href="{{ url_for('config.add') }}" class="btn btn-primary d-sm-none btn-icon">
            <i class="ti ti-plus"></i>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    {% if configs %}
    <!-- 配置列表卡片 -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">配置列表</h3>
        <div class="card-actions">
          <a href="#" class="btn btn-outline-secondary btn-sm">
            <i class="ti ti-refresh"></i>
            刷新
          </a>
        </div>
      </div>
      <div class="card-body border-bottom py-3">
        <div class="d-flex">
          <div class="text-muted">
            共 <strong>{{ configs|length }}</strong> 个配置
          </div>
          <div class="ms-auto text-muted">
            搜索:
            <div class="ms-2 d-inline-block">
              <input type="text" class="form-control form-control-sm" id="search-input" aria-label="搜索配置">
            </div>
          </div>
        </div>
      </div>
      <div class="table-responsive">
        <table class="table card-table table-vcenter text-nowrap datatable">
          <thead>
            <tr>
              <th>名称</th>
              <th>类型</th>
              <th>URL</th>
              <th>状态</th>
              <th>更新时间</th>
              <th class="w-1"></th>
            </tr>
          </thead>
          <tbody>
            {% for config in configs %}
            <tr>
              <td>
                <span class="fw-bold">{{ config.name }}</span>
              </td>
              <td>{{ config.type }}</td>
              <td>
                <div class="text-truncate" style="max-width: 200px;" title="{{ config.url }}">
                  {{ config.url }}
                </div>
              </td>
              <td>
                {% if config.status %}
                <span class="badge bg-success-lt">
                  <i class="ti ti-check me-1"></i> 正常
                </span>
                {% else %}
                <span class="badge bg-secondary-lt">
                  <i class="ti ti-x me-1"></i> 停用
                </span>
                {% endif %}
              </td>
              <td>
                {% if config.last_update %}
                <span title="{{ config.last_update }}">{{ config.last_update.strftime('%Y-%m-%d %H:%M') }}</span>
                {% else %}
                <span class="text-muted">从未更新</span>
                {% endif %}
              </td>
              <td>
                <div class="btn-list flex-nowrap">
                  <a href="{{ url_for('config.edit', id=config.id) }}" class="btn btn-icon btn-sm" title="编辑">
                    <i class="ti ti-edit text-primary"></i>
                  </a>
                  <a href="{{ url_for('config.refresh', id=config.id) }}" class="btn btn-icon btn-sm" title="刷新">
                    <i class="ti ti-refresh text-info"></i>
                  </a>
                  <a href="{{ url_for('config.delete', id=config.id) }}" class="btn btn-icon btn-sm" 
                     onclick="return confirm('确认删除此配置?')" title="删除">
                    <i class="ti ti-trash text-danger"></i>
                  </a>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
    {% else %}
    <!-- 空状态卡片 -->
    <div class="card">
      <div class="card-body empty">
        <div class="empty-img">
          <i class="ti ti-settings" style="font-size: 3rem; opacity: 0.5;"></i>
        </div>
        <p class="empty-title">没有可用配置</p>
        <p class="empty-subtitle text-muted">
          您还没有添加任何配置。配置用于管理TVBox的设置，添加后可自定义TVBox的功能和外观。
        </p>
        <div class="empty-action">
          <a href="{{ url_for('config.add') }}" class="btn btn-primary">
            <i class="ti ti-plus me-2"></i>
            添加第一个配置
          </a>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- API接口说明 -->
    <div class="row mt-3">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">API接口说明</h3>
          </div>
          <div class="card-body">
            <p>TVBox Manager提供了以下API接口，可供应用程序或设备直接调用：</p>
            
            <div class="row mt-4">
              <div class="col-md-6">
                <div class="card">
                  <div class="card-body">
                    <h4 class="card-title mb-3">获取默认配置</h4>
                    <div class="card bg-light p-3">
                      <code class="d-block text-wrap">{{ request.host_url }}config/api/default</code>
                    </div>
                    <div class="mt-3 text-muted">
                      <small>使用GET请求获取系统默认配置，返回JSON格式</small>
                    </div>
                    <div class="mt-3">
                      <a href="{{ request.host_url }}config/api/default" target="_blank" class="btn btn-outline-primary btn-sm">
                        <i class="ti ti-external-link me-1"></i>
                        测试请求
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="col-md-6">
                <div class="card">
                  <div class="card-body">
                    <h4 class="card-title mb-3">获取指定配置</h4>
                    <div class="card bg-light p-3">
                      <code class="d-block text-wrap">{{ request.host_url }}config/api/get/{id}</code>
                    </div>
                    <div class="mt-3 text-muted">
                      <small>使用GET请求获取指定ID的配置，返回JSON格式</small>
                    </div>
                    <div class="mt-3">
                      <button class="btn btn-outline-secondary btn-sm disabled">
                        <i class="ti ti-id me-1"></i>
                        需要配置ID
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="alert alert-info mt-4">
              <div class="d-flex">
                <div>
                  <i class="ti ti-info-circle me-2"></i>
                </div>
                <div>
                  <h4>API使用说明</h4>
                  <div class="text-muted">
                    <p>上述API接口可以直接在TVBox客户端中配置使用，确保您的服务器可以被客户端访问。API返回标准JSON格式，包含所有必要的配置信息。</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
  const searchInput = document.getElementById('search-input');
  if(searchInput) {
    searchInput.addEventListener('keyup', function() {
      const searchValue = this.value.toLowerCase();
      const tableRows = document.querySelectorAll('.datatable tbody tr');
      
      tableRows.forEach(function(row) {
        const name = row.querySelector('td:first-child').textContent.toLowerCase();
        const url = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
        
        if(name.includes(searchValue) || url.includes(searchValue)) {
          row.style.display = '';
        } else {
          row.style.display = 'none';
        }
      });
    });
  }
});
</script>
{% endblock %} 

{% set active_nav = 'config' %}
{% block title %}配置管理 - TVBox Manager{% endblock %}
{% block page_title %}配置管理{% endblock %}

{% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <h2 class="page-title">
          配置管理
        </h2>
        <div class="text-muted mt-1">管理TVBox的配置文件</div>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <a href="{{ url_for('config.add') }}" class="btn btn-primary d-none d-sm-inline-block">
            <i class="ti ti-plus"></i>
            添加配置
          </a>
          <a href="{{ url_for('config.add') }}" class="btn btn-primary d-sm-none btn-icon">
            <i class="ti ti-plus"></i>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    {% if configs %}
    <!-- 配置列表卡片 -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">配置列表</h3>
        <div class="card-actions">
          <a href="#" class="btn btn-outline-secondary btn-sm">
            <i class="ti ti-refresh"></i>
            刷新
          </a>
        </div>
      </div>
      <div class="card-body border-bottom py-3">
        <div class="d-flex">
          <div class="text-muted">
            共 <strong>{{ configs|length }}</strong> 个配置
          </div>
          <div class="ms-auto text-muted">
            搜索:
            <div class="ms-2 d-inline-block">
              <input type="text" class="form-control form-control-sm" id="search-input" aria-label="搜索配置">
            </div>
          </div>
        </div>
      </div>
      <div class="table-responsive">
        <table class="table card-table table-vcenter text-nowrap datatable">
          <thead>
            <tr>
              <th>名称</th>
              <th>类型</th>
              <th>URL</th>
              <th>状态</th>
              <th>更新时间</th>
              <th class="w-1"></th>
            </tr>
          </thead>
          <tbody>
            {% for config in configs %}
            <tr>
              <td>
                <span class="fw-bold">{{ config.name }}</span>
              </td>
              <td>{{ config.type }}</td>
              <td>
                <div class="text-truncate" style="max-width: 200px;" title="{{ config.url }}">
                  {{ config.url }}
                </div>
              </td>
              <td>
                {% if config.status %}
                <span class="badge bg-success-lt">
                  <i class="ti ti-check me-1"></i> 正常
                </span>
                {% else %}
                <span class="badge bg-secondary-lt">
                  <i class="ti ti-x me-1"></i> 停用
                </span>
                {% endif %}
              </td>
              <td>
                {% if config.last_update %}
                <span title="{{ config.last_update }}">{{ config.last_update.strftime('%Y-%m-%d %H:%M') }}</span>
                {% else %}
                <span class="text-muted">从未更新</span>
                {% endif %}
              </td>
              <td>
                <div class="btn-list flex-nowrap">
                  <a href="{{ url_for('config.edit', id=config.id) }}" class="btn btn-icon btn-sm" title="编辑">
                    <i class="ti ti-edit text-primary"></i>
                  </a>
                  <a href="{{ url_for('config.refresh', id=config.id) }}" class="btn btn-icon btn-sm" title="刷新">
                    <i class="ti ti-refresh text-info"></i>
                  </a>
                  <a href="{{ url_for('config.delete', id=config.id) }}" class="btn btn-icon btn-sm" 
                     onclick="return confirm('确认删除此配置?')" title="删除">
                    <i class="ti ti-trash text-danger"></i>
                  </a>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
    {% else %}
    <!-- 空状态卡片 -->
    <div class="card">
      <div class="card-body empty">
        <div class="empty-img">
          <i class="ti ti-settings" style="font-size: 3rem; opacity: 0.5;"></i>
        </div>
        <p class="empty-title">没有可用配置</p>
        <p class="empty-subtitle text-muted">
          您还没有添加任何配置。配置用于管理TVBox的设置，添加后可自定义TVBox的功能和外观。
        </p>
        <div class="empty-action">
          <a href="{{ url_for('config.add') }}" class="btn btn-primary">
            <i class="ti ti-plus me-2"></i>
            添加第一个配置
          </a>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- API接口说明 -->
    <div class="row mt-3">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">API接口说明</h3>
          </div>
          <div class="card-body">
            <p>TVBox Manager提供了以下API接口，可供应用程序或设备直接调用：</p>
            
            <div class="row mt-4">
              <div class="col-md-6">
                <div class="card">
                  <div class="card-body">
                    <h4 class="card-title mb-3">获取默认配置</h4>
                    <div class="card bg-light p-3">
                      <code class="d-block text-wrap">{{ request.host_url }}config/api/default</code>
                    </div>
                    <div class="mt-3 text-muted">
                      <small>使用GET请求获取系统默认配置，返回JSON格式</small>
                    </div>
                    <div class="mt-3">
                      <a href="{{ request.host_url }}config/api/default" target="_blank" class="btn btn-outline-primary btn-sm">
                        <i class="ti ti-external-link me-1"></i>
                        测试请求
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="col-md-6">
                <div class="card">
                  <div class="card-body">
                    <h4 class="card-title mb-3">获取指定配置</h4>
                    <div class="card bg-light p-3">
                      <code class="d-block text-wrap">{{ request.host_url }}config/api/get/{id}</code>
                    </div>
                    <div class="mt-3 text-muted">
                      <small>使用GET请求获取指定ID的配置，返回JSON格式</small>
                    </div>
                    <div class="mt-3">
                      <button class="btn btn-outline-secondary btn-sm disabled">
                        <i class="ti ti-id me-1"></i>
                        需要配置ID
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="alert alert-info mt-4">
              <div class="d-flex">
                <div>
                  <i class="ti ti-info-circle me-2"></i>
                </div>
                <div>
                  <h4>API使用说明</h4>
                  <div class="text-muted">
                    <p>上述API接口可以直接在TVBox客户端中配置使用，确保您的服务器可以被客户端访问。API返回标准JSON格式，包含所有必要的配置信息。</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
  const searchInput = document.getElementById('search-input');
  if(searchInput) {
    searchInput.addEventListener('keyup', function() {
      const searchValue = this.value.toLowerCase();
      const tableRows = document.querySelectorAll('.datatable tbody tr');
      
      tableRows.forEach(function(row) {
        const name = row.querySelector('td:first-child').textContent.toLowerCase();
        const url = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
        
        if(name.includes(searchValue) || url.includes(searchValue)) {
          row.style.display = '';
        } else {
          row.style.display = 'none';
        }
      });
    });
  }
});
</script>
{% endblock %} 