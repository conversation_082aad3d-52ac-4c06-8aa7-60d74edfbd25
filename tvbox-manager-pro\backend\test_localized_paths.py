#!/usr/bin/env python3
"""
测试本地化路径访问的脚本
"""
import requests
import json

def test_localized_paths():
    """测试本地化路径访问"""
    print("开始测试本地化路径访问...")
    
    # 测试路径列表
    test_paths = [
        # Spider文件
        "/localized/interface_1_真实TVBox配置/spider/f0729.jar",
        
        # JavaScript文件
        "/localized/interface_1_真实TVBox配置/js/drpy2.js",
        "/localized/interface_1_真实TVBox配置/js/drpy2.min.js",
        "/localized/interface_1_真实TVBox配置/js/huya2.js",
        "/localized/interface_1_真实TVBox配置/js/斗鱼直播.js",
        "/localized/interface_1_真实TVBox配置/js/有声小说吧.js",
        
        # URL编码的文件名
        "/localized/interface_1_真实TVBox配置/js/%E5%85%94%E5%B0%8F%E8%B4%9D.js",
        
        # 直播源文件
        "/localized/interface_1_真实TVBox配置/live/tv.m3u",
        "/localized/interface_1_真实TVBox配置/live/20250425-868403-********************************.m3u",
        
        # HTML文件
        "/localized/interface_1_真实TVBox配置/js/file.html",
    ]
    
    base_url = "http://localhost:3000"
    success_count = 0
    total_count = len(test_paths)
    
    for path in test_paths:
        try:
            url = base_url + path
            print(f"\n测试: {path}")
            
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                print(f"  ✅ 成功 (状态码: {response.status_code})")
                print(f"  📄 内容类型: {response.headers.get('content-type', 'unknown')}")
                print(f"  📏 文件大小: {len(response.content)} bytes")
                success_count += 1
            else:
                print(f"  ❌ 失败 (状态码: {response.status_code})")
                
        except requests.exceptions.RequestException as e:
            print(f"  ❌ 请求异常: {e}")
    
    print(f"\n📊 测试结果:")
    print(f"  总计: {total_count} 个路径")
    print(f"  成功: {success_count} 个")
    print(f"  失败: {total_count - success_count} 个")
    print(f"  成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print(f"\n🎉 所有路径测试通过！本地化功能完全正常工作！")
    else:
        print(f"\n⚠️  有 {total_count - success_count} 个路径测试失败")

if __name__ == "__main__":
    test_localized_paths()
