{"version": 3, "file": "transfer.js", "sources": ["../../../../../../packages/components/transfer/src/transfer.ts"], "sourcesContent": ["import { isNil } from 'lodash-unified'\nimport {\n  buildProps,\n  definePropType,\n  isArray,\n  mutable,\n} from '@element-plus/utils'\nimport { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\n\nimport type {\n  ExtractPropTypes,\n  h as H,\n  VNode,\n  __ExtractPublicPropTypes,\n} from 'vue'\nimport type Transfer from './transfer.vue'\n\nexport type TransferKey = string | number\nexport type TransferDirection = 'left' | 'right'\n\nexport type TransferDataItem = Record<string, any>\n\nexport type renderContent = (\n  h: typeof H,\n  option: TransferDataItem\n) => VNode | VNode[]\n\nexport interface TransferFormat {\n  noChecked?: string\n  hasChecked?: string\n}\n\nexport interface TransferPropsAlias {\n  label?: string\n  key?: string\n  disabled?: string\n}\n\nexport interface TransferCheckedState {\n  leftChecked: TransferKey[]\n  rightChecked: TransferKey[]\n}\n\nexport const LEFT_CHECK_CHANGE_EVENT = 'left-check-change'\nexport const RIGHT_CHECK_CHANGE_EVENT = 'right-check-change'\n\nexport const transferProps = buildProps({\n  /**\n   * @description data source\n   */\n  data: {\n    type: definePropType<TransferDataItem[]>(Array),\n    default: () => [],\n  },\n  /**\n   * @description custom list titles\n   */\n  titles: {\n    type: definePropType<[string, string]>(Array),\n    default: () => [],\n  },\n  /**\n   * @description custom button texts\n   */\n  buttonTexts: {\n    type: definePropType<[string, string]>(Array),\n    default: () => [],\n  },\n  /**\n   * @description placeholder for the filter input\n   */\n  filterPlaceholder: String,\n  /**\n   * @description custom filter method\n   */\n  filterMethod: {\n    type: definePropType<(query: string, item: TransferDataItem) => boolean>(\n      Function\n    ),\n  },\n  /**\n   * @description key array of initially checked data items of the left list\n   */\n  leftDefaultChecked: {\n    type: definePropType<TransferKey[]>(Array),\n    default: () => [],\n  },\n  /**\n   * @description key array of initially checked data items of the right list\n   */\n  rightDefaultChecked: {\n    type: definePropType<TransferKey[]>(Array),\n    default: () => [],\n  },\n  /**\n   * @description custom render function for data items\n   */\n  renderContent: {\n    type: definePropType<renderContent>(Function),\n  },\n  /**\n   * @description binding value\n   */\n  modelValue: {\n    type: definePropType<TransferKey[]>(Array),\n    default: () => [],\n  },\n  /**\n   * @description texts for checking status in list header\n   */\n  format: {\n    type: definePropType<TransferFormat>(Object),\n    default: () => ({}),\n  },\n  /**\n   * @description whether Transfer is filterable\n   */\n  filterable: Boolean,\n  /**\n   * @description prop aliases for data source\n   */\n  props: {\n    type: definePropType<TransferPropsAlias>(Object),\n    default: () =>\n      mutable({\n        label: 'label',\n        key: 'key',\n        disabled: 'disabled',\n      } as const),\n  },\n  /**\n   * @description order strategy for elements in the target list. If set to `original`, the elements will keep the same order as the data source. If set to `push`, the newly added elements will be pushed to the bottom. If set to `unshift`, the newly added elements will be inserted on the top\n   */\n  targetOrder: {\n    type: String,\n    values: ['original', 'push', 'unshift'],\n    default: 'original',\n  },\n  /**\n   * @description whether to trigger form validation\n   */\n  validateEvent: {\n    type: Boolean,\n    default: true,\n  },\n} as const)\nexport type TransferProps = ExtractPropTypes<typeof transferProps>\nexport type TransferPropsPublic = __ExtractPublicPropTypes<typeof transferProps>\n\nexport const transferCheckedChangeFn = (\n  value: TransferKey[],\n  movedKeys?: TransferKey[]\n) => [value, movedKeys].every(isArray) || (isArray(value) && isNil(movedKeys))\n\nexport const transferEmits = {\n  [CHANGE_EVENT]: (\n    value: TransferKey[],\n    direction: TransferDirection,\n    movedKeys: TransferKey[]\n  ) =>\n    [value, movedKeys].every(isArray) && ['left', 'right'].includes(direction),\n  [UPDATE_MODEL_EVENT]: (value: TransferKey[]) => isArray(value),\n  [LEFT_CHECK_CHANGE_EVENT]: transferCheckedChangeFn,\n  [RIGHT_CHECK_CHANGE_EVENT]: transferCheckedChangeFn,\n}\nexport type TransferEmits = typeof transferEmits\n\nexport type TransferInstance = InstanceType<typeof Transfer> & unknown\n"], "names": ["buildProps", "definePropType", "mutable", "isArray", "isNil", "CHANGE_EVENT", "UPDATE_MODEL_EVENT"], "mappings": ";;;;;;;;;;AAQY,MAAC,uBAAuB,GAAG,oBAAoB;AAC/C,MAAC,wBAAwB,GAAG,qBAAqB;AACjD,MAAC,aAAa,GAAGA,kBAAU,CAAC;AACxC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAEC,sBAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,MAAM,EAAE;AACrB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAEA,sBAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,MAAM,EAAE;AACrB,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAEA,sBAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,MAAM,EAAE;AACrB,GAAG;AACH,EAAE,iBAAiB,EAAE,MAAM;AAC3B,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAEA,sBAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,kBAAkB,EAAE;AACtB,IAAI,IAAI,EAAEA,sBAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,MAAM,EAAE;AACrB,GAAG;AACH,EAAE,mBAAmB,EAAE;AACvB,IAAI,IAAI,EAAEA,sBAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,MAAM,EAAE;AACrB,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAEA,sBAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAEA,sBAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,MAAM,EAAE;AACrB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAEA,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,OAAO,EAAE,CAAC;AACvB,GAAG;AACH,EAAE,UAAU,EAAE,OAAO;AACrB,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAEA,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,MAAMC,kBAAO,CAAC;AAC3B,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,GAAG,EAAE,KAAK;AAChB,MAAM,QAAQ,EAAE,UAAU;AAC1B,KAAK,CAAC;AACN,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,SAAS,CAAC;AAC3C,IAAI,OAAO,EAAE,UAAU;AACvB,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,uBAAuB,GAAG,CAAC,KAAK,EAAE,SAAS,KAAK,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,KAAK,CAACC,cAAO,CAAC,IAAIA,cAAO,CAAC,KAAK,CAAC,IAAIC,mBAAK,CAAC,SAAS,EAAE;AACzH,MAAC,aAAa,GAAG;AAC7B,EAAE,CAACC,kBAAY,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,KAAK,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,KAAK,CAACF,cAAO,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC;AAC7H,EAAE,CAACG,wBAAkB,GAAG,CAAC,KAAK,KAAKH,cAAO,CAAC,KAAK,CAAC;AACjD,EAAE,CAAC,uBAAuB,GAAG,uBAAuB;AACpD,EAAE,CAAC,wBAAwB,GAAG,uBAAuB;AACrD;;;;;;;;"}