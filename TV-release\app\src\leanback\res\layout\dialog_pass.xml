<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="horizontal"
    android:padding="16dp">

    <com.google.android.material.textfield.TextInputEditText
        android:id="@+id/pass"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:layout_weight="1"
        android:background="@null"
        android:hint="@string/live_pass"
        android:imeOptions="actionDone"
        android:inputType="textPassword"
        android:maxLength="20"
        android:singleLine="true"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/positive"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/selector_text"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:gravity="center"
        android:singleLine="true"
        android:text="@string/dialog_positive"
        android:textColor="@color/white"
        android:textSize="14sp" />

</LinearLayout>