{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/result/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Result from './src/result.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElResult: SFCWithInstall<typeof Result> = withInstall(Result)\n\nexport default ElResult\n\nexport * from './src/result'\n"], "names": [], "mappings": ";;;;AAEY,MAAC,QAAQ,GAAG,WAAW,CAAC,MAAM;;;;"}