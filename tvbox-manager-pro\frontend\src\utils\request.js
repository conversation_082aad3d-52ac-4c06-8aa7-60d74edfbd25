import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { getToken } from '@/utils/auth'
import router from '@/router'

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 添加认证令牌
    const token = getToken()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }
    
    return config
  },
  error => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 如果是文件下载，直接返回
    if (response.config.responseType === 'blob') {
      return response
    }

    // 直接返回响应，让具体的API调用处理数据格式
    return response
  },
  async error => {
    console.error('响应拦截器错误:', error)
    
    const { response } = error
    
    if (!response) {
      ElMessage.error('网络连接异常，请检查网络设置')
      return Promise.reject(error)
    }
    
    const { status, data } = response
    
    switch (status) {
      case 401:
        // 未授权，尝试刷新令牌
        const userStore = useUserStore()
        
        if (userStore.refreshToken) {
          try {
            await userStore.refreshAccessToken()
            // 重新发送原请求
            return service.request(error.config)
          } catch (refreshError) {
            // 刷新失败，跳转到登录页
            ElMessageBox.confirm(
              '登录状态已过期，请重新登录',
              '系统提示',
              {
                confirmButtonText: '重新登录',
                cancelButtonText: '取消',
                type: 'warning'
              }
            ).then(() => {
              userStore.logout().then(() => {
                router.push('/login')
              })
            })
          }
        } else {
          ElMessage.error('登录状态已过期，请重新登录')
          router.push('/login')
        }
        break
        
      case 403:
        ElMessage.error('权限不足，无法访问该资源')
        break
        
      case 404:
        ElMessage.error('请求的资源不存在')
        break
        
      case 422:
        // 表单验证错误
        if (data.detail && Array.isArray(data.detail)) {
          const errors = data.detail.map(item => item.msg || item.message || item).join(', ')
          ElMessage.error(`参数验证失败: ${errors}`)
        } else {
          ElMessage.error(data.detail || data.message || '参数验证失败')
        }
        break
        
      case 429:
        ElMessage.error('请求过于频繁，请稍后再试')
        break
        
      case 500:
        ElMessage.error('服务器内部错误，请稍后再试')
        break
        
      case 502:
      case 503:
      case 504:
        ElMessage.error('服务暂时不可用，请稍后再试')
        break
        
      default:
        ElMessage.error(data.detail || data.message || `请求失败 (${status})`)
    }
    
    return Promise.reject(error)
  }
)

// 请求方法封装
export const request = {
  get(url, params = {}, config = {}) {
    return service.get(url, { params, ...config })
  },
  
  post(url, data = {}, config = {}) {
    return service.post(url, data, config)
  },
  
  put(url, data = {}, config = {}) {
    return service.put(url, data, config)
  },
  
  patch(url, data = {}, config = {}) {
    return service.patch(url, data, config)
  },
  
  delete(url, config = {}) {
    return service.delete(url, config)
  },
  
  upload(url, formData, config = {}) {
    return service.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      ...config
    })
  },
  
  download(url, params = {}, config = {}) {
    return service.get(url, {
      params,
      responseType: 'blob',
      ...config
    })
  }
}

export default service
