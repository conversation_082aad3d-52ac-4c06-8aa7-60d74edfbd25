{"version": 3, "file": "de.mjs", "sources": ["../../../../../packages/locale/lang/de.ts"], "sourcesContent": ["export default {\n  name: 'de',\n  el: {\n    breadcrumb: {\n      label: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: '<PERSON><PERSON>',\n    },\n    datepicker: {\n      now: '<PERSON>zt',\n      today: 'Heute',\n      cancel: 'Abbrechen',\n      clear: '<PERSON><PERSON>',\n      confirm: 'OK',\n      selectDate: 'Datum wählen',\n      selectTime: 'Uhrzeit wählen',\n      startDate: 'Startdatum',\n      startTime: 'Startzeit',\n      endDate: 'Enddatum',\n      endTime: 'Endzeit',\n      prevYear: 'Letztes Jahr',\n      nextYear: 'Nächtes Jahr',\n      prevMonth: 'Letzter Monat',\n      nextMonth: 'Nächster Monat',\n      day: 'Tag',\n      week: 'Woche',\n      month: '<PERSON><PERSON>',\n      year: '',\n      month1: 'Januar',\n      month2: 'Februar',\n      month3: 'M<PERSON>rz',\n      month4: 'April',\n      month5: 'Mai',\n      month6: 'Juni',\n      month7: 'Juli',\n      month8: 'August',\n      month9: 'September',\n      month10: 'Okto<PERSON>',\n      month11: 'November',\n      month12: 'Dezember',\n      weeks: {\n        sun: 'So',\n        mon: 'Mo',\n        tue: 'Di',\n        wed: 'Mi',\n        thu: 'Do',\n        fri: 'Fr',\n        sat: 'Sa',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Feb',\n        mar: '<PERSON><PERSON>r',\n        apr: 'Apr',\n        may: '<PERSON>',\n        jun: 'Jun',\n        jul: 'Jul',\n        aug: 'Aug',\n        sep: 'Sep',\n        oct: 'Okt',\n        nov: 'Nov',\n        dec: 'Dez',\n      },\n    },\n    select: {\n      loading: 'Lädt.',\n      noMatch: 'Nichts gefunden.',\n      noData: 'Keine Daten',\n      placeholder: 'Daten wählen',\n    },\n    mention: {\n      loading: 'Lädt.',\n    },\n    cascader: {\n      noMatch: 'Nichts gefunden.',\n      loading: 'Lädt.',\n      placeholder: 'Daten wählen',\n      noData: 'Keine Daten',\n    },\n    pagination: {\n      goto: 'Gehe zu',\n      pagesize: ' pro Seite',\n      total: 'Gesamt {total}',\n      pageClassifier: '',\n      page: 'Seite',\n      prev: 'Zur vorherigen Seite gehen',\n      next: 'Zur nächsten Seite gehen',\n      currentPage: 'Seite {pager}',\n      prevPages: 'Vorherige {pager} Seiten',\n      nextPages: 'Nächste {pager} Seiten',\n    },\n    messagebox: {\n      confirm: 'OK',\n      cancel: 'Abbrechen',\n      error: 'Fehler',\n    },\n    upload: {\n      deleteTip: 'Klicke löschen zum entfernen',\n      delete: 'Löschen',\n      preview: 'Vorschau',\n      continue: 'Fortsetzen',\n    },\n    table: {\n      emptyText: 'Keine Daten',\n      confirmFilter: 'Anwenden',\n      resetFilter: 'Zurücksetzen',\n      clearFilter: 'Alles ',\n      sumText: 'Summe',\n    },\n    tour: {\n      next: 'Weiter',\n      previous: 'Zurück',\n      finish: 'Fertig',\n    },\n    tree: {\n      emptyText: 'Keine Einträge',\n    },\n    transfer: {\n      noMatch: 'Nichts gefunden.',\n      noData: 'Keine Einträge',\n      titles: ['Liste 1', 'Liste 2'],\n      filterPlaceholder: 'Einträge filtern',\n      noCheckedFormat: '{total} Einträge',\n      hasCheckedFormat: '{checked}/{total} ausgewählt',\n    },\n    image: {\n      error: 'FEHLGESCHLAGEN',\n    },\n    pageHeader: {\n      title: 'Zurück',\n    },\n    popconfirm: {\n      confirmButtonText: 'Ja',\n      cancelButtonText: 'Nein',\n    },\n    carousel: {\n      leftArrow: 'Karussell-Pfeil links',\n      rightArrow: 'Karussell-Pfeil rechts',\n      indicator: 'Karussell zu Index {index} wechseln',\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,eAAe;AAC5B,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,OAAO;AAClB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,KAAK,EAAE,QAAQ;AACrB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,UAAU,EAAE,iBAAiB;AACnC,MAAM,UAAU,EAAE,mBAAmB;AACrC,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,SAAS,EAAE,WAAW;AAC5B,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,QAAQ,EAAE,cAAc;AAC9B,MAAM,QAAQ,EAAE,iBAAiB;AACjC,MAAM,SAAS,EAAE,eAAe;AAChC,MAAM,SAAS,EAAE,mBAAmB;AACpC,MAAM,GAAG,EAAE,KAAK;AAChB,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,kBAAkB;AACjC,MAAM,MAAM,EAAE,aAAa;AAC3B,MAAM,WAAW,EAAE,iBAAiB;AACpC,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,UAAU;AACzB,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,kBAAkB;AACjC,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,WAAW,EAAE,iBAAiB;AACpC,MAAM,MAAM,EAAE,aAAa;AAC3B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,QAAQ,EAAE,YAAY;AAC5B,MAAM,KAAK,EAAE,gBAAgB;AAC7B,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,IAAI,EAAE,4BAA4B;AACxC,MAAM,IAAI,EAAE,6BAA6B;AACzC,MAAM,WAAW,EAAE,eAAe;AAClC,MAAM,SAAS,EAAE,0BAA0B;AAC3C,MAAM,SAAS,EAAE,2BAA2B;AAC5C,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,iCAAiC;AAClD,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,QAAQ,EAAE,YAAY;AAC5B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,aAAa;AAC9B,MAAM,aAAa,EAAE,UAAU;AAC/B,MAAM,WAAW,EAAE,iBAAiB;AACpC,MAAM,WAAW,EAAE,QAAQ;AAC3B,MAAM,OAAO,EAAE,OAAO;AACtB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,QAAQ;AACpB,MAAM,QAAQ,EAAE,WAAW;AAC3B,MAAM,MAAM,EAAE,QAAQ;AACtB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,mBAAmB;AACpC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,kBAAkB;AACjC,MAAM,MAAM,EAAE,mBAAmB;AACjC,MAAM,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;AACpC,MAAM,iBAAiB,EAAE,qBAAqB;AAC9C,MAAM,eAAe,EAAE,qBAAqB;AAC5C,MAAM,gBAAgB,EAAE,iCAAiC;AACzD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,gBAAgB;AAC7B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,WAAW;AACxB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,IAAI;AAC7B,MAAM,gBAAgB,EAAE,MAAM;AAC9B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,uBAAuB;AACxC,MAAM,UAAU,EAAE,wBAAwB;AAC1C,MAAM,SAAS,EAAE,qCAAqC;AACtD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}