#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
安全认证模块
"""

from datetime import datetime, timedelta
from typing import Optional, Union
from passlib.context import CryptContext
from fastapi import HTTPException, status, Depends, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>earer, HTTPAuthorizationCredentials, HTTPBasic, HTTPBasicCredentials
import secrets
import logging
import base64

try:
    from jose import jwt
except ImportError:
    try:
        import jwt
    except ImportError:
        # 如果都没有，创建一个简单的JWT实现
        class SimpleJWT:
            @staticmethod
            def encode(payload, key, algorithm='HS256'):
                import json
                import base64
                import hmac
                import hashlib

                header = {"alg": algorithm, "typ": "JWT"}
                header_encoded = base64.urlsafe_b64encode(json.dumps(header).encode()).decode().rstrip('=')
                payload_encoded = base64.urlsafe_b64encode(json.dumps(payload, default=str).encode()).decode().rstrip('=')

                message = f"{header_encoded}.{payload_encoded}"
                signature = base64.urlsafe_b64encode(
                    hmac.new(key.encode(), message.encode(), hashlib.sha256).digest()
                ).decode().rstrip('=')

                return f"{message}.{signature}"

            @staticmethod
            def decode(token, key, algorithms=None):
                import json
                import base64
                parts = token.split('.')
                if len(parts) != 3:
                    raise Exception("Invalid token")

                payload_encoded = parts[1]
                # 添加填充
                payload_encoded += '=' * (4 - len(payload_encoded) % 4)
                payload = json.loads(base64.urlsafe_b64decode(payload_encoded))

                # 简单验证（生产环境需要更严格的验证）
                exp = payload.get('exp')
                if exp and datetime.fromtimestamp(exp) < datetime.utcnow():
                    raise Exception("Token expired")

                return payload

        jwt = SimpleJWT()

from app.core.config import settings

logger = logging.getLogger(__name__)

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT Bearer认证
security = HTTPBearer()

# Basic认证
basic_security = HTTPBasic()

class SecurityManager:
    """安全管理器"""
    
    def __init__(self):
        self.secret_key = settings.SECRET_KEY
        self.algorithm = settings.ALGORITHM
        self.access_token_expire_minutes = settings.ACCESS_TOKEN_EXPIRE_MINUTES
        self.refresh_token_expire_minutes = settings.REFRESH_TOKEN_EXPIRE_MINUTES
    
    def create_password_hash(self, password: str) -> str:
        """创建密码哈希"""
        return pwd_context.hash(password)
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return pwd_context.verify(plain_password, hashed_password)
    
    def create_access_token(
        self, 
        data: dict, 
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """创建访问令牌"""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        
        to_encode.update({
            "exp": expire,
            "type": "access",
            "iat": datetime.utcnow()
        })
        
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def create_refresh_token(
        self, 
        data: dict, 
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """创建刷新令牌"""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.refresh_token_expire_minutes)
        
        to_encode.update({
            "exp": expire,
            "type": "refresh",
            "iat": datetime.utcnow()
        })
        
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def verify_token(self, token: str, token_type: str = "access") -> dict:
        """验证令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            # 检查令牌类型
            if payload.get("type") != token_type:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="令牌类型错误"
                )
            
            # 检查过期时间
            exp = payload.get("exp")
            if exp is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="令牌格式错误"
                )
            
            if datetime.utcnow() > datetime.fromtimestamp(exp):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="令牌已过期"
                )
            
            return payload
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌已过期"
            )
        except jwt.JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌验证失败"
            )
    
    def generate_api_key(self) -> str:
        """生成API密钥"""
        return secrets.token_urlsafe(32)
    
    def generate_reset_token(self) -> str:
        """生成重置令牌"""
        return secrets.token_urlsafe(16)

# 创建全局安全管理器实例
security_manager = SecurityManager()

# 便捷函数
def create_password_hash(password: str) -> str:
    """创建密码哈希"""
    return security_manager.create_password_hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return security_manager.verify_password(plain_password, hashed_password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """创建访问令牌"""
    return security_manager.create_access_token(data, expires_delta)

def create_refresh_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """创建刷新令牌"""
    return security_manager.create_refresh_token(data, expires_delta)

def verify_token(token: str, token_type: str = "access") -> dict:
    """验证令牌"""
    return security_manager.verify_token(token, token_type)

def get_current_user_id(credentials: HTTPAuthorizationCredentials = Depends(security)) -> int:
    """获取当前用户ID"""
    token = credentials.credentials
    payload = verify_token(token)
    user_id = payload.get("sub")
    
    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的令牌"
        )
    
    return int(user_id)

def get_current_user_payload(credentials: HTTPAuthorizationCredentials = Depends(security)) -> dict:
    """获取当前用户载荷"""
    token = credentials.credentials
    return verify_token(token)

# Basic认证相关函数
def authenticate_user_basic(email: str, password: str, db) -> Optional[dict]:
    """Basic认证用户验证"""
    from app.services.user_service import UserService

    user_service = UserService()
    user = user_service.authenticate_user(db, email, password)

    if user:
        return {
            "id": user.id,
            "email": user.email,
            "username": user.username,
            "role": user.role,
            "is_active": user.is_active
        }
    return None

def get_current_user_basic(
    request: Request,
    credentials: HTTPBasicCredentials = Depends(basic_security)
) -> dict:
    """通过Basic认证获取当前用户"""
    from app.core.database import get_db

    # 获取数据库会话
    db = next(get_db())

    try:
        user = authenticate_user_basic(credentials.username, credentials.password, db)

        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Basic"},
            )

        if not user["is_active"]:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户账号已被禁用",
                headers={"WWW-Authenticate": "Basic"},
            )

        return user
    finally:
        db.close()

def get_current_user_id_basic(user: dict = Depends(get_current_user_basic)) -> int:
    """通过Basic认证获取当前用户ID"""
    return user["id"]

def get_current_user_flexible(request: Request) -> dict:
    """灵活的用户认证，支持Bearer和Basic两种方式"""
    from app.core.database import get_db

    authorization = request.headers.get("Authorization")

    if not authorization:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="缺少认证信息",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # 获取数据库会话
    db = next(get_db())

    try:
        if authorization.startswith("Bearer "):
            # JWT Bearer认证
            token = authorization[7:]  # 移除 "Bearer " 前缀
            payload = verify_token(token)
            user_id = payload.get("sub")

            if user_id is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="无效的令牌"
                )

            # 从数据库获取用户信息
            from app.services.user_service import UserService
            user_service = UserService()
            user = user_service.get_user_by_id(db, int(user_id))

            if not user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="用户不存在"
                )

            return {
                "id": user.id,
                "email": user.email,
                "username": user.username,
                "role": user.role,
                "is_active": user.is_active
            }

        elif authorization.startswith("Basic "):
            # Basic认证
            try:
                credentials = authorization[6:]  # 移除 "Basic " 前缀
                decoded = base64.b64decode(credentials).decode('utf-8')
                email, password = decoded.split(':', 1)

                user = authenticate_user_basic(email, password, db)

                if not user:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="用户名或密码错误",
                        headers={"WWW-Authenticate": "Basic"},
                    )

                return user

            except (ValueError, UnicodeDecodeError):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Basic认证格式错误",
                    headers={"WWW-Authenticate": "Basic"},
                )
        else:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="不支持的认证方式",
                headers={"WWW-Authenticate": "Bearer"},
            )
    finally:
        db.close()

def get_current_user(request: Request) -> dict:
    """获取当前用户（支持多种认证方式）"""
    return get_current_user_flexible(request)

def get_current_user_id(request: Request) -> int:
    """获取当前用户ID（支持多种认证方式）"""
    user = get_current_user_flexible(request)
    return user["id"]

# API密钥验证
def verify_api_key(api_key: str) -> bool:
    """验证API密钥"""
    # 这里应该从数据库中验证API密钥
    # 暂时返回True，实际实现时需要查询数据库
    return True

class RoleChecker:
    """角色检查器"""

    def __init__(self, allowed_roles: list):
        self.allowed_roles = allowed_roles

    def __call__(self, current_user_payload: dict = Depends(get_current_user_payload)):
        user_roles = current_user_payload.get("roles", [])

        if not any(role in self.allowed_roles for role in user_roles):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足"
            )

        return current_user_payload

# 角色权限装饰器
def require_roles(roles: list):
    """要求特定角色"""
    return RoleChecker(roles)

# 常用角色检查器
require_admin = require_roles(["admin"])
require_user = require_roles(["user", "admin"])
require_manager = require_roles(["manager", "admin"])

# 权限常量
class Permissions:
    """权限常量"""
    # 用户管理权限
    USER_CREATE = "user:create"
    USER_READ = "user:read"
    USER_UPDATE = "user:update"
    USER_DELETE = "user:delete"

    # 接口管理权限
    INTERFACE_CREATE = "interface:create"
    INTERFACE_READ = "interface:read"
    INTERFACE_UPDATE = "interface:update"
    INTERFACE_DELETE = "interface:delete"

    # 配置管理权限
    CONFIG_CREATE = "config:create"
    CONFIG_READ = "config:read"
    CONFIG_UPDATE = "config:update"
    CONFIG_DELETE = "config:delete"

    # 系统管理权限
    SYSTEM_SETTINGS = "system:settings"
    SYSTEM_LOGS = "system:logs"
    SYSTEM_BACKUP = "system:backup"
