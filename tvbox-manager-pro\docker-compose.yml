version: '3.8'

services:
  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: tvbox-backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=sqlite:///./data/tvbox.db
      - SECRET_KEY=your-secret-key-change-in-production
      - REDIS_URL=redis://redis:6379/0
      - CORS_ORIGINS=http://localhost:3000,http://localhost:8080
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - tvbox-network

  # 前端Web服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: tvbox-frontend
    ports:
      - "3000:80"
    environment:
      - VITE_API_BASE_URL=http://localhost:8000
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - tvbox-network

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: tvbox-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - tvbox-network

  # MySQL数据库（可选，生产环境推荐）
  mysql:
    image: mysql:8.0
    container_name: tvbox-mysql
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=rootpassword
      - MYSQL_DATABASE=tvbox_manager
      - MYSQL_USER=tvbox
      - MYSQL_PASSWORD=tvboxpassword
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    networks:
      - tvbox-network
    profiles:
      - mysql

  # Nginx反向代理（生产环境）
  nginx:
    image: nginx:alpine
    container_name: tvbox-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - frontend
    restart: unless-stopped
    networks:
      - tvbox-network
    profiles:
      - production

volumes:
  redis_data:
  mysql_data:

networks:
  tvbox-network:
    driver: bridge
