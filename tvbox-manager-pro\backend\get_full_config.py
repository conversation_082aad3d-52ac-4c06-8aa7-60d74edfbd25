#!/usr/bin/env python3
"""
获取完整配置
"""
import sqlite3
import os
import json

def get_full_config():
    """获取完整配置"""
    db_path = "data/tvbox.db"
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有接口的完整配置
        cursor.execute("""
            SELECT id, name, url, config_content
            FROM interface_sources 
            WHERE config_content IS NOT NULL AND config_content != ''
            ORDER BY id
        """)
        
        results = cursor.fetchall()
        for result in results:
            interface_id, name, url, config_content = result
            print(f"\n接口ID: {interface_id}")
            print(f"接口名称: {name}")
            print(f"接口URL: {url}")
            
            if config_content:
                try:
                    config_data = json.loads(config_content)
                    spider = config_data.get('spider', 'N/A')
                    print(f"Spider: {spider}")
                    
                    # 检查sites数量
                    sites = config_data.get('sites', [])
                    print(f"Sites数量: {len(sites)}")
                    
                    # 检查是否是真实配置
                    if spider and isinstance(spider, str):
                        if spider.startswith('http') and 'example.com' not in spider:
                            print("✅ 这看起来是真实配置")
                            
                            # 显示前几个sites的信息
                            for i, site in enumerate(sites[:3]):
                                api = site.get('api', 'N/A')
                                jar = site.get('jar', 'N/A')
                                print(f"  Site {i+1}: api={api}, jar={jar}")
                                
                        elif spider.startswith('./'):
                            print("📁 这是本地化配置")
                        elif 'example.com' in spider:
                            print("🧪 这是测试配置")
                        else:
                            print("❓ 配置类型未知")
                            
                except Exception as e:
                    print(f"配置解析失败: {e}")
                    print(f"配置内容前200字符: {config_content[:200]}")
            
            print("-" * 80)
            
    except Exception as e:
        print(f"检查失败: {str(e)}")
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    get_full_config()
