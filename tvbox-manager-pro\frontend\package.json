{"name": "tvbox-manager-pro-frontend", "version": "1.0.0", "description": "TVBox Manager Pro 前端应用", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.3.8", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "echarts": "^5.4.3", "vue-echarts": "^6.6.1", "dayjs": "^1.11.10", "nprogress": "^0.2.0", "js-cookie": "^3.0.5", "lodash-es": "^4.17.21", "mitt": "^3.0.1"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "vite": "^5.0.0", "@vue/eslint-config-prettier": "^8.0.0", "@rushstack/eslint-patch": "^1.3.3", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "prettier": "^3.0.3", "sass": "^1.69.5", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.25.2", "unplugin-element-plus": "^0.8.0"}, "keywords": ["tvbox", "manager", "vue3", "element-plus", "vite"], "author": "TVBox Manager Pro Team", "license": "MIT"}