#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Web应用初始化脚本
检查并创建Web应用所需的目录结构
"""

import os
import sys
import shutil
import logging

def init_web_app():
    """初始化Web应用目录结构"""
    logger = logging.getLogger('tvbox_init')
    logger.info("初始化Web应用目录结构")
    
    # 获取当前模块所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    logger.debug(f"当前目录: {current_dir}")
    
    # Web应用目录
    web_dir = os.path.join(current_dir, 'web')
    templates_dir = os.path.join(web_dir, 'templates')
    static_dir = os.path.join(web_dir, 'static')
    
    # 创建目录
    for directory in [web_dir, templates_dir, static_dir]:
        if not os.path.exists(directory):
            logger.info(f"创建目录: {directory}")
            os.makedirs(directory, exist_ok=True)
    
    # 创建子目录
    for subdir in ['css', 'js', 'img']:
        subdir_path = os.path.join(static_dir, subdir)
        if not os.path.exists(subdir_path):
            logger.info(f"创建子目录: {subdir_path}")
            os.makedirs(subdir_path, exist_ok=True)
    
    # 检查模板文件
    required_templates = ['base.html', 'index.html', 'configs.html', 'decrypt.html', 'resources.html', '404.html']
    missing_templates = []
    
    for template in required_templates:
        template_path = os.path.join(templates_dir, template)
        if not os.path.exists(template_path):
            missing_templates.append(template)
    
    if missing_templates:
        logger.warning(f"缺少模板文件: {', '.join(missing_templates)}")
    else:
        logger.info("所有必需模板文件已存在")
    
    # 检查静态文件
    required_static = ['css/style.css', 'js/main.js']
    missing_static = []
    
    for static_file in required_static:
        static_path = os.path.join(static_dir, static_file)
        if not os.path.exists(static_path):
            missing_static.append(static_file)
    
    if missing_static:
        logger.warning(f"缺少静态文件: {', '.join(missing_static)}")
    else:
        logger.info("所有必需静态文件已存在")
    
    # 打印目录结构
    logger.info("Web应用目录结构:")
    _print_dir_tree(web_dir)

def _print_dir_tree(directory, indent=0):
    """打印目录树结构"""
    logger = logging.getLogger('tvbox_init')
    if indent == 0:
        logger.info(directory)
    
    for item in sorted(os.listdir(directory)):
        item_path = os.path.join(directory, item)
        if os.path.isdir(item_path):
            logger.info("  " * indent + "├── " + item + "/")
            _print_dir_tree(item_path, indent + 1)
        else:
            logger.info("  " * indent + "├── " + item)

if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    init_web_app() 