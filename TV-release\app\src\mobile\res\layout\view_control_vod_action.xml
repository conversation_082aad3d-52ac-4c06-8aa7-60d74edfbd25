<?xml version="1.0" encoding="utf-8"?>
<HorizontalScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:fillViewport="true"
    android:paddingStart="8dp"
    android:paddingEnd="8dp"
    android:scrollbars="none">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/player"
            style="@style/Control.Action"
            android:text="@string/play_exo" />

        <TextView
            android:id="@+id/decode"
            style="@style/Control.Action"
            tools:text="硬解" />

        <TextView
            android:id="@+id/speed"
            style="@style/Control.Action"
            tools:text="速度" />

        <TextView
            android:id="@+id/scale"
            style="@style/Control.Action"
            tools:text="縮放" />

        <TextView
            android:id="@+id/reset"
            style="@style/Control.Action"
            android:textColor="@color/text"
            tools:text="刷新" />

        <TextView
            android:id="@+id/loop"
            style="@style/Control.Action"
            android:text="@string/play_loop"
            android:textColor="@color/text" />

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/text"
            style="@style/Control.Action"
            android:tag="3"
            android:text="@string/play_track_text"
            android:visibility="gone"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/audio"
            style="@style/Control.Action"
            android:tag="1"
            android:text="@string/play_track_audio"
            android:visibility="gone"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/video"
            style="@style/Control.Action"
            android:tag="2"
            android:text="@string/play_track_video"
            android:visibility="gone"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/opening"
            style="@style/Control.Action"
            tools:text="片頭" />

        <TextView
            android:id="@+id/ending"
            style="@style/Control.Action"
            tools:text="片尾" />

        <TextView
            android:id="@+id/danmaku"
            style="@style/Control.Action"
            android:text="@string/danmaku"
            android:visibility="gone"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/episodes"
            style="@style/Control.Action"
            android:text="@string/detail_episode" />

    </LinearLayout>
</HorizontalScrollView>