{"sites": [{"key": "drpy_js_豆瓣", "name": "🔥聚玩盒子4K", "type": 3, "api": "/localized/interface_5_xhztv.top测试/js/drpy2.min.js", "ext": "/localized/interface_5_xhztv.top测试/js/douban.js"}, {"key": "豆瓣", "name": "🔥公众号：聚玩盒", "type": 3, "api": "csp_<PERSON><PERSON><PERSON>", "searchable": 0, "quickSearch": 0, "filterable": 0}, {"key": "小胡", "name": "🔥┃小胡┃4K", "type": 1, "playerType": 1, "api": "http://xh1.xn--yetu07f.icu/api.php/provide/vod/", "jar": "http://xiaohu.xzam.cn/jar/1.jar"}, {"key": "csp_Mp4Mov", "name": "🧲┃Mp4┃4k", "type": 3, "api": "csp_Mp4Mov", "searchable": 1, "quickSearch": 1, "filterable": 1, "jar": "https://agit.ai/guot54/ygbh/raw/branch/master/JAR/83.jar"}, {"key": "csp_SeedHub", "name": "🧲┃SeedHub┃4k", "type": 3, "api": "csp_SeedHub", "playerType": 1, "searchable": 1, "quickSearch": 1, "filterable": 1, "jar": "https://agit.ai/guot54/ygbh/raw/branch/master/JAR/83.jar"}, {"key": "csp_SixV", "name": "💕┃磁力┃4K", "type": 3, "api": "csp_SixV", "searchable": 1, "ext": "https://www.6vdy.org/", "jar": "https://github.moeyy.xyz/raw.githubusercontent.com/CandyMuj/ResourceInterface/main/TVBox/candymuj.jar;md5;********************************"}, {"key": "七新", "name": "👼┃七新┃2K", "type": 3, "api": "csp_XBPQ", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "http://rihou.vip:88/hccx/七新影视.json", "jar": "http://rihou.vip:88/hccx/HeChengChaXiu.jar"}, {"key": "一起看 ", "name": "❤┃一起┃2K", "type": 3, "api": "csp_<PERSON><PERSON><PERSON><PERSON>", "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": " 在线┃直播1", "name": "📺┃竞技┃直播", "type": 3, "api": "csp_Yj1211", "searchable": 1, "quickSearch": 1, "filterable": 1}, {"key": " 在线┃直播2", "name": "📺┃网红┃直播", "type": 3, "api": "http://alist.xn--z7x900a.live/ddrpy2.m.js", "ext": "http://alist.xn--z7x900a.live/JustLive.js", "gridview": "0-0-H", "style": {"type": "rect", "ratio": 1.68}}, {"key": "cctv", "name": "📺┃央视┃大全", "type": 3, "api": "csp_CCTV", "searchable": 0, "filterable": 0, "ext": "http://rihou.vip:88/hccx/央视大全.json", "jar": "http://rihou.vip:88/hccx/HeChengChaXiu.jar"}, {"key": "py_cctv_少儿", "name": "📺┃央视┃少儿", "type": 3, "api": "py_cctv_full", "searchable": 0, "quickSearch": 0, "filterable": 1, "ext": "http://rihou.vip:88/hccx/py_央视少儿.py", "jar": "http://rihou.vip:88/hccx/HeChengChaXiu.jar"}, {"key": "央视经典", "name": "📺┃央视┃经典", "type": 3, "api": "csp_<PERSON><PERSON>", "searchable": 0, "quickSearch": 1, "filterable": 1, "ext": "http://rihou.vip:88/hccx/哔哩经典.json", "jar": "http://rihou.vip:88/hccx/HeChengChaXiu.jar"}, {"key": "drpy_js_360影视", "name": "🧡┃360┃官源", "type": 3, "api": "https://jihulab.com/yw88075/tvbox/-/raw/main/dr/lib/drpy2.min.js", "ext": "https://jihulab.com/yw88075/tvbox/-/raw/main/dr/js/360%E5%BD%B1%E8%A7%86.js"}, {"key": "初恋资源", "name": "🧡┃初恋┃官源", "type": 1, "api": "https://video.adminqt.cn/api.php/provide/vod/", "searchable": 1, "quickSearch": 1}, {"key": "小柚", "name": "🍊┃小柚┃App", "type": 3, "api": "csp_AppSK", "searchable": 1, "quickSearch": 1, "changeable": 1, "ext": "FbjPcVZY48PN/RIZ5QqOrsp7JoZYCZdLEy9R9ri6ykNdUNSIsT3IgxCP9qyzByO7ZG6Z030vgWoyXGNlCJx9KFpYXtyZ3B/cLTtO0SAclaw3AOm0fS9oLx2taGF6hg=="}, {"key": "南坊", "name": "🎃┃南坊┃App", "type": 3, "api": "csp_AppMao", "searchable": 1, "quickSearch": 1, "changeable": 1, "ext": "FbjDcUxPqpfNr0QF4QvE6sExbd4UXJxJXzdL462ywU1XScGa5G6Hj0/c+Ou1GW6rdX6N2XIhnD46QzIsRoZ8bk4fG4OYi0iCaWwRj2ddkaI+FqHtLjQhalHqIy0+kpiTv2eOfJYxTshgoxEZvRX9BA0UIrOurFxHa4dPZw=="}, {"key": "fok", "name": "🌙┃夸克┃影视", "type": 3, "api": "csp_XBPQ", "jar": "/localized/interface_5_xhztv.top测试/jar/1.jar", "ext": {"分类url": "https://www.freeok.vip/vod-show/{cateId}-{area}-------{catePg}---{year}.html", "分类": "FREE电影&FREE剧集&FREE动漫&FREE综艺&FREE短剧&FREE少儿", "分类值": "1&2&3&4&12&5", "播放请求头": "User-Agent$Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "副标题": "<div class=\"module-item-note\">&&</div>", "嗅探词": "m3u8#.m3u8#.mp4#freeok.mp4#/obj/", "线路数组": "data-dropdown-value=&&</div>[不包含:夸克]", "线路标题": "<span>&&</small>", "导演": "导演：&&</div>", "主演": "主演：&&</div>", "简介": "<p>&&</p>"}}, {"key": "耐看", "name": "🕶┃耐看┃影视", "type": 3, "api": "csp_XBPQ", "jar": "/localized/interface_5_xhztv.top测试/jar/1.jar", "ext": "http://xiaohu.xzam.cn/XBPQ/nk.json"}, {"key": "南瓜", "name": "🎃┃南瓜┃App", "type": 3, "api": "csp_<PERSON>", "playerType": 2, "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "萌米", "name": "👀┃萌米┃App", "type": 3, "api": "csp_AppMao", "searchable": 1, "quickSearch": 1, "changeable": 1, "ext": "FbjKZ1gO+9u81wMIxzHg+7x1Oep4CIFuZAlCy8a4qCgjGLacnxDo6wiBr66jP3X8J2mRx31u1XgkTTVrENVjfhQKUN2Yi12XNWQSlmVZxLtiSaSiZG0xf1Hw"}, {"key": "繁星", "name": "💥┃繁星┃App", "type": 3, "api": "csp_AppMao", "playerType": 2, "searchable": 1, "quickSearch": 1, "changeable": 1, "ext": "FbjDcUxPqpfNr0QF4QvE6sExbd4UXJxJXzdL462ywU1XScGa5G6Hj0/c+Ou1GW6rdX6N2XIhnD46QzIsRoZ8bk4fG4OYi0iCaWwRj2ddkacwFqHtLjQhalHqIy0+kpiTv2eOfJYxTshgrxcJ+g3lEHx7ZLC9kB1TCfZUSHwqHB3tt6V/1OhRENIOZNRFfXVBEFd7jQg+J06kjCAF7z1Bt8hRvMjC2VcbQXsEz8MCZDgu06C9/wirWcTa/wWJsfT+Z7fXaDs+dIWMYiwf6td5CWTx6LfL6eTuiMhs2KU5Byw9F+4Y/AHBg4r+LEe+Hm4EM2d8X3ca5zHSj+kjmpXOiGDo4TalFqgTq67eggmthCbV2d131SKc03V+5gruudQQk/Tb9e4lg6SQhA9/5kWOOM/LRmkqIxSt6d0wpu7yKKh65enwKbgasc93/HnGr6W4LuZjf6eYnS2GgRzWRE0fT5lYNmxnWeRnYuLhoZbDqslAfJzg"}, {"key": "csp_<PERSON><PERSON><PERSON>", "name": "🌾┃农民┃直连", "type": 3, "api": "csp_<PERSON><PERSON><PERSON>", "searchable": 1, "quickSearch": 1, "changeable": 1, "ext": "https://agit.ai/fantaiying/fty/raw/branch/master/ext/nmys.json"}, {"key": "荐片", "name": "🧲┃荐片┃磁力", "type": 3, "api": "http://rihou.vip:55/lib/drpy2.min.js", "ext": "http://rihou.vip:55/lib/jianpian.js"}, {"key": "77", "name": "👒┃七七┃App", "type": 3, "api": "csp_Kunyu77", "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "奈飞中文", "name": "🎗┃奈飞┃秒播", "type": 3, "api": "csp_XYQHiker", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "http://qrh.yimkj.cn/ym/lib/nfzw.json", "jar": "http://qrh.yimkj.cn/ym/lib/ttkx.jar;md5;c253e1930a210fa5141e5418f1f90949"}, {"key": "九六", "name": "🎀┃九六┃直连", "type": 3, "api": "csp_Cs1369", "playerType": 2, "searchable": 1, "quickSearch": 1, "changeable": 1}, {"key": "熊掌", "name": "🐻┃熊掌┃高清", "type": 1, "api": "https://xzcjz.com/api.php/provide/vod", "jar": "https://tvbox.cainisi.cf/jar/fty0101.jar;md5;********************************"}, {"key": "ikunzy", "name": "🐓┃爱坤┃资源", "type": 1, "api": "https://ikunzyapi.com/api.php/provide/vod ", "searchable": 1, "filterable": 0, "categories": ["欧美剧", "韩剧", "日剧", "动作片", "喜剧片", "爱情片", "科幻片", "恐怖片", "剧情片", "战争片"]}, {"key": "天天", "name": "💡┃天天┃影视", "type": 3, "api": "csp_TTian", "playerType": 1, "ext": "http://op.ysdqjs.cn", "jar": "http://tv.nxog.top/m/jar.php?id=ou;md5;59a98ed1869e26027a3ff8d6070003f8"}, {"key": "Bili", "name": "🅱┃哔哔┃合集", "type": 3, "api": "csp_<PERSON><PERSON>", "searchable": 0, "quickSearch": 0, "ext": "http://www.lyyytv.cn/yt/bili/bili.json"}, {"key": "赛文", "name": "🍩┃赛文┃资源", "type": 3, "api": "csp_AppYsV2", "ext": "http://xiutan.shiyifacai.com/zuoan/api.php/app/", "jar": "http://xiaohu.xzam.cn/jar/1.jar;md5;6120d08f4eb1edce507bfcd1b339c482"}, {"key": "豪华资源", "name": "🎥┃豪华┃资源", "type": 1, "api": "https://hhzyapi.com/api.php/provide/vod/?ac=list", "searchable": 1, "quickSearch": 1, "categories": ["内地剧", "香港剧", "欧美剧", "韩剧", "日剧", "马泰剧", "台湾剧", "动画片", "中国动漫", "日本动漫", "欧美动漫", "剧情片", "战争片", "动作片", "科幻片", "记录片", "爱情片", "喜剧片", "灾难片", "悬疑片", "犯罪片", "大陆综艺", "日韩综艺", "港台综艺", "欧美综艺"]}, {"key": "hnzy", "name": "🎥┃红牛┃资源", "type": 1, "api": "https://www.hongniuzy2.com/api.php/provide/vod/", "searchable": 1, "filterable": 0, "categories": ["欧美剧", "韩剧", "日剧", "动作片", "喜剧片", "爱情片", "科幻片", "恐怖片", "剧情片", "战争片"]}, {"key": "gszy", "name": "🎥┃光速┃资源", "type": 1, "api": "https://api.guangsuapi.com/api.php/provide/vod/", "searchable": 1, "filterable": 0, "categories": ["欧美剧", "韩剧", "日剧", "动作片", "喜剧片", "爱情片", "科幻片", "恐怖片", "剧情片", "战争片"]}, {"key": "lzzy", "name": "🎥┃量子┃资源", "type": 1, "api": "https://cj.lziapi.com/api.php/provide/vod/", "searchable": 1, "filterable": 0, "categories": ["国产动漫", "日韩动漫", "大陆剧", "欧美剧", "韩剧", "日剧", "动作片", "喜剧片", "爱情片", "科幻片", "恐怖片", "剧情片", "战争片"]}, {"key": "snzy", "name": "🎥┃索尼┃资源", "type": 1, "api": "https://suoniapi.com/api.php/provide/vod/", "searchable": 1, "filterable": 0, "categories": ["欧美剧", "韩剧", "日剧", "动作片", "喜剧片", "爱情片", "科幻片", "恐怖片", "剧情片", "战争片"]}, {"key": "drpy_js_310直播", "name": "⚽310直播", "type": 3, "api": "https://github.moeyy.xyz/raw.githubusercontent.com/gaotianliuyun/gao/master/lib/drpy2.min.js", "changeable": 0, "style": {"type": "rect", "ratio": 1}, "ext": "https://github.moeyy.xyz/raw.githubusercontent.com/gaotianliuyun/gao/master/js/310直播.js"}, {"key": "MV_vod", "name": "⚡┃明星┃MV", "type": 3, "api": "csp_<PERSON><PERSON>", "style": {"type": "rect", "ratio": 1.597}, "searchable": 0, "quickSearch": 0, "changeable": 0, "ext": "https://agit.ai/fantaiying/fty/raw/branch/master/ext/MTV.json"}, {"key": "相声小品", "name": "😍┃相声┃小品", "type": 3, "api": "csp_<PERSON><PERSON>", "searchable": 0, "quickSearch": 0, "ext": "http://tt.iitvba.com/js/相声小品.json"}, {"key": "戏 曲", "name": "👵┃老年┃戏曲", "type": 3, "api": "csp_<PERSON><PERSON>", "searchable": 0, "quickSearch": 0, "ext": "http://tt.iitvba.com/js/戏曲.json"}, {"key": "短剧one", "name": "🥰┃短剧┃视频", "type": 3, "api": "csp_<PERSON><PERSON>", "ext": "https://d.kstore.space/download/6296/json/短剧.json", "jar": "http://like.xn--z7x900a.live:66/jar/PandaQ231205.jar;md5;9cc29f6286b2fe7910628852929c4bdb"}, {"key": "种子短剧", "name": "💮┃短剧┃视频", "type": 3, "api": "csp_<PERSON><PERSON>", "ext": "分类url:http://web.zzdj.cc/index.php/vod/show/by/{by}/id/{cateId}/page/{catePg}.html,分类:快手$2#抖音$3#都市$4#古装$5#重生$6#逆袭$7#虐恋$8#萌宝$9#言情$10#穿越$11#战神$12#神医$13#赘婿$14#甜宠$15#其它$16#短剧$1", "jar": "http://like.xn--z7x900a.live:66/jar/PandaQ231205.jar;md5;9cc29f6286b2fe7910628852929c4bdb"}, {"key": "csp_WoGG", "name": "👽┃玩偶┃4K①", "type": 3, "api": "csp_WoGG", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": "http://127.0.0.1:9978/file/tvfan/token.txt+4k|auto|fhd$$$https://api.wogg.xyz/$$$弹"}, {"key": "玩我哥哥", "name": "测┃玩我┃哥哥", "type": 3, "api": "csp_XYQHikerAL", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "http://rihou.vip:55/lib/玩我哥哥.json", "jar": "http://rihou.vip:55/lib/ttkx.jar;md5;c253e1930a210fa5141e5418f1f90949"}, {"key": "玩偶哥哥", "name": "测┃玩偶┃哥哥", "type": 3, "api": "csp_XYQHikerAL", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "http://rihou.vip:55/lib/玩偶哥哥.json", "jar": "http://rihou.vip:55/lib/ttkx.jar;md5;c253e1930a210fa5141e5418f1f90949"}, {"key": "玩偶运输车", "name": "测┃玩偶┃运输", "type": 3, "api": "csp_XYQHikerAL", "searchable": 1, "quickSearch": 1, "filterable": 1, "ext": "http://rihou.vip:55/lib/玩偶运输车.json", "jar": "http://rihou.vip:55/lib/ttkx.jar;md5;c253e1930a210fa5141e5418f1f90949"}, {"key": "PanSou", "name": "🦊┃盘搜┃搜索", "type": 3, "api": "csp_Pan<PERSON>ou", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": "http://127.0.0.1:9978/file/tvfan/token.txt+4k|auto|fhd"}, {"key": "UpYun", "name": "😻┃Up搜┃搜索", "type": 3, "api": "csp_UpYun", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": "http://127.0.0.1:9978/file/tvfan/token.txt+4k|auto|fhd"}, {"key": "PanSearch", "name": "🙀┃盘Se┃搜索", "type": 3, "api": "csp_PanSearch", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": "http://127.0.0.1:9978/file/tvfan/token.txt+4k|auto|fhd"}, {"key": "七夜", "name": "😾┃七夜┃搜索", "type": 3, "api": "csp_Dovx", "searchable": 1, "quickSearch": 1, "changeable": 0, "ext": "http://127.0.0.1:9978/file/tvfan/token.txt+4k|auto|fhd"}, {"key": "阿里云盘登录", "name": "🐲阿里┃云盘登录", "type": 3, "api": "csp_<PERSON><PERSON><PERSON>", "searchable": 0, "quickSearch": 0, "filterable": 0, "ext": "http://127.0.0.1:9978/file/TVBox/tok.txt", "jar": "http://tv.nxog.top/m/jar.php?id=ou;md5;34c50af4380beab4dbfeae6c9b41939b"}, {"key": "push_agent", "name": "📽推送", "type": 3, "api": "csp_PushAgent", "playerType": 1, "searchable": 1, "quickSearch": 1, "filterable": 0, "jar": "http://tv.nxog.top/m/jar.php?id=ou;md5;d437c6e042174f181efd9649a1a33f48", "ext": "http://127.0.0.1:9978/file/tvbox/tok.txt"}], "lives": [{"name": "直播", "type": 0, "playerType": 1, "url": "http://xhztv.top/njyy.txt", "epg": "https://epg.112114.xyz/?ch={name}&date={date}", "logo": "https://epg.112114.xyz/logo/{name}.png"}, {"group": "redirect", "channels": [{"name": "live", "epg": "https://epg.112114.xyz/?ch={name}&date={date}", "urls": ["proxy://do=live&type=txt&ext=http://home.jundie.top:81/ray/tvlive.txt"]}]}], "parses": [{"name": "Json聚合", "type": 3, "url": "Demo"}, {"name": "Web聚合", "type": 3, "url": "Web"}, {"name": "json一", "type": 1, "url": "http://**************:2345/Api/yun.php?url=", "ext": {"flag": ["<PERSON><PERSON><PERSON>", "FYNB", "qq", "腾讯", "qiyi", "爱奇艺", "奇艺", "youku", "优酷", "mgtv", "芒果", "letv", "乐视", "pptv", "PPTV", "sohu", "bilibili", "哔哩哔哩", "哔哩"], "header": {"User-Agent": "okhttp/3.12.11"}}}, {"name": "赛文", "type": 1, "url": "http://xiutan.shiyifacai.com/json/qingfeng.php?url=", "ext": {"flag": ["seven"], "header": {"User-Agent": "Lavf/58.12.100"}}}, {"name": "json三", "type": 1, "url": "http://xiutan.shiyifacai.com/json/qingfeng.php?url=", "ext": {"flag": ["<PERSON><PERSON><PERSON>", "SMD", "FYNB", "qq", "腾讯", "qiyi", "爱奇艺", "奇艺", "youku", "优酷", "mgtv", "芒果", "letv", "乐视", "pptv", "PPTV", "sohu", "bilibili", "哔哩哔哩", "哔哩"], "header": {"User-Agent": "okhttp/3.12.11"}}}, {"name": "json四", "type": 1, "url": "https://vip.mosangkeji.com:443/api/?key=q7mSY5L8ssbm1J3Eai&url=", "ext": {"flag": ["<PERSON><PERSON><PERSON>", "SMD", "FYNB", "qq", "腾讯", "qiyi", "爱奇艺", "奇艺", "youku", "优酷", "mgtv", "芒果", "letv", "乐视", "pptv", "PPTV", "sohu", "bilibili", "哔哩哔哩", "哔哩"], "header": {"User-Agent": "okhttp/3.12.11"}}}, {"name": "json五", "type": 1, "url": "http://************:880/analysis/json/?uid=2148&my=ehmqrtuCEJNQTVX047&format=json&url=", "ext": {"flag": ["<PERSON><PERSON><PERSON>", "SMD", "FYNB", "qq", "腾讯", "qiyi", "爱奇艺", "奇艺", "youku", "优酷", "mgtv", "芒果", "letv", "乐视", "pptv", "PPTV", "sohu", "bilibili", "哔哩哔哩", "哔哩"], "header": {"User-Agent": "okhttp/3.12.11"}}}, {"name": "解析一", "type": 1, "url": "http://**************:2345/Api/yun.php?url=", "ext": {"flag": ["qq", "腾讯", "企鹅", "IQiYi", "qiyi", "爱奇艺", "奇艺", "youku", "You<PERSON><PERSON>", "优酷", "sohu", "SoHu", "搜狐", "letv", "Le<PERSON><PERSON>", "乐视", "imgo", "mgtv", "<PERSON><PERSON><PERSON><PERSON>", "芒果", "SLYS4k", "BYGA", "luanzi", "AliS", "dxzy", "bilibili", "QEYSS", "xigua", "西瓜视频", "腾讯视频", "奇艺视频", "优酷视频", "芒果视频", "乐视视频"], "header": {"User-Agent": "Dart/2.18 (dart:io)"}}}, {"name": "绣探", "type": 0, "url": "https://www.pangujiexi.com/pangu/?url="}, {"name": "虾米", "type": 0, "url": "https://jx.xmflv.com/?url=", "ext": {"flag": ["qq", "腾讯", "qiyi", "爱奇艺", "奇艺", "youku", "优酷", "sohu", "搜狐", "letv", "乐视", "mgtv", "芒果", "imgo", "rx", "ltnb", "bilibili", "1905", "xigua"]}}, {"name": "天下嗅探", "type": 0, "url": "https://jx.jsonplayer.com/player/?=&url=", "ext": {"flag": ["qq", "腾讯", "qiyi", "<PERSON><PERSON><PERSON>", "爱奇艺", "奇艺", "youku", "yk", "优\n酷", "mgtv", "imgo", "芒果"]}}, {"name": "7777嗅探", "type": 0, "url": "https://jx.777jiexi.com/player/?url="}, {"name": "杰森嗅探", "type": 0, "url": "https://jx.jsonplayer.com/player/?url="}, {"name": "vip嗅探", "type": 0, "url": "https://vip.lianfaka.com/vip/?url=", "ext": {"flag": ["qq", "腾讯", "qiyi", "爱奇艺", "奇艺", "youku", "优酷", "mgtv", "芒\n果", "imgo", "letv", "乐视", "pptv", "PPTV", "sohu", "bilibili", "哔哩哔哩", "哔哩"], "header": {"User-Agent": "okhttp/4.1.0"}}}, {"name": "8090嗅探", "type": 0, "url": "https://www.8090g.cn/jiexi/?url="}, {"name": "m3u8嗅探", "type": 0, "url": "https://jx.m3u8.tv/jiexi/?url=", "ext": {"flag": ["qq", "腾讯", "qiyi", "<PERSON><PERSON><PERSON>", "爱奇艺", "奇艺", "youku", "yk", "优\n酷", "mgtv", "imgo", "芒果"]}}, {"name": "夜幕嗅探", "type": 0, "url": "https://www.yemu.xyz/?url="}], "flags": ["youku", "优酷", "优 酷", "优酷视频", "qq", "腾讯", "腾 讯", "腾讯视频", "<PERSON><PERSON><PERSON>", "qiyi", "奇艺", "爱奇艺", "爱 奇 艺", "m1905", "xigua", "letv", "leshi", "乐视", "乐 视", "sohu", "搜狐", "搜 狐", "搜狐视频", "tudou", "mgtv", "芒果", "imgo", "芒果TV", "芒 果 T V", "bilibili", "哔 哩", "哔 哩 哔 哩", "SPA", "Yu<PERSON>i-vip", "pptv", "PPTV", "ltnb", "rx", "SLYS4k", "tucheng", "BYGA", "luanzi", "dxzy", "QEYSS", "<PERSON><PERSON><PERSON>", "AliS", "122", "chuang<PERSON>", "CL4K", "x<PERSON><PERSON>", "wuduzy", "wasu", "ren<PERSON><PERSON>", "ppayun", "haiwaikan", "cool", "dbm3u8", "xmm", "funshion", "ruyi1080", "ruyib1080"], "doh": [{"name": "proxy", "hosts": ["manifest.googlevideo.com", ".*workers.dev", "www.cilixiong.com", "*.t4tv.hz.cz", "kuba222.com", "mp4us.com", "dydhhy.com", "magicalsearch.top", "api123.adys.app", ".*wogg.xyz", "dmku.thefilehosting.com", "epg.112114.xyz", "raw.githubusercontent.com", "googlevideo.com", "cdn.v82u1l.com", "cdn.iz8qkg.com", "cdn.kin6c1.com", "c.biggggg.com", "c.olddddd.com", "haiwaikan.com", "www.histar.tv", "youtube.com", "uhibo.com", ".*boku.*", ".*nivod.*", ".*ulivetv.*"]}, {"name": "暴风廣告", "hosts": ["bfzy"], "regex": ["#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:3,[\\s\\S]*?#EXT-X-DISCONTINUITY"]}, {"name": "量子廣告", "hosts": ["vip.lz", "hd.lz", "v.cdnlz1", "v.cdnlz"], "regex": ["18.5333"]}, {"name": "非凡廣告", "hosts": ["vip.ffzy", "hd.ffzy"], "regex": ["25.0666", "25.08"]}, {"name": "火山嗅探", "hosts": ["huoshan.com"], "regex": ["item_id="]}, {"name": "抖音嗅探", "hosts": ["douyin.com"], "regex": ["is_play_url="]}, {"name": "農民嗅探", "hosts": ["toutiaovod.com"], "regex": ["video/tos/cn"]}, {"name": "磁力廣告", "hosts": ["magnet"], "regex": ["更多", "社區", "xuu", "最新", "最新", "直播", "更新", "社区", "有趣", "有趣", "英皇体育", "全中文AV在线", "澳门皇冠赌场", "哥哥快来", "美女荷官", "裸聊", "新片首发", "UUE29"]}, {"name": "海外看", "hosts": ["haiwaikan"], "regex": ["10.0099", "10.3333", "16.0599", "8.1748", "10.85"]}, {"name": "星星", "hosts": ["aws.ulivetv.net"], "regex": ["#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:8,[\\s\\S]*?#EXT-X-DISCONTINUITY"]}, {"name": "索尼", "hosts": ["suonizy"], "regex": ["15.1666", "15.2666"]}, {"name": "Google", "url": "https://dns.google/dns-query", "ips": ["*******", "*******"]}, {"name": "Cloudflare", "url": "https://cloudflare-dns.com/dns-query", "ips": ["*******", "*******", "2606:4700:4700::1111", "2606:4700:4700::1001"]}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://dns.adguard.com/dns-query", "ips": ["*************", "*************"]}, {"name": "DNSWatch", "url": "https://resolver2.dns.watch/dns-query", "ips": ["************", "************"]}, {"name": "Quad9", "url": "https://dns.quad9.net/dns-quer", "ips": ["*******", "***************"]}], "rules": [{"host": "www.djuu.com", "rule": ["mp4.djuu.com", "m4a"]}, {"host": "www.sharenice.net", "rule": ["huoshan.com", "/item/video/"], "filter": []}, {"host": "www.sharenice.net", "rule": ["sovv.qianpailive.com", "vid="], "filter": []}, {"host": "www.sharenice.net", "rule": ["douyin.com", "/play/"]}, {"host": "m.ysxs8.vip", "rule": ["ysting.ysxs8.vip:81", "xmcdn.com"], "filter": []}, {"host": "hdmoli.com", "rule": [".m3u8"]}, {"host": "https://api.live.bilibili.com", "rule": ["bilivideo.com", "/index.m3u8"], "filter": ["data.bilibili.com/log/web", "i0.hdslb.com/bfs/live/"]}, {"host": "www.agemys.cc", "rule": ["cdn-tos", "obj/tos-cn"]}, {"host": "www.fun4k.com", "rule": ["https://hd.ijycnd.com/play", "index.m3u8"]}, {"host": "zjmiao.com", "rule": ["play.videomiao.vip/API.php", "time=", "key=", "path="]}], "ijk": [{"group": "软解码", "options": [{"category": 4, "name": "opensles", "value": "0"}, {"category": 4, "name": "overlay-format", "value": "842225234"}, {"category": 4, "name": "framedrop", "value": "1"}, {"category": 4, "name": "soundtouch", "value": "1"}, {"category": 4, "name": "start-on-prepared", "value": "1"}, {"category": 1, "name": "http-detect-range-support", "value": "0"}, {"category": 1, "name": "fflags", "value": "fastseek"}, {"category": 2, "name": "skip_loop_filter", "value": "48"}, {"category": 4, "name": "reconnect", "value": "1"}, {"category": 4, "name": "max-buffer-size", "value": "5242880"}, {"category": 4, "name": "enable-accurate-seek", "value": "0"}, {"category": 4, "name": "mediacodec", "value": "0"}, {"category": 4, "name": "mediacodec-auto-rotate", "value": "0"}, {"category": 4, "name": "mediacodec-handle-resolution-change", "value": "0"}, {"category": 4, "name": "mediacodec-hevc", "value": "0"}, {"category": 1, "name": "dns_cache_timeout", "value": "600000000"}]}, {"group": "硬解码", "options": [{"category": 4, "name": "opensles", "value": "0"}, {"category": 4, "name": "overlay-format", "value": "842225234"}, {"category": 4, "name": "framedrop", "value": "1"}, {"category": 4, "name": "soundtouch", "value": "1"}, {"category": 4, "name": "start-on-prepared", "value": "1"}, {"category": 1, "name": "http-detect-range-support", "value": "0"}, {"category": 1, "name": "fflags", "value": "fastseek"}, {"category": 2, "name": "skip_loop_filter", "value": "48"}, {"category": 4, "name": "reconnect", "value": "1"}, {"category": 4, "name": "max-buffer-size", "value": "5242880"}, {"category": 4, "name": "enable-accurate-seek", "value": "0"}, {"category": 4, "name": "mediacodec", "value": "1"}, {"category": 4, "name": "mediacodec-auto-rotate", "value": "1"}, {"category": 4, "name": "mediacodec-handle-resolution-change", "value": "1"}, {"category": 4, "name": "mediacodec-hevc", "value": "1"}, {"category": 1, "name": "dns_cache_timeout", "value": "600000000"}]}], "ads": ["wan.51img1.com", "iqiyi.hbuioo.com", "vip.ffzyad.com", "http://itvba.xyz/tv/jar/fty1020.jar"], "wallpaper": "http://饭太硬.top/深色壁纸/api.php", "warningText": "记得没事清理清理缓存哦", "spider": "https://fs-im-kefu.7moor-fs1.com/29397395/4d2c3f00-7d4c-11e5-af15-41bf63ae4ea0/1707044072121/fan.txt;md5;c3e8c08b4cd6f7fd0f868ea48dd42cb2"}