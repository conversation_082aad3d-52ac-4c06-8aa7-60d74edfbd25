#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
解密API路由
"""

from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel, HttpUrl
from typing import Optional, List, Dict, Any
import logging
import json
import base64
import gzip
import requests
from urllib.parse import urlparse

from app.core.tvbox_decryptor import TVBoxDecryptor

logger = logging.getLogger(__name__)
router = APIRouter()
decryptor = TVBoxDecryptor()

# Pydantic模型
class URLDecryptRequest(BaseModel):
    url: HttpUrl

class ContentDecryptRequest(BaseModel):
    content: str
    method: Optional[str] = "auto"

class BatchDecryptRequest(BaseModel):
    urls: List[HttpUrl]

class DecryptResponse(BaseModel):
    success: bool
    method: Optional[str] = None
    content: Optional[str] = None
    analysis: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

class BatchDecryptResponse(BaseModel):
    results: List[DecryptResponse]
    success_count: int
    total_count: int

class MethodsResponse(BaseModel):
    methods: List[Dict[str, str]]

@router.post("/url", response_model=DecryptResponse, summary="URL解密")
async def decrypt_url(request: URLDecryptRequest):
    """解密TVBox配置URL"""
    try:
        url = str(request.url)
        logger.info(f"开始解密URL: {url}")
        
        # 使用解密器解密
        content, method = decryptor.decrypt_config_url(url)
        
        if content:
            # 分析配置内容
            analysis = decryptor.analyze_config(content)
            
            return DecryptResponse(
                success=True,
                method=method,
                content=content,
                analysis=analysis
            )
        else:
            return DecryptResponse(
                success=False,
                error="无法解密URL内容"
            )
            
    except requests.exceptions.RequestException as e:
        logger.error(f"URL请求异常: {str(e)}")
        return DecryptResponse(
            success=False,
            error=f"URL访问失败: {str(e)}"
        )
    except Exception as e:
        logger.error(f"URL解密异常: {str(e)}")
        return DecryptResponse(
            success=False,
            error=f"解密失败: {str(e)}"
        )

@router.post("/content", response_model=DecryptResponse, summary="内容解密")
async def decrypt_content(request: ContentDecryptRequest):
    """解密配置内容"""
    try:
        content = request.content
        method = request.method
        
        logger.info(f"开始解密内容，方法: {method}")
        
        decrypted_content = None
        used_method = method
        
        if method == "auto":
            # 自动检测解密方法
            decrypted_content, used_method = decryptor.auto_decrypt(content)
        elif method == "base64":
            try:
                decrypted_content = base64.b64decode(content).decode('utf-8')
                used_method = "base64"
            except Exception as e:
                raise ValueError(f"Base64解密失败: {str(e)}")
        elif method == "gzip":
            try:
                decoded = base64.b64decode(content)
                decrypted_content = gzip.decompress(decoded).decode('utf-8')
                used_method = "gzip"
            except Exception as e:
                raise ValueError(f"Gzip解密失败: {str(e)}")
        elif method == "custom":
            # 自定义解密方法
            decrypted_content, used_method = decryptor.custom_decrypt(content)
        else:
            raise ValueError(f"不支持的解密方法: {method}")
        
        if decrypted_content:
            # 分析配置内容
            analysis = decryptor.analyze_config(decrypted_content)
            
            return DecryptResponse(
                success=True,
                method=used_method,
                content=decrypted_content,
                analysis=analysis
            )
        else:
            return DecryptResponse(
                success=False,
                error="解密失败，无法获取有效内容"
            )
            
    except Exception as e:
        logger.error(f"内容解密异常: {str(e)}")
        return DecryptResponse(
            success=False,
            error=f"解密失败: {str(e)}"
        )

@router.post("/batch", response_model=BatchDecryptResponse, summary="批量解密")
async def batch_decrypt(request: BatchDecryptRequest):
    """批量解密多个URL"""
    try:
        results = []
        success_count = 0
        
        for url in request.urls:
            try:
                url_str = str(url)
                content, method = decryptor.decrypt_config_url(url_str)
                
                if content:
                    analysis = decryptor.analyze_config(content)
                    results.append(DecryptResponse(
                        success=True,
                        method=method,
                        content=content,
                        analysis=analysis
                    ))
                    success_count += 1
                else:
                    results.append(DecryptResponse(
                        success=False,
                        error="无法解密URL内容"
                    ))
                    
            except Exception as e:
                results.append(DecryptResponse(
                    success=False,
                    error=f"解密失败: {str(e)}"
                ))
        
        return BatchDecryptResponse(
            results=results,
            success_count=success_count,
            total_count=len(request.urls)
        )
        
    except Exception as e:
        logger.error(f"批量解密异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量解密失败: {str(e)}"
        )

@router.get("/methods", response_model=MethodsResponse, summary="获取解密方法")
async def get_decrypt_methods():
    """获取支持的解密方法列表"""
    try:
        methods = [
            {
                "name": "auto",
                "description": "自动检测解密方法"
            },
            {
                "name": "base64",
                "description": "Base64解码"
            },
            {
                "name": "gzip",
                "description": "Gzip压缩解密"
            },
            {
                "name": "aes",
                "description": "AES加密解密"
            },
            {
                "name": "custom",
                "description": "自定义解密方法"
            }
        ]
        
        return MethodsResponse(methods=methods)
        
    except Exception as e:
        logger.error(f"获取解密方法异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取解密方法失败"
        )

@router.post("/validate", summary="验证配置格式")
async def validate_config(request: ContentDecryptRequest):
    """验证TVBox配置格式"""
    try:
        content = request.content
        
        # 尝试解析JSON
        try:
            config_data = json.loads(content)
        except json.JSONDecodeError as e:
            return {
                "valid": False,
                "error": f"JSON格式错误: {str(e)}"
            }
        
        # 验证TVBox配置结构
        required_fields = ["sites", "lives", "parses"]
        missing_fields = []
        
        for field in required_fields:
            if field not in config_data:
                missing_fields.append(field)
        
        if missing_fields:
            return {
                "valid": False,
                "error": f"缺少必需字段: {', '.join(missing_fields)}"
            }
        
        # 分析配置
        analysis = decryptor.analyze_config(content)
        
        return {
            "valid": True,
            "analysis": analysis,
            "message": "配置格式验证通过"
        }
        
    except Exception as e:
        logger.error(f"配置验证异常: {str(e)}")
        return {
            "valid": False,
            "error": f"验证失败: {str(e)}"
        }

@router.post("/merge", summary="合并配置")
async def merge_configs(configs: List[str]):
    """合并多个TVBox配置"""
    try:
        if not configs:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="配置列表不能为空"
            )
        
        merged_config = decryptor.merge_configs(configs)
        
        if merged_config:
            analysis = decryptor.analyze_config(merged_config)
            
            return {
                "success": True,
                "config": merged_config,
                "analysis": analysis
            }
        else:
            return {
                "success": False,
                "error": "配置合并失败"
            }
            
    except Exception as e:
        logger.error(f"配置合并异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"配置合并失败: {str(e)}"
        )
