#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
TVBox Manager Pro - FastAPI主应用
"""

import logging
import os
import sys
import time
from contextlib import asynccontextmanager
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
backend_dir = current_dir.parent
sys.path.insert(0, str(backend_dir))

from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from urllib.parse import unquote
from contextlib import asynccontextmanager

try:
    import uvicorn
except ImportError:
    uvicorn = None

from app.core.config import settings
from app.core.database import engine, Base
from app.core.security import create_access_token
from app.api.v1.api import api_router
from app.utils.logger import setup_logging

# 设置日志
logger = setup_logging()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("TVBox Manager Pro 启动中...")
    
    # 创建数据库表
    Base.metadata.create_all(bind=engine)
    logger.info("数据库表创建完成")
    
    # 创建默认管理员用户
    from app.services.user_service import UserService
    user_service = UserService()
    await user_service.create_default_admin()
    
    logger.info("TVBox Manager Pro 启动完成")
    
    yield
    
    # 关闭时执行
    logger.info("TVBox Manager Pro 关闭中...")

# 创建FastAPI应用
app = FastAPI(
    title="TVBox Manager Pro",
    description="现代化的TVBox接口管理系统",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加可信主机中间件
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS
)

# 注册API路由
app.include_router(api_router, prefix="/api/v1")

# 静态文件服务
if os.path.exists("static"):
    app.mount("/static", StaticFiles(directory="static"), name="static")

# 本地化文件服务 - 使用自定义路由处理URL编码文件名
localized_dir = "data/localized"
print(f"检查本地化目录: {localized_dir}")
print(f"当前工作目录: {os.getcwd()}")
print(f"目录存在: {os.path.exists(localized_dir)}")

if os.path.exists(localized_dir):
    print(f"挂载静态文件服务: /localized -> {localized_dir}")

    # 测试路由
    @app.get("/test-localized")
    async def test_localized():
        """测试路由是否工作"""
        return {"message": "自定义路由工作正常", "localized_dir": localized_dir}

    # 自定义文件服务路由，支持URL编码文件名
    @app.get("/localized/{file_path:path}")
    async def serve_localized_files(file_path: str):
        """自定义本地化文件服务，支持URL编码文件名"""
        try:
            print(f"🐾 收到文件请求: {file_path}")

            # 分解路径
            path_parts = file_path.split('/')
            print(f"🐾 路径部分: {path_parts}")

            # 构建可能的文件路径组合
            # 1. 完全解码的路径 (FastAPI已经解码了)
            decoded_path = os.path.join(localized_dir, file_path)
            print(f"🐾 尝试完全解码路径: {decoded_path}")

            if os.path.isfile(decoded_path):
                print(f"🐾 找到完全解码文件: {decoded_path}")
                return FileResponse(decoded_path)

            # 2. 文件名重新编码的路径 (目录解码，文件名编码)
            if len(path_parts) > 0:
                # 目录部分保持解码，文件名部分重新编码
                dir_parts = path_parts[:-1]  # 目录部分
                filename = path_parts[-1]    # 文件名部分

                # 将文件名重新编码为URL格式
                from urllib.parse import quote
                encoded_filename = quote(filename, safe='')
                print(f"🐾 文件名 '{filename}' 重新编码为: '{encoded_filename}'")

                # 构建混合编码路径
                mixed_path_parts = dir_parts + [encoded_filename]
                mixed_path = os.path.join(localized_dir, *mixed_path_parts)
                print(f"🐾 尝试混合编码路径: {mixed_path}")

                if os.path.isfile(mixed_path):
                    print(f"🐾 找到混合编码文件: {mixed_path}")
                    return FileResponse(mixed_path)

            # 如果都不存在，返回404
            print(f"🐾 文件不存在，已尝试路径:")
            print(f"  1. {decoded_path}")
            if len(path_parts) > 0:
                print(f"  2. {mixed_path}")
            raise HTTPException(status_code=404, detail="File not found")

        except HTTPException:
            raise
        except Exception as e:
            print(f"🐾 文件服务错误: {str(e)}")
            import traceback
            traceback.print_exc()
            raise HTTPException(status_code=500, detail="Internal server error")
else:
    print(f"本地化目录不存在: {localized_dir}")

# 全局异常处理
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "message": exc.detail,
            "status_code": exc.status_code
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理"""
    logger.error(f"未处理的异常: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": True,
            "message": "服务器内部错误",
            "status_code": 500
        }
    )

# 健康检查
@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "service": "TVBox Manager Pro",
        "version": "1.0.0"
    }

# 根路径
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "TVBox Manager Pro API",
        "version": "1.0.0",
        "docs": "/docs",
        "redoc": "/redoc"
    }

# 中间件：请求日志
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """记录请求日志"""
    start_time = time.time()
    
    # 记录请求信息
    logger.info(f"请求开始: {request.method} {request.url}")
    
    # 处理请求
    response = await call_next(request)
    
    # 计算处理时间
    process_time = time.time() - start_time
    
    # 记录响应信息
    logger.info(
        f"请求完成: {request.method} {request.url} "
        f"状态码: {response.status_code} "
        f"耗时: {process_time:.3f}s"
    )
    
    # 添加处理时间到响应头
    response.headers["X-Process-Time"] = str(process_time)
    
    return response

if __name__ == "__main__":
    print("🚀 启动 TVBox Manager Pro 后端服务...")
    print(f"🌐 访问地址: http://{settings.HOST}:{settings.PORT}")
    print(f"📚 API文档: http://{settings.HOST}:{settings.PORT}/docs")
    print("=" * 50)

    if uvicorn:
        # 开发环境直接运行
        uvicorn.run(
            "app.main:app",
            host=settings.HOST,
            port=settings.PORT,
            reload=settings.DEBUG,
            log_level="info" if not settings.DEBUG else "debug"
        )
    else:
        print("❌ uvicorn 未安装，请运行: pip install uvicorn")
        print("或使用: python -m uvicorn app.main:app --reload")
