<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/error"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/shape_widget_error"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:scaleType="fitCenter"
            android:src="@drawable/ic_widget_error" />

        <TextView
            android:id="@+id/text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="12sp"
            tools:text="@string/error_play_url" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible">

        <include layout="@layout/view_progress" />

        <TextView
            android:id="@+id/traffic"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:visibility="gone"
            tools:text="120KB/s"
            tools:visibility="visible" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/seek"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="top|center"
        android:layout_marginTop="24dp"
        android:background="@drawable/shape_widget"
        android:gravity="center"
        android:orientation="horizontal"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/action"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:src="@drawable/ic_widget_rewind" />

        <TextView
            android:id="@+id/time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="14sp"
            tools:text="00:00:00" />

    </LinearLayout>

    <ImageView
        android:id="@+id/speed"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="top|center"
        android:layout_marginTop="24dp"
        android:src="@drawable/ic_widget_forward"
        android:visibility="gone" />

    <LinearLayout
        android:id="@+id/bright"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="top|center"
        android:layout_marginTop="24dp"
        android:background="@drawable/shape_widget"
        android:gravity="center"
        android:orientation="horizontal"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/brightIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            tools:src="@drawable/ic_widget_bright_high" />

        <com.google.android.material.progressindicator.LinearProgressIndicator
            android:id="@+id/brightProgress"
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            app:indicatorColor="@color/white"
            app:trackColor="@color/grey_500" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/volume"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="top|center"
        android:layout_marginTop="24dp"
        android:background="@drawable/shape_widget"
        android:gravity="center"
        android:orientation="horizontal"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/volumeIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            tools:src="@drawable/ic_widget_volume_high" />

        <com.google.android.material.progressindicator.LinearProgressIndicator
            android:id="@+id/volumeProgress"
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            app:indicatorColor="@color/white"
            app:trackColor="@color/grey_500" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/info"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_gravity="bottom"
        android:background="@drawable/shape_live_info"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            android:id="@+id/number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:textColor="@color/white"
            android:textSize="14sp"
            tools:text="005" />

        <ImageView
            android:id="@+id/logo"
            android:layout_width="44dp"
            android:layout_height="33dp"
            android:layout_marginEnd="12dp"
            android:scaleType="fitCenter"
            android:visibility="gone" />

        <TextView
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:maxEms="48"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="14sp"
            tools:text="民視" />

        <TextView
            android:id="@+id/play"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:layout_weight="1"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="14sp"
            tools:text="正在播放：食神" />

        <TextView
            android:id="@+id/line"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="14sp"
            tools:text="來源 1" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/info_pip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@drawable/shape_live_info"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="12dp"
        android:paddingTop="8dp"
        android:paddingEnd="12dp"
        android:paddingBottom="8dp"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            android:id="@+id/number_pip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:textColor="@color/white"
            android:textSize="12sp"
            tools:text="005" />

        <TextView
            android:id="@+id/name_pip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="12sp"
            tools:text="民視" />

    </LinearLayout>
</FrameLayout>