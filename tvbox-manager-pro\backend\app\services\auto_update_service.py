#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
自动更新服务
负责定时检查和更新TVBox接口配置
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.models.tvbox import InterfaceSource, TvboxConfig
from app.models.system import ScheduledTask
from app.services.interface_service import InterfaceService


class AutoUpdateService:
    """自动更新服务"""
    
    def __init__(self):
        """初始化自动更新服务"""
        self.logger = logging.getLogger(__name__)
        self.interface_service = InterfaceService()
        self.is_running = False
        self.update_interval = 300  # 5分钟检查一次
        
    async def start(self):
        """启动自动更新服务"""
        if self.is_running:
            self.logger.warning("自动更新服务已在运行")
            return
            
        self.is_running = True
        self.logger.info("自动更新服务启动")
        
        try:
            while self.is_running:
                await self._check_and_update_interfaces()
                await asyncio.sleep(self.update_interval)
        except Exception as e:
            self.logger.error(f"自动更新服务异常: {str(e)}")
        finally:
            self.is_running = False
            self.logger.info("自动更新服务停止")
    
    async def stop(self):
        """停止自动更新服务"""
        self.logger.info("正在停止自动更新服务...")
        self.is_running = False
    
    async def _check_and_update_interfaces(self):
        """检查并更新需要更新的接口"""
        try:
            db = next(get_db())
            
            # 获取需要更新的接口
            now = datetime.utcnow()
            interfaces_to_update = db.query(InterfaceSource).filter(
                InterfaceSource.status == "online",
                InterfaceSource.next_update_at <= now
            ).all()
            
            if not interfaces_to_update:
                self.logger.debug("没有需要更新的接口")
                return
            
            self.logger.info(f"发现 {len(interfaces_to_update)} 个接口需要更新")
            
            # 批量更新接口
            update_results = []
            for source in interfaces_to_update:
                try:
                    result = await self._update_single_interface(db, source)
                    update_results.append(result)
                except Exception as e:
                    self.logger.error(f"更新接口 {source.name} 失败: {str(e)}")
                    update_results.append({
                        'source_id': source.id,
                        'source_name': source.name,
                        'success': False,
                        'message': str(e)
                    })
            
            # 统计更新结果
            success_count = sum(1 for r in update_results if r['success'])
            total_count = len(update_results)
            
            self.logger.info(f"批量更新完成: {success_count}/{total_count} 成功")
            
            # 记录更新任务
            await self._record_update_task(db, update_results)
            
        except Exception as e:
            self.logger.error(f"检查更新接口失败: {str(e)}")
        finally:
            if 'db' in locals():
                db.close()
    
    async def _update_single_interface(self, db: Session, source: InterfaceSource) -> Dict[str, Any]:
        """更新单个接口"""
        try:
            self.logger.info(f"开始更新接口: {source.name}")
            
            # 调用接口服务进行智能更新
            success, message = self.interface_service.parse_interface_source(
                db, source.id, force_update=False
            )
            
            result = {
                'source_id': source.id,
                'source_name': source.name,
                'success': success,
                'message': message,
                'updated_at': datetime.utcnow()
            }
            
            if success:
                if "跳过更新" in message:
                    self.logger.info(f"接口 {source.name} {message}")
                else:
                    self.logger.info(f"接口 {source.name} 更新成功: {message}")
            else:
                self.logger.warning(f"接口 {source.name} 更新失败: {message}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"更新接口 {source.name} 异常: {str(e)}")
            return {
                'source_id': source.id,
                'source_name': source.name,
                'success': False,
                'message': f"更新异常: {str(e)}",
                'updated_at': datetime.utcnow()
            }
    
    async def _record_update_task(self, db: Session, results: List[Dict[str, Any]]):
        """记录更新任务"""
        try:
            # 创建或更新定时任务记录
            task = db.query(ScheduledTask).filter(
                ScheduledTask.task_type == "auto_update_interfaces"
            ).first()
            
            if not task:
                task = ScheduledTask(
                    name="自动更新接口",
                    task_type="auto_update_interfaces",
                    config="{}",
                    is_active=True
                )
                db.add(task)
            
            # 更新任务统计
            task.last_run_at = datetime.utcnow()
            task.next_run_at = datetime.utcnow() + timedelta(seconds=self.update_interval)
            task.run_count += 1
            
            success_count = sum(1 for r in results if r['success'])
            if success_count == len(results):
                task.success_count += 1
            else:
                task.error_count += 1
                task.last_error = f"部分更新失败: {success_count}/{len(results)} 成功"
            
            db.commit()
            
        except Exception as e:
            self.logger.error(f"记录更新任务失败: {str(e)}")
    
    async def force_update_all(self) -> Dict[str, Any]:
        """强制更新所有接口"""
        try:
            db = next(get_db())
            
            # 获取所有在线接口
            interfaces = db.query(InterfaceSource).filter(
                InterfaceSource.status == "online"
            ).all()
            
            if not interfaces:
                return {
                    'success': True,
                    'message': '没有可更新的接口',
                    'total': 0,
                    'updated': 0,
                    'skipped': 0,
                    'failed': 0
                }
            
            self.logger.info(f"开始强制更新 {len(interfaces)} 个接口")
            
            # 批量强制更新
            results = []
            for source in interfaces:
                try:
                    success, message = self.interface_service.parse_interface_source(
                        db, source.id, force_update=True
                    )
                    results.append({
                        'source_id': source.id,
                        'source_name': source.name,
                        'success': success,
                        'message': message
                    })
                except Exception as e:
                    results.append({
                        'source_id': source.id,
                        'source_name': source.name,
                        'success': False,
                        'message': str(e)
                    })
            
            # 统计结果
            total = len(results)
            updated = sum(1 for r in results if r['success'] and "跳过" not in r['message'])
            skipped = sum(1 for r in results if r['success'] and "跳过" in r['message'])
            failed = sum(1 for r in results if not r['success'])
            
            return {
                'success': True,
                'message': f'强制更新完成: 总计{total}个，更新{updated}个，跳过{skipped}个，失败{failed}个',
                'total': total,
                'updated': updated,
                'skipped': skipped,
                'failed': failed,
                'details': results
            }
            
        except Exception as e:
            self.logger.error(f"强制更新所有接口失败: {str(e)}")
            return {
                'success': False,
                'message': f'强制更新失败: {str(e)}',
                'total': 0,
                'updated': 0,
                'skipped': 0,
                'failed': 0
            }
        finally:
            if 'db' in locals():
                db.close()


# 全局自动更新服务实例
auto_update_service = AutoUpdateService()
