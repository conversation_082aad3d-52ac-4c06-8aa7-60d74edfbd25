/* TVBox API 测试工具样式 */

:root {
    --primary-color: #0d6efd;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --dark-color: #212529;
    --light-color: #f8f9fa;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    padding: 1rem 1.25rem;
}

/* 列表组样式 */
.list-group-item {
    border: none;
    padding: 0.75rem 1.25rem;
    transition: all 0.15s ease-in-out;
}

.list-group-item:hover {
    background-color: #f8f9fa;
}

.list-group-item.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.list-group-item i {
    width: 1.2rem;
    margin-right: 0.5rem;
}

/* 表单样式 */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* 按钮样式 */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.15s ease-in-out;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* 标签页样式 */
.nav-tabs {
    border-bottom: 2px solid #dee2e6;
}

.nav-tabs .nav-link {
    border: none;
    color: #6c757d;
    font-weight: 500;
    padding: 0.75rem 1rem;
    margin-bottom: -2px;
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    color: var(--primary-color);
}

.nav-tabs .nav-link.active {
    color: var(--primary-color);
    background-color: transparent;
    border-bottom: 2px solid var(--primary-color);
}

/* 日志容器样式 */
.logs-container {
    background-color: #1e1e1e;
    color: #d4d4d4;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.875rem;
    padding: 1rem;
    border-radius: 0.375rem;
    max-height: 500px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.log-entry {
    margin-bottom: 0.5rem;
    padding: 0.25rem 0;
    border-bottom: 1px solid #333;
}

.log-timestamp {
    color: #569cd6;
    font-weight: 500;
}

.log-level-info {
    color: #4ec9b0;
}

.log-level-success {
    color: #b5cea8;
}

.log-level-warning {
    color: #dcdcaa;
}

.log-level-error {
    color: #f44747;
}

/* 响应显示样式 */
.response-container {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-top: 1rem;
}

.response-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #dee2e6;
}

.response-status {
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

.response-status.success {
    background-color: #d1e7dd;
    color: #0f5132;
}

.response-status.error {
    background-color: #f8d7da;
    color: #842029;
}

.response-time {
    color: #6c757d;
    font-size: 0.875rem;
}

.response-body {
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 1rem;
    max-height: 400px;
    overflow-y: auto;
}

/* 代码高亮样式 */
pre[class*="language-"] {
    margin: 0;
    padding: 1rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* 进度条样式 */
.progress {
    height: 0.75rem;
    border-radius: 0.375rem;
}

.progress-bar {
    transition: width 0.3s ease;
}

/* 状态徽章样式 */
.badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
}

.status-online {
    background-color: var(--success-color) !important;
}

.status-offline {
    background-color: var(--danger-color) !important;
}

.status-testing {
    background-color: var(--warning-color) !important;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
}

.table td {
    vertical-align: middle;
}

/* 统计卡片样式 */
.stats-card {
    text-align: center;
    padding: 1.5rem;
    border-radius: 0.5rem;
    color: white;
    margin-bottom: 1rem;
}

.stats-card.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-card.success {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-card.warning {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-card.danger {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .logs-container {
        font-size: 0.75rem;
        max-height: 300px;
    }
    
    .stats-number {
        font-size: 1.5rem;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

/* 加载动画 */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* 自定义复选框样式 */
.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* 模态框样式 */
.modal-xl {
    max-width: 90%;
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
}
