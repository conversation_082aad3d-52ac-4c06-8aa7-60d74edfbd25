#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
TVBox Manager Pro 启动脚本
"""

import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

if __name__ == "__main__":
    import uvicorn
    
    # 设置环境变量
    os.environ.setdefault("PYTHONPATH", str(current_dir))
    
    print("🚀 启动 TVBox Manager Pro 后端服务...")
    print(f"📁 工作目录: {current_dir}")
    print(f"🌐 访问地址: http://localhost:8001")
    print(f"📚 API文档: http://localhost:8001/docs")
    print("=" * 50)

    # 启动服务
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        reload_dirs=[str(current_dir)],
        log_level="info"
    )
