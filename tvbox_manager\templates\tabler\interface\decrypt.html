{% extends "tabler/base.html" %}

{% set active_nav = 'decrypt' %}
{% block title %}接口解密 - TVBox Manager{% endblock %}
{% block page_title %}接口解密{% endblock %}

{% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <h2 class="page-title">
          接口解密
        </h2>
        <div class="text-muted mt-1">解析并解密TVBox接口</div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <div class="row row-deck row-cards">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">解密工具</h3>
          </div>
          <div class="card-body">
            <form action="{{ url_for('interface.decrypt') }}" method="post" enctype="multipart/form-data">
              {{ form.csrf_token }}
              
              <div class="row mb-3">
                <div class="col-md-4">
                  <div class="form-group mb-3">
                    <label class="form-label">解密类型</label>
                    <div class="form-selectgroup">
                      <label class="form-selectgroup-item">
                        <input type="radio" name="type" value="url" class="form-selectgroup-input" checked>
                        <span class="form-selectgroup-label">
                          <i class="ti ti-link me-2"></i>URL
                        </span>
                      </label>
                      <label class="form-selectgroup-item">
                        <input type="radio" name="type" value="text" class="form-selectgroup-input">
                        <span class="form-selectgroup-label">
                          <i class="ti ti-text me-2"></i>文本
                        </span>
                      </label>
                      <label class="form-selectgroup-item">
                        <input type="radio" name="type" value="file" class="form-selectgroup-input">
                        <span class="form-selectgroup-label">
                          <i class="ti ti-file me-2"></i>文件
                        </span>
                      </label>
                    </div>
                  </div>
                </div>
                
                <div class="col-md-4">
                  <div class="form-group mb-3">
                    <label class="form-label">解密方法</label>
                    <select name="method" class="form-select" id="method-select">
                      <option value="auto">自动检测</option>
                      <option value="base64">Base64</option>
                      <option value="json">JSON</option>
                      <option value="aes">AES</option>
                      <option value="rc4">RC4</option>
                    </select>
                  </div>
                </div>
                
                <div class="col-md-4">
                  <div class="form-group mb-3" id="key-group" style="display: none;">
                    <label class="form-label">密钥(Key)</label>
                    <input type="text" name="key" class="form-control" placeholder="输入解密密钥">
                    <div class="form-hint">对于AES或RC4等加密方法，需要提供密钥</div>
                  </div>
                </div>
              </div>
              
              <div class="mb-3" id="url-input">
                <label class="form-label">接口URL</label>
                <div class="input-group">
                  <span class="input-group-text">
                    <i class="ti ti-link"></i>
                  </span>
                  <input type="text" name="url" class="form-control" placeholder="输入TVBox接口URL，如: http://example.com/tv">
                </div>
              </div>
              
              <div class="mb-3" id="text-input" style="display: none;">
                <label class="form-label">加密文本</label>
                <textarea name="text" class="form-control" rows="5" placeholder="输入需要解密的文本内容"></textarea>
              </div>
              
              <div class="mb-3" id="file-input" style="display: none;">
                <label class="form-label">加密文件</label>
                <input type="file" name="file" class="form-control">
              </div>
              
              <div class="form-footer">
                <button type="submit" class="btn btn-primary">
                  <i class="ti ti-unlock me-2"></i>解密
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
      
      {% if result %}
      <div class="col-12 mt-3">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">解密结果</h3>
            <div class="card-actions">
              <div class="btn-list">
                <button type="button" id="copy-btn" class="btn btn-outline-primary btn-sm">
                  <i class="ti ti-copy me-1"></i>复制
                </button>
                <button type="button" id="save-btn" class="btn btn-outline-secondary btn-sm">
                  <i class="ti ti-download me-1"></i>保存
                </button>
                <a href="{{ url_for('interface.add') }}?url={{ url }}&method={{ method }}" class="btn btn-primary btn-sm">
                  <i class="ti ti-plus me-1"></i>添加到接口
                </a>
              </div>
            </div>
          </div>
          
          <div class="card-body">
            {% if success %}
            <div class="alert alert-success">
              <div class="d-flex">
                <div>
                  <i class="ti ti-check me-2"></i>
                </div>
                <div>
                  <h4>解密成功</h4>
                  <div class="text-muted">解密方法: {{ method }}</div>
                </div>
              </div>
            </div>
            
            <div class="mt-3">
              <pre id="result-content" style="max-height: 500px; overflow: auto; background-color: #f8fafc; padding: 15px; border-radius: 4px;">{{ result }}</pre>
            </div>
            {% else %}
            <div class="alert alert-danger">
              <div class="d-flex">
                <div>
                  <i class="ti ti-alert-circle me-2"></i>
                </div>
                <div>
                  <h4>解密失败</h4>
                  <div class="text-muted">{{ result }}</div>
                </div>
              </div>
            </div>
            {% endif %}
          </div>
        </div>
      </div>
      {% endif %}
      
      <div class="col-12 mt-3">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">解密帮助</h3>
          </div>
          <div class="card-body">
            <div class="alert alert-info">
              <div class="d-flex">
                <div>
                  <i class="ti ti-info-circle me-2"></i>
                </div>
                <div>
                  <h4>接口解密说明</h4>
                  <p>接口解密工具支持多种加密方式，常见的TVBox接口加密方式包括Base64、AES、RC4等。</p>
                </div>
              </div>
            </div>
            
            <div class="row mt-4">
              <div class="col-md-4">
                <div class="card">
                  <div class="card-body">
                    <h4 class="mb-3 card-title">自动检测失败怎么办？</h4>
                    <p class="text-muted">尝试手动选择解密方法，并提供正确的密钥（如需要）。</p>
                  </div>
                </div>
              </div>
              
              <div class="col-md-4">
                <div class="card">
                  <div class="card-body">
                    <h4 class="mb-3 card-title">解密后格式错乱？</h4>
                    <p class="text-muted">可能是选择了错误的解密方法，或者源文件使用了多重加密。</p>
                  </div>
                </div>
              </div>
              
              <div class="col-md-4">
                <div class="card">
                  <div class="card-body">
                    <h4 class="mb-3 card-title">无法获取远程接口？</h4>
                    <p class="text-muted">检查URL是否正确，或者服务器是否有访问限制。</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
  // 切换解密类型
  const typeRadios = document.querySelectorAll('input[name="type"]');
  const urlInput = document.getElementById('url-input');
  const textInput = document.getElementById('text-input');
  const fileInput = document.getElementById('file-input');
  
  typeRadios.forEach(function(radio) {
    radio.addEventListener('change', function() {
      // 隐藏所有输入框
      urlInput.style.display = 'none';
      textInput.style.display = 'none';
      fileInput.style.display = 'none';
      
      // 显示选中的输入框
      if (this.value === 'url') {
        urlInput.style.display = 'block';
      } else if (this.value === 'text') {
        textInput.style.display = 'block';
      } else if (this.value === 'file') {
        fileInput.style.display = 'block';
      }
    });
  });
  
  // 切换解密方法显示密钥输入框
  const methodSelect = document.getElementById('method-select');
  const keyGroup = document.getElementById('key-group');
  
  methodSelect.addEventListener('change', function() {
    if (this.value === 'aes' || this.value === 'rc4') {
      keyGroup.style.display = 'block';
    } else {
      keyGroup.style.display = 'none';
    }
  });
  
  // 复制解密结果
  const copyBtn = document.getElementById('copy-btn');
  if (copyBtn) {
    copyBtn.addEventListener('click', function() {
      const resultContent = document.getElementById('result-content');
      const text = resultContent.textContent;
      
      navigator.clipboard.writeText(text)
        .then(() => {
          copyBtn.innerHTML = '<i class="ti ti-check me-1"></i>已复制';
          copyBtn.classList.remove('btn-outline-primary');
          copyBtn.classList.add('btn-success');
          
          setTimeout(() => {
            copyBtn.innerHTML = '<i class="ti ti-copy me-1"></i>复制';
            copyBtn.classList.remove('btn-success');
            copyBtn.classList.add('btn-outline-primary');
          }, 2000);
        })
        .catch(err => {
          console.error('复制失败:', err);
        });
    });
  }
  
  // 保存为文件
  const saveBtn = document.getElementById('save-btn');
  if (saveBtn) {
    saveBtn.addEventListener('click', function() {
      const resultContent = document.getElementById('result-content');
      const text = resultContent.textContent;
      const blob = new Blob([text], {type: 'application/json'});
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      
      a.href = url;
      a.download = 'decrypted_' + new Date().getTime() + '.json';
      a.click();
      
      URL.revokeObjectURL(url);
    });
  }
});
</script>
{% endblock %} 

{% set active_nav = 'decrypt' %}
{% block title %}接口解密 - TVBox Manager{% endblock %}
{% block page_title %}接口解密{% endblock %}

{% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <h2 class="page-title">
          接口解密
        </h2>
        <div class="text-muted mt-1">解析并解密TVBox接口</div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <div class="row row-deck row-cards">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">解密工具</h3>
          </div>
          <div class="card-body">
            <form action="{{ url_for('interface.decrypt') }}" method="post" enctype="multipart/form-data">
              {{ form.csrf_token }}
              
              <div class="row mb-3">
                <div class="col-md-4">
                  <div class="form-group mb-3">
                    <label class="form-label">解密类型</label>
                    <div class="form-selectgroup">
                      <label class="form-selectgroup-item">
                        <input type="radio" name="type" value="url" class="form-selectgroup-input" checked>
                        <span class="form-selectgroup-label">
                          <i class="ti ti-link me-2"></i>URL
                        </span>
                      </label>
                      <label class="form-selectgroup-item">
                        <input type="radio" name="type" value="text" class="form-selectgroup-input">
                        <span class="form-selectgroup-label">
                          <i class="ti ti-text me-2"></i>文本
                        </span>
                      </label>
                      <label class="form-selectgroup-item">
                        <input type="radio" name="type" value="file" class="form-selectgroup-input">
                        <span class="form-selectgroup-label">
                          <i class="ti ti-file me-2"></i>文件
                        </span>
                      </label>
                    </div>
                  </div>
                </div>
                
                <div class="col-md-4">
                  <div class="form-group mb-3">
                    <label class="form-label">解密方法</label>
                    <select name="method" class="form-select" id="method-select">
                      <option value="auto">自动检测</option>
                      <option value="base64">Base64</option>
                      <option value="json">JSON</option>
                      <option value="aes">AES</option>
                      <option value="rc4">RC4</option>
                    </select>
                  </div>
                </div>
                
                <div class="col-md-4">
                  <div class="form-group mb-3" id="key-group" style="display: none;">
                    <label class="form-label">密钥(Key)</label>
                    <input type="text" name="key" class="form-control" placeholder="输入解密密钥">
                    <div class="form-hint">对于AES或RC4等加密方法，需要提供密钥</div>
                  </div>
                </div>
              </div>
              
              <div class="mb-3" id="url-input">
                <label class="form-label">接口URL</label>
                <div class="input-group">
                  <span class="input-group-text">
                    <i class="ti ti-link"></i>
                  </span>
                  <input type="text" name="url" class="form-control" placeholder="输入TVBox接口URL，如: http://example.com/tv">
                </div>
              </div>
              
              <div class="mb-3" id="text-input" style="display: none;">
                <label class="form-label">加密文本</label>
                <textarea name="text" class="form-control" rows="5" placeholder="输入需要解密的文本内容"></textarea>
              </div>
              
              <div class="mb-3" id="file-input" style="display: none;">
                <label class="form-label">加密文件</label>
                <input type="file" name="file" class="form-control">
              </div>
              
              <div class="form-footer">
                <button type="submit" class="btn btn-primary">
                  <i class="ti ti-unlock me-2"></i>解密
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
      
      {% if result %}
      <div class="col-12 mt-3">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">解密结果</h3>
            <div class="card-actions">
              <div class="btn-list">
                <button type="button" id="copy-btn" class="btn btn-outline-primary btn-sm">
                  <i class="ti ti-copy me-1"></i>复制
                </button>
                <button type="button" id="save-btn" class="btn btn-outline-secondary btn-sm">
                  <i class="ti ti-download me-1"></i>保存
                </button>
                <a href="{{ url_for('interface.add') }}?url={{ url }}&method={{ method }}" class="btn btn-primary btn-sm">
                  <i class="ti ti-plus me-1"></i>添加到接口
                </a>
              </div>
            </div>
          </div>
          
          <div class="card-body">
            {% if success %}
            <div class="alert alert-success">
              <div class="d-flex">
                <div>
                  <i class="ti ti-check me-2"></i>
                </div>
                <div>
                  <h4>解密成功</h4>
                  <div class="text-muted">解密方法: {{ method }}</div>
                </div>
              </div>
            </div>
            
            <div class="mt-3">
              <pre id="result-content" style="max-height: 500px; overflow: auto; background-color: #f8fafc; padding: 15px; border-radius: 4px;">{{ result }}</pre>
            </div>
            {% else %}
            <div class="alert alert-danger">
              <div class="d-flex">
                <div>
                  <i class="ti ti-alert-circle me-2"></i>
                </div>
                <div>
                  <h4>解密失败</h4>
                  <div class="text-muted">{{ result }}</div>
                </div>
              </div>
            </div>
            {% endif %}
          </div>
        </div>
      </div>
      {% endif %}
      
      <div class="col-12 mt-3">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">解密帮助</h3>
          </div>
          <div class="card-body">
            <div class="alert alert-info">
              <div class="d-flex">
                <div>
                  <i class="ti ti-info-circle me-2"></i>
                </div>
                <div>
                  <h4>接口解密说明</h4>
                  <p>接口解密工具支持多种加密方式，常见的TVBox接口加密方式包括Base64、AES、RC4等。</p>
                </div>
              </div>
            </div>
            
            <div class="row mt-4">
              <div class="col-md-4">
                <div class="card">
                  <div class="card-body">
                    <h4 class="mb-3 card-title">自动检测失败怎么办？</h4>
                    <p class="text-muted">尝试手动选择解密方法，并提供正确的密钥（如需要）。</p>
                  </div>
                </div>
              </div>
              
              <div class="col-md-4">
                <div class="card">
                  <div class="card-body">
                    <h4 class="mb-3 card-title">解密后格式错乱？</h4>
                    <p class="text-muted">可能是选择了错误的解密方法，或者源文件使用了多重加密。</p>
                  </div>
                </div>
              </div>
              
              <div class="col-md-4">
                <div class="card">
                  <div class="card-body">
                    <h4 class="mb-3 card-title">无法获取远程接口？</h4>
                    <p class="text-muted">检查URL是否正确，或者服务器是否有访问限制。</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
  // 切换解密类型
  const typeRadios = document.querySelectorAll('input[name="type"]');
  const urlInput = document.getElementById('url-input');
  const textInput = document.getElementById('text-input');
  const fileInput = document.getElementById('file-input');
  
  typeRadios.forEach(function(radio) {
    radio.addEventListener('change', function() {
      // 隐藏所有输入框
      urlInput.style.display = 'none';
      textInput.style.display = 'none';
      fileInput.style.display = 'none';
      
      // 显示选中的输入框
      if (this.value === 'url') {
        urlInput.style.display = 'block';
      } else if (this.value === 'text') {
        textInput.style.display = 'block';
      } else if (this.value === 'file') {
        fileInput.style.display = 'block';
      }
    });
  });
  
  // 切换解密方法显示密钥输入框
  const methodSelect = document.getElementById('method-select');
  const keyGroup = document.getElementById('key-group');
  
  methodSelect.addEventListener('change', function() {
    if (this.value === 'aes' || this.value === 'rc4') {
      keyGroup.style.display = 'block';
    } else {
      keyGroup.style.display = 'none';
    }
  });
  
  // 复制解密结果
  const copyBtn = document.getElementById('copy-btn');
  if (copyBtn) {
    copyBtn.addEventListener('click', function() {
      const resultContent = document.getElementById('result-content');
      const text = resultContent.textContent;
      
      navigator.clipboard.writeText(text)
        .then(() => {
          copyBtn.innerHTML = '<i class="ti ti-check me-1"></i>已复制';
          copyBtn.classList.remove('btn-outline-primary');
          copyBtn.classList.add('btn-success');
          
          setTimeout(() => {
            copyBtn.innerHTML = '<i class="ti ti-copy me-1"></i>复制';
            copyBtn.classList.remove('btn-success');
            copyBtn.classList.add('btn-outline-primary');
          }, 2000);
        })
        .catch(err => {
          console.error('复制失败:', err);
        });
    });
  }
  
  // 保存为文件
  const saveBtn = document.getElementById('save-btn');
  if (saveBtn) {
    saveBtn.addEventListener('click', function() {
      const resultContent = document.getElementById('result-content');
      const text = resultContent.textContent;
      const blob = new Blob([text], {type: 'application/json'});
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      
      a.href = url;
      a.download = 'decrypted_' + new Date().getTime() + '.json';
      a.click();
      
      URL.revokeObjectURL(url);
    });
  }
});
</script>
{% endblock %} 