#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def test_xhztv_simple():
    """简单测试xhztv.top/4k.json接口"""
    
    from app.services.interface_service import InterfaceService
    import json
    
    service = InterfaceService()
    url = "http://xhztv.top/4k.json"
    
    print(f"测试xhztv接口: {url}")
    
    try:
        # 完整解密流程
        print("=== 完整解密流程 ===")
        full_content, full_method = service.decryptor.decrypt_config_url(url)
        print(f"解密方法: {full_method}")
        print(f"内容长度: {len(full_content)}")
        
        # 检查最终结果
        try:
            final_config = json.loads(full_content)
            print(f"✅ JSON解析成功")
            if isinstance(final_config, dict):
                sites_count = len(final_config.get('sites', []))
                lives_count = len(final_config.get('lives', []))
                parses_count = len(final_config.get('parses', []))
                print(f"配置统计: {sites_count}个站点, {lives_count}个直播源, {parses_count}个解析器")
                
                if sites_count == 0:
                    print("⚠️ 警告：没有解析出任何站点！")
                    print(f"配置包含的字段: {list(final_config.keys())}")
                    
                    # 检查sites字段的具体内容
                    sites = final_config.get('sites', [])
                    print(f"sites字段类型: {type(sites)}")
                    if isinstance(sites, list):
                        print(f"sites列表长度: {len(sites)}")
                        if len(sites) > 0:
                            print(f"第一个站点: {sites[0]}")
                else:
                    print("✅ 成功解析出站点数据")
                    # 显示前几个站点
                    sites = final_config.get('sites', [])
                    print("前3个站点:")
                    for i, site in enumerate(sites[:3]):
                        name = site.get('name', '未知') if isinstance(site, dict) else str(site)
                        print(f"  {i+1}. {name}")
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            print(f"内容前500字符: {repr(full_content[:500])}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_xhztv_simple()
