// 测试结果管理
class ResultsManager {
    constructor(app) {
        this.app = app;
        this.currentFilter = 'all';
        this.currentSort = 'timestamp';
    }
    
    updateResults() {
        const resultsContent = document.getElementById('resultsContent');
        
        if (!this.app.testResults || this.app.testResults.length === 0) {
            resultsContent.innerHTML = `
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> 
                    暂无测试结果，请先运行测试。
                </div>
            `;
            return;
        }
        
        // 生成统计信息
        const stats = this.generateStats();
        
        // 生成结果表格
        const filteredResults = this.filterResults();
        const sortedResults = this.sortResults(filteredResults);
        
        let html = `
            <!-- 统计卡片 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stats-card primary">
                        <div class="stats-number">${stats.total}</div>
                        <div class="stats-label">总测试数</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card success">
                        <div class="stats-number">${stats.success}</div>
                        <div class="stats-label">成功</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card danger">
                        <div class="stats-number">${stats.failed}</div>
                        <div class="stats-label">失败</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card warning">
                        <div class="stats-number">${stats.successRate}%</div>
                        <div class="stats-label">成功率</div>
                    </div>
                </div>
            </div>
            
            <!-- 过滤和排序 -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary ${this.currentFilter === 'all' ? 'active' : ''}" 
                                onclick="resultsManager.setFilter('all')">
                            全部 (${stats.total})
                        </button>
                        <button type="button" class="btn btn-outline-success ${this.currentFilter === 'success' ? 'active' : ''}" 
                                onclick="resultsManager.setFilter('success')">
                            成功 (${stats.success})
                        </button>
                        <button type="button" class="btn btn-outline-danger ${this.currentFilter === 'failed' ? 'active' : ''}" 
                                onclick="resultsManager.setFilter('failed')">
                            失败 (${stats.failed})
                        </button>
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="resultsManager.exportResults('json')">
                            <i class="bi bi-download"></i> 导出JSON
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="resultsManager.exportResults('html')">
                            <i class="bi bi-file-earmark-text"></i> 导出HTML
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="resultsManager.clearResults()">
                            <i class="bi bi-trash"></i> 清空
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 结果表格 -->
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>
                                <a href="#" onclick="resultsManager.setSort('timestamp')" class="text-decoration-none">
                                    时间 ${this.currentSort === 'timestamp' ? '<i class="bi bi-arrow-down"></i>' : ''}
                                </a>
                            </th>
                            <th>
                                <a href="#" onclick="resultsManager.setSort('category')" class="text-decoration-none">
                                    分类 ${this.currentSort === 'category' ? '<i class="bi bi-arrow-down"></i>' : ''}
                                </a>
                            </th>
                            <th>API名称</th>
                            <th>方法</th>
                            <th>
                                <a href="#" onclick="resultsManager.setSort('status')" class="text-decoration-none">
                                    状态 ${this.currentSort === 'status' ? '<i class="bi bi-arrow-down"></i>' : ''}
                                </a>
                            </th>
                            <th>
                                <a href="#" onclick="resultsManager.setSort('responseTime')" class="text-decoration-none">
                                    响应时间 ${this.currentSort === 'responseTime' ? '<i class="bi bi-arrow-down"></i>' : ''}
                                </a>
                            </th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        if (sortedResults.length === 0) {
            html += `
                <tr>
                    <td colspan="7" class="text-center text-muted">
                        没有符合条件的测试结果
                    </td>
                </tr>
            `;
        } else {
            sortedResults.forEach((result, index) => {
                const timestamp = new Date(result.timestamp).toLocaleString();
                const success = result.success || (result.response && result.response.status >= 200 && result.response.status < 300);
                const statusBadge = success ? 
                    '<span class="badge bg-success">成功</span>' : 
                    '<span class="badge bg-danger">失败</span>';
                
                const responseTime = result.responseTime || 0;
                const categoryName = result.test ? result.test.categoryName : '手动测试';
                const apiName = result.test ? result.test.apiDef.name : '手动测试';
                const method = result.test ? result.test.apiDef.method : (result.request ? result.request.method : 'GET');
                
                html += `
                    <tr>
                        <td>${timestamp}</td>
                        <td>${categoryName}</td>
                        <td>${apiName}</td>
                        <td><span class="badge bg-secondary">${method}</span></td>
                        <td>${statusBadge}</td>
                        <td>${responseTime}ms</td>
                        <td>
                            <button class="btn btn-sm btn-outline-info" onclick="resultsManager.showResultDetail(${index})">
                                <i class="bi bi-eye"></i> 详情
                            </button>
                        </td>
                    </tr>
                `;
            });
        }
        
        html += `
                    </tbody>
                </table>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
    
    generateStats() {
        const results = this.app.testResults;
        const total = results.length;
        const success = results.filter(r => r.success || (r.response && r.response.status >= 200 && r.response.status < 300)).length;
        const failed = total - success;
        const successRate = total > 0 ? Math.round((success / total) * 100) : 0;
        
        return { total, success, failed, successRate };
    }
    
    filterResults() {
        if (this.currentFilter === 'all') {
            return this.app.testResults;
        } else if (this.currentFilter === 'success') {
            return this.app.testResults.filter(r => r.success || (r.response && r.response.status >= 200 && r.response.status < 300));
        } else if (this.currentFilter === 'failed') {
            return this.app.testResults.filter(r => !r.success && !(r.response && r.response.status >= 200 && r.response.status < 300));
        }
        return this.app.testResults;
    }
    
    sortResults(results) {
        return [...results].sort((a, b) => {
            switch (this.currentSort) {
                case 'timestamp':
                    return new Date(b.timestamp) - new Date(a.timestamp);
                case 'category':
                    const aCat = a.test ? a.test.categoryName : 'Z';
                    const bCat = b.test ? b.test.categoryName : 'Z';
                    return aCat.localeCompare(bCat);
                case 'status':
                    const aSuccess = a.success || (a.response && a.response.status >= 200 && a.response.status < 300);
                    const bSuccess = b.success || (b.response && b.response.status >= 200 && b.response.status < 300);
                    return bSuccess - aSuccess;
                case 'responseTime':
                    return (b.responseTime || 0) - (a.responseTime || 0);
                default:
                    return 0;
            }
        });
    }
    
    setFilter(filter) {
        this.currentFilter = filter;
        this.updateResults();
    }
    
    setSort(sort) {
        this.currentSort = sort;
        this.updateResults();
    }
    
    showResultDetail(index) {
        const filteredResults = this.filterResults();
        const sortedResults = this.sortResults(filteredResults);
        const result = sortedResults[index];
        
        if (!result) {
            this.app.showToast('结果不存在', 'error');
            return;
        }
        
        const modal = document.getElementById('responseModal');
        const modalContent = document.getElementById('responseContent');
        
        let detailHtml = `
            <div class="row">
                <div class="col-md-6">
                    <h6><i class="bi bi-info-circle"></i> 基本信息</h6>
                    <table class="table table-sm">
                        <tr><td>时间:</td><td>${new Date(result.timestamp).toLocaleString()}</td></tr>
                        <tr><td>分类:</td><td>${result.test ? result.test.categoryName : '手动测试'}</td></tr>
                        <tr><td>API:</td><td>${result.test ? result.test.apiDef.name : '手动测试'}</td></tr>
                        <tr><td>方法:</td><td>${result.test ? result.test.apiDef.method : (result.request ? result.request.method : 'GET')}</td></tr>
                        <tr><td>状态:</td><td>${result.success ? '成功' : '失败'}</td></tr>
                        <tr><td>响应时间:</td><td>${result.responseTime || 0}ms</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6><i class="bi bi-arrow-up-right"></i> 请求信息</h6>
                    <pre><code class="language-json">${JSON.stringify(result.request, null, 2)}</code></pre>
                </div>
            </div>
            
            <div class="mt-3">
                <h6><i class="bi bi-arrow-down-left"></i> 响应信息</h6>
        `;
        
        if (result.errorMessage) {
            detailHtml += `
                <div class="alert alert-danger">
                    <strong>错误:</strong> ${result.errorMessage}
                </div>
            `;
        }
        
        if (result.response) {
            detailHtml += `
                <pre><code class="language-json">${JSON.stringify(result.response, null, 2)}</code></pre>
            `;
        }
        
        detailHtml += '</div>';
        
        modalContent.innerHTML = detailHtml;
        
        // 高亮代码
        if (window.Prism) {
            Prism.highlightAll();
        }
        
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
    }
    
    exportResults(format) {
        if (!this.app.testResults || this.app.testResults.length === 0) {
            this.app.showToast('没有可导出的结果', 'warning');
            return;
        }
        
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `tvbox-api-test-results-${timestamp}`;
        
        if (format === 'json') {
            this.exportJSON(filename);
        } else if (format === 'html') {
            this.exportHTML(filename);
        }
    }
    
    exportJSON(filename) {
        const data = {
            exportTime: new Date().toISOString(),
            totalResults: this.app.testResults.length,
            stats: this.generateStats(),
            results: this.app.testResults
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        this.downloadFile(blob, `${filename}.json`);
        
        this.app.showToast('JSON结果已导出', 'success');
    }
    
    exportHTML(filename) {
        const stats = this.generateStats();
        
        let html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TVBox API 测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .stats { display: flex; justify-content: space-around; margin-bottom: 30px; }
        .stat-card { text-align: center; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .stat-number { font-size: 2em; font-weight: bold; }
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .success { color: green; }
        .failed { color: red; }
    </style>
</head>
<body>
    <div class="header">
        <h1>TVBox API 测试报告</h1>
        <p>生成时间: ${new Date().toLocaleString()}</p>
    </div>
    
    <div class="stats">
        <div class="stat-card">
            <div class="stat-number">${stats.total}</div>
            <div>总测试数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number success">${stats.success}</div>
            <div>成功</div>
        </div>
        <div class="stat-card">
            <div class="stat-number failed">${stats.failed}</div>
            <div>失败</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${stats.successRate}%</div>
            <div>成功率</div>
        </div>
    </div>
    
    <table>
        <thead>
            <tr>
                <th>时间</th>
                <th>分类</th>
                <th>API名称</th>
                <th>方法</th>
                <th>状态</th>
                <th>响应时间</th>
            </tr>
        </thead>
        <tbody>
        `;
        
        this.app.testResults.forEach(result => {
            const timestamp = new Date(result.timestamp).toLocaleString();
            const success = result.success || (result.response && result.response.status >= 200 && result.response.status < 300);
            const statusClass = success ? 'success' : 'failed';
            const statusText = success ? '成功' : '失败';
            const categoryName = result.test ? result.test.categoryName : '手动测试';
            const apiName = result.test ? result.test.apiDef.name : '手动测试';
            const method = result.test ? result.test.apiDef.method : (result.request ? result.request.method : 'GET');
            
            html += `
                <tr>
                    <td>${timestamp}</td>
                    <td>${categoryName}</td>
                    <td>${apiName}</td>
                    <td>${method}</td>
                    <td class="${statusClass}">${statusText}</td>
                    <td>${result.responseTime || 0}ms</td>
                </tr>
            `;
        });
        
        html += `
        </tbody>
    </table>
</body>
</html>
        `;
        
        const blob = new Blob([html], { type: 'text/html' });
        this.downloadFile(blob, `${filename}.html`);
        
        this.app.showToast('HTML报告已导出', 'success');
    }
    
    downloadFile(blob, filename) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
    
    clearResults() {
        if (confirm('确定要清空所有测试结果吗？')) {
            this.app.testResults = [];
            this.updateResults();
            this.app.showToast('测试结果已清空', 'info');
        }
    }
}

// 全局函数
function refreshResults() {
    if (window.resultsManager) {
        window.resultsManager.updateResults();
    }
}

function exportResults() {
    if (!window.resultsManager) {
        window.resultsManager = new ResultsManager(app);
    }
    window.resultsManager.exportResults('json');
}

function clearResults() {
    if (!window.resultsManager) {
        window.resultsManager = new ResultsManager(app);
    }
    window.resultsManager.clearResults();
}

// 初始化结果管理器
document.addEventListener('DOMContentLoaded', function() {
    // 监听结果页面标签点击
    const resultsTab = document.getElementById('results-tab');
    if (resultsTab) {
        resultsTab.addEventListener('click', function() {
            setTimeout(() => {
                if (!window.resultsManager) {
                    window.resultsManager = new ResultsManager(app);
                }
                window.resultsManager.updateResults();
            }, 100);
        });
    }
});
