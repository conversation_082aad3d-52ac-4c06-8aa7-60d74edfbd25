{% extends 'base.html' %}

{% block title %}TVBox助手 - 首页{% endblock %}

{% block content %}
<div class="container py-4">
    <header class="pb-3 mb-4 border-bottom">
        <h1 class="display-5 fw-bold">
            <i class="fas fa-tv me-2"></i> TVBox助手
        </h1>
        <p class="fs-4">解析、解密和管理TVBox配置文件</p>
    </header>

    <div class="p-5 mb-4 bg-light rounded-3">
        <div class="container-fluid py-5">
            <h2 class="display-6 fw-bold">上传配置文件</h2>
            <p class="col-md-8 fs-5">
                上传TVBox配置文件，自动解析并显示配置信息。支持解密、下载资源和本地化处理。
            </p>
            <form action="{{ url_for('routes.upload_file') }}" method="post" enctype="multipart/form-data" class="mt-4">
                <div class="mb-3">
                    <input class="form-control" type="file" id="formFile" name="file" accept=".json,.txt">
                    <div class="form-text">支持JSON和TXT格式的TVBox配置文件</div>
                </div>
                <button class="btn btn-primary btn-lg" type="submit">
                    <i class="fas fa-upload me-2"></i>上传并解析
                </button>
            </form>
        </div>
    </div>

    <div class="row align-items-md-stretch">
        <div class="col-md-6 mb-4">
            <div class="h-100 p-5 bg-light border rounded-3">
                <h2>
                    <i class="fas fa-unlock-alt me-2"></i>接口解密
                </h2>
                <p>解密TVBox中的加密接口URL，支持多种解密算法和自动解密。</p>
                <a href="{{ url_for('routes.decrypt') }}" class="btn btn-outline-secondary">
                    开始解密
                </a>
            </div>
        </div>
        <div class="col-md-6 mb-4">
            <div class="h-100 p-5 bg-light border rounded-3">
                <h2>
                    <i class="fas fa-cog me-2"></i>配置管理
                </h2>
                <p>管理上传的配置文件，查看配置详情，处理配置文件并下载资源。</p>
                <a href="{{ url_for('routes.configs') }}" class="btn btn-outline-secondary">
                    管理配置
                </a>
            </div>
        </div>
    </div>

    <div class="row align-items-md-stretch mt-2">
        <div class="col-md-12 mb-4">
            <div class="h-100 p-5 bg-light border rounded-3">
                <h2>
                    <i class="fas fa-info-circle me-2"></i>关于TVBox助手
                </h2>
                <p>
                    TVBox助手是一个用于解析、解密和管理TVBox配置文件的工具。它可以自动解析TVBox配置文件中的资源链接，
                    解密加密的URL，下载远程资源到本地，并更新配置文件指向本地资源。这样可以有效提高TVBox的使用体验，
                    避免因远程资源不稳定导致的问题。
                </p>
                <p>
                    <strong>主要功能：</strong>
                </p>
                <ul>
                    <li>解析TVBox配置文件结构</li>
                    <li>自动识别并解密加密的接口URL</li>
                    <li>下载远程资源到本地</li>
                    <li>更新配置文件中的链接指向本地资源</li>
                    <li>提供配置文件的详细信息展示</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %} 