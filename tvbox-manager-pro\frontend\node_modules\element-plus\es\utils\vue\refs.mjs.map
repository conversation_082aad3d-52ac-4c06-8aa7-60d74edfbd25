{"version": 3, "file": "refs.mjs", "sources": ["../../../../../packages/utils/vue/refs.ts"], "sourcesContent": ["import { isFunction } from '../types'\n\nimport type { ComponentPublicInstance, Ref } from 'vue'\n\nexport type RefSetter = (\n  el: Element | ComponentPublicInstance | undefined\n) => void\n\nexport const composeRefs = (\n  ...refs: (Ref<HTMLElement | undefined> | RefSetter)[]\n) => {\n  return (el: Element | ComponentPublicInstance | null) => {\n    refs.forEach((ref) => {\n      if (isFunction(ref)) {\n        ref(el as Element | ComponentPublicInstance)\n      } else {\n        ref.value = el as HTMLElement | undefined\n      }\n    })\n  }\n}\n"], "names": [], "mappings": ";;AACY,MAAC,WAAW,GAAG,CAAC,GAAG,IAAI,KAAK;AACxC,EAAE,OAAO,CAAC,EAAE,KAAK;AACjB,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AAC1B,MAAM,IAAI,UAAU,CAAC,GAAG,CAAC,EAAE;AAC3B,QAAQ,GAAG,CAAC,EAAE,CAAC,CAAC;AAChB,OAAO,MAAM;AACb,QAAQ,GAAG,CAAC,KAAK,GAAG,EAAE,CAAC;AACvB,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ;;;;"}