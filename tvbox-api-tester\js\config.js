// 配置管理
class ConfigManager {
    constructor() {
        this.storageKey = 'tvbox-api-tester-config';
        this.defaultConfig = {
            // 服务器配置
            apiBaseUrl: 'http://localhost:8001',
            timeout: 30000,
            userEmail: '<EMAIL>',
            userPassword: 'admin123',
            
            // 测试配置
            concurrency: 1,
            delay: 1000,
            retries: 1,
            
            // 界面配置
            theme: 'light',
            language: 'zh-CN',
            autoSave: true,
            showTimestamps: true,
            
            // 日志配置
            logLevel: 'info',
            maxLogEntries: 1000,
            
            // 导出配置
            exportFormat: 'json',
            includeHeaders: true,
            includeRequestBody: true,
            includeResponseBody: true,
            
            // 认证配置
            defaultCredentials: {
                email: '<EMAIL>',
                password: 'admin123'
            },
            
            // 测试选项
            testCategories: {
                auth: true,
                interfaces: true,
                subscriptions: true,
                users: false,
                system: true,
                decrypt: true
            }
        };
        
        this.config = this.loadConfig();
    }
    
    // 加载配置
    loadConfig() {
        try {
            const saved = localStorage.getItem(this.storageKey);
            if (saved) {
                const parsed = JSON.parse(saved);
                return { ...this.defaultConfig, ...parsed };
            }
        } catch (e) {
            console.warn('加载配置失败:', e);
        }
        
        return { ...this.defaultConfig };
    }
    
    // 保存配置
    saveConfig() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(this.config));
            return true;
        } catch (e) {
            console.error('保存配置失败:', e);
            return false;
        }
    }
    
    // 获取配置值
    get(key, defaultValue = null) {
        const keys = key.split('.');
        let value = this.config;
        
        for (const k of keys) {
            if (value && typeof value === 'object' && k in value) {
                value = value[k];
            } else {
                return defaultValue;
            }
        }
        
        return value;
    }
    
    // 设置配置值
    set(key, value) {
        const keys = key.split('.');
        let target = this.config;
        
        for (let i = 0; i < keys.length - 1; i++) {
            const k = keys[i];
            if (!(k in target) || typeof target[k] !== 'object') {
                target[k] = {};
            }
            target = target[k];
        }
        
        target[keys[keys.length - 1]] = value;
        
        if (this.config.autoSave) {
            this.saveConfig();
        }
    }
    
    // 重置配置
    reset() {
        this.config = { ...this.defaultConfig };
        this.saveConfig();
    }
    
    // 导出配置
    export() {
        return JSON.stringify(this.config, null, 2);
    }
    
    // 导入配置
    import(configJson) {
        try {
            const imported = JSON.parse(configJson);
            this.config = { ...this.defaultConfig, ...imported };
            this.saveConfig();
            return true;
        } catch (e) {
            console.error('导入配置失败:', e);
            return false;
        }
    }
    
    // 获取服务器配置
    getServerConfig() {
        return {
            apiBaseUrl: this.get('apiBaseUrl'),
            timeout: this.get('timeout'),
            userEmail: this.get('userEmail'),
            userPassword: this.get('userPassword')
        };
    }

    // 设置服务器配置
    setServerConfig(config) {
        if (config.apiBaseUrl) this.set('apiBaseUrl', config.apiBaseUrl);
        if (config.timeout) this.set('timeout', config.timeout);
        if (config.userEmail !== undefined) this.set('userEmail', config.userEmail);
        if (config.userPassword !== undefined) this.set('userPassword', config.userPassword);
    }
    
    // 获取测试配置
    getTestConfig() {
        return {
            concurrency: this.get('concurrency'),
            delay: this.get('delay'),
            retries: this.get('retries'),
            testCategories: this.get('testCategories')
        };
    }
    
    // 设置测试配置
    setTestConfig(config) {
        if (config.concurrency) this.set('concurrency', config.concurrency);
        if (config.delay) this.set('delay', config.delay);
        if (config.retries) this.set('retries', config.retries);
        if (config.testCategories) this.set('testCategories', config.testCategories);
    }
    
    // 获取认证配置
    getAuthConfig() {
        return this.get('defaultCredentials');
    }
    
    // 设置认证配置
    setAuthConfig(credentials) {
        this.set('defaultCredentials', credentials);
    }
    
    // 验证配置
    validate() {
        const errors = [];
        
        // 验证API地址
        if (!this.get('apiBaseUrl')) {
            errors.push('API地址不能为空');
        } else if (!Utils.isValidUrl(this.get('apiBaseUrl'))) {
            errors.push('API地址格式不正确');
        }
        
        // 验证超时时间
        const timeout = this.get('timeout');
        if (!timeout || timeout < 1000 || timeout > 300000) {
            errors.push('超时时间必须在1-300秒之间');
        }
        
        // 验证并发数
        const concurrency = this.get('concurrency');
        if (!concurrency || concurrency < 1 || concurrency > 10) {
            errors.push('并发数必须在1-10之间');
        }
        
        // 验证延迟时间
        const delay = this.get('delay');
        if (delay < 0 || delay > 10000) {
            errors.push('延迟时间必须在0-10000毫秒之间');
        }
        
        return errors;
    }
    
    // 应用配置到界面
    applyToUI() {
        // 服务器配置
        const apiBaseUrlInput = document.getElementById('apiBaseUrl');
        if (apiBaseUrlInput) {
            apiBaseUrlInput.value = this.get('apiBaseUrl');
        }
        
        const timeoutInput = document.getElementById('timeout');
        if (timeoutInput) {
            timeoutInput.value = this.get('timeout') / 1000;
        }
        
        const userEmailInput = document.getElementById('userEmail');
        if (userEmailInput) {
            userEmailInput.value = this.get('userEmail');
        }

        const userPasswordInput = document.getElementById('userPassword');
        if (userPasswordInput) {
            userPasswordInput.value = this.get('userPassword');
        }
        
        // 测试配置
        const concurrencyInput = document.getElementById('concurrency');
        if (concurrencyInput) {
            concurrencyInput.value = this.get('concurrency');
        }
        
        const delayInput = document.getElementById('delay');
        if (delayInput) {
            delayInput.value = this.get('delay');
        }
        
        const retriesInput = document.getElementById('retries');
        if (retriesInput) {
            retriesInput.value = this.get('retries');
        }
        
        // 测试分类选择
        const testCategories = this.get('testCategories');
        for (const [category, enabled] of Object.entries(testCategories)) {
            const checkbox = document.getElementById(`test${category.charAt(0).toUpperCase() + category.slice(1)}`);
            if (checkbox) {
                checkbox.checked = enabled;
            }
        }
    }
    
    // 从界面收集配置
    collectFromUI() {
        // 服务器配置
        const apiBaseUrlInput = document.getElementById('apiBaseUrl');
        if (apiBaseUrlInput) {
            this.set('apiBaseUrl', apiBaseUrlInput.value.trim());
        }
        
        const timeoutInput = document.getElementById('timeout');
        if (timeoutInput) {
            this.set('timeout', parseInt(timeoutInput.value) * 1000);
        }
        
        const userEmailInput = document.getElementById('userEmail');
        if (userEmailInput) {
            this.set('userEmail', userEmailInput.value.trim());
        }

        const userPasswordInput = document.getElementById('userPassword');
        if (userPasswordInput) {
            this.set('userPassword', userPasswordInput.value.trim());
        }
        
        // 测试配置
        const concurrencyInput = document.getElementById('concurrency');
        if (concurrencyInput) {
            this.set('concurrency', parseInt(concurrencyInput.value));
        }
        
        const delayInput = document.getElementById('delay');
        if (delayInput) {
            this.set('delay', parseInt(delayInput.value));
        }
        
        const retriesInput = document.getElementById('retries');
        if (retriesInput) {
            this.set('retries', parseInt(retriesInput.value));
        }
        
        // 测试分类选择
        const testCategories = {};
        const categories = ['auth', 'interfaces', 'subscriptions', 'users', 'system', 'decrypt'];
        
        for (const category of categories) {
            const checkbox = document.getElementById(`test${category.charAt(0).toUpperCase() + category.slice(1)}`);
            if (checkbox) {
                testCategories[category] = checkbox.checked;
            }
        }
        
        this.set('testCategories', testCategories);
    }
    
    // 创建配置备份
    createBackup() {
        const backup = {
            timestamp: new Date().toISOString(),
            config: this.config,
            version: '1.0.0'
        };
        
        return JSON.stringify(backup, null, 2);
    }
    
    // 恢复配置备份
    restoreBackup(backupJson) {
        try {
            const backup = JSON.parse(backupJson);
            if (backup.config) {
                this.config = { ...this.defaultConfig, ...backup.config };
                this.saveConfig();
                return true;
            }
            return false;
        } catch (e) {
            console.error('恢复配置备份失败:', e);
            return false;
        }
    }
}

// 全局配置管理器实例
window.configManager = new ConfigManager();

// 配置相关的全局函数
function saveConfig() {
    if (window.configManager) {
        window.configManager.collectFromUI();
        const errors = window.configManager.validate();
        
        if (errors.length > 0) {
            app.showToast(`配置验证失败: ${errors[0]}`, 'error');
            return false;
        }
        
        if (window.configManager.saveConfig()) {
            app.showToast('配置已保存', 'success');
            return true;
        } else {
            app.showToast('配置保存失败', 'error');
            return false;
        }
    }
    return false;
}

function resetConfig() {
    if (confirm('确定要重置所有配置到默认值吗？')) {
        if (window.configManager) {
            window.configManager.reset();
            window.configManager.applyToUI();
            app.showToast('配置已重置', 'info');
        }
    }
}

function exportConfig() {
    if (window.configManager) {
        const configJson = window.configManager.export();
        Utils.downloadFile(configJson, 'tvbox-api-tester-config.json', 'application/json');
        app.showToast('配置已导出', 'success');
    }
}

function importConfig() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    if (window.configManager && window.configManager.import(e.target.result)) {
                        window.configManager.applyToUI();
                        app.showToast('配置导入成功', 'success');
                    } else {
                        app.showToast('配置导入失败', 'error');
                    }
                } catch (err) {
                    app.showToast('配置文件格式错误', 'error');
                }
            };
            reader.readAsText(file);
        }
    };
    
    input.click();
}
