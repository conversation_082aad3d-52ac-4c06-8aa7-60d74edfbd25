/*!
* Tabler v1.0.0-beta20 (https://tabler.io)
* @version 1.0.0-beta20
* @link https://tabler.io
* Copyright 2018-2023 The Tabler Authors
* Copyright 2018-2023 codecalm.net Pa<PERSON>ł <PERSON>
* Licensed under MIT (https://github.com/tabler/tabler/blob/master/LICENSE)
*/
:root{--tblr-blue:#206bc4;--tblr-azure:#45aaf2;--tblr-indigo:#6574cd;--tblr-purple:#a55eea;--tblr-pink:#f66d9b;--tblr-red:#fa4654;--tblr-orange:#fd9644;--tblr-yellow:#f1c40f;--tblr-lime:#7bd235;--tblr-green:#5eba00;--tblr-teal:#2bcbba;--tblr-cyan:#17a2b8;--tblr-primary:#206bc4;--tblr-secondary:#656d77;--tblr-success:#2fb344;--tblr-info:#4299e1;--tblr-warning:#f76707;--tblr-danger:#d63939;--tblr-light:#f8fafc;--tblr-dark:#232e3c;--tblr-muted:#656d77;--tblr-body-color:#1e293b;--tblr-body-color-rgb:30,41,59;--tblr-body-bg:#f6f8fb;--tblr-body-bg-rgb:246,248,251;--tblr-card-bg:#fff;--tblr-card-bg-rgb:255,255,255;--tblr-highlight-bg:rgba(32,107,196,.03);--tblr-border-color:rgba(98,105,118,.16);--tblr-border-light-color:rgba(98,105,118,.08);--tblr-border-color-dark:rgba(98,105,118,.28);--tblr-border-color-active:#206bc4;--tblr-breakpoint-xs:0;--tblr-breakpoint-sm:576px;--tblr-breakpoint-md:768px;--tblr-breakpoint-lg:992px;--tblr-breakpoint-xl:1200px;--tblr-breakpoint-xxl:1400px;--tblr-primary-rgb:32,107,196;--tblr-secondary-rgb:101,109,119;--tblr-success-rgb:47,179,68;--tblr-info-rgb:66,153,225;--tblr-warning-rgb:247,103,7;--tblr-danger-rgb:214,57,57;--tblr-light-rgb:248,250,252;--tblr-dark-rgb:35,46,60;--tblr-font-sans-serif:system-ui,-apple-system,"Segoe UI",Roboto,"Helvetica Neue","Noto Sans","Liberation Sans",Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";--tblr-font-monospace:SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;--tblr-font-size-base:0.875rem;--tblr-font-size-xs:0.75rem;--tblr-font-size-sm:0.8125rem;--tblr-font-size-lg:1rem;--tblr-font-weight-light:300;--tblr-font-weight-normal:400;--tblr-font-weight-medium:500;--tblr-font-weight-bold:600;--tblr-font-weight-headings:600;--tblr-line-height-base:1.4285714;--tblr-line-height-sm:1.25;--tblr-line-height-lg:1.5714285714;--tblr-body-font-size:var(--tblr-font-size-base);--tblr-body-font-weight:var(--tblr-font-weight-normal);--tblr-body-line-height:var(--tblr-line-height-base);--tblr-text-muted:var(--tblr-secondary);--tblr-border-width:1px;--tblr-border-radius:4px;--tblr-border-radius-sm:2px;--tblr-border-radius-lg:8px;--tblr-border-radius-pill:100rem;--tblr-svg-icon-size:1rem;--tblr-component-active-color:#fff;--tblr-component-active-bg:var(--tblr-primary);--tblr-link-hover-decoration:underline;--tblr-link-color:var(--tblr-primary);--tblr-link-color-rgb:var(--tblr-primary-rgb);--tblr-link-decoration:none;--tblr-link-hover-color:#195099;--tblr-link-hover-color-rgb:25,80,153}@media (prefers-reduced-motion:no-preference){:root{--tblr-general-transition-time:300ms}:root{--tblr-animation-fade:300ms;--tblr-transition-base:color 0.15s ease-in-out,background-color 0.15s ease-in-out,border-color 0.15s ease-in-out,box-shadow 0.15s ease-in-out;--tblr-transition-fade:opacity 0.15s linear;--tblr-transition-collapse:height 0.35s ease}}@media (min-width:576px){:root{--tblr-content-padding:1.5rem}}:root{--tblr-theme-blue:#0054a6;--tblr-theme-blue-rgb:0,84,166;--tblr-theme-blue-fg:#ffffff;--tblr-theme-azure:#45aaf2;--tblr-theme-azure-rgb:69,170,242;--tblr-theme-azure-fg:#ffffff;--tblr-theme-indigo:#6574cd;--tblr-theme-indigo-rgb:101,116,205;--tblr-theme-indigo-fg:#ffffff;--tblr-theme-purple:#a855f7;--tblr-theme-purple-rgb:168,85,247;--tblr-theme-purple-fg:#ffffff;--tblr-theme-pink:#f66d9b;--tblr-theme-pink-rgb:246,109,155;--tblr-theme-pink-fg:#ffffff;--tblr-theme-red:#e53e3e;--tblr-theme-red-rgb:229,62,62;--tblr-theme-red-fg:#ffffff;--tblr-theme-orange:#fd9644;--tblr-theme-orange-rgb:253,150,68;--tblr-theme-orange-fg:#ffffff;--tblr-theme-yellow:#f59f00;--tblr-theme-yellow-rgb:245,159,0;--tblr-theme-yellow-fg:#ffffff;--tblr-theme-lime:#82c91e;--tblr-theme-lime-rgb:130,201,30;--tblr-theme-lime-fg:#ffffff;--tblr-theme-green:#5eba00;--tblr-theme-green-rgb:94,186,0;--tblr-theme-green-fg:#ffffff;--tblr-theme-teal:#2bcbba;--tblr-theme-teal-rgb:43,203,186;--tblr-theme-teal-fg:#ffffff;--tblr-theme-cyan:#17a2b8;--tblr-theme-cyan-rgb:23,162,184;--tblr-theme-cyan-fg:#ffffff;--tblr-theme-gray:#475569;--tblr-theme-gray-rgb:71,85,105;--tblr-theme-gray-fg:#ffffff;--tblr-theme-gray-dark:#1e293b;--tblr-theme-gray-dark-rgb:30,41,59;--tblr-theme-gray-dark-fg:#ffffff;--tblr-footer-bg:#212f4d;--tblr-footer-color:#cbd5e1}:root{--tblr-navbar-height:3.5rem;--tblr-offcanvas-width:20rem;--tblr-content-min-height:calc(100vh - var(--tblr-navbar-height) - var(--tblr-footer-height))}.dark-mode{--tblr-body-color:var(--tblr-light);--tblr-body-bg:#242a38;--tblr-border-color:rgba(98,105,118,.16);--tblr-card-bg:#242a38}.theme-dark{--tblr-body-color:var(--tblr-light);--tblr-body-bg:#232e3c;--tblr-card-bg:#1b2434;--tblr-border-color:rgba(98,105,118,.16);--tblr-footer-bg:#1b2434}.theme-dark.theme-azure{--tblr-body-bg:#081525;--tblr-card-bg:#060f1b;--tblr-border-color:#0c213a;--tblr-footer-bg:#060f1b}.theme-dark.theme-blue{--tblr-body-bg:#0c1628;--tblr-card-bg:#080f1c;--tblr-border-color:#142e66;--tblr-footer-bg:#080f1c}.theme-dark.theme-indigo{--tblr-body-bg:#101028;--tblr-card-bg:#08081c;--tblr-border-color:#202066;--tblr-footer-bg:#08081c}.theme-dark.theme-purple{--tblr-body-bg:#161729;--tblr-card-bg:#0c0d1c;--tblr-border-color:#362e66;--tblr-footer-bg:#0c0d1c}.theme-dark.theme-pink{--tblr-body-bg:#201225;--tblr-card-bg:#14091a;--tblr-border-color:#4e1c4d;--tblr-footer-bg:#14091a}.theme-dark.theme-red{--tblr-body-bg:#251215;--tblr-card-bg:#160b0d;--tblr-border-color:#66272d;--tblr-footer-bg:#160b0d}.theme-dark.theme-orange{--tblr-body-bg:#2b140f;--tblr-card-bg:#190d0a;--tblr-border-color:#663918;--tblr-footer-bg:#190d0a}.theme-dark.theme-yellow{--tblr-body-bg:#2d200c;--tblr-card-bg:#1a1408;--tblr-border-color:#665317;--tblr-footer-bg:#1a1408}.theme-dark.theme-lime{--tblr-body-bg:#1d270e;--tblr-card-bg:#121909;--tblr-border-color:#425e1a;--tblr-footer-bg:#121909}.theme-dark.theme-green{--tblr-body-bg:#132113;--tblr-card-bg:#0c160c;--tblr-border-color:#24572a;--tblr-footer-bg:#0c160c}.theme-dark.theme-teal{--tblr-body-bg:#112428;--tblr-card-bg:#0a161a;--tblr-border-color:#1e5c66;--tblr-footer-bg:#0a161a}.theme-dark.theme-cyan{--tblr-body-bg:#112429;--tblr-card-bg:#0a161a;--tblr-border-color:#1e5966;--tblr-footer-bg:#0a161a}.footer{--tblr-footer-bg:var(--tblr-card-bg);--tblr-footer-border-width:1px;--tblr-footer-height:3.5rem;background:var(--tblr-footer-bg);border-top:var(--tblr-footer-border-width) solid var(--tblr-border-color);color:var(--tblr-footer-color);display:flex;flex-direction:column;padding:1.5rem 0 1rem}.navbar{--tblr-navbar-padding-y:0.75rem;--tblr-navbar-padding-x:1rem;--tblr-navbar-nav-link-padding-x:0.75rem;--tblr-navbar-nav-link-padding-y:0.5rem;--tblr-navbar-brand-padding-y:0.75rem;--tblr-navbar-toggler-padding-y:0.25rem;--tblr-navbar-toggler-padding-x:0.75rem;--tblr-navbar-toggler-transition:box-shadow 0.15s ease-in-out;--tblr-navbar-toggler-border-radius:2px;--tblr-navbar-dark-color:rgba(255, 255, 255, 0.7);--tblr-navbar-dark-hover-color:#ffffff;--tblr-navbar-dark-active-color:#ffffff;--tblr-navbar-dark-disabled-color:rgba(255, 255, 255, 0.25);--tblr-navbar-dark-toggler-border-color:rgba(255, 255, 255, 0.1);--tblr-navbar-light-color:var(--tblr-body-color);--tblr-navbar-light-hover-color:var(--tblr-body-color);--tblr-navbar-light-active-color:var(--tblr-body-color);--tblr-navbar-light-disabled-color:rgba(var(--tblr-body-color-rgb), 0.3);--tblr-navbar-light-toggler-border-color:rgba(var(--tblr-body-color-rgb), 0.1);display:flex;flex-wrap:wrap;align-items:center;justify-content:space-between;padding:var(--tblr-navbar-padding-y) var(--tblr-navbar-padding-x);transition:background-color var(--tblr-general-transition-time);border-width:0;background-color:var(--tblr-card-bg);color:var(--tblr-muted);border-bottom:var(--tblr-border-width) solid var(--tblr-border-color);position:relative;height:var(--tblr-navbar-height);z-index:100;display:flex;align-items:center;min-height:var(--tblr-navbar-height);justify-content:flex-start;padding:0 .75rem;min-height:var(--tblr-navbar-height)}.navbar-dark{--tblr-navbar-color:rgba(255, 255, 255, 0.7);--tblr-navbar-hover-color:#ffffff;--tblr-navbar-disabled-color:rgba(255, 255, 255, 0.25);--tblr-navbar-active-color:#ffffff;--tblr-navbar-brand-color:#ffffff;--tblr-navbar-brand-hover-color:#ffffff;--tblr-navbar-toggler-border-color:rgba(255, 255, 255, 0.1);background-color:#232e3c;color:#fff}.navbar-brand{padding-top:var(--tblr-navbar-brand-padding-y);padding-bottom:var(--tblr-navbar-brand-padding-y);margin-right:0;white-space:nowrap;display:inline-flex;align-items:center;gap:.5rem;font-weight:var(--tblr-font-weight-bold);font-size:1rem;min-height:var(--tblr-navbar-height)}.nav{--tblr-nav-link-padding-x:0.75rem;--tblr-nav-link-padding-y:0.75rem;--tblr-nav-link-font-weight:;--tblr-nav-link-color:inherit;--tblr-nav-link-hover-color:var(--tblr-primary);--tblr-nav-link-disabled-color:var(--tblr-secondary);display:flex;flex-wrap:wrap;padding-left:0;margin-bottom:0;list-style:none;gap:var(--tblr-gap-1)}.nav-vertical{flex-direction:column}.nav-vertical .nav-item{margin-left:0}.nav-vertical .nav-link.active{font-weight:var(--tblr-font-weight-medium)}.nav-vertical.nav-pills{margin:0 -1rem}.nav-link{display:flex;align-items:center;padding:var(--tblr-nav-link-padding-y) var(--tblr-nav-link-padding-x);font-weight:var(--tblr-nav-link-font-weight);color:var(--tblr-nav-link-color);cursor:pointer}.nav-link:focus,.nav-link:hover{color:var(--tblr-nav-link-hover-color)}.nav-link:focus{z-index:3}.nav-link.disabled{color:var(--tblr-nav-link-disabled-color);pointer-events:none;cursor:default}.nav-link .icon{width:1.25rem;height:1.25rem;margin-right:.5rem;color:var(--tblr-text-muted);stroke-width:1;fill:var(--tblr-link-color);margin-top:-0.25rem;margin-bottom:-0.25rem}.nav-link .badge{margin-left:auto}.nav-pills{--tblr-nav-pills-border-radius:var(--tblr-border-radius);--tblr-nav-pills-link-active-color:var(--tblr-primary);--tblr-nav-pills-link-active-bg:var(--tblr-component-active-bg)}.nav-pills .nav-link{border-radius:var(--tblr-nav-pills-border-radius)}.nav-pills .nav-link.active{color:var(--tblr-component-active-color);background-color:var(--tblr-component-active-bg)}.nav-pills .nav-item.show .nav-link{color:var(--tblr-component-active-color);background-color:var(--tblr-component-active-bg)}.nav-tabs{--tblr-nav-tabs-border-width:1px;--tblr-nav-tabs-border-color:var(--tblr-border-color);--tblr-nav-tabs-border-radius:var(--tblr-border-radius);--tblr-nav-tabs-link-hover-border-color:var(--tblr-card-bg) var(--tblr-card-bg) var(--tblr-border-color);--tblr-nav-tabs-link-active-color:var(--tblr-body-color);--tblr-nav-tabs-link-active-bg:var(--tblr-card-bg);--tblr-nav-tabs-link-active-border-color:var(--tblr-border-color) var(--tblr-border-color) var(--tblr-card-bg);border-bottom:var(--tblr-nav-tabs-border-width) solid var(--tblr-nav-tabs-border-color);position:relative}.nav-tabs .nav-item{position:relative}.nav-tabs .nav-item:hover{cursor:pointer}.nav-tabs .nav-link{border:var(--tblr-nav-tabs-border-width) solid transparent;border-top-left-radius:var(--tblr-nav-tabs-border-radius);border-top-right-radius:var(--tblr-nav-tabs-border-radius);color:inherit;transition:color .3s,background .3s,border .3s}.nav-tabs .nav-link:focus,.nav-tabs .nav-link:hover{isolation:isolate;border-color:var(--tblr-nav-tabs-link-hover-border-color)}.nav-tabs .nav-link.disabled{color:var(--tblr-nav-link-disabled-color);background-color:transparent;border-color:transparent}.nav-tabs .nav-item.show .nav-link,.nav-tabs .nav-link.active{color:var(--tblr-nav-tabs-link-active-color);background-color:var(--tblr-nav-tabs-link-active-bg);border-color:var(--tblr-nav-tabs-link-active-border-color)}.nav-tabs.nav-tabs-alt{background:0 0;margin:-.75rem -1.25rem 0;padding:.75rem 1.25rem 0;border-bottom-width:1px;flex-shrink:0}@media (min-width:768px){.nav-tabs .nav-link{display:flex}}.card{--tblr-card-spacer-y:1.25rem;--tblr-card-spacer-x:1.25rem;--tblr-card-title-spacer-y:1.25rem;--tblr-card-border-width:var(--tblr-border-width);--tblr-card-border-color:var(--tblr-border-color);--tblr-card-border-radius:var(--tblr-border-radius);--tblr-card-box-shadow:rgba(35, 46, 60, .04) 0 2px 4px 0;--tblr-card-inner-border-radius:calc(4px - 1px);--tblr-card-cap-padding-y:0.75rem;--tblr-card-cap-padding-x:1.25rem;--tblr-card-cap-bg:transparent;--tblr-card-bg:var(--tblr-card-bg);--tblr-card-img-overlay-padding:1.25rem;--tblr-card-group-margin:1.5rem;position:relative;display:flex;flex-direction:column;min-width:0;height:var(--tblr-card-height,auto);word-wrap:break-word;background-color:var(--tblr-card-bg);background-clip:border-box;border:var(--tblr-card-border-width) solid var(--tblr-card-border-color);border-radius:var(--tblr-card-border-radius);box-shadow:var(--tblr-card-box-shadow)}.card>hr{margin-right:0;margin-left:0}.card>.list-group{border-top:inherit;border-bottom:inherit}.card>.list-group:first-child{border-top-width:0;border-top-left-radius:var(--tblr-card-inner-border-radius);border-top-right-radius:var(--tblr-card-inner-border-radius)}.card>.list-group:last-child{border-bottom-width:0;border-bottom-right-radius:var(--tblr-card-inner-border-radius);border-bottom-left-radius:var(--tblr-card-inner-border-radius)}.card>.card-header+.list-group,.card>.list-group+.card-footer{border-top:0}.card-body{flex:1 1 auto;padding:var(--tblr-card-spacer-y) var(--tblr-card-spacer-x);color:inherit}.card-title{margin-bottom:var(--tblr-card-title-spacer-y);font-size:.875rem;line-height:1.25rem;font-weight:var(--tblr-font-weight-medium)}.card-subtitle{margin-top:calc(-.5 * var(--tblr-card-title-spacer-y));margin-bottom:0;color:var(--tblr-text-muted)}.card-text:last-child{margin-bottom:0}.card-link+.card-link{margin-left:var(--tblr-card-spacer-x)}.card-header{padding:var(--tblr-card-cap-padding-y) var(--tblr-card-cap-padding-x);margin-bottom:0;color:inherit;background-color:var(--tblr-card-cap-bg);border-bottom:var(--tblr-card-border-width) solid var(--tblr-card-border-color)}.card-header:first-child{border-radius:var(--tblr-card-inner-border-radius) var(--tblr-card-inner-border-radius) 0 0}.card-footer{padding:var(--tblr-card-cap-padding-y) var(--tblr-card-cap-padding-x);color:inherit;background-color:var(--tblr-card-cap-bg);border-top:var(--tblr-card-border-width) solid var(--tblr-card-border-color)}.card-footer:last-child{border-radius:0 0 var(--tblr-card-inner-border-radius) var(--tblr-card-inner-border-radius)}.card-header-tabs{margin-right:calc(-.5 * var(--tblr-card-cap-padding-x));margin-bottom:calc(-1 * var(--tblr-card-cap-padding-y));margin-left:calc(-.5 * var(--tblr-card-cap-padding-x));border-bottom:0}.card-header-tabs .nav-link{padding-right:calc(var(--tblr-card-cap-padding-x) * 0.5);padding-left:calc(var(--tblr-card-cap-padding-x) * 0.5)}.card-header-tabs .nav-link.active{background-color:var(--tblr-card-bg);border-bottom-color:var(--tblr-card-bg)}.card-img-overlay{position:absolute;top:0;right:0;bottom:0;left:0;padding:var(--tblr-card-img-overlay-padding);border-radius:var(--tblr-card-inner-border-radius)}.card-img,.card-img-bottom,.card-img-top{width:100%}.card-img,.card-img-top{border-top-left-radius:var(--tblr-card-inner-border-radius);border-top-right-radius:var(--tblr-card-inner-border-radius)}.card-img,.card-img-bottom{border-bottom-right-radius:var(--tblr-card-inner-border-radius);border-bottom-left-radius:var(--tblr-card-inner-border-radius)}.card-group>.card{margin-bottom:var(--tblr-card-group-margin)}@media (min-width:576px){.card-group{display:flex;flex-flow:row wrap}.card-group>.card{flex:1 0 0%;margin-bottom:0}.card-group>.card+.card{margin-left:0;border-left:0}.card-group>.card:not(:last-child){border-top-right-radius:0;border-bottom-right-radius:0}.card-group>.card:not(:last-child) .card-header,.card-group>.card:not(:last-child) .card-img-top{border-top-right-radius:0}.card-group>.card:not(:last-child) .card-footer,.card-group>.card:not(:last-child) .card-img-bottom{border-bottom-right-radius:0}.card-group>.card:not(:first-child){border-top-left-radius:0;border-bottom-left-radius:0}.card-group>.card:not(:first-child) .card-header,.card-group>.card:not(:first-child) .card-img-top{border-top-left-radius:0}.card-group>.card:not(:first-child) .card-footer,.card-group>.card:not(:first-child) .card-img-bottom{border-bottom-left-radius:0}}} 
* Tabler v1.0.0-beta20 (https://tabler.io)
* @version 1.0.0-beta20
* @link https://tabler.io
* Copyright 2018-2023 The Tabler Authors
* Copyright 2018-2023 codecalm.net Paweł Kuna
* Licensed under MIT (https://github.com/tabler/tabler/blob/master/LICENSE)
*/
:root{--tblr-blue:#206bc4;--tblr-azure:#45aaf2;--tblr-indigo:#6574cd;--tblr-purple:#a55eea;--tblr-pink:#f66d9b;--tblr-red:#fa4654;--tblr-orange:#fd9644;--tblr-yellow:#f1c40f;--tblr-lime:#7bd235;--tblr-green:#5eba00;--tblr-teal:#2bcbba;--tblr-cyan:#17a2b8;--tblr-primary:#206bc4;--tblr-secondary:#656d77;--tblr-success:#2fb344;--tblr-info:#4299e1;--tblr-warning:#f76707;--tblr-danger:#d63939;--tblr-light:#f8fafc;--tblr-dark:#232e3c;--tblr-muted:#656d77;--tblr-body-color:#1e293b;--tblr-body-color-rgb:30,41,59;--tblr-body-bg:#f6f8fb;--tblr-body-bg-rgb:246,248,251;--tblr-card-bg:#fff;--tblr-card-bg-rgb:255,255,255;--tblr-highlight-bg:rgba(32,107,196,.03);--tblr-border-color:rgba(98,105,118,.16);--tblr-border-light-color:rgba(98,105,118,.08);--tblr-border-color-dark:rgba(98,105,118,.28);--tblr-border-color-active:#206bc4;--tblr-breakpoint-xs:0;--tblr-breakpoint-sm:576px;--tblr-breakpoint-md:768px;--tblr-breakpoint-lg:992px;--tblr-breakpoint-xl:1200px;--tblr-breakpoint-xxl:1400px;--tblr-primary-rgb:32,107,196;--tblr-secondary-rgb:101,109,119;--tblr-success-rgb:47,179,68;--tblr-info-rgb:66,153,225;--tblr-warning-rgb:247,103,7;--tblr-danger-rgb:214,57,57;--tblr-light-rgb:248,250,252;--tblr-dark-rgb:35,46,60;--tblr-font-sans-serif:system-ui,-apple-system,"Segoe UI",Roboto,"Helvetica Neue","Noto Sans","Liberation Sans",Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";--tblr-font-monospace:SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;--tblr-font-size-base:0.875rem;--tblr-font-size-xs:0.75rem;--tblr-font-size-sm:0.8125rem;--tblr-font-size-lg:1rem;--tblr-font-weight-light:300;--tblr-font-weight-normal:400;--tblr-font-weight-medium:500;--tblr-font-weight-bold:600;--tblr-font-weight-headings:600;--tblr-line-height-base:1.4285714;--tblr-line-height-sm:1.25;--tblr-line-height-lg:1.5714285714;--tblr-body-font-size:var(--tblr-font-size-base);--tblr-body-font-weight:var(--tblr-font-weight-normal);--tblr-body-line-height:var(--tblr-line-height-base);--tblr-text-muted:var(--tblr-secondary);--tblr-border-width:1px;--tblr-border-radius:4px;--tblr-border-radius-sm:2px;--tblr-border-radius-lg:8px;--tblr-border-radius-pill:100rem;--tblr-svg-icon-size:1rem;--tblr-component-active-color:#fff;--tblr-component-active-bg:var(--tblr-primary);--tblr-link-hover-decoration:underline;--tblr-link-color:var(--tblr-primary);--tblr-link-color-rgb:var(--tblr-primary-rgb);--tblr-link-decoration:none;--tblr-link-hover-color:#195099;--tblr-link-hover-color-rgb:25,80,153}@media (prefers-reduced-motion:no-preference){:root{--tblr-general-transition-time:300ms}:root{--tblr-animation-fade:300ms;--tblr-transition-base:color 0.15s ease-in-out,background-color 0.15s ease-in-out,border-color 0.15s ease-in-out,box-shadow 0.15s ease-in-out;--tblr-transition-fade:opacity 0.15s linear;--tblr-transition-collapse:height 0.35s ease}}@media (min-width:576px){:root{--tblr-content-padding:1.5rem}}:root{--tblr-theme-blue:#0054a6;--tblr-theme-blue-rgb:0,84,166;--tblr-theme-blue-fg:#ffffff;--tblr-theme-azure:#45aaf2;--tblr-theme-azure-rgb:69,170,242;--tblr-theme-azure-fg:#ffffff;--tblr-theme-indigo:#6574cd;--tblr-theme-indigo-rgb:101,116,205;--tblr-theme-indigo-fg:#ffffff;--tblr-theme-purple:#a855f7;--tblr-theme-purple-rgb:168,85,247;--tblr-theme-purple-fg:#ffffff;--tblr-theme-pink:#f66d9b;--tblr-theme-pink-rgb:246,109,155;--tblr-theme-pink-fg:#ffffff;--tblr-theme-red:#e53e3e;--tblr-theme-red-rgb:229,62,62;--tblr-theme-red-fg:#ffffff;--tblr-theme-orange:#fd9644;--tblr-theme-orange-rgb:253,150,68;--tblr-theme-orange-fg:#ffffff;--tblr-theme-yellow:#f59f00;--tblr-theme-yellow-rgb:245,159,0;--tblr-theme-yellow-fg:#ffffff;--tblr-theme-lime:#82c91e;--tblr-theme-lime-rgb:130,201,30;--tblr-theme-lime-fg:#ffffff;--tblr-theme-green:#5eba00;--tblr-theme-green-rgb:94,186,0;--tblr-theme-green-fg:#ffffff;--tblr-theme-teal:#2bcbba;--tblr-theme-teal-rgb:43,203,186;--tblr-theme-teal-fg:#ffffff;--tblr-theme-cyan:#17a2b8;--tblr-theme-cyan-rgb:23,162,184;--tblr-theme-cyan-fg:#ffffff;--tblr-theme-gray:#475569;--tblr-theme-gray-rgb:71,85,105;--tblr-theme-gray-fg:#ffffff;--tblr-theme-gray-dark:#1e293b;--tblr-theme-gray-dark-rgb:30,41,59;--tblr-theme-gray-dark-fg:#ffffff;--tblr-footer-bg:#212f4d;--tblr-footer-color:#cbd5e1}:root{--tblr-navbar-height:3.5rem;--tblr-offcanvas-width:20rem;--tblr-content-min-height:calc(100vh - var(--tblr-navbar-height) - var(--tblr-footer-height))}.dark-mode{--tblr-body-color:var(--tblr-light);--tblr-body-bg:#242a38;--tblr-border-color:rgba(98,105,118,.16);--tblr-card-bg:#242a38}.theme-dark{--tblr-body-color:var(--tblr-light);--tblr-body-bg:#232e3c;--tblr-card-bg:#1b2434;--tblr-border-color:rgba(98,105,118,.16);--tblr-footer-bg:#1b2434}.theme-dark.theme-azure{--tblr-body-bg:#081525;--tblr-card-bg:#060f1b;--tblr-border-color:#0c213a;--tblr-footer-bg:#060f1b}.theme-dark.theme-blue{--tblr-body-bg:#0c1628;--tblr-card-bg:#080f1c;--tblr-border-color:#142e66;--tblr-footer-bg:#080f1c}.theme-dark.theme-indigo{--tblr-body-bg:#101028;--tblr-card-bg:#08081c;--tblr-border-color:#202066;--tblr-footer-bg:#08081c}.theme-dark.theme-purple{--tblr-body-bg:#161729;--tblr-card-bg:#0c0d1c;--tblr-border-color:#362e66;--tblr-footer-bg:#0c0d1c}.theme-dark.theme-pink{--tblr-body-bg:#201225;--tblr-card-bg:#14091a;--tblr-border-color:#4e1c4d;--tblr-footer-bg:#14091a}.theme-dark.theme-red{--tblr-body-bg:#251215;--tblr-card-bg:#160b0d;--tblr-border-color:#66272d;--tblr-footer-bg:#160b0d}.theme-dark.theme-orange{--tblr-body-bg:#2b140f;--tblr-card-bg:#190d0a;--tblr-border-color:#663918;--tblr-footer-bg:#190d0a}.theme-dark.theme-yellow{--tblr-body-bg:#2d200c;--tblr-card-bg:#1a1408;--tblr-border-color:#665317;--tblr-footer-bg:#1a1408}.theme-dark.theme-lime{--tblr-body-bg:#1d270e;--tblr-card-bg:#121909;--tblr-border-color:#425e1a;--tblr-footer-bg:#121909}.theme-dark.theme-green{--tblr-body-bg:#132113;--tblr-card-bg:#0c160c;--tblr-border-color:#24572a;--tblr-footer-bg:#0c160c}.theme-dark.theme-teal{--tblr-body-bg:#112428;--tblr-card-bg:#0a161a;--tblr-border-color:#1e5c66;--tblr-footer-bg:#0a161a}.theme-dark.theme-cyan{--tblr-body-bg:#112429;--tblr-card-bg:#0a161a;--tblr-border-color:#1e5966;--tblr-footer-bg:#0a161a}.footer{--tblr-footer-bg:var(--tblr-card-bg);--tblr-footer-border-width:1px;--tblr-footer-height:3.5rem;background:var(--tblr-footer-bg);border-top:var(--tblr-footer-border-width) solid var(--tblr-border-color);color:var(--tblr-footer-color);display:flex;flex-direction:column;padding:1.5rem 0 1rem}.navbar{--tblr-navbar-padding-y:0.75rem;--tblr-navbar-padding-x:1rem;--tblr-navbar-nav-link-padding-x:0.75rem;--tblr-navbar-nav-link-padding-y:0.5rem;--tblr-navbar-brand-padding-y:0.75rem;--tblr-navbar-toggler-padding-y:0.25rem;--tblr-navbar-toggler-padding-x:0.75rem;--tblr-navbar-toggler-transition:box-shadow 0.15s ease-in-out;--tblr-navbar-toggler-border-radius:2px;--tblr-navbar-dark-color:rgba(255, 255, 255, 0.7);--tblr-navbar-dark-hover-color:#ffffff;--tblr-navbar-dark-active-color:#ffffff;--tblr-navbar-dark-disabled-color:rgba(255, 255, 255, 0.25);--tblr-navbar-dark-toggler-border-color:rgba(255, 255, 255, 0.1);--tblr-navbar-light-color:var(--tblr-body-color);--tblr-navbar-light-hover-color:var(--tblr-body-color);--tblr-navbar-light-active-color:var(--tblr-body-color);--tblr-navbar-light-disabled-color:rgba(var(--tblr-body-color-rgb), 0.3);--tblr-navbar-light-toggler-border-color:rgba(var(--tblr-body-color-rgb), 0.1);display:flex;flex-wrap:wrap;align-items:center;justify-content:space-between;padding:var(--tblr-navbar-padding-y) var(--tblr-navbar-padding-x);transition:background-color var(--tblr-general-transition-time);border-width:0;background-color:var(--tblr-card-bg);color:var(--tblr-muted);border-bottom:var(--tblr-border-width) solid var(--tblr-border-color);position:relative;height:var(--tblr-navbar-height);z-index:100;display:flex;align-items:center;min-height:var(--tblr-navbar-height);justify-content:flex-start;padding:0 .75rem;min-height:var(--tblr-navbar-height)}.navbar-dark{--tblr-navbar-color:rgba(255, 255, 255, 0.7);--tblr-navbar-hover-color:#ffffff;--tblr-navbar-disabled-color:rgba(255, 255, 255, 0.25);--tblr-navbar-active-color:#ffffff;--tblr-navbar-brand-color:#ffffff;--tblr-navbar-brand-hover-color:#ffffff;--tblr-navbar-toggler-border-color:rgba(255, 255, 255, 0.1);background-color:#232e3c;color:#fff}.navbar-brand{padding-top:var(--tblr-navbar-brand-padding-y);padding-bottom:var(--tblr-navbar-brand-padding-y);margin-right:0;white-space:nowrap;display:inline-flex;align-items:center;gap:.5rem;font-weight:var(--tblr-font-weight-bold);font-size:1rem;min-height:var(--tblr-navbar-height)}.nav{--tblr-nav-link-padding-x:0.75rem;--tblr-nav-link-padding-y:0.75rem;--tblr-nav-link-font-weight:;--tblr-nav-link-color:inherit;--tblr-nav-link-hover-color:var(--tblr-primary);--tblr-nav-link-disabled-color:var(--tblr-secondary);display:flex;flex-wrap:wrap;padding-left:0;margin-bottom:0;list-style:none;gap:var(--tblr-gap-1)}.nav-vertical{flex-direction:column}.nav-vertical .nav-item{margin-left:0}.nav-vertical .nav-link.active{font-weight:var(--tblr-font-weight-medium)}.nav-vertical.nav-pills{margin:0 -1rem}.nav-link{display:flex;align-items:center;padding:var(--tblr-nav-link-padding-y) var(--tblr-nav-link-padding-x);font-weight:var(--tblr-nav-link-font-weight);color:var(--tblr-nav-link-color);cursor:pointer}.nav-link:focus,.nav-link:hover{color:var(--tblr-nav-link-hover-color)}.nav-link:focus{z-index:3}.nav-link.disabled{color:var(--tblr-nav-link-disabled-color);pointer-events:none;cursor:default}.nav-link .icon{width:1.25rem;height:1.25rem;margin-right:.5rem;color:var(--tblr-text-muted);stroke-width:1;fill:var(--tblr-link-color);margin-top:-0.25rem;margin-bottom:-0.25rem}.nav-link .badge{margin-left:auto}.nav-pills{--tblr-nav-pills-border-radius:var(--tblr-border-radius);--tblr-nav-pills-link-active-color:var(--tblr-primary);--tblr-nav-pills-link-active-bg:var(--tblr-component-active-bg)}.nav-pills .nav-link{border-radius:var(--tblr-nav-pills-border-radius)}.nav-pills .nav-link.active{color:var(--tblr-component-active-color);background-color:var(--tblr-component-active-bg)}.nav-pills .nav-item.show .nav-link{color:var(--tblr-component-active-color);background-color:var(--tblr-component-active-bg)}.nav-tabs{--tblr-nav-tabs-border-width:1px;--tblr-nav-tabs-border-color:var(--tblr-border-color);--tblr-nav-tabs-border-radius:var(--tblr-border-radius);--tblr-nav-tabs-link-hover-border-color:var(--tblr-card-bg) var(--tblr-card-bg) var(--tblr-border-color);--tblr-nav-tabs-link-active-color:var(--tblr-body-color);--tblr-nav-tabs-link-active-bg:var(--tblr-card-bg);--tblr-nav-tabs-link-active-border-color:var(--tblr-border-color) var(--tblr-border-color) var(--tblr-card-bg);border-bottom:var(--tblr-nav-tabs-border-width) solid var(--tblr-nav-tabs-border-color);position:relative}.nav-tabs .nav-item{position:relative}.nav-tabs .nav-item:hover{cursor:pointer}.nav-tabs .nav-link{border:var(--tblr-nav-tabs-border-width) solid transparent;border-top-left-radius:var(--tblr-nav-tabs-border-radius);border-top-right-radius:var(--tblr-nav-tabs-border-radius);color:inherit;transition:color .3s,background .3s,border .3s}.nav-tabs .nav-link:focus,.nav-tabs .nav-link:hover{isolation:isolate;border-color:var(--tblr-nav-tabs-link-hover-border-color)}.nav-tabs .nav-link.disabled{color:var(--tblr-nav-link-disabled-color);background-color:transparent;border-color:transparent}.nav-tabs .nav-item.show .nav-link,.nav-tabs .nav-link.active{color:var(--tblr-nav-tabs-link-active-color);background-color:var(--tblr-nav-tabs-link-active-bg);border-color:var(--tblr-nav-tabs-link-active-border-color)}.nav-tabs.nav-tabs-alt{background:0 0;margin:-.75rem -1.25rem 0;padding:.75rem 1.25rem 0;border-bottom-width:1px;flex-shrink:0}@media (min-width:768px){.nav-tabs .nav-link{display:flex}}.card{--tblr-card-spacer-y:1.25rem;--tblr-card-spacer-x:1.25rem;--tblr-card-title-spacer-y:1.25rem;--tblr-card-border-width:var(--tblr-border-width);--tblr-card-border-color:var(--tblr-border-color);--tblr-card-border-radius:var(--tblr-border-radius);--tblr-card-box-shadow:rgba(35, 46, 60, .04) 0 2px 4px 0;--tblr-card-inner-border-radius:calc(4px - 1px);--tblr-card-cap-padding-y:0.75rem;--tblr-card-cap-padding-x:1.25rem;--tblr-card-cap-bg:transparent;--tblr-card-bg:var(--tblr-card-bg);--tblr-card-img-overlay-padding:1.25rem;--tblr-card-group-margin:1.5rem;position:relative;display:flex;flex-direction:column;min-width:0;height:var(--tblr-card-height,auto);word-wrap:break-word;background-color:var(--tblr-card-bg);background-clip:border-box;border:var(--tblr-card-border-width) solid var(--tblr-card-border-color);border-radius:var(--tblr-card-border-radius);box-shadow:var(--tblr-card-box-shadow)}.card>hr{margin-right:0;margin-left:0}.card>.list-group{border-top:inherit;border-bottom:inherit}.card>.list-group:first-child{border-top-width:0;border-top-left-radius:var(--tblr-card-inner-border-radius);border-top-right-radius:var(--tblr-card-inner-border-radius)}.card>.list-group:last-child{border-bottom-width:0;border-bottom-right-radius:var(--tblr-card-inner-border-radius);border-bottom-left-radius:var(--tblr-card-inner-border-radius)}.card>.card-header+.list-group,.card>.list-group+.card-footer{border-top:0}.card-body{flex:1 1 auto;padding:var(--tblr-card-spacer-y) var(--tblr-card-spacer-x);color:inherit}.card-title{margin-bottom:var(--tblr-card-title-spacer-y);font-size:.875rem;line-height:1.25rem;font-weight:var(--tblr-font-weight-medium)}.card-subtitle{margin-top:calc(-.5 * var(--tblr-card-title-spacer-y));margin-bottom:0;color:var(--tblr-text-muted)}.card-text:last-child{margin-bottom:0}.card-link+.card-link{margin-left:var(--tblr-card-spacer-x)}.card-header{padding:var(--tblr-card-cap-padding-y) var(--tblr-card-cap-padding-x);margin-bottom:0;color:inherit;background-color:var(--tblr-card-cap-bg);border-bottom:var(--tblr-card-border-width) solid var(--tblr-card-border-color)}.card-header:first-child{border-radius:var(--tblr-card-inner-border-radius) var(--tblr-card-inner-border-radius) 0 0}.card-footer{padding:var(--tblr-card-cap-padding-y) var(--tblr-card-cap-padding-x);color:inherit;background-color:var(--tblr-card-cap-bg);border-top:var(--tblr-card-border-width) solid var(--tblr-card-border-color)}.card-footer:last-child{border-radius:0 0 var(--tblr-card-inner-border-radius) var(--tblr-card-inner-border-radius)}.card-header-tabs{margin-right:calc(-.5 * var(--tblr-card-cap-padding-x));margin-bottom:calc(-1 * var(--tblr-card-cap-padding-y));margin-left:calc(-.5 * var(--tblr-card-cap-padding-x));border-bottom:0}.card-header-tabs .nav-link{padding-right:calc(var(--tblr-card-cap-padding-x) * 0.5);padding-left:calc(var(--tblr-card-cap-padding-x) * 0.5)}.card-header-tabs .nav-link.active{background-color:var(--tblr-card-bg);border-bottom-color:var(--tblr-card-bg)}.card-img-overlay{position:absolute;top:0;right:0;bottom:0;left:0;padding:var(--tblr-card-img-overlay-padding);border-radius:var(--tblr-card-inner-border-radius)}.card-img,.card-img-bottom,.card-img-top{width:100%}.card-img,.card-img-top{border-top-left-radius:var(--tblr-card-inner-border-radius);border-top-right-radius:var(--tblr-card-inner-border-radius)}.card-img,.card-img-bottom{border-bottom-right-radius:var(--tblr-card-inner-border-radius);border-bottom-left-radius:var(--tblr-card-inner-border-radius)}.card-group>.card{margin-bottom:var(--tblr-card-group-margin)}@media (min-width:576px){.card-group{display:flex;flex-flow:row wrap}.card-group>.card{flex:1 0 0%;margin-bottom:0}.card-group>.card+.card{margin-left:0;border-left:0}.card-group>.card:not(:last-child){border-top-right-radius:0;border-bottom-right-radius:0}.card-group>.card:not(:last-child) .card-header,.card-group>.card:not(:last-child) .card-img-top{border-top-right-radius:0}.card-group>.card:not(:last-child) .card-footer,.card-group>.card:not(:last-child) .card-img-bottom{border-bottom-right-radius:0}.card-group>.card:not(:first-child){border-top-left-radius:0;border-bottom-left-radius:0}.card-group>.card:not(:first-child) .card-header,.card-group>.card:not(:first-child) .card-img-top{border-top-left-radius:0}.card-group>.card:not(:first-child) .card-footer,.card-group>.card:not(:first-child) .card-img-bottom{border-bottom-left-radius:0}}} 