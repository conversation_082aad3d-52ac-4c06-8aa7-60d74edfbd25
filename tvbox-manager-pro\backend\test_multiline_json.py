#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import re

def test_multiline_json_parsing():
    """测试多行JSON解析"""
    
    # 读取原始文件
    with open('raw_config_output.txt', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f'原始内容长度: {len(content)}')
    
    # 方法1: 使用json5库（如果可用）
    try:
        import json5
        print("尝试使用json5库...")
        data = json5.loads(content)
        print("✅ json5解析成功!")
        print(f"站点数: {len(data.get('sites', []))}")
        return True, data
    except ImportError:
        print("json5库不可用，尝试其他方法")
    except Exception as e:
        print(f"json5解析失败: {e}")
    
    # 方法2: 使用hjson库（如果可用）
    try:
        import hjson
        print("尝试使用hjson库...")
        data = hjson.loads(content)
        print("✅ hjson解析成功!")
        print(f"站点数: {len(data.get('sites', []))}")
        return True, data
    except ImportError:
        print("hjson库不可用，尝试其他方法")
    except Exception as e:
        print(f"hjson解析失败: {e}")
    
    # 方法3: 自定义多行JSON处理
    print("尝试自定义多行JSON处理...")
    try:
        # 移除注释
        from app.services.tvbox_decryptor import TVBoxDecryptor
        decryptor = TVBoxDecryptor()
        cleaned_content = decryptor._smart_remove_comments(content)
        
        # 尝试使用Python的json模块的宽松特性
        # Python的json模块实际上可以处理一些多行情况
        data = json.loads(cleaned_content)
        print("✅ 自定义处理成功!")
        print(f"站点数: {len(data.get('sites', []))}")
        return True, data
        
    except json.JSONDecodeError as e:
        print(f"自定义处理失败: {e}")
        print(f"错误位置: 行{e.lineno} 列{e.colno}")
        
        # 显示错误位置的内容
        lines = cleaned_content.split('\n')
        if e.lineno <= len(lines):
            error_line = lines[e.lineno - 1]
            print(f"错误行内容: {repr(error_line)}")
            
            if e.colno <= len(error_line):
                error_char = error_line[e.colno - 1]
                print(f"错误字符: {repr(error_char)} (Unicode: U+{ord(error_char):04X})")
        
        return False, None
    
    except Exception as e:
        print(f"处理失败: {e}")
        return False, None

if __name__ == "__main__":
    success, data = test_multiline_json_parsing()
    if success:
        print("\n🎉 多行JSON解析成功!")
        
        # 保存解析结果
        with open('multiline_parsed_result.json', 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print("解析结果已保存到 multiline_parsed_result.json")
    else:
        print("\n❌ 多行JSON解析失败")
        print("这个配置文件可能需要更复杂的处理方法")
