{% extends "tabler/base.html" %}

{% set active_nav = 'live' %}
{% block title %}直播管理 - TVBox Manager{% endblock %}
{% block page_title %}直播管理{% endblock %}

{% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <h2 class="page-title">
          直播管理
        </h2>
        <div class="text-muted mt-1">管理TVBox的直播源和频道</div>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <a href="{{ url_for('live.add') }}" class="btn btn-primary d-none d-sm-inline-block">
            <i class="ti ti-plus"></i>
            添加直播源
          </a>
          <a href="{{ url_for('live.add') }}" class="btn btn-primary d-sm-none btn-icon">
            <i class="ti ti-plus"></i>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    {% if lives %}
    <!-- 直播源列表卡片 -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">直播源列表</h3>
        <div class="card-actions">
          <a href="#" class="btn btn-outline-secondary btn-sm">
            <i class="ti ti-refresh"></i>
            刷新
          </a>
        </div>
      </div>
      <div class="card-body border-bottom py-3">
        <div class="d-flex">
          <div class="text-muted">
            共 <strong>{{ lives|length }}</strong> 个直播源
          </div>
          <div class="ms-auto text-muted">
            搜索:
            <div class="ms-2 d-inline-block">
              <input type="text" class="form-control form-control-sm" id="search-input" aria-label="搜索直播源">
            </div>
          </div>
        </div>
      </div>
      <div class="table-responsive">
        <table class="table card-table table-vcenter text-nowrap datatable">
          <thead>
            <tr>
              <th>名称</th>
              <th>类型</th>
              <th>URL</th>
              <th>频道数</th>
              <th>状态</th>
              <th class="w-1"></th>
            </tr>
          </thead>
          <tbody>
            {% for live in lives %}
            <tr>
              <td>
                <span class="fw-bold">{{ live.name }}</span>
              </td>
              <td>{{ live.type }}</td>
              <td>
                <div class="text-truncate" style="max-width: 200px;" title="{{ live.url }}">
                  {{ live.url }}
                </div>
              </td>
              <td>
                <span class="badge bg-primary-lt">{{ live.channels_count or '未知' }}</span>
              </td>
              <td>
                {% if live.status %}
                <span class="badge bg-success-lt">
                  <i class="ti ti-check me-1"></i> 正常
                </span>
                {% else %}
                <span class="badge bg-secondary-lt">
                  <i class="ti ti-x me-1"></i> 停用
                </span>
                {% endif %}
              </td>
              <td>
                <div class="btn-list flex-nowrap">
                  <a href="{{ url_for('live.edit', id=live.id) }}" class="btn btn-icon btn-sm" title="编辑">
                    <i class="ti ti-edit text-primary"></i>
                  </a>
                  <a href="{{ url_for('live.refresh', id=live.id) }}" class="btn btn-icon btn-sm" title="刷新">
                    <i class="ti ti-refresh text-info"></i>
                  </a>
                  <a href="{{ url_for('live.delete', id=live.id) }}" class="btn btn-icon btn-sm" 
                     onclick="return confirm('确认删除此直播源?')" title="删除">
                    <i class="ti ti-trash text-danger"></i>
                  </a>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
    {% else %}
    <!-- 空状态卡片 -->
    <div class="card">
      <div class="card-body empty">
        <div class="empty-img">
          <i class="ti ti-device-tv" style="font-size: 3rem; opacity: 0.5;"></i>
        </div>
        <p class="empty-title">没有可用直播源</p>
        <p class="empty-subtitle text-muted">
          您还没有添加任何直播源。直播源用于提供电视直播功能，添加后可观看各种电视频道。
        </p>
        <div class="empty-action">
          <a href="{{ url_for('live.add') }}" class="btn btn-primary">
            <i class="ti ti-plus me-2"></i>
            添加第一个直播源
          </a>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- 直播源类型说明 -->
    <div class="row mt-3">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">直播源类型说明</h3>
          </div>
          <div class="card-body">
            <div class="row row-cards">
              <div class="col-md-4">
                <div class="card">
                  <div class="card-status-top bg-primary"></div>
                  <div class="card-body">
                    <div class="row align-items-center mb-3">
                      <div class="col-auto">
                        <div class="avatar bg-primary-lt">
                          <i class="ti ti-broadcast"></i>
                        </div>
                      </div>
                      <div class="col">
                        <h3 class="card-title mb-0">M3U</h3>
                      </div>
                    </div>
                    <div class="text-muted">
                      最常见的直播源格式，包含频道列表和播放地址，可导入多个频道。
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="col-md-4">
                <div class="card">
                  <div class="card-status-top bg-info"></div>
                  <div class="card-body">
                    <div class="row align-items-center mb-3">
                      <div class="col-auto">
                        <div class="avatar bg-info-lt">
                          <i class="ti ti-code"></i>
                        </div>
                      </div>
                      <div class="col">
                        <h3 class="card-title mb-0">JSON</h3>
                      </div>
                    </div>
                    <div class="text-muted">
                      以JSON格式存储的频道列表，支持分组和更多元数据，灵活性更高。
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="col-md-4">
                <div class="card">
                  <div class="card-status-top bg-warning"></div>
                  <div class="card-body">
                    <div class="row align-items-center mb-3">
                      <div class="col-auto">
                        <div class="avatar bg-warning-lt">
                          <i class="ti ti-file-text"></i>
                        </div>
                      </div>
                      <div class="col">
                        <h3 class="card-title mb-0">TXT</h3>
                      </div>
                    </div>
                    <div class="text-muted">
                      简单的文本格式直播源，每行包含一个频道名称和播放地址，适合简单使用。
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 使用说明卡片 -->
    <div class="card mt-3">
      <div class="card-header">
        <h3 class="card-title">直播功能使用说明</h3>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-8">
            <div class="alert alert-info">
              <div class="d-flex">
                <div>
                  <i class="ti ti-info-circle me-2"></i>
                </div>
                <div>
                  <h4>TVBox直播使用提示</h4>
                  <div class="text-muted">
                    <p>直播源添加后会自动解析频道列表，您可以在TVBox应用中直接使用。要在TVBox中使用直播功能，请在设置中选择"默认直播源"或在配置文件中指定直播源。</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="col-md-4">
            <div class="card">
              <div class="card-body">
                <h3 class="card-title">直播源统计</h3>
                <div class="mt-3">
                  <div class="progress mb-2">
                    <div class="progress-bar bg-primary" style="width: {{ (lives|selectattr('type', 'equalto', 'M3U')|list|length / (lives|length or 1)) * 100 }}%" role="progressbar" aria-label="M3U">
                      M3U格式
                    </div>
                  </div>
                  <div class="progress mb-2">
                    <div class="progress-bar bg-info" style="width: {{ (lives|selectattr('type', 'equalto', 'JSON')|list|length / (lives|length or 1)) * 100 }}%" role="progressbar" aria-label="JSON">
                      JSON格式
                    </div>
                  </div>
                  <div class="progress">
                    <div class="progress-bar bg-warning" style="width: {{ (lives|selectattr('type', 'equalto', 'TXT')|list|length / (lives|length or 1)) * 100 }}%" role="progressbar" aria-label="TXT">
                      TXT格式
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
  const searchInput = document.getElementById('search-input');
  if(searchInput) {
    searchInput.addEventListener('keyup', function() {
      const searchValue = this.value.toLowerCase();
      const tableRows = document.querySelectorAll('.datatable tbody tr');
      
      tableRows.forEach(function(row) {
        const name = row.querySelector('td:first-child').textContent.toLowerCase();
        const url = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
        
        if(name.includes(searchValue) || url.includes(searchValue)) {
          row.style.display = '';
        } else {
          row.style.display = 'none';
        }
      });
    });
  }
});
</script>
{% endblock %} 

{% set active_nav = 'live' %}
{% block title %}直播管理 - TVBox Manager{% endblock %}
{% block page_title %}直播管理{% endblock %}

{% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <h2 class="page-title">
          直播管理
        </h2>
        <div class="text-muted mt-1">管理TVBox的直播源和频道</div>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <a href="{{ url_for('live.add') }}" class="btn btn-primary d-none d-sm-inline-block">
            <i class="ti ti-plus"></i>
            添加直播源
          </a>
          <a href="{{ url_for('live.add') }}" class="btn btn-primary d-sm-none btn-icon">
            <i class="ti ti-plus"></i>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    {% if lives %}
    <!-- 直播源列表卡片 -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">直播源列表</h3>
        <div class="card-actions">
          <a href="#" class="btn btn-outline-secondary btn-sm">
            <i class="ti ti-refresh"></i>
            刷新
          </a>
        </div>
      </div>
      <div class="card-body border-bottom py-3">
        <div class="d-flex">
          <div class="text-muted">
            共 <strong>{{ lives|length }}</strong> 个直播源
          </div>
          <div class="ms-auto text-muted">
            搜索:
            <div class="ms-2 d-inline-block">
              <input type="text" class="form-control form-control-sm" id="search-input" aria-label="搜索直播源">
            </div>
          </div>
        </div>
      </div>
      <div class="table-responsive">
        <table class="table card-table table-vcenter text-nowrap datatable">
          <thead>
            <tr>
              <th>名称</th>
              <th>类型</th>
              <th>URL</th>
              <th>频道数</th>
              <th>状态</th>
              <th class="w-1"></th>
            </tr>
          </thead>
          <tbody>
            {% for live in lives %}
            <tr>
              <td>
                <span class="fw-bold">{{ live.name }}</span>
              </td>
              <td>{{ live.type }}</td>
              <td>
                <div class="text-truncate" style="max-width: 200px;" title="{{ live.url }}">
                  {{ live.url }}
                </div>
              </td>
              <td>
                <span class="badge bg-primary-lt">{{ live.channels_count or '未知' }}</span>
              </td>
              <td>
                {% if live.status %}
                <span class="badge bg-success-lt">
                  <i class="ti ti-check me-1"></i> 正常
                </span>
                {% else %}
                <span class="badge bg-secondary-lt">
                  <i class="ti ti-x me-1"></i> 停用
                </span>
                {% endif %}
              </td>
              <td>
                <div class="btn-list flex-nowrap">
                  <a href="{{ url_for('live.edit', id=live.id) }}" class="btn btn-icon btn-sm" title="编辑">
                    <i class="ti ti-edit text-primary"></i>
                  </a>
                  <a href="{{ url_for('live.refresh', id=live.id) }}" class="btn btn-icon btn-sm" title="刷新">
                    <i class="ti ti-refresh text-info"></i>
                  </a>
                  <a href="{{ url_for('live.delete', id=live.id) }}" class="btn btn-icon btn-sm" 
                     onclick="return confirm('确认删除此直播源?')" title="删除">
                    <i class="ti ti-trash text-danger"></i>
                  </a>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
    {% else %}
    <!-- 空状态卡片 -->
    <div class="card">
      <div class="card-body empty">
        <div class="empty-img">
          <i class="ti ti-device-tv" style="font-size: 3rem; opacity: 0.5;"></i>
        </div>
        <p class="empty-title">没有可用直播源</p>
        <p class="empty-subtitle text-muted">
          您还没有添加任何直播源。直播源用于提供电视直播功能，添加后可观看各种电视频道。
        </p>
        <div class="empty-action">
          <a href="{{ url_for('live.add') }}" class="btn btn-primary">
            <i class="ti ti-plus me-2"></i>
            添加第一个直播源
          </a>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- 直播源类型说明 -->
    <div class="row mt-3">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">直播源类型说明</h3>
          </div>
          <div class="card-body">
            <div class="row row-cards">
              <div class="col-md-4">
                <div class="card">
                  <div class="card-status-top bg-primary"></div>
                  <div class="card-body">
                    <div class="row align-items-center mb-3">
                      <div class="col-auto">
                        <div class="avatar bg-primary-lt">
                          <i class="ti ti-broadcast"></i>
                        </div>
                      </div>
                      <div class="col">
                        <h3 class="card-title mb-0">M3U</h3>
                      </div>
                    </div>
                    <div class="text-muted">
                      最常见的直播源格式，包含频道列表和播放地址，可导入多个频道。
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="col-md-4">
                <div class="card">
                  <div class="card-status-top bg-info"></div>
                  <div class="card-body">
                    <div class="row align-items-center mb-3">
                      <div class="col-auto">
                        <div class="avatar bg-info-lt">
                          <i class="ti ti-code"></i>
                        </div>
                      </div>
                      <div class="col">
                        <h3 class="card-title mb-0">JSON</h3>
                      </div>
                    </div>
                    <div class="text-muted">
                      以JSON格式存储的频道列表，支持分组和更多元数据，灵活性更高。
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="col-md-4">
                <div class="card">
                  <div class="card-status-top bg-warning"></div>
                  <div class="card-body">
                    <div class="row align-items-center mb-3">
                      <div class="col-auto">
                        <div class="avatar bg-warning-lt">
                          <i class="ti ti-file-text"></i>
                        </div>
                      </div>
                      <div class="col">
                        <h3 class="card-title mb-0">TXT</h3>
                      </div>
                    </div>
                    <div class="text-muted">
                      简单的文本格式直播源，每行包含一个频道名称和播放地址，适合简单使用。
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 使用说明卡片 -->
    <div class="card mt-3">
      <div class="card-header">
        <h3 class="card-title">直播功能使用说明</h3>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-8">
            <div class="alert alert-info">
              <div class="d-flex">
                <div>
                  <i class="ti ti-info-circle me-2"></i>
                </div>
                <div>
                  <h4>TVBox直播使用提示</h4>
                  <div class="text-muted">
                    <p>直播源添加后会自动解析频道列表，您可以在TVBox应用中直接使用。要在TVBox中使用直播功能，请在设置中选择"默认直播源"或在配置文件中指定直播源。</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="col-md-4">
            <div class="card">
              <div class="card-body">
                <h3 class="card-title">直播源统计</h3>
                <div class="mt-3">
                  <div class="progress mb-2">
                    <div class="progress-bar bg-primary" style="width: {{ (lives|selectattr('type', 'equalto', 'M3U')|list|length / (lives|length or 1)) * 100 }}%" role="progressbar" aria-label="M3U">
                      M3U格式
                    </div>
                  </div>
                  <div class="progress mb-2">
                    <div class="progress-bar bg-info" style="width: {{ (lives|selectattr('type', 'equalto', 'JSON')|list|length / (lives|length or 1)) * 100 }}%" role="progressbar" aria-label="JSON">
                      JSON格式
                    </div>
                  </div>
                  <div class="progress">
                    <div class="progress-bar bg-warning" style="width: {{ (lives|selectattr('type', 'equalto', 'TXT')|list|length / (lives|length or 1)) * 100 }}%" role="progressbar" aria-label="TXT">
                      TXT格式
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
  const searchInput = document.getElementById('search-input');
  if(searchInput) {
    searchInput.addEventListener('keyup', function() {
      const searchValue = this.value.toLowerCase();
      const tableRows = document.querySelectorAll('.datatable tbody tr');
      
      tableRows.forEach(function(row) {
        const name = row.querySelector('td:first-child').textContent.toLowerCase();
        const url = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
        
        if(name.includes(searchValue) || url.includes(searchValue)) {
          row.style.display = '';
        } else {
          row.style.display = 'none';
        }
      });
    });
  }
});
</script>
{% endblock %} 