{
  "panOrder": "quark|uc|115|p123|ali|yd|ty###原畫|普畫",
  "aliToken": "",
  "quarkCookie": "",
  "ucCookie": "",
  "ucToken": "",
  "ucUt": "",
  "115Cookie": "",
  "pwdRb115": "",
  //115 删除码
  "goServerUrl": "http://127.0.0.1:9966",
  "ydAuth": "",
  //请求头中authorization 取basic 后面的值
  "tyAuth": "",
  //账号|密码
  "p123Auth":"",
  //账号|密码
  "proxy": "http://127.0.0.1:1072",
  //默认代理端口 留空则使用外置代理:********************************************
  "tgPic": "true",
  //tgsou 图片显示与否
  //每次更新jar 该值都必须更新
  "exeMd5": "ubeoXWP83DY73dG7Or29czm5VwGxq/9YHQodehF30gUNWh+HJ31Kuq9YM4YsfiZenKQ/F3PelZgzcj1Sz0GjdfDT9ZjhpUTNExnB4ZHpLFGCSK2dkODnA/k4pSu6OpPFiE1sUA0QDBVqDBkGsqfPOUeKCzXjzbM7CNLHjY1WSfVQOok2xsgyJ5Yo5L5v2iJ2eALUKIoer8glYB17U73hm8yAmWxwXPZfSXS2NpYElP/LsH0tRMXzg11ZmuU8yVGk92P2iXBOVhQZqySd1DjIZudUWaf625WI2v2Jhvw6MT7pMQ7Dx6vEBcq0bM2luQhhLeEVey10K0St+HaT+aUANn+y2g2mx0woDY1vhkhNvdL3vbregqIB/e2B1Niknj56uH+f8lpLtF19GI3zvhei58ruB+pVB3ahuVAaXBoUn+NP+NOyJePDAbPPZsjYmpuxWgRRoeJ/A6MZNfbyuw=="
}
