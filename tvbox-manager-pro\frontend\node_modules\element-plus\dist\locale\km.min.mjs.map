{"version": 3, "file": "km.min.mjs", "sources": ["../../../../packages/locale/lang/km.ts"], "sourcesContent": ["export default {\n  name: 'km',\n  el: {\n    breadcrumb: {\n      label: 'ទីតាំងនៃទំព័របច្ចុប្បន្ន',\n    },\n    colorpicker: {\n      confirm: 'យល់ព្រម',\n      clear: 'លុបចោល',\n      defaultLabel: 'ឧបករណ៍ជ្រើសរើសពណ៌',\n      description:\n        '{color} ជាពណ៌បច្ចុប្បន្ន។ សូមចុច Enter ដើម្បីជ្រើសរើសពណ៌ថ្មី',\n      alphaLabel: 'ជ្រើសរើសភាពស្រអាប់',\n    },\n    datepicker: {\n      now: 'ឥឡូវនេះ',\n      today: 'ថ្ងៃនេះ',\n      cancel: 'បោះបង់',\n      clear: 'លុបចោល',\n      confirm: 'យល់ព្រម',\n      dateTablePrompt:\n        'ប្រើគ្រាប់ចុចដើម្បីរំកិល និងចុច Enter ដើម្បីជ្រើសរើសកាលបរិច្ឆេទ',\n      monthTablePrompt:\n        'ប្រើគ្រាប់ចុចដើម្បីរំកិល និងចុច Enter ដើម្បីជ្រើសរើសខែ',\n      yearTablePrompt:\n        'ប្រើគ្រាប់ចុចដើម្បីរំកិល និងចុច Enter ដើម្បីជ្រើសរើសឆ្នាំ',\n      selectedDate: 'កាលបរិច្ឆេទដែលបានជ្រើសរើស',\n      selectDate: 'ជ្រើសរើសកាលបរិច្ឆេទ',\n      selectTime: 'ជ្រើសរើសម៉ោង',\n      startDate: 'កាលបរិច្ឆេទចាប់ផ្តើម',\n      startTime: 'ម៉ោងចាប់ផ្តើម',\n      endDate: 'កាលបរិច្ឆេទបញ្ចប់',\n      endTime: 'ម៉ោងបញ្ចប់',\n      prevYear: 'ឆ្នាំមុន',\n      nextYear: 'ឆ្នាំក្រោយ',\n      prevMonth: 'ខែមុន',\n      nextMonth: 'ខែក្រោយ',\n      year: 'ឆ្នាំ',\n      month1: 'ខែមករា',\n      month2: 'ខែកុម្ភៈ',\n      month3: 'ខែមីនា',\n      month4: 'ខែមេសា',\n      month5: 'ខែឧសភា',\n      month6: 'ខែមិថុនា',\n      month7: 'ខែកក្កដា',\n      month8: 'ខែសីហា',\n      month9: 'ខែកញ្ញា',\n      month10: 'ខែតុលា',\n      month11: 'ខែវិច្ឆិកា',\n      month12: 'ខែធ្នូ',\n      // week: 'សប្តាហ៍',\n      weeks: {\n        sun: 'អាទិត្យ',\n        mon: 'ច័ន្ទ',\n        tue: 'អង្គារ',\n        wed: 'ពុធ',\n        thu: 'ព្រហស្បតិ៍',\n        fri: 'សុក្រ',\n        sat: 'សៅរ៍',\n      },\n      weeksFull: {\n        sun: 'ថ្ងៃអាទិត្យ',\n        mon: 'ថ្ងៃច័ន្ទ',\n        tue: 'ថ្ងៃអង្គារ',\n        wed: 'ថ្ងៃពុធ',\n        thu: 'ថ្ងៃព្រហស្បតិ៍',\n        fri: 'ថ្ងៃសុក្រ',\n        sat: 'ថ្ងៃសៅរ៍',\n      },\n      months: {\n        jan: 'មករា',\n        feb: 'កុម្ភៈ',\n        mar: 'មីនា',\n        apr: 'មេសា',\n        may: 'ឧសភា',\n        jun: 'មិថុនា',\n        jul: 'កក្កដា',\n        aug: 'សីហា',\n        sep: 'កញ្ញា',\n        oct: 'តុលា',\n        nov: 'វិច្ឆិកា',\n        dec: 'ធ្នូ',\n      },\n    },\n    inputNumber: {\n      decrease: 'បន្ថយតម្លៃ',\n      increase: 'បង្កើនតម្លៃ',\n    },\n    select: {\n      loading: 'កំពុងដំណើរការ',\n      noMatch: 'គ្មានទិន្នន័យដែលត្រូវគ្នា',\n      noData: 'គ្មានទិន្នន័យ',\n      placeholder: 'សូមជ្រើសរើស',\n    },\n    dropdown: {\n      toggleDropdown: 'បើកបិទផ្ទាំងជម្រើស',\n    },\n    mention: {\n      loading: 'កំពុងដំណើរការ',\n    },\n    cascader: {\n      noMatch: 'គ្មានទិន្នន័យដែលត្រូវគ្នា',\n      loading: 'កំពុងដំណើរការ',\n      placeholder: 'សូមជ្រើសរើស',\n      noData: 'គ្មានទិន្នន័យ',\n    },\n    pagination: {\n      goto: 'ទៅកាន់',\n      pagesize: '/ទំព័រ',\n      total: 'សរុប {total}',\n      pageClassifier: 'ទំព័រ',\n      page: 'ទំព័រ',\n      prev: 'មុន',\n      next: 'បន្ទាប់',\n      currentPage: 'ទំព័រទី {pager}',\n      prevPages: 'ទៅមុខ {pager} ទំព័រ',\n      nextPages: 'ថយក្រោយ {pager} ទំព័រ',\n      deprecationWarning:\n        'អ្នកបានប្រើប្រាស់របស់ដែលបានផ្អាកឈប់ប្រើ សូមចូលទៅកាន់ឯកសារផ្លូវការរបស់ el-pagination សម្រាប់ព័ត៌មានបន្ថែម',\n    },\n    dialog: {\n      close: 'បិទ',\n    },\n    drawer: {\n      close: 'បិទ',\n    },\n    messagebox: {\n      title: 'ជំនួយ',\n      confirm: 'យល់ព្រម',\n      cancel: 'បោះបង់',\n      error: 'បញ្ចូលទិន្នន័យមិនត្រឹមត្រូវ!',\n      close: 'បិទប្រអប់សារ',\n    },\n    upload: {\n      deleteTip: 'ចុច Delete ដើម្បីលុបចេញ',\n      delete: 'លុប',\n      preview: 'មើលជាមុនសិន',\n      continue: 'បន្តបញ្ជូន',\n    },\n    slider: {\n      defaultLabel: 'ស្លាយចាប់ពី {min} ដល់ {max}',\n      defaultRangeStartLabel: 'ជ្រើសរើសតម្លៃចាប់ផ្តើម',\n      defaultRangeEndLabel: 'ជ្រើសរើសតម្លៃបញ្ចប់',\n    },\n    table: {\n      emptyText: 'គ្មានទិន្នន័យ',\n      confirmFilter: 'យល់ព្រម',\n      resetFilter: 'កំណត់ឡើងវិញ',\n      clearFilter: 'ទាំងអស់',\n      sumText: 'សរុប',\n    },\n    tour: {\n      next: 'បន្ទាប់',\n      previous: 'ថយក្រោយ',\n      finish: 'បញ្ចប់ការណែនាំ',\n    },\n    tree: {\n      emptyText: 'គ្មានទិន្នន័យ',\n    },\n    transfer: {\n      noMatch: 'គ្មានទិន្នន័យដែលត្រូវគ្នា',\n      noData: 'គ្មានទិន្នន័យ',\n      titles: ['បញ្ជីទី១', 'បញ្ជីទី២'],\n      filterPlaceholder: 'សូមបញ្ចូលពាក្យគន្លឹះដើម្បីស្វែងរក',\n      noCheckedFormat: 'ចំនួនសរុប {total}',\n      hasCheckedFormat: 'ធាតុដែលបានជ្រើស {checked}/{total}',\n    },\n    image: {\n      error: 'ការទាញយកបរាជ័យ',\n    },\n    pageHeader: {\n      title: 'ត្រឡប់ក្រោយ',\n    },\n    popconfirm: {\n      confirmButtonText: 'យល់ព្រម',\n      cancelButtonText: 'បោះបង់',\n    },\n    carousel: {\n      leftArrow: 'ស្លាយមុន',\n      rightArrow: 'ស្លាយបន្ទាប់',\n      indicator: 'ប្តូរទៅស្លាយទី {index}',\n    },\n  },\n}\n"], "names": [], "mappings": ";;AAAA,SAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,kJAAkJ,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,4CAA4C,CAAC,KAAK,CAAC,sCAAsC,CAAC,YAAY,CAAC,wGAAwG,CAAC,WAAW,CAAC,0RAA0R,CAAC,UAAU,CAAC,8GAA8G,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,4CAA4C,CAAC,KAAK,CAAC,4CAA4C,CAAC,MAAM,CAAC,sCAAsC,CAAC,KAAK,CAAC,sCAAsC,CAAC,OAAO,CAAC,4CAA4C,CAAC,eAAe,CAAC,oVAAoV,CAAC,gBAAgB,CAAC,8RAA8R,CAAC,eAAe,CAAC,gTAAgT,CAAC,YAAY,CAAC,wJAAwJ,CAAC,UAAU,CAAC,oHAAoH,CAAC,UAAU,CAAC,0EAA0E,CAAC,SAAS,CAAC,0HAA0H,CAAC,SAAS,CAAC,gFAAgF,CAAC,OAAO,CAAC,wGAAwG,CAAC,OAAO,CAAC,8DAA8D,CAAC,QAAQ,CAAC,kDAAkD,CAAC,QAAQ,CAAC,8DAA8D,CAAC,SAAS,CAAC,gCAAgC,CAAC,SAAS,CAAC,4CAA4C,CAAC,IAAI,CAAC,gCAAgC,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,kDAAkD,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,kDAAkD,CAAC,MAAM,CAAC,kDAAkD,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,4CAA4C,CAAC,OAAO,CAAC,sCAAsC,CAAC,OAAO,CAAC,8DAA8D,CAAC,OAAO,CAAC,sCAAsC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,4CAA4C,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,8DAA8D,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,oEAAoE,CAAC,GAAG,CAAC,wDAAwD,CAAC,GAAG,CAAC,8DAA8D,CAAC,GAAG,CAAC,4CAA4C,CAAC,GAAG,CAAC,sFAAsF,CAAC,GAAG,CAAC,wDAAwD,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,8DAA8D,CAAC,QAAQ,CAAC,oEAAoE,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,gFAAgF,CAAC,OAAO,CAAC,wJAAwJ,CAAC,MAAM,CAAC,gFAAgF,CAAC,WAAW,CAAC,oEAAoE,CAAC,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,8GAA8G,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,gFAAgF,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,wJAAwJ,CAAC,OAAO,CAAC,gFAAgF,CAAC,WAAW,CAAC,oEAAoE,CAAC,MAAM,CAAC,gFAAgF,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,QAAQ,CAAC,iCAAiC,CAAC,KAAK,CAAC,kCAAkC,CAAC,cAAc,CAAC,gCAAgC,CAAC,IAAI,CAAC,gCAAgC,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,4CAA4C,CAAC,WAAW,CAAC,oDAAoD,CAAC,SAAS,CAAC,uEAAuE,CAAC,SAAS,CAAC,mFAAmF,CAAC,kBAAkB,CAAC,kiBAAkiB,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,gCAAgC,CAAC,OAAO,CAAC,4CAA4C,CAAC,MAAM,CAAC,sCAAsC,CAAC,KAAK,CAAC,qKAAqK,CAAC,KAAK,CAAC,0EAA0E,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,oGAAoG,CAAC,MAAM,CAAC,oBAAoB,CAAC,OAAO,CAAC,oEAAoE,CAAC,QAAQ,CAAC,8DAA8D,CAAC,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,mGAAmG,CAAC,sBAAsB,CAAC,sIAAsI,CAAC,oBAAoB,CAAC,oHAAoH,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,gFAAgF,CAAC,aAAa,CAAC,4CAA4C,CAAC,WAAW,CAAC,oEAAoE,CAAC,WAAW,CAAC,4CAA4C,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,4CAA4C,CAAC,QAAQ,CAAC,4CAA4C,CAAC,MAAM,CAAC,sFAAsF,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,gFAAgF,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,wJAAwJ,CAAC,MAAM,CAAC,gFAAgF,CAAC,MAAM,CAAC,CAAC,kDAAkD,CAAC,kDAAkD,CAAC,CAAC,iBAAiB,CAAC,wMAAwM,CAAC,eAAe,CAAC,gEAAgE,CAAC,gBAAgB,CAAC,8GAA8G,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,sFAAsF,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,oEAAoE,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,4CAA4C,CAAC,gBAAgB,CAAC,sCAAsC,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,kDAAkD,CAAC,UAAU,CAAC,0EAA0E,CAAC,SAAS,CAAC,8FAA8F,CAAC,CAAC,CAAC;;;;"}