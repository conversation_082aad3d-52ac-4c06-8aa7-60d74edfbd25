'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var index = require('./src/index.js');

const _InfiniteScroll = index["default"];
_InfiniteScroll.install = (app) => {
  app.directive("InfiniteScroll", _InfiniteScroll);
};
const ElInfiniteScroll = _InfiniteScroll;

exports.ElInfiniteScroll = ElInfiniteScroll;
exports["default"] = _InfiniteScroll;
//# sourceMappingURL=index.js.map
