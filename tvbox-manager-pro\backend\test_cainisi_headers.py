#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def test_cainisi_headers():
    """测试不同请求头访问菜妮丝接口"""
    
    import requests
    import time
    
    url = "https://tv.菜妮丝.top"
    
    # 不同的User-Agent
    user_agents = [
        # 默认
        None,
        # TVBox常用
        "okhttp/3.15",
        "okhttp/4.1.0", 
        "Dalvik/2.1.0 (Linux; U; Android 11; M2012K11AC Build/RKQ1.200826.002)",
        # 浏览器
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36"
    ]
    
    for i, ua in enumerate(user_agents):
        print(f"\n=== 测试 {i+1}: {ua or '默认'} ===")
        
        try:
            headers = {}
            if ua:
                headers['User-Agent'] = ua
            
            response = requests.get(url, headers=headers, timeout=10)
            print(f"状态码: {response.status_code}")
            print(f"内容长度: {len(response.text)}")
            print(f"Content-Type: {response.headers.get('Content-Type', '未知')}")
            
            content = response.text
            print(f"内容前200字符: {repr(content[:200])}")
            
            # 检查是否包含加密特征
            has_base64_marker = "**" in content
            starts_with_2423 = content.startswith("2423")
            is_html = content.strip().startswith("<!DOCTYPE") or content.strip().startswith("<html")
            
            print(f"是否包含Base64标记(**): {has_base64_marker}")
            print(f"是否以2423开头(AES/CBC): {starts_with_2423}")
            print(f"是否是HTML页面: {is_html}")
            
            if not is_html and (has_base64_marker or starts_with_2423 or len(content) > 10000):
                print("✅ 可能是有效的配置数据！")
            else:
                print("❌ 看起来不是配置数据")
                
        except Exception as e:
            print(f"请求失败: {e}")
        
        time.sleep(1)  # 避免请求过快
    
    # 测试一些可能的路径
    print(f"\n=== 测试可能的路径 ===")
    possible_paths = [
        "https://tv.菜妮丝.top/",
        "https://tv.菜妮丝.top/tv",
        "https://tv.菜妮丝.top/api",
        "https://tv.菜妮丝.top/config",
        "https://tv.菜妮丝.top/json",
    ]
    
    for path in possible_paths:
        print(f"\n测试路径: {path}")
        try:
            headers = {'User-Agent': 'okhttp/3.15'}
            response = requests.get(path, headers=headers, timeout=10)
            print(f"状态码: {response.status_code}")
            print(f"内容长度: {len(response.text)}")
            
            content = response.text
            is_html = content.strip().startswith("<!DOCTYPE") or content.strip().startswith("<html")
            has_json_like = content.strip().startswith("{") or "sites" in content
            
            if not is_html and has_json_like:
                print("✅ 可能找到了配置数据！")
                print(f"内容前500字符: {repr(content[:500])}")
            else:
                print("❌ 还是HTML页面或无效内容")
                
        except Exception as e:
            print(f"请求失败: {e}")
        
        time.sleep(1)

if __name__ == "__main__":
    test_cainisi_headers()
