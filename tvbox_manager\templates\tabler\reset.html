<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>重置密码 - TVBox Manager</title>
  
  <!-- Favicon -->
  <link rel="icon" href="{{ url_for('static', filename='img/favicon.ico') }}" type="image/x-icon">
  
  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@2.44.0/tabler-icons.min.css">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/tabler.min.css') }}">
  
  <style>
    body {
      background-color: #f5f7fb;
    }
    .container {
      display: flex;
      flex-direction: column;
      min-height: 100vh;
      justify-content: center;
    }
    .card {
      max-width: 400px;
      margin: 0 auto;
      border: none;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .logo {
      font-size: 24px;
      font-weight: 700;
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
    }
    .form-control-lg {
      padding: 0.75rem 1rem;
      font-size: 1rem;
    }
    .btn-primary {
      padding: 0.75rem 1rem;
      font-size: 1rem;
    }
    .alert {
      margin-bottom: 1.5rem;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="card">
      <div class="card-body p-4">
        <div class="text-center mb-4">
          <div class="logo">
            <i class="ti ti-device-tv text-primary"></i>
            <span>TVBox Manager</span>
          </div>
          <h2 class="card-title text-center mb-2">重置密码</h2>
          <p class="text-muted">请输入您的新密码</p>
        </div>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
          {% if messages %}
            {% for category, message in messages %}
              {% set alert_class = "alert-" + category if category in ['success', 'danger', 'warning', 'info'] else "alert-info" %}
              <div class="alert {{ alert_class }} alert-dismissible" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
              </div>
            {% endfor %}
          {% endif %}
        {% endwith %}
        
        <form action="{{ url_for_security('reset_password', token=reset_password_token) }}" method="POST" name="reset_password_form">
          {{ reset_password_form.hidden_tag() }}
          
          <div class="mb-3">
            {{ reset_password_form.password(class_="form-control form-control-lg", placeholder="新密码") }}
            {% if reset_password_form.password.errors %}
              <div class="text-danger">
                {% for error in reset_password_form.password.errors %}
                  <small>{{ error }}</small>
                {% endfor %}
              </div>
            {% endif %}
          </div>
          
          <div class="mb-3">
            {{ reset_password_form.password_confirm(class_="form-control form-control-lg", placeholder="确认新密码") }}
            {% if reset_password_form.password_confirm.errors %}
              <div class="text-danger">
                {% for error in reset_password_form.password_confirm.errors %}
                  <small>{{ error }}</small>
                {% endfor %}
              </div>
            {% endif %}
          </div>
          
          <div class="d-grid gap-2">
            {{ reset_password_form.submit(class_="btn btn-primary btn-lg", value="重置密码") }}
          </div>
        </form>
      </div>
    </div>
    
    <div class="text-center mt-4">
      <a href="{{ url_for('security.login') }}" class="text-muted">返回登录</a>
    </div>
  </div>
  
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>重置密码 - TVBox Manager</title>
  
  <!-- Favicon -->
  <link rel="icon" href="{{ url_for('static', filename='img/favicon.ico') }}" type="image/x-icon">
  
  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@2.44.0/tabler-icons.min.css">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/tabler.min.css') }}">
  
  <style>
    body {
      background-color: #f5f7fb;
    }
    .container {
      display: flex;
      flex-direction: column;
      min-height: 100vh;
      justify-content: center;
    }
    .card {
      max-width: 400px;
      margin: 0 auto;
      border: none;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .logo {
      font-size: 24px;
      font-weight: 700;
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
    }
    .form-control-lg {
      padding: 0.75rem 1rem;
      font-size: 1rem;
    }
    .btn-primary {
      padding: 0.75rem 1rem;
      font-size: 1rem;
    }
    .alert {
      margin-bottom: 1.5rem;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="card">
      <div class="card-body p-4">
        <div class="text-center mb-4">
          <div class="logo">
            <i class="ti ti-device-tv text-primary"></i>
            <span>TVBox Manager</span>
          </div>
          <h2 class="card-title text-center mb-2">重置密码</h2>
          <p class="text-muted">请输入您的新密码</p>
        </div>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
          {% if messages %}
            {% for category, message in messages %}
              {% set alert_class = "alert-" + category if category in ['success', 'danger', 'warning', 'info'] else "alert-info" %}
              <div class="alert {{ alert_class }} alert-dismissible" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
              </div>
            {% endfor %}
          {% endif %}
        {% endwith %}
        
        <form action="{{ url_for_security('reset_password', token=reset_password_token) }}" method="POST" name="reset_password_form">
          {{ reset_password_form.hidden_tag() }}
          
          <div class="mb-3">
            {{ reset_password_form.password(class_="form-control form-control-lg", placeholder="新密码") }}
            {% if reset_password_form.password.errors %}
              <div class="text-danger">
                {% for error in reset_password_form.password.errors %}
                  <small>{{ error }}</small>
                {% endfor %}
              </div>
            {% endif %}
          </div>
          
          <div class="mb-3">
            {{ reset_password_form.password_confirm(class_="form-control form-control-lg", placeholder="确认新密码") }}
            {% if reset_password_form.password_confirm.errors %}
              <div class="text-danger">
                {% for error in reset_password_form.password_confirm.errors %}
                  <small>{{ error }}</small>
                {% endfor %}
              </div>
            {% endif %}
          </div>
          
          <div class="d-grid gap-2">
            {{ reset_password_form.submit(class_="btn btn-primary btn-lg", value="重置密码") }}
          </div>
        </form>
      </div>
    </div>
    
    <div class="text-center mt-4">
      <a href="{{ url_for('security.login') }}" class="text-muted">返回登录</a>
    </div>
  </div>
  
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 