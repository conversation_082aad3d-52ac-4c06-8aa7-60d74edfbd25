#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
TVBox Manager 主程序入口
"""

import os
import sys
import argparse
import logging
import inspect
import importlib.util

# 修复相对导入问题
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

def configure_logging(level=logging.INFO, log_file=None):
    """配置日志系统"""
    # 格式化
    log_format = '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    
    # 创建格式化器
    formatter = logging.Formatter(fmt=log_format, datefmt=date_format)
    
    # 根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # 清除现有处理程序
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 控制台处理程序
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # 文件处理程序(如果指定)
    if log_file:
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    # 设置各模块日志级别
    logging.getLogger('werkzeug').setLevel(logging.INFO)  # Flask的WSGI日志
    logging.getLogger('urllib3').setLevel(logging.INFO)   # HTTP请求日志
    
    # 设置更详细的日志输出
    logging.getLogger('tvbox_manager').setLevel(level)
    
    return root_logger

def main():
    """主程序入口"""
    parser = argparse.ArgumentParser(description='TVBox Manager - 接口解析、解密和管理系统')
    
    # Web模式参数
    parser.add_argument('-H', '--host', default='127.0.0.1', help='Web服务主机地址')
    parser.add_argument('-p', '--port', type=int, default=5000, help='Web服务端口')
    parser.add_argument('-d', '--debug', action='store_true', help='开启调试模式')
    parser.add_argument('-l', '--log-file', help='日志文件路径')
    parser.add_argument('-v', '--verbose', action='store_true', help='显示详细日志')
    
    # 解析参数
    args = parser.parse_args()
    
    # 配置日志系统
    log_level = logging.DEBUG if args.debug or args.verbose else logging.INFO
    logger = configure_logging(level=log_level, log_file=args.log_file)
    
    # 导入应用 - 使用绝对路径导入
    factory_path = os.path.join(current_dir, "app", "factory.py")
    spec = importlib.util.spec_from_file_location("factory", factory_path)
    factory = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(factory)
    app = factory.app
    
    # 打印环境信息
    from tvbox_manager import __version__
    logger.info(f"TVBox Manager启动，版本: {__version__}")
    logger.info(f"Python版本: {sys.version}")
    logger.info(f"工作目录: {os.getcwd()}")
    
    # 启动Web服务
    logger.info(f"以Web模式启动，访问地址: http://{args.host}:{args.port}")
    
    try:
        app.run(host=args.host, port=args.port, debug=args.debug)
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序异常: {str(e)}", exc_info=True)
        sys.exit(1)

# 设置应用对象以供WSGI服务器引用
# 使用绝对路径导入
factory_path = os.path.join(current_dir, "app", "factory.py")
spec = importlib.util.spec_from_file_location("factory", factory_path)
factory = importlib.util.module_from_spec(spec)
spec.loader.exec_module(factory)
app = factory.app

if __name__ == "__main__":
    main() 