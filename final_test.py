#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试脚本：验证URL编码文件名访问功能
"""

import requests
import urllib.parse

def test_file_access():
    """测试不同类型的文件访问"""
    base_url = "http://localhost:8001/localized/interface_1_%E7%9C%9F%E5%AE%9ETVBox%E9%85%8D%E7%BD%AE"
    
    test_files = [
        # URL编码的中文文件名
        ("js/%E5%85%94%E5%B0%8F%E8%B4%9D.js", "URL编码中文文件名"),
        ("js/%E6%96%97%E9%B1%BC%E7%9B%B4%E6%92%AD.js", "URL编码中文文件名2"),
        
        # 普通英文文件名
        ("js/drpy2.js", "英文文件名"),
        
        # spider文件
        ("spider/f0720.jar", "Spider JAR文件"),
        
        # JSON配置文件
        ("json/config.json", "JSON配置文件"),
    ]
    
    print("🐾 开始测试文件访问功能...")
    print("=" * 60)
    
    success_count = 0
    total_count = len(test_files)
    
    for file_path, description in test_files:
        url = f"{base_url}/{file_path}"
        print(f"\n测试: {description}")
        print(f"URL: {url}")
        
        try:
            response = requests.get(url, timeout=10)
            status = response.status_code
            
            if status == 200:
                print(f"✅ 成功 - 状态码: {status}")
                print(f"   内容长度: {len(response.content)} 字节")
                success_count += 1
            else:
                print(f"❌ 失败 - 状态码: {status}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 测试结果: {success_count}/{total_count} 成功")
    
    if success_count == total_count:
        print("🎉 所有测试通过！URL编码文件名访问功能完美工作！")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查")
        return False

def test_encoding_conversion():
    """测试编码转换功能"""
    print("\n🔧 测试编码转换...")
    
    test_cases = [
        ("兔小贝.js", "%E5%85%94%E5%B0%8F%E8%B4%9D.js"),
        ("斗鱼直播.js", "%E6%96%97%E9%B1%BC%E7%9B%B4%E6%92%AD.js"),
        ("真实TVBox配置", "%E7%9C%9F%E5%AE%9ETVBox%E9%85%8D%E7%BD%AE"),
    ]
    
    for original, expected in test_cases:
        encoded = urllib.parse.quote(original, safe='')
        decoded = urllib.parse.unquote(expected)
        
        print(f"原文: {original}")
        print(f"编码: {encoded} {'✅' if encoded == expected else '❌'}")
        print(f"解码: {decoded} {'✅' if decoded == original else '❌'}")
        print()

if __name__ == "__main__":
    print("🐾 TVBox Manager Pro - URL编码文件名访问测试")
    print("=" * 60)
    
    # 测试编码转换
    test_encoding_conversion()
    
    # 测试文件访问
    success = test_file_access()
    
    if success:
        print("\n🎊 恭喜！所有功能测试通过！")
        print("📝 问题解决总结:")
        print("   1. ✅ URL编码文件名可以正常访问")
        print("   2. ✅ 普通中文文件名可以正常访问") 
        print("   3. ✅ 英文文件名可以正常访问")
        print("   4. ✅ 混合编码路径解析正常工作")
        print("   5. ✅ FastAPI自动URL解码问题已解决")
    else:
        print("\n❌ 部分功能需要进一步调试")
