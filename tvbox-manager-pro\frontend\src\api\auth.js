import { request } from '@/utils/request'

export const authApi = {
  // 用户登录
  login(data) {
    return request.post('/v1/auth/login', data)
  },

  // 用户注册
  register(data) {
    return request.post('/v1/auth/register', data)
  },

  // 获取当前用户信息
  getCurrentUser() {
    return request.get('/v1/auth/me')
  },

  // 刷新令牌
  refreshToken(data) {
    return request.post('/v1/auth/refresh', data)
  },

  // 用户登出
  logout() {
    return request.post('/v1/auth/logout')
  },

  // 修改密码
  changePassword(data) {
    return request.post('/v1/auth/change-password', data)
  },

  // 忘记密码
  forgotPassword(data) {
    return request.post('/v1/auth/forgot-password', data)
  },

  // 重置密码
  resetPassword(data) {
    return request.post('/v1/auth/reset-password', data)
  }
}
