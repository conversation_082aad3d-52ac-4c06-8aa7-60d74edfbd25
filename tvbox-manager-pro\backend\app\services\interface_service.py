#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
接口管理服务
"""

import logging
import json
from typing import Optional, List, Dict, Any, Tuple
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc
from fastapi import HTTPException, status

from app.models.tvbox import InterfaceSource, TvboxConfig, SiteInfo, LiveGroup, ParseInfo
from app.models.system import Subscription
from app.services.tvbox_decryptor import TVBoxDecryptor

logger = logging.getLogger(__name__)

class InterfaceService:
    """接口管理服务类"""
    
    def __init__(self):
        self.logger = logger
        self.decryptor = TVBoxDecryptor()
    
    def create_interface_source(self, db: Session, source_data: Dict[str, Any], user_id: int) -> InterfaceSource:
        """创建接口源"""
        try:
            # 检查URL是否已存在
            existing_source = db.query(InterfaceSource).filter(
                InterfaceSource.url == source_data["url"]
            ).first()
            
            if existing_source:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="接口URL已存在"
                )
            
            # 创建接口源
            source = InterfaceSource(
                name=source_data["name"],
                url=source_data["url"],
                description=source_data.get("description", ""),
                category=source_data.get("category", ""),
                tags=source_data.get("tags", ""),
                is_public=source_data.get("is_public", False),
                update_interval=source_data.get("update_interval", 3600),
                enable_localization=source_data.get("enable_localization", False)
            )
            
            db.add(source)
            db.commit()
            db.refresh(source)
            
            # 尝试解析接口
            try:
                self.parse_interface_source(db, source.id)
            except Exception as e:
                self.logger.warning(f"初始解析接口失败: {str(e)}")

            # 如果启用了本地化，则自动执行本地化
            if source_data.get("enable_localization", False):
                try:
                    from app.services.localization_service import NewLocalizationService
                    localization_service = NewLocalizationService()
                    # 异步执行本地化，不阻塞接口创建
                    import asyncio
                    asyncio.create_task(localization_service.localize_interface(db, source.id))
                    self.logger.info(f"已启动接口 {source.name} 的本地化任务")
                except Exception as e:
                    self.logger.warning(f"启动本地化任务失败: {str(e)}")

            self.logger.info(f"接口源创建成功: {source.name}")
            return source
            
        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            self.logger.error(f"创建接口源失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="创建接口源失败"
            )
    
    def parse_interface_source(self, db: Session, source_id: int, force_update: bool = False) -> Tuple[bool, str]:
        """解析接口源"""
        try:
            source = db.query(InterfaceSource).filter(InterfaceSource.id == source_id).first()
            if not source:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="接口源不存在"
                )

            # 解密接口
            try:
                content, method = self.decryptor.decrypt_config_url(source.url)
                self.logger.info(f"解密结果: method={method}, content_length={len(content) if content else 0}")
            except Exception as e:
                self.logger.error(f"解密过程异常: {str(e)}")
                return False, f"解密失败: {str(e)}"

            # 更新解密方法
            source.decrypt_method = method
            source.last_update_at = datetime.utcnow()

            # 验证配置
            validation_result = self.decryptor.validate_tvbox_config(content)
            self.logger.info(f"配置验证结果: {validation_result}")
            if not validation_result:
                source.status = "error"
                source.error_count += 1
                source.last_error_at = datetime.utcnow()
                source.last_error_message = "配置验证失败"
                # 即使验证失败，也保存配置内容供调试
                source.config_content = content
                db.commit()
                return False, "配置验证失败"

            # 解析配置内容
            config_info = self.decryptor.parse_tvbox_config_content(content)

            # 智能更新检测
            if not force_update:
                changes = self._detect_config_changes(db, source, content, config_info)
                if not changes['has_changes']:
                    self.logger.info(f"接口 {source.name} 无变化，跳过更新: {changes['reason']}")
                    # 更新最后检查时间但不更新内容
                    source.last_update_at = datetime.utcnow()
                    source.next_update_at = datetime.utcnow() + timedelta(seconds=source.update_interval)
                    db.commit()
                    return True, f"无变化，跳过更新: {changes['reason']}"
                else:
                    self.logger.info(f"接口 {source.name} 检测到变化: {changes['details']}")

            # 更新统计信息
            source.sites_count = len(config_info.get('sites', []))
            source.lives_count = sum(len(group.get('channels', [])) for group in config_info.get('lives', []))
            source.parses_count = len(config_info.get('parses', []))
            source.success_count += 1
            source.last_success_at = datetime.utcnow()
            source.status = "online"

            # 保存配置内容到接口源
            source.config_content = content

            # 计算下次更新时间
            source.next_update_at = datetime.utcnow() + timedelta(seconds=source.update_interval)

            # 创建或更新配置
            config = db.query(TvboxConfig).filter(TvboxConfig.source_id == source_id).first()

            if config:
                # 更新配置
                new_checksum = self.decryptor.generate_config_checksum(content)
                config.content = content
                config.checksum = new_checksum
                config.updated_at = datetime.utcnow()
                self._update_config_details(db, config, config_info)
                update_type = "更新"
            else:
                # 创建新配置
                config = TvboxConfig(
                    name=f"{source.name} 配置",
                    content=content,
                    source_id=source_id,
                    checksum=self.decryptor.generate_config_checksum(content)
                )
                # 先添加配置到数据库并获取ID
                db.add(config)
                db.flush()  # 获取ID但不提交事务
                # 然后更新详细信息
                self._update_config_details(db, config, config_info)
                update_type = "创建"

            db.commit()

            message = f"解析成功 - {update_type}配置"
            if not force_update and 'changes' in locals() and changes['has_changes']:
                message += f"，变化: {changes['details']}"

            self.logger.info(f"接口解析成功: {source.name} - {message}")
            return True, message

        except HTTPException:
            raise
        except Exception as e:
            # 更新错误信息
            if 'source' in locals():
                source.status = "error"
                source.error_count += 1
                source.last_error_at = datetime.utcnow()
                source.last_error_message = str(e)
                db.commit()

            self.logger.error(f"解析接口失败: {str(e)}")
            return False, str(e)

    def _detect_config_changes(self, db: Session, source: InterfaceSource, new_content: str, new_config_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        检测配置变化

        Args:
            db: 数据库会话
            source: 接口源
            new_content: 新的配置内容
            new_config_info: 新的配置信息

        Returns:
            Dict: 变化检测结果
        """
        changes = {
            'has_changes': False,
            'reason': '',
            'details': []
        }

        try:
            # 获取现有配置
            existing_config = db.query(TvboxConfig).filter(TvboxConfig.source_id == source.id).first()

            if not existing_config:
                changes['has_changes'] = True
                changes['reason'] = '首次创建配置'
                changes['details'] = ['新建配置']
                return changes

            # 检查内容checksum
            new_checksum = self.decryptor.generate_config_checksum(new_content)
            if existing_config.checksum != new_checksum:
                changes['has_changes'] = True
                changes['details'].append('配置内容变化')

            # 检查站点数量变化
            new_sites_count = len(new_config_info.get('sites', []))
            if source.sites_count != new_sites_count:
                changes['has_changes'] = True
                changes['details'].append(f'站点数量: {source.sites_count} → {new_sites_count}')

            # 检查直播源数量变化
            new_lives_count = sum(len(group.get('channels', [])) for group in new_config_info.get('lives', []))
            if source.lives_count != new_lives_count:
                changes['has_changes'] = True
                changes['details'].append(f'直播源数量: {source.lives_count} → {new_lives_count}')

            # 检查解析器数量变化
            new_parses_count = len(new_config_info.get('parses', []))
            if source.parses_count != new_parses_count:
                changes['has_changes'] = True
                changes['details'].append(f'解析器数量: {source.parses_count} → {new_parses_count}')

            # 检查关键字段变化
            new_spider = new_config_info.get('spider', '')
            if existing_config.spider != new_spider:
                changes['has_changes'] = True
                changes['details'].append('爬虫规则变化')

            new_wallpaper = new_config_info.get('wallpaper', '')
            if existing_config.wallpaper != new_wallpaper:
                changes['has_changes'] = True
                changes['details'].append('壁纸地址变化')

            new_logo = new_config_info.get('logo', '')
            if existing_config.logo != new_logo:
                changes['has_changes'] = True
                changes['details'].append('Logo地址变化')

            # 如果没有变化
            if not changes['has_changes']:
                changes['reason'] = '配置内容无变化'
            else:
                changes['reason'] = f"检测到 {len(changes['details'])} 项变化"

            return changes

        except Exception as e:
            self.logger.error(f"检测配置变化失败: {str(e)}")
            # 出错时默认认为有变化，确保更新
            changes['has_changes'] = True
            changes['reason'] = f'检测失败，强制更新: {str(e)}'
            changes['details'] = ['检测异常']
            return changes

    def _update_config_details(self, db: Session, config: TvboxConfig, config_info: Dict[str, Any]):
        """更新配置详细信息"""
        # 更新基本信息
        config.spider = config_info.get('spider', '')
        config.wallpaper = config_info.get('wallpaper', '')
        config.logo = config_info.get('logo', '')
        config.sites_count = len(config_info.get('sites', []))
        config.lives_count = sum(len(group.get('channels', [])) for group in config_info.get('lives', []))
        config.parses_count = len(config_info.get('parses', []))
        
        # 清除旧的详细信息
        db.query(SiteInfo).filter(SiteInfo.config_id == config.id).delete()
        db.query(LiveGroup).filter(LiveGroup.config_id == config.id).delete()
        db.query(ParseInfo).filter(ParseInfo.config_id == config.id).delete()
        
        # 添加站点信息
        for site_data in config_info.get('sites', []):
            site = SiteInfo(
                config_id=config.id,
                key=site_data.get('key', ''),
                name=site_data.get('name', ''),
                type=site_data.get('type', 0),
                api=site_data.get('api', ''),
                searchable=site_data.get('searchable', 1),
                quickSearch=site_data.get('quickSearch', 1),
                filterable=site_data.get('filterable', 1),
                ext=json.dumps(site_data.get('ext', ''), ensure_ascii=False)
            )
            db.add(site)
        
        # 添加直播分组信息
        for live_data in config_info.get('lives', []):
            live_group = LiveGroup(
                config_id=config.id,
                group=live_data.get('group', ''),
                channels=json.dumps(live_data.get('channels', []), ensure_ascii=False)
            )
            db.add(live_group)
        
        # 添加解析器信息
        for parse_data in config_info.get('parses', []):
            parse_info = ParseInfo(
                config_id=config.id,
                name=parse_data.get('name', ''),
                url=parse_data.get('url', ''),
                type=parse_data.get('type', 0),
                ext=json.dumps(parse_data.get('ext', {}), ensure_ascii=False)
            )
            db.add(parse_info)
    
    def get_interface_sources(self, db: Session, skip: int = 0, limit: int = 100,
                            filters: Optional[Dict[str, Any]] = None) -> Tuple[List[InterfaceSource], int]:
        """获取接口源列表"""
        query = db.query(InterfaceSource)

        if filters:
            if filters.get("category"):
                query = query.filter(InterfaceSource.category == filters["category"])

            if filters.get("status"):
                query = query.filter(InterfaceSource.status == filters["status"])

            if filters.get("search"):
                search_term = f"%{filters['search']}%"
                query = query.filter(
                    or_(
                        InterfaceSource.name.like(search_term),
                        InterfaceSource.description.like(search_term),
                        InterfaceSource.tags.like(search_term)
                    )
                )

        # 获取总数
        total = query.count()

        # 获取分页数据
        items = query.order_by(desc(InterfaceSource.created_at)).offset(skip).limit(limit).all()

        return items, total
    
    def get_interface_source_by_id(self, db: Session, source_id: int) -> Optional[InterfaceSource]:
        """根据ID获取接口源"""
        return db.query(InterfaceSource).filter(InterfaceSource.id == source_id).first()
    
    def update_interface_source(self, db: Session, source_id: int, source_data: Dict[str, Any]) -> InterfaceSource:
        """更新接口源"""
        try:
            source = self.get_interface_source_by_id(db, source_id)
            if not source:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="接口源不存在"
                )
            
            # 更新字段
            for field, value in source_data.items():
                if hasattr(source, field):
                    setattr(source, field, value)
            
            source.updated_at = datetime.utcnow()
            db.commit()
            db.refresh(source)
            
            self.logger.info(f"接口源更新成功: {source.name}")
            return source
            
        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            self.logger.error(f"更新接口源失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="更新接口源失败"
            )
    
    def delete_interface_source(self, db: Session, source_id: int) -> bool:
        """删除接口源"""
        try:
            source = self.get_interface_source_by_id(db, source_id)
            if not source:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="接口源不存在"
                )
            
            db.delete(source)
            db.commit()
            
            self.logger.info(f"接口源删除成功: {source.name}")
            return True
            
        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            self.logger.error(f"删除接口源失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="删除接口源失败"
            )
    
    def create_subscription(self, db: Session, user_id: int, source_id: int, 
                          subscription_data: Dict[str, Any]) -> Subscription:
        """创建订阅"""
        try:
            # 检查是否已订阅
            existing_sub = db.query(Subscription).filter(
                and_(Subscription.user_id == user_id, Subscription.source_id == source_id)
            ).first()
            
            if existing_sub:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="已订阅该接口"
                )
            
            # 检查接口源是否存在
            source = self.get_interface_source_by_id(db, source_id)
            if not source:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="接口源不存在"
                )
            
            # 创建订阅
            subscription = Subscription(
                user_id=user_id,
                source_id=source_id,
                name=subscription_data.get("name", source.name),
                auto_update=subscription_data.get("auto_update", True),
                update_interval=subscription_data.get("update_interval", 3600),
                notify_on_update=subscription_data.get("notify_on_update", False),
                notify_on_error=subscription_data.get("notify_on_error", True)
            )
            
            # 计算下次更新时间
            subscription.next_update_at = datetime.utcnow() + timedelta(seconds=subscription.update_interval)
            
            db.add(subscription)
            db.commit()
            db.refresh(subscription)
            
            self.logger.info(f"订阅创建成功: 用户{user_id} 订阅接口{source_id}")
            return subscription
            
        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            self.logger.error(f"创建订阅失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="创建订阅失败"
            )

    def test_interface_source(self, db: Session, source_id: int) -> Dict[str, Any]:
        """测试接口源"""
        try:
            source = self.get_interface_source_by_id(db, source_id)
            if not source:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="接口源不存在"
                )

            # 使用解密器测试接口
            start_time = datetime.utcnow()
            try:
                content, decrypt_method = self.decryptor.decrypt_config_url(source.url)
                end_time = datetime.utcnow()
                response_time = (end_time - start_time).total_seconds()

                # 更新接口状态
                source.status = "online"
                source.last_success_at = datetime.utcnow()
                source.success_count += 1
                source.decrypt_method = decrypt_method

                db.commit()

                return {
                    "success": True,
                    "message": "接口测试成功",
                    "data": {"content_length": len(content)},
                    "decrypt_method": decrypt_method,
                    "response_time": response_time
                }

            except Exception as e:
                end_time = datetime.utcnow()
                response_time = (end_time - start_time).total_seconds()

                # 更新错误状态
                source.status = "error"
                source.last_error_at = datetime.utcnow()
                source.last_error_message = str(e)
                source.error_count += 1

                db.commit()

                return {
                    "success": False,
                    "message": f"接口测试失败: {str(e)}",
                    "data": {},
                    "decrypt_method": "",
                    "response_time": response_time
                }

        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"测试接口源失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="测试接口源失败"
            )

    def refresh_interface_source(self, db: Session, source_id: int) -> Dict[str, Any]:
        """刷新接口源数据"""
        try:
            source = self.get_interface_source_by_id(db, source_id)
            if not source:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="接口源不存在"
                )

            # 获取并解析配置
            try:
                content, decrypt_method = self.decryptor.decrypt_config_url(source.url)
                config_data = json.loads(content)

                # 更新解密方法
                source.decrypt_method = decrypt_method

                # 解析并保存配置数据
                self._parse_and_save_config(db, source, config_data)

                # 更新状态
                source.status = "online"
                source.last_success_at = datetime.utcnow()
                source.success_count += 1
                source.updated_at = datetime.utcnow()

                db.commit()

                return {
                    "success": True,
                    "message": "接口数据刷新成功",
                    "sites_count": source.sites_count,
                    "lives_count": source.lives_count,
                    "parses_count": source.parses_count
                }

            except Exception as e:
                # 更新错误状态
                source.status = "error"
                source.last_error_at = datetime.utcnow()
                source.last_error_message = str(e)
                source.error_count += 1

                db.commit()

                return {
                    "success": False,
                    "message": f"刷新失败: {str(e)}",
                    "sites_count": source.sites_count,
                    "lives_count": source.lives_count,
                    "parses_count": source.parses_count
                }

        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"刷新接口源失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="刷新接口源失败"
            )
