<template>
  <div class="decrypt-page">
    <!-- 解密工具卡片 -->
    <el-card class="decrypt-card">
      <template #header>
        <div class="card-header">
          <span>解密工具</span>
          <el-button type="primary" @click="getDecryptMethods">
            <el-icon><Refresh /></el-icon>
            刷新方法
          </el-button>
        </div>
      </template>

      <el-tabs v-model="activeTab" type="card">
        <!-- URL解密 -->
        <el-tab-pane label="URL解密" name="url">
          <el-form :model="urlForm" label-width="100px">
            <el-form-item label="解密方法">
              <el-select v-model="urlForm.method" placeholder="选择解密方法">
                <el-option
                  v-for="method in decryptMethods"
                  :key="method.id"
                  :label="method.name"
                  :value="method.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="待解密URL">
              <el-input
                v-model="urlForm.url"
                type="textarea"
                :rows="4"
                placeholder="请输入需要解密的URL"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleUrlDecrypt" :loading="urlDecrypting">
                <el-icon><Key /></el-icon>
                开始解密
              </el-button>
              <el-button @click="clearUrlForm">
                <el-icon><Delete /></el-icon>
                清空
              </el-button>
            </el-form-item>
          </el-form>

          <!-- URL解密结果 -->
          <div v-if="urlResult" class="result-section">
            <h4>解密结果</h4>
            <el-input
              v-model="urlResult"
              type="textarea"
              :rows="6"
              readonly
              class="result-textarea"
            />
            <div class="result-actions">
              <el-button type="success" @click="copyToClipboard(urlResult)">
                <el-icon><DocumentCopy /></el-icon>
                复制结果
              </el-button>
              <el-button type="info" @click="validateResult(urlResult)">
                <el-icon><Check /></el-icon>
                验证结果
              </el-button>
            </div>
          </div>
        </el-tab-pane>

        <!-- 内容解密 -->
        <el-tab-pane label="内容解密" name="content">
          <el-form :model="contentForm" label-width="100px">
            <el-form-item label="解密方法">
              <el-select v-model="contentForm.method" placeholder="选择解密方法">
                <el-option
                  v-for="method in decryptMethods"
                  :key="method.id"
                  :label="method.name"
                  :value="method.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="待解密内容">
              <el-input
                v-model="contentForm.content"
                type="textarea"
                :rows="6"
                placeholder="请输入需要解密的内容"
              />
            </el-form-item>
            <el-form-item label="解密密钥">
              <el-input
                v-model="contentForm.key"
                placeholder="请输入解密密钥（可选）"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleContentDecrypt" :loading="contentDecrypting">
                <el-icon><Key /></el-icon>
                开始解密
              </el-button>
              <el-button @click="clearContentForm">
                <el-icon><Delete /></el-icon>
                清空
              </el-button>
            </el-form-item>
          </el-form>

          <!-- 内容解密结果 -->
          <div v-if="contentResult" class="result-section">
            <h4>解密结果</h4>
            <el-input
              v-model="contentResult"
              type="textarea"
              :rows="8"
              readonly
              class="result-textarea"
            />
            <div class="result-actions">
              <el-button type="success" @click="copyToClipboard(contentResult)">
                <el-icon><DocumentCopy /></el-icon>
                复制结果
              </el-button>
              <el-button type="warning" @click="exportResult(contentResult)">
                <el-icon><Download /></el-icon>
                导出结果
              </el-button>
            </div>
          </div>
        </el-tab-pane>

        <!-- 批量解密 -->
        <el-tab-pane label="批量解密" name="batch">
          <el-form :model="batchForm" label-width="100px">
            <el-form-item label="解密方法">
              <el-select v-model="batchForm.method" placeholder="选择解密方法">
                <el-option
                  v-for="method in decryptMethods"
                  :key="method.id"
                  :label="method.name"
                  :value="method.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="批量URL">
              <el-input
                v-model="batchForm.urls"
                type="textarea"
                :rows="8"
                placeholder="请输入需要批量解密的URL，每行一个"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleBatchDecrypt" :loading="batchDecrypting">
                <el-icon><Key /></el-icon>
                批量解密
              </el-button>
              <el-button @click="clearBatchForm">
                <el-icon><Delete /></el-icon>
                清空
              </el-button>
            </el-form-item>
          </el-form>

          <!-- 批量解密结果 -->
          <div v-if="batchResults.length > 0" class="batch-results">
            <h4>批量解密结果</h4>
            <el-table :data="batchResults" stripe style="width: 100%">
              <el-table-column prop="index" label="序号" width="80" />
              <el-table-column prop="original" label="原始URL" width="300" show-overflow-tooltip />
              <el-table-column prop="decrypted" label="解密结果" show-overflow-tooltip />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.status === 'success' ? 'success' : 'danger'">
                    {{ row.status === 'success' ? '成功' : '失败' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150">
                <template #default="{ row }">
                  <el-button 
                    v-if="row.status === 'success'" 
                    type="primary" 
                    size="small" 
                    @click="copyToClipboard(row.decrypted)"
                  >
                    复制
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="batch-actions">
              <el-button type="success" @click="exportBatchResults">
                <el-icon><Download /></el-icon>
                导出全部结果
              </el-button>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 解密历史 -->
    <el-card class="history-card">
      <template #header>
        <div class="card-header">
          <span>解密历史</span>
          <div>
            <el-button @click="getDecryptHistory">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="danger" @click="clearHistory">
              <el-icon><Delete /></el-icon>
              清空历史
            </el-button>
          </div>
        </div>
      </template>

      <el-table v-loading="historyLoading" :data="historyList" stripe style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag>{{ row.type === 'url' ? 'URL解密' : '内容解密' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="method" label="方法" width="120" />
        <el-table-column prop="input" label="输入内容" width="200" show-overflow-tooltip />
        <el-table-column prop="output" label="输出结果" width="200" show-overflow-tooltip />
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="copyToClipboard(row.output)">
              复制
            </el-button>
            <el-button type="success" size="small" @click="reuseHistory(row)">
              重用
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="historyPagination.page"
          v-model:page-size="historyPagination.limit"
          :page-sizes="[10, 20, 50]"
          :total="historyPagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleHistorySizeChange"
          @current-change="handleHistoryCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh, Key, Delete, DocumentCopy, Check, Download
} from '@element-plus/icons-vue'
import { decryptApi } from '@/api/decrypt'
import { formatDate } from '@/utils/date'

// 响应式数据
const activeTab = ref('url')
const urlDecrypting = ref(false)
const contentDecrypting = ref(false)
const batchDecrypting = ref(false)
const historyLoading = ref(false)

const decryptMethods = ref([])
const urlResult = ref('')
const contentResult = ref('')
const batchResults = ref([])
const historyList = ref([])

// 表单数据
const urlForm = reactive({
  method: '',
  url: ''
})

const contentForm = reactive({
  method: '',
  content: '',
  key: ''
})

const batchForm = reactive({
  method: '',
  urls: ''
})

// 分页数据
const historyPagination = reactive({
  page: 1,
  limit: 20,
  total: 0
})

// 获取解密方法列表
const getDecryptMethods = async () => {
  try {
    const response = await decryptApi.getDecryptMethods()
    decryptMethods.value = response.data || []
  } catch (error) {
    console.error('获取解密方法失败:', error)
    ElMessage.error('获取解密方法失败')
  }
}

// URL解密
const handleUrlDecrypt = async () => {
  if (!urlForm.method || !urlForm.url) {
    ElMessage.warning('请选择解密方法并输入URL')
    return
  }

  urlDecrypting.value = true
  try {
    const response = await decryptApi.decryptUrl({
      method: urlForm.method,
      url: urlForm.url
    })

    urlResult.value = response.data.result || ''
    ElMessage.success('URL解密成功')
  } catch (error) {
    console.error('URL解密失败:', error)
    ElMessage.error('URL解密失败')
  } finally {
    urlDecrypting.value = false
  }
}

// 内容解密
const handleContentDecrypt = async () => {
  if (!contentForm.method || !contentForm.content) {
    ElMessage.warning('请选择解密方法并输入内容')
    return
  }

  contentDecrypting.value = true
  try {
    const response = await decryptApi.decryptContent({
      method: contentForm.method,
      content: contentForm.content,
      key: contentForm.key
    })

    contentResult.value = response.data.result || ''
    ElMessage.success('内容解密成功')
  } catch (error) {
    console.error('内容解密失败:', error)
    ElMessage.error('内容解密失败')
  } finally {
    contentDecrypting.value = false
  }
}

// 批量解密
const handleBatchDecrypt = async () => {
  if (!batchForm.method || !batchForm.urls) {
    ElMessage.warning('请选择解密方法并输入URL列表')
    return
  }

  const urls = batchForm.urls.split('\n').filter(url => url.trim())
  if (urls.length === 0) {
    ElMessage.warning('请输入有效的URL列表')
    return
  }

  batchDecrypting.value = true
  try {
    const response = await decryptApi.batchDecryptUrls({
      method: batchForm.method,
      urls: urls
    })

    batchResults.value = response.data.results || []
    ElMessage.success(`批量解密完成，成功 ${batchResults.value.filter(r => r.status === 'success').length} 个`)
  } catch (error) {
    console.error('批量解密失败:', error)
    ElMessage.error('批量解密失败')
  } finally {
    batchDecrypting.value = false
  }
}

// 复制到剪贴板
const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('复制成功')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

// 验证解密结果
const validateResult = async (result) => {
  try {
    const response = await decryptApi.validateDecryptResult({ result })
    if (response.data.valid) {
      ElMessage.success('解密结果验证通过')
    } else {
      ElMessage.warning('解密结果验证失败')
    }
  } catch (error) {
    console.error('验证失败:', error)
    ElMessage.error('验证失败')
  }
}

// 导出结果
const exportResult = (result) => {
  const blob = new Blob([result], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `decrypt_result_${Date.now()}.txt`
  a.click()
  URL.revokeObjectURL(url)
  ElMessage.success('导出成功')
}

// 导出批量结果
const exportBatchResults = () => {
  const results = batchResults.value.map(item =>
    `${item.original} -> ${item.decrypted} (${item.status})`
  ).join('\n')

  const blob = new Blob([results], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `batch_decrypt_results_${Date.now()}.txt`
  a.click()
  URL.revokeObjectURL(url)
  ElMessage.success('导出成功')
}

// 清空表单
const clearUrlForm = () => {
  urlForm.method = ''
  urlForm.url = ''
  urlResult.value = ''
}

const clearContentForm = () => {
  contentForm.method = ''
  contentForm.content = ''
  contentForm.key = ''
  contentResult.value = ''
}

const clearBatchForm = () => {
  batchForm.method = ''
  batchForm.urls = ''
  batchResults.value = []
}

// 获取解密历史
const getDecryptHistory = async () => {
  historyLoading.value = true
  try {
    // TODO: 暂时禁用解密历史功能，等待后端API实现
    // const params = {
    //   page: historyPagination.page,
    //   limit: historyPagination.limit
    // }

    // const response = await decryptApi.getDecryptHistory(params)
    // const data = response.data

    // historyList.value = data.items || []
    // historyPagination.total = data.total || 0

    // 暂时返回空数据
    historyList.value = []
    historyPagination.total = 0
  } catch (error) {
    console.error('获取解密历史失败:', error)
    ElMessage.error('获取解密历史失败')
  } finally {
    historyLoading.value = false
  }
}

// 清空历史
const clearHistory = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有解密历史吗？',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // TODO: 暂时禁用清空历史功能，等待后端API实现
    // await decryptApi.clearDecryptHistory()
    ElMessage.success('清空成功')
    getDecryptHistory()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空历史失败:', error)
      ElMessage.error('清空历史失败')
    }
  }
}

// 重用历史记录
const reuseHistory = (record) => {
  if (record.type === 'url') {
    activeTab.value = 'url'
    urlForm.method = record.method
    urlForm.url = record.input
    urlResult.value = record.output
  } else {
    activeTab.value = 'content'
    contentForm.method = record.method
    contentForm.content = record.input
    contentResult.value = record.output
  }
  ElMessage.success('历史记录已重用')
}

// 分页处理
const handleHistorySizeChange = (size) => {
  historyPagination.limit = size
  historyPagination.page = 1
  getDecryptHistory()
}

const handleHistoryCurrentChange = (page) => {
  historyPagination.page = page
  getDecryptHistory()
}

// 页面加载时获取数据
onMounted(() => {
  getDecryptMethods()
  getDecryptHistory()
})
</script>

<style scoped>
.decrypt-page {
  padding: 20px;
}

.decrypt-card {
  margin-bottom: 20px;
}

.history-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-section {
  margin-top: 20px;
  padding: 20px;
  background-color: var(--el-bg-color-page);
  border-radius: 8px;
}

.result-section h4 {
  margin: 0 0 16px 0;
  color: var(--el-text-color-primary);
}

.result-textarea {
  margin-bottom: 16px;
}

.result-actions {
  display: flex;
  gap: 12px;
}

.batch-results {
  margin-top: 20px;
}

.batch-results h4 {
  margin: 0 0 16px 0;
  color: var(--el-text-color-primary);
}

.batch-actions {
  margin-top: 16px;
  text-align: right;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}
</style>
