{"version": 3, "file": "calendar2.js", "sources": ["../../../../../../packages/components/calendar/src/calendar.vue"], "sourcesContent": ["<template>\n  <div :class=\"ns.b()\">\n    <div :class=\"ns.e('header')\">\n      <slot name=\"header\" :date=\"i18nDate\">\n        <div :class=\"ns.e('title')\">{{ i18nDate }}</div>\n        <div v-if=\"validatedRange.length === 0\" :class=\"ns.e('button-group')\">\n          <el-button-group>\n            <el-button size=\"small\" @click=\"selectDate('prev-month')\">\n              {{ t('el.datepicker.prevMonth') }}\n            </el-button>\n            <el-button size=\"small\" @click=\"selectDate('today')\">\n              {{ t('el.datepicker.today') }}\n            </el-button>\n            <el-button size=\"small\" @click=\"selectDate('next-month')\">\n              {{ t('el.datepicker.nextMonth') }}\n            </el-button>\n          </el-button-group>\n        </div>\n      </slot>\n    </div>\n    <div v-if=\"validatedRange.length === 0\" :class=\"ns.e('body')\">\n      <date-table :date=\"date\" :selected-day=\"realSelectedDay\" @pick=\"pickDay\">\n        <template v-if=\"$slots['date-cell']\" #date-cell=\"data\">\n          <slot name=\"date-cell\" v-bind=\"data\" />\n        </template>\n      </date-table>\n    </div>\n    <div v-else :class=\"ns.e('body')\">\n      <date-table\n        v-for=\"(range_, index) in validatedRange\"\n        :key=\"index\"\n        :date=\"range_[0]\"\n        :selected-day=\"realSelectedDay\"\n        :range=\"range_\"\n        :hide-header=\"index !== 0\"\n        @pick=\"pickDay\"\n      >\n        <template v-if=\"$slots['date-cell']\" #date-cell=\"data\">\n          <slot name=\"date-cell\" v-bind=\"data\" />\n        </template>\n      </date-table>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { ElButton, ElButtonGroup } from '@element-plus/components/button'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport DateTable from './date-table.vue'\nimport { useCalendar } from './use-calendar'\nimport { calendarEmits, calendarProps } from './calendar'\n\nconst ns = useNamespace('calendar')\n\nconst COMPONENT_NAME = 'ElCalendar'\ndefineOptions({\n  name: COMPONENT_NAME,\n})\n\nconst props = defineProps(calendarProps)\nconst emit = defineEmits(calendarEmits)\n\nconst {\n  calculateValidatedDateRange,\n  date,\n  pickDay,\n  realSelectedDay,\n  selectDate,\n  validatedRange,\n} = useCalendar(props, emit, COMPONENT_NAME)\n\nconst { t } = useLocale()\n\nconst i18nDate = computed(() => {\n  const pickedMonth = `el.datepicker.month${date.value.format('M')}`\n  return `${date.value.year()} ${t('el.datepicker.year')} ${t(pickedMonth)}`\n})\n\ndefineExpose({\n  /** @description currently selected date */\n  selectedDay: realSelectedDay,\n  /** @description select a specific date */\n  pickDay,\n  /** @description select date */\n  selectDate,\n  /** @description Calculate the validate date range according to the start and end dates */\n  calculateValidatedDateRange,\n})\n</script>\n"], "names": ["useNamespace", "useCalendar", "useLocale", "computed", "_openBlock", "_createElementBlock", "_normalizeClass", "_unref"], "mappings": ";;;;;;;;;;;;;;uCAwDc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR;;;;;;;AALA,IAAM,MAAA,EAAA,GAAKA,mBAAa,UAAU,CAAA,CAAA;AAUlC,IAAM,MAAA;AAAA,MACJ,2BAAA;AAAA,MACA,IAAA;AAAA,MACA,OAAA;AAAA,MACA,eAAA;AAAA,MACA,UAAA;AAAA,MACA,cAAA;AAAA,KACE,GAAAC,uBAAA,CAAY,KAAO,EAAA,IAAA,EAAM,cAAc,CAAA,CAAA;AAE3C,IAAM,MAAA,EAAE,CAAE,EAAA,GAAIC,iBAAU,EAAA,CAAA;AAExB,IAAM,MAAA,QAAA,GAAWC,aAAS,MAAM;AAC9B,MAAA,MAAM,cAAc,CAAsB,mBAAA,EAAA,IAAA,CAAK,KAAM,CAAA,MAAA,CAAO,GAAG,CAAC,CAAA,CAAA,CAAA;AAChE,MAAA,OAAO,CAAG,EAAA,IAAA,CAAK,KAAM,CAAA,IAAA,EAAM,CAAA,CAAA,EAAI,CAAE,CAAA,oBAAoB,CAAC,CAAA,CAAA,EAAI,CAAE,CAAA,WAAW,CAAC,CAAA,CAAA,CAAA;AAAA,KACzE,CAAA,CAAA;AAED,IAAa,MAAA,CAAA;AAAA,MAAA,WAAA,EAAA,eAAA;AAAA,MAEX,OAAa;AAAA,MAAA,UAAA;AAAA,MAEb,2BAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAEA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAAA,OAAAC,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AAAA,QAEA,KAAA,EAAAC,kBAAA,CAAAC,SAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA;AAAA,OACD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}