{"version": 3, "file": "tab-bar.js", "sources": ["../../../../../../packages/components/tabs/src/tab-bar.ts"], "sourcesContent": ["import { buildProps, definePropType, mutable } from '@element-plus/utils'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type { TabPaneName, TabsPaneContext } from './constants'\nimport type TabBar from './tab-bar.vue'\n\nexport const tabBarProps = buildProps({\n  tabs: {\n    type: definePropType<TabsPaneContext[]>(Array),\n    default: () => mutable([] as const),\n  },\n  tabRefs: {\n    type: definePropType<{ [key: TabPaneName]: HTMLDivElement }>(Object),\n    default: () => mutable({} as const),\n  },\n} as const)\n\nexport type TabBarProps = ExtractPropTypes<typeof tabBarProps>\nexport type TabBarPropsPublic = __ExtractPublicPropTypes<typeof tabBarProps>\nexport type TabBarInstance = InstanceType<typeof TabBar> & unknown\n"], "names": ["buildProps", "definePropType", "mutable"], "mappings": ";;;;;;;AACY,MAAC,WAAW,GAAGA,kBAAU,CAAC;AACtC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAEC,sBAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,MAAMC,kBAAO,CAAC,EAAE,CAAC;AAC9B,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAED,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,MAAMC,kBAAO,CAAC,EAAE,CAAC;AAC9B,GAAG;AACH,CAAC;;;;"}