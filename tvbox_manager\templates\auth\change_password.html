<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修改密码 - TVBox Manager</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        /* 重置样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body, html {
            height: 100%;
            font-family: 'Nunito', system-ui, -apple-system, sans-serif;
            background-color: #f3f4f6;
        }
        
        /* 登录页面主容器 */
        .login-container {
            height: 100vh;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #06b6d4 0%, #0369a1 100%);
            position: relative;
            overflow: hidden;
        }
        
        /* 背景装饰元素 */
        .bg-circles {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }
        
        .bg-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.05);
        }
        
        .bg-circle:nth-child(1) {
            width: 300px;
            height: 300px;
            top: -100px;
            right: -100px;
        }
        
        .bg-circle:nth-child(2) {
            width: 500px;
            height: 500px;
            bottom: -200px;
            left: -200px;
        }
        
        .bg-circle:nth-child(3) {
            width: 150px;
            height: 150px;
            top: 50%;
            left: 10%;
        }
        
        /* 登录卡片容器 - 减小最大宽度 */
        .login-card-container {
            width: 100%;
            max-width: 380px;
            padding: 0 15px;
            z-index: 2;
        }
        
        /* 登录卡片 */
        .login-card {
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 15px 30px -10px rgba(0, 0, 0, 0.25);
            backdrop-filter: blur(10px);
            transition: transform 0.3s ease;
        }
        
        .login-card:hover {
            transform: translateY(-3px);
        }
        
        /* 卡片头部 - 减小高度和内边距 */
        .login-header {
            padding: 20px 20px;
            text-align: center;
            background: linear-gradient(to right, #0ea5e9, #0369a1);
            color: white;
            position: relative;
        }
        
        /* 缩小logo尺寸 */
        .login-logo {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
        }
        
        .login-logo i {
            font-size: 24px;
            color: white;
        }
        
        /* 减小标题和副标题的字体大小 */
        .login-title {
            font-size: 22px;
            font-weight: 700;
            margin-bottom: 3px;
        }
        
        .login-subtitle {
            font-size: 14px;
            opacity: 0.85;
        }
        
        /* 卡片内容 - 减小内边距 */
        .login-body {
            padding: 20px 20px;
        }
        
        /* 表单样式 - 减小间距 */
        .login-form .form-group {
            margin-bottom: 16px;
        }
        
        .login-form label {
            display: block;
            font-size: 13px;
            font-weight: 600;
            color: #4b5563;
            margin-bottom: 5px;
        }
        
        .input-group {
            position: relative;
        }
        
        .input-group i {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #0284c7;
        }
        
        /* 减小输入框的高度和内边距 */
        .login-form input[type="email"],
        .login-form input[type="password"],
        .login-form input[type="text"] {
            width: 100%;
            padding: 10px 15px 10px 35px;
            font-size: 14px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background-color: #f9fafb;
            transition: all 0.3s;
        }
        
        .login-form input:focus {
            outline: none;
            border-color: #0ea5e9;
            box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.15);
            background-color: #fff;
        }
        
        /* 登录按钮 - 减小高度和内边距 */
        .login-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            padding: 10px 15px;
            font-size: 15px;
            font-weight: 600;
            color: white;
            background: linear-gradient(to right, #0ea5e9, #0284c7);
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .login-btn:hover {
            background: linear-gradient(to right, #0284c7, #0369a1);
            transform: translateY(-2px);
            box-shadow: 0 6px 10px -3px rgba(0, 0, 0, 0.1);
        }
        
        .login-btn:active {
            transform: translateY(0);
        }
        
        .login-btn i {
            margin-right: 8px;
        }
        
        /* 注册链接 - 减小间距 */
        .register-link {
            text-align: center;
            margin-top: 18px;
            padding-top: 15px;
            border-top: 1px solid #e5e7eb;
            font-size: 13px;
            color: #6b7280;
        }
        
        .register-link a {
            color: #0ea5e9;
            font-weight: 600;
            text-decoration: none;
            transition: color 0.2s;
        }
        
        .register-link a:hover {
            color: #0369a1;
            text-decoration: underline;
        }
        
        /* 页脚 - 减小内边距 */
        .login-footer {
            text-align: center;
            padding: 12px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
        }
        
        .login-footer a {
            color: white;
            font-weight: 500;
            text-decoration: none;
        }
        
        .login-footer a:hover {
            text-decoration: underline;
        }
        
        /* 错误信息样式 */
        .form-error {
            color: #ef4444;
            font-size: 12px;
            margin-top: 5px;
            display: flex;
            align-items: center;
        }
        
        .form-error i {
            margin-right: 4px;
        }
        
        /* 密码强度指示器 */
        .password-strength {
            height: 5px;
            margin-top: 8px;
            border-radius: 3px;
            background-color: #e5e7eb;
            overflow: hidden;
        }
        
        .password-strength-bar {
            height: 100%;
            width: 0;
            transition: width 0.3s ease, background-color 0.3s ease;
        }
        
        .password-strength-bar.weak {
            width: 33%;
            background-color: #ef4444;
        }
        
        .password-strength-bar.medium {
            width: 66%;
            background-color: #f59e0b;
        }
        
        .password-strength-bar.strong {
            width: 100%;
            background-color: #10b981;
        }
        
        /* 媒体查询 */
        @media (max-width: 640px) {
            .login-card {
                border-radius: 10px;
            }
            
            .login-header {
                padding: 15px 15px;
            }
            
            .login-body {
                padding: 20px 15px;
            }
            
            .login-logo {
                width: 40px;
                height: 40px;
            }
            
            .login-title {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- 背景装饰 -->
        <div class="bg-circles">
            <div class="bg-circle"></div>
            <div class="bg-circle"></div>
            <div class="bg-circle"></div>
        </div>
        
        <div class="login-card-container">
            <!-- 登录卡片 -->
            <div class="login-card">
                <!-- 卡片头部 -->
                <div class="login-header">
                    <div class="login-logo">
                        <i class="fas fa-key"></i>
                    </div>
                    <h1 class="login-title">修改密码</h1>
                    <p class="login-subtitle">更新您的账户密码</p>
                </div>
                
                <!-- 卡片内容 -->
                <div class="login-body">
                    <form action="{{ url_for('security.change_password') }}" method="POST" class="login-form" name="change_password_form">
                        {{ change_password_form.hidden_tag() }}
                        
                        <div class="form-group">
                            <label for="password">当前密码</label>
                            <div class="input-group">
                                <i class="fas fa-lock"></i>
                                {{ change_password_form.password(class="", placeholder="请输入当前密码") }}
                            </div>
                            {% if change_password_form.password.errors %}
                                {% for error in change_password_form.password.errors %}
                                <div class="form-error">
                                    <i class="fas fa-exclamation-circle"></i> {{ error }}
                                </div>
                                {% endfor %}
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label for="new_password">新密码</label>
                            <div class="input-group">
                                <i class="fas fa-lock"></i>
                                {{ change_password_form.new_password(class="", placeholder="请输入新密码", id="new_password") }}
                            </div>
                            {% if change_password_form.new_password.errors %}
                                {% for error in change_password_form.new_password.errors %}
                                <div class="form-error">
                                    <i class="fas fa-exclamation-circle"></i> {{ error }}
                                </div>
                                {% endfor %}
                            {% endif %}
                            <div class="password-strength">
                                <div class="password-strength-bar" id="password-strength-bar"></div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="new_password_confirm">确认新密码</label>
                            <div class="input-group">
                                <i class="fas fa-lock"></i>
                                {{ change_password_form.new_password_confirm(class="", placeholder="请再次输入新密码") }}
                            </div>
                            {% if change_password_form.new_password_confirm.errors %}
                                {% for error in change_password_form.new_password_confirm.errors %}
                                <div class="form-error">
                                    <i class="fas fa-exclamation-circle"></i> {{ error }}
                                </div>
                                {% endfor %}
                            {% endif %}
                        </div>
                        
                        <button type="submit" class="login-btn">
                            <i class="fas fa-save"></i> 保存修改
                        </button>
                        
                        <div class="register-link">
                            <a href="{{ url_for('interface.index') }}">返回主页</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 移除Flask-Security默认内容
        const allContainers = document.querySelectorAll('.container, .navbar, .page-header, h1.flask-security');
        allContainers.forEach(element => {
            if (element.closest('.login-card') === null) {
                element.style.display = 'none';
            }
        });
        
        // 添加输入框聚焦效果
        const inputs = document.querySelectorAll('.login-form input');
        inputs.forEach(input => {
            const iconEl = input.parentElement.querySelector('i');
            
            input.addEventListener('focus', () => {
                if (iconEl) iconEl.style.color = '#0ea5e9';
            });
            
            input.addEventListener('blur', () => {
                if (iconEl) iconEl.style.color = '#0284c7';
            });
        });
        
        // 密码强度检测
        const passwordInput = document.getElementById('new_password');
        const strengthBar = document.getElementById('password-strength-bar');
        
        if (passwordInput && strengthBar) {
            passwordInput.addEventListener('input', function() {
                const password = this.value;
                let strength = 0;
                
                // 检查密码长度
                if (password.length >= 6) strength += 1;
                if (password.length >= 10) strength += 1;
                
                // 检查是否包含数字和字母
                if (/\d/.test(password) && /[a-zA-Z]/.test(password)) strength += 1;
                
                // 检查是否包含特殊字符
                if (/[^a-zA-Z0-9]/.test(password)) strength += 1;
                
                // 根据得分更新强度条
                strengthBar.className = 'password-strength-bar';
                if (strength >= 3) {
                    strengthBar.classList.add('strong');
                } else if (strength >= 2) {
                    strengthBar.classList.add('medium');
                } else if (strength >= 1) {
                    strengthBar.classList.add('weak');
                }
            });
        }
        
        // 错误信息本地化处理
        const errorMessages = document.querySelectorAll('.form-error');
        errorMessages.forEach(errorMsg => {
            let text = errorMsg.textContent.trim();
            
            // 将常见的英文错误信息转换为中文
            if (text.includes('Password not provided')) {
                errorMsg.textContent = '请提供密码';
            } else if (text.includes('New password not provided')) {
                errorMsg.textContent = '请提供新密码';
            } else if (text.includes('Password must be at least')) {
                errorMsg.textContent = '密码长度不足';
            } else if (text.includes('Passwords do not match')) {
                errorMsg.textContent = '两次密码输入不一致';
            } else if (text.includes('Password does not match')) {
                errorMsg.textContent = '当前密码不正确';
            }
        });
    });
    </script>
</body>
</html> 