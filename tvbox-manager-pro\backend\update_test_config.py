#!/usr/bin/env python3
"""
更新测试配置
"""
import sqlite3
import os
import json

def update_test_config():
    """更新测试配置"""
    db_path = "data/tvbox.db"
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    # 读取测试配置
    with open('test_config.json', 'r', encoding='utf-8') as f:
        test_config = json.load(f)
    
    test_config_str = json.dumps(test_config, ensure_ascii=False)
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 更新接口1的配置为测试配置
        cursor.execute("""
            UPDATE interface_sources 
            SET config_content = ?, localized_config = NULL
            WHERE id = 1
        """, (test_config_str,))
        
        # 清理旧的本地化文件记录
        cursor.execute("DELETE FROM localized_files WHERE interface_id = 1")
        
        conn.commit()
        print("测试配置已更新到数据库")
        print(f"配置长度: {len(test_config_str)}")
        print(f"Spider URL: {test_config.get('spider', 'N/A')}")
        
    except Exception as e:
        print(f"更新失败: {str(e)}")
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    update_test_config()
