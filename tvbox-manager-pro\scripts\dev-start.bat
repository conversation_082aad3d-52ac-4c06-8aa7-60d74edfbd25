@echo off
chcp 65001 >nul
echo 🚀 启动 TVBox Manager Pro 开发环境...

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python 未安装，请先安装 Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请先安装 Node.js 16+
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo 📁 创建必要的目录...
if not exist "backend\data" mkdir backend\data
if not exist "backend\data\uploads" mkdir backend\data\uploads
if not exist "backend\data\backups" mkdir backend\data\backups
if not exist "backend\logs" mkdir backend\logs

echo 🔧 启动后端服务...
cd backend

REM 检查虚拟环境
if not exist "venv" (
    echo 📦 创建Python虚拟环境...
    python -m venv venv
)

REM 激活虚拟环境
call venv\Scripts\activate.bat

REM 安装依赖
echo 📦 安装Python依赖...
pip install -r requirements.txt

REM 启动后端服务
echo 🚀 启动后端服务 (端口: 8000)...
start "TVBox Backend" cmd /k "venv\Scripts\activate.bat && python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"

cd ..\frontend

echo 📦 安装前端依赖...
if not exist "node_modules" (
    npm install
)

echo 🚀 启动前端服务 (端口: 3000)...
start "TVBox Frontend" cmd /k "npm run dev"

echo ⏳ 等待服务启动...
timeout /t 5 /nobreak >nul

echo.
echo ✅ TVBox Manager Pro 开发环境启动成功！
echo.
echo 📱 前端访问地址: http://localhost:3000
echo 🔧 后端API地址: http://localhost:8000
echo 📚 API文档地址: http://localhost:8000/docs
echo.
echo 👤 默认管理员账号:
echo    邮箱: <EMAIL>
echo    密码: admin123
echo.
echo 🎉 享受开发 TVBox Manager Pro！
echo.
echo 按任意键打开浏览器...
pause >nul

start http://localhost:3000
