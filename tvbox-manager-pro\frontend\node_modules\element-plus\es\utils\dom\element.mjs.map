{"version": 3, "file": "element.mjs", "sources": ["../../../../../packages/utils/dom/element.ts"], "sourcesContent": ["import { isString } from '../types'\nimport { isClient } from '../browser'\n\ntype GetElement = <T extends string | HTMLElement | Window | null | undefined>(\n  target: T\n) => T extends string ? HTMLElement | null : T\n\nexport const getElement = ((\n  target: string | HTMLElement | Window | null | undefined\n) => {\n  if (!isClient || target === '') return null\n  if (isString(target)) {\n    try {\n      return document.querySelector<HTMLElement>(target)\n    } catch {\n      return null\n    }\n  }\n  return target\n}) as GetElement\n"], "names": [], "mappings": ";;;AAEY,MAAC,UAAU,GAAG,CAAC,MAAM,KAAK;AACtC,EAAE,IAAI,CAAC,QAAQ,IAAI,MAAM,KAAK,EAAE;AAChC,IAAI,OAAO,IAAI,CAAC;AAChB,EAAE,IAAI,QAAQ,CAAC,MAAM,CAAC,EAAE;AACxB,IAAI,IAAI;AACR,MAAM,OAAO,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AAC5C,KAAK,CAAC,OAAO,CAAC,EAAE;AAChB,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,GAAG;AACH,EAAE,OAAO,MAAM,CAAC;AAChB;;;;"}