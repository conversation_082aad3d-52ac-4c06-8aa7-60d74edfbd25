import { request } from '@/utils/request'

export const decryptApi = {
  // URL解密
  decryptUrl(data) {
    return request.post('/v1/decrypt/url', data)
  },

  // 内容解密
  decryptContent(data) {
    return request.post('/v1/decrypt/content', data)
  },

  // 获取解密方法列表
  getDecryptMethods() {
    return request.get('/v1/decrypt/methods')
  },

  // 批量URL解密
  batchDecryptUrls(data) {
    return request.post('/v1/decrypt/batch', data)
  },

  // 验证解密结果
  validateDecryptResult(data) {
    return request.post('/v1/decrypt/validate', data)
  },

  // 获取解密历史
  getDecryptHistory(params = {}) {
    return request.get('/v1/decrypt/history', params)
  },

  // 清理解密历史
  clearDecryptHistory() {
    return request.delete('/v1/decrypt/history')
  },

  // 导出解密结果
  exportDecryptResults(data) {
    return request.post('/v1/decrypt/export', data, {
      responseType: 'blob'
    })
  },

  // 获取解密统计
  getDecryptStats() {
    return request.get('/v1/decrypt/stats')
  }
}
