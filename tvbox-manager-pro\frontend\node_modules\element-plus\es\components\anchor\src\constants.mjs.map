{"version": 3, "file": "constants.mjs", "sources": ["../../../../../../packages/components/anchor/src/constants.ts"], "sourcesContent": ["import type { Injection<PERSON><PERSON>, Ref } from 'vue'\nimport type { UseNamespaceReturn } from '@element-plus/hooks'\n\nexport interface AnchorLinkState {\n  el: HTMLElement\n  href: string\n}\n\nexport interface AnchorContext {\n  ns: UseNamespaceReturn\n  direction: string\n  currentAnchor: Ref<string>\n  addLink(state: AnchorLinkState): void\n  removeLink(href: string): void\n  handleClick(e: MouseEvent, href?: string): void\n}\n\nexport const anchorKey: InjectionKey<AnchorContext> = Symbol('anchor')\n"], "names": [], "mappings": "AAAY,MAAC,SAAS,GAAG,MAAM,CAAC,QAAQ;;;;"}