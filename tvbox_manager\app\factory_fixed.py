#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
应用工厂模块
实现Flask应用的创建和配置
"""

import logging
import traceback
import os
import sys
import importlib.util

from flask import Flask, request, current_app, render_template
from sqlalchemy.exc import SQLAlchemyError
from werkzeug.middleware.proxy_fix import ProxyFix
from flask_wtf.csrf import CSRFProtect

# 直接加载config.py模块
config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config.py")
spec = importlib.util.spec_from_file_location("config", config_path)
config = importlib.util.module_from_spec(spec)
spec.loader.exec_module(config)

# 直接加载utils.py模块
utils_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "utils.py")
spec = importlib.util.spec_from_file_location("utils", utils_path)
utils = importlib.util.module_from_spec(spec)
spec.loader.exec_module(utils)

# 直接加载security.py模块
security_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "auth", "security.py")
spec = importlib.util.spec_from_file_location("security", security_path)
security_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(security_module)

# 导入数据库实例
from tvbox_manager.config.models import db

# CSRF保护
csrf = CSRFProtect()

def register_extensions(app):
    """Initialize extensions for the app"""
    from importlib import import_module

    # Import and initialize the extensions
    # Each extension registers itself with the app
    extensions = [
        'flask_migrate',
        'flask_login',
    ]
    
    for extension_module in extensions:
        import_module(extension_module)

def configure_extensions(app):
    """Configure extensions after they have been initialized"""
    db.init_app(app)
    csrf.init_app(app)

def create_app(config_obj):
    """Factory function for app creation"""
    app = Flask(__name__, static_folder='../static')
    app.wsgi_app = ProxyFix(app.wsgi_app)
    app.config.from_object(config_obj)
    app.config['PRESERVE_CONTEXT_ON_EXCEPTION'] = False
    
    # Configure custom template folder path
    template_dir = utils.get_current_template_dir()
    if template_dir:
        app.template_folder = os.path.join('../templates', template_dir)
    
    # Register extensions
    register_extensions(app)
    configure_extensions(app)
    
    # 初始化安全模块
    security = security_module.init_security(app)

    # 导入视图模块
    from tvbox_manager.config.views import config_bp
    from tvbox_manager.interface.views import interface_bp
    from tvbox_manager.live.views import live_bp

    # 注册蓝图
    app.register_blueprint(config_bp, url_prefix='/config')
    app.register_blueprint(interface_bp, url_prefix='/')
    app.register_blueprint(live_bp, url_prefix='/live')

    # Add security headers
    @app.after_request
    def add_security_headers(response):
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'SAMEORIGIN'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        return response

    # Error handlers
    @app.errorhandler(404)
    def page_not_found(error):
        return render_template(f'{template_dir}/errors/404.html'), 404

    @app.errorhandler(500)
    def internal_server_error(error):
        # Log the error and stacktrace
        app.logger.error(f"500 error: {error}")
        app.logger.error(traceback.format_exc())
        
        # Handle database errors
        if isinstance(error, SQLAlchemyError):
            db.session.rollback()
            app.logger.error("Database error: Rolling back session.")
        
        return render_template(f'{template_dir}/errors/500.html'), 500
    
    @app.errorhandler(403)
    def forbidden(error):
        return render_template(f'{template_dir}/errors/403.html'), 403

    # Ensure database session is closed if any error occurs during request
    @app.teardown_request
    def session_cleanup(exception=None):
        if exception and db.session.is_active:
            db.session.rollback()

    # Inject app info to templates
    @app.context_processor
    def inject_app_info():
        return {
            'app_name': 'TVBox Manager',
            'app_version': '1.0.0',
            'current_year': '2025'
        }

    # Configure logging
    if not app.debug:
        format_str = '%(asctime)s - %(levelname)s - %(message)s'
        formatter = logging.Formatter(format_str)
        
        file_handler = logging.FileHandler('app.log')
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.INFO)
        
        app.logger.addHandler(file_handler)
        app.logger.setLevel(logging.INFO)

    return app

# for development and production
app_config = config.config_dict['Debug']
app = create_app(app_config) 