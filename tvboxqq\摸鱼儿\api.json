//资源全部免费，仅供测试交流，请勿内置传播与转卖；关注微信公众号：奇奇乐分享，发送“本地包”获取教程与更多资源！

{
    "spider": "./spider.jar",
    "wallpaper": "http://我不是.摸鱼儿.com/wallpaper/",
    "logo": "https://s1.imagehub.cc/images/2024/06/11/80538bfb3835ffebf7c5fd0716280801.jpeg",
    "warningText": "摸鱼儿：资源来自网络，无盈利纯技术分享！[所有内容仅供学习使用，请勿用于违法及商业用途，请勿付费购买]",
    "sites": [
        {
            "key": "豆豆",
            "name": "♨️网盘升级┃全速启动",
            "type": 3,
            "api": "csp_DouDouGuard",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 0
        },
        {
            "key": "update",
            "name": "🎬️摸鱼┃电影┃预告",
            "type": 3,
            "api": "csp_YGPGuard",
            "searchable": 0,
            "quickSearch": 0,
            "changeable": 0
        },
        {
            "key": "config",
            "name": "🚀辅助┃配置┃中心",
            "type": 3,
            "api": "csp_Config",
            "jar": "./jars/config.jar",
            "searchable": 0,
            "quickSearch": 0,
            "style": {
                "type": "rect",
                "ratio": 1.61
            }
        },
        {
            "key": "huban",
            "name": "🚀虎斑┃弹幕┃小窗",
            "type": 3,
            "jar": "./jars/huban.jar",
            "api": "csp_Huban"
        },
        {
            "key": "玩偶",
            "name": "♨️玩偶智能┃4K弹幕",
            "type": 3,
            "api": "csp_WoGGGuard",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 0,
            "ext": {
                "Cloud-drive": "TVBox/Cloud-drive.txt",
                "from": "4k|auto",
                "siteUrl": "https://www.wogg.com/",
                "danMu": "弹"
            }
        },
        {
            "key": "行动",
            "name": "🌀行动┃超清┃弹幕",
            "type": 3,
            "api": "csp_AppSy",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "http://160.202.246.9:2356",
                "key1": "aassddwwxxllsx1x",
                "key2": "aassddwwxxllsx1x",
                "key3": "aassddwwxxllsx1x"
            }
        },
        {
            "key": "追忆",
            "name": "🌀追忆┃超清┃弹幕",
            "type": 3,
            "api": "csp_AppSy",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "http://110.42.7.130:1866",
                "key1": "aassddwwxxllsx1x",
                "key2": "2083c87e98b6ce08",
                "key3": "2083c87e98b6ce08"
            }
        },
        {
            "key": "咖啡",
            "name": "🌀咖啡┃超清┃弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet2",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "",
                "site": "./txt/2.txt",
                "dataKey": "qwertyuiopqwertt",
                "dataIv": "qwertyuiopqwertt",
                "deviceId": "",
                "version": "109"
            }
        },
        {
            "key": "灵虎",
            "name": "🌀灵虎┃超清┃弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "",
                "site": "./txt/89.txt",
                "dataKey": "#getapp@TMD@2025",
                "dataIv": "#getapp@TMD@2025",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "雄鹰",
            "name": "🌀雄鹰┃超清┃弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet2",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "http://122.228.193.2:9988",
                "dataKey": "ca94b06ca359d80e",
                "dataIv": "ca94b06ca359d80e",
                "deviceId": "",
                "version": "120"
            }
        },
        {
            "key": "榴莲",
            "name": "🌀榴莲┃超清┃弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet2",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "https://qjappcms.ll4k.xyz",
                "dataKey": "1yGA85sJ5STtE7uj",
                "dataIv": "1yGA85sJ5STtE7uj",
                "deviceId": "",
                "version": "50000"
            }
        },
        {
            "key": "蓝鹰",
            "name": "🌀蓝鹰┃超清┃弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet2",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "http://172.247.192.138:18520",
                "dataKey": "SuNlEkOLAoWJj1Oe",
                "dataIv": "SuNlEkOLAoWJj1Oe",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "仓鼠",
            "name": "🌀仓鼠┃超清┃弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet2",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "https://qjappcms.cs4k.top",
                "dataKey": "Z98KXaLtO2wC1Pte",
                "dataIv": "Z98KXaLtO2wC1Pte",
                "deviceId": "",
                "version": "120"
            }
        },
        {
            "key": "码头",
            "name": "🌀码头┃超清┃弹幕",
            "type": 3,
            "api": "csp_AppGet",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "https://www.lgmt.cc",
                "dataKey": "asfjaskasgggassf",
                "dataIv": "asfjaskasgggassf",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "米诺",
            "name": "🌀米诺┃超清┃弹幕",
            "type": 3,
            "api": "csp_AppGet",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "http://www.milkidc.cn",
                "dataKey": "20c79c979da8db0f",
                "dataIv": "20c79c979da8db0f",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "优质",
            "name": "🌀优质┃超清┃弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "https://apiapplbys.lbys.app:5678",
                "dataKey": "apiapplbyskey168",
                "dataIv": "apiapplbyskey168",
                "deviceId": "",
                "version": "107"
            }
        },
        {
            "key": "云速",
            "name": "🌀云速┃超清┃弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "",
                "site": "./json/1.json",
                "dataKey": "4d83b87c4c5ea111",
                "dataIv": "4d83b87c4c5ea111",
                "deviceId": "",
                "version": "105"
            }
        },
        {
            "key": "桃子",
            "name": "🌀桃子┃超清┃弹幕",
            "jar": "./jars/config.jar",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "ext": {
                "url": "https://appcms.tzys.xyz",
                "dataKey": "KL6vlZkw6WL5x90U",
                "dataIv": "KL6vlZkw6WL5x90U",
                "deviceId": "4b4c36766c5a6b7736574c3578393055",
                "version": "119"
            }
        },
        {
            "key": "海豚",
            "name": "🌀海豚┃超清┃弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "https://appcms.htsp4k.top",
                "dataKey": "7CYQQzwchRQpHCOj",
                "dataIv": "7CYQQzwchRQpHCOj",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "溜溜",
            "name": "🌀溜溜┃超清┃弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "https://appcms.ll4k.xyz",
                "dataKey": "NiDGaKiVnkO3QX1Q",
                "dataIv": "NiDGaKiVnkO3QX1Q",
                "deviceId": "2fbaf48ee97783260bc907e3ab0bd40c3",
                "version": "200"
            }
        },
        {
            "key": "瓜萌",
            "name": "🌀瓜萌┃超清┃弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "https://www.guahd.com",
                "dataKey": "f2A7D4B9E8C16531",
                "dataIv": "f2A7D4B9E8C16531",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "晴天",
            "name": "🌀晴天┃超清┃弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "https://qjappcms.sun4k.top",
                "dataKey": "sBxqXVF5pAHbGzrH",
                "dataIv": "sBxqXVF5pAHbGzrH",
                "deviceId": "",
                "version": "119"
            }
        },
        {
            "key": "再看",
            "name": "🌀在看┃超清┃弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "http://149.88.64.161:8627",
                "dataKey": "123456789ABCDEFG",
                "dataIv": "123456789ABCDEFG",
                "deviceId": "2bb4c10f3e043307dbfc579bd0db23f4e",
                "version": "110"
            }
        },
        {
            "key": "橘子",
            "name": "🌀橘子┃超清┃弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "https://api1.bffree.cn",
                "dataKey": "2015692015692015",
                "dataIv": "2015692015692015",
                "deviceId": "",
                "version": "300"
            }
        },
        {
            "key": "雨滴",
            "name": "🌀雨滴┃超清┃弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "https://c8w2ov7u5wg2z1o8p21c.aliyuncs.click:27899",
                "dataKey": "k9o3p2c8b7m3z0o8",
                "dataIv": "k9o3p2c8b7m3z0o8",
                "deviceId": "",
                "version": "100"
            }
        },
        {
            "key": "萝卜",
            "name": "🌀萝卜┃超清┃弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "https://apiapplbys.lbys.app:5678",
                "dataKey": "apiapplbyskey168",
                "dataIv": "apiapplbyskey168",
                "deviceId": "",
                "version": "107"
            }
        },
        {
            "key": "小红",
            "name": "🌀小红┃超清┃弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "https://www.xiaohys.com",
                "dataKey": "ENonBHeVBoYZhVUV",
                "dataIv": "ENonBHeVBoYZhVUV",
                "deviceId": "298e5fe29c74b35aabb9836ee2f6f449f",
                "version": "166"
            }
        },
        {
            "key": "在看",
            "name": "🌀小红┃超清┃弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "http://149.88.64.161:9525",
                "dataKey": "123456789ABCDEFG",
                "dataIv": "123456789ABCDEFG",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "若惜",
            "name": "🌀若惜┃超清┃弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "http://110.40.59.188:9527",
                "dataKey": "ebad3f1a58b13933",
                "dataIv": "ebad3f1a58b13933",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "外剧",
            "name": "🌀外剧┃超清┃弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "https://guowaiju.com",
                "dataKey": "7xv16h7qgkrs9b1p",
                "dataIv": "7xv16h7qgkrs9b1p",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "樱桃",
            "name": "🌀樱桃┃超清┃弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppMuou",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "http://sp.dxgsp.cc",
                "dataKey": "25f9e794323b4538",
                "dataIv": "25f9e794323b4538",
                "jxurl": "https://ap.dxgsp.cc"
            }
        },
        {
            "key": "趣看",
            "name": "🌀趣看┃超清┃弹幕",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppMuou",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "https://xjuzi.top",
                "dataKey": "6a482a70b80eefc9",
                "dataIv": "c995826a3e86fedd",
                "jxurl": "https://www.ququkan.cc"
            }
        },
        {
            "key": "玩偶哥哥",
            "name": "♨️玩偶备用┃4K弹幕",
            "type": 3,
            "api": "csp_PanWebShare",
            "jar": "./jars/config.jar",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/wogg.json"
        },
        {
            "key": "MoggV2",
            "name": "🍁优汐木偶┃4K弹幕",
            "type": 3,
            "api": "csp_PanWebShare",
            "jar": "./jars/config.jar",
            "searchable": 1,
            "quickSearch": 0,
            "filterable": 0,
            "ext": "./json/mogg.json"
        },
        {
            "key": "UcXmV2",
            "name": "🐂小二资源┃4K弹幕",
            "type": 3,
            "api": "csp_PanWebShare",
            "jar": "./jars/config.jar",
            "searchable": 1,
            "filterable": 0,
            "changeable": 0,
            "timeout": 60,
            "ext": "./json/ex.json"
        },
        {
            "key": "QuarkLaBiV2",
            "name": "🖍︎蜡笔资源┃4K弹幕",
            "type": 3,
            "api": "csp_PanWebShare",
            "jar": "./jars/config.jar",
            "searchable": 1,
            "filterable": 0,
            "changeable": 0,
            "timeout": 60,
            "ext": "./json/lb.json"
        },
        {
            "key": "QuarkzzV2",
            "name": "🏆️夸克至臻┃4K弹幕",
            "type": 3,
            "api": "csp_PanWebShare",
            "jar": "./jars/config.jar",
            "searchable": 1,
            "filterable": 0,
            "changeable": 0,
            "timeout": 60,
            "ext": "./json/zz.json"
        },
        {
            "key": "YYDSYS",
            "name": "🍡多多网盘┃4K弹幕",
            "type": 3,
            "api": "csp_PanWebShare",
            "jar": "./jars/config.jar",
            "searchable": 1,
            "filterable": 0,
            "changeable": 0,
            "timeout": 60,
            "ext": "./json/yyds.json"
        },
        {
            "key": "QuarkTZ",
            "name": "✊️团长网盘┃4K弹幕",
            "type": 3,
            "api": "csp_PanWebTz",
            "jar": "./jars/config.jar",
            "searchable": 1,
            "filterable": 0,
            "changeable": 0
        },
        {
            "key": "cloudLJ",
            "name": "🐋天翼雷鲸┃4K弹幕",
            "type": 3,
            "api": "csp_PanWebShareCloudLJ",
            "jar": "./jars/config.jar",
            "searchable": 0,
            "filterable": 0,
            "changeable": 0,
            "style": {
                "type": "rect",
                "ratio": 1.433
            },
            "ext": "./json/lj.json"
        },
        {
            "key": "趣盘",
            "name": "🥳百度趣盘┃4K弹幕",
            "type": 3,
            "api": "csp_PanWebQu",
            "jar": "./jars/config.jar",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "style": {
                "type": "list",
                "ratio": 1.433
            },
            "ext": {
                "url": "https://www.qupanshe.com"
            }
        },
        {
            "key": "盘库",
            "name": "🐼盘库资源┃4K弹幕",
            "type": 3,
            "api": "csp_PanWebKuBa",
            "jar": "./jars/config.jar",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "changeable": 1,
            "ext": {
                "url": "https://panku8.com,https://yipanso.com"
            }
        },
        {
            "key": "瓜子影视",
            "name": "🍉瓜子┃蓝光┃无广",
            "type": 3,
            "api": "csp_Gz360",
            "jar": "./jars/瓜子影视.jar",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "金牌影视",
            "name": "🏅金牌┃蓝光┃无广",
            "type": 3,
            "api": "./api/金牌影视.py",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "playerType": 2,
            "ext": {
                "site": "https://www.hkybqufgh.com,https://www.sizhengxt.com,https://0996zp.com,https://9zhoukj.com/,https://www.sizhengxt.com,https://www.tjrongze.com,https://www.jiabaide.cn,https://cqzuoer.com"
            }
        },
        {
            "key": "夕晨踏雪",
            "name": "♨️夕晨踏雪┃4K弹幕",
            "jar": "./jars/config.jar",
            "type": 3,
            "api": "csp_SP360"
        },
        {
            "key": "移动",
            "name": "🌀移动急速┃4K纯净",
            "type": 3,
            "api": "csp_YD",
            "searchable": 1,
            "quickSearch": 1
        },
        {
            "key": "alllive",
            "name": "📽️星河共影┃2K直播",
            "type": 3,
            "api": "csp_AllliveGuard",
            "playerType": 2,
            "searchable": 0,
            "quickSearch": 0,
            "changeable": 0
        },
        {
            "key": "抠搜",
            "name": "🍄抠抠搜搜┃网盘搜索",
            "type": 3,
            "api": "csp_KkSsGuard",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 0,
            "ext": {
                "Cloud-drive": "TVBox/Cloud-drive.txt",
                "from": "4k|auto"
            }
        },
        {
            "key": "UCsousou",
            "name": "🌈优汐搜搜┃网盘搜索",
            "type": 3,
            "api": "csp_UuSsGuard",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 0,
            "ext": {
                "Cloud-drive": "TVBox/Cloud-drive.txt",
                "from": "4k|auto"
            }
        },
        {
            "key": "厂长",
            "name": "📔厂长┃不卡┃纯净",
            "type": 3,
            "api": "csp_NewCzGuard",
            "timeout": 10,
            "playerType": 2,
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1
        },
        {
            "key": "贱贱",
            "name": "🐭荐片┃速览┃纯净",
            "type": 3,
            "api": "csp_JPJGuard",
            "playerType": 2,
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 0
        },
        {
            "key": "白白",
            "name": "🐭白白┃秒播┃纯净",
            "type": 3,
            "api": "csp_SbaibaiGuard",
            "playerType": 2,
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1
        },
        {
            "key": "原创",
            "name": "☀原创┃不卡┃纯净",
            "type": 3,
            "api": "csp_YCyzGuard",
            "timeout": 15,
            "playerType": 1,
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1
        },
        {
            "key": "苹果",
            "name": "🍎苹果┃不卡┃纯净",
            "type": 3,
            "api": "csp_LiteAppleGuard",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1
        },
        {
            "key": "糯米",
            "name": "🍓糯米┃秒播┃纯净",
            "type": 3,
            "api": "csp_NmyswvGuard",
            "timeout": 15,
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1
        },
        {
            "key": "文采",
            "name": "💮文采┃秒播┃纯净",
            "type": 3,
            "api": "csp_JpysGuard",
            "playerType": 2,
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1
        },
        {
            "key": "溢彩",
            "name": "💡溢彩┃秒播┃纯净",
            "type": 3,
            "api": "csp_AppSKGuard",
            "searchable": 1,
            "quickSearch": 0,
            "changeable": 0,
            "ext": "rfOb1uAWbkRHp7hdxprG9un3+TfN183v1zIyaYDoDAIaLw5L8Dp8+v88LrEL3dBzrmWbdMBX0WNm7HtkQuw0AIzUurGBVyPqCKzDmbriATuukhctJlsLo8KxCw=="
        },
        {
            "key": "Lib",
            "name": "🌟立播┃秒播┃纯净",
            "type": 3,
            "api": "csp_LibvioGuard",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "ext": {
                "Cloud-drive": "TVBox/Cloud-drive.txt",
                "from": "4k|auto"
            }
        },
        {
            "key": "zxzj",
            "name": "🍊在线┃外剧┃纯净",
            "type": 3,
            "api": "csp_Zxzj",
            "timeout": 15,
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "ext": "https://www.zxzjhd.com/"
        },
        {
            "key": "比特",
            "name": "🍄比特┃秒播┃纯净",
            "type": 3,
            "api": "csp_BttwooGuard",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1
        },
        {
            "key": "低端",
            "name": "⏮️低端┃外剧┃纯净",
            "type": 3,
            "api": "csp_DdrkGuard",
            "playerType": "2",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1
        },
        {
            "key": "萌米",
            "name": "👀萌米┃多线┃纯净",
            "type": 3,
            "api": "csp_AppTTGuard",
            "playerType": 2,
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "ext": "uqGL1bNENEIVq+dC1p/Y9uWjuA=="
        },
        {
            "key": "热播",
            "name": "📺热播┃多线┃纯净",
            "type": 3,
            "api": "csp_AppTTGuard",
            "playerType": 2,
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "ext": "uqGL1bNENExT7/hGxpSE5qU="
        },
        {
            "key": "兄弟",
            "name": "🍊兄弟┃多线┃纯净",
            "type": 3,
            "api": "csp_AppSxGuard",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "rfOb1uAWbkRHp7hdxprG9un3+SLP183q3ik3cJDiAwlFdF8L6SIvrvc9LrpTyIg76T7QJZdEkWNj43wiSaA0TJyQpu2IF2jsSLWFx7WkAmG40hFxJ1tI+Jf+EVG8DtoDRcNi+TtVGULnWrSz3EWnVcxR3EJhXnrwYWe1kJtNW5txuHAO"
        },
        {
            "key": "即看",
            "name": "🐻即看┃多线┃纯净",
            "type": 3,
            "api": "csp_AppSxGuard",
            "timeout": 10,
            "searchable": 1,
            "quickSearch": 0,
            "changeable": 0,
            "ext": "rfOX1voDIQhH8epBwtCFsub1+2maloq8lmJuL821WUsZJAZft2UtrrwhKK5Zxt1toWyFctBUmThhuDAjVuU="
        },
        {
            "key": "欢视",
            "name": "👓欢视┃多线┃纯净",
            "type": 3,
            "api": "csp_AppTTGuard",
            "playerType": 2,
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "ext": "uqGL1bNENExT9fFAy5mE5qU="
        },
        {
            "key": "Auete",
            "name": "🏝奥特┃无广┃纯净",
            "type": 3,
            "api": "csp_AueteGuard",
            "timeout": 15,
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 1,
            "ext": "https://auete.com/"
        },
        {
            "key": "新6V",
            "name": "🧲新6V┃磁力┃纯净",
            "type": 3,
            "api": "csp_SixVGuard",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 0,
            "ext": "https://www.xb6v.com/"
        },
        {
            "key": "926看球",
            "name": "⚽926┃看球┃直播",
            "type": 3,
            "api": "csp_kanqiu926Guard",
            "searchable": 0,
            "changeable": 0,
            "style": {
                "type": "list"
            }
        },
        {
            "key": "664K",
            "name": "🌀沐风┃蓝光┃无广",
            "type": 3,
            "api": "csp_XBPQ",
            "ext": "http://我不是.摸鱼儿.com/api/moyu.php?file=664k"
        },
        {
            "key": "4Kdy",
            "name": "🕊️凝安┃蓝光┃无广",
            "type": 3,
            "api": "csp_XBPQ",
            "ext": "http://我不是.摸鱼儿.com/api/moyu.php?file=4kdy"
        },
        {
            "key": "88js",
            "name": "⚽ 88┃看球┃直播",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/88看球.js",
            "style": {
                "type": "list"
            },
            "searchable": 0,
            "quickSearch": 0,
            "changeable": 0
        },
        {
            "key": "看球",
            "name": "⚽聚合┃看球┃直播",
            "type": 3,
            "api": "csp_KanqiuGuard",
            "timeout": 15,
            "searchable": 0,
            "changeable": 0,
            "style": {
                "type": "list"
            }
        },
        {
            "key": "MTV",
            "name": "🎧明星┃MV┃蓝光",
            "type": 3,
            "api": "csp_BiliGuard",
            "style": {
                "type": "rect",
                "ratio": 1.597
            },
            "searchable": 0,
            "quickSearch": 0,
            "changeable": 0,
            "ext": {
                "json": "https://im.feelec.com.cn/res/file.html?id=2c9a91099584ed490195f7bf50ac60b9"
            }
        },
        {
            "key": "虎牙直播js",
            "name": "🐯虎牙┃娱乐┃蓝光",
            "type": 3,
            "api": "./api/drpy2.js",
            "ext": "./js/huya2.js",
            "style": {
                "type": "rect",
                "ratio": 1.755
            },
            "timeout": 15,
            "playerType": "2",
            "searchable": 0,
            "quickSearch": 0,
            "changeable": 0
        },
        {
            "key": "斗鱼js",
            "name": "🐟斗鱼┃娱乐┃蓝光",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/斗鱼直播.js",
            "style": {
                "type": "rect",
                "ratio": 1.755
            },
            "timeout": 15,
            "playerType": "2",
            "searchable": 0,
            "quickSearch": 0,
            "changeable": 0
        },
        {
            "key": "有声小说js",
            "name": "📻️戏曲┃娱乐┃蓝光",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/有声小说吧.js",
            "style": {
                "type": "rect",
                "ratio": 1
            },
            "timeout": 15,
            "searchable": 0,
            "quickSearch": 0,
            "changeable": 0
        },
        {
            "key": "Aid",
            "name": "🚑️急救┃常识┃教育",
            "type": 3,
            "api": "csp_FirstAidGuard",
            "searchable": 0,
            "quickSearch": 0,
            "changeable": 0,
            "style": {
                "type": "rect",
                "ratio": 3.8
            }
        },
        {
            "key": "PanSso",
            "name": "🐌盘他盘他┃三盘搜索",
            "type": 3,
            "api": "csp_PanSsoGuard",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 0,
            "ext": {
                "Cloud-drive": "TVBox/Cloud-drive.txt",
                "from": "4k|auto"
            }
        },
        {
            "key": "YpanSo",
            "name": "🐟盘她盘她┃三盘搜索",
            "type": 3,
            "api": "csp_YpanSoGuard",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 0,
            "ext": {
                "Cloud-drive": "TVBox/Cloud-drive.txt",
                "from": "4k|auto"
            }
        },
        {
            "key": "xzso",
            "name": "👻盘它盘它┃三盘搜索",
            "type": 3,
            "api": "csp_xzsoGuard",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 0,
            "ext": {
                "Cloud-drive": "TVBox/Cloud-drive.txt",
                "from": "4k|auto"
            }
        },
        {
            "key": "米搜",
            "name": "🦋米盘搜搜┃网盘搜索",
            "type": 3,
            "api": "csp_MIPanSoGuard",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 0,
            "ext": {
                "Cloud-drive": "tvfan/Cloud-drive.txt",
                "from": "4k|auto"
            }
        },
        {
            "key": "夸搜",
            "name": "😻夸可搜搜┃网盘搜索",
            "type": 3,
            "api": "csp_PanSearchGuard",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 0,
            "ext": {
                "pan": "quark",
                "Cloud-drive": "tvfan/Cloud-drive.txt",
                "from": "4k|auto"
            }
        },
        {
            "key": "Aliso",
            "name": "🙀盘盘搜搜┃网盘搜索",
            "type": 3,
            "api": "csp_PanSearchGuard",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 0,
            "ext": {
                "Cloud-drive": "tvfan/Cloud-drive.txt",
                "from": "4k|auto"
            }
        },
        {
            "key": "YiSo",
            "name": "😹易一搜搜┃网盘搜索",
            "type": 3,
            "api": "csp_YiSoGuard",
            "searchable": 1,
            "quickSearch": 1,
            "changeable": 0,
            "ext": {
                "Cloud-drive": "tvfan/Cloud-drive.txt",
                "from": "4k|auto"
            }
        },
        {
            "key": "Bili",
            "name": "🅱哔哔合集┃4K弹幕",
            "type": 3,
            "api": "csp_BiliGuard",
            "style": {
                "type": "rect",
                "ratio": 1.597
            },
            "searchable": 1,
            "quickSearch": 0,
            "changeable": 0,
            "ext": {
                "json": "https://im.feelec.com.cn/res/file.html?id=2c9a91099293dce40194194e5a5f7474"
            }
        },
        {
            "key": "Biliych",
            "name": "🅱哔哔演唱会┃4K弹幕",
            "type": 3,
            "api": "csp_BiliGuard",
            "style": {
                "type": "rect",
                "ratio": 1.597
            },
            "searchable": 1,
            "quickSearch": 0,
            "changeable": 0,
            "ext": {
                "json": "https://im.feelec.com.cn/res/file.html?id=2c9a91099293dce40194194f4eb2747e"
            }
        },
        {
            "key": "dr_兔小贝",
            "name": "📚少儿┃兔兔┃教育",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/兔小贝.js",
            "style": {
                "type": "rect",
                "ratio": 1.597
            },
            "searchable": 0,
            "quickSearch": 0,
            "changeable": 0
        },
        {
            "key": "少儿教育",
            "name": "📚少儿┃学识┃教育",
            "type": 3,
            "api": "csp_BiliGuard",
            "style": {
                "type": "rect",
                "ratio": 1.597
            },
            "searchable": 0,
            "quickSearch": 0,
            "changeable": 0,
            "ext": {
                "json": "http://我不是.摸鱼儿.com/api/moyu.php?file=少儿教育"
            }
        },
        {
            "key": "小学课堂",
            "name": "📚小学┃课堂┃教育",
            "type": 3,
            "api": "csp_BiliGuard",
            "style": {
                "type": "rect",
                "ratio": 1.597
            },
            "searchable": 0,
            "quickSearch": 0,
            "changeable": 0,
            "ext": {
                "json": "http://我不是.摸鱼儿.com/api/moyu.php?file=小学教育"
            }
        },
        {
            "key": "初中课堂",
            "name": "📚初中┃课堂┃教育",
            "type": 3,
            "api": "csp_BiliGuard",
            "style": {
                "type": "rect",
                "ratio": 1.597
            },
            "searchable": 0,
            "quickSearch": 0,
            "changeable": 0,
            "ext": {
                "json": "http://我不是.摸鱼儿.com/api/moyu.php?file=初中课堂"
            }
        },
        {
            "key": "高中教育",
            "name": "📚高中┃课堂┃教育",
            "type": 3,
            "api": "csp_BiliGuard",
            "style": {
                "type": "rect",
                "ratio": 1.597
            },
            "searchable": 0,
            "quickSearch": 0,
            "changeable": 0,
            "ext": {
                "json": "http://我不是.摸鱼儿.com/api/moyu.php?file=高中课堂"
            }
        },
        {
            "key": "曼波动漫",
            "name": "🍼曼波┃动漫┃蓝光",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "https://app.omofun1.top",
                "dataKey": "66dc309cbeeca454",
                "dataIv": "66dc309cbeeca454",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "咕咕动漫",
            "name": "🍚咕咕┃动漫┃蓝光",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "https://www.gugu3.com",
                "dataKey": "nKfZ8KX6JTNWRzTD",
                "dataIv": "nKfZ8KX6JTNWRzTD",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "米饭动漫",
            "name": "🍙米饭┃动漫┃蓝光",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "http://45.43.29.111:9527",
                "dataKey": "GETMIFUNGEIMIFUN",
                "dataIv": "GETMIFUNGEIMIFUN",
                "deviceId": "",
                "version": ""
            }
        },
        {
            "key": "次元动漫",
            "name": "😋次元┃动漫┃蓝光",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "https://jjjj.nilinili.org",
                "dataKey": "672263e98f232a05",
                "dataIv": "672263e98f232a05",
                "deviceId": "",
                "version": "170"
            }
        },
        {
            "key": "黑猫动漫",
            "name": "🐈‍⬛黑猫┃动漫┃蓝光",
            "type": 3,
            "quickSearch": 1,
            "api": "csp_AppGet",
            "jar": "./jars/config.jar",
            "ext": {
                "url": "https://dm.xxdm123.top:9991",
                "dataKey": "0fe3b5781782c621",
                "dataIv": "0fe3b5781782c621",
                "deviceId": "",
                "version": "203"
            }
        },
        {
            "key": "樱花",
            "name": "🌸樱花┃动漫┃樱花",
            "type": 3,
            "api": "csp_XBPQ",
            "ext": "http://我不是.摸鱼儿.com/api/moyu.php?file=樱花动漫"
        },
        {
            "key": "巴士动漫",
            "name": "🚎巴士┃动漫┃樱花",
            "type": 3,
            "api": "csp_XYQHiker",
            "ext": "http://我不是.摸鱼儿.com/api/moyu.php?file=巴士"
        },
        {
            "key": "duanju",
            "name": "🌟星芽┃短剧┃热推",
            "type": 3,
            "api": "csp_AppXY",
            "jar": "./jars/config.jar",
            "searchable": 1,
            "quickSearch": 0,
            "filterable": 0
        },
        {
            "key": "河马短剧",
            "name": "🦛河马┃短剧┃热推",
            "type": 3,
            "api": "./api/河马短剧.py",
            "searchable": 1,
            "changeable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "playerType": 2
        },
        {
            "key": "偷乐短剧",
            "name": "🤣偷乐┃短剧┃热推",
            "type": 3,
            "api": "./api/偷乐短剧.py",
            "searchable": 1,
            "changeable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "playerType": 2
        },
        {
            "key": "QuarkYunPan",
            "name": "📁文件┃夸克┃资源",
            "type": 3,
            "api": "csp_PanQuark",
            "jar": "./jars/config.jar",
            "searchable": 0,
            "filterable": 0,
            "changeable": 0,
            "style": {
                "type": "list",
                "ratio": 1.433
            },
            "ext": "./json/quarkShare.json"
        },
        {
            "key": "AliYunPanV2",
            "name": "📁文件┃阿里┃资源",
            "type": 3,
            "api": "csp_PanAli",
            "jar": "./jars/config.jar",
            "searchable": 0,
            "filterable": 0,
            "changeable": 0,
            "style": {
                "type": "list",
                "ratio": 1.433
            },
            "ext": "./json/aliShare.json"
        },
        {
            "key": "UcYunPan",
            "name": "📁文件┃优视┃资源",
            "type": 3,
            "api": "csp_PanUc",
            "jar": "./jars/config.jar",
            "searchable": 0,
            "filterable": 0,
            "changeable": 0,
            "style": {
                "type": "list",
                "ratio": 1.433
            },
            "ext": "./json/ucShare.json"
        },
        {
            "key": "baidu",
            "name": "📁文件┃百度┃资源",
            "type": 3,
            "api": "csp_PanBaiDu",
            "jar": "./jars/config.jar",
            "searchable": 0,
            "filterable": 0,
            "changeable": 0,
            "style": {
                "type": "list",
                "ratio": 1.433
            }
        },
        {
            "key": "push_agent",
            "name": "🛴手机┃推送┃链接",
            "type": 3,
            "api": "csp_PushGuard",
            "searchable": 0,
            "quickSearch": 0,
            "ext": {
                "Cloud-drive": "tvfan/Cloud-drive.txt",
                "from": "4k|auto"
            }
        }
    ],
    "parses": [
        {
            "name": "聚合",
            "type": 3,
            "url": "Demo"
        }
    ],
    "doh": [
        {
            "name": "Google",
            "url": "https://dns.google/dns-query",
            "ips": [
                "*******",
                "*******"
            ]
        },
        {
            "name": "Cloudflare",
            "url": "https://cloudflare-dns.com/dns-query",
            "ips": [
                "*******",
                "*******",
                "2606:4700:4700::1111",
                "2606:4700:4700::1001"
            ]
        },
        {
            "name": "AdGuard",
            "url": "https://dns.adguard.com/dns-query",
            "ips": [
                "*************",
                "*************"
            ]
        },
        {
            "name": "DNSWatch",
            "url": "https://resolver2.dns.watch/dns-query",
            "ips": [
                "************",
                "************"
            ]
        },
        {
            "name": "Quad9",
            "url": "https://dns.quad9.net/dns-quer",
            "ips": [
                "*******",
                "***************"
            ]
        }
    ],
    "lives": [
        {
            "name": "【备用】全是气氛组",
            "type": 0,
            "ua": "okhttp/3.15"
        }
    ],
    "flags": [
        "youku",
        "优酷",
        "优 酷",
        "优酷视频",
        "qq",
        "腾讯",
        "腾 讯",
        "腾讯视频",
        "iqiyi",
        "qiyi",
        "奇艺",
        "爱奇艺",
        "爱 奇 艺",
        "m1905",
        "xigua",
        "letv",
        "leshi",
        "乐视",
        "乐 视",
        "sohu",
        "搜狐",
        "搜 狐",
        "搜狐视频",
        "tudou",
        "pptv",
        "mgtv",
        "芒果",
        "imgo",
        "芒果TV",
        "芒 果 T V",
        "bilibili",
        "哔 哩",
        "哔 哩 哔 哩"
    ],
    "ijk": [
        {
            "group": "软解码",
            "options": [
                {
                    "category": 4,
                    "name": "opensles",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "overlay-format",
                    "value": "842225234"
                },
                {
                    "category": 4,
                    "name": "framedrop",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "soundtouch",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "start-on-prepared",
                    "value": "1"
                },
                {
                    "category": 1,
                    "name": "http-detect-range-support",
                    "value": "0"
                },
                {
                    "category": 1,
                    "name": "fflags",
                    "value": "fastseek"
                },
                {
                    "category": 2,
                    "name": "skip_loop_filter",
                    "value": "48"
                },
                {
                    "category": 4,
                    "name": "reconnect",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "enable-accurate-seek",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "mediacodec",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "mediacodec-auto-rotate",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "mediacodec-handle-resolution-change",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "mediacodec-hevc",
                    "value": "0"
                },
                {
                    "category": 1,
                    "name": "dns_cache_timeout",
                    "value": "600000000"
                }
            ]
        },
        {
            "group": "硬解码",
            "options": [
                {
                    "category": 4,
                    "name": "opensles",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "overlay-format",
                    "value": "842225234"
                },
                {
                    "category": 4,
                    "name": "framedrop",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "soundtouch",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "start-on-prepared",
                    "value": "1"
                },
                {
                    "category": 1,
                    "name": "http-detect-range-support",
                    "value": "0"
                },
                {
                    "category": 1,
                    "name": "fflags",
                    "value": "fastseek"
                },
                {
                    "category": 2,
                    "name": "skip_loop_filter",
                    "value": "48"
                },
                {
                    "category": 4,
                    "name": "reconnect",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "enable-accurate-seek",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "mediacodec",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "mediacodec-auto-rotate",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "mediacodec-handle-resolution-change",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "mediacodec-hevc",
                    "value": "1"
                },
                {
                    "category": 1,
                    "name": "dns_cache_timeout",
                    "value": "600000000"
                }
            ]
        }
    ],
    "ads": [
        "static-mozai.4gtv.tv"
    ]
}