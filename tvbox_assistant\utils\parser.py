#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
TVBox配置解析器
解析TVBox配置文件，识别和处理各种格式的接口
"""

import json
import os
import re
import logging
from urllib.parse import urlparse

class TVBoxParser:
    """TVBox配置文件解析器"""
    
    # 需要处理的资源类型
    RESOURCE_TYPES = [
        'spider',    # 爬虫规则
        'lives',     # 直播源
        'wallpaper', # 壁纸
        'sites',     # 站点配置
        'parses',    # 解析器
        'ijk',       # 播放器配置
        'ads',       # 广告过滤
        'rules',     # 规则文件
        'doh'        # DNS over HTTPS
    ]
    
    # 可能包含资源URL的字段
    URL_FIELDS = [
        'api', 'ext', 'jar', 'playerUrl', 'url', 'playUrl', 
        'parse', 'json', 'file', 'logo', 'epg', 'ua',
        'webPlus', 'webPic', 'click', 'header', 'proxy'
    ]
    
    # 可能含有加密内容的字段
    ENCRYPTED_FIELDS = [
        'api', 'ext', 'url', 'jar', 'parse', 'json'
    ]
    
    def __init__(self):
        """初始化解析器"""
        self.resources = {}  # 资源映射 {url: {type, path, encrypted}}
        self.logger = logging.getLogger('tvbox_parser')
        
    def parse_config(self, config_path):
        """
        解析配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            dict: 包含解析结果的字典 {config, resources}
        """
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 尝试修复非法JSON格式
            content = self._fix_json(content)
            
            # 解析JSON
            config = json.loads(content)
            
            self.logger.info(f"开始解析配置文件: {config_path}")
            self.resources = {}
            
            # 处理顶级资源字段
            for res_type in self.RESOURCE_TYPES:
                if res_type in config:
                    self._process_field(config[res_type], [], res_type, res_type)
            
            self.logger.info(f"配置解析完成，发现 {len(self.resources)} 个远程资源")
            
            return {
                'config': config,
                'resources': self.resources
            }
        except Exception as e:
            self.logger.error(f"解析配置文件失败: {str(e)}")
            raise
    
    def _process_field(self, value, path, field_name, res_type):
        """
        递归处理配置字段
        
        Args:
            value: 字段值
            path: 字段路径
            field_name: 字段名称
            res_type: 资源类型
        """
        if isinstance(value, str) and self._is_remote_url(value):
            # URL字符串
            is_encrypted = field_name in self.ENCRYPTED_FIELDS and self._may_be_encrypted(value)
            self.resources[value] = {
                'type': res_type,
                'path': path,
                'field': field_name,
                'encrypted': is_encrypted
            }
        elif isinstance(value, list):
            # 列表类型
            for i, item in enumerate(value):
                item_path = path + [i]
                if isinstance(item, str) and self._is_remote_url(item):
                    is_encrypted = field_name in self.ENCRYPTED_FIELDS and self._may_be_encrypted(item)
                    self.resources[item] = {
                        'type': res_type,
                        'path': item_path,
                        'field': 'item',
                        'encrypted': is_encrypted
                    }
                elif isinstance(item, (dict, list)):
                    self._process_value(item, item_path, res_type)
        elif isinstance(value, dict):
            # 字典类型
            self._process_value(value, path, res_type)
    
    def _process_value(self, value, path, res_type):
        """
        处理复杂值类型
        
        Args:
            value: 值对象
            path: 路径
            res_type: 资源类型
        """
        if isinstance(value, dict):
            for k, v in value.items():
                if k in self.URL_FIELDS and isinstance(v, str) and self._is_remote_url(v):
                    is_encrypted = k in self.ENCRYPTED_FIELDS and self._may_be_encrypted(v)
                    self.resources[v] = {
                        'type': res_type,
                        'path': path + [k],
                        'field': k,
                        'encrypted': is_encrypted
                    }
                elif isinstance(v, (dict, list)):
                    self._process_field(v, path + [k], k, res_type)
        elif isinstance(value, list):
            for i, item in enumerate(value):
                self._process_field(item, path + [i], 'item', res_type)

    def _is_remote_url(self, url):
        """
        检查是否是远程URL
        
        Args:
            url: URL字符串
            
        Returns:
            bool: 是否是远程URL
        """
        if not url or not isinstance(url, str):
            return False
        return url.startswith('http://') or url.startswith('https://') or url.startswith('clan://') or '://' in url
    
    def _may_be_encrypted(self, url):
        """
        判断URL是否可能被加密
        
        Args:
            url: URL字符串
            
        Returns:
            bool: 是否可能被加密
        """
        # 识别可能的加密格式
        # 1. Base64: 以"="结尾且包含大量字母和数字
        if url.endswith('=') and re.match(r'^[A-Za-z0-9+/=]+$', url):
            return True
        
        # 2. 混淆的URL: 缺少协议部分但包含大量特殊字符
        if not url.startswith(('http://', 'https://')) and re.search(r'[;,\{\}\[\]\\]', url):
            return True
        
        # 3. 检查特殊格式的加密字符串
        if re.match(r'^[A-Za-z0-9+/]{20,}$', url):
            return True
        
        # 简单URL解析检查
        try:
            parsed = urlparse(url)
            # 正常URL应该有netloc(域名)
            if not parsed.netloc and parsed.path:
                return True
        except:
            return True
            
        return False

    def _fix_json(self, content):
        """
        修复可能的非法JSON格式
        
        Args:
            content: JSON内容字符串
            
        Returns:
            str: 修复后的JSON字符串
        """
        # 处理注释行
        content = re.sub(r'//.*?$', '', content, flags=re.MULTILINE)
        # 处理多行注释
        content = re.sub(r'/\*.*?\*/', '', content, flags=re.DOTALL)
        # 处理可能的尾部逗号
        content = re.sub(r',(\s*[\]}])', r'\1', content)
        return content

    def extract_resource_info(self, url, resource_info):
        """
        提取资源信息
        
        Args:
            url: 资源URL
            resource_info: 资源信息
            
        Returns:
            dict: 资源详细信息
        """
        file_info = {}
        file_info['url'] = url
        file_info['type'] = resource_info['type']
        file_info['encrypted'] = resource_info['encrypted']
        
        # 尝试从URL中提取文件名和扩展名
        try:
            parsed = urlparse(url)
            path = parsed.path
            if path:
                basename = os.path.basename(path)
                file_info['name'] = basename
                _, ext = os.path.splitext(basename)
                file_info['ext'] = ext.lstrip('.').lower() if ext else None
            else:
                file_info['name'] = f"resource_{resource_info['type']}"
                file_info['ext'] = None
        except:
            file_info['name'] = f"resource_{resource_info['type']}"
            file_info['ext'] = None
        
        return file_info 