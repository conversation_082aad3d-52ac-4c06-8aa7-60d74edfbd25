{"version": 3, "file": "browser.mjs", "sources": ["../../../../packages/utils/browser.ts"], "sourcesContent": ["import { isClient, isIOS } from '@vueuse/core'\n\nexport const isFirefox = (): boolean =>\n  isClient && /firefox/i.test(window.navigator.userAgent)\n\nexport { isClient, isIOS }\n"], "names": [], "mappings": ";;;AACY,MAAC,SAAS,GAAG,MAAM,QAAQ,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS;;;;"}