{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/pagination/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Pagination from './src/pagination'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElPagination: SFCWithInstall<typeof Pagination> =\n  withInstall(Pagination)\nexport default ElPagination\n\nexport * from './src/pagination'\nexport * from './src/constants'\n"], "names": [], "mappings": ";;;;;AAEY,MAAC,YAAY,GAAG,WAAW,CAAC,UAAU;;;;"}