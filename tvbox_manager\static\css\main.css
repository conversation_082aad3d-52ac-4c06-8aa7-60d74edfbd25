/* TVBox Manager - 主样式文件 */
/* 作者: <PERSON> Assistant */
/* 版本: 1.0.0 */

/* 导入字体 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* ============ 全局样式 ============ */
:root {
  /* 主题色 */
  --primary-50: #eef2ff;
  --primary-100: #e0e7ff;
  --primary-200: #c7d2fe;
  --primary-300: #a5b4fc;
  --primary-400: #818cf8;
  --primary-500: #6366f1;
  --primary-600: #4f46e5;
  --primary-700: #4338ca;
  --primary-800: #3730a3;
  --primary-900: #312e81;
  
  /* 中性色 */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  /* 功能色 */
  --success-light: #d1fae5;
  --success: #10b981;
  --success-dark: #047857;
  
  --warning-light: #fef3c7;
  --warning: #f59e0b;
  --warning-dark: #b45309;
  
  --danger-light: #fee2e2;
  --danger: #ef4444;
  --danger-dark: #b91c1c;
  
  --info-light: #dbeafe;
  --info: #3b82f6;
  --info-dark: #1d4ed8;
  
  /* 间距 */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  
  /* 圆角 */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-full: 9999px;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 10px 15px -3px rgba(0, 0, 0, 0.05);
  
  /* 过渡 */
  --transition-fast: 150ms;
  --transition-normal: 300ms;
  --transition-slow: 500ms;
  
  /* Z-index 层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  
  /* 侧边栏宽度 */
  --sidebar-width: 180px;
  --sidebar-collapsed-width: 50px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  height: 100%;
  font-size: 16px;
}

body {
  height: 100%;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-weight: 400;
  line-height: 1.5;
  color: var(--gray-700);
  background-color: var(--gray-50);
  overflow-x: hidden;
}

a {
  text-decoration: none;
  color: var(--primary-600);
  transition: color var(--transition-fast) ease-in-out;
}

a:hover {
  color: var(--primary-700);
}

/* ============ 布局组件 ============ */

/* 应用主容器 */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100%;
}

/* 主内容区域 */
.main-content {
  display: flex;
  flex: 1;
  height: calc(100vh - 60px); /* 减去顶部导航高度 */
}

/* 侧边栏 */
.sidebar {
  width: var(--sidebar-width);
  background: var(--gray-50);
  border-right: 1px solid var(--gray-100);
  box-shadow: none;
  overflow-y: auto;
  transition: width var(--transition-normal) ease-in-out;
  z-index: var(--z-fixed);
}

.sidebar-content {
  padding: var(--space-md) 0;
}

/* 侧边栏收起状态 */
.sidebar-collapsed {
  width: var(--sidebar-collapsed-width);
}

.sidebar-collapsed .sidebar-item-text,
.sidebar-collapsed .sidebar-header-text,
.sidebar-collapsed .sidebar-section-title {
  display: none;
}

.sidebar-collapsed .sidebar-item-icon {
  margin-right: 0;
}

.sidebar-collapsed .sidebar-item {
  padding: var(--space-sm);
  justify-content: center;
}

/* 工作区 */
.workspace {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-lg);
  background-color: var(--gray-50);
  transition: margin-left var(--transition-normal) ease-in-out;
}

/* 页头 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-lg);
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--gray-900);
}

/* 卡片 */
.card {
  background: white;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--space-lg);
  overflow: hidden;
  transition: box-shadow var(--transition-fast) ease-in-out, transform var(--transition-fast) ease-in-out;
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  padding: var(--space-md) var(--space-lg);
  border-bottom: 1px solid var(--gray-100);
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--gray-800);
  margin: 0;
}

.card-body {
  padding: var(--space-lg);
}

.card-footer {
  padding: var(--space-lg);
  border-top: 1px solid var(--gray-100);
  background-color: white;
}

/* ============ 导航组件 ============ */

/* 顶部导航 */
.navbar {
  height: 60px;
  background-color: white;
  border-bottom: 1px solid var(--gray-100);
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  padding: 0 var(--space-lg);
  z-index: var(--z-sticky);
  position: sticky;
  top: 0;
}

.navbar-brand {
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 1.125rem;
  color: var(--primary-700);
}

.navbar-logo {
  height: 28px;
  width: auto;
  margin-right: var(--space-md);
}

.navbar-menu {
  margin-left: auto;
  display: flex;
  align-items: center;
}

/* 用户菜单 */
.user-menu {
  position: relative;
}

.user-menu-button {
  display: flex;
  align-items: center;
  padding: var(--space-xs) var(--space-sm);
  background-color: var(--gray-50);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: background-color var(--transition-fast) ease-in-out;
}

.user-menu-button:hover {
  background-color: var(--gray-100);
}

.user-avatar {
  height: 30px;
  width: 30px;
  border-radius: 50%;
  background-color: var(--primary-100);
  color: var(--primary-700);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: var(--space-sm);
}

.user-info {
  margin-right: var(--space-sm);
}

.user-name {
  font-weight: 500;
  color: var(--gray-800);
  font-size: 0.875rem;
}

.user-email {
  font-size: 0.75rem;
  color: var(--gray-500);
}

.user-menu-dropdown {
  position: absolute;
  top: calc(100% + 4px);
  right: 0;
  width: 200px;
  background-color: white;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  z-index: var(--z-dropdown);
  border: 1px solid var(--gray-100);
  display: none;
}

.user-menu-dropdown.show {
  display: block;
  animation: slideDown 0.2s ease-out;
}

.user-menu-item {
  display: flex;
  align-items: center;
  padding: var(--space-md);
  color: var(--gray-700);
  transition: background-color var(--transition-fast) ease-in-out;
}

.user-menu-item:hover {
  background-color: var(--gray-50);
  color: var(--gray-900);
}

.user-menu-icon {
  margin-right: var(--space-md);
  color: var(--gray-500);
}

/* 侧边栏导航 */
.sidebar-header {
  padding: var(--space-sm) var(--space-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--gray-100);
  margin-bottom: var(--space-sm);
}

.sidebar-header-text {
  font-weight: 500;
  font-size: 0.875rem;
  color: var(--gray-500);
}

.sidebar-toggle {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--gray-400);
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.sidebar-toggle:hover {
  color: var(--gray-600);
}

.sidebar-toggle:focus {
  outline: none;
}

.sidebar-section {
  margin-top: var(--space-sm);
  padding: 0 var(--space-xs);
}

.sidebar-section-title {
  padding: var(--space-xs) var(--space-md);
  color: var(--gray-500);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.sidebar-item {
  display: flex;
  align-items: center;
  padding: var(--space-sm) var(--space-md);
  color: var(--gray-600);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-xs);
  transition: background-color var(--transition-fast) ease-in-out, 
              color var(--transition-fast) ease-in-out;
}

.sidebar-item:hover {
  background-color: var(--gray-100);
  color: var(--gray-800);
}

.sidebar-item.active {
  background-color: var(--primary-50);
  color: var(--primary-700);
  font-weight: 500;
  border-left: 2px solid var(--primary-500);
}

.sidebar-item-icon {
  margin-right: var(--space-md);
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 移动端菜单 */
.mobile-toggle {
  display: none;
  background: none;
  border: none;
  color: var(--gray-700);
  font-size: 1.25rem;
  cursor: pointer;
}

/* ============ 表单组件 ============ */
.form-group {
  margin-bottom: var(--space-lg);
}

.form-label {
  display: block;
  margin-bottom: var(--space-sm);
  font-weight: 500;
  color: var(--gray-700);
}

.form-control {
  width: 100%;
  padding: var(--space-sm) var(--space-md);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  background-color: white;
  color: var(--gray-700);
  transition: border-color var(--transition-fast) ease-in-out, 
              box-shadow var(--transition-fast) ease-in-out;
}

.form-control:focus {
  border-color: var(--primary-400);
  outline: none;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}

.form-control::placeholder {
  color: var(--gray-400);
}

.form-check {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-sm);
}

.form-check-input {
  margin-right: var(--space-sm);
}

.form-text {
  display: block;
  margin-top: var(--space-xs);
  font-size: 0.875rem;
  color: var(--gray-500);
}

.form-error {
  color: var(--danger);
  font-size: 0.875rem;
  margin-top: var(--space-xs);
}

/* ============ 按钮组件 ============ */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-sm) var(--space-lg);
  font-weight: 500;
  border-radius: var(--radius-md);
  border: 1px solid transparent;
  cursor: pointer;
  transition: background-color var(--transition-fast) ease-in-out,
              border-color var(--transition-fast) ease-in-out,
              color var(--transition-fast) ease-in-out,
              box-shadow var(--transition-fast) ease-in-out;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}

.btn-icon {
  margin-right: var(--space-sm);
}

/* 主要按钮 */
.btn-primary {
  background-color: var(--primary-600);
  color: white;
  border-color: var(--primary-600);
}

.btn-primary:hover {
  background-color: var(--primary-700);
  border-color: var(--primary-700);
}

/* 次要按钮 */
.btn-secondary {
  background-color: white;
  color: var(--gray-700);
  border-color: var(--gray-300);
}

.btn-secondary:hover {
  background-color: var(--gray-100);
  color: var(--gray-800);
}

/* 成功按钮 */
.btn-success {
  background-color: var(--success);
  color: white;
  border-color: var(--success);
}

.btn-success:hover {
  background-color: var(--success-dark);
  border-color: var(--success-dark);
}

/* 危险按钮 */
.btn-danger {
  background-color: var(--danger);
  color: white;
  border-color: var(--danger);
}

.btn-danger:hover {
  background-color: var(--danger-dark);
  border-color: var(--danger-dark);
}

/* 警告按钮 */
.btn-warning {
  background-color: var(--warning);
  color: white;
  border-color: var(--warning);
}

.btn-warning:hover {
  background-color: var(--warning-dark);
  border-color: var(--warning-dark);
}

/* 信息按钮 */
.btn-info {
  background-color: var(--info);
  color: white;
  border-color: var(--info);
}

.btn-info:hover {
  background-color: var(--info-dark);
  border-color: var(--info-dark);
}

/* 链接按钮 */
.btn-link {
  background-color: transparent;
  color: var(--primary-600);
  border-color: transparent;
  padding-left: 0;
  padding-right: 0;
}

.btn-link:hover {
  color: var(--primary-700);
  text-decoration: underline;
}

/* 按钮大小 */
.btn-sm {
  padding: var(--space-xs) var(--space-md);
  font-size: 0.875rem;
}

.btn-lg {
  padding: var(--space-md) var(--space-lg);
  font-size: 1.125rem;
}

/* 块级按钮 */
.btn-block {
  display: flex;
  width: 100%;
}

/* ============ 表格组件 ============ */
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  margin-bottom: var(--space-lg);
}

.table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
}

.table th,
.table td {
  padding: var(--space-md);
  vertical-align: middle;
  text-align: left;
}

.table thead th {
  background-color: var(--gray-50);
  color: var(--gray-700);
  font-weight: 500;
  border-bottom: 1px solid var(--gray-200);
  white-space: nowrap;
}

.table tbody tr {
  border-bottom: 1px solid var(--gray-100);
  transition: background-color var(--transition-fast) ease-in-out;
}

.table tbody tr:hover {
  background-color: var(--gray-50);
}

.table tbody tr:last-child {
  border-bottom: none;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: var(--gray-50);
}

.table-striped tbody tr:nth-of-type(odd):hover {
  background-color: var(--gray-100);
}

.table-bordered {
  border: 1px solid var(--gray-200);
}

.table-bordered th,
.table-bordered td {
  border: 1px solid var(--gray-200);
}

/* ============ 徽章组件 ============ */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-xs) var(--space-sm);
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: var(--radius-full);
}

.badge-primary {
  background-color: var(--primary-50);
  color: var(--primary-700);
}

.badge-success {
  background-color: var(--success-light);
  color: var(--success-dark);
}

.badge-warning {
  background-color: var(--warning-light);
  color: var(--warning-dark);
}

.badge-danger {
  background-color: var(--danger-light);
  color: var(--danger-dark);
}

.badge-info {
  background-color: var(--info-light);
  color: var(--info-dark);
}

/* ============ 警告提示组件 ============ */
.alert {
  padding: var(--space-md);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-lg);
  display: flex;
  align-items: flex-start;
}

.alert-icon {
  margin-right: var(--space-md);
  font-size: 1.25rem;
  display: flex;
  align-items: center;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: 600;
  margin-bottom: var(--space-xs);
}

.alert-close {
  background: none;
  border: none;
  color: inherit;
  opacity: 0.7;
  cursor: pointer;
  margin-left: var(--space-md);
  font-size: 1.25rem;
}

.alert-close:hover {
  opacity: 1;
}

.alert-success {
  background-color: var(--success-light);
  color: var(--success-dark);
  border-left: 3px solid var(--success);
}

.alert-warning {
  background-color: var(--warning-light);
  color: var(--warning-dark);
  border-left: 3px solid var(--warning);
}

.alert-danger {
  background-color: var(--danger-light);
  color: var(--danger-dark);
  border-left: 3px solid var(--danger);
}

.alert-info {
  background-color: var(--info-light);
  color: var(--info-dark);
  border-left: 3px solid var(--info);
}

/* ============ 状态指示器 ============ */
.status {
  display: inline-flex;
  align-items: center;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
  margin-right: var(--space-sm);
}

.status-active .status-indicator {
  background-color: var(--success);
  box-shadow: 0 0 0 2px var(--success-light);
}

.status-inactive .status-indicator {
  background-color: var(--gray-400);
  box-shadow: 0 0 0 2px var(--gray-200);
}

.status-warning .status-indicator {
  background-color: var(--warning);
  box-shadow: 0 0 0 2px var(--warning-light);
}

.status-danger .status-indicator {
  background-color: var(--danger);
  box-shadow: 0 0 0 2px var(--danger-light);
}

/* ============ 空状态 ============ */
.empty-state {
  text-align: center;
  padding: var(--space-xl) var(--space-lg);
}

.empty-state-icon {
  font-size: 3rem;
  color: var(--gray-300);
  margin-bottom: var(--space-lg);
}

.empty-state-title {
  font-size: 1.25rem;
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: var(--space-md);
}

.empty-state-description {
  color: var(--gray-500);
  margin-bottom: var(--space-lg);
  max-width: 450px;
  margin-left: auto;
  margin-right: auto;
}

/* ============ 动画 ============ */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideDown {
  from { transform: translateY(-10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.fade-in {
  animation: fadeIn var(--transition-normal) ease-in-out;
}

/* ============ 响应式 ============ */
@media (max-width: 1024px) {
  .workspace {
    padding: var(--space-md);
  }
}

@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    top: 60px;
    left: calc(-1 * var(--sidebar-width));
    height: calc(100% - 60px);
    transition: left var(--transition-normal) ease-in-out;
  }
  
  .sidebar.show {
    left: 0;
    z-index: 1050;
  }
  
  .workspace {
    padding: var(--space-md);
    margin-left: 0 !important;
  }
  
  .mobile-toggle {
    display: block;
    margin-right: var(--space-md);
  }
  
  .navbar-brand {
    font-size: 1rem;
  }
  
  .user-info {
    display: none;
  }
  
  .table-responsive {
    margin-left: -1rem;
    margin-right: -1rem;
    width: calc(100% + 2rem);
  }
}

@media (max-width: 576px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .page-header .btn {
    margin-top: var(--space-md);
    width: 100%;
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .card-header .btn {
    margin-top: var(--space-md);
    width: 100%;
  }
}

/* ============ 页脚样式 ============ */
.footer {
  background-color: white;
  border-top: 1px solid var(--gray-100);
  padding: var(--space-md);
  margin-top: auto;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.footer-copyright {
  color: var(--gray-500);
  font-size: 0.875rem;
}

.footer-info {
  color: var(--gray-400);
  font-size: 0.875rem;
}

/* 闪烁信息列表 */
.flash-messages {
  margin-bottom: var(--space-lg);
}

/* 移动菜单遮罩层 */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 1040;
  opacity: 0;
  pointer-events: none;
  transition: opacity var(--transition-normal) ease-in-out;
}

.mobile-overlay.show {
  opacity: 1;
  pointer-events: auto;
}

/* 间距工具类 */
.mr-2 {
  margin-right: 0.5rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.ml-3 {
  margin-left: 0.75rem;
}

/* 表格容器 */
.table-container {
  background-color: white;
  border-radius: var(--radius-md);
  overflow: hidden;
  margin-bottom: var(--space-lg);
}

/* 操作按钮组 */
.action-buttons {
  display: flex;
  gap: var(--space-sm);
}

.action-btn {
  padding: var(--space-xs);
  border-radius: var(--radius-md);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
}

.action-btn-edit {
  color: var(--info);
}

.action-btn-edit:hover {
  color: var(--info-dark);
  background-color: var(--info-light);
}

.action-btn-refresh {
  color: var(--success);
}

.action-btn-refresh:hover {
  color: var(--success-dark);
  background-color: var(--success-light);
}

.action-btn-delete {
  color: var(--danger);
}

.action-btn-delete:hover {
  color: var(--danger-dark);
  background-color: var(--danger-light);
}

/* 状态标签 */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge i {
  margin-right: var(--space-xs);
}

.status-badge-active {
  background-color: var(--success-light);
  color: var(--success-dark);
}

.status-badge-inactive {
  background-color: var(--gray-200);
  color: var(--gray-600);
}

/* ============ 通知组件 ============ */
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: var(--z-tooltip);
  width: 320px;
  max-width: calc(100% - 40px);
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.notification {
  background-color: white;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  padding: var(--space-md);
  display: flex;
  align-items: flex-start;
  transform: translateX(100%);
  opacity: 0;
  transition: transform var(--transition-normal) ease-out, 
              opacity var(--transition-normal) ease-out;
}

.notification.show {
  transform: translateX(0);
  opacity: 1;
}

.notification-icon {
  margin-right: var(--space-md);
  font-size: 1.25rem;
  display: flex;
  align-items: center;
}

.notification-content {
  flex: 1;
  font-size: 0.875rem;
}

.notification-close {
  background: none;
  border: none;
  color: var(--gray-500);
  cursor: pointer;
  margin-left: var(--space-md);
  padding: 0;
  font-size: 1rem;
}

.notification-close:hover {
  opacity: 1;
}

.notification-success {
  border-left: 3px solid var(--success);
}

.notification-success .notification-icon {
  color: var(--success);
}

.notification-warning {
  border-left: 3px solid var(--warning);
}

.notification-warning .notification-icon {
  color: var(--warning);
}

.notification-danger {
  border-left: 3px solid var(--danger);
}

.notification-danger .notification-icon {
  color: var(--danger);
}

.notification-info {
  border-left: 3px solid var(--info);
}

.notification-info .notification-icon {
  color: var(--info);
}

/* ============ 工具提示 ============ */
.tooltip {
  position: absolute;
  background-color: var(--gray-800);
  color: white;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  z-index: var(--z-tooltip);
  max-width: 200px;
  text-align: center;
  pointer-events: none;
  opacity: 0;
  transform: translateY(5px);
  transition: opacity var(--transition-fast) ease-out, 
              transform var(--transition-fast) ease-out;
  box-shadow: var(--shadow-md);
}

.tooltip::after {
  content: '';
  position: absolute;
  left: 50%;
  bottom: -5px;
  transform: translateX(-50%);
  border-width: 5px 5px 0;
  border-style: solid;
  border-color: var(--gray-800) transparent transparent transparent;
}

.tooltip.show {
  opacity: 1;
  transform: translateY(0);
}

/* 表单控件错误状态 */
.form-control-error {
  border-color: var(--danger);
}

.form-control-error:focus {
  border-color: var(--danger);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
}

/* 加载中状态 */
.loading {
  display: inline-block;
  position: relative;
  width: 16px;
  height: 16px;
}

.loading:after {
  content: " ";
  display: block;
  width: 12px;
  height: 12px;
  margin: 2px;
  border-radius: 50%;
  border: 2px solid currentColor;
  border-color: currentColor transparent currentColor transparent;
  animation: loading-ring 1.2s linear infinite;
}

@keyframes loading-ring {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 文本截断 */
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 显示/隐藏 */
.d-none {
  display: none !important;
}

.d-inline-block {
  display: inline-block !important;
}

.d-block {
  display: block !important;
}

.d-flex {
  display: flex !important;
}

/* 字体权重 */
.font-weight-medium {
  font-weight: 500 !important;
}

.font-weight-semibold {
  font-weight: 600 !important;
} 

.font-weight-semibold {
  font-weight: 600 !important;
} 