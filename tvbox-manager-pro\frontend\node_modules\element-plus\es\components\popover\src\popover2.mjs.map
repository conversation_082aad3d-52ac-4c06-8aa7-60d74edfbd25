{"version": 3, "file": "popover2.mjs", "sources": ["../../../../../../packages/components/popover/src/popover.vue"], "sourcesContent": ["<template>\n  <el-tooltip\n    ref=\"tooltipRef\"\n    v-bind=\"$attrs\"\n    :trigger=\"trigger\"\n    :trigger-keys=\"triggerKeys\"\n    :placement=\"placement\"\n    :disabled=\"disabled\"\n    :visible=\"visible\"\n    :transition=\"transition\"\n    :popper-options=\"popperOptions\"\n    :tabindex=\"tabindex\"\n    :content=\"content\"\n    :offset=\"offset\"\n    :show-after=\"showAfter\"\n    :hide-after=\"hideAfter\"\n    :auto-close=\"autoClose\"\n    :show-arrow=\"showArrow\"\n    :aria-label=\"title\"\n    :effect=\"effect\"\n    :enterable=\"enterable\"\n    :popper-class=\"kls\"\n    :popper-style=\"style\"\n    :teleported=\"teleported\"\n    :append-to=\"appendTo\"\n    :persistent=\"persistent\"\n    :gpu-acceleration=\"gpuAcceleration\"\n    @update:visible=\"onUpdateVisible\"\n    @before-show=\"beforeEnter\"\n    @before-hide=\"beforeLeave\"\n    @show=\"afterEnter\"\n    @hide=\"afterLeave\"\n  >\n    <template v-if=\"$slots.reference\">\n      <slot name=\"reference\" />\n    </template>\n\n    <template #content>\n      <div v-if=\"title\" :class=\"ns.e('title')\" role=\"title\">\n        {{ title }}\n      </div>\n      <slot>\n        {{ content }}\n      </slot>\n    </template>\n  </el-tooltip>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, ref, unref } from 'vue'\nimport { ElTooltip } from '@element-plus/components/tooltip'\nimport { addUnit } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { popoverEmits, popoverProps } from './popover'\n\nimport type { TooltipInstance } from '@element-plus/components/tooltip'\n\ndefineOptions({\n  name: 'ElPopover',\n})\n\nconst props = defineProps(popoverProps)\nconst emit = defineEmits(popoverEmits)\n\nconst updateEventKeyRaw = `onUpdate:visible` as const\n\nconst onUpdateVisible = computed(() => {\n  return props[updateEventKeyRaw]\n})\n\nconst ns = useNamespace('popover')\nconst tooltipRef = ref<TooltipInstance>()\nconst popperRef = computed(() => {\n  return unref(tooltipRef)?.popperRef\n})\n\nconst style = computed(() => {\n  return [\n    {\n      width: addUnit(props.width),\n    },\n    props.popperStyle!,\n  ]\n})\n\nconst kls = computed(() => {\n  return [ns.b(), props.popperClass!, { [ns.m('plain')]: !!props.content }]\n})\n\nconst gpuAcceleration = computed(() => {\n  return props.transition === `${ns.namespace.value}-fade-in-linear`\n})\n\nconst hide = () => {\n  tooltipRef.value?.hide()\n}\n\nconst beforeEnter = () => {\n  emit('before-enter')\n}\nconst beforeLeave = () => {\n  emit('before-leave')\n}\n\nconst afterEnter = () => {\n  emit('after-enter')\n}\n\nconst afterLeave = () => {\n  emit('update:visible', false)\n  emit('after-leave')\n}\n\ndefineExpose({\n  /** @description popper ref */\n  popperRef,\n  /** @description hide popover */\n  hide,\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;mCAyDc,CAAA;AAAA,EACZ,IAAM,EAAA,WAAA;AACR;;;;;;;AAOA,IAAM,MAAA,eAAA,GAAkB,SAAS,MAAM;AACrC,MAAA,OAAO,MAAM,iBAAiB,CAAA,CAAA;AAAA,KAC/B,CAAA,CAAA;AAED,IAAM,MAAA,EAAA,GAAK,aAAa,SAAS,CAAA,CAAA;AACjC,IAAA,MAAM,aAAa,GAAqB,EAAA,CAAA;AACxC,IAAM,MAAA,SAAA,GAAY,SAAS,MAAM;AAC/B,MAAO,IAAA,EAAA,CAAA;AAAmB,MAC3B,OAAA,CAAA,EAAA,GAAA,KAAA,CAAA,UAAA,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,SAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAO,MAAA,KAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MACL,OAAA;AAAA,QACE;AAA0B,UAC5B,KAAA,EAAA,OAAA,CAAA,KAAA,CAAA,KAAA,CAAA;AAAA,SACM;AAAA,QACR,KAAA,CAAA,WAAA;AAAA,OACD,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAA,MAAA,GAAO,GAAI,QAAK,CAAA;AAAwD,MACzE,OAAA,CAAA,EAAA,CAAA,CAAA,EAAA,EAAA,KAAA,CAAA,WAAA,EAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,KAAA,CAAA,OAAA,EAAA,CAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAA,MAAA,eAAa,GAAA,QAAkB,CAAA;AAAkB,MAClD,OAAA,KAAA,CAAA,UAAA,KAAA,CAAA,EAAA,EAAA,CAAA,SAAA,CAAA,KAAA,CAAA,eAAA,CAAA,CAAA;AAED,KAAA,CAAA,CAAA;AACE,IAAA,MAAA,IAAA,GAAW;AAAY,MACzB,IAAA,EAAA,CAAA;AAEA,MAAA,CAAA,EAAA,mBAA0B,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,EAAA,CAAA;AACxB,KAAA,CAAA;AAAmB,IACrB,MAAA,WAAA,GAAA,MAAA;AACA,MAAA,IAAM,eAAoB,CAAA,CAAA;AACxB,KAAA,CAAA;AAAmB,IACrB,MAAA,WAAA,GAAA,MAAA;AAEA,MAAA,IAAM,eAAmB,CAAA,CAAA;AACvB,KAAA,CAAA;AAAkB,IACpB,MAAA,UAAA,GAAA,MAAA;AAEA,MAAA,IAAM,cAAmB,CAAA,CAAA;AACvB,KAAA,CAAA;AACA,IAAA,MAAA,UAAkB,GAAA,MAAA;AAAA,MACpB,IAAA,CAAA,gBAAA,EAAA,KAAA,CAAA,CAAA;AAEA,MAAa,IAAA,CAAA,aAAA,CAAA,CAAA;AAAA,KAAA,CAAA;AAAA,IAEX,MAAA,CAAA;AAAA,MAAA,SAAA;AAAA,MAEA,IAAA;AAAA,KACD,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}