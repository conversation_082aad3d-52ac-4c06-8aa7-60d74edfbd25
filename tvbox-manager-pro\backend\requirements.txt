# FastAPI核心
fastapi>=0.100.0
uvicorn[standard]>=0.20.0
pydantic>=2.0.0
pydantic-settings>=2.0.0

# 数据库
sqlalchemy==2.0.23
alembic==1.13.1

# 数据库驱动
# SQLite (内置)
# MySQL
pymysql==1.1.0
cryptography==41.0.8
# PostgreSQL
psycopg2-binary==2.9.9

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# HTTP客户端
httpx==0.25.2
requests==2.31.0

# 加密解密
pycryptodome==3.19.0

# 缓存
redis==5.0.1

# 工具库
python-dotenv==1.0.0
click==8.1.7
rich==13.7.0

# 日期时间
python-dateutil==2.8.2

# JSON处理
orjson==3.9.10

# 文件处理
aiofiles==23.2.1

# 任务调度
apscheduler==3.10.4

# 邮件发送
fastapi-mail==1.4.1

# 监控和日志
prometheus-client==0.19.0

# 开发工具
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
black==23.11.0
isort==5.12.0
flake8==6.1.0

# 生产环境
gunicorn==21.2.0
