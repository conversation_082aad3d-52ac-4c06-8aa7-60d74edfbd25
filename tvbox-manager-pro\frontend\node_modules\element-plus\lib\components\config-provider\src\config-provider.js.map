{"version": 3, "file": "config-provider.js", "sources": ["../../../../../../packages/components/config-provider/src/config-provider.ts"], "sourcesContent": ["import { defineComponent, renderSlot, watch } from 'vue'\nimport { provideGlobalConfig } from './hooks/use-global-config'\nimport { configProviderProps } from './config-provider-props'\n\nimport type { MessageConfigContext } from '@element-plus/components/message'\n\nexport const messageConfig: MessageConfigContext = {}\n\nconst ConfigProvider = defineComponent({\n  name: 'ElConfigProvider',\n  props: configProviderProps,\n\n  setup(props, { slots }) {\n    const config = provideGlobalConfig(props)\n    watch(\n      () => props.message,\n      (val) => {\n        Object.assign(messageConfig, config?.value?.message ?? {}, val ?? {})\n      },\n      { immediate: true, deep: true }\n    )\n    return () => renderSlot(slots, 'default', { config: config?.value })\n  },\n})\nexport type ConfigProviderInstance = InstanceType<typeof ConfigProvider> &\n  unknown\n\nexport default ConfigProvider\n"], "names": ["defineComponent", "configProviderProps", "provideGlobalConfig", "watch", "renderSlot"], "mappings": ";;;;;;;;AAGY,MAAC,aAAa,GAAG,GAAG;AAC3B,MAAC,cAAc,GAAGA,mBAAe,CAAC;AACvC,EAAE,IAAI,EAAE,kBAAkB;AAC1B,EAAE,KAAK,EAAEC,uCAAmB;AAC5B,EAAE,KAAK,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE;AAC1B,IAAI,MAAM,MAAM,GAAGC,mCAAmB,CAAC,KAAK,CAAC,CAAC;AAC9C,IAAIC,SAAK,CAAC,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK;AACxC,MAAM,IAAI,EAAE,EAAE,EAAE,CAAC;AACjB,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,OAAO,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,EAAE,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;AACjK,KAAK,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AACxC,IAAI,OAAO,MAAMC,cAAU,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;AAClG,GAAG;AACH,CAAC;;;;;"}