{"version": 3, "file": "fr.min.js", "sources": ["../../../../packages/locale/lang/fr.ts"], "sourcesContent": ["export default {\n  name: 'fr',\n  el: {\n    breadcrumb: {\n      label: `<PERSON><PERSON> d'Ariane`,\n    },\n    colorpicker: {\n      confirm: 'Confirmer',\n      clear: 'Effacer',\n      defaultLabel: 'color picker',\n      description:\n        'La couleur actuelle est {color}. Appuyer sur Entrée pour sélectionner une nouvelle couleur.',\n    },\n    datepicker: {\n      now: 'Maintenant',\n      today: 'Auj.',\n      cancel: 'Annule<PERSON>',\n      clear: 'Effacer',\n      confirm: 'Confirmer',\n      dateTablePrompt:\n        'Utiliser les touches fléchées et appuyer sur Entrée pour sélectionner le jour du mois',\n      monthTablePrompt:\n        'Utiliser les touches fléchées et appuyer sur Entrée pour sélectionner le mois',\n      yearTablePrompt:\n        \"Utiliser les touches fléchées et appuyer sur Entrée pour sélectionner l'année\",\n      selectedDate: 'Date sélectionnée',\n      selectDate: 'Choisir date',\n      selectTime: 'Choisir horaire',\n      startDate: 'Date début',\n      startTime: '<PERSON><PERSON>re début',\n      endDate: 'Date fin',\n      endTime: '<PERSON><PERSON>re fin',\n      prevYear: '<PERSON><PERSON> précédente',\n      nextYear: '<PERSON><PERSON> suivante',\n      prevMonth: 'Mois précédent',\n      nextMonth: 'Mois suivant',\n      year: '', // In french, like in english, we don't say \"Année\" after the year number.\n      month1: 'Janvier',\n      month2: 'Février',\n      month3: 'Mars',\n      month4: 'Avril',\n      month5: 'Mai',\n      month6: 'Juin',\n      month7: 'Juillet',\n      month8: 'Août',\n      month9: 'Septembre',\n      month10: 'Octobre',\n      month11: 'Novembre',\n      month12: 'Décembre',\n      week: 'Semaine',\n      weeks: {\n        sun: 'Dim',\n        mon: 'Lun',\n        tue: 'Mar',\n        wed: 'Mer',\n        thu: 'Jeu',\n        fri: 'Ven',\n        sat: 'Sam',\n      },\n      weeksFull: {\n        sun: 'Dimanche',\n        mon: 'Lundi',\n        tue: 'Mardi',\n        wed: 'Mercredi',\n        thu: 'Jeudi',\n        fri: 'Vendredi',\n        sat: 'Samedi',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Fév',\n        mar: 'Mar',\n        apr: 'Avr',\n        may: 'Mai',\n        jun: 'Jun',\n        jul: 'Jul',\n        aug: 'Aoû',\n        sep: 'Sep',\n        oct: 'Oct',\n        nov: 'Nov',\n        dec: 'Déc',\n      },\n    },\n    inputNumber: {\n      decrease: 'décrémenter',\n      increase: 'incrémenter',\n    },\n    select: {\n      loading: 'Chargement',\n      noMatch: 'Aucune correspondance',\n      noData: 'Aucune donnée',\n      placeholder: 'Choisir',\n    },\n    mention: {\n      loading: 'Chargement',\n    },\n    cascader: {\n      noMatch: 'Aucune correspondance',\n      loading: 'Chargement',\n      placeholder: 'Choisir',\n      noData: 'Aucune donnée',\n    },\n    pagination: {\n      goto: 'Aller à',\n      pagesize: '/page',\n      total: 'Total {total}',\n      pageClassifier: '',\n      page: 'Page',\n      prev: 'Aller à la page précédente',\n      next: 'Aller à la page suivante',\n      currentPage: 'page {pager}',\n      prevPages: '{pager} pages précédentes',\n      nextPages: '{pager} pages suivantes',\n      deprecationWarning:\n        'Utilisations obsolètes détectées, veuillez vous référer à la documentation el-pagination pour plus de détails',\n    },\n    dialog: {\n      close: 'Fermer la boîte de dialogue',\n    },\n    drawer: {\n      close: 'Fermer la boîte de dialogue',\n    },\n    messagebox: {\n      title: 'Message',\n      confirm: 'Confirmer',\n      cancel: 'Annuler',\n      error: 'Erreur',\n      close: 'Fermer la boîte de dialogue',\n    },\n    upload: {\n      deleteTip: 'Cliquer sur supprimer pour retirer le fichier',\n      delete: 'Supprimer',\n      preview: 'Aperçu',\n      continue: 'Continuer',\n    },\n    slider: {\n      defaultLabel: 'curseur entre {min} et {max}',\n      defaultRangeStartLabel: 'choisir la valeur de départ',\n      defaultRangeEndLabel: 'sélectionner la valeur finale',\n    },\n    table: {\n      emptyText: 'Aucune donnée',\n      confirmFilter: 'Confirmer',\n      resetFilter: 'Réinitialiser',\n      clearFilter: 'Tous',\n      sumText: 'Somme',\n    },\n    tour: {\n      next: 'suivant',\n      previous: 'précédent',\n      finish: 'fin',\n    },\n    tree: {\n      emptyText: 'Aucune donnée',\n    },\n    transfer: {\n      noMatch: 'Aucune correspondance',\n      noData: 'Aucune donnée',\n      titles: ['Liste 1', 'Liste 2'],\n      filterPlaceholder: 'Entrer un mot clef',\n      noCheckedFormat: '{total} elements',\n      hasCheckedFormat: '{checked}/{total} coché(s)',\n    },\n    image: {\n      error: 'ECHEC',\n    },\n    pageHeader: {\n      title: 'Retour',\n    },\n    popconfirm: {\n      confirmButtonText: 'Oui',\n      cancelButtonText: 'Non',\n    },\n    carousel: {\n      leftArrow: 'Flèche du carrousel vers la gauche',\n      rightArrow: 'Flèche du carrousel vers la droite',\n      indicator: 'Passer au carrousel index {index}',\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,WAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,YAAY,CAAC,cAAc,CAAC,WAAW,CAAC,mGAAmG,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,eAAe,CAAC,mGAAmG,CAAC,gBAAgB,CAAC,2FAA2F,CAAC,eAAe,CAAC,8FAA8F,CAAC,YAAY,CAAC,yBAAyB,CAAC,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,iBAAiB,CAAC,SAAS,CAAC,eAAe,CAAC,SAAS,CAAC,kBAAkB,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,2BAA2B,CAAC,QAAQ,CAAC,mBAAmB,CAAC,SAAS,CAAC,sBAAsB,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,mBAAmB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,uBAAuB,CAAC,MAAM,CAAC,kBAAkB,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,uBAAuB,CAAC,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,eAAe,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,IAAI,CAAC,6BAA6B,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,iCAAiC,CAAC,SAAS,CAAC,yBAAyB,CAAC,kBAAkB,CAAC,oIAAoI,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,+CAA+C,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,8BAA8B,CAAC,sBAAsB,CAAC,gCAAgC,CAAC,oBAAoB,CAAC,kCAAkC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,aAAa,CAAC,WAAW,CAAC,WAAW,CAAC,kBAAkB,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,iBAAiB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,uBAAuB,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,eAAe,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,+BAA+B,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,uCAAuC,CAAC,UAAU,CAAC,uCAAuC,CAAC,SAAS,CAAC,mCAAmC,CAAC,CAAC,CAAC;;;;;;;;"}