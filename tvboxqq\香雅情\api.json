//资源全部免费，仅供测试交流，请勿内置传播与转卖；关注微信公众号：奇奇乐分享，发送“本地包”获取教程与更多资源！

{
    "wallpaper": "https://wp.upx8.com/api.php",
    "spider": "./spider.jar",
    "lives": [
        {
            "name": "BMCH",
            "type": 0,
            "url": "./lives/BMCH.txt",
            "epg": "https://epg.v1.mk/json?ch={name}&date={date}"
        },
        {
            "name": "Tianmu",
            "type": 0,
            "url": "./lives/Tianmu.txt",
            "epg": "https://epg.v1.mk/json?ch={name}&date={date}"
        },
        {
            "name": "fanmingming",
            "type": 0,
            "url": "./lives/fanmingming.txt"
        }
    ],
    "sites": [
        {
            "key": "csp_LocalFile",
            "name": "本机文件(接口完全免费)",
            "type": 3,
            "api": "csp_LocalFile",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "type_flag": 0
        },
        {
            "key": "drpy_js_豆瓣",
            "name": "豆瓣[js]",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/drpy.js"
        },
        {
            "key": "drpy_js_奇珍异兽",
            "name": "奇珍异兽[js]",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/奇珍异兽.js"
        },
        {
            "key": "drpy_js_优酷",
            "name": "优酷[js]",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/优酷.js"
        },
        {
            "key": "drpy_js_腾云驾雾",
            "name": "腾云驾雾[js]",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/腾云驾雾.js"
        },
        {
            "key": "drpy_js_百忙无果",
            "name": "百忙无果[js]",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/百忙无果.js"
        },
        {
            "key": "csp_Yisou",
            "name": "📀易搜(搜索)",
            "type": 3,
            "api": "csp_Yisou",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 0,
            "ext": "http://127.0.0.1:9978/file/XYQTVBox/alitoken.txt"
        },
        {
            "key": "csp_XYQHikerAL_云盘资源",
            "name": "📀云盘资源(搜索)",
            "type": 3,
            "api": "csp_XYQHikerAL",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 0,
            "ext": "./json/云盘资源.json"
        },
        {
            "key": "push_agent",
            "name": "📽推送",
            "type": 3,
            "api": "csp_PushAgent",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 0,
            "ext": "http://127.0.0.1:9978/file/XYQTVBox/alitoken.txt"
        },
        {
            "key": "csp_New6v",
            "name": "🧲新6V",
            "type": 3,
            "api": "csp_New6v",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "https://www.xb6v.com"
        },
        {
            "key": "csp_DyGod",
            "name": "🧲电影天堂",
            "type": 3,
            "api": "csp_DyGod",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "csp_QnMp4",
            "name": "🧲七妹",
            "type": 3,
            "api": "csp_QnMp4",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "csp_SeedHub",
            "name": "🧲SeedHub",
            "type": 3,
            "api": "csp_SeedHub",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "https://www.seedhub.cc"
        },
        {
            "key": "csp_MeijuTT",
            "name": "🧲美剧天堂",
            "type": 3,
            "api": "csp_MeijuTT",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "https://www.meijutt.net"
        },
        {
            "key": "csp_BLSGod",
            "name": "🧲80S影视",
            "type": 3,
            "api": "csp_BLSGod",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "csp_Xunlei8",
            "name": "🧲迅雷吧",
            "type": 3,
            "api": "csp_Xunlei8",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "360_spider",
            "name": "🐞360影视",
            "type": 3,
            "api": "csp_SP360",
            "filterable": 1,
            "quickSearch": 1,
            "searchable": 1
        },
        {
            "key": "csp_Kuaikan",
            "name": "💡快看影视",
            "type": 3,
            "api": "csp_Kuaikan",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "csp_LiteApple",
            "name": "🐞小苹果影视",
            "type": 3,
            "api": "csp_LiteApple",
            "filterable": 1,
            "quickSearch": 1,
            "searchable": 1
        },
        {
            "key": "csp_Bdys",
            "name": "🐞哔嘀影视",
            "type": 3,
            "api": "csp_Bdys",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "https://v.xlys.ltd.ua"
        },
        {
            "key": "csp_XYQHiker_修罗影视",
            "name": "🧲修罗影视(XYQH)",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/哔嘀影视.json"
        },
        {
            "key": "csp_Ddys",
            "name": "🐞低端影视",
            "type": 3,
            "api": "csp_Ddys",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "csp_JianPian",
            "name": "🔨荐片",
            "type": 3,
            "api": "csp_JianPian",
            "playerType": 1,
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1
        },
        {
            "key": "csp_Ikanbot",
            "name": "👾Ikanbot",
            "type": 3,
            "api": "csp_Ikanbot",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "https://v.aikanbot.com"
        },
        {
            "key": "csp_Bili",
            "name": "🐞哔哩综合",
            "type": 3,
            "api": "csp_Bili",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/Bili.json"
        },
        {
            "key": "csp_Bili幼儿",
            "name": "🐞哔哩幼儿",
            "type": 3,
            "api": "csp_Bili",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "ext": "./json/幼儿乐园.json"
        },
        {
            "key": "csp_Bili少儿",
            "name": "🐞哔哩少儿",
            "type": 3,
            "api": "csp_Bili",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "ext": "./json/少儿教育.json"
        },
        {
            "key": "csp_Bili小学",
            "name": "🐞哔哩小学",
            "type": 3,
            "api": "csp_Bili",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "ext": "./json/小学课堂.json"
        },
        {
            "key": "csp_Bili初中",
            "name": "🐞哔哩初中",
            "type": 3,
            "api": "csp_Bili",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "ext": "./json/初中课堂.json"
        },
        {
            "key": "csp_Bili高中",
            "name": "🐞哔哩高中",
            "type": 3,
            "api": "csp_Bili",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "ext": "./json/高中课堂.json"
        },
        {
            "key": "JS哔哩直播",
            "name": "哔哩直播[js]",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "ext": "./js/哔哩直播.js"
        },
        {
            "key": "csp_XYQHiker_戏曲多多",
            "name": "🎻戏曲多多",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/戏曲多多.json"
        },
        {
            "key": "csp_XYQHiker_酷奇MV",
            "name": "🎤酷奇MV(XYQH)",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "ext": "./json/酷奇MV.json"
        },
        {
            "key": "csp_XYQHiker_短剧屋",
            "name": "🎬短剧屋",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/短剧屋.json"
        },
        {
            "key": "csp_XYQHiker_兔小贝",
            "name": "🐰兔小贝(XYQH)",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/兔小贝.json"
        },
        {
            "key": "csp_XYQHiker_兔小贝2",
            "name": "🐰兔小贝2(XYQH)",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 0,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/兔小贝2.json"
        },
        {
            "key": "csp_XYQHiker_播视童趣",
            "name": "👶🏻播视童趣(XYQH)",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1,
            "ext": "./json/播视童趣.json"
        },
        {
            "key": "csp_XYQHiker_风车动漫",
            "name": "🌪风车动漫(XYQH)",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/风车动漫.json"
        },
        {
            "key": "csp_XYQHiker_樱花动漫",
            "name": "🌸樱花动漫(XYQH)",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/樱花动漫.json"
        },
        {
            "key": "csp_XYQHiker_去看吧动漫",
            "name": "🔭去看吧动漫(XYQH)",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/去看吧动漫.json"
        },
        {
            "key": "csp_XYQHiker_嗷呜动漫",
            "name": "🙀嗷呜动漫(XYQH)",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/嗷呜动漫.json"
        },
        {
            "key": "csp_XYQHiker_动漫巴士",
            "name": "🚌动漫巴士(XYQH)",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/动漫巴士.json"
        },
        {
            "key": "csp_XYQHiker_农民影视",
            "name": "🧑🏻‍农民影视",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/农民影视.json"
        },
        {
            "key": "csp_XYQHiker_剧圈圈",
            "name": "🌟剧圈圈(XYQH)",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/剧圈圈.json",
            "click": "document.getElementById('playleft').children[0].contentWindow.document.getElementById('start').click()"
        },
        {
            "key": "csp_XYQHiker_骚火电影VIP",
            "name": "🔥骚火电影VIP(XYQH)",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/骚火电影VIP.json"
        },
        {
            "key": "csp_XYQHiker_电影盒子",
            "name": "📦电影盒子",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/电影盒子.json"
        },
        {
            "key": "csp_XYQHiker_星辰影院",
            "name": "⭐️星辰影院(XYQH)",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/星辰影院.json"
        },
        {
            "key": "csp_XYQHiker_可可影视",
            "name": "☕️可可影视(XYQH)",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/可可影视.json"
        },
        {
            "key": "csp_XYQHiker_爱看影视",
            "name": "👀爱看影视(XYQH)",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/爱看影视.json"
        },
        {
            "key": "csp_XYQHiker_看一看影视",
            "name": "🔍看一看影视(XYQH)",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/看一看影视.json"
        },
        {
            "key": "csp_XYQHiker_瓜子影院",
            "name": "瓜子影院(XYQH)",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/瓜子影视.json"
        },
        {
            "key": "csp_XYQHiker_八号影视",
            "name": "8️⃣八号影视(XYQH)",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/八号影视.json"
        },
        {
            "key": "csp_XYQHiker_来看点播",
            "name": "⛅️来看点播(XYQH)",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "ext": "./json/来看点播.json"
        },
        {
            "key": "drpy_js_金牌",
            "name": "金牌影视[js]",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/金牌影视.js"
        },
        {
            "key": "柚子资源",
            "name": "🍊柚子资源",
            "type": 0,
            "api": "https://api.yzzy-api.com/inc/api.php",
            "searchable": 1,
            "quickSearch": 1,
            "categories": [
                "动作片",
                "喜剧片",
                "爱情片",
                "科幻片",
                "恐怖片",
                "剧情片",
                "战争片",
                "国产剧",
                "台湾剧",
                "韩国剧",
                "欧美剧",
                "香港剧",
                "泰国剧",
                "日本剧",
                "福利",
                "记录片",
                "动画片",
                "海外剧",
                "倫理片",
                "大陆综艺",
                "港台综艺",
                "日韩综艺",
                "欧美综艺",
                "国产动漫",
                "日韩动漫",
                "欧美动漫",
                "港台动漫",
                "海外动漫",
                "搞笑",
                "音乐",
                "影视",
                "汽车",
                "短剧大全",
                "预告片",
                "预告片",
                "体育"
            ]
        },
        {
            "key": "DRJS_虎牙",
            "name": "虎牙直播(JS)",
            "type": 3,
            "api": "./api/drpy2.min.js",
            "ext": "./js/虎牙直播.js",
            "searchable": 0,
            "quickSearch": 0,
            "filterable": 1
        },
        {
            "key": "csp_XYQHiker_虎牙直播",
            "name": "🐯虎牙直播(XYQH)",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 0,
            "filterable": 1,
            "ext": "./json/虎牙直播.json"
        },
        {
            "key": "csp_XYQHiker_斗鱼直播",
            "name": "🐠斗鱼直播(XYQH)",
            "type": 3,
            "api": "csp_XYQHiker",
            "searchable": 1,
            "quickSearch": 0,
            "filterable": 1,
            "ext": "./json/斗鱼直播.json"
        }
    ],
    "parses": [
        {
            "name": "Web聚合",
            "type": 3,
            "url": "Web"
        },
        {
            "name": "-咸鱼-",
            "type": 0,
            "url": "https://jx.xymp4.cc/?url=",
            "ext": {
                "header": {
                    "user-agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 Edg/134.0.3124.68"
                }
            }
        },
        {
            "name": "-虾米-",
            "type": 0,
            "url": "https://jx.xmflv.com/?url=",
            "ext": {
                "header": {
                    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.57"
                }
            }
        },
        {
            "name": "-淘片-",
            "type": 0,
            "url": "https://jx.yparse.com/index.php?url=",
            "ext": {
                "header": {
                    "user-agent": "Mozilla/5.0 (Linux; Android 13; V2049A Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/********* Mobile Safari/537.36"
                }
            }
        },
        {
            "name": "-冰豆-",
            "type": 0,
            "url": "https://bd.jx.cn/?url=",
            "ext": {
                "header": {
                    "user-agent": "Mozilla/5.0 (Linux; Android 13; V2049A Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/********* Mobile Safari/537.36"
                }
            }
        },
        {
            "name": "-77解析-",
            "type": 0,
            "url": "https://jx.77flv.cc/?url=",
            "ext": {
                "header": {
                    "user-agent": "Mozilla/5.0 (Linux; Android 13; V2049A Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/********* Mobile Safari/537.36"
                }
            }
        },
        {
            "name": "-zui-",
            "type": 0,
            "url": "https://jx.zui.cm/?url=",
            "ext": {
                "header": {
                    "user-agent": "Mozilla/5.0 (Linux; Android 13; V2049A Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/********* Mobile Safari/537.36"
                }
            }
        },
        {
            "name": "-盘古-",
            "type": 0,
            "url": "https://www.playm3u8.cn/jiexi.php?url=",
            "ext": {
                "header": {
                    "user-agent": "Mozilla/5.0 (Linux; Android 13; V2049A Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/********* Mobile Safari/537.36"
                }
            }
        },
        {
            "name": "-夜幕-",
            "type": 0,
            "url": "https://yemu.xyz/?url=",
            "ext": {
                "header": {
                    "user-agent": "Mozilla/5.0 (Linux; Android 13; V2049A Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/********* Mobile Safari/537.36"
                }
            }
        }
    ],
    "flags": [
        "youku",
        "qq",
        "iqiyi",
        "qiyi",
        "letv",
        "sohu",
        "tudou",
        "pptv",
        "mgtv",
        "wasu",
        "bilibili",
        "renrenmi"
    ],
    "rules": [
        {
            "host": "www.iesdouyin.com",
            "rule": [
                "playwm/?video_id="
            ]
        },
        {
            "host": "www.ysgc.vip",
            "rule": [
                "getm3u8?url=http"
            ]
        },
        {
            "host": "v.douyin.com",
            "rule": [
                "playwm/?video_id="
            ]
        },
        {
            "host": "dyxs20.com",
            "rule": [
                ".m3u8"
            ]
        },
        {
            "host": "www.agemys.cc",
            "rule": [
                "cdn-tos",
                "obj/tos-cn"
            ]
        },
        {
            "host": "www.sharenice.net",
            "rule": [
                "http.*?/play.{0,3}\\?[^url]{2,8}=.*",
                "qianpailive.com",
                "vid="
            ]
        },
        {
            "name": "暴风",
            "hosts": [
                "bfzy",
                "bfbfvip",
                "bfengbf"
            ],
            "regex": [
                "#EXTINF.*?\\s+.*?adjump.*?\\.ts"
            ]
        },
        {
            "name": "量子",
            "hosts": [
                "vip.lz",
                "hd.lz",
                ".cdnlz"
            ],
            "regex": [
                "#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:7\\.166667,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXTINF.*?\\s+.*?08646.*?\\.ts",
                "17.19",
                "19.63"
            ]
        },
        {
            "name": "非凡",
            "hosts": [
                "vip.ffzy",
                "hd.ffzy",
                "super.ffzy",
                "cachem3u8.2s0"
            ],
            "regex": [
                "#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:6\\.400000,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:6\\.666667,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXTINF.*?\\s+.*?1171(057).*?\\.ts",
                "#EXTINF.*?\\s+.*?6d7b(077).*?\\.ts",
                "#EXTINF.*?\\s+.*?6718a(403).*?\\.ts",
                "17.99",
                "14.45"
            ]
        },
        {
            "name": "索尼",
            "hosts": [
                "suonizy"
            ],
            "regex": [
                "#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:1\\.000000,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXTINF.*?\\s+.*?p1ayer.*?\\.ts",
                "#EXTINF.*?\\s+.*?\\/video\\/original.*?\\.ts"
            ]
        },
        {
            "name": "快看",
            "hosts": [
                "kuaikan"
            ],
            "regex": [
                "#EXT-X-KEY:METHOD=NONE\\r*\\n*#EXTINF:5,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXT-X-KEY:METHOD=NONE\\r*\\n*#EXTINF:2\\.4,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXT-X-KEY:METHOD=NONE\\r*\\n*#EXTINF:1\\.467,[\\s\\S]*?#EXT-X-DISCONTINUITY"
            ]
        },
        {
            "name": "leshiyun",
            "hosts": [
                "leshiyuncdn"
            ],
            "regex": [
                "15.92"
            ]
        },
        {
            "name": "1080zyk",
            "hosts": [
                "high24-playback",
                "high20-playback",
                "yzzy.play",
                "yzzy-dy"
            ],
            "regex": [
                "16.63",
                "17.66"
            ]
        },
        {
            "name": "ikun",
            "hosts": [
                "bfikuncdn"
            ],
            "regex": [
                "#EXTINF.*?\\s+.*?XR8pDxQk.*?\\.ts"
            ]
        },
        {
            "name": "黑木耳hmr",
            "hosts": [
                "hmrvideo"
            ],
            "regex": [
                "#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:3\\.366667,[\\s\\S]*?#EXT-X-DISCONTINUITY"
            ]
        },
        {
            "name": "ryplay",
            "hosts": [
                "cdn.ryplay"
            ],
            "regex": [
                "#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:6\\.633333,[\\s\\S]*?#EXT-X-DISCONTINUITY",
                "#EXT-X-DISCONTINUITY\\r*\\n*#EXTINF:8\\.333333,[\\s\\S].*?\\.ts",
                "#EXTINF:2\\.933333,[\\s\\S].*?\\.ts"
            ]
        },
        {
            "name": "555DM",
            "hosts": [
                "cqxfjz"
            ],
            "regex": [
                "10.56"
            ]
        },
        {
            "name": "海外看",
            "hosts": [
                "haiwaikan"
            ],
            "regex": [
                "10.0099",
                "10.3333",
                "16.0599",
                "8.1748",
                "10.85"
            ]
        },
        {
            "name": "磁力广告",
            "hosts": [
                "magnet"
            ],
            "regex": [
                "更多",
                "社 區",
                "x u u",
                "最 新",
                "更 新",
                "社 区",
                "有趣",
                "有 趣",
                "英皇体育",
                "全中文AV在线",
                "澳门皇冠赌场",
                "哥哥快来",
                "美女荷官",
                "裸聊",
                "新片首发",
                "UUE29"
            ]
        }
    ],
    "ijk": [
        {
            "group": "软解码",
            "options": [
                {
                    "category": 4,
                    "name": "opensles",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "overlay-format",
                    "value": "842225234"
                },
                {
                    "category": 4,
                    "name": "framedrop",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "soundtouch",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "start-on-prepared",
                    "value": "1"
                },
                {
                    "category": 1,
                    "name": "http-detect-range-support",
                    "value": "0"
                },
                {
                    "category": 1,
                    "name": "fflags",
                    "value": "fastseek"
                },
                {
                    "category": 2,
                    "name": "skip_loop_filter",
                    "value": "48"
                },
                {
                    "category": 4,
                    "name": "reconnect",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "enable-accurate-seek",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "mediacodec",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "mediacodec-auto-rotate",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "mediacodec-handle-resolution-change",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "mediacodec-hevc",
                    "value": "0"
                },
                {
                    "category": 1,
                    "name": "dns_cache_timeout",
                    "value": "600000000"
                }
            ]
        },
        {
            "group": "硬解码",
            "options": [
                {
                    "category": 4,
                    "name": "opensles",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "overlay-format",
                    "value": "842225234"
                },
                {
                    "category": 4,
                    "name": "framedrop",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "soundtouch",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "start-on-prepared",
                    "value": "1"
                },
                {
                    "category": 1,
                    "name": "http-detect-range-support",
                    "value": "0"
                },
                {
                    "category": 1,
                    "name": "fflags",
                    "value": "fastseek"
                },
                {
                    "category": 2,
                    "name": "skip_loop_filter",
                    "value": "48"
                },
                {
                    "category": 4,
                    "name": "reconnect",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "enable-accurate-seek",
                    "value": "0"
                },
                {
                    "category": 4,
                    "name": "mediacodec",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "mediacodec-auto-rotate",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "mediacodec-handle-resolution-change",
                    "value": "1"
                },
                {
                    "category": 4,
                    "name": "mediacodec-hevc",
                    "value": "1"
                },
                {
                    "category": 1,
                    "name": "dns_cache_timeout",
                    "value": "600000000"
                }
            ]
        }
    ],
    "ads": [
        "mimg.0c1q0l.cn",
        "www.googletagmanager.com",
        "www.google-analytics.com",
        "mc.usihnbcq.cn",
        "mg.g1mm3d.cn",
        "mscs.svaeuzh.cn",
        "cnzz.hhttm.top",
        "tp.vinuxhome.com",
        "cnzz.mmstat.com",
        "www.baihuillq.com",
        "s23.cnzz.com",
        "z3.cnzz.com",
        "c.cnzz.com",
        "stj.v1vo.top",
        "z12.cnzz.com",
        "img.mosflower.cn",
        "tips.gamevvip.com",
        "ehwe.yhdtns.com",
        "xdn.cqqc3.com",
        "www.jixunkyy.cn",
        "sp.chemacid.cn",
        "hm.baidu.com",
        "s9.cnzz.com",
        "z6.cnzz.com",
        "um.cavuc.com",
        "mav.mavuz.com",
        "wofwk.aoidf3.com",
        "z5.cnzz.com",
        "xc.hubeijieshikj.cn",
        "tj.tianwenhu.com",
        "xg.gars57.cn",
        "k.jinxiuzhilv.com",
        "cdn.bootcss.com",
        "ppl.xunzhuo123.com",
        "xomk.jiangjunmh.top",
        "img.xunzhuo123.com",
        "z1.cnzz.com",
        "s13.cnzz.com",
        "xg.huataisangao.cn",
        "z7.cnzz.com",
        "xg.huataisangao.cn",
        "z2.cnzz.com",
        "s96.cnzz.com",
        "q11.cnzz.com",
        "thy.dacedsfa.cn",
        "xg.whsbpw.cn",
        "s19.cnzz.com",
        "z8.cnzz.com",
        "s4.cnzz.com",
        "f5w.as12df.top",
        "ae01.alicdn.com",
        "www.92424.cn",
        "k.wudejia.com",
        "vivovip.mmszxc.top",
        "qiu.xixiqiu.com",
        "cdnjs.hnfenxun.com",
        "cms.qdwght.com",
        "api.htpan.net"
    ]
}