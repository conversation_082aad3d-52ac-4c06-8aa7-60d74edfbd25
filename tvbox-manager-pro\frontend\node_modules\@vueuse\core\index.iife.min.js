var VueDemi=function(g,d,l){if(g.install)return g;if(!d)return console.error("[vue-demi] no Vue instance found, please be sure to import `vue` before `vue-demi`."),g;if(d.version.slice(0,4)==="2.7."){let B=function(k,A){var U,W={},se={config:d.config,use:d.use.bind(d),mixin:d.mixin.bind(d),component:d.component.bind(d),provide:function(S,Y){return W[S]=Y,this},directive:function(S,Y){return Y?(d.directive(S,Y),se):d.directive(S)},mount:function(S,Y){return U||(U=new d(Object.assign({propsData:A},k,{provide:Object.assign(W,k.provide)})),U.$mount(S,Y),U)},unmount:function(){U&&(U.$destroy(),U=void 0)}};return se};var Pn=B;for(var X in d)g[X]=d[X];g.isVue2=!0,g.isVue3=!1,g.install=function(){},g.Vue=d,g.Vue2=d,g.version=d.version,g.warn=d.util.warn,g.createApp=B}else if(d.version.slice(0,2)==="2.")if(l){for(var X in l)g[X]=l[X];g.isVue2=!0,g.isVue3=!1,g.install=function(){},g.Vue=d,g.Vue2=d,g.version=d.version}else console.error("[vue-demi] no VueCompositionAPI instance found, please be sure to import `@vue/composition-api` before `vue-demi`.");else if(d.version.slice(0,2)==="3."){for(var X in d)g[X]=d[X];g.isVue2=!1,g.isVue3=!0,g.install=function(){},g.Vue=d,g.Vue2=void 0,g.version=d.version,g.set=function(B,k,A){return Array.isArray(B)?(B.length=Math.max(B.length,k),B.splice(k,1,A),A):(B[k]=A,A)},g.del=function(B,k){if(Array.isArray(B)){B.splice(k,1);return}delete B[k]}}else console.error("[vue-demi] Vue version "+d.version+" is unsupported.");return g}(this.VueDemi=this.VueDemi||(typeof VueDemi!="undefined"?VueDemi:{}),this.Vue||(typeof Vue!="undefined"?Vue:void 0),this.VueCompositionAPI||(typeof VueCompositionAPI!="undefined"?VueCompositionAPI:void 0));(function(g,d,l){"use strict";function X(e,t,n){let r;l.isRef(n)?r={evaluating:n}:r=n||{};const{lazy:o=!1,evaluating:s=void 0,shallow:u=!1,onError:a=d.noop}=r,i=l.ref(!o),c=u?l.shallowRef(t):l.ref(t);let f=0;return l.watchEffect(async v=>{if(!i.value)return;f++;const w=f;let y=!1;s&&Promise.resolve().then(()=>{s.value=!0});try{const p=await e(m=>{v(()=>{s&&(s.value=!1),y||m()})});w===f&&(c.value=p)}catch(p){a(p)}finally{s&&w===f&&(s.value=!1),y=!0}}),o?l.computed(()=>(i.value=!0,c.value)):c}function Pn(e,t,n,r){let o=l.inject(e);return n&&(o=l.inject(e,n)),r&&(o=l.inject(e,n,r)),typeof t=="function"?l.computed(s=>t(o,s)):l.computed({get:s=>t.get(o,s),set:t.set})}const B=e=>function(...t){return e.apply(this,t.map(n=>l.unref(n)))};function k(e){var t;const n=d.resolveUnref(e);return(t=n==null?void 0:n.$el)!=null?t:n}const A=d.isClient?window:void 0,U=d.isClient?window.document:void 0,W=d.isClient?window.navigator:void 0,se=d.isClient?window.location:void 0;function S(...e){let t,n,r,o;if(d.isString(e[0])||Array.isArray(e[0])?([n,r,o]=e,t=A):[t,n,r,o]=e,!t)return d.noop;Array.isArray(n)||(n=[n]),Array.isArray(r)||(r=[r]);const s=[],u=()=>{s.forEach(f=>f()),s.length=0},a=(f,v,w,y)=>(f.addEventListener(v,w,y),()=>f.removeEventListener(v,w,y)),i=l.watch(()=>[k(t),d.resolveUnref(o)],([f,v])=>{u(),!!f&&s.push(...n.flatMap(w=>r.map(y=>a(f,w,y,v))))},{immediate:!0,flush:"post"}),c=()=>{i(),u()};return d.tryOnScopeDispose(c),c}let Y=!1;function En(e,t,n={}){const{window:r=A,ignore:o=[],capture:s=!0,detectIframe:u=!1}=n;if(!r)return;d.isIOS&&!Y&&(Y=!0,Array.from(r.document.body.children).forEach(w=>w.addEventListener("click",d.noop)));let a=!0;const i=w=>o.some(y=>{if(typeof y=="string")return Array.from(r.document.querySelectorAll(y)).some(p=>p===w.target||w.composedPath().includes(p));{const p=k(y);return p&&(w.target===p||w.composedPath().includes(p))}}),f=[S(r,"click",w=>{const y=k(e);if(!(!y||y===w.target||w.composedPath().includes(y))){if(w.detail===0&&(a=!i(w)),!a){a=!0;return}t(w)}},{passive:!0,capture:s}),S(r,"pointerdown",w=>{const y=k(e);y&&(a=!w.composedPath().includes(y)&&!i(w))},{passive:!0}),u&&S(r,"blur",w=>{var y;const p=k(e);((y=r.document.activeElement)==null?void 0:y.tagName)==="IFRAME"&&!(p==null?void 0:p.contains(r.document.activeElement))&&t(w)})].filter(Boolean);return()=>f.forEach(w=>w())}var $n=Object.defineProperty,Tn=Object.defineProperties,Fn=Object.getOwnPropertyDescriptors,Ae=Object.getOwnPropertySymbols,Rn=Object.prototype.hasOwnProperty,In=Object.prototype.propertyIsEnumerable,ke=(e,t,n)=>t in e?$n(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,me=(e,t)=>{for(var n in t||(t={}))Rn.call(t,n)&&ke(e,n,t[n]);if(Ae)for(var n of Ae(t))In.call(t,n)&&ke(e,n,t[n]);return e},he=(e,t)=>Tn(e,Fn(t));const Cn=e=>typeof e=="function"?e:typeof e=="string"?t=>t.key===e:Array.isArray(e)?t=>e.includes(t.key):()=>!0;function ue(...e){let t,n,r={};e.length===3?(t=e[0],n=e[1],r=e[2]):e.length===2?typeof e[1]=="object"?(t=!0,n=e[0],r=e[1]):(t=e[0],n=e[1]):(t=!0,n=e[0]);const{target:o=A,eventName:s="keydown",passive:u=!1}=r,a=Cn(t);return S(o,s,c=>{a(c)&&n(c)},u)}function An(e,t,n={}){return ue(e,t,he(me({},n),{eventName:"keydown"}))}function kn(e,t,n={}){return ue(e,t,he(me({},n),{eventName:"keypress"}))}function jn(e,t,n={}){return ue(e,t,he(me({},n),{eventName:"keyup"}))}const Ln=500;function Un(e,t,n){var r,o;const s=l.computed(()=>k(e));let u;function a(){u&&(clearTimeout(u),u=void 0)}function i(f){var v,w,y,p;((v=n==null?void 0:n.modifiers)==null?void 0:v.self)&&f.target!==s.value||(a(),((w=n==null?void 0:n.modifiers)==null?void 0:w.prevent)&&f.preventDefault(),((y=n==null?void 0:n.modifiers)==null?void 0:y.stop)&&f.stopPropagation(),u=setTimeout(()=>t(f),(p=n==null?void 0:n.delay)!=null?p:Ln))}const c={capture:(r=n==null?void 0:n.modifiers)==null?void 0:r.capture,once:(o=n==null?void 0:n.modifiers)==null?void 0:o.once};S(s,"pointerdown",i,c),S(s,"pointerup",a,c),S(s,"pointerleave",a,c)}const Mn=()=>{const{activeElement:e,body:t}=document;if(!e||e===t)return!1;switch(e.tagName){case"INPUT":case"TEXTAREA":return!0}return e.hasAttribute("contenteditable")},Nn=({keyCode:e,metaKey:t,ctrlKey:n,altKey:r})=>t||n||r?!1:e>=48&&e<=57||e>=96&&e<=105||e>=65&&e<=90;function Hn(e,t={}){const{document:n=U}=t;n&&S(n,"keydown",o=>{!Mn()&&Nn(o)&&e(o)},{passive:!0})}function Wn(e,t=null){const n=l.getCurrentInstance();let r=()=>{};const o=l.customRef((s,u)=>(r=u,{get(){var a,i;return s(),(i=(a=n==null?void 0:n.proxy)==null?void 0:a.$refs[e])!=null?i:t},set(){}}));return d.tryOnMounted(r),l.onUpdated(r),o}function je(e={}){var t;const{window:n=A}=e,r=(t=e.document)!=null?t:n==null?void 0:n.document,o=d.computedWithControl(()=>null,()=>r==null?void 0:r.activeElement);return n&&(S(n,"blur",s=>{s.relatedTarget===null&&o.trigger()},!0),S(n,"focus",o.trigger,!0)),o}function Bn(e,t={}){const{interrupt:n=!0,onError:r=d.noop,onFinished:o=d.noop}=t,s={pending:"pending",rejected:"rejected",fulfilled:"fulfilled"},u=Array.from(new Array(e.length),()=>({state:s.pending,data:null})),a=l.reactive(u),i=l.ref(-1);if(!e||e.length===0)return o(),{activeIndex:i,result:a};function c(f,v){i.value++,a[i.value].data=v,a[i.value].state=f}return e.reduce((f,v)=>f.then(w=>{var y;if(((y=a[i.value])==null?void 0:y.state)===s.rejected&&n){o();return}return v(w).then(p=>(c(s.fulfilled,p),i.value===e.length-1&&o(),p))}).catch(w=>(c(s.rejected,w),r(),w)),Promise.resolve()),{activeIndex:i,result:a}}function Le(e,t,n){const{immediate:r=!0,delay:o=0,onError:s=d.noop,onSuccess:u=d.noop,resetOnExecute:a=!0,shallow:i=!0,throwError:c}=n??{},f=i?l.shallowRef(t):l.ref(t),v=l.ref(!1),w=l.ref(!1),y=l.ref(void 0);async function p(m=0,...h){a&&(f.value=t),y.value=void 0,v.value=!1,w.value=!0,m>0&&await d.promiseTimeout(m);const O=typeof e=="function"?e(...h):e;try{const _=await O;f.value=_,v.value=!0,u(_)}catch(_){if(y.value=_,s(_),c)throw y}finally{w.value=!1}return f.value}return r&&p(o),{state:f,isReady:v,isLoading:w,error:y,execute:p}}const re={array:e=>JSON.stringify(e),object:e=>JSON.stringify(e),set:e=>JSON.stringify(Array.from(e)),map:e=>JSON.stringify(Object.fromEntries(e)),null:()=>""};function Vn(e){return e?e instanceof Map?re.map:e instanceof Set?re.set:Array.isArray(e)?re.array:re.object:re.null}function xn(e,t){const n=l.ref(""),r=l.ref();function o(){if(!!d.isClient)return r.value=new Promise((s,u)=>{try{const a=d.resolveUnref(e);if(a==null)s("");else if(typeof a=="string")s(_e(new Blob([a],{type:"text/plain"})));else if(a instanceof Blob)s(_e(a));else if(a instanceof ArrayBuffer)s(window.btoa(String.fromCharCode(...new Uint8Array(a))));else if(a instanceof HTMLCanvasElement)s(a.toDataURL(t==null?void 0:t.type,t==null?void 0:t.quality));else if(a instanceof HTMLImageElement){const i=a.cloneNode(!1);i.crossOrigin="Anonymous",zn(i).then(()=>{const c=document.createElement("canvas"),f=c.getContext("2d");c.width=i.width,c.height=i.height,f.drawImage(i,0,0,c.width,c.height),s(c.toDataURL(t==null?void 0:t.type,t==null?void 0:t.quality))}).catch(u)}else if(typeof a=="object"){const c=((t==null?void 0:t.serializer)||Vn(a))(a);return s(_e(new Blob([c],{type:"application/json"})))}else u(new Error("target is unsupported types"))}catch(a){u(a)}}),r.value.then(s=>n.value=s),r.value}return l.isRef(e)||d.isFunction(e)?l.watch(e,o,{immediate:!0}):o(),{base64:n,promise:r,execute:o}}function zn(e){return new Promise((t,n)=>{e.complete?t():(e.onload=()=>{t()},e.onerror=n)})}function _e(e){return new Promise((t,n)=>{const r=new FileReader;r.onload=o=>{t(o.target.result)},r.onerror=n,r.readAsDataURL(e)})}function L(e,t=!1){const n=l.ref(),r=()=>n.value=Boolean(e());return r(),d.tryOnMounted(r,t),n}function qn({navigator:e=W}={}){const t=["chargingchange","chargingtimechange","dischargingtimechange","levelchange"],n=L(()=>e&&"getBattery"in e),r=l.ref(!1),o=l.ref(0),s=l.ref(0),u=l.ref(1);let a;function i(){r.value=this.charging,o.value=this.chargingTime||0,s.value=this.dischargingTime||0,u.value=this.level}return n.value&&e.getBattery().then(c=>{a=c,i.call(a);for(const f of t)S(a,f,i,{passive:!0})}),{isSupported:n,charging:r,chargingTime:o,dischargingTime:s,level:u}}function Gn(e){let{acceptAllDevices:t=!1}=e||{};const{filters:n=void 0,optionalServices:r=void 0,navigator:o=W}=e||{},s=L(()=>o&&"bluetooth"in o),u=l.shallowRef(void 0),a=l.shallowRef(null);l.watch(u,()=>{v()});async function i(){if(!!s.value){a.value=null,n&&n.length>0&&(t=!1);try{u.value=await(o==null?void 0:o.bluetooth.requestDevice({acceptAllDevices:t,filters:n,optionalServices:r}))}catch(w){a.value=w}}}const c=l.ref(),f=l.computed(()=>{var w;return((w=c.value)==null?void 0:w.connected)||!1});async function v(){if(a.value=null,u.value&&u.value.gatt){u.value.addEventListener("gattserverdisconnected",()=>{});try{c.value=await u.value.gatt.connect()}catch(w){a.value=w}}}return d.tryOnMounted(()=>{var w;u.value&&((w=u.value.gatt)==null||w.connect())}),d.tryOnScopeDispose(()=>{var w;u.value&&((w=u.value.gatt)==null||w.disconnect())}),{isSupported:s,isConnected:f,device:u,requestDevice:i,server:c,error:a}}function q(e,t={}){const{window:n=A}=t,r=L(()=>n&&"matchMedia"in n&&typeof n.matchMedia=="function");let o;const s=l.ref(!1),u=()=>{!o||("removeEventListener"in o?o.removeEventListener("change",a):o.removeListener(a))},a=()=>{!r.value||(u(),o=n.matchMedia(d.resolveRef(e).value),s.value=o.matches,"addEventListener"in o?o.addEventListener("change",a):o.addListener(a))};return l.watchEffect(a),d.tryOnScopeDispose(()=>u()),s}const Xn={sm:640,md:768,lg:1024,xl:1280,"2xl":1536},Yn={sm:576,md:768,lg:992,xl:1200,xxl:1400},Kn={xs:600,sm:960,md:1264,lg:1904},Qn={xs:480,sm:576,md:768,lg:992,xl:1200,xxl:1600},Jn={xs:600,sm:1024,md:1440,lg:1920},Zn={mobileS:320,mobileM:375,mobileL:425,tablet:768,laptop:1024,laptopL:1440,desktop4K:2560},Dn={"3xs":360,"2xs":480,xs:600,sm:768,md:1024,lg:1280,xl:1440,"2xl":1600,"3xl":1920,"4xl":2560};var er=Object.defineProperty,Ue=Object.getOwnPropertySymbols,tr=Object.prototype.hasOwnProperty,nr=Object.prototype.propertyIsEnumerable,Me=(e,t,n)=>t in e?er(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,rr=(e,t)=>{for(var n in t||(t={}))tr.call(t,n)&&Me(e,n,t[n]);if(Ue)for(var n of Ue(t))nr.call(t,n)&&Me(e,n,t[n]);return e};function or(e,t={}){function n(a,i){let c=e[a];return i!=null&&(c=d.increaseWithUnit(c,i)),typeof c=="number"&&(c=`${c}px`),c}const{window:r=A}=t;function o(a){return r?r.matchMedia(a).matches:!1}const s=a=>q(`(min-width: ${n(a)})`,t),u=Object.keys(e).reduce((a,i)=>(Object.defineProperty(a,i,{get:()=>s(i),enumerable:!0,configurable:!0}),a),{});return rr({greater(a){return q(`(min-width: ${n(a,.1)})`,t)},greaterOrEqual:s,smaller(a){return q(`(max-width: ${n(a,-.1)})`,t)},smallerOrEqual(a){return q(`(max-width: ${n(a)})`,t)},between(a,i){return q(`(min-width: ${n(a)}) and (max-width: ${n(i,-.1)})`,t)},isGreater(a){return o(`(min-width: ${n(a,.1)})`)},isGreaterOrEqual(a){return o(`(min-width: ${n(a)})`)},isSmaller(a){return o(`(max-width: ${n(a,-.1)})`)},isSmallerOrEqual(a){return o(`(max-width: ${n(a)})`)},isInBetween(a,i){return o(`(min-width: ${n(a)}) and (max-width: ${n(i,-.1)})`)}},u)}const ar=e=>{const{name:t,window:n=A}=e,r=L(()=>n&&"BroadcastChannel"in n),o=l.ref(!1),s=l.ref(),u=l.ref(),a=l.ref(null),i=f=>{s.value&&s.value.postMessage(f)},c=()=>{s.value&&s.value.close(),o.value=!0};return r.value&&d.tryOnMounted(()=>{a.value=null,s.value=new BroadcastChannel(t),s.value.addEventListener("message",f=>{u.value=f.data},{passive:!0}),s.value.addEventListener("messageerror",f=>{a.value=f},{passive:!0}),s.value.addEventListener("close",()=>{o.value=!0})}),d.tryOnScopeDispose(()=>{c()}),{isSupported:r,channel:s,data:u,post:i,close:c,error:a,isClosed:o}};function lr({window:e=A}={}){const t=r=>{const{state:o,length:s}=(e==null?void 0:e.history)||{},{hash:u,host:a,hostname:i,href:c,origin:f,pathname:v,port:w,protocol:y,search:p}=(e==null?void 0:e.location)||{};return{trigger:r,state:o,length:s,hash:u,host:a,hostname:i,href:c,origin:f,pathname:v,port:w,protocol:y,search:p}},n=l.ref(t("load"));return e&&(S(e,"popstate",()=>n.value=t("popstate"),{passive:!0}),S(e,"hashchange",()=>n.value=t("hashchange"),{passive:!0})),n}function sr(e,t=(r,o)=>r===o,n){const r=l.ref(e.value);return l.watch(()=>e.value,o=>{t(o,r.value)||(r.value=o)},n),r}function ur(e={}){const{navigator:t=W,read:n=!1,source:r,copiedDuring:o=1500,legacy:s=!1}=e,u=["copy","cut"],a=L(()=>t&&"clipboard"in t),i=l.computed(()=>a.value||s),c=l.ref(""),f=l.ref(!1),v=d.useTimeoutFn(()=>f.value=!1,o);function w(){a.value?t.clipboard.readText().then(h=>{c.value=h}):c.value=m()}if(i.value&&n)for(const h of u)S(h,w);async function y(h=d.resolveUnref(r)){i.value&&h!=null&&(a.value?await t.clipboard.writeText(h):p(h),c.value=h,f.value=!0,v.start())}function p(h){const O=document.createElement("textarea");O.value=h??"",O.style.position="absolute",O.style.opacity="0",document.body.appendChild(O),O.select(),document.execCommand("copy"),O.remove()}function m(){var h,O,_;return(_=(O=(h=document==null?void 0:document.getSelection)==null?void 0:h.call(document))==null?void 0:O.toString())!=null?_:""}return{isSupported:i,text:c,copied:f,copy:y}}var ir=Object.defineProperty,cr=Object.defineProperties,fr=Object.getOwnPropertyDescriptors,Ne=Object.getOwnPropertySymbols,dr=Object.prototype.hasOwnProperty,vr=Object.prototype.propertyIsEnumerable,He=(e,t,n)=>t in e?ir(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,pr=(e,t)=>{for(var n in t||(t={}))dr.call(t,n)&&He(e,n,t[n]);if(Ne)for(var n of Ne(t))vr.call(t,n)&&He(e,n,t[n]);return e},yr=(e,t)=>cr(e,fr(t));function oe(e){return JSON.parse(JSON.stringify(e))}function gr(e,t={}){const n=l.ref({}),{manual:r,clone:o=oe,deep:s=!0,immediate:u=!0}=t;function a(){n.value=o(l.unref(e))}return!r&&l.isRef(e)?l.watch(e,a,yr(pr({},t),{deep:s,immediate:u})):a(),{cloned:n,sync:a}}const be=typeof globalThis!="undefined"?globalThis:typeof window!="undefined"?window:typeof global!="undefined"?global:typeof self!="undefined"?self:{},Oe="__vueuse_ssr_handlers__";be[Oe]=be[Oe]||{};const We=be[Oe];function ie(e,t){return We[e]||t}function wr(e,t){We[e]=t}function Be(e){return e==null?"any":e instanceof Set?"set":e instanceof Map?"map":e instanceof Date?"date":typeof e=="boolean"?"boolean":typeof e=="string"?"string":typeof e=="object"?"object":Number.isNaN(e)?"any":"number"}var mr=Object.defineProperty,Ve=Object.getOwnPropertySymbols,hr=Object.prototype.hasOwnProperty,_r=Object.prototype.propertyIsEnumerable,xe=(e,t,n)=>t in e?mr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,ze=(e,t)=>{for(var n in t||(t={}))hr.call(t,n)&&xe(e,n,t[n]);if(Ve)for(var n of Ve(t))_r.call(t,n)&&xe(e,n,t[n]);return e};const Se={boolean:{read:e=>e==="true",write:e=>String(e)},object:{read:e=>JSON.parse(e),write:e=>JSON.stringify(e)},number:{read:e=>Number.parseFloat(e),write:e=>String(e)},any:{read:e=>e,write:e=>String(e)},string:{read:e=>e,write:e=>String(e)},map:{read:e=>new Map(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e.entries()))},set:{read:e=>new Set(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e))},date:{read:e=>new Date(e),write:e=>e.toISOString()}},Pe="vueuse-storage";function ce(e,t,n,r={}){var o;const{flush:s="pre",deep:u=!0,listenToStorageChanges:a=!0,writeDefaults:i=!0,mergeDefaults:c=!1,shallow:f,window:v=A,eventFilter:w,onError:y=I=>{console.error(I)}}=r,p=(f?l.shallowRef:l.ref)(t);if(!n)try{n=ie("getDefaultStorage",()=>{var I;return(I=A)==null?void 0:I.localStorage})()}catch(I){y(I)}if(!n)return p;const m=d.resolveUnref(t),h=Be(m),O=(o=r.serializer)!=null?o:Se[h],{pause:_,resume:b}=d.pausableWatch(p,()=>$(p.value),{flush:s,deep:u,eventFilter:w});return v&&a&&(S(v,"storage",T),S(v,Pe,F)),T(),p;function $(I){try{if(I==null)n.removeItem(e);else{const R=O.write(I),E=n.getItem(e);E!==R&&(n.setItem(e,R),v&&v.dispatchEvent(new CustomEvent(Pe,{detail:{key:e,oldValue:E,newValue:R,storageArea:n}})))}}catch(R){y(R)}}function P(I){const R=I?I.newValue:n.getItem(e);if(R==null)return i&&m!==null&&n.setItem(e,O.write(m)),m;if(!I&&c){const E=O.read(R);return d.isFunction(c)?c(E,m):h==="object"&&!Array.isArray(E)?ze(ze({},m),E):E}else return typeof R!="string"?R:O.read(R)}function F(I){T(I.detail)}function T(I){if(!(I&&I.storageArea!==n)){if(I&&I.key==null){p.value=m;return}if(!(I&&I.key!==e)){_();try{p.value=P(I)}catch(R){y(R)}finally{I?l.nextTick(b):b()}}}}}function Ee(e){return q("(prefers-color-scheme: dark)",e)}var br=Object.defineProperty,qe=Object.getOwnPropertySymbols,Or=Object.prototype.hasOwnProperty,Sr=Object.prototype.propertyIsEnumerable,Ge=(e,t,n)=>t in e?br(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Pr=(e,t)=>{for(var n in t||(t={}))Or.call(t,n)&&Ge(e,n,t[n]);if(qe)for(var n of qe(t))Sr.call(t,n)&&Ge(e,n,t[n]);return e};function Xe(e={}){const{selector:t="html",attribute:n="class",initialValue:r="auto",window:o=A,storage:s,storageKey:u="vueuse-color-scheme",listenToStorageChanges:a=!0,storageRef:i,emitAuto:c}=e,f=Pr({auto:"",light:"light",dark:"dark"},e.modes||{}),v=Ee({window:o}),w=l.computed(()=>v.value?"dark":"light"),y=i||(u==null?l.ref(r):ce(u,r,s,{window:o,listenToStorageChanges:a})),p=l.computed({get(){return y.value==="auto"&&!c?w.value:y.value},set(_){y.value=_}}),m=ie("updateHTMLAttrs",(_,b,$)=>{const P=o==null?void 0:o.document.querySelector(_);if(!!P)if(b==="class"){const F=$.split(/\s/g);Object.values(f).flatMap(T=>(T||"").split(/\s/g)).filter(Boolean).forEach(T=>{F.includes(T)?P.classList.add(T):P.classList.remove(T)})}else P.setAttribute(b,$)});function h(_){var b;const $=_==="auto"?w.value:_;m(t,n,(b=f[$])!=null?b:$)}function O(_){e.onChanged?e.onChanged(_,h):h(_)}return l.watch(p,O,{flush:"post",immediate:!0}),c&&l.watch(w,()=>O(p.value),{flush:"post"}),d.tryOnMounted(()=>O(p.value)),p}function Er(e=l.ref(!1)){const t=d.createEventHook(),n=d.createEventHook(),r=d.createEventHook();let o=d.noop;const s=i=>(r.trigger(i),e.value=!0,new Promise(c=>{o=c})),u=i=>{e.value=!1,t.trigger(i),o({data:i,isCanceled:!1})},a=i=>{e.value=!1,n.trigger(i),o({data:i,isCanceled:!0})};return{isRevealed:l.computed(()=>e.value),reveal:s,confirm:u,cancel:a,onReveal:r.on,onConfirm:t.on,onCancel:n.on}}function ae(e,t,{window:n=A,initialValue:r=""}={}){const o=l.ref(r),s=l.computed(()=>{var u;return k(t)||((u=n==null?void 0:n.document)==null?void 0:u.documentElement)});return l.watch([s,()=>d.resolveUnref(e)],([u,a])=>{var i;if(u&&n){const c=(i=n.getComputedStyle(u).getPropertyValue(a))==null?void 0:i.trim();o.value=c||r}},{immediate:!0}),l.watch(o,u=>{var a;((a=s.value)==null?void 0:a.style)&&s.value.style.setProperty(d.resolveUnref(e),u)}),o}function $r(){const e=l.getCurrentInstance(),t=d.computedWithControl(()=>null,()=>e.proxy.$el);return l.onUpdated(t.trigger),l.onMounted(t.trigger),t}function Tr(e,t){var n;const r=l.shallowRef((n=t==null?void 0:t.initialValue)!=null?n:e[0]),o=l.computed({get(){var c;let f=(t==null?void 0:t.getIndexOf)?t.getIndexOf(r.value,e):e.indexOf(r.value);return f<0&&(f=(c=t==null?void 0:t.fallbackIndex)!=null?c:0),f},set(c){s(c)}});function s(c){const f=e.length,v=(c%f+f)%f,w=e[v];return r.value=w,w}function u(c=1){return s(o.value+c)}function a(c=1){return u(c)}function i(c=1){return u(-c)}return{state:r,index:o,next:a,prev:i}}var Fr=Object.defineProperty,Rr=Object.defineProperties,Ir=Object.getOwnPropertyDescriptors,Ye=Object.getOwnPropertySymbols,Cr=Object.prototype.hasOwnProperty,Ar=Object.prototype.propertyIsEnumerable,Ke=(e,t,n)=>t in e?Fr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,kr=(e,t)=>{for(var n in t||(t={}))Cr.call(t,n)&&Ke(e,n,t[n]);if(Ye)for(var n of Ye(t))Ar.call(t,n)&&Ke(e,n,t[n]);return e},jr=(e,t)=>Rr(e,Ir(t));function Lr(e={}){const{valueDark:t="dark",valueLight:n="",window:r=A}=e,o=Xe(jr(kr({},e),{onChanged:(a,i)=>{var c;e.onChanged?(c=e.onChanged)==null||c.call(e,a==="dark"):i(a)},modes:{dark:t,light:n}})),s=Ee({window:r});return l.computed({get(){return o.value==="dark"},set(a){a===s.value?o.value="auto":o.value=a?"dark":"light"}})}const Qe=e=>e,Ur=(e,t)=>e.value=t;function Mr(e){return e?d.isFunction(e)?e:oe:Qe}function Nr(e){return e?d.isFunction(e)?e:oe:Qe}function Je(e,t={}){const{clone:n=!1,dump:r=Mr(n),parse:o=Nr(n),setSource:s=Ur}=t;function u(){return l.markRaw({snapshot:r(e.value),timestamp:d.timestamp()})}const a=l.ref(u()),i=l.ref([]),c=l.ref([]),f=b=>{s(e,o(b.snapshot)),a.value=b},v=()=>{i.value.unshift(a.value),a.value=u(),t.capacity&&i.value.length>t.capacity&&i.value.splice(t.capacity,1/0),c.value.length&&c.value.splice(0,c.value.length)},w=()=>{i.value.splice(0,i.value.length),c.value.splice(0,c.value.length)},y=()=>{const b=i.value.shift();b&&(c.value.unshift(a.value),f(b))},p=()=>{const b=c.value.shift();b&&(i.value.unshift(a.value),f(b))},m=()=>{f(a.value)},h=l.computed(()=>[a.value,...i.value]),O=l.computed(()=>i.value.length>0),_=l.computed(()=>c.value.length>0);return{source:e,undoStack:i,redoStack:c,last:a,history:h,canUndo:O,canRedo:_,clear:w,commit:v,reset:m,undo:y,redo:p}}var Hr=Object.defineProperty,Wr=Object.defineProperties,Br=Object.getOwnPropertyDescriptors,Ze=Object.getOwnPropertySymbols,Vr=Object.prototype.hasOwnProperty,xr=Object.prototype.propertyIsEnumerable,De=(e,t,n)=>t in e?Hr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,et=(e,t)=>{for(var n in t||(t={}))Vr.call(t,n)&&De(e,n,t[n]);if(Ze)for(var n of Ze(t))xr.call(t,n)&&De(e,n,t[n]);return e},tt=(e,t)=>Wr(e,Br(t));function $e(e,t={}){const{deep:n=!1,flush:r="pre",eventFilter:o}=t,{eventFilter:s,pause:u,resume:a,isActive:i}=d.pausableFilter(o),{ignoreUpdates:c,ignorePrevAsyncUpdates:f,stop:v}=d.watchIgnorable(e,h,{deep:n,flush:r,eventFilter:s});function w($,P){f(),c(()=>{$.value=P})}const y=Je(e,tt(et({},t),{clone:t.clone||n,setSource:w})),{clear:p,commit:m}=y;function h(){f(),m()}function O($){a(),$&&h()}function _($){let P=!1;const F=()=>P=!0;c(()=>{$(F)}),P||h()}function b(){v(),p()}return tt(et({},y),{isTracking:i,pause:u,resume:O,commit:h,batch:_,dispose:b})}var zr=Object.defineProperty,qr=Object.defineProperties,Gr=Object.getOwnPropertyDescriptors,nt=Object.getOwnPropertySymbols,Xr=Object.prototype.hasOwnProperty,Yr=Object.prototype.propertyIsEnumerable,rt=(e,t,n)=>t in e?zr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,ot=(e,t)=>{for(var n in t||(t={}))Xr.call(t,n)&&rt(e,n,t[n]);if(nt)for(var n of nt(t))Yr.call(t,n)&&rt(e,n,t[n]);return e},Kr=(e,t)=>qr(e,Gr(t));function Qr(e,t={}){const n=t.debounce?d.debounceFilter(t.debounce):void 0,r=$e(e,Kr(ot({},t),{eventFilter:n}));return ot({},r)}function Jr(e={}){const{window:t=A,eventFilter:n=d.bypassFilter}=e,r=l.ref({x:null,y:null,z:null}),o=l.ref({alpha:null,beta:null,gamma:null}),s=l.ref(0),u=l.ref({x:null,y:null,z:null});if(t){const a=d.createFilterWrapper(n,i=>{r.value=i.acceleration,u.value=i.accelerationIncludingGravity,o.value=i.rotationRate,s.value=i.interval});S(t,"devicemotion",a)}return{acceleration:r,accelerationIncludingGravity:u,rotationRate:o,interval:s}}function at(e={}){const{window:t=A}=e,n=L(()=>t&&"DeviceOrientationEvent"in t),r=l.ref(!1),o=l.ref(null),s=l.ref(null),u=l.ref(null);return t&&n.value&&S(t,"deviceorientation",a=>{r.value=a.absolute,o.value=a.alpha,s.value=a.beta,u.value=a.gamma}),{isSupported:n,isAbsolute:r,alpha:o,beta:s,gamma:u}}function Zr({window:e=A}={}){const t=l.ref(1);if(e){let n=function(){t.value=e.devicePixelRatio,r(),o=e.matchMedia(`(resolution: ${t.value}dppx)`),o.addEventListener("change",n,{once:!0})},r=function(){o==null||o.removeEventListener("change",n)},o;n(),d.tryOnScopeDispose(r)}return{pixelRatio:t}}function lt(e,t={}){const{controls:n=!1,navigator:r=W}=t,o=L(()=>r&&"permissions"in r);let s;const u=typeof e=="string"?{name:e}:e,a=l.ref(),i=()=>{s&&(a.value=s.state)},c=d.createSingletonPromise(async()=>{if(!!o.value){if(!s)try{s=await r.permissions.query(u),S(s,"change",i),i()}catch{a.value="prompt"}return s}});return c(),n?{state:a,isSupported:o,query:c}:a}function Dr(e={}){const{navigator:t=W,requestPermissions:n=!1,constraints:r={audio:!0,video:!0},onUpdated:o}=e,s=l.ref([]),u=l.computed(()=>s.value.filter(y=>y.kind==="videoinput")),a=l.computed(()=>s.value.filter(y=>y.kind==="audioinput")),i=l.computed(()=>s.value.filter(y=>y.kind==="audiooutput")),c=L(()=>t&&t.mediaDevices&&t.mediaDevices.enumerateDevices),f=l.ref(!1);async function v(){!c.value||(s.value=await t.mediaDevices.enumerateDevices(),o==null||o(s.value))}async function w(){if(!c.value)return!1;if(f.value)return!0;const{state:y,query:p}=lt("camera",{controls:!0});return await p(),y.value!=="granted"&&((await t.mediaDevices.getUserMedia(r)).getTracks().forEach(h=>h.stop()),v()),f.value=!0,f.value}return c.value&&(n&&w(),S(t.mediaDevices,"devicechange",v),v()),{devices:s,ensurePermissions:w,permissionGranted:f,videoInputs:u,audioInputs:a,audioOutputs:i,isSupported:c}}function eo(e={}){var t;const n=l.ref((t=e.enabled)!=null?t:!1),r=e.video,o=e.audio,{navigator:s=W}=e,u=L(()=>{var y;return(y=s==null?void 0:s.mediaDevices)==null?void 0:y.getDisplayMedia}),a={audio:o,video:r},i=l.shallowRef();async function c(){if(!(!u.value||i.value))return i.value=await s.mediaDevices.getDisplayMedia(a),i.value}async function f(){var y;(y=i.value)==null||y.getTracks().forEach(p=>p.stop()),i.value=void 0}function v(){f(),n.value=!1}async function w(){return await c(),i.value&&(n.value=!0),i.value}return l.watch(n,y=>{y?c():f()},{immediate:!0}),{isSupported:u,stream:i,start:w,stop:v,enabled:n}}function to({document:e=U}={}){if(!e)return l.ref("visible");const t=l.ref(e.visibilityState);return S(e,"visibilitychange",()=>{t.value=e.visibilityState}),t}var no=Object.defineProperty,ro=Object.defineProperties,oo=Object.getOwnPropertyDescriptors,st=Object.getOwnPropertySymbols,ao=Object.prototype.hasOwnProperty,lo=Object.prototype.propertyIsEnumerable,ut=(e,t,n)=>t in e?no(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,so=(e,t)=>{for(var n in t||(t={}))ao.call(t,n)&&ut(e,n,t[n]);if(st)for(var n of st(t))lo.call(t,n)&&ut(e,n,t[n]);return e},uo=(e,t)=>ro(e,oo(t));function io(e,t={}){var n,r,o;const s=(n=t.draggingElement)!=null?n:A,u=(r=t.handle)!=null?r:e,a=l.ref((o=d.resolveUnref(t.initialValue))!=null?o:{x:0,y:0}),i=l.ref(),c=p=>t.pointerTypes?t.pointerTypes.includes(p.pointerType):!0,f=p=>{d.resolveUnref(t.preventDefault)&&p.preventDefault(),d.resolveUnref(t.stopPropagation)&&p.stopPropagation()},v=p=>{var m;if(!c(p)||d.resolveUnref(t.exact)&&p.target!==d.resolveUnref(e))return;const h=d.resolveUnref(e).getBoundingClientRect(),O={x:p.clientX-h.left,y:p.clientY-h.top};((m=t.onStart)==null?void 0:m.call(t,O,p))!==!1&&(i.value=O,f(p))},w=p=>{var m;!c(p)||!i.value||(a.value={x:p.clientX-i.value.x,y:p.clientY-i.value.y},(m=t.onMove)==null||m.call(t,a.value,p),f(p))},y=p=>{var m;!c(p)||!i.value||(i.value=void 0,(m=t.onEnd)==null||m.call(t,a.value,p),f(p))};return d.isClient&&(S(u,"pointerdown",v,!0),S(s,"pointermove",w,!0),S(s,"pointerup",y,!0)),uo(so({},d.toRefs(a)),{position:a,isDragging:l.computed(()=>!!i.value),style:l.computed(()=>`left:${a.value.x}px;top:${a.value.y}px;`)})}function co(e,t){const n=l.ref(!1);let r=0;return d.isClient&&(S(e,"dragenter",o=>{o.preventDefault(),r+=1,n.value=!0}),S(e,"dragover",o=>{o.preventDefault()}),S(e,"dragleave",o=>{o.preventDefault(),r-=1,r===0&&(n.value=!1)}),S(e,"drop",o=>{var s,u;o.preventDefault(),r=0,n.value=!1;const a=Array.from((u=(s=o.dataTransfer)==null?void 0:s.files)!=null?u:[]);t==null||t(a.length===0?null:a)})),{isOverDropZone:n}}var it=Object.getOwnPropertySymbols,fo=Object.prototype.hasOwnProperty,vo=Object.prototype.propertyIsEnumerable,po=(e,t)=>{var n={};for(var r in e)fo.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&it)for(var r of it(e))t.indexOf(r)<0&&vo.call(e,r)&&(n[r]=e[r]);return n};function fe(e,t,n={}){const r=n,{window:o=A}=r,s=po(r,["window"]);let u;const a=L(()=>o&&"ResizeObserver"in o),i=()=>{u&&(u.disconnect(),u=void 0)},c=l.watch(()=>k(e),v=>{i(),a.value&&o&&v&&(u=new ResizeObserver(t),u.observe(v,s))},{immediate:!0,flush:"post"}),f=()=>{i(),c()};return d.tryOnScopeDispose(f),{isSupported:a,stop:f}}function yo(e,t={}){const{reset:n=!0,windowResize:r=!0,windowScroll:o=!0,immediate:s=!0}=t,u=l.ref(0),a=l.ref(0),i=l.ref(0),c=l.ref(0),f=l.ref(0),v=l.ref(0),w=l.ref(0),y=l.ref(0);function p(){const m=k(e);if(!m){n&&(u.value=0,a.value=0,i.value=0,c.value=0,f.value=0,v.value=0,w.value=0,y.value=0);return}const h=m.getBoundingClientRect();u.value=h.height,a.value=h.bottom,i.value=h.left,c.value=h.right,f.value=h.top,v.value=h.width,w.value=h.x,y.value=h.y}return fe(e,p),l.watch(()=>k(e),m=>!m&&p()),o&&S("scroll",p,{capture:!0,passive:!0}),r&&S("resize",p,{passive:!0}),d.tryOnMounted(()=>{s&&p()}),{height:u,bottom:a,left:i,right:c,top:f,width:v,x:w,y,update:p}}function D(e,t={}){const{immediate:n=!0,window:r=A}=t,o=l.ref(!1);let s=0,u=null;function a(f){if(!o.value||!r)return;const v=f-s;e({delta:v,timestamp:f}),s=f,u=r.requestAnimationFrame(a)}function i(){!o.value&&r&&(o.value=!0,u=r.requestAnimationFrame(a))}function c(){o.value=!1,u!=null&&r&&(r.cancelAnimationFrame(u),u=null)}return n&&i(),d.tryOnScopeDispose(c),{isActive:l.readonly(o),pause:c,resume:i}}var go=Object.defineProperty,ct=Object.getOwnPropertySymbols,wo=Object.prototype.hasOwnProperty,mo=Object.prototype.propertyIsEnumerable,ft=(e,t,n)=>t in e?go(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,ho=(e,t)=>{for(var n in t||(t={}))wo.call(t,n)&&ft(e,n,t[n]);if(ct)for(var n of ct(t))mo.call(t,n)&&ft(e,n,t[n]);return e};function _o(e){const t=l.ref(null),{x:n,y:r,document:o=U}=e,s=D(()=>{t.value=(o==null?void 0:o.elementFromPoint(d.resolveUnref(n),d.resolveUnref(r)))||null});return ho({element:t},s)}function bo(e,t={}){const n=t?t.delayEnter:0,r=t?t.delayLeave:0,o=l.ref(!1);let s;const u=a=>{const i=a?n:r;s&&(clearTimeout(s),s=void 0),i?s=setTimeout(()=>o.value=a,i):o.value=a};return window&&(S(e,"mouseenter",()=>u(!0),{passive:!0}),S(e,"mouseleave",()=>u(!1),{passive:!0})),o}function dt(e,t={width:0,height:0},n={}){const{window:r=A,box:o="content-box"}=n,s=l.computed(()=>{var i,c;return(c=(i=k(e))==null?void 0:i.namespaceURI)==null?void 0:c.includes("svg")}),u=l.ref(t.width),a=l.ref(t.height);return fe(e,([i])=>{const c=o==="border-box"?i.borderBoxSize:o==="content-box"?i.contentBoxSize:i.devicePixelContentBoxSize;if(r&&s.value){const f=k(e);if(f){const v=r.getComputedStyle(f);u.value=parseFloat(v.width),a.value=parseFloat(v.height)}}else if(c){const f=Array.isArray(c)?c:[c];u.value=f.reduce((v,{inlineSize:w})=>v+w,0),a.value=f.reduce((v,{blockSize:w})=>v+w,0)}else u.value=i.contentRect.width,a.value=i.contentRect.height},n),l.watch(()=>k(e),i=>{u.value=i?t.width:0,a.value=i?t.height:0}),{width:u,height:a}}function Oo(e,{window:t=A,scrollTarget:n}={}){const r=l.ref(!1),o=()=>{if(!t)return;const s=t.document,u=k(e);if(!u)r.value=!1;else{const a=u.getBoundingClientRect();r.value=a.top<=(t.innerHeight||s.documentElement.clientHeight)&&a.left<=(t.innerWidth||s.documentElement.clientWidth)&&a.bottom>=0&&a.right>=0}};return l.watch(()=>k(e),()=>o(),{immediate:!0,flush:"post"}),t&&S(n||t,"scroll",o,{capture:!1,passive:!0}),r}const ee=new Map;function So(e){const t=l.getCurrentScope();function n(a){var i;const c=ee.get(e)||[];c.push(a),ee.set(e,c);const f=()=>o(a);return(i=t==null?void 0:t.cleanups)==null||i.push(f),f}function r(a){function i(...c){o(i),a(...c)}return n(i)}function o(a){const i=ee.get(e);if(!i)return;const c=i.indexOf(a);c>-1&&i.splice(c,1),i.length||ee.delete(e)}function s(){ee.delete(e)}function u(a,i){var c;(c=ee.get(e))==null||c.forEach(f=>f(a,i))}return{on:n,once:r,off:o,emit:u,reset:s}}function Po(e,t=[],n={}){const r=l.ref(null),o=l.ref(null),s=l.ref("CONNECTING"),u=l.ref(null),a=l.ref(null),{withCredentials:i=!1}=n,c=()=>{u.value&&(u.value.close(),u.value=null,s.value="CLOSED")},f=new EventSource(e,{withCredentials:i});u.value=f,f.onopen=()=>{s.value="OPEN",a.value=null},f.onerror=v=>{s.value="CLOSED",a.value=v},f.onmessage=v=>{r.value=null,o.value=v.data};for(const v of t)S(f,v,w=>{r.value=v,o.value=w.data||null});return d.tryOnScopeDispose(()=>{c()}),{eventSource:u,event:r,data:o,status:s,error:a,close:c}}function Eo(e={}){const{initialValue:t=""}=e,n=L(()=>typeof window!="undefined"&&"EyeDropper"in window),r=l.ref(t);async function o(s){if(!n.value)return;const a=await new window.EyeDropper().open(s);return r.value=a.sRGBHex,a}return{isSupported:n,sRGBHex:r,open:o}}function $o(e=null,t={}){const{baseUrl:n="",rel:r="icon",document:o=U}=t,s=d.resolveRef(e),u=a=>{o==null||o.head.querySelectorAll(`link[rel*="${r}"]`).forEach(i=>i.href=`${n}${a}`)};return l.watch(s,(a,i)=>{d.isString(a)&&a!==i&&u(a)},{immediate:!0}),s}var To=Object.defineProperty,Fo=Object.defineProperties,Ro=Object.getOwnPropertyDescriptors,vt=Object.getOwnPropertySymbols,Io=Object.prototype.hasOwnProperty,Co=Object.prototype.propertyIsEnumerable,pt=(e,t,n)=>t in e?To(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,M=(e,t)=>{for(var n in t||(t={}))Io.call(t,n)&&pt(e,n,t[n]);if(vt)for(var n of vt(t))Co.call(t,n)&&pt(e,n,t[n]);return e},Z=(e,t)=>Fo(e,Ro(t));const Ao={json:"application/json",text:"text/plain"};function de(e){return e&&d.containsProp(e,"immediate","refetch","initialData","timeout","beforeFetch","afterFetch","onFetchError","fetch")}function ko(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function le(e){return typeof Headers!="undefined"&&e instanceof Headers?Object.fromEntries([...e.entries()]):e}function te(e,...t){return e==="overwrite"?async n=>{const r=t[t.length-1];return r!==void 0&&await r(n),n}:async n=>(await t.reduce((r,o)=>r.then(async()=>{o&&(n=M(M({},n),await o(n)))}),Promise.resolve()),n)}function jo(e={}){const t=e.combination||"chain",n=e.options||{},r=e.fetchOptions||{};function o(s,...u){const a=l.computed(()=>{const f=d.resolveUnref(e.baseUrl),v=d.resolveUnref(s);return f&&!ko(v)?Lo(f,v):v});let i=n,c=r;return u.length>0&&(de(u[0])?i=Z(M(M({},i),u[0]),{beforeFetch:te(t,n.beforeFetch,u[0].beforeFetch),afterFetch:te(t,n.afterFetch,u[0].afterFetch),onFetchError:te(t,n.onFetchError,u[0].onFetchError)}):c=Z(M(M({},c),u[0]),{headers:M(M({},le(c.headers)||{}),le(u[0].headers)||{})})),u.length>1&&de(u[1])&&(i=Z(M(M({},i),u[1]),{beforeFetch:te(t,n.beforeFetch,u[1].beforeFetch),afterFetch:te(t,n.afterFetch,u[1].afterFetch),onFetchError:te(t,n.onFetchError,u[1].onFetchError)})),yt(a,c,i)}return o}function yt(e,...t){var n;const r=typeof AbortController=="function";let o={},s={immediate:!0,refetch:!1,timeout:0};const u={method:"GET",type:"text",payload:void 0};t.length>0&&(de(t[0])?s=M(M({},s),t[0]):o=t[0]),t.length>1&&de(t[1])&&(s=M(M({},s),t[1]));const{fetch:a=(n=A)==null?void 0:n.fetch,initialData:i,timeout:c}=s,f=d.createEventHook(),v=d.createEventHook(),w=d.createEventHook(),y=l.ref(!1),p=l.ref(!1),m=l.ref(!1),h=l.ref(null),O=l.shallowRef(null),_=l.shallowRef(null),b=l.shallowRef(i),$=l.computed(()=>r&&p.value);let P,F;const T=()=>{r&&P&&(P.abort(),P=void 0)},I=N=>{p.value=N,y.value=!N};c&&(F=d.useTimeoutFn(T,c,{immediate:!1}));const R=async(N=!1)=>{var G;I(!0),_.value=null,h.value=null,m.value=!1,r&&(T(),P=new AbortController,P.signal.onabort=()=>m.value=!0,o=Z(M({},o),{signal:P.signal}));const V={method:u.method,headers:{}};if(u.payload){const ge=le(V.headers);u.payloadType&&(ge["Content-Type"]=(G=Ao[u.payloadType])!=null?G:u.payloadType);const we=d.resolveUnref(u.payload);V.body=u.payloadType==="json"?JSON.stringify(we):we}let x=!1;const Q={url:d.resolveUnref(e),options:M(M({},V),o),cancel:()=>{x=!0}};if(s.beforeFetch&&Object.assign(Q,await s.beforeFetch(Q)),x||!a)return I(!1),Promise.resolve(null);let J=null;return F&&F.start(),new Promise((ge,we)=>{var On;a(Q.url,Z(M(M({},V),Q.options),{headers:M(M({},le(V.headers)),le((On=Q.options)==null?void 0:On.headers))})).then(async z=>{if(O.value=z,h.value=z.status,J=await z[u.type](),s.afterFetch&&h.value>=200&&h.value<300&&({data:J}=await s.afterFetch({data:J,response:z})),b.value=J,!z.ok)throw new Error(z.statusText);return f.trigger(z),ge(z)}).catch(async z=>{let Sn=z.message||z.name;return s.onFetchError&&({data:J,error:Sn}=await s.onFetchError({data:J,error:z,response:O.value})),b.value=J,_.value=Sn,v.trigger(z),N?we(z):ge(null)}).finally(()=>{I(!1),F&&F.stop(),w.trigger(null)})})},E=d.resolveRef(s.refetch);l.watch([E,d.resolveRef(e)],([N])=>N&&R(),{deep:!0});const C={isFinished:y,statusCode:h,response:O,error:_,data:b,isFetching:p,canAbort:$,aborted:m,abort:T,execute:R,onFetchResponse:f.on,onFetchError:v.on,onFetchFinally:w.on,get:j("GET"),put:j("PUT"),post:j("POST"),delete:j("DELETE"),patch:j("PATCH"),head:j("HEAD"),options:j("OPTIONS"),json:K("json"),text:K("text"),blob:K("blob"),arrayBuffer:K("arrayBuffer"),formData:K("formData")};function j(N){return(G,V)=>{if(!p.value){u.method=N,u.payload=G,u.payloadType=V,l.isRef(u.payload)&&l.watch([E,d.resolveRef(u.payload)],([Q])=>Q&&R(),{deep:!0});const x=d.resolveUnref(u.payload);return!V&&x&&Object.getPrototypeOf(x)===Object.prototype&&!(x instanceof FormData)&&(u.payloadType="json"),Z(M({},C),{then(Q,J){return H().then(Q,J)}})}}}function H(){return new Promise((N,G)=>{d.until(y).toBe(!0).then(()=>N(C)).catch(V=>G(V))})}function K(N){return()=>{if(!p.value)return u.type=N,Z(M({},C),{then(G,V){return H().then(G,V)}})}}return s.immediate&&setTimeout(R,0),Z(M({},C),{then(N,G){return H().then(N,G)}})}function Lo(e,t){return!e.endsWith("/")&&!t.startsWith("/")?`${e}/${t}`:`${e}${t}`}var Uo=Object.defineProperty,gt=Object.getOwnPropertySymbols,Mo=Object.prototype.hasOwnProperty,No=Object.prototype.propertyIsEnumerable,wt=(e,t,n)=>t in e?Uo(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Te=(e,t)=>{for(var n in t||(t={}))Mo.call(t,n)&&wt(e,n,t[n]);if(gt)for(var n of gt(t))No.call(t,n)&&wt(e,n,t[n]);return e};const Ho={multiple:!0,accept:"*"};function Wo(e={}){const{document:t=U}=e,n=l.ref(null);let r;t&&(r=t.createElement("input"),r.type="file",r.onchange=u=>{const a=u.target;n.value=a.files});const o=u=>{if(!r)return;const a=Te(Te(Te({},Ho),e),u);r.multiple=a.multiple,r.accept=a.accept,d.hasOwn(a,"capture")&&(r.capture=a.capture),r.click()},s=()=>{n.value=null,r&&(r.value="")};return{files:l.readonly(n),open:o,reset:s}}var Bo=Object.defineProperty,mt=Object.getOwnPropertySymbols,Vo=Object.prototype.hasOwnProperty,xo=Object.prototype.propertyIsEnumerable,ht=(e,t,n)=>t in e?Bo(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,ne=(e,t)=>{for(var n in t||(t={}))Vo.call(t,n)&&ht(e,n,t[n]);if(mt)for(var n of mt(t))xo.call(t,n)&&ht(e,n,t[n]);return e};function zo(e={}){const{window:t=A,dataType:n="Text"}=l.unref(e),r=t,o=L(()=>r&&"showSaveFilePicker"in r&&"showOpenFilePicker"in r),s=l.ref(),u=l.ref(),a=l.ref(),i=l.computed(()=>{var _,b;return(b=(_=a.value)==null?void 0:_.name)!=null?b:""}),c=l.computed(()=>{var _,b;return(b=(_=a.value)==null?void 0:_.type)!=null?b:""}),f=l.computed(()=>{var _,b;return(b=(_=a.value)==null?void 0:_.size)!=null?b:0}),v=l.computed(()=>{var _,b;return(b=(_=a.value)==null?void 0:_.lastModified)!=null?b:0});async function w(_={}){if(!o.value)return;const[b]=await r.showOpenFilePicker(ne(ne({},l.unref(e)),_));s.value=b,await h(),await O()}async function y(_={}){!o.value||(s.value=await r.showSaveFilePicker(ne(ne({},l.unref(e)),_)),u.value=void 0,await h(),await O())}async function p(_={}){if(!!o.value){if(!s.value)return m(_);if(u.value){const b=await s.value.createWritable();await b.write(u.value),await b.close()}await h()}}async function m(_={}){if(!!o.value){if(s.value=await r.showSaveFilePicker(ne(ne({},l.unref(e)),_)),u.value){const b=await s.value.createWritable();await b.write(u.value),await b.close()}await h()}}async function h(){var _;a.value=await((_=s.value)==null?void 0:_.getFile())}async function O(){var _,b;l.unref(n)==="Text"&&(u.value=await((_=a.value)==null?void 0:_.text())),l.unref(n)==="ArrayBuffer"&&(u.value=await((b=a.value)==null?void 0:b.arrayBuffer())),l.unref(n)==="Blob"&&(u.value=a.value)}return l.watch(()=>l.unref(n),O),{isSupported:o,data:u,file:a,fileName:i,fileMIME:c,fileSize:f,fileLastModified:v,open:w,create:y,save:p,saveAs:m,updateData:O}}function qo(e,t={}){const{initialValue:n=!1}=t,r=l.ref(!1),o=l.computed(()=>k(e));S(o,"focus",()=>r.value=!0),S(o,"blur",()=>r.value=!1);const s=l.computed({get:()=>r.value,set(u){var a,i;!u&&r.value?(a=o.value)==null||a.blur():u&&!r.value&&((i=o.value)==null||i.focus())}});return l.watch(o,()=>{s.value=n},{immediate:!0,flush:"post"}),{focused:s}}function Go(e,t={}){const n=je(t),r=l.computed(()=>k(e));return{focused:l.computed(()=>r.value&&n.value?r.value.contains(n.value):!1)}}function Xo(e){var t;const n=l.ref(0);if(typeof performance=="undefined")return n;const r=(t=e==null?void 0:e.every)!=null?t:10;let o=performance.now(),s=0;return D(()=>{if(s+=1,s>=r){const u=performance.now(),a=u-o;n.value=Math.round(1e3/(a/s)),o=u,s=0}}),n}const _t=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]];function Yo(e,t={}){const{document:n=U,autoExit:r=!1}=t,o=e||(n==null?void 0:n.querySelector("html")),s=l.ref(!1);let u=_t[0];const a=L(()=>{if(n){for(const m of _t)if(m[1]in n)return u=m,!0}else return!1;return!1}),[i,c,f,,v]=u;async function w(){!a.value||((n==null?void 0:n[f])&&await n[c](),s.value=!1)}async function y(){if(!a.value)return;await w();const m=k(o);m&&(await m[i](),s.value=!0)}async function p(){s.value?await w():await y()}return n&&S(n,v,()=>{s.value=!!(n==null?void 0:n[f])},!1),r&&d.tryOnScopeDispose(w),{isSupported:a,isFullscreen:s,enter:y,exit:w,toggle:p}}function Ko(e){return l.computed(()=>e.value?{buttons:{a:e.value.buttons[0],b:e.value.buttons[1],x:e.value.buttons[2],y:e.value.buttons[3]},bumper:{left:e.value.buttons[4],right:e.value.buttons[5]},triggers:{left:e.value.buttons[6],right:e.value.buttons[7]},stick:{left:{horizontal:e.value.axes[0],vertical:e.value.axes[1],button:e.value.buttons[10]},right:{horizontal:e.value.axes[2],vertical:e.value.axes[3],button:e.value.buttons[11]}},dpad:{up:e.value.buttons[12],down:e.value.buttons[13],left:e.value.buttons[14],right:e.value.buttons[15]},back:e.value.buttons[8],start:e.value.buttons[9]}:null)}function Qo(e={}){const{navigator:t=W}=e,n=L(()=>t&&"getGamepads"in t),r=l.ref([]),o=d.createEventHook(),s=d.createEventHook(),u=y=>{const p=[],m="vibrationActuator"in y?y.vibrationActuator:null;return m&&p.push(m),y.hapticActuators&&p.push(...y.hapticActuators),{id:y.id,hapticActuators:p,index:y.index,mapping:y.mapping,connected:y.connected,timestamp:y.timestamp,axes:y.axes.map(h=>h),buttons:y.buttons.map(h=>({pressed:h.pressed,touched:h.touched,value:h.value}))}},a=()=>{const y=(t==null?void 0:t.getGamepads())||[];for(let p=0;p<y.length;++p){const m=y[p];if(m){const h=r.value.findIndex(({index:O})=>O===m.index);h>-1&&(r.value[h]=u(m))}}},{isActive:i,pause:c,resume:f}=D(a),v=y=>{r.value.some(({index:p})=>p===y.index)||(r.value.push(u(y)),o.trigger(y.index)),f()},w=y=>{r.value=r.value.filter(p=>p.index!==y.index),s.trigger(y.index)};return S("gamepadconnected",y=>v(y.gamepad)),S("gamepaddisconnected",y=>w(y.gamepad)),d.tryOnMounted(()=>{const y=(t==null?void 0:t.getGamepads())||[];if(y)for(let p=0;p<y.length;++p){const m=y[p];m&&v(m)}}),c(),{isSupported:n,onConnected:o.on,onDisconnected:s.on,gamepads:r,pause:c,resume:f,isActive:i}}function Jo(e={}){const{enableHighAccuracy:t=!0,maximumAge:n=3e4,timeout:r=27e3,navigator:o=W,immediate:s=!0}=e,u=L(()=>o&&"geolocation"in o),a=l.ref(null),i=l.ref(null),c=l.ref({accuracy:0,latitude:1/0,longitude:1/0,altitude:null,altitudeAccuracy:null,heading:null,speed:null});function f(p){a.value=p.timestamp,c.value=p.coords,i.value=null}let v;function w(){u.value&&(v=o.geolocation.watchPosition(f,p=>i.value=p,{enableHighAccuracy:t,maximumAge:n,timeout:r}))}s&&w();function y(){v&&o&&o.geolocation.clearWatch(v)}return d.tryOnScopeDispose(()=>{y()}),{isSupported:u,coords:c,locatedAt:a,error:i,resume:w,pause:y}}const Zo=["mousemove","mousedown","resize","keydown","touchstart","wheel"],Do=6e4;function ea(e=Do,t={}){const{initialState:n=!1,listenForVisibilityChange:r=!0,events:o=Zo,window:s=A,eventFilter:u=d.throttleFilter(50)}=t,a=l.ref(n),i=l.ref(d.timestamp());let c;const f=d.createFilterWrapper(u,()=>{a.value=!1,i.value=d.timestamp(),clearTimeout(c),c=setTimeout(()=>a.value=!0,e)});if(s){const v=s.document;for(const w of o)S(s,w,f,{passive:!0});r&&S(v,"visibilitychange",()=>{v.hidden||f()})}return c=setTimeout(()=>a.value=!0,e),{idle:a,lastActive:i}}var ta=Object.defineProperty,bt=Object.getOwnPropertySymbols,na=Object.prototype.hasOwnProperty,ra=Object.prototype.propertyIsEnumerable,Ot=(e,t,n)=>t in e?ta(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,oa=(e,t)=>{for(var n in t||(t={}))na.call(t,n)&&Ot(e,n,t[n]);if(bt)for(var n of bt(t))ra.call(t,n)&&Ot(e,n,t[n]);return e};async function aa(e){return new Promise((t,n)=>{const r=new Image,{src:o,srcset:s,sizes:u}=e;r.src=o,s&&(r.srcset=s),u&&(r.sizes=u),r.onload=()=>t(r),r.onerror=n})}const la=(e,t={})=>{const n=Le(()=>aa(d.resolveUnref(e)),void 0,oa({resetOnExecute:!0},t));return l.watch(()=>d.resolveUnref(e),()=>n.execute(t.delay),{deep:!0}),n},St=1;function Pt(e,t={}){const{throttle:n=0,idle:r=200,onStop:o=d.noop,onScroll:s=d.noop,offset:u={left:0,right:0,top:0,bottom:0},eventListenerOptions:a={capture:!1,passive:!0},behavior:i="auto"}=t,c=l.ref(0),f=l.ref(0),v=l.computed({get(){return c.value},set($){y($,void 0)}}),w=l.computed({get(){return f.value},set($){y(void 0,$)}});function y($,P){var F,T,I;const R=d.resolveUnref(e);!R||(I=R instanceof Document?document.body:R)==null||I.scrollTo({top:(F=d.resolveUnref(P))!=null?F:w.value,left:(T=d.resolveUnref($))!=null?T:v.value,behavior:d.resolveUnref(i)})}const p=l.ref(!1),m=l.reactive({left:!0,right:!1,top:!0,bottom:!1}),h=l.reactive({left:!1,right:!1,top:!1,bottom:!1}),O=$=>{!p.value||(p.value=!1,h.left=!1,h.right=!1,h.top=!1,h.bottom=!1,o($))},_=d.useDebounceFn(O,n+r),b=$=>{const P=$.target===document?$.target.documentElement:$.target,F=P.scrollLeft;h.left=F<c.value,h.right=F>f.value,m.left=F<=0+(u.left||0),m.right=F+P.clientWidth>=P.scrollWidth-(u.right||0)-St,c.value=F;let T=P.scrollTop;$.target===document&&!T&&(T=document.body.scrollTop),h.top=T<f.value,h.bottom=T>f.value,m.top=T<=0+(u.top||0),m.bottom=T+P.clientHeight>=P.scrollHeight-(u.bottom||0)-St,f.value=T,p.value=!0,_($),s($)};return S(e,"scroll",n?d.useThrottleFn(b,n,!0,!1):b,a),S(e,"scrollend",O,a),{x:v,y:w,isScrolling:p,arrivedState:m,directions:h}}var sa=Object.defineProperty,ua=Object.defineProperties,ia=Object.getOwnPropertyDescriptors,Et=Object.getOwnPropertySymbols,ca=Object.prototype.hasOwnProperty,fa=Object.prototype.propertyIsEnumerable,$t=(e,t,n)=>t in e?sa(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Tt=(e,t)=>{for(var n in t||(t={}))ca.call(t,n)&&$t(e,n,t[n]);if(Et)for(var n of Et(t))fa.call(t,n)&&$t(e,n,t[n]);return e},da=(e,t)=>ua(e,ia(t));function va(e,t,n={}){var r,o;const s=(r=n.direction)!=null?r:"bottom",u=l.reactive(Pt(e,da(Tt({},n),{offset:Tt({[s]:(o=n.distance)!=null?o:0},n.offset)})));l.watch(()=>u.arrivedState[s],async a=>{var i,c;if(a){const f=d.resolveUnref(e),v={height:(i=f==null?void 0:f.scrollHeight)!=null?i:0,width:(c=f==null?void 0:f.scrollWidth)!=null?c:0};await t(u),n.preserveScrollPosition&&f&&l.nextTick(()=>{f.scrollTo({top:f.scrollHeight-v.height,left:f.scrollWidth-v.width})})}})}function pa(e,t,n={}){const{root:r,rootMargin:o="0px",threshold:s=.1,window:u=A}=n,a=L(()=>u&&"IntersectionObserver"in u);let i=d.noop;const c=a.value?l.watch(()=>({el:k(e),root:k(r)}),({el:v,root:w})=>{if(i(),!v)return;const y=new IntersectionObserver(t,{root:w,rootMargin:o,threshold:s});y.observe(v),i=()=>{y.disconnect(),i=d.noop}},{immediate:!0,flush:"post"}):d.noop,f=()=>{i(),c()};return d.tryOnScopeDispose(f),{isSupported:a,stop:f}}const ya=["mousedown","mouseup","keydown","keyup"];function ga(e,t={}){const{events:n=ya,document:r=U,initial:o=null}=t,s=l.ref(o);return r&&n.forEach(u=>{S(r,u,a=>{typeof a.getModifierState=="function"&&(s.value=a.getModifierState(e))})}),s}function wa(e,t,n={}){const{window:r=A}=n;return ce(e,t,r==null?void 0:r.localStorage,n)}const Ft={ctrl:"control",command:"meta",cmd:"meta",option:"alt",up:"arrowup",down:"arrowdown",left:"arrowleft",right:"arrowright"};function ma(e={}){const{reactive:t=!1,target:n=A,aliasMap:r=Ft,passive:o=!0,onEventFired:s=d.noop}=e,u=l.reactive(new Set),a={toJSON(){return{}},current:u},i=t?l.reactive(a):a,c=new Set,f=new Set;function v(m,h){m in i&&(t?i[m]=h:i[m].value=h)}function w(){u.clear();for(const m of f)v(m,!1)}function y(m,h){var O,_;const b=(O=m.key)==null?void 0:O.toLowerCase(),P=[(_=m.code)==null?void 0:_.toLowerCase(),b].filter(Boolean);b&&(h?u.add(b):u.delete(b));for(const F of P)f.add(F),v(F,h);b==="meta"&&!h?(c.forEach(F=>{u.delete(F),v(F,!1)}),c.clear()):typeof m.getModifierState=="function"&&m.getModifierState("Meta")&&h&&[...u,...P].forEach(F=>c.add(F))}S(n,"keydown",m=>(y(m,!0),s(m)),{passive:o}),S(n,"keyup",m=>(y(m,!1),s(m)),{passive:o}),S("blur",w,{passive:!0}),S("focus",w,{passive:!0});const p=new Proxy(i,{get(m,h,O){if(typeof h!="string")return Reflect.get(m,h,O);if(h=h.toLowerCase(),h in r&&(h=r[h]),!(h in i))if(/[+_-]/.test(h)){const b=h.split(/[+_-]/g).map($=>$.trim());i[h]=l.computed(()=>b.every($=>l.unref(p[$])))}else i[h]=l.ref(!1);const _=Reflect.get(m,h,O);return t?l.unref(_):_}});return p}var ha=Object.defineProperty,Rt=Object.getOwnPropertySymbols,_a=Object.prototype.hasOwnProperty,ba=Object.prototype.propertyIsEnumerable,It=(e,t,n)=>t in e?ha(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Ct=(e,t)=>{for(var n in t||(t={}))_a.call(t,n)&&It(e,n,t[n]);if(Rt)for(var n of Rt(t))ba.call(t,n)&&It(e,n,t[n]);return e};function Fe(e,t){d.resolveUnref(e)&&t(d.resolveUnref(e))}function Oa(e){let t=[];for(let n=0;n<e.length;++n)t=[...t,[e.start(n),e.end(n)]];return t}function Re(e){return Array.from(e).map(({label:t,kind:n,language:r,mode:o,activeCues:s,cues:u,inBandMetadataTrackDispatchType:a},i)=>({id:i,label:t,kind:n,language:r,mode:o,activeCues:s,cues:u,inBandMetadataTrackDispatchType:a}))}const Sa={src:"",tracks:[]};function Pa(e,t={}){t=Ct(Ct({},Sa),t);const{document:n=U}=t,r=l.ref(0),o=l.ref(0),s=l.ref(!1),u=l.ref(1),a=l.ref(!1),i=l.ref(!1),c=l.ref(!1),f=l.ref(1),v=l.ref(!1),w=l.ref([]),y=l.ref([]),p=l.ref(-1),m=l.ref(!1),h=l.ref(!1),O=n&&"pictureInPictureEnabled"in n,_=d.createEventHook(),b=E=>{Fe(e,C=>{if(E){const j=d.isNumber(E)?E:E.id;C.textTracks[j].mode="disabled"}else for(let j=0;j<C.textTracks.length;++j)C.textTracks[j].mode="disabled";p.value=-1})},$=(E,C=!0)=>{Fe(e,j=>{const H=d.isNumber(E)?E:E.id;C&&b(),j.textTracks[H].mode="showing",p.value=H})},P=()=>new Promise((E,C)=>{Fe(e,async j=>{O&&(m.value?n.exitPictureInPicture().then(E).catch(C):j.requestPictureInPicture().then(E).catch(C))})});l.watchEffect(()=>{if(!n)return;const E=d.resolveUnref(e);if(!E)return;const C=d.resolveUnref(t.src);let j=[];!C||(d.isString(C)?j=[{src:C}]:Array.isArray(C)?j=C:d.isObject(C)&&(j=[C]),E.querySelectorAll("source").forEach(H=>{H.removeEventListener("error",_.trigger),H.remove()}),j.forEach(({src:H,type:K})=>{const N=n.createElement("source");N.setAttribute("src",H),N.setAttribute("type",K||""),N.addEventListener("error",_.trigger),E.appendChild(N)}),E.load())}),d.tryOnScopeDispose(()=>{const E=d.resolveUnref(e);!E||E.querySelectorAll("source").forEach(C=>C.removeEventListener("error",_.trigger))}),l.watch(u,E=>{const C=d.resolveUnref(e);!C||(C.volume=E)}),l.watch(h,E=>{const C=d.resolveUnref(e);!C||(C.muted=E)}),l.watch(f,E=>{const C=d.resolveUnref(e);!C||(C.playbackRate=E)}),l.watchEffect(()=>{if(!n)return;const E=d.resolveUnref(t.tracks),C=d.resolveUnref(e);!E||!E.length||!C||(C.querySelectorAll("track").forEach(j=>j.remove()),E.forEach(({default:j,kind:H,label:K,src:N,srcLang:G},V)=>{const x=n.createElement("track");x.default=j||!1,x.kind=H,x.label=K,x.src=N,x.srclang=G,x.default&&(p.value=V),C.appendChild(x)}))});const{ignoreUpdates:F}=d.watchIgnorable(r,E=>{const C=d.resolveUnref(e);!C||(C.currentTime=E)}),{ignoreUpdates:T}=d.watchIgnorable(c,E=>{const C=d.resolveUnref(e);!C||(E?C.play():C.pause())});S(e,"timeupdate",()=>F(()=>r.value=d.resolveUnref(e).currentTime)),S(e,"durationchange",()=>o.value=d.resolveUnref(e).duration),S(e,"progress",()=>w.value=Oa(d.resolveUnref(e).buffered)),S(e,"seeking",()=>s.value=!0),S(e,"seeked",()=>s.value=!1),S(e,"waiting",()=>a.value=!0),S(e,"playing",()=>{a.value=!1,i.value=!1}),S(e,"ratechange",()=>f.value=d.resolveUnref(e).playbackRate),S(e,"stalled",()=>v.value=!0),S(e,"ended",()=>i.value=!0),S(e,"pause",()=>T(()=>c.value=!1)),S(e,"play",()=>T(()=>c.value=!0)),S(e,"enterpictureinpicture",()=>m.value=!0),S(e,"leavepictureinpicture",()=>m.value=!1),S(e,"volumechange",()=>{const E=d.resolveUnref(e);!E||(u.value=E.volume,h.value=E.muted)});const I=[],R=l.watch([e],()=>{const E=d.resolveUnref(e);!E||(R(),I[0]=S(E.textTracks,"addtrack",()=>y.value=Re(E.textTracks)),I[1]=S(E.textTracks,"removetrack",()=>y.value=Re(E.textTracks)),I[2]=S(E.textTracks,"change",()=>y.value=Re(E.textTracks)))});return d.tryOnScopeDispose(()=>I.forEach(E=>E())),{currentTime:r,duration:o,waiting:a,seeking:s,ended:i,stalled:v,buffered:w,playing:c,rate:f,volume:u,muted:h,tracks:y,selectedTrack:p,enableTrack:$,disableTrack:b,supportsPictureInPicture:O,togglePictureInPicture:P,isPictureInPicture:m,onSourceError:_.on}}const Ea=()=>{const e=l.reactive({});return{get:t=>e[t],set:(t,n)=>l.set(e,t,n),has:t=>d.hasOwn(e,t),delete:t=>l.del(e,t),clear:()=>{Object.keys(e).forEach(t=>{l.del(e,t)})}}};function $a(e,t){const r=(()=>(t==null?void 0:t.cache)?l.reactive(t.cache):l.isVue2?Ea():l.reactive(new Map))(),o=(...f)=>(t==null?void 0:t.getKey)?t.getKey(...f):JSON.stringify(f),s=(f,...v)=>(r.set(f,e(...v)),r.get(f)),u=(...f)=>s(o(...f),...f),a=(...f)=>{r.delete(o(...f))},i=()=>{r.clear()},c=(...f)=>{const v=o(...f);return r.has(v)?r.get(v):s(v,...f)};return c.load=u,c.delete=a,c.clear=i,c.generateKey=o,c.cache=r,c}function Ta(e={}){const t=l.ref(),n=L(()=>typeof performance!="undefined"&&"memory"in performance);if(n.value){const{interval:r=1e3}=e;d.useIntervalFn(()=>{t.value=performance.memory},r,{immediate:e.immediate,immediateCallback:e.immediateCallback})}return{isSupported:n,memory:t}}function Fa(){const e=l.ref(!1);return l.onMounted(()=>{e.value=!0}),e}function At(e={}){const{type:t="page",touch:n=!0,resetOnTouchEnds:r=!1,initialValue:o={x:0,y:0},window:s=A,eventFilter:u}=e,a=l.ref(o.x),i=l.ref(o.y),c=l.ref(null),f=m=>{t==="page"?(a.value=m.pageX,i.value=m.pageY):t==="client"?(a.value=m.clientX,i.value=m.clientY):t==="movement"&&(a.value=m.movementX,i.value=m.movementY),c.value="mouse"},v=()=>{a.value=o.x,i.value=o.y},w=m=>{if(m.touches.length>0){const h=m.touches[0];t==="page"?(a.value=h.pageX,i.value=h.pageY):t==="client"&&(a.value=h.clientX,i.value=h.clientY),c.value="touch"}},y=m=>u===void 0?f(m):u(()=>f(m),{}),p=m=>u===void 0?w(m):u(()=>w(m),{});return s&&(S(s,"mousemove",y,{passive:!0}),S(s,"dragover",y,{passive:!0}),n&&t!=="movement"&&(S(s,"touchstart",p,{passive:!0}),S(s,"touchmove",p,{passive:!0}),r&&S(s,"touchend",v,{passive:!0}))),{x:a,y:i,sourceType:c}}function kt(e,t={}){const{handleOutside:n=!0,window:r=A}=t,{x:o,y:s,sourceType:u}=At(t),a=l.ref(e??(r==null?void 0:r.document.body)),i=l.ref(0),c=l.ref(0),f=l.ref(0),v=l.ref(0),w=l.ref(0),y=l.ref(0),p=l.ref(!0);let m=()=>{};return r&&(m=l.watch([a,o,s],()=>{const h=k(a);if(!h)return;const{left:O,top:_,width:b,height:$}=h.getBoundingClientRect();f.value=O+r.pageXOffset,v.value=_+r.pageYOffset,w.value=$,y.value=b;const P=o.value-f.value,F=s.value-v.value;p.value=b===0||$===0||P<0||F<0||P>b||F>$,(n||!p.value)&&(i.value=P,c.value=F)},{immediate:!0}),S(document,"mouseleave",()=>{p.value=!0})),{x:o,y:s,sourceType:u,elementX:i,elementY:c,elementPositionX:f,elementPositionY:v,elementHeight:w,elementWidth:y,isOutside:p,stop:m}}function Ra(e={}){const{touch:t=!0,drag:n=!0,initialValue:r=!1,window:o=A}=e,s=l.ref(r),u=l.ref(null);if(!o)return{pressed:s,sourceType:u};const a=f=>()=>{s.value=!0,u.value=f},i=()=>{s.value=!1,u.value=null},c=l.computed(()=>k(e.target)||o);return S(c,"mousedown",a("mouse"),{passive:!0}),S(o,"mouseleave",i,{passive:!0}),S(o,"mouseup",i,{passive:!0}),n&&(S(c,"dragstart",a("mouse"),{passive:!0}),S(o,"drop",i,{passive:!0}),S(o,"dragend",i,{passive:!0})),t&&(S(c,"touchstart",a("touch"),{passive:!0}),S(o,"touchend",i,{passive:!0}),S(o,"touchcancel",i,{passive:!0})),{pressed:s,sourceType:u}}var jt=Object.getOwnPropertySymbols,Ia=Object.prototype.hasOwnProperty,Ca=Object.prototype.propertyIsEnumerable,Aa=(e,t)=>{var n={};for(var r in e)Ia.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&jt)for(var r of jt(e))t.indexOf(r)<0&&Ca.call(e,r)&&(n[r]=e[r]);return n};function Ie(e,t,n={}){const r=n,{window:o=A}=r,s=Aa(r,["window"]);let u;const a=L(()=>o&&"MutationObserver"in o),i=()=>{u&&(u.disconnect(),u=void 0)},c=l.watch(()=>k(e),v=>{i(),a.value&&o&&v&&(u=new MutationObserver(t),u.observe(v,s))},{immediate:!0}),f=()=>{i(),c()};return d.tryOnScopeDispose(f),{isSupported:a,stop:f}}const ka=(e={})=>{const{window:t=A}=e,n=t==null?void 0:t.navigator,r=L(()=>n&&"language"in n),o=l.ref(n==null?void 0:n.language);return S(t,"languagechange",()=>{n&&(o.value=n.language)}),{isSupported:r,language:o}};function Lt(e={}){const{window:t=A}=e,n=t==null?void 0:t.navigator,r=L(()=>n&&"connection"in n),o=l.ref(!0),s=l.ref(!1),u=l.ref(void 0),a=l.ref(void 0),i=l.ref(void 0),c=l.ref(void 0),f=l.ref(void 0),v=l.ref(void 0),w=l.ref("unknown"),y=r.value&&n.connection;function p(){!n||(o.value=n.onLine,u.value=o.value?void 0:Date.now(),a.value=o.value?Date.now():void 0,y&&(i.value=y.downlink,c.value=y.downlinkMax,v.value=y.effectiveType,f.value=y.rtt,s.value=y.saveData,w.value=y.type))}return t&&(S(t,"offline",()=>{o.value=!1,u.value=Date.now()}),S(t,"online",()=>{o.value=!0,a.value=Date.now()})),y&&S(y,"change",p,!1),p(),{isSupported:r,isOnline:o,saveData:s,offlineAt:u,onlineAt:a,downlink:i,downlinkMax:c,effectiveType:v,rtt:f,type:w}}var ja=Object.defineProperty,Ut=Object.getOwnPropertySymbols,La=Object.prototype.hasOwnProperty,Ua=Object.prototype.propertyIsEnumerable,Mt=(e,t,n)=>t in e?ja(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Ma=(e,t)=>{for(var n in t||(t={}))La.call(t,n)&&Mt(e,n,t[n]);if(Ut)for(var n of Ut(t))Ua.call(t,n)&&Mt(e,n,t[n]);return e};function Nt(e={}){const{controls:t=!1,interval:n="requestAnimationFrame"}=e,r=l.ref(new Date),o=()=>r.value=new Date,s=n==="requestAnimationFrame"?D(o,{immediate:!0}):d.useIntervalFn(o,n,{immediate:!0});return t?Ma({now:r},s):r}function Na(e){const t=l.ref(),n=()=>{t.value&&URL.revokeObjectURL(t.value),t.value=void 0};return l.watch(()=>l.unref(e),r=>{n(),r&&(t.value=URL.createObjectURL(r))},{immediate:!0}),d.tryOnScopeDispose(n),l.readonly(t)}function Ht(e,t,n){if(d.isFunction(e)||l.isReadonly(e))return l.computed(()=>d.clamp(d.resolveUnref(e),d.resolveUnref(t),d.resolveUnref(n)));const r=l.ref(e);return l.computed({get(){return r.value=d.clamp(r.value,d.resolveUnref(t),d.resolveUnref(n))},set(o){r.value=d.clamp(o,d.resolveUnref(t),d.resolveUnref(n))}})}function Ha(e){const{total:t=1/0,pageSize:n=10,page:r=1,onPageChange:o=d.noop,onPageSizeChange:s=d.noop,onPageCountChange:u=d.noop}=e,a=Ht(n,1,1/0),i=l.computed(()=>Math.max(1,Math.ceil(l.unref(t)/l.unref(a)))),c=Ht(r,1,i),f=l.computed(()=>c.value===1),v=l.computed(()=>c.value===i.value);l.isRef(r)&&d.syncRef(r,c),l.isRef(n)&&d.syncRef(n,a);function w(){c.value--}function y(){c.value++}const p={currentPage:c,currentPageSize:a,pageCount:i,isFirstPage:f,isLastPage:v,prev:w,next:y};return l.watch(c,()=>{o(l.reactive(p))}),l.watch(a,()=>{s(l.reactive(p))}),l.watch(i,()=>{u(l.reactive(p))}),p}function Wa(e={}){const{isOnline:t}=Lt(e);return t}function Ba(e={}){const{window:t=A}=e,n=l.ref(!1),r=o=>{if(!t)return;o=o||t.event;const s=o.relatedTarget||o.toElement;n.value=!s};return t&&(S(t,"mouseout",r,{passive:!0}),S(t.document,"mouseleave",r,{passive:!0}),S(t.document,"mouseenter",r,{passive:!0})),n}function Va(e,t={}){const{deviceOrientationTiltAdjust:n=m=>m,deviceOrientationRollAdjust:r=m=>m,mouseTiltAdjust:o=m=>m,mouseRollAdjust:s=m=>m,window:u=A}=t,a=l.reactive(at({window:u})),{elementX:i,elementY:c,elementWidth:f,elementHeight:v}=kt(e,{handleOutside:!1,window:u}),w=l.computed(()=>a.isSupported&&(a.alpha!=null&&a.alpha!==0||a.gamma!=null&&a.gamma!==0)?"deviceOrientation":"mouse"),y=l.computed(()=>{if(w.value==="deviceOrientation"){const m=-a.beta/90;return r(m)}else{const m=-(c.value-v.value/2)/v.value;return s(m)}}),p=l.computed(()=>{if(w.value==="deviceOrientation"){const m=a.gamma/90;return n(m)}else{const m=(i.value-f.value/2)/f.value;return o(m)}});return{roll:y,tilt:p,source:w}}var xa=Object.defineProperty,za=Object.defineProperties,qa=Object.getOwnPropertyDescriptors,Wt=Object.getOwnPropertySymbols,Ga=Object.prototype.hasOwnProperty,Xa=Object.prototype.propertyIsEnumerable,Bt=(e,t,n)=>t in e?xa(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Ya=(e,t)=>{for(var n in t||(t={}))Ga.call(t,n)&&Bt(e,n,t[n]);if(Wt)for(var n of Wt(t))Xa.call(t,n)&&Bt(e,n,t[n]);return e},Ka=(e,t)=>za(e,qa(t));const Vt={x:0,y:0,pointerId:0,pressure:0,tiltX:0,tiltY:0,width:0,height:0,twist:0,pointerType:null},Qa=Object.keys(Vt);function Ja(e={}){const{target:t=A}=e,n=l.ref(!1),r=l.ref(e.initialValue||{});Object.assign(r.value,Vt,r.value);const o=s=>{n.value=!0,!(e.pointerTypes&&!e.pointerTypes.includes(s.pointerType))&&(r.value=d.objectPick(s,Qa,!1))};return t&&(S(t,"pointerdown",o,{passive:!0}),S(t,"pointermove",o,{passive:!0}),S(t,"pointerleave",()=>n.value=!1,{passive:!0})),Ka(Ya({},d.toRefs(r)),{isInside:n})}function Za(e,t={}){const{document:n=U,pointerLockOptions:r}=t,o=L(()=>n&&"pointerLockElement"in n),s=l.ref(),u=l.ref();let a;o.value&&(S(n,"pointerlockchange",()=>{var f;const v=(f=n.pointerLockElement)!=null?f:s.value;a&&v===a&&(s.value=n.pointerLockElement,s.value||(a=u.value=null))}),S(n,"pointerlockerror",()=>{var f;const v=(f=n.pointerLockElement)!=null?f:s.value;if(a&&v===a){const w=n.pointerLockElement?"release":"acquire";throw new Error(`Failed to ${w} pointer lock.`)}}));async function i(f,v){var w;if(!o.value)throw new Error("Pointer Lock API is not supported by your browser.");if(u.value=f instanceof Event?f.currentTarget:null,a=f instanceof Event?(w=k(e))!=null?w:u.value:k(f),!a)throw new Error("Target element undefined.");return a.requestPointerLock(v??r),await d.until(s).toBe(a)}async function c(){return s.value?(n.exitPointerLock(),await d.until(s).toBeNull(),!0):!1}return{isSupported:o,element:s,triggerElement:u,lock:i,unlock:c}}g.SwipeDirection=void 0,function(e){e.UP="UP",e.RIGHT="RIGHT",e.DOWN="DOWN",e.LEFT="LEFT",e.NONE="NONE"}(g.SwipeDirection||(g.SwipeDirection={}));function Da(e,t={}){const{threshold:n=50,onSwipe:r,onSwipeEnd:o,onSwipeStart:s,passive:u=!0,window:a=A}=t,i=l.reactive({x:0,y:0}),c=l.reactive({x:0,y:0}),f=l.computed(()=>i.x-c.x),v=l.computed(()=>i.y-c.y),{max:w,abs:y}=Math,p=l.computed(()=>w(y(f.value),y(v.value))>=n),m=l.ref(!1),h=l.computed(()=>p.value?y(f.value)>y(v.value)?f.value>0?g.SwipeDirection.LEFT:g.SwipeDirection.RIGHT:v.value>0?g.SwipeDirection.UP:g.SwipeDirection.DOWN:g.SwipeDirection.NONE),O=R=>[R.touches[0].clientX,R.touches[0].clientY],_=(R,E)=>{i.x=R,i.y=E},b=(R,E)=>{c.x=R,c.y=E};let $;const P=el(a==null?void 0:a.document);u?$=P?{passive:!0}:{capture:!1}:$=P?{passive:!1,capture:!0}:{capture:!0};const F=R=>{m.value&&(o==null||o(R,h.value)),m.value=!1},T=[S(e,"touchstart",R=>{$.capture&&!$.passive&&R.preventDefault();const[E,C]=O(R);_(E,C),b(E,C),s==null||s(R)},$),S(e,"touchmove",R=>{const[E,C]=O(R);b(E,C),!m.value&&p.value&&(m.value=!0),m.value&&(r==null||r(R))},$),S(e,"touchend",F,$),S(e,"touchcancel",F,$)];return{isPassiveEventSupported:P,isSwiping:m,direction:h,coordsStart:i,coordsEnd:c,lengthX:f,lengthY:v,stop:()=>T.forEach(R=>R())}}function el(e){if(!e)return!1;let t=!1;const n={get passive(){return t=!0,!1}};return e.addEventListener("x",d.noop,n),e.removeEventListener("x",d.noop),t}function tl(e,t={}){const n=d.resolveRef(e),{threshold:r=50,onSwipe:o,onSwipeEnd:s,onSwipeStart:u}=t,a=l.reactive({x:0,y:0}),i=(F,T)=>{a.x=F,a.y=T},c=l.reactive({x:0,y:0}),f=(F,T)=>{c.x=F,c.y=T},v=l.computed(()=>a.x-c.x),w=l.computed(()=>a.y-c.y),{max:y,abs:p}=Math,m=l.computed(()=>y(p(v.value),p(w.value))>=r),h=l.ref(!1),O=l.ref(!1),_=l.computed(()=>m.value?p(v.value)>p(w.value)?v.value>0?g.SwipeDirection.LEFT:g.SwipeDirection.RIGHT:w.value>0?g.SwipeDirection.UP:g.SwipeDirection.DOWN:g.SwipeDirection.NONE),b=F=>{var T,I,R;const E=F.buttons===0,C=F.buttons===1;return(R=(I=(T=t.pointerTypes)==null?void 0:T.includes(F.pointerType))!=null?I:E||C)!=null?R:!0},$=[S(e,"pointerdown",F=>{var T,I;if(!b(F))return;O.value=!0,(I=(T=n.value)==null?void 0:T.style)==null||I.setProperty("touch-action","none");const R=F.target;R==null||R.setPointerCapture(F.pointerId);const{clientX:E,clientY:C}=F;i(E,C),f(E,C),u==null||u(F)}),S(e,"pointermove",F=>{if(!b(F)||!O.value)return;const{clientX:T,clientY:I}=F;f(T,I),!h.value&&m.value&&(h.value=!0),h.value&&(o==null||o(F))}),S(e,"pointerup",F=>{var T,I;!b(F)||(h.value&&(s==null||s(F,_.value)),O.value=!1,h.value=!1,(I=(T=n.value)==null?void 0:T.style)==null||I.setProperty("touch-action","initial"))})],P=()=>$.forEach(F=>F());return{isSwiping:l.readonly(h),direction:l.readonly(_),posStart:l.readonly(a),posEnd:l.readonly(c),distanceX:v,distanceY:w,stop:P}}function nl(e){const t=q("(prefers-color-scheme: light)",e),n=q("(prefers-color-scheme: dark)",e);return l.computed(()=>n.value?"dark":t.value?"light":"no-preference")}function rl(e){const t=q("(prefers-contrast: more)",e),n=q("(prefers-contrast: less)",e),r=q("(prefers-contrast: custom)",e);return l.computed(()=>t.value?"more":n.value?"less":r.value?"custom":"no-preference")}function ol(e={}){const{window:t=A}=e;if(!t)return l.ref(["en"]);const n=t.navigator,r=l.ref(n.languages);return S(t,"languagechange",()=>{r.value=n.languages}),r}function al(e){const t=q("(prefers-reduced-motion: reduce)",e);return l.computed(()=>t.value?"reduce":"no-preference")}function ll(e,t){const n=l.shallowRef(t);return l.watch(d.resolveRef(e),(r,o)=>{n.value=o},{flush:"sync"}),l.readonly(n)}const sl=(e={})=>{const{window:t=A}=e,n=L(()=>t&&"screen"in t&&"orientation"in t.screen),r=n.value?t.screen.orientation:{},o=l.ref(r.type),s=l.ref(r.angle||0);return n.value&&S(t,"orientationchange",()=>{o.value=r.type,s.value=r.angle}),{isSupported:n,orientation:o,angle:s,lockOrientation:i=>n.value?r.lock(i):Promise.reject(new Error("Not supported")),unlockOrientation:()=>{n.value&&r.unlock()}}},xt="--vueuse-safe-area-top",zt="--vueuse-safe-area-right",qt="--vueuse-safe-area-bottom",Gt="--vueuse-safe-area-left";function ul(){const e=l.ref(""),t=l.ref(""),n=l.ref(""),r=l.ref("");if(d.isClient){const s=ae(xt),u=ae(zt),a=ae(qt),i=ae(Gt);s.value="env(safe-area-inset-top, 0px)",u.value="env(safe-area-inset-right, 0px)",a.value="env(safe-area-inset-bottom, 0px)",i.value="env(safe-area-inset-left, 0px)",o(),S("resize",d.useDebounceFn(o))}function o(){e.value=ve(xt),t.value=ve(zt),n.value=ve(qt),r.value=ve(Gt)}return{top:e,right:t,bottom:n,left:r,update:o}}function ve(e){return getComputedStyle(document.documentElement).getPropertyValue(e)}function il(e,t=d.noop,n={}){const{immediate:r=!0,manual:o=!1,type:s="text/javascript",async:u=!0,crossOrigin:a,referrerPolicy:i,noModule:c,defer:f,document:v=U,attrs:w={}}=n,y=l.ref(null);let p=null;const m=_=>new Promise((b,$)=>{const P=I=>(y.value=I,b(I),I);if(!v){b(!1);return}let F=!1,T=v.querySelector(`script[src="${d.resolveUnref(e)}"]`);T?T.hasAttribute("data-loaded")&&P(T):(T=v.createElement("script"),T.type=s,T.async=u,T.src=d.resolveUnref(e),f&&(T.defer=f),a&&(T.crossOrigin=a),c&&(T.noModule=c),i&&(T.referrerPolicy=i),Object.entries(w).forEach(([I,R])=>T==null?void 0:T.setAttribute(I,R)),F=!0),T.addEventListener("error",I=>$(I)),T.addEventListener("abort",I=>$(I)),T.addEventListener("load",()=>{T.setAttribute("data-loaded","true"),t(T),P(T)}),F&&(T=v.head.appendChild(T)),_||P(T)}),h=(_=!0)=>(p||(p=m(_)),p),O=()=>{if(!v)return;p=null,y.value&&(y.value=null);const _=v.querySelector(`script[src="${d.resolveUnref(e)}"]`);_&&v.head.removeChild(_)};return r&&!o&&d.tryOnMounted(h),o||d.tryOnUnmounted(O),{scriptTag:y,load:h,unload:O}}function Xt(e){const t=window.getComputedStyle(e);if(t.overflowX==="scroll"||t.overflowY==="scroll"||t.overflowX==="auto"&&e.clientHeight<e.scrollHeight||t.overflowY==="auto"&&e.clientWidth<e.scrollWidth)return!0;{const n=e.parentNode;return!n||n.tagName==="BODY"?!1:Xt(n)}}function cl(e){const t=e||window.event,n=t.target;return Xt(n)?!1:t.touches.length>1?!0:(t.preventDefault&&t.preventDefault(),!1)}function fl(e,t=!1){const n=l.ref(t);let r=null,o;l.watch(d.resolveRef(e),a=>{if(a){const i=a;o=i.style.overflow,n.value&&(i.style.overflow="hidden")}},{immediate:!0});const s=()=>{const a=d.resolveUnref(e);!a||n.value||(d.isIOS&&(r=S(a,"touchmove",i=>{cl(i)},{passive:!1})),a.style.overflow="hidden",n.value=!0)},u=()=>{const a=d.resolveUnref(e);!a||!n.value||(d.isIOS&&(r==null||r()),a.style.overflow=o,n.value=!1)};return d.tryOnScopeDispose(u),l.computed({get(){return n.value},set(a){a?s():u()}})}function dl(e,t,n={}){const{window:r=A}=n;return ce(e,t,r==null?void 0:r.sessionStorage,n)}var vl=Object.defineProperty,Yt=Object.getOwnPropertySymbols,pl=Object.prototype.hasOwnProperty,yl=Object.prototype.propertyIsEnumerable,Kt=(e,t,n)=>t in e?vl(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Qt=(e,t)=>{for(var n in t||(t={}))pl.call(t,n)&&Kt(e,n,t[n]);if(Yt)for(var n of Yt(t))yl.call(t,n)&&Kt(e,n,t[n]);return e};function gl(e={},t={}){const{navigator:n=W}=t,r=n,o=L(()=>r&&"canShare"in r);return{isSupported:o,share:async(u={})=>{if(o.value){const a=Qt(Qt({},d.resolveUnref(e)),d.resolveUnref(u));let i=!0;if(a.files&&r.canShare&&(i=r.canShare({files:a.files})),i)return r.share(a)}}}}const wl=(e,t)=>e.sort(t),pe=(e,t)=>e-t;function ml(...e){var t,n,r,o;const[s]=e;let u=pe,a={};e.length===2?typeof e[1]=="object"?(a=e[1],u=(t=a.compareFn)!=null?t:pe):u=(n=e[1])!=null?n:pe:e.length>2&&(u=(r=e[1])!=null?r:pe,a=(o=e[2])!=null?o:{});const{dirty:i=!1,sortFn:c=wl}=a;return i?(l.watchEffect(()=>{const f=c(l.unref(s),u);l.isRef(s)?s.value=f:s.splice(0,s.length,...f)}),s):l.computed(()=>c([...l.unref(s)],u))}function hl(e={}){const{interimResults:t=!0,continuous:n=!0,window:r=A}=e,o=d.resolveRef(e.lang||"en-US"),s=l.ref(!1),u=l.ref(!1),a=l.ref(""),i=l.shallowRef(void 0),c=(m=!s.value)=>{s.value=m},f=()=>{s.value=!0},v=()=>{s.value=!1},w=r&&(r.SpeechRecognition||r.webkitSpeechRecognition),y=L(()=>w);let p;return y.value&&(p=new w,p.continuous=n,p.interimResults=t,p.lang=l.unref(o),p.onstart=()=>{u.value=!1},l.watch(o,m=>{p&&!s.value&&(p.lang=m)}),p.onresult=m=>{const h=Array.from(m.results).map(O=>(u.value=O.isFinal,O[0])).map(O=>O.transcript).join("");a.value=h,i.value=void 0},p.onerror=m=>{i.value=m},p.onend=()=>{s.value=!1,p.lang=l.unref(o)},l.watch(s,()=>{s.value?p.start():p.stop()})),d.tryOnScopeDispose(()=>{s.value=!1}),{isSupported:y,isListening:s,isFinal:u,recognition:p,result:a,error:i,toggle:c,start:f,stop:v}}function _l(e,t={}){const{pitch:n=1,rate:r=1,volume:o=1,window:s=A}=t,u=s&&s.speechSynthesis,a=L(()=>u),i=l.ref(!1),c=l.ref("init"),f=d.resolveRef(e||""),v=d.resolveRef(t.lang||"en-US"),w=l.shallowRef(void 0),y=(_=!i.value)=>{i.value=_},p=_=>{_.lang=l.unref(v),_.voice=l.unref(t.voice)||null,_.pitch=n,_.rate=r,_.volume=o,_.onstart=()=>{i.value=!0,c.value="play"},_.onpause=()=>{i.value=!1,c.value="pause"},_.onresume=()=>{i.value=!0,c.value="play"},_.onend=()=>{i.value=!1,c.value="end"},_.onerror=b=>{w.value=b}},m=l.computed(()=>{i.value=!1,c.value="init";const _=new SpeechSynthesisUtterance(f.value);return p(_),_}),h=()=>{u.cancel(),m&&u.speak(m.value)},O=()=>{u.cancel(),i.value=!1};return a.value&&(p(m.value),l.watch(v,_=>{m.value&&!i.value&&(m.value.lang=_)}),t.voice&&l.watch(t.voice,()=>{u.cancel()}),l.watch(i,()=>{i.value?u.resume():u.pause()})),d.tryOnScopeDispose(()=>{i.value=!1}),{isSupported:a,isPlaying:i,status:c,utterance:m,error:w,stop:O,toggle:y,speak:h}}function bl(e,t){const n=l.ref(e),r=l.computed(()=>Array.isArray(n.value)?n.value:Object.keys(n.value)),o=l.ref(r.value.indexOf(t??r.value[0])),s=l.computed(()=>f(o.value)),u=l.computed(()=>o.value===0),a=l.computed(()=>o.value===r.value.length-1),i=l.computed(()=>r.value[o.value+1]),c=l.computed(()=>r.value[o.value-1]);function f(P){return Array.isArray(n.value)?n.value[P]:n.value[r.value[P]]}function v(P){if(!!r.value.includes(P))return f(r.value.indexOf(P))}function w(P){r.value.includes(P)&&(o.value=r.value.indexOf(P))}function y(){a.value||o.value++}function p(){u.value||o.value--}function m(P){$(P)&&w(P)}function h(P){return r.value.indexOf(P)===o.value+1}function O(P){return r.value.indexOf(P)===o.value-1}function _(P){return r.value.indexOf(P)===o.value}function b(P){return o.value<r.value.indexOf(P)}function $(P){return o.value>r.value.indexOf(P)}return{steps:n,stepNames:r,index:o,current:s,next:i,previous:c,isFirst:u,isLast:a,at:f,get:v,goTo:w,goToNext:y,goToPrevious:p,goBackTo:m,isNext:h,isPrevious:O,isCurrent:_,isBefore:b,isAfter:$}}var Ol=Object.defineProperty,Jt=Object.getOwnPropertySymbols,Sl=Object.prototype.hasOwnProperty,Pl=Object.prototype.propertyIsEnumerable,Zt=(e,t,n)=>t in e?Ol(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Dt=(e,t)=>{for(var n in t||(t={}))Sl.call(t,n)&&Zt(e,n,t[n]);if(Jt)for(var n of Jt(t))Pl.call(t,n)&&Zt(e,n,t[n]);return e};function El(e,t,n,r={}){var o;const{flush:s="pre",deep:u=!0,listenToStorageChanges:a=!0,writeDefaults:i=!0,mergeDefaults:c=!1,shallow:f,window:v=A,eventFilter:w,onError:y=b=>{console.error(b)}}=r,p=d.resolveUnref(t),m=Be(p),h=(f?l.shallowRef:l.ref)(t),O=(o=r.serializer)!=null?o:Se[m];if(!n)try{n=ie("getDefaultStorage",()=>{var b;return(b=A)==null?void 0:b.localStorage})()}catch(b){y(b)}async function _(b){if(!(!n||b&&b.key!==e))try{const $=b?b.newValue:await n.getItem(e);if($==null)h.value=p,i&&p!==null&&await n.setItem(e,await O.write(p));else if(c){const P=await O.read($);d.isFunction(c)?h.value=c(P,p):m==="object"&&!Array.isArray(P)?h.value=Dt(Dt({},p),P):h.value=P}else h.value=await O.read($)}catch($){y($)}}return _(),v&&a&&S(v,"storage",b=>setTimeout(()=>_(b),0)),n&&d.watchWithFilter(h,async()=>{try{h.value==null?await n.removeItem(e):await n.setItem(e,await O.write(h.value))}catch(b){y(b)}},{flush:s,deep:u,eventFilter:w}),h}let $l=0;function Tl(e,t={}){const n=l.ref(!1),{document:r=U,immediate:o=!0,manual:s=!1,id:u=`vueuse_styletag_${++$l}`}=t,a=l.ref(e);let i=()=>{};const c=()=>{if(!r)return;const v=r.getElementById(u)||r.createElement("style");v.isConnected||(v.type="text/css",v.id=u,t.media&&(v.media=t.media),r.head.appendChild(v)),!n.value&&(i=l.watch(a,w=>{v.textContent=w},{immediate:!0}),n.value=!0)},f=()=>{!r||!n.value||(i(),r.head.removeChild(r.getElementById(u)),n.value=!1)};return o&&!s&&d.tryOnMounted(c),s||d.tryOnScopeDispose(f),{id:u,css:a,unload:f,load:c,isLoaded:l.readonly(n)}}function Fl(){const e=l.ref([]);return e.value.set=t=>{t&&e.value.push(t)},l.onBeforeUpdate(()=>{e.value.length=0}),e}function Rl(e={}){const{document:t=U,selector:n="html",observe:r=!1,initialValue:o="ltr"}=e;function s(){var a,i;return(i=(a=t==null?void 0:t.querySelector(n))==null?void 0:a.getAttribute("dir"))!=null?i:o}const u=l.ref(s());return d.tryOnMounted(()=>u.value=s()),r&&t&&Ie(t.querySelector(n),()=>u.value=s(),{attributes:!0}),l.computed({get(){return u.value},set(a){var i,c;u.value=a,!!t&&(u.value?(i=t.querySelector(n))==null||i.setAttribute("dir",u.value):(c=t.querySelector(n))==null||c.removeAttribute("dir"))}})}function Il(e){var t;const n=(t=e.rangeCount)!=null?t:0,r=new Array(n);for(let o=0;o<n;o++){const s=e.getRangeAt(o);r[o]=s}return r}function Cl(e={}){const{window:t=A}=e,n=l.ref(null),r=l.computed(()=>{var a,i;return(i=(a=n.value)==null?void 0:a.toString())!=null?i:""}),o=l.computed(()=>n.value?Il(n.value):[]),s=l.computed(()=>o.value.map(a=>a.getBoundingClientRect()));function u(){n.value=null,t&&(n.value=t.getSelection())}return t&&S(t.document,"selectionchange",u),{text:r,rects:s,ranges:o,selection:n}}function Al(e){const t=l.ref(e==null?void 0:e.element),n=l.ref(e==null?void 0:e.input);function r(){var o,s;!t.value||(t.value.style.height="1px",t.value.style.height=`${(o=t.value)==null?void 0:o.scrollHeight}px`,(s=e==null?void 0:e.onResize)==null||s.call(e))}return l.watch([n,t],r,{immediate:!0}),fe(t,()=>r()),(e==null?void 0:e.watch)&&l.watch(e.watch,r,{immediate:!0,deep:!0}),{textarea:t,input:n,triggerResize:r}}var kl=Object.defineProperty,jl=Object.defineProperties,Ll=Object.getOwnPropertyDescriptors,en=Object.getOwnPropertySymbols,Ul=Object.prototype.hasOwnProperty,Ml=Object.prototype.propertyIsEnumerable,tn=(e,t,n)=>t in e?kl(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,nn=(e,t)=>{for(var n in t||(t={}))Ul.call(t,n)&&tn(e,n,t[n]);if(en)for(var n of en(t))Ml.call(t,n)&&tn(e,n,t[n]);return e},Nl=(e,t)=>jl(e,Ll(t));function Hl(e,t={}){const{throttle:n=200,trailing:r=!0}=t,o=d.throttleFilter(n,r),s=$e(e,Nl(nn({},t),{eventFilter:o}));return nn({},s)}var Wl=Object.defineProperty,ye=Object.getOwnPropertySymbols,rn=Object.prototype.hasOwnProperty,on=Object.prototype.propertyIsEnumerable,an=(e,t,n)=>t in e?Wl(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Bl=(e,t)=>{for(var n in t||(t={}))rn.call(t,n)&&an(e,n,t[n]);if(ye)for(var n of ye(t))on.call(t,n)&&an(e,n,t[n]);return e},Vl=(e,t)=>{var n={};for(var r in e)rn.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&ye)for(var r of ye(e))t.indexOf(r)<0&&on.call(e,r)&&(n[r]=e[r]);return n};const xl=[{max:6e4,value:1e3,name:"second"},{max:276e4,value:6e4,name:"minute"},{max:72e6,value:36e5,name:"hour"},{max:5184e5,value:864e5,name:"day"},{max:24192e5,value:6048e5,name:"week"},{max:28512e6,value:2592e6,name:"month"},{max:1/0,value:31536e6,name:"year"}],zl={justNow:"just now",past:e=>e.match(/\d/)?`${e} ago`:e,future:e=>e.match(/\d/)?`in ${e}`:e,month:(e,t)=>e===1?t?"last month":"next month":`${e} month${e>1?"s":""}`,year:(e,t)=>e===1?t?"last year":"next year":`${e} year${e>1?"s":""}`,day:(e,t)=>e===1?t?"yesterday":"tomorrow":`${e} day${e>1?"s":""}`,week:(e,t)=>e===1?t?"last week":"next week":`${e} week${e>1?"s":""}`,hour:e=>`${e} hour${e>1?"s":""}`,minute:e=>`${e} minute${e>1?"s":""}`,second:e=>`${e} second${e>1?"s":""}`,invalid:""},ql=e=>e.toISOString().slice(0,10);function Gl(e,t={}){const{controls:n=!1,updateInterval:r=3e4}=t,o=Nt({interval:r,controls:!0}),{now:s}=o,u=Vl(o,["now"]),a=l.computed(()=>ln(new Date(d.resolveUnref(e)),t,l.unref(s.value)));return n?Bl({timeAgo:a},u):a}function ln(e,t={},n=Date.now()){var r;const{max:o,messages:s=zl,fullDateFormatter:u=ql,units:a=xl,showSecond:i=!1,rounding:c="round"}=t,f=typeof c=="number"?h=>+h.toFixed(c):Math[c],v=+n-+e,w=Math.abs(v);function y(h,O){return f(Math.abs(h)/O.value)}function p(h,O){const _=y(h,O),b=h>0,$=m(O.name,_,b);return m(b?"past":"future",$,b)}function m(h,O,_){const b=s[h];return typeof b=="function"?b(O,_):b.replace("{0}",O.toString())}if(w<6e4&&!i)return s.justNow;if(typeof o=="number"&&w>o)return u(new Date(e));if(typeof o=="string"){const h=(r=a.find(O=>O.name===o))==null?void 0:r.max;if(h&&w>h)return u(new Date(e))}for(const[h,O]of a.entries()){if(y(v,O)<=0&&a[h-1])return p(v,a[h-1]);if(w<O.max)return p(v,O)}return s.invalid}function Xl(e,t,n){const{start:r}=d.useTimeoutFn(s,t),o=l.ref(!1);async function s(){!o.value||(await e(),r())}function u(){o.value||(o.value=!0,s())}function a(){o.value=!1}return(n==null?void 0:n.immediate)&&u(),d.tryOnScopeDispose(a),{isActive:o,pause:a,resume:u}}var Yl=Object.defineProperty,sn=Object.getOwnPropertySymbols,Kl=Object.prototype.hasOwnProperty,Ql=Object.prototype.propertyIsEnumerable,un=(e,t,n)=>t in e?Yl(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Jl=(e,t)=>{for(var n in t||(t={}))Kl.call(t,n)&&un(e,n,t[n]);if(sn)for(var n of sn(t))Ql.call(t,n)&&un(e,n,t[n]);return e};function Zl(e={}){const{controls:t=!1,offset:n=0,immediate:r=!0,interval:o="requestAnimationFrame",callback:s}=e,u=l.ref(d.timestamp()+n),a=()=>u.value=d.timestamp()+n,i=s?()=>{a(),s(u.value)}:a,c=o==="requestAnimationFrame"?D(i,{immediate:r}):d.useIntervalFn(i,o,{immediate:r});return t?Jl({timestamp:u},c):u}function Dl(e=null,t={}){var n,r;const{document:o=U}=t,s=d.resolveRef((n=e??(o==null?void 0:o.title))!=null?n:null),u=e&&d.isFunction(e);function a(i){if(!("titleTemplate"in t))return i;const c=t.titleTemplate||"%s";return d.isFunction(c)?c(i):l.unref(c).replace(/%s/g,i)}return l.watch(s,(i,c)=>{i!==c&&o&&(o.title=a(d.isString(i)?i:""))},{immediate:!0}),t.observe&&!t.titleTemplate&&o&&!u&&Ie((r=o.head)==null?void 0:r.querySelector("title"),()=>{o&&o.title!==s.value&&(s.value=a(o.title))},{childList:!0}),s}var es=Object.defineProperty,cn=Object.getOwnPropertySymbols,ts=Object.prototype.hasOwnProperty,ns=Object.prototype.propertyIsEnumerable,fn=(e,t,n)=>t in e?es(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,rs=(e,t)=>{for(var n in t||(t={}))ts.call(t,n)&&fn(e,n,t[n]);if(cn)for(var n of cn(t))ns.call(t,n)&&fn(e,n,t[n]);return e};const os={easeInSine:[.12,0,.39,0],easeOutSine:[.61,1,.88,1],easeInOutSine:[.37,0,.63,1],easeInQuad:[.11,0,.5,0],easeOutQuad:[.5,1,.89,1],easeInOutQuad:[.45,0,.55,1],easeInCubic:[.32,0,.67,0],easeOutCubic:[.33,1,.68,1],easeInOutCubic:[.65,0,.35,1],easeInQuart:[.5,0,.75,0],easeOutQuart:[.25,1,.5,1],easeInOutQuart:[.76,0,.24,1],easeInQuint:[.64,0,.78,0],easeOutQuint:[.22,1,.36,1],easeInOutQuint:[.83,0,.17,1],easeInExpo:[.7,0,.84,0],easeOutExpo:[.16,1,.3,1],easeInOutExpo:[.87,0,.13,1],easeInCirc:[.55,0,1,.45],easeOutCirc:[0,.55,.45,1],easeInOutCirc:[.85,0,.15,1],easeInBack:[.36,0,.66,-.56],easeOutBack:[.34,1.56,.64,1],easeInOutBack:[.68,-.6,.32,1.6]},as=rs({linear:d.identity},os);function ls([e,t,n,r]){const o=(f,v)=>1-3*v+3*f,s=(f,v)=>3*v-6*f,u=f=>3*f,a=(f,v,w)=>((o(v,w)*f+s(v,w))*f+u(v))*f,i=(f,v,w)=>3*o(v,w)*f*f+2*s(v,w)*f+u(v),c=f=>{let v=f;for(let w=0;w<4;++w){const y=i(v,e,n);if(y===0)return v;v-=(a(v,e,n)-f)/y}return v};return f=>e===t&&n===r?f:a(c(f),t,r)}function ss(e,t={}){const{delay:n=0,disabled:r=!1,duration:o=1e3,onFinished:s=d.noop,onStarted:u=d.noop,transition:a=d.identity}=t,i=l.computed(()=>{const P=l.unref(a);return d.isFunction(P)?P:ls(P)}),c=l.computed(()=>{const P=l.unref(e);return d.isNumber(P)?P:P.map(l.unref)}),f=l.computed(()=>d.isNumber(c.value)?[c.value]:c.value),v=l.ref(f.value.slice(0));let w,y,p,m,h;const{resume:O,pause:_}=D(()=>{const P=Date.now(),F=d.clamp(1-(p-P)/w,0,1);v.value=h.map((T,I)=>{var R;return T+((R=y[I])!=null?R:0)*i.value(F)}),F>=1&&(_(),s())},{immediate:!1}),b=()=>{_(),w=l.unref(o),y=v.value.map((P,F)=>{var T,I;return((T=f.value[F])!=null?T:0)-((I=v.value[F])!=null?I:0)}),h=v.value.slice(0),m=Date.now(),p=m+w,O(),u()},$=d.useTimeoutFn(b,n,{immediate:!1});return l.watch(f,()=>{l.unref(r)||(l.unref(n)<=0?b():$.start())},{deep:!0}),l.watch(()=>l.unref(r),P=>{P&&(v.value=f.value.slice(0),_())}),l.computed(()=>{const P=l.unref(r)?f:v;return d.isNumber(c.value)?P.value[0]:P.value})}function us(e="history",t={}){const{initialValue:n={},removeNullishValues:r=!0,removeFalsyValues:o=!1,write:s=!0,window:u=A}=t;if(!u)return l.reactive(n);const a=l.reactive({});function i(){if(e==="history")return u.location.search||"";if(e==="hash"){const O=u.location.hash||"",_=O.indexOf("?");return _>0?O.slice(_):""}else return(u.location.hash||"").replace(/^#/,"")}function c(O){const _=O.toString();if(e==="history")return`${_?`?${_}`:""}${u.location.hash||""}`;if(e==="hash-params")return`${u.location.search||""}${_?`#${_}`:""}`;const b=u.location.hash||"#",$=b.indexOf("?");return $>0?`${b.slice(0,$)}${_?`?${_}`:""}`:`${b}${_?`?${_}`:""}`}function f(){return new URLSearchParams(i())}function v(O){const _=new Set(Object.keys(a));for(const b of O.keys()){const $=O.getAll(b);a[b]=$.length>1?$:O.get(b)||"",_.delete(b)}Array.from(_).forEach(b=>delete a[b])}const{pause:w,resume:y}=d.pausableWatch(a,()=>{const O=new URLSearchParams("");Object.keys(a).forEach(_=>{const b=a[_];Array.isArray(b)?b.forEach($=>O.append(_,$)):r&&b==null||o&&!b?O.delete(_):O.set(_,b)}),p(O)},{deep:!0});function p(O,_){w(),_&&v(O),u.history.replaceState(u.history.state,u.document.title,u.location.pathname+c(O)),y()}function m(){!s||p(f(),!0)}S(u,"popstate",m,!1),e!=="history"&&S(u,"hashchange",m,!1);const h=f();return h.keys().next().value?v(h):Object.assign(a,n),a}function is(e={}){var t,n;const r=l.ref((t=e.enabled)!=null?t:!1),o=l.ref((n=e.autoSwitch)!=null?n:!0),s=l.ref(e.videoDeviceId),u=l.ref(e.audioDeviceId),{navigator:a=W}=e,i=L(()=>{var h;return(h=a==null?void 0:a.mediaDevices)==null?void 0:h.getUserMedia}),c=l.shallowRef();function f(h){return h.value==="none"||h.value===!1?!1:h.value==null?!0:{deviceId:h.value}}async function v(){if(!(!i.value||c.value))return c.value=await a.mediaDevices.getUserMedia({video:f(s),audio:f(u)}),c.value}async function w(){var h;(h=c.value)==null||h.getTracks().forEach(O=>O.stop()),c.value=void 0}function y(){w(),r.value=!1}async function p(){return await v(),c.value&&(r.value=!0),c.value}async function m(){return w(),await p()}return l.watch(r,h=>{h?v():w()},{immediate:!0}),l.watch([s,u],()=>{o.value&&c.value&&m()},{immediate:!0}),{isSupported:i,stream:c,start:p,stop:y,restart:m,videoDeviceId:s,audioDeviceId:u,enabled:r,autoSwitch:o}}function dn(e,t,n,r={}){var o,s,u,a,i;const{clone:c=!1,passive:f=!1,eventName:v,deep:w=!1,defaultValue:y}=r,p=l.getCurrentInstance(),m=n||(p==null?void 0:p.emit)||((o=p==null?void 0:p.$emit)==null?void 0:o.bind(p))||((u=(s=p==null?void 0:p.proxy)==null?void 0:s.$emit)==null?void 0:u.bind(p==null?void 0:p.proxy));let h=v;if(!t)if(l.isVue2){const b=(i=(a=p==null?void 0:p.proxy)==null?void 0:a.$options)==null?void 0:i.model;t=(b==null?void 0:b.value)||"value",v||(h=(b==null?void 0:b.event)||"input")}else t="modelValue";h=v||h||`update:${t.toString()}`;const O=b=>c?d.isFunction(c)?c(b):oe(b):b,_=()=>d.isDef(e[t])?O(e[t]):y;if(f){const b=_(),$=l.ref(b);return l.watch(()=>e[t],P=>$.value=O(P)),l.watch($,P=>{(P!==e[t]||w)&&m(h,P)},{deep:w}),$}else return l.computed({get(){return _()},set(b){m(h,b)}})}function cs(e,t,n={}){const r={};for(const o in e)r[o]=dn(e,o,t,n);return r}function fs(e){const{pattern:t=[],interval:n=0,navigator:r=W}=e||{},o=L(()=>typeof r!="undefined"&&"vibrate"in r),s=d.resolveRef(t);let u;const a=(c=s.value)=>{o.value&&r.vibrate(c)},i=()=>{o.value&&r.vibrate(0),u==null||u.pause()};return n>0&&(u=d.useIntervalFn(a,n,{immediate:!1,immediateCallback:!1})),{isSupported:o,pattern:t,intervalControls:u,vibrate:a,stop:i}}function ds(e,t){const{containerStyle:n,wrapperProps:r,scrollTo:o,calculateRange:s,currentList:u,containerRef:a}="itemHeight"in t?ys(t,e):ps(t,e);return{list:u,scrollTo:o,containerProps:{ref:a,onScroll:()=>{s()},style:n},wrapperProps:r}}function vn(e){const t=l.ref(null),n=dt(t),r=l.ref([]),o=l.shallowRef(e);return{state:l.ref({start:0,end:10}),source:o,currentList:r,size:n,containerRef:t}}function pn(e,t,n){return r=>{if(typeof n=="number")return Math.ceil(r/n);const{start:o=0}=e.value;let s=0,u=0;for(let a=o;a<t.value.length&&(s+=n(a),u=a,!(s>r));a++);return u-o}}function yn(e,t){return n=>{if(typeof t=="number")return Math.floor(n/t)+1;let r=0,o=0;for(let s=0;s<e.value.length;s++)if(r+=t(s),r>=n){o=s;break}return o+1}}function gn(e,t,n,r,{containerRef:o,state:s,currentList:u,source:a}){return()=>{const i=o.value;if(i){const c=n(e==="vertical"?i.scrollTop:i.scrollLeft),f=r(e==="vertical"?i.clientHeight:i.clientWidth),v=c-t,w=c+f+t;s.value={start:v<0?0:v,end:w>a.value.length?a.value.length:w},u.value=a.value.slice(s.value.start,s.value.end).map((y,p)=>({data:y,index:p+s.value.start}))}}}function wn(e,t){return n=>typeof e=="number"?n*e:t.value.slice(0,n).reduce((o,s,u)=>o+e(u),0)}function mn(e,t,n){l.watch([e.width,e.height,t],()=>{n()})}function hn(e,t){return l.computed(()=>typeof e=="number"?t.value.length*e:t.value.reduce((n,r,o)=>n+e(o),0))}const vs={horizontal:"scrollLeft",vertical:"scrollTop"};function _n(e,t,n,r){return o=>{r.value&&(r.value[vs[e]]=n(o),t())}}function ps(e,t){const n=vn(t),{state:r,source:o,currentList:s,size:u,containerRef:a}=n,i={overflowX:"auto"},{itemWidth:c,overscan:f=5}=e,v=pn(r,o,c),w=yn(o,c),y=gn("horizontal",f,w,v,n),p=wn(c,o),m=l.computed(()=>p(r.value.start)),h=hn(c,o);mn(u,t,y);const O=_n("horizontal",y,p,a),_=l.computed(()=>({style:{height:"100%",width:`${h.value-m.value}px`,marginLeft:`${m.value}px`,display:"flex"}}));return{scrollTo:O,calculateRange:y,wrapperProps:_,containerStyle:i,currentList:s,containerRef:a}}function ys(e,t){const n=vn(t),{state:r,source:o,currentList:s,size:u,containerRef:a}=n,i={overflowY:"auto"},{itemHeight:c,overscan:f=5}=e,v=pn(r,o,c),w=yn(o,c),y=gn("vertical",f,w,v,n),p=wn(c,o),m=l.computed(()=>p(r.value.start)),h=hn(c,o);mn(u,t,y);const O=_n("vertical",y,p,a),_=l.computed(()=>({style:{width:"100%",height:`${h.value-m.value}px`,marginTop:`${m.value}px`}}));return{calculateRange:y,scrollTo:O,containerStyle:i,wrapperProps:_,currentList:s,containerRef:a}}const gs=(e={})=>{const{navigator:t=W,document:n=U}=e;let r;const o=L(()=>t&&"wakeLock"in t),s=l.ref(!1);async function u(){!o.value||!r||(n&&n.visibilityState==="visible"&&(r=await t.wakeLock.request("screen")),s.value=!r.released)}n&&S(n,"visibilitychange",u,{passive:!0});async function a(c){!o.value||(r=await t.wakeLock.request(c),s.value=!r.released)}async function i(){!o.value||!r||(await r.release(),s.value=!r.released,r=null)}return{isSupported:o,isActive:s,request:a,release:i}},ws=(e={})=>{const{window:t=A}=e,n=L(()=>!!t&&"Notification"in t),r=l.ref(null),o=async()=>{!n.value||"permission"in Notification&&Notification.permission!=="denied"&&await Notification.requestPermission()},s=d.createEventHook(),u=d.createEventHook(),a=d.createEventHook(),i=d.createEventHook(),c=async v=>{if(!n.value)return;await o();const w=Object.assign({},e,v);return r.value=new Notification(w.title||"",w),r.value.onclick=y=>s.trigger(y),r.value.onshow=y=>u.trigger(y),r.value.onerror=y=>a.trigger(y),r.value.onclose=y=>i.trigger(y),r.value},f=()=>{r.value&&r.value.close(),r.value=null};if(d.tryOnMounted(async()=>{n.value&&await o()}),d.tryOnScopeDispose(f),n.value&&t){const v=t.document;S(v,"visibilitychange",w=>{w.preventDefault(),v.visibilityState==="visible"&&f()})}return{isSupported:n,notification:r,show:c,close:f,onClick:s,onShow:u,onError:a,onClose:i}},bn="ping";function Ce(e){return e===!0?{}:e}function ms(e,t={}){const{onConnected:n,onDisconnected:r,onError:o,onMessage:s,immediate:u=!0,autoClose:a=!0,protocols:i=[]}=t,c=l.ref(null),f=l.ref("CLOSED"),v=l.ref(),w=d.resolveRef(e);let y,p,m=!1,h=0,O=[],_;const b=(R=1e3,E)=>{!v.value||(m=!0,y==null||y(),v.value.close(R,E))},$=()=>{if(O.length&&v.value&&f.value==="OPEN"){for(const R of O)v.value.send(R);O=[]}},P=()=>{clearTimeout(_),_=void 0},F=(R,E=!0)=>!v.value||f.value!=="OPEN"?(E&&O.push(R),!1):($(),v.value.send(R),!0),T=()=>{if(m||typeof w.value=="undefined")return;const R=new WebSocket(w.value,i);v.value=R,f.value="CONNECTING",R.onopen=()=>{f.value="OPEN",n==null||n(R),p==null||p(),$()},R.onclose=E=>{if(f.value="CLOSED",v.value=void 0,r==null||r(R,E),!m&&t.autoReconnect){const{retries:C=-1,delay:j=1e3,onFailed:H}=Ce(t.autoReconnect);h+=1,typeof C=="number"&&(C<0||h<C)||typeof C=="function"&&C()?setTimeout(T,j):H==null||H()}},R.onerror=E=>{o==null||o(R,E)},R.onmessage=E=>{if(t.heartbeat){P();const{message:C=bn}=Ce(t.heartbeat);if(E.data===C)return}c.value=E.data,s==null||s(R,E)}};if(t.heartbeat){const{message:R=bn,interval:E=1e3,pongTimeout:C=1e3}=Ce(t.heartbeat),{pause:j,resume:H}=d.useIntervalFn(()=>{F(R,!1),_==null&&(_=setTimeout(()=>{b()},C))},E,{immediate:!1});y=j,p=H}a&&(S(window,"beforeunload",()=>b()),d.tryOnScopeDispose(b));const I=()=>{b(),m=!1,h=0,T()};return u&&l.watch(w,I,{immediate:!0}),{data:c,status:f,close:b,send:F,open:I,ws:v}}function hs(e,t,n){const{window:r=A}=n??{},o=l.ref(null),s=l.shallowRef(),u=function(c){!s.value||s.value.postMessage(c)},a=function(){!s.value||s.value.terminate()};return r&&(d.isString(e)?s.value=new Worker(e,t):d.isFunction(e)?s.value=e():s.value=e,s.value.onmessage=i=>{o.value=i.data},d.tryOnScopeDispose(()=>{s.value&&s.value.terminate()})),{data:o,post:u,terminate:a,worker:s}}const _s=e=>t=>{const n=t.data[0];return Promise.resolve(e.apply(void 0,n)).then(r=>{postMessage(["SUCCESS",r])}).catch(r=>{postMessage(["ERROR",r])})},bs=e=>e.length===0?"":`importScripts(${e.map(n=>`'${n}'`).toString()})`,Os=(e,t)=>{const n=`${bs(t)}; onmessage=(${_s})(${e})`,r=new Blob([n],{type:"text/javascript"});return URL.createObjectURL(r)},Ss=(e,t={})=>{const{dependencies:n=[],timeout:r,window:o=A}=t,s=l.ref(),u=l.ref("PENDING"),a=l.ref({}),i=l.ref(),c=(y="PENDING")=>{s.value&&s.value._url&&o&&(s.value.terminate(),URL.revokeObjectURL(s.value._url),a.value={},s.value=void 0,o.clearTimeout(i.value),u.value=y)};c(),d.tryOnScopeDispose(c);const f=()=>{const y=Os(e,n),p=new Worker(y);return p._url=y,p.onmessage=m=>{const{resolve:h=()=>{},reject:O=()=>{}}=a.value,[_,b]=m.data;switch(_){case"SUCCESS":h(b),c(_);break;default:O(b),c("ERROR");break}},p.onerror=m=>{const{reject:h=()=>{}}=a.value;h(m),c("ERROR")},r&&(i.value=setTimeout(()=>c("TIMEOUT_EXPIRED"),r)),p},v=(...y)=>new Promise((p,m)=>{a.value={resolve:p,reject:m},s.value&&s.value.postMessage([[...y]]),u.value="RUNNING"});return{workerFn:(...y)=>u.value==="RUNNING"?(console.error("[useWebWorkerFn] You can only run one instance of the worker at a time."),Promise.reject()):(s.value=f(),v(...y)),workerStatus:u,workerTerminate:c}};function Ps({window:e=A}={}){if(!e)return l.ref(!1);const t=l.ref(e.document.hasFocus());return S(e,"blur",()=>{t.value=!1}),S(e,"focus",()=>{t.value=!0}),t}function Es({window:e=A}={}){if(!e)return{x:l.ref(0),y:l.ref(0)};const t=l.ref(e.scrollX),n=l.ref(e.scrollY);return S(e,"scroll",()=>{t.value=e.scrollX,n.value=e.scrollY},{capture:!1,passive:!0}),{x:t,y:n}}function $s(e={}){const{window:t=A,initialWidth:n=1/0,initialHeight:r=1/0,listenOrientation:o=!0,includeScrollbar:s=!0}=e,u=l.ref(n),a=l.ref(r),i=()=>{t&&(s?(u.value=t.innerWidth,a.value=t.innerHeight):(u.value=t.document.documentElement.clientWidth,a.value=t.document.documentElement.clientHeight))};return i(),d.tryOnMounted(i),S("resize",i,{passive:!0}),o&&S("orientationchange",i,{passive:!0}),{width:u,height:a}}g.DefaultMagicKeysAliasMap=Ft,g.StorageSerializers=Se,g.TransitionPresets=as,g.asyncComputed=X,g.breakpointsAntDesign=Qn,g.breakpointsBootstrapV5=Yn,g.breakpointsMasterCss=Dn,g.breakpointsQuasar=Jn,g.breakpointsSematic=Zn,g.breakpointsTailwind=Xn,g.breakpointsVuetify=Kn,g.cloneFnJSON=oe,g.computedAsync=X,g.computedInject=Pn,g.createFetch=jo,g.createUnrefFn=B,g.customStorageEventName=Pe,g.defaultDocument=U,g.defaultLocation=se,g.defaultNavigator=W,g.defaultWindow=A,g.formatTimeAgo=ln,g.getSSRHandler=ie,g.mapGamepadToXbox360Controller=Ko,g.onClickOutside=En,g.onKeyDown=An,g.onKeyPressed=kn,g.onKeyStroke=ue,g.onKeyUp=jn,g.onLongPress=Un,g.onStartTyping=Hn,g.setSSRHandler=wr,g.templateRef=Wn,g.unrefElement=k,g.useActiveElement=je,g.useAsyncQueue=Bn,g.useAsyncState=Le,g.useBase64=xn,g.useBattery=qn,g.useBluetooth=Gn,g.useBreakpoints=or,g.useBroadcastChannel=ar,g.useBrowserLocation=lr,g.useCached=sr,g.useClipboard=ur,g.useCloned=gr,g.useColorMode=Xe,g.useConfirmDialog=Er,g.useCssVar=ae,g.useCurrentElement=$r,g.useCycleList=Tr,g.useDark=Lr,g.useDebouncedRefHistory=Qr,g.useDeviceMotion=Jr,g.useDeviceOrientation=at,g.useDevicePixelRatio=Zr,g.useDevicesList=Dr,g.useDisplayMedia=eo,g.useDocumentVisibility=to,g.useDraggable=io,g.useDropZone=co,g.useElementBounding=yo,g.useElementByPoint=_o,g.useElementHover=bo,g.useElementSize=dt,g.useElementVisibility=Oo,g.useEventBus=So,g.useEventListener=S,g.useEventSource=Po,g.useEyeDropper=Eo,g.useFavicon=$o,g.useFetch=yt,g.useFileDialog=Wo,g.useFileSystemAccess=zo,g.useFocus=qo,g.useFocusWithin=Go,g.useFps=Xo,g.useFullscreen=Yo,g.useGamepad=Qo,g.useGeolocation=Jo,g.useIdle=ea,g.useImage=la,g.useInfiniteScroll=va,g.useIntersectionObserver=pa,g.useKeyModifier=ga,g.useLocalStorage=wa,g.useMagicKeys=ma,g.useManualRefHistory=Je,g.useMediaControls=Pa,g.useMediaQuery=q,g.useMemoize=$a,g.useMemory=Ta,g.useMounted=Fa,g.useMouse=At,g.useMouseInElement=kt,g.useMousePressed=Ra,g.useMutationObserver=Ie,g.useNavigatorLanguage=ka,g.useNetwork=Lt,g.useNow=Nt,g.useObjectUrl=Na,g.useOffsetPagination=Ha,g.useOnline=Wa,g.usePageLeave=Ba,g.useParallax=Va,g.usePermission=lt,g.usePointer=Ja,g.usePointerLock=Za,g.usePointerSwipe=tl,g.usePreferredColorScheme=nl,g.usePreferredContrast=rl,g.usePreferredDark=Ee,g.usePreferredLanguages=ol,g.usePreferredReducedMotion=al,g.usePrevious=ll,g.useRafFn=D,g.useRefHistory=$e,g.useResizeObserver=fe,g.useScreenOrientation=sl,g.useScreenSafeArea=ul,g.useScriptTag=il,g.useScroll=Pt,g.useScrollLock=fl,g.useSessionStorage=dl,g.useShare=gl,g.useSorted=ml,g.useSpeechRecognition=hl,g.useSpeechSynthesis=_l,g.useStepper=bl,g.useStorage=ce,g.useStorageAsync=El,g.useStyleTag=Tl,g.useSupported=L,g.useSwipe=Da,g.useTemplateRefsList=Fl,g.useTextDirection=Rl,g.useTextSelection=Cl,g.useTextareaAutosize=Al,g.useThrottledRefHistory=Hl,g.useTimeAgo=Gl,g.useTimeoutPoll=Xl,g.useTimestamp=Zl,g.useTitle=Dl,g.useTransition=ss,g.useUrlSearchParams=us,g.useUserMedia=is,g.useVModel=dn,g.useVModels=cs,g.useVibrate=fs,g.useVirtualList=ds,g.useWakeLock=gs,g.useWebNotification=ws,g.useWebSocket=ms,g.useWebWorker=hs,g.useWebWorkerFn=Ss,g.useWindowFocus=Ps,g.useWindowScroll=Es,g.useWindowSize=$s,Object.keys(d).forEach(function(e){e!=="default"&&!g.hasOwnProperty(e)&&Object.defineProperty(g,e,{enumerable:!0,get:function(){return d[e]}})})})(this.VueUse=this.VueUse||{},VueUse,VueDemi);
