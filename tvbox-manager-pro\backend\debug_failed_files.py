#!/usr/bin/env python3
"""
调试失败文件的脚本
"""
import sqlite3
import os

def debug_failed_files():
    """调试失败文件"""
    db_path = "data/tvbox.db"
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查询失败的本地化文件
        cursor.execute("""
            SELECT id, interface_id, file_type, original_filename, original_url,
                   local_path, download_status, error_message, file_size
            FROM localized_files
            WHERE interface_id = 1 AND download_status IN ('failed', 'error')
        """)
        
        failed_files = cursor.fetchall()
        print(f"失败的文件数量: {len(failed_files)}")
        print()
        
        for file in failed_files:
            print(f"文件ID: {file[0]}")
            print(f"接口ID: {file[1]}")
            print(f"文件类型: {file[2]}")
            print(f"原始文件名: {file[3]}")
            print(f"原始URL: {file[4]}")
            print(f"本地路径: {file[5]}")
            print(f"下载状态: {file[6]}")
            print(f"错误信息: {file[7]}")
            print(f"文件大小: {file[8]}")
            print("-" * 50)
            
        # 查询所有本地化文件
        cursor.execute("""
            SELECT download_status, COUNT(*) 
            FROM localized_files 
            WHERE interface_id = 1
            GROUP BY download_status
        """)
        
        status_counts = cursor.fetchall()
        print("文件状态统计:")
        for status, count in status_counts:
            print(f"  {status}: {count}")
            
    except Exception as e:
        print(f"检查失败: {str(e)}")
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    debug_failed_files()
