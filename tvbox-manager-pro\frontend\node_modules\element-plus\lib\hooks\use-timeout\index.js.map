{"version": 3, "file": "index.js", "sources": ["../../../../../packages/hooks/use-timeout/index.ts"], "sourcesContent": ["import { tryOnScopeDispose } from '@vueuse/core'\n\nexport function useTimeout() {\n  let timeoutHandle: number\n\n  const registerTimeout = (fn: (...args: any[]) => any, delay: number) => {\n    cancelTimeout()\n    timeoutHandle = window.setTimeout(fn, delay)\n  }\n  const cancelTimeout = () => window.clearTimeout(timeoutHandle)\n\n  tryOnScopeDispose(() => cancelTimeout())\n\n  return {\n    registerTimeout,\n    cancelTimeout,\n  }\n}\n"], "names": ["tryOnScopeDispose"], "mappings": ";;;;;;AACO,SAAS,UAAU,GAAG;AAC7B,EAAE,IAAI,aAAa,CAAC;AACpB,EAAE,MAAM,eAAe,GAAG,CAAC,EAAE,EAAE,KAAK,KAAK;AACzC,IAAI,aAAa,EAAE,CAAC;AACpB,IAAI,aAAa,GAAG,MAAM,CAAC,UAAU,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;AACjD,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;AACjE,EAAEA,sBAAiB,CAAC,MAAM,aAAa,EAAE,CAAC,CAAC;AAC3C,EAAE,OAAO;AACT,IAAI,eAAe;AACnB,IAAI,aAAa;AACjB,GAAG,CAAC;AACJ;;;;"}