#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
系统相关数据模型
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
import json

from app.core.database import Base

class SystemSetting(Base):
    """系统设置模型"""
    __tablename__ = "system_settings"
    
    id = Column(Integer, primary_key=True, index=True)
    key = Column(String(100), unique=True, index=True, nullable=False)
    value = Column(Text)
    description = Column(Text)
    category = Column(String(50))  # 设置分类
    data_type = Column(String(20), default="string")  # string, int, bool, json
    
    # 状态字段
    is_active = Column(Boolean, default=True)
    is_system = Column(Boolean, default=False)  # 系统设置不可删除
    
    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self):
        return f"<SystemSetting(key='{self.key}', value='{self.value}')>"
    
    def get_value(self):
        """获取类型化的值"""
        if self.data_type == "int":
            return int(self.value) if self.value else 0
        elif self.data_type == "bool":
            return self.value.lower() in ("true", "1", "yes") if self.value else False
        elif self.data_type == "json":
            try:
                return json.loads(self.value) if self.value else {}
            except:
                return {}
        else:
            return self.value or ""

class OperationLog(Base):
    """操作日志模型"""
    __tablename__ = "operation_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    
    # 操作信息
    action = Column(String(100), nullable=False)  # 操作类型
    resource = Column(String(100))  # 操作资源
    resource_id = Column(String(50))  # 资源ID
    description = Column(Text)  # 操作描述
    
    # 请求信息
    method = Column(String(10))  # HTTP方法
    path = Column(String(500))  # 请求路径
    ip_address = Column(String(45))  # IP地址
    user_agent = Column(Text)  # 用户代理
    
    # 响应信息
    status_code = Column(Integer)  # 响应状态码
    response_time = Column(Integer)  # 响应时间（毫秒）
    
    # 额外数据
    extra_data = Column(Text)  # JSON格式的额外数据
    
    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关联关系
    user = relationship("User", back_populates="operation_logs")
    
    def __repr__(self):
        return f"<OperationLog(id={self.id}, action='{self.action}', user_id={self.user_id})>"
    
    def get_extra_data(self) -> dict:
        """获取额外数据"""
        try:
            return json.loads(self.extra_data) if self.extra_data else {}
        except:
            return {}

class Subscription(Base):
    """订阅模型"""
    __tablename__ = "subscriptions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    source_id = Column(Integer, ForeignKey("interface_sources.id"), nullable=False)
    
    # 订阅信息
    name = Column(String(100))  # 自定义名称
    is_active = Column(Boolean, default=True)
    
    # 更新设置
    auto_update = Column(Boolean, default=True)
    update_interval = Column(Integer, default=3600)  # 更新间隔（秒）
    
    # 通知设置
    notify_on_update = Column(Boolean, default=False)
    notify_on_error = Column(Boolean, default=True)
    
    # 统计信息
    update_count = Column(Integer, default=0)
    last_update_at = Column(DateTime(timezone=True))
    next_update_at = Column(DateTime(timezone=True))
    
    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    user = relationship("User", back_populates="subscriptions")
    source = relationship("InterfaceSource", back_populates="subscriptions")
    
    def __repr__(self):
        return f"<Subscription(id={self.id}, user_id={self.user_id}, source_id={self.source_id})>"

class ScheduledTask(Base):
    """定时任务模型"""
    __tablename__ = "scheduled_tasks"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    task_type = Column(String(50), nullable=False)  # 任务类型
    
    # 任务配置
    config = Column(Text)  # JSON格式的任务配置
    cron_expression = Column(String(100))  # Cron表达式
    
    # 状态信息
    is_active = Column(Boolean, default=True)
    is_running = Column(Boolean, default=False)
    
    # 执行信息
    last_run_at = Column(DateTime(timezone=True))
    next_run_at = Column(DateTime(timezone=True))
    run_count = Column(Integer, default=0)
    success_count = Column(Integer, default=0)
    error_count = Column(Integer, default=0)
    last_error = Column(Text)
    
    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self):
        return f"<ScheduledTask(id={self.id}, name='{self.name}', type='{self.task_type}')>"
    
    def get_config(self) -> dict:
        """获取任务配置"""
        try:
            return json.loads(self.config) if self.config else {}
        except:
            return {}
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.run_count == 0:
            return 0.0
        return round(self.success_count / self.run_count * 100, 2)

class BackupRecord(Base):
    """备份记录模型"""
    __tablename__ = "backup_records"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer)  # 文件大小（字节）
    
    # 备份信息
    backup_type = Column(String(20), default="manual")  # manual, auto
    description = Column(Text)
    
    # 状态信息
    status = Column(String(20), default="completed")  # pending, completed, failed
    error_message = Column(Text)
    
    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    def __repr__(self):
        return f"<BackupRecord(id={self.id}, name='{self.name}', status='{self.status}')>"
    
    @property
    def file_size_mb(self) -> float:
        """文件大小（MB）"""
        if self.file_size:
            return round(self.file_size / 1024 / 1024, 2)
        return 0.0

class SystemStatus(Base):
    """系统状态模型"""
    __tablename__ = "system_status"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 系统信息
    cpu_usage = Column(Integer)  # CPU使用率
    memory_usage = Column(Integer)  # 内存使用率
    disk_usage = Column(Integer)  # 磁盘使用率
    
    # 服务状态
    database_status = Column(String(20), default="unknown")  # online, offline, error
    redis_status = Column(String(20), default="unknown")
    
    # 统计信息
    total_users = Column(Integer, default=0)
    total_sources = Column(Integer, default=0)
    total_configs = Column(Integer, default=0)
    active_subscriptions = Column(Integer, default=0)
    
    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    def __repr__(self):
        return f"<SystemStatus(id={self.id}, cpu={self.cpu_usage}%, memory={self.memory_usage}%)>"
