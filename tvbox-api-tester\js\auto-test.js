// 自动测试功能
class AutoTester {
    constructor(app) {
        this.app = app;
        this.isRunning = false;
        this.currentTest = 0;
        this.totalTests = 0;
        this.results = [];
        this.testQueue = [];
        this.accessToken = '';
    }
    
    async startAutoTest() {
        if (this.isRunning) {
            this.app.showToast('测试正在进行中', 'warning');
            return;
        }
        
        // 准备测试
        this.prepareTests();
        
        if (this.testQueue.length === 0) {
            this.app.showToast('没有选择要测试的API', 'warning');
            return;
        }
        
        this.isRunning = true;
        this.currentTest = 0;
        this.results = [];
        
        // 更新UI
        this.updateUI(true);
        this.app.log(`开始自动测试，共 ${this.testQueue.length} 个API`, 'info');
        
        try {
            // 验证认证信息
            this.validateCredentials();

            // 执行测试
            await this.executeTests();
            
            // 显示测试完成
            this.showTestSummary();
            
        } catch (error) {
            this.app.log(`自动测试失败: ${error.message}`, 'error');
            this.app.showToast('自动测试失败', 'error');
        } finally {
            this.isRunning = false;
            this.updateUI(false);
        }
    }
    
    stopAutoTest() {
        if (!this.isRunning) {
            return;
        }
        
        this.isRunning = false;
        this.updateUI(false);
        this.app.log('自动测试已停止', 'warning');
        this.app.showToast('测试已停止', 'warning');
    }
    
    prepareTests() {
        this.testQueue = [];
        
        // 获取测试配置
        const testAuth = document.getElementById('testAuth').checked;
        const testInterfaces = document.getElementById('testInterfaces').checked;
        const testSubscriptions = document.getElementById('testSubscriptions').checked;
        const testUsers = document.getElementById('testUsers').checked;
        const testSystem = document.getElementById('testSystem').checked;
        const testDecrypt = document.getElementById('testDecrypt').checked;
        
        // 构建测试队列
        if (testAuth) {
            this.addCategoryTests('auth');
        }
        if (testInterfaces) {
            this.addCategoryTests('interfaces');
        }
        if (testSubscriptions) {
            this.addCategoryTests('subscriptions');
        }
        if (testUsers) {
            this.addCategoryTests('users');
        }
        if (testSystem) {
            this.addCategoryTests('system');
        }
        if (testDecrypt) {
            this.addCategoryTests('decrypt');
        }
        
        this.totalTests = this.testQueue.length;
    }
    
    addCategoryTests(categoryKey) {
        const category = API_DEFINITIONS[categoryKey];
        if (!category) return;
        
        for (const [apiKey, apiDef] of Object.entries(category.apis)) {
            this.testQueue.push({
                category: categoryKey,
                categoryName: category.name,
                apiKey: apiKey,
                apiDef: apiDef
            });
        }
    }
    
    validateCredentials() {
        this.app.log('验证认证信息...', 'info');

        if (!this.app.config.userEmail || !this.app.config.userPassword) {
            throw new Error('请先配置用户邮箱和密码');
        }

        this.app.log('认证信息验证通过', 'success');
    }
    
    async executeTests() {
        const concurrency = parseInt(document.getElementById('concurrency').value) || 1;
        const delay = parseInt(document.getElementById('delay').value) || 1000;
        
        for (let i = 0; i < this.testQueue.length; i++) {
            if (!this.isRunning) break;
            
            const test = this.testQueue[i];
            this.currentTest = i + 1;
            
            // 更新进度
            this.updateProgress();
            
            try {
                // 执行单个测试
                const result = await this.executeTest(test);
                this.results.push(result);
                
                // 记录结果
                const status = result.success ? '成功' : '失败';
                this.app.log(`[${this.currentTest}/${this.totalTests}] ${test.categoryName} - ${test.apiDef.name}: ${status}`, 
                           result.success ? 'success' : 'error');
                
            } catch (error) {
                const result = {
                    test: test,
                    success: false,
                    error: error.message,
                    responseTime: 0
                };
                this.results.push(result);
                
                this.app.log(`[${this.currentTest}/${this.totalTests}] ${test.categoryName} - ${test.apiDef.name}: 失败 - ${error.message}`, 'error');
            }
            
            // 延迟
            if (delay > 0 && i < this.testQueue.length - 1) {
                await this.sleep(delay);
            }
        }
    }
    
    async executeTest(test) {
        const startTime = Date.now();
        
        try {
            // 构建请求
            const request = this.buildTestRequest(test);
            
            // 发送请求
            const response = await axios(request);
            const endTime = Date.now();
            
            return {
                test: test,
                success: true,
                request: request,
                response: response,
                responseTime: endTime - startTime
            };
            
        } catch (error) {
            const endTime = Date.now();
            
            return {
                test: test,
                success: false,
                request: error.config || {},
                response: error.response || {},
                error: error.message,
                responseTime: endTime - startTime
            };
        }
    }
    
    buildTestRequest(test) {
        const apiDef = test.apiDef;
        let url = this.app.config.apiBaseUrl + apiDef.path;
        
        const headers = {
            'Content-Type': 'application/json'
        };
        
        // 添加认证头
        if (apiDef.requiresAuth && this.app.config.userEmail && this.app.config.userPassword) {
            const credentials = btoa(`${this.app.config.userEmail}:${this.app.config.userPassword}`);
            headers['Authorization'] = `Basic ${credentials}`;
        }
        
        // 构建测试数据
        const testData = this.buildTestData(apiDef);
        
        // 处理路径参数
        for (const [key, param] of Object.entries(apiDef.parameters)) {
            if (param.isPathParam && testData[key]) {
                url = url.replace(`{${key}}`, encodeURIComponent(testData[key]));
                delete testData[key];
            }
        }
        
        const request = {
            method: apiDef.method.toLowerCase(),
            url: url,
            headers: headers,
            timeout: this.app.config.timeout
        };
        
        // 添加请求体或查询参数
        if (apiDef.method === 'GET' && Object.keys(testData).length > 0) {
            const params = new URLSearchParams();
            for (const [key, value] of Object.entries(testData)) {
                if (value !== undefined && value !== '') {
                    params.append(key, value);
                }
            }
            if (params.toString()) {
                request.url += '?' + params.toString();
            }
        } else if (Object.keys(testData).length > 0) {
            request.data = testData;
        }
        
        return request;
    }
    
    buildTestData(apiDef) {
        const testData = {};
        
        for (const [key, param] of Object.entries(apiDef.parameters)) {
            if (param.default !== undefined) {
                testData[key] = param.default;
            } else if (param.required) {
                // 为必需参数生成测试值
                testData[key] = this.generateTestValue(param);
            }
        }
        
        // 使用示例数据覆盖
        if (apiDef.example) {
            Object.assign(testData, apiDef.example);
        }
        
        return testData;
    }
    
    generateTestValue(param) {
        switch (param.type) {
            case 'email':
                return '<EMAIL>';
            case 'password':
                return 'test123456';
            case 'text':
                return 'test_value';
            case 'number':
                return 1;
            case 'url':
                return 'https://example.com/test.json';
            case 'checkbox':
                return false;
            case 'select':
                return param.options ? param.options[0] : 'test';
            case 'textarea':
                return 'test content';
            default:
                return 'test';
        }
    }
    
    updateUI(isRunning) {
        const startBtn = document.getElementById('startAutoTest');
        const stopBtn = document.getElementById('stopAutoTest');
        const progressContainer = document.getElementById('autoTestProgress');
        
        if (isRunning) {
            startBtn.style.display = 'none';
            stopBtn.style.display = 'block';
            progressContainer.style.display = 'block';
        } else {
            startBtn.style.display = 'block';
            stopBtn.style.display = 'none';
            progressContainer.style.display = 'none';
        }
    }
    
    updateProgress() {
        const progressBar = document.querySelector('#autoTestProgress .progress-bar');
        const progressText = document.getElementById('progressText');
        
        const percentage = (this.currentTest / this.totalTests) * 100;
        
        progressBar.style.width = `${percentage}%`;
        progressBar.setAttribute('aria-valuenow', percentage);
        
        progressText.textContent = `正在测试: ${this.currentTest}/${this.totalTests} (${percentage.toFixed(1)}%)`;
    }
    
    showTestSummary() {
        const successCount = this.results.filter(r => r.success).length;
        const failCount = this.results.length - successCount;
        const successRate = (successCount / this.results.length * 100).toFixed(1);
        
        const avgResponseTime = this.results.reduce((sum, r) => sum + r.responseTime, 0) / this.results.length;
        
        this.app.log('='.repeat(50), 'info');
        this.app.log('自动测试完成！', 'success');
        this.app.log(`总测试数: ${this.results.length}`, 'info');
        this.app.log(`成功: ${successCount}`, 'success');
        this.app.log(`失败: ${failCount}`, failCount > 0 ? 'error' : 'info');
        this.app.log(`成功率: ${successRate}%`, 'info');
        this.app.log(`平均响应时间: ${avgResponseTime.toFixed(0)}ms`, 'info');
        this.app.log('='.repeat(50), 'info');
        
        // 保存结果到全局
        this.app.testResults = [...this.results, ...this.app.testResults];
        
        // 显示成功提示
        this.app.showToast(`测试完成！成功率: ${successRate}%`, successRate >= 80 ? 'success' : 'warning');
        
        // 切换到结果页面
        const resultsTab = document.getElementById('results-tab');
        if (resultsTab) {
            resultsTab.click();
        }
    }
    
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 全局函数
function startAutoTest() {
    if (!window.autoTester) {
        window.autoTester = new AutoTester(app);
    }
    window.autoTester.startAutoTest();
}

function stopAutoTest() {
    if (window.autoTester) {
        window.autoTester.stopAutoTest();
    }
}

function runAllTests() {
    // 选中所有测试选项
    document.getElementById('testAuth').checked = true;
    document.getElementById('testInterfaces').checked = true;
    document.getElementById('testSubscriptions').checked = true;
    document.getElementById('testUsers').checked = false; // 用户管理需要管理员权限
    document.getElementById('testSystem').checked = true;
    document.getElementById('testDecrypt').checked = true;
    
    // 切换到自动测试页面
    const autoTab = document.getElementById('auto-tab');
    if (autoTab) {
        autoTab.click();
    }
    
    // 启动测试
    setTimeout(() => {
        startAutoTest();
    }, 500);
}
