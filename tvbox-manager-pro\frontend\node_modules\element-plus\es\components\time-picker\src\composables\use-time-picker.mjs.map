{"version": 3, "file": "use-time-picker.mjs", "sources": ["../../../../../../../packages/components/time-picker/src/composables/use-time-picker.ts"], "sourcesContent": ["import { ref, watch } from 'vue'\nimport { makeList } from '../utils'\n\nimport type { Dayjs } from 'dayjs'\nimport type {\n  GetDisabledHours,\n  GetDisabledMinutes,\n  GetDisabledSeconds,\n} from '../common/props'\n\nconst makeAvailableArr = (disabledList: boolean[]): number[] => {\n  const trueOrNumber = (isDisabled: boolean, index: number) =>\n    isDisabled || index\n\n  const getNumber = (predicate: number | true): predicate is number =>\n    predicate !== true\n\n  return disabledList.map(trueOrNumber).filter(getNumber)\n}\n\nexport const getTimeLists = (\n  disabledHours?: GetDisabledHours,\n  disabledMinutes?: GetDisabledMinutes,\n  disabledSeconds?: GetDisabledSeconds\n) => {\n  const getHoursList = (role: string, compare?: Dayjs) => {\n    return makeList(24, disabledHours && (() => disabledHours?.(role, compare)))\n  }\n\n  const getMinutesList = (hour: number, role: string, compare?: Dayjs) => {\n    return makeList(\n      60,\n      disabledMinutes && (() => disabledMinutes?.(hour, role, compare))\n    )\n  }\n\n  const getSecondsList = (\n    hour: number,\n    minute: number,\n    role: string,\n    compare?: Dayjs\n  ) => {\n    return makeList(\n      60,\n      disabledSeconds && (() => disabledSeconds?.(hour, minute, role, compare))\n    )\n  }\n\n  return {\n    getHoursList,\n    getMinutesList,\n    getSecondsList,\n  }\n}\n\nexport const buildAvailableTimeSlotGetter = (\n  disabledHours: GetDisabledHours,\n  disabledMinutes: GetDisabledMinutes,\n  disabledSeconds: GetDisabledSeconds\n) => {\n  const { getHoursList, getMinutesList, getSecondsList } = getTimeLists(\n    disabledHours,\n    disabledMinutes,\n    disabledSeconds\n  )\n\n  const getAvailableHours: GetDisabledHours = (role, compare?) => {\n    return makeAvailableArr(getHoursList(role, compare))\n  }\n\n  const getAvailableMinutes: GetDisabledMinutes = (hour, role, compare?) => {\n    return makeAvailableArr(getMinutesList(hour, role, compare))\n  }\n\n  const getAvailableSeconds: GetDisabledSeconds = (\n    hour,\n    minute,\n    role,\n    compare?\n  ) => {\n    return makeAvailableArr(getSecondsList(hour, minute, role, compare))\n  }\n\n  return {\n    getAvailableHours,\n    getAvailableMinutes,\n    getAvailableSeconds,\n  }\n}\n\nexport const useOldValue = (props: {\n  parsedValue?: string | Dayjs | Dayjs[]\n  visible: boolean\n}) => {\n  const oldValue = ref(props.parsedValue)\n\n  watch(\n    () => props.visible,\n    (val) => {\n      if (!val) {\n        oldValue.value = props.parsedValue\n      }\n    }\n  )\n\n  return oldValue\n}\n"], "names": [], "mappings": ";;;AAEA,MAAM,gBAAgB,GAAG,CAAC,YAAY,KAAK;AAC3C,EAAE,MAAM,YAAY,GAAG,CAAC,UAAU,EAAE,KAAK,KAAK,UAAU,IAAI,KAAK,CAAC;AAClE,EAAE,MAAM,SAAS,GAAG,CAAC,SAAS,KAAK,SAAS,KAAK,IAAI,CAAC;AACtD,EAAE,OAAO,YAAY,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AAC1D,CAAC,CAAC;AACU,MAAC,YAAY,GAAG,CAAC,aAAa,EAAE,eAAe,EAAE,eAAe,KAAK;AACjF,EAAE,MAAM,YAAY,GAAG,CAAC,IAAI,EAAE,OAAO,KAAK;AAC1C,IAAI,OAAO,QAAQ,CAAC,EAAE,EAAE,aAAa,KAAK,MAAM,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;AAChH,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,KAAK;AAClD,IAAI,OAAO,QAAQ,CAAC,EAAE,EAAE,eAAe,KAAK,MAAM,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;AAC5H,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,KAAK;AAC1D,IAAI,OAAO,QAAQ,CAAC,EAAE,EAAE,eAAe,KAAK,MAAM,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;AACpI,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,YAAY;AAChB,IAAI,cAAc;AAClB,IAAI,cAAc;AAClB,GAAG,CAAC;AACJ,EAAE;AACU,MAAC,4BAA4B,GAAG,CAAC,aAAa,EAAE,eAAe,EAAE,eAAe,KAAK;AACjG,EAAE,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,EAAE,GAAG,YAAY,CAAC,aAAa,EAAE,eAAe,EAAE,eAAe,CAAC,CAAC;AACzH,EAAE,MAAM,iBAAiB,GAAG,CAAC,IAAI,EAAE,OAAO,KAAK;AAC/C,IAAI,OAAO,gBAAgB,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;AACzD,GAAG,CAAC;AACJ,EAAE,MAAM,mBAAmB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,KAAK;AACvD,IAAI,OAAO,gBAAgB,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;AACjE,GAAG,CAAC;AACJ,EAAE,MAAM,mBAAmB,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,KAAK;AAC/D,IAAI,OAAO,gBAAgB,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;AACzE,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,iBAAiB;AACrB,IAAI,mBAAmB;AACvB,IAAI,mBAAmB;AACvB,GAAG,CAAC;AACJ,EAAE;AACU,MAAC,WAAW,GAAG,CAAC,KAAK,KAAK;AACtC,EAAE,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;AAC1C,EAAE,KAAK,CAAC,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK;AACtC,IAAI,IAAI,CAAC,GAAG,EAAE;AACd,MAAM,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC,WAAW,CAAC;AACzC,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,QAAQ,CAAC;AAClB;;;;"}