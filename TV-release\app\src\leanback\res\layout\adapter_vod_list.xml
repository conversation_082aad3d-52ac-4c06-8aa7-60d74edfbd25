<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_vod_list"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:foreground="@drawable/selector_vod">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/image"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_margin="16dp"
            android:scaleType="fitCenter"
            app:shapeAppearanceOverlay="@style/Vod.Grid"
            tools:src="@drawable/ic_img_error" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <TextView
                android:id="@+id/name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:textColor="@color/white"
                android:textSize="16sp"
                tools:text="蜘蛛人" />

            <TextView
                android:id="@+id/remark"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:singleLine="true"
                android:textColor="@color/white"
                android:textSize="14sp"
                tools:text="1080p" />

        </LinearLayout>
    </LinearLayout>
</FrameLayout>