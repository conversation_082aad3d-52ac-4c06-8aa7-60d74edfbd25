2025-07-25 15:46:28,676 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 15:46:28,840 [INFO] root:48 - 数据库表创建完成
2025-07-25 15:46:29,234 [INFO] app.services.user_service:78 - 默认管理员用户创建成功: <EMAIL>
2025-07-25 15:46:29,235 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 15:46:43,446 [INFO] root:149 - 请求开始: GET http://127.0.0.1:8000/
2025-07-25 15:46:43,447 [INFO] root:158 - 请求完成: GET http://127.0.0.1:8000/ 状态码: 200 耗时: 0.001s
2025-07-25 15:46:43,901 [INFO] root:149 - 请求开始: GET http://127.0.0.1:8000/favicon.ico
2025-07-25 15:46:43,903 [INFO] root:158 - 请求完成: GET http://127.0.0.1:8000/favicon.ico 状态码: 404 耗时: 0.002s
2025-07-25 15:53:08,299 [INFO] root:149 - 请求开始: GET http://127.0.0.1:8000/
2025-07-25 15:53:08,300 [INFO] root:158 - 请求完成: GET http://127.0.0.1:8000/ 状态码: 200 耗时: 0.001s
2025-07-25 16:04:26,877 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/auth/login
2025-07-25 16:04:26,878 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/auth/login 状态码: 200 耗时: 0.002s
2025-07-25 16:04:26,880 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/auth/login
2025-07-25 16:04:27,186 [ERROR] app.api.v1.auth:120 - 登录异常: module 'jwt' has no attribute 'encode'
2025-07-25 16:04:27,187 [ERROR] app.core.database:75 - 数据库会话错误: 500: 登录服务异常
2025-07-25 16:04:27,189 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/auth/login 状态码: 500 耗时: 0.309s
2025-07-25 18:32:22,231 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 18:32:22,238 [INFO] root:48 - 数据库表创建完成
2025-07-25 18:32:22,277 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 18:32:22,278 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 18:32:28,689 [INFO] root:149 - 请求开始: GET http://localhost:8000/docs
2025-07-25 18:32:28,690 [INFO] root:158 - 请求完成: GET http://localhost:8000/docs 状态码: 200 耗时: 0.001s
2025-07-25 18:32:32,260 [INFO] root:149 - 请求开始: GET http://localhost:8000/docs
2025-07-25 18:32:32,260 [INFO] root:158 - 请求完成: GET http://localhost:8000/docs 状态码: 200 耗时: 0.001s
2025-07-25 18:32:49,129 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/auth/login
2025-07-25 18:32:49,129 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/auth/login 状态码: 200 耗时: 0.000s
2025-07-25 18:32:49,132 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/auth/login
2025-07-25 18:32:49,457 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/auth/login 状态码: 200 耗时: 0.324s
2025-07-25 18:33:02,756 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/auth/register
2025-07-25 18:33:02,757 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/auth/register 状态码: 200 耗时: 0.001s
2025-07-25 18:33:02,758 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/auth/register
2025-07-25 18:33:03,064 [INFO] app.services.user_service:202 - 用户创建成功: <EMAIL>
2025-07-25 18:33:03,074 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/auth/register 状态码: 200 耗时: 0.316s
2025-07-25 18:33:15,956 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/auth/me
2025-07-25 18:33:15,956 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/auth/me 状态码: 200 耗时: 0.000s
2025-07-25 18:33:15,958 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/auth/me
2025-07-25 18:33:15,959 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/auth/me 状态码: 403 耗时: 0.001s
2025-07-25 18:33:22,560 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/auth/refresh
2025-07-25 18:33:22,560 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/auth/refresh 状态码: 200 耗时: 0.000s
2025-07-25 18:33:22,562 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/auth/refresh
2025-07-25 18:33:22,565 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/auth/refresh 状态码: 422 耗时: 0.003s
2025-07-25 18:33:36,681 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/
2025-07-25 18:33:36,681 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/ 状态码: 200 耗时: 0.000s
2025-07-25 18:33:36,684 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/interfaces/
2025-07-25 18:33:38,162 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 1/3): 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 18:33:39,415 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 2/3): 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 18:33:40,669 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 3/3): 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 18:33:40,669 [ERROR] app.services.tvbox_decryptor:86 - 解密配置URL失败: 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 18:33:40,676 [ERROR] app.services.interface_service:155 - 解析接口失败: 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 18:33:40,678 [INFO] app.services.interface_service:64 - 接口源创建成功: 测试接口
2025-07-25 18:33:40,680 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/interfaces/ 状态码: 200 耗时: 3.997s
2025-07-25 18:33:56,773 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/?page=1&limit=10
2025-07-25 18:33:56,774 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/?page=1&limit=10 状态码: 200 耗时: 0.001s
2025-07-25 18:33:56,776 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/?page=1&limit=10
2025-07-25 18:33:56,785 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/?page=1&limit=10 状态码: 200 耗时: 0.009s
2025-07-25 18:34:05,567 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/subscriptions/
2025-07-25 18:34:05,568 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/subscriptions/ 状态码: 200 耗时: 0.001s
2025-07-25 18:34:05,569 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/subscriptions/
2025-07-25 18:34:05,570 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/subscriptions/ 状态码: 404 耗时: 0.001s
2025-07-25 18:34:08,776 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/subscriptions/
2025-07-25 18:34:08,777 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/subscriptions/ 状态码: 200 耗时: 0.001s
2025-07-25 18:34:08,780 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/subscriptions/
2025-07-25 18:34:08,780 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/subscriptions/ 状态码: 404 耗时: 0.000s
2025-07-25 18:34:16,369 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/auth/login
2025-07-25 18:34:16,656 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/auth/login 状态码: 200 耗时: 0.287s
2025-07-25 18:34:17,661 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/auth/register
2025-07-25 18:34:17,664 [ERROR] app.core.database:75 - 数据库会话错误: 400: 邮箱已被注册
2025-07-25 18:34:17,665 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/auth/register 状态码: 400 耗时: 0.004s
2025-07-25 18:34:18,681 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/auth/me
2025-07-25 18:34:18,682 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/auth/me 状态码: 403 耗时: 0.001s
2025-07-25 18:34:19,692 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/auth/refresh
2025-07-25 18:34:19,693 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/auth/refresh 状态码: 422 耗时: 0.001s
2025-07-25 18:34:20,705 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/?page=1&limit=10
2025-07-25 18:34:20,708 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/?page=1&limit=10 状态码: 200 耗时: 0.003s
2025-07-25 18:34:21,724 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/interfaces/
2025-07-25 18:34:21,726 [ERROR] app.api.v1.interfaces:207 - 创建接口失败: 400: 接口URL已存在
2025-07-25 18:34:21,726 [ERROR] app.core.database:75 - 数据库会话错误: 500: 创建接口失败: 400: 接口URL已存在
2025-07-25 18:34:21,728 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/interfaces/ 状态码: 500 耗时: 0.004s
2025-07-25 18:34:22,742 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/1
2025-07-25 18:34:22,742 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/1 状态码: 200 耗时: 0.000s
2025-07-25 18:34:22,744 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/interfaces/1
2025-07-25 18:34:22,747 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/interfaces/1 状态码: 200 耗时: 0.003s
2025-07-25 18:34:23,765 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/1
2025-07-25 18:34:23,767 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/1 状态码: 200 耗时: 0.001s
2025-07-25 18:34:23,769 [INFO] root:149 - 请求开始: PUT http://localhost:8000/api/v1/interfaces/1
2025-07-25 18:34:23,773 [ERROR] app.api.v1.interfaces:266 - 更新接口失败: InterfaceService.update_interface_source() got an unexpected keyword argument 'update_data'
2025-07-25 18:34:23,773 [ERROR] app.core.database:75 - 数据库会话错误: 500: 更新接口失败: InterfaceService.update_interface_source() got an unexpected keyword argument 'update_data'
2025-07-25 18:34:23,774 [INFO] root:158 - 请求完成: PUT http://localhost:8000/api/v1/interfaces/1 状态码: 500 耗时: 0.005s
2025-07-25 18:34:24,782 [INFO] root:149 - 请求开始: DELETE http://localhost:8000/api/v1/interfaces/1
2025-07-25 18:34:24,795 [INFO] app.services.interface_service:287 - 接口源删除成功: 测试接口
2025-07-25 18:34:24,797 [INFO] root:158 - 请求完成: DELETE http://localhost:8000/api/v1/interfaces/1 状态码: 200 耗时: 0.015s
2025-07-25 18:34:25,806 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/1/test
2025-07-25 18:34:25,807 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/1/test 状态码: 200 耗时: 0.001s
2025-07-25 18:34:25,808 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/interfaces/1/test
2025-07-25 18:34:25,810 [ERROR] app.api.v1.interfaces:301 - 测试接口失败: 404: 接口源不存在
2025-07-25 18:34:25,811 [ERROR] app.core.database:75 - 数据库会话错误: 500: 测试接口失败: 404: 接口源不存在
2025-07-25 18:34:25,812 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/interfaces/1/test 状态码: 500 耗时: 0.003s
2025-07-25 18:34:26,820 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/interfaces/1/refresh
2025-07-25 18:34:26,821 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/interfaces/1/refresh 状态码: 200 耗时: 0.000s
2025-07-25 18:34:26,823 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/interfaces/1/refresh
2025-07-25 18:34:26,825 [ERROR] app.api.v1.interfaces:319 - 刷新接口失败: 404: 接口源不存在
2025-07-25 18:34:26,825 [ERROR] app.core.database:75 - 数据库会话错误: 500: 刷新接口失败: 404: 接口源不存在
2025-07-25 18:34:26,826 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/interfaces/1/refresh 状态码: 500 耗时: 0.003s
2025-07-25 18:34:27,833 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/subscriptions/
2025-07-25 18:34:27,833 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/subscriptions/ 状态码: 404 耗时: 0.000s
2025-07-25 18:34:28,848 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/subscriptions/
2025-07-25 18:34:28,849 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/subscriptions/ 状态码: 404 耗时: 0.002s
2025-07-25 18:34:29,858 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/subscriptions/1
2025-07-25 18:34:29,858 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/subscriptions/1 状态码: 200 耗时: 0.000s
2025-07-25 18:34:29,862 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/subscriptions/1
2025-07-25 18:34:29,863 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/subscriptions/1 状态码: 404 耗时: 0.001s
2025-07-25 18:34:30,870 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/subscriptions/1/config
2025-07-25 18:34:30,871 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/subscriptions/1/config 状态码: 404 耗时: 0.001s
2025-07-25 18:34:31,876 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/system/stats
2025-07-25 18:34:31,876 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/system/stats 状态码: 200 耗时: 0.001s
2025-07-25 18:34:31,879 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/system/stats
2025-07-25 18:34:31,880 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/system/stats 状态码: 404 耗时: 0.001s
2025-07-25 18:34:32,898 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/system/settings
2025-07-25 18:34:32,899 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/system/settings 状态码: 200 耗时: 0.000s
2025-07-25 18:34:32,901 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/system/settings
2025-07-25 18:34:32,901 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/system/settings 状态码: 404 耗时: 0.000s
2025-07-25 18:34:33,908 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/decrypt/url
2025-07-25 18:34:33,909 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/decrypt/url 状态码: 200 耗时: 0.001s
2025-07-25 18:34:33,911 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/decrypt/url
2025-07-25 18:34:33,912 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/decrypt/url 状态码: 404 耗时: 0.001s
2025-07-25 18:34:34,919 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8000/api/v1/decrypt/content
2025-07-25 18:34:34,921 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8000/api/v1/decrypt/content 状态码: 200 耗时: 0.001s
2025-07-25 18:34:34,922 [INFO] root:149 - 请求开始: POST http://localhost:8000/api/v1/decrypt/content
2025-07-25 18:34:34,924 [INFO] root:158 - 请求完成: POST http://localhost:8000/api/v1/decrypt/content 状态码: 404 耗时: 0.001s
2025-07-25 18:34:35,935 [INFO] root:149 - 请求开始: GET http://localhost:8000/api/v1/decrypt/methods
2025-07-25 18:34:35,935 [INFO] root:158 - 请求完成: GET http://localhost:8000/api/v1/decrypt/methods 状态码: 404 耗时: 0.000s
2025-07-25 18:42:18,789 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 19:14:50,051 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:14:50,076 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:14:50,135 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 19:14:50,135 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 19:14:50,146 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:14:50,152 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:14:50,206 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 19:14:50,206 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 19:15:15,688 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:15:15,694 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:15:15,739 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 19:15:15,739 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 19:19:46,196 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 19:20:38,650 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:20:38,670 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:20:38,749 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 19:20:38,751 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 19:21:12,726 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:21:12,734 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:21:12,790 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 19:21:12,790 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 19:21:58,867 [INFO] root:149 - 请求开始: GET http://localhost:8001/docs
2025-07-25 19:21:58,868 [INFO] root:158 - 请求完成: GET http://localhost:8001/docs 状态码: 200 耗时: 0.001s
2025-07-25 19:22:10,524 [INFO] root:149 - 请求开始: GET http://localhost:8001/docs
2025-07-25 19:22:10,524 [INFO] root:158 - 请求完成: GET http://localhost:8001/docs 状态码: 200 耗时: 0.000s
2025-07-25 19:22:35,694 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/health
2025-07-25 19:22:35,695 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/health 状态码: 200 耗时: 0.001s
2025-07-25 19:22:52,224 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/health
2025-07-25 19:22:52,224 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/health 状态码: 200 耗时: 0.000s
2025-07-25 19:23:36,097 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:23:36,102 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:23:36,147 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 19:23:36,148 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 19:26:06,206 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/auth/login
2025-07-25 19:26:06,334 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.128s
2025-07-25 19:26:06,492 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/login
2025-07-25 19:26:08,717 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 2.225s
2025-07-25 19:26:09,733 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/auth/register
2025-07-25 19:26:09,744 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/auth/register 状态码: 200 耗时: 0.011s
2025-07-25 19:26:09,761 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/register
2025-07-25 19:26:09,799 [ERROR] app.core.database:75 - 数据库会话错误: 400: 邮箱已被注册
2025-07-25 19:26:09,803 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/register 状态码: 400 耗时: 0.043s
2025-07-25 19:26:10,854 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/auth/me
2025-07-25 19:26:10,934 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/auth/me 状态码: 200 耗时: 0.080s
2025-07-25 19:26:11,072 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me
2025-07-25 19:26:11,739 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me 状态码: 200 耗时: 0.667s
2025-07-25 19:26:12,963 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/auth/refresh
2025-07-25 19:26:12,965 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/auth/refresh 状态码: 200 耗时: 0.002s
2025-07-25 19:26:12,971 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/refresh
2025-07-25 19:26:12,975 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/refresh 状态码: 422 耗时: 0.004s
2025-07-25 19:26:13,992 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/?page=1&limit=10
2025-07-25 19:26:14,000 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/?page=1&limit=10 状态码: 200 耗时: 0.008s
2025-07-25 19:26:14,035 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?page=1&limit=10
2025-07-25 19:26:14,055 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?page=1&limit=10 状态码: 200 耗时: 0.020s
2025-07-25 19:26:15,299 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/
2025-07-25 19:26:15,305 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/ 状态码: 200 耗时: 0.006s
2025-07-25 19:26:15,320 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/
2025-07-25 19:26:16,895 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 1/3): 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 19:26:18,160 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 2/3): 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 19:26:19,419 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 3/3): 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 19:26:19,420 [ERROR] app.services.tvbox_decryptor:86 - 解密配置URL失败: 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 19:26:19,427 [ERROR] app.services.interface_service:155 - 解析接口失败: 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 19:26:19,430 [INFO] app.services.interface_service:64 - 接口源创建成功: 测试接口
2025-07-25 19:26:19,432 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/ 状态码: 200 耗时: 4.112s
2025-07-25 19:26:20,451 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/1
2025-07-25 19:26:20,452 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.001s
2025-07-25 19:26:20,454 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1
2025-07-25 19:26:20,456 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.002s
2025-07-25 19:26:21,464 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/1
2025-07-25 19:26:21,465 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.001s
2025-07-25 19:26:21,467 [INFO] root:149 - 请求开始: PUT http://localhost:8001/api/v1/interfaces/1
2025-07-25 19:26:21,471 [ERROR] app.api.v1.interfaces:267 - 更新接口失败: InterfaceService.update_interface_source() got an unexpected keyword argument 'update_data'
2025-07-25 19:26:21,472 [ERROR] app.core.database:75 - 数据库会话错误: 500: 更新接口失败: InterfaceService.update_interface_source() got an unexpected keyword argument 'update_data'
2025-07-25 19:26:21,472 [INFO] root:158 - 请求完成: PUT http://localhost:8001/api/v1/interfaces/1 状态码: 500 耗时: 0.005s
2025-07-25 19:26:22,490 [INFO] root:149 - 请求开始: DELETE http://localhost:8001/api/v1/interfaces/1
2025-07-25 19:26:22,506 [INFO] app.services.interface_service:287 - 接口源删除成功: 测试接口
2025-07-25 19:26:22,506 [INFO] root:158 - 请求完成: DELETE http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.017s
2025-07-25 19:26:23,517 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/1/test
2025-07-25 19:26:23,518 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/1/test 状态码: 200 耗时: 0.001s
2025-07-25 19:26:23,521 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/test
2025-07-25 19:26:23,523 [ERROR] app.api.v1.interfaces:302 - 测试接口失败: 404: 接口源不存在
2025-07-25 19:26:23,523 [ERROR] app.core.database:75 - 数据库会话错误: 500: 测试接口失败: 404: 接口源不存在
2025-07-25 19:26:23,525 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/test 状态码: 500 耗时: 0.004s
2025-07-25 19:26:24,546 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/1/refresh
2025-07-25 19:26:24,547 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/1/refresh 状态码: 200 耗时: 0.001s
2025-07-25 19:26:24,550 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/refresh
2025-07-25 19:26:24,551 [ERROR] app.api.v1.interfaces:320 - 刷新接口失败: 404: 接口源不存在
2025-07-25 19:26:24,552 [ERROR] app.core.database:75 - 数据库会话错误: 500: 刷新接口失败: 404: 接口源不存在
2025-07-25 19:26:24,554 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/refresh 状态码: 500 耗时: 0.004s
2025-07-25 19:26:25,570 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/subscriptions/
2025-07-25 19:26:25,571 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/subscriptions/ 状态码: 200 耗时: 0.001s
2025-07-25 19:26:25,572 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions/
2025-07-25 19:26:25,866 [ERROR] app.api.v1.subscriptions:73 - 获取订阅列表异常: 'dict' object has no attribute 'id'
2025-07-25 19:26:25,868 [ERROR] app.core.database:75 - 数据库会话错误: 500: 获取订阅列表失败
2025-07-25 19:26:25,868 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions/ 状态码: 500 耗时: 0.295s
2025-07-25 19:26:26,879 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/subscriptions/
2025-07-25 19:26:26,880 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/subscriptions/ 状态码: 200 耗时: 0.001s
2025-07-25 19:26:26,882 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/subscriptions/
2025-07-25 19:26:27,170 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/subscriptions/ 状态码: 422 耗时: 0.288s
2025-07-25 19:26:28,184 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/subscriptions/1
2025-07-25 19:26:28,188 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/subscriptions/1 状态码: 200 耗时: 0.004s
2025-07-25 19:26:28,190 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions/1
2025-07-25 19:26:28,498 [ERROR] app.api.v1.subscriptions:161 - 获取订阅详情异常: 'dict' object has no attribute 'user_id'
2025-07-25 19:26:28,498 [ERROR] app.core.database:75 - 数据库会话错误: 500: 获取订阅详情失败
2025-07-25 19:26:28,499 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions/1 状态码: 500 耗时: 0.309s
2025-07-25 19:26:29,507 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions/1/config
2025-07-25 19:26:29,509 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions/1/config 状态码: 200 耗时: 0.002s
2025-07-25 19:26:30,524 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/system/stats
2025-07-25 19:26:30,525 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/system/stats 状态码: 200 耗时: 0.001s
2025-07-25 19:26:30,528 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats
2025-07-25 19:26:30,805 [ERROR] app.api.v1.system:117 - 获取系统统计异常: 'dict' object has no attribute 'role'
2025-07-25 19:26:30,805 [ERROR] app.core.database:75 - 数据库会话错误: 500: 获取系统统计失败
2025-07-25 19:26:30,806 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats 状态码: 500 耗时: 0.278s
2025-07-25 19:26:31,820 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/system/settings
2025-07-25 19:26:31,821 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/system/settings 状态码: 200 耗时: 0.001s
2025-07-25 19:26:31,822 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/settings
2025-07-25 19:26:32,100 [ERROR] app.api.v1.system:173 - 获取系统设置异常: 'dict' object has no attribute 'role'
2025-07-25 19:26:32,100 [ERROR] app.core.database:75 - 数据库会话错误: 500: 获取系统设置失败
2025-07-25 19:26:32,101 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/settings 状态码: 500 耗时: 0.279s
2025-07-25 19:26:33,110 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/decrypt/url
2025-07-25 19:26:33,111 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/decrypt/url 状态码: 200 耗时: 0.001s
2025-07-25 19:26:33,115 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/decrypt/url
2025-07-25 19:26:33,117 [INFO] app.api.v1.decrypt:55 - 开始解密URL: https://example.com/config.json
2025-07-25 19:26:33,954 [ERROR] app.core.tvbox_decryptor:51 - 解密URL失败: 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 19:26:33,955 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/decrypt/url 状态码: 200 耗时: 0.841s
2025-07-25 19:26:34,961 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/decrypt/content
2025-07-25 19:26:34,962 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/decrypt/content 状态码: 200 耗时: 0.001s
2025-07-25 19:26:34,964 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/decrypt/content
2025-07-25 19:26:34,966 [INFO] app.api.v1.decrypt:96 - 开始解密内容，方法: base64
2025-07-25 19:26:34,966 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/decrypt/content 状态码: 200 耗时: 0.002s
2025-07-25 19:26:35,972 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/decrypt/methods
2025-07-25 19:26:35,973 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/decrypt/methods 状态码: 200 耗时: 0.001s
2025-07-25 19:27:15,862 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/login
2025-07-25 19:27:16,145 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.283s
2025-07-25 19:27:17,153 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/register
2025-07-25 19:27:17,155 [ERROR] app.core.database:75 - 数据库会话错误: 400: 邮箱已被注册
2025-07-25 19:27:17,156 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/register 状态码: 400 耗时: 0.004s
2025-07-25 19:27:18,165 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me
2025-07-25 19:27:18,445 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me 状态码: 200 耗时: 0.280s
2025-07-25 19:27:19,452 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/refresh
2025-07-25 19:27:19,453 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/refresh 状态码: 422 耗时: 0.001s
2025-07-25 19:27:20,466 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?page=1&limit=10
2025-07-25 19:27:20,469 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?page=1&limit=10 状态码: 200 耗时: 0.003s
2025-07-25 19:27:21,480 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/
2025-07-25 19:27:23,016 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 1/3): 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 19:27:24,294 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 2/3): 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 19:27:25,567 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 3/3): 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 19:27:25,568 [ERROR] app.services.tvbox_decryptor:86 - 解密配置URL失败: 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 19:27:25,573 [ERROR] app.services.interface_service:155 - 解析接口失败: 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 19:27:25,574 [INFO] app.services.interface_service:64 - 接口源创建成功: 测试接口
2025-07-25 19:27:25,575 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/ 状态码: 200 耗时: 4.095s
2025-07-25 19:27:26,590 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1
2025-07-25 19:27:26,594 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.004s
2025-07-25 19:27:27,599 [INFO] root:149 - 请求开始: PUT http://localhost:8001/api/v1/interfaces/1
2025-07-25 19:27:27,601 [ERROR] app.api.v1.interfaces:267 - 更新接口失败: InterfaceService.update_interface_source() got an unexpected keyword argument 'update_data'
2025-07-25 19:27:27,602 [ERROR] app.core.database:75 - 数据库会话错误: 500: 更新接口失败: InterfaceService.update_interface_source() got an unexpected keyword argument 'update_data'
2025-07-25 19:27:27,602 [INFO] root:158 - 请求完成: PUT http://localhost:8001/api/v1/interfaces/1 状态码: 500 耗时: 0.003s
2025-07-25 19:27:28,612 [INFO] root:149 - 请求开始: DELETE http://localhost:8001/api/v1/interfaces/1
2025-07-25 19:27:28,619 [INFO] app.services.interface_service:287 - 接口源删除成功: 测试接口
2025-07-25 19:27:28,620 [INFO] root:158 - 请求完成: DELETE http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.008s
2025-07-25 19:27:29,625 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/test
2025-07-25 19:27:29,629 [ERROR] app.api.v1.interfaces:302 - 测试接口失败: 404: 接口源不存在
2025-07-25 19:27:29,629 [ERROR] app.core.database:75 - 数据库会话错误: 500: 测试接口失败: 404: 接口源不存在
2025-07-25 19:27:29,630 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/test 状态码: 500 耗时: 0.005s
2025-07-25 19:27:30,641 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/refresh
2025-07-25 19:27:30,643 [ERROR] app.api.v1.interfaces:320 - 刷新接口失败: 404: 接口源不存在
2025-07-25 19:27:30,644 [ERROR] app.core.database:75 - 数据库会话错误: 500: 刷新接口失败: 404: 接口源不存在
2025-07-25 19:27:30,646 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/refresh 状态码: 500 耗时: 0.005s
2025-07-25 19:27:31,656 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions/
2025-07-25 19:27:31,926 [ERROR] app.api.v1.subscriptions:73 - 获取订阅列表异常: 'dict' object has no attribute 'id'
2025-07-25 19:27:31,926 [ERROR] app.core.database:75 - 数据库会话错误: 500: 获取订阅列表失败
2025-07-25 19:27:31,927 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions/ 状态码: 500 耗时: 0.271s
2025-07-25 19:27:32,933 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/subscriptions/
2025-07-25 19:27:33,206 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/subscriptions/ 状态码: 422 耗时: 0.272s
2025-07-25 19:27:34,214 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions/1
2025-07-25 19:27:34,485 [ERROR] app.api.v1.subscriptions:161 - 获取订阅详情异常: 'dict' object has no attribute 'user_id'
2025-07-25 19:27:34,486 [ERROR] app.core.database:75 - 数据库会话错误: 500: 获取订阅详情失败
2025-07-25 19:27:34,486 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions/1 状态码: 500 耗时: 0.272s
2025-07-25 19:27:35,498 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions/1/config
2025-07-25 19:27:35,500 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions/1/config 状态码: 200 耗时: 0.002s
2025-07-25 19:27:36,513 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/users/?page=1&limit=10
2025-07-25 19:27:36,514 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/users/?page=1&limit=10 状态码: 200 耗时: 0.001s
2025-07-25 19:27:36,516 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/users/?page=1&limit=10
2025-07-25 19:27:36,787 [ERROR] app.api.v1.users:116 - 获取用户列表异常: 'dict' object has no attribute 'role'
2025-07-25 19:27:36,787 [ERROR] app.core.database:75 - 数据库会话错误: 500: 获取用户列表失败
2025-07-25 19:27:36,788 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/users/?page=1&limit=10 状态码: 500 耗时: 0.272s
2025-07-25 19:27:37,806 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/users/
2025-07-25 19:27:37,807 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/users/ 状态码: 200 耗时: 0.001s
2025-07-25 19:27:37,808 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/users/
2025-07-25 19:27:38,088 [ERROR] app.api.v1.users:181 - 创建用户异常: 'dict' object has no attribute 'role'
2025-07-25 19:27:38,088 [ERROR] app.core.database:75 - 数据库会话错误: 500: 创建用户失败
2025-07-25 19:27:38,089 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/users/ 状态码: 500 耗时: 0.280s
2025-07-25 19:27:39,099 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats
2025-07-25 19:27:39,372 [ERROR] app.api.v1.system:117 - 获取系统统计异常: 'dict' object has no attribute 'role'
2025-07-25 19:27:39,372 [ERROR] app.core.database:75 - 数据库会话错误: 500: 获取系统统计失败
2025-07-25 19:27:39,373 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats 状态码: 500 耗时: 0.275s
2025-07-25 19:27:40,387 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/settings
2025-07-25 19:27:40,658 [ERROR] app.api.v1.system:173 - 获取系统设置异常: 'dict' object has no attribute 'role'
2025-07-25 19:27:40,658 [ERROR] app.core.database:75 - 数据库会话错误: 500: 获取系统设置失败
2025-07-25 19:27:40,660 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/settings 状态码: 500 耗时: 0.272s
2025-07-25 19:27:41,665 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/decrypt/url
2025-07-25 19:27:41,666 [INFO] app.api.v1.decrypt:55 - 开始解密URL: https://example.com/config.json
2025-07-25 19:27:42,441 [ERROR] app.core.tvbox_decryptor:51 - 解密URL失败: 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 19:27:42,441 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/decrypt/url 状态码: 200 耗时: 0.776s
2025-07-25 19:27:43,461 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/decrypt/content
2025-07-25 19:27:43,464 [INFO] app.api.v1.decrypt:96 - 开始解密内容，方法: base64
2025-07-25 19:27:43,464 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/decrypt/content 状态码: 200 耗时: 0.002s
2025-07-25 19:27:44,481 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/decrypt/methods
2025-07-25 19:27:44,482 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/decrypt/methods 状态码: 200 耗时: 0.000s
2025-07-25 19:30:34,658 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 19:30:38,361 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:30:38,370 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:30:38,425 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 19:30:38,425 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 19:32:21,625 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 19:32:25,722 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:32:25,727 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:32:25,770 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 19:32:25,770 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 19:32:50,098 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:32:50,103 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:32:50,149 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 19:32:50,150 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 19:33:15,146 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/login
2025-07-25 19:33:15,467 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.321s
2025-07-25 19:33:16,485 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/register
2025-07-25 19:33:16,501 [ERROR] app.core.database:75 - 数据库会话错误: 400: 邮箱已被注册
2025-07-25 19:33:16,501 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/register 状态码: 400 耗时: 0.016s
2025-07-25 19:33:17,518 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me
2025-07-25 19:33:17,802 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me 状态码: 200 耗时: 0.285s
2025-07-25 19:33:18,821 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/refresh
2025-07-25 19:33:18,823 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/refresh 状态码: 422 耗时: 0.003s
2025-07-25 19:33:19,833 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?page=1&limit=10
2025-07-25 19:33:19,844 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?page=1&limit=10 状态码: 200 耗时: 0.011s
2025-07-25 19:33:20,851 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/
2025-07-25 19:33:21,928 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 1/3): 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 19:33:23,246 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 2/3): 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 19:33:24,567 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 3/3): 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 19:33:24,568 [ERROR] app.services.tvbox_decryptor:86 - 解密配置URL失败: 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 19:33:24,575 [ERROR] app.services.interface_service:155 - 解析接口失败: 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 19:33:24,577 [INFO] app.services.interface_service:64 - 接口源创建成功: 测试接口
2025-07-25 19:33:24,580 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/ 状态码: 200 耗时: 3.729s
2025-07-25 19:33:25,592 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1
2025-07-25 19:33:25,598 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.005s
2025-07-25 19:33:26,608 [INFO] root:149 - 请求开始: PUT http://localhost:8001/api/v1/interfaces/1
2025-07-25 19:33:26,613 [ERROR] app.api.v1.interfaces:267 - 更新接口失败: InterfaceService.update_interface_source() got an unexpected keyword argument 'update_data'
2025-07-25 19:33:26,614 [ERROR] app.core.database:75 - 数据库会话错误: 500: 更新接口失败: InterfaceService.update_interface_source() got an unexpected keyword argument 'update_data'
2025-07-25 19:33:26,615 [INFO] root:158 - 请求完成: PUT http://localhost:8001/api/v1/interfaces/1 状态码: 500 耗时: 0.008s
2025-07-25 19:33:27,621 [INFO] root:149 - 请求开始: DELETE http://localhost:8001/api/v1/interfaces/1
2025-07-25 19:33:27,638 [INFO] app.services.interface_service:287 - 接口源删除成功: 测试接口
2025-07-25 19:33:27,639 [INFO] root:158 - 请求完成: DELETE http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.018s
2025-07-25 19:33:28,653 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/test
2025-07-25 19:33:28,656 [ERROR] app.api.v1.interfaces:302 - 测试接口失败: 404: 接口源不存在
2025-07-25 19:33:28,656 [ERROR] app.core.database:75 - 数据库会话错误: 500: 测试接口失败: 404: 接口源不存在
2025-07-25 19:33:28,657 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/test 状态码: 500 耗时: 0.005s
2025-07-25 19:33:29,665 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/refresh
2025-07-25 19:33:29,669 [ERROR] app.api.v1.interfaces:320 - 刷新接口失败: 404: 接口源不存在
2025-07-25 19:33:29,670 [ERROR] app.core.database:75 - 数据库会话错误: 500: 刷新接口失败: 404: 接口源不存在
2025-07-25 19:33:29,671 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/refresh 状态码: 500 耗时: 0.006s
2025-07-25 19:33:30,682 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions/
2025-07-25 19:33:30,963 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions/ 状态码: 200 耗时: 0.280s
2025-07-25 19:33:31,977 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/subscriptions/
2025-07-25 19:33:31,982 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/subscriptions/ 状态码: 422 耗时: 0.005s
2025-07-25 19:33:32,990 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions/1
2025-07-25 19:33:33,271 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions/1 状态码: 200 耗时: 0.280s
2025-07-25 19:33:34,282 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions/1/config
2025-07-25 19:33:34,285 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions/1/config 状态码: 200 耗时: 0.003s
2025-07-25 19:33:35,290 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats
2025-07-25 19:33:35,575 [ERROR] app.api.v1.system:117 - 获取系统统计异常: 'dict' object has no attribute 'role'
2025-07-25 19:33:35,576 [ERROR] app.core.database:75 - 数据库会话错误: 500: 获取系统统计失败
2025-07-25 19:33:35,577 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats 状态码: 500 耗时: 0.286s
2025-07-25 19:33:36,587 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/settings
2025-07-25 19:33:36,875 [ERROR] app.api.v1.system:173 - 获取系统设置异常: 'dict' object has no attribute 'role'
2025-07-25 19:33:36,876 [ERROR] app.core.database:75 - 数据库会话错误: 500: 获取系统设置失败
2025-07-25 19:33:36,876 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/settings 状态码: 500 耗时: 0.289s
2025-07-25 19:33:37,895 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/decrypt/url
2025-07-25 19:33:37,899 [INFO] app.api.v1.decrypt:55 - 开始解密URL: https://example.com/config.json
2025-07-25 19:33:38,826 [ERROR] app.core.tvbox_decryptor:51 - 解密URL失败: 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 19:33:38,827 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/decrypt/url 状态码: 200 耗时: 0.932s
2025-07-25 19:33:39,838 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/decrypt/content
2025-07-25 19:33:39,842 [INFO] app.api.v1.decrypt:96 - 开始解密内容，方法: base64
2025-07-25 19:33:39,842 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/decrypt/content 状态码: 200 耗时: 0.004s
2025-07-25 19:33:40,864 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/decrypt/methods
2025-07-25 19:33:40,866 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/decrypt/methods 状态码: 200 耗时: 0.001s
2025-07-25 19:37:22,734 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 19:37:26,848 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:37:26,856 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:37:26,902 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 19:37:26,903 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 19:40:05,496 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:40:05,502 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:40:05,555 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 19:40:05,555 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 19:40:20,402 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/auth/login
2025-07-25 19:40:20,403 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.001s
2025-07-25 19:40:20,404 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/login
2025-07-25 19:40:20,730 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.325s
2025-07-25 19:40:21,734 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/auth/register
2025-07-25 19:40:21,735 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/auth/register 状态码: 200 耗时: 0.001s
2025-07-25 19:40:21,737 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/register
2025-07-25 19:40:21,751 [ERROR] app.core.database:75 - 数据库会话错误: 400: 邮箱已被注册
2025-07-25 19:40:21,751 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/register 状态码: 400 耗时: 0.014s
2025-07-25 19:40:22,768 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/auth/me
2025-07-25 19:40:22,768 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/auth/me 状态码: 200 耗时: 0.001s
2025-07-25 19:40:22,771 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me
2025-07-25 19:40:23,058 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me 状态码: 200 耗时: 0.287s
2025-07-25 19:40:24,066 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/auth/refresh
2025-07-25 19:40:24,067 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/auth/refresh 状态码: 200 耗时: 0.001s
2025-07-25 19:40:24,070 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/refresh
2025-07-25 19:40:24,072 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/refresh 状态码: 422 耗时: 0.003s
2025-07-25 19:40:25,078 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/?page=1&limit=10
2025-07-25 19:40:25,079 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/?page=1&limit=10 状态码: 200 耗时: 0.001s
2025-07-25 19:40:25,081 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?page=1&limit=10
2025-07-25 19:40:25,091 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?page=1&limit=10 状态码: 200 耗时: 0.010s
2025-07-25 19:40:26,105 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/
2025-07-25 19:40:26,106 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/ 状态码: 200 耗时: 0.001s
2025-07-25 19:40:26,109 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/
2025-07-25 19:40:27,570 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 1/3): 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 19:40:28,832 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 2/3): 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 19:40:30,124 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 3/3): 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 19:40:30,125 [ERROR] app.services.tvbox_decryptor:86 - 解密配置URL失败: 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 19:40:30,135 [ERROR] app.services.interface_service:155 - 解析接口失败: 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 19:40:30,139 [INFO] app.services.interface_service:64 - 接口源创建成功: 测试接口
2025-07-25 19:40:30,140 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/ 状态码: 200 耗时: 4.031s
2025-07-25 19:40:31,156 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/1
2025-07-25 19:40:31,157 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.001s
2025-07-25 19:40:31,158 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1
2025-07-25 19:40:31,161 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.004s
2025-07-25 19:40:32,169 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/1
2025-07-25 19:40:32,170 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.001s
2025-07-25 19:40:32,172 [INFO] root:149 - 请求开始: PUT http://localhost:8001/api/v1/interfaces/1
2025-07-25 19:40:32,174 [ERROR] app.api.v1.interfaces:267 - 更新接口失败: InterfaceService.update_interface_source() got an unexpected keyword argument 'update_data'
2025-07-25 19:40:32,175 [ERROR] app.core.database:75 - 数据库会话错误: 500: 更新接口失败: InterfaceService.update_interface_source() got an unexpected keyword argument 'update_data'
2025-07-25 19:40:32,177 [INFO] root:158 - 请求完成: PUT http://localhost:8001/api/v1/interfaces/1 状态码: 500 耗时: 0.005s
2025-07-25 19:40:33,195 [INFO] root:149 - 请求开始: DELETE http://localhost:8001/api/v1/interfaces/1
2025-07-25 19:40:33,209 [INFO] app.services.interface_service:287 - 接口源删除成功: 测试接口
2025-07-25 19:40:33,210 [INFO] root:158 - 请求完成: DELETE http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.015s
2025-07-25 19:40:34,222 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/1/test
2025-07-25 19:40:34,222 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/1/test 状态码: 200 耗时: 0.000s
2025-07-25 19:40:34,225 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/test
2025-07-25 19:40:34,228 [ERROR] app.api.v1.interfaces:302 - 测试接口失败: 404: 接口源不存在
2025-07-25 19:40:34,228 [ERROR] app.core.database:75 - 数据库会话错误: 500: 测试接口失败: 404: 接口源不存在
2025-07-25 19:40:34,229 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/test 状态码: 500 耗时: 0.005s
2025-07-25 19:40:35,248 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/1/refresh
2025-07-25 19:40:35,248 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/1/refresh 状态码: 200 耗时: 0.001s
2025-07-25 19:40:35,250 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/refresh
2025-07-25 19:40:35,252 [ERROR] app.api.v1.interfaces:320 - 刷新接口失败: 404: 接口源不存在
2025-07-25 19:40:35,252 [ERROR] app.core.database:75 - 数据库会话错误: 500: 刷新接口失败: 404: 接口源不存在
2025-07-25 19:40:35,254 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/refresh 状态码: 500 耗时: 0.004s
2025-07-25 19:40:36,274 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/subscriptions/
2025-07-25 19:40:36,274 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/subscriptions/ 状态码: 200 耗时: 0.000s
2025-07-25 19:40:36,276 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions/
2025-07-25 19:40:36,551 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions/ 状态码: 200 耗时: 0.275s
2025-07-25 19:40:37,567 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/subscriptions/
2025-07-25 19:40:37,568 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/subscriptions/ 状态码: 200 耗时: 0.001s
2025-07-25 19:40:37,569 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/subscriptions/
2025-07-25 19:40:37,572 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/subscriptions/ 状态码: 422 耗时: 0.003s
2025-07-25 19:40:38,581 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/subscriptions/1
2025-07-25 19:40:38,581 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/subscriptions/1 状态码: 200 耗时: 0.000s
2025-07-25 19:40:38,583 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions/1
2025-07-25 19:40:38,866 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions/1 状态码: 200 耗时: 0.283s
2025-07-25 19:40:39,885 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions/1/config
2025-07-25 19:40:39,887 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions/1/config 状态码: 200 耗时: 0.003s
2025-07-25 19:40:40,897 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/system/stats
2025-07-25 19:40:40,898 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/system/stats 状态码: 200 耗时: 0.001s
2025-07-25 19:40:40,901 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats
2025-07-25 19:40:41,175 [ERROR] app.core.database:75 - 数据库会话错误: 403: 需要管理员权限
2025-07-25 19:40:41,175 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats 状态码: 403 耗时: 0.274s
2025-07-25 19:40:42,187 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/system/settings
2025-07-25 19:40:42,188 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/system/settings 状态码: 200 耗时: 0.002s
2025-07-25 19:40:42,191 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/settings
2025-07-25 19:40:42,464 [ERROR] app.core.database:75 - 数据库会话错误: 403: 需要管理员权限
2025-07-25 19:40:42,465 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/settings 状态码: 403 耗时: 0.275s
2025-07-25 19:40:43,482 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/decrypt/url
2025-07-25 19:40:43,483 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/decrypt/url 状态码: 200 耗时: 0.001s
2025-07-25 19:40:43,485 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/decrypt/url
2025-07-25 19:40:43,487 [INFO] app.api.v1.decrypt:55 - 开始解密URL: https://example.com/config.json
2025-07-25 19:40:44,304 [ERROR] app.core.tvbox_decryptor:51 - 解密URL失败: 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 19:40:44,305 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/decrypt/url 状态码: 200 耗时: 0.819s
2025-07-25 19:40:44,948 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 19:40:45,309 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/decrypt/content
2025-07-25 19:40:45,311 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/decrypt/content 状态码: 200 耗时: 0.002s
2025-07-25 19:40:45,312 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/decrypt/content
2025-07-25 19:40:45,316 [INFO] app.api.v1.decrypt:96 - 开始解密内容，方法: base64
2025-07-25 19:40:45,318 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/decrypt/content 状态码: 200 耗时: 0.004s
2025-07-25 19:40:46,322 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/decrypt/methods
2025-07-25 19:40:46,323 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/decrypt/methods 状态码: 200 耗时: 0.002s
2025-07-25 19:40:49,071 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:40:49,080 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:40:49,138 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 19:40:49,140 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 19:45:46,031 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 19:45:50,567 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:45:50,572 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:45:50,617 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 19:45:50,618 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 19:46:02,468 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:46:02,474 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:46:02,524 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 19:46:02,524 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 19:47:26,273 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats
2025-07-25 19:47:26,585 [ERROR] app.core.database:75 - 数据库会话错误: 403: 需要管理员权限
2025-07-25 19:47:26,587 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats 状态码: 403 耗时: 0.313s
2025-07-25 19:50:47,268 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 19:50:52,334 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:50:52,341 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:50:52,404 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 19:50:52,404 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 19:50:56,337 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:50:56,342 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:50:56,389 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 19:50:56,390 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 19:51:19,155 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:51:19,161 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:51:19,210 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 19:51:19,211 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 19:51:41,707 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:51:41,714 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:51:41,773 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 19:51:41,773 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 19:51:41,775 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 19:51:57,791 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:51:57,807 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:51:57,905 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 19:51:57,905 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 19:51:57,906 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 19:52:16,925 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 19:52:16,931 [INFO] root:48 - 数据库表创建完成
2025-07-25 19:52:16,980 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 19:52:16,981 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 19:52:16,982 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 19:54:23,067 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/system/stats
2025-07-25 19:54:23,068 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/system/stats 状态码: 200 耗时: 0.001s
2025-07-25 19:54:23,071 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats
2025-07-25 19:54:23,381 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats 状态码: 200 耗时: 0.311s
2025-07-25 19:54:51,308 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/system/settings
2025-07-25 19:54:51,308 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/system/settings 状态码: 200 耗时: 0.000s
2025-07-25 19:54:51,311 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/settings
2025-07-25 19:54:51,590 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/settings 状态码: 200 耗时: 0.279s
2025-07-25 19:55:17,608 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/auth/login
2025-07-25 19:55:17,608 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.000s
2025-07-25 19:55:17,611 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/login
2025-07-25 19:55:17,914 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.302s
2025-07-25 19:55:18,921 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/auth/register
2025-07-25 19:55:18,921 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/auth/register 状态码: 200 耗时: 0.000s
2025-07-25 19:55:18,924 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/register
2025-07-25 19:55:18,938 [ERROR] app.core.database:75 - 数据库会话错误: 400: 邮箱已被注册
2025-07-25 19:55:18,939 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/register 状态码: 400 耗时: 0.015s
2025-07-25 19:55:19,968 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/auth/me
2025-07-25 19:55:19,969 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/auth/me 状态码: 200 耗时: 0.001s
2025-07-25 19:55:19,970 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me
2025-07-25 19:55:20,246 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me 状态码: 200 耗时: 0.276s
2025-07-25 19:55:21,259 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/auth/refresh
2025-07-25 19:55:21,261 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/auth/refresh 状态码: 200 耗时: 0.002s
2025-07-25 19:55:21,263 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/refresh
2025-07-25 19:55:21,265 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/refresh 状态码: 422 耗时: 0.002s
2025-07-25 19:55:22,271 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/?page=1&limit=10
2025-07-25 19:55:22,272 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/?page=1&limit=10 状态码: 200 耗时: 0.001s
2025-07-25 19:55:22,273 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?page=1&limit=10
2025-07-25 19:55:22,283 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?page=1&limit=10 状态码: 200 耗时: 0.010s
2025-07-25 19:55:23,299 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/
2025-07-25 19:55:23,300 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/ 状态码: 200 耗时: 0.001s
2025-07-25 19:55:23,303 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/
2025-07-25 19:55:24,815 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 1/3): 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 19:55:26,078 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 2/3): 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 19:55:27,362 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 3/3): 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 19:55:27,362 [ERROR] app.services.tvbox_decryptor:86 - 解密配置URL失败: 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 19:55:27,368 [ERROR] app.services.interface_service:155 - 解析接口失败: 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 19:55:27,373 [INFO] app.services.interface_service:64 - 接口源创建成功: 测试接口
2025-07-25 19:55:27,374 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/ 状态码: 200 耗时: 4.071s
2025-07-25 19:55:28,386 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/1
2025-07-25 19:55:28,386 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.000s
2025-07-25 19:55:28,389 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1
2025-07-25 19:55:28,391 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.002s
2025-07-25 19:55:29,399 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/1
2025-07-25 19:55:29,399 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.000s
2025-07-25 19:55:29,403 [INFO] root:149 - 请求开始: PUT http://localhost:8001/api/v1/interfaces/1
2025-07-25 19:55:29,407 [ERROR] app.api.v1.interfaces:267 - 更新接口失败: InterfaceService.update_interface_source() got an unexpected keyword argument 'update_data'
2025-07-25 19:55:29,407 [ERROR] app.core.database:75 - 数据库会话错误: 500: 更新接口失败: InterfaceService.update_interface_source() got an unexpected keyword argument 'update_data'
2025-07-25 19:55:29,408 [INFO] root:158 - 请求完成: PUT http://localhost:8001/api/v1/interfaces/1 状态码: 500 耗时: 0.005s
2025-07-25 19:55:30,429 [INFO] root:149 - 请求开始: DELETE http://localhost:8001/api/v1/interfaces/1
2025-07-25 19:55:30,441 [INFO] app.services.interface_service:287 - 接口源删除成功: 测试接口
2025-07-25 19:55:30,444 [INFO] root:158 - 请求完成: DELETE http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.015s
2025-07-25 19:55:31,454 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/1/test
2025-07-25 19:55:31,455 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/1/test 状态码: 200 耗时: 0.001s
2025-07-25 19:55:31,456 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/test
2025-07-25 19:55:31,459 [ERROR] app.api.v1.interfaces:302 - 测试接口失败: 404: 接口源不存在
2025-07-25 19:55:31,459 [ERROR] app.core.database:75 - 数据库会话错误: 500: 测试接口失败: 404: 接口源不存在
2025-07-25 19:55:31,460 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/test 状态码: 500 耗时: 0.003s
2025-07-25 19:55:32,466 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/1/refresh
2025-07-25 19:55:32,467 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/1/refresh 状态码: 200 耗时: 0.001s
2025-07-25 19:55:32,469 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/refresh
2025-07-25 19:55:32,470 [ERROR] app.api.v1.interfaces:320 - 刷新接口失败: 404: 接口源不存在
2025-07-25 19:55:32,471 [ERROR] app.core.database:75 - 数据库会话错误: 500: 刷新接口失败: 404: 接口源不存在
2025-07-25 19:55:32,471 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/refresh 状态码: 500 耗时: 0.003s
2025-07-25 19:55:33,483 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/subscriptions/
2025-07-25 19:55:33,483 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/subscriptions/ 状态码: 200 耗时: 0.000s
2025-07-25 19:55:33,485 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions/
2025-07-25 19:55:33,760 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions/ 状态码: 200 耗时: 0.276s
2025-07-25 19:55:34,778 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/subscriptions/
2025-07-25 19:55:34,779 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/subscriptions/ 状态码: 200 耗时: 0.001s
2025-07-25 19:55:34,781 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/subscriptions/
2025-07-25 19:55:34,785 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/subscriptions/ 状态码: 422 耗时: 0.004s
2025-07-25 19:55:35,799 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/subscriptions/1
2025-07-25 19:55:35,800 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/subscriptions/1 状态码: 200 耗时: 0.001s
2025-07-25 19:55:35,803 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions/1
2025-07-25 19:55:36,083 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions/1 状态码: 200 耗时: 0.280s
2025-07-25 19:55:37,093 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions/1/config
2025-07-25 19:55:37,095 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions/1/config 状态码: 200 耗时: 0.002s
2025-07-25 19:55:38,107 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats
2025-07-25 19:55:38,382 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats 状态码: 200 耗时: 0.275s
2025-07-25 19:55:39,396 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/settings
2025-07-25 19:55:39,671 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/settings 状态码: 200 耗时: 0.275s
2025-07-25 19:55:40,679 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/decrypt/url
2025-07-25 19:55:40,680 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/decrypt/url 状态码: 200 耗时: 0.001s
2025-07-25 19:55:40,683 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/decrypt/url
2025-07-25 19:55:40,685 [INFO] app.api.v1.decrypt:55 - 开始解密URL: https://example.com/config.json
2025-07-25 19:55:41,541 [ERROR] app.core.tvbox_decryptor:51 - 解密URL失败: 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 19:55:41,543 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/decrypt/url 状态码: 200 耗时: 0.860s
2025-07-25 19:55:42,550 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/decrypt/content
2025-07-25 19:55:42,551 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/decrypt/content 状态码: 200 耗时: 0.001s
2025-07-25 19:55:42,553 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/decrypt/content
2025-07-25 19:55:42,555 [INFO] app.api.v1.decrypt:96 - 开始解密内容，方法: base64
2025-07-25 19:55:42,555 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/decrypt/content 状态码: 200 耗时: 0.002s
2025-07-25 19:55:43,574 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/decrypt/methods
2025-07-25 19:55:43,574 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/decrypt/methods 状态码: 200 耗时: 0.000s
2025-07-25 20:09:00,453 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:09:00,461 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:09:00,520 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 20:09:00,521 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 20:09:47,605 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/auth/login
2025-07-25 20:09:47,605 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.000s
2025-07-25 20:09:47,608 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/login
2025-07-25 20:09:47,930 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.324s
2025-07-25 20:09:48,940 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/auth/register
2025-07-25 20:09:48,941 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/auth/register 状态码: 200 耗时: 0.001s
2025-07-25 20:09:48,943 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/register
2025-07-25 20:09:48,966 [ERROR] app.core.database:75 - 数据库会话错误: 400: 邮箱已被注册
2025-07-25 20:09:48,967 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/register 状态码: 400 耗时: 0.024s
2025-07-25 20:09:49,984 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/auth/me
2025-07-25 20:09:49,985 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/auth/me 状态码: 200 耗时: 0.001s
2025-07-25 20:09:49,987 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me
2025-07-25 20:09:50,267 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me 状态码: 200 耗时: 0.280s
2025-07-25 20:09:51,280 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/auth/refresh
2025-07-25 20:09:51,280 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/auth/refresh 状态码: 200 耗时: 0.000s
2025-07-25 20:09:51,282 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/refresh
2025-07-25 20:09:51,284 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/refresh 状态码: 422 耗时: 0.002s
2025-07-25 20:09:52,294 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/?page=1&limit=10
2025-07-25 20:09:52,294 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/?page=1&limit=10 状态码: 200 耗时: 0.000s
2025-07-25 20:09:52,298 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?page=1&limit=10
2025-07-25 20:09:52,308 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?page=1&limit=10 状态码: 200 耗时: 0.011s
2025-07-25 20:09:53,319 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/
2025-07-25 20:09:53,320 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/ 状态码: 200 耗时: 0.001s
2025-07-25 20:09:53,323 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/
2025-07-25 20:09:54,504 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 1/3): 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 20:09:55,797 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 2/3): 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 20:09:57,107 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 3/3): 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 20:09:57,107 [ERROR] app.services.tvbox_decryptor:86 - 解密配置URL失败: 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 20:09:57,115 [ERROR] app.services.interface_service:155 - 解析接口失败: 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 20:09:57,119 [INFO] app.services.interface_service:64 - 接口源创建成功: 测试接口
2025-07-25 20:09:57,120 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/ 状态码: 200 耗时: 3.799s
2025-07-25 20:09:58,140 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/1
2025-07-25 20:09:58,142 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.002s
2025-07-25 20:09:58,146 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1
2025-07-25 20:09:58,151 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.005s
2025-07-25 20:09:59,160 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/1
2025-07-25 20:09:59,161 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.001s
2025-07-25 20:09:59,163 [INFO] root:149 - 请求开始: PUT http://localhost:8001/api/v1/interfaces/1
2025-07-25 20:09:59,168 [ERROR] app.api.v1.interfaces:245 - 更新接口失败: 1 validation error for InterfaceSourceResponse
tags
  Input should be a valid string [type=string_type, input_value=['测试', '更新'], input_type=list]
    For further information visit https://errors.pydantic.dev/2.10/v/string_type
2025-07-25 20:09:59,168 [ERROR] app.core.database:75 - 数据库会话错误: 500: 更新接口失败: 1 validation error for InterfaceSourceResponse
tags
  Input should be a valid string [type=string_type, input_value=['测试', '更新'], input_type=list]
    For further information visit https://errors.pydantic.dev/2.10/v/string_type
2025-07-25 20:09:59,169 [INFO] root:158 - 请求完成: PUT http://localhost:8001/api/v1/interfaces/1 状态码: 500 耗时: 0.006s
2025-07-25 20:10:00,186 [INFO] root:149 - 请求开始: DELETE http://localhost:8001/api/v1/interfaces/1
2025-07-25 20:10:00,201 [INFO] app.services.interface_service:287 - 接口源删除成功: 测试接口
2025-07-25 20:10:00,203 [INFO] root:158 - 请求完成: DELETE http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.018s
2025-07-25 20:10:01,215 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/1/test
2025-07-25 20:10:01,216 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/1/test 状态码: 200 耗时: 0.001s
2025-07-25 20:10:01,219 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/test
2025-07-25 20:10:01,221 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/test 状态码: 200 耗时: 0.001s
2025-07-25 20:10:02,240 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/1/refresh
2025-07-25 20:10:02,241 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/1/refresh 状态码: 200 耗时: 0.001s
2025-07-25 20:10:02,245 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/refresh
2025-07-25 20:10:02,247 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/refresh 状态码: 200 耗时: 0.002s
2025-07-25 20:10:03,264 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/subscriptions/
2025-07-25 20:10:03,265 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/subscriptions/ 状态码: 200 耗时: 0.001s
2025-07-25 20:10:03,267 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions/
2025-07-25 20:10:03,551 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions/ 状态码: 200 耗时: 0.283s
2025-07-25 20:10:04,555 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/subscriptions/
2025-07-25 20:10:04,557 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/subscriptions/ 状态码: 200 耗时: 0.001s
2025-07-25 20:10:04,559 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/subscriptions/
2025-07-25 20:10:04,563 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/subscriptions/ 状态码: 422 耗时: 0.004s
2025-07-25 20:10:05,566 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/subscriptions/1
2025-07-25 20:10:05,568 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/subscriptions/1 状态码: 200 耗时: 0.002s
2025-07-25 20:10:05,570 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions/1
2025-07-25 20:10:05,845 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions/1 状态码: 200 耗时: 0.275s
2025-07-25 20:10:06,856 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions/1/config
2025-07-25 20:10:06,860 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions/1/config 状态码: 200 耗时: 0.002s
2025-07-25 20:10:07,869 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/users/?page=1&limit=10
2025-07-25 20:10:07,869 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/users/?page=1&limit=10 状态码: 200 耗时: 0.000s
2025-07-25 20:10:07,871 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/users/?page=1&limit=10
2025-07-25 20:10:08,156 [ERROR] app.api.v1.users:121 - 获取用户列表异常: 'User' object has no attribute 'nickname'
2025-07-25 20:10:08,157 [ERROR] app.core.database:75 - 数据库会话错误: 500: 获取用户列表失败
2025-07-25 20:10:08,159 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/users/?page=1&limit=10 状态码: 500 耗时: 0.288s
2025-07-25 20:10:09,174 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/users/
2025-07-25 20:10:09,176 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/users/ 状态码: 200 耗时: 0.002s
2025-07-25 20:10:09,178 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/users/
2025-07-25 20:10:09,479 [ERROR] app.api.v1.users:189 - 创建用户异常: 'UserService' object has no attribute 'get_user_by_username'
2025-07-25 20:10:09,480 [ERROR] app.core.database:75 - 数据库会话错误: 500: 创建用户失败
2025-07-25 20:10:09,480 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/users/ 状态码: 500 耗时: 0.302s
2025-07-25 20:10:10,499 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/system/stats
2025-07-25 20:10:10,500 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/system/stats 状态码: 200 耗时: 0.001s
2025-07-25 20:10:10,503 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats
2025-07-25 20:10:10,781 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats 状态码: 200 耗时: 0.279s
2025-07-25 20:10:11,800 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/system/settings
2025-07-25 20:10:11,800 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/system/settings 状态码: 200 耗时: 0.000s
2025-07-25 20:10:11,803 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/settings
2025-07-25 20:10:12,079 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/settings 状态码: 200 耗时: 0.276s
2025-07-25 20:10:13,093 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/decrypt/url
2025-07-25 20:10:13,093 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/decrypt/url 状态码: 200 耗时: 0.001s
2025-07-25 20:10:13,097 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/decrypt/url
2025-07-25 20:10:13,098 [INFO] app.api.v1.decrypt:55 - 开始解密URL: https://example.com/config.json
2025-07-25 20:10:13,964 [ERROR] app.core.tvbox_decryptor:51 - 解密URL失败: 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 20:10:13,964 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/decrypt/url 状态码: 200 耗时: 0.868s
2025-07-25 20:10:14,979 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/decrypt/content
2025-07-25 20:10:14,979 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/decrypt/content 状态码: 200 耗时: 0.000s
2025-07-25 20:10:14,981 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/decrypt/content
2025-07-25 20:10:14,983 [INFO] app.api.v1.decrypt:96 - 开始解密内容，方法: base64
2025-07-25 20:10:14,983 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/decrypt/content 状态码: 200 耗时: 0.002s
2025-07-25 20:10:15,997 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/decrypt/methods
2025-07-25 20:10:15,997 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/decrypt/methods 状态码: 200 耗时: 0.001s
2025-07-25 20:15:26,908 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 20:15:31,458 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:15:31,464 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:15:31,517 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 20:15:31,517 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 20:18:53,218 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 20:18:57,523 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:18:57,529 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:18:57,580 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 20:18:57,581 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 20:20:26,541 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:20:26,548 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:20:26,619 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 20:20:26,620 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 20:20:49,643 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:20:49,648 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:20:49,908 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 20:20:49,913 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 20:21:31,058 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:21:31,065 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:21:31,130 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 20:21:31,130 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 20:22:28,170 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:22:28,176 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:22:28,226 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 20:22:28,226 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 20:22:54,600 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:22:54,605 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:22:54,657 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 20:22:54,657 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 20:23:37,385 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/auth/login
2025-07-25 20:23:37,385 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.000s
2025-07-25 20:23:37,387 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/login
2025-07-25 20:23:37,713 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.326s
2025-07-25 20:23:38,733 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/auth/register
2025-07-25 20:23:38,735 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/auth/register 状态码: 200 耗时: 0.001s
2025-07-25 20:23:38,736 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/register
2025-07-25 20:23:38,755 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/register 状态码: 200 耗时: 0.018s
2025-07-25 20:23:39,791 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/auth/me
2025-07-25 20:23:39,793 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/auth/me 状态码: 200 耗时: 0.002s
2025-07-25 20:23:39,795 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me
2025-07-25 20:23:40,078 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me 状态码: 200 耗时: 0.282s
2025-07-25 20:23:41,083 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/auth/refresh
2025-07-25 20:23:41,084 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/auth/refresh 状态码: 200 耗时: 0.001s
2025-07-25 20:23:41,085 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/refresh
2025-07-25 20:23:41,089 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/refresh 状态码: 200 耗时: 0.003s
2025-07-25 20:23:42,096 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/?page=1&limit=10
2025-07-25 20:23:42,097 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/?page=1&limit=10 状态码: 200 耗时: 0.001s
2025-07-25 20:23:42,098 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?page=1&limit=10
2025-07-25 20:23:42,109 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?page=1&limit=10 状态码: 200 耗时: 0.011s
2025-07-25 20:23:43,122 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/
2025-07-25 20:23:43,123 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/ 状态码: 200 耗时: 0.002s
2025-07-25 20:23:43,125 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/
2025-07-25 20:23:44,153 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 1/3): 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 20:23:45,418 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 2/3): 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 20:23:46,687 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 3/3): 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 20:23:46,687 [ERROR] app.services.tvbox_decryptor:86 - 解密配置URL失败: 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 20:23:46,694 [ERROR] app.services.interface_service:155 - 解析接口失败: 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 20:23:46,697 [INFO] app.services.interface_service:64 - 接口源创建成功: 测试接口
2025-07-25 20:23:46,698 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/ 状态码: 200 耗时: 3.573s
2025-07-25 20:23:47,718 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/1
2025-07-25 20:23:47,720 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.002s
2025-07-25 20:23:47,722 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1
2025-07-25 20:23:47,725 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.004s
2025-07-25 20:23:48,745 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/1
2025-07-25 20:23:48,746 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.001s
2025-07-25 20:23:48,749 [INFO] root:149 - 请求开始: PUT http://localhost:8001/api/v1/interfaces/1
2025-07-25 20:23:48,753 [INFO] root:158 - 请求完成: PUT http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.004s
2025-07-25 20:23:49,760 [INFO] root:149 - 请求开始: DELETE http://localhost:8001/api/v1/interfaces/1
2025-07-25 20:23:49,775 [INFO] app.services.interface_service:287 - 接口源删除成功: 测试接口
2025-07-25 20:23:49,777 [INFO] root:158 - 请求完成: DELETE http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.018s
2025-07-25 20:23:50,787 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/1/test
2025-07-25 20:23:50,787 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/1/test 状态码: 200 耗时: 0.000s
2025-07-25 20:23:50,789 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/test
2025-07-25 20:23:50,792 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/test 状态码: 200 耗时: 0.003s
2025-07-25 20:23:51,810 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/1/refresh
2025-07-25 20:23:51,812 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/1/refresh 状态码: 200 耗时: 0.001s
2025-07-25 20:23:51,813 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/refresh
2025-07-25 20:23:51,815 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/refresh 状态码: 200 耗时: 0.002s
2025-07-25 20:23:52,823 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/subscriptions/
2025-07-25 20:23:52,824 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/subscriptions/ 状态码: 200 耗时: 0.001s
2025-07-25 20:23:52,828 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions/
2025-07-25 20:23:53,118 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions/ 状态码: 200 耗时: 0.290s
2025-07-25 20:23:54,136 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/subscriptions/
2025-07-25 20:23:54,136 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/subscriptions/ 状态码: 200 耗时: 0.000s
2025-07-25 20:23:54,139 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/subscriptions/
2025-07-25 20:23:54,142 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/subscriptions/ 状态码: 200 耗时: 0.003s
2025-07-25 20:23:55,160 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/subscriptions/1
2025-07-25 20:23:55,161 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/subscriptions/1 状态码: 200 耗时: 0.001s
2025-07-25 20:23:55,163 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions/1
2025-07-25 20:23:55,435 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions/1 状态码: 200 耗时: 0.271s
2025-07-25 20:23:56,452 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions/1/config
2025-07-25 20:23:56,454 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions/1/config 状态码: 200 耗时: 0.002s
2025-07-25 20:23:57,465 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/users/?page=1&limit=10
2025-07-25 20:23:57,466 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/users/?page=1&limit=10 状态码: 200 耗时: 0.001s
2025-07-25 20:23:57,470 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/users/?page=1&limit=10
2025-07-25 20:23:57,749 [ERROR] app.api.v1.users:121 - 获取用户列表异常: 'User' object has no attribute 'nickname'
2025-07-25 20:23:57,751 [ERROR] app.core.database:75 - 数据库会话错误: 500: 获取用户列表失败
2025-07-25 20:23:57,752 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/users/?page=1&limit=10 状态码: 500 耗时: 0.283s
2025-07-25 20:23:58,763 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/users/
2025-07-25 20:23:58,764 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/users/ 状态码: 200 耗时: 0.001s
2025-07-25 20:23:58,766 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/users/
2025-07-25 20:23:59,051 [ERROR] app.api.v1.users:189 - 创建用户异常: 'UserService' object has no attribute 'get_user_by_username'
2025-07-25 20:23:59,053 [ERROR] app.core.database:75 - 数据库会话错误: 500: 创建用户失败
2025-07-25 20:23:59,053 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/users/ 状态码: 500 耗时: 0.287s
2025-07-25 20:24:00,058 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/system/stats
2025-07-25 20:24:00,059 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/system/stats 状态码: 200 耗时: 0.002s
2025-07-25 20:24:00,061 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats
2025-07-25 20:24:00,337 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats 状态码: 200 耗时: 0.276s
2025-07-25 20:24:01,345 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/system/settings
2025-07-25 20:24:01,349 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/system/settings 状态码: 200 耗时: 0.004s
2025-07-25 20:24:01,354 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/settings
2025-07-25 20:24:01,645 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/settings 状态码: 200 耗时: 0.291s
2025-07-25 20:24:02,651 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/decrypt/url
2025-07-25 20:24:02,651 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/decrypt/url 状态码: 200 耗时: 0.000s
2025-07-25 20:24:02,653 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/decrypt/url
2025-07-25 20:24:02,655 [INFO] app.api.v1.decrypt:55 - 开始解密URL: https://example.com/config.json
2025-07-25 20:24:03,423 [ERROR] app.core.tvbox_decryptor:51 - 解密URL失败: 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 20:24:03,423 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/decrypt/url 状态码: 200 耗时: 0.770s
2025-07-25 20:24:04,438 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/decrypt/content
2025-07-25 20:24:04,439 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/decrypt/content 状态码: 200 耗时: 0.001s
2025-07-25 20:24:04,442 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/decrypt/content
2025-07-25 20:24:04,445 [INFO] app.api.v1.decrypt:96 - 开始解密内容，方法: base64
2025-07-25 20:24:04,446 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/decrypt/content 状态码: 200 耗时: 0.005s
2025-07-25 20:24:05,453 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/decrypt/methods
2025-07-25 20:24:05,455 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/decrypt/methods 状态码: 200 耗时: 0.001s
2025-07-25 20:27:44,598 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 20:27:47,986 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:27:47,994 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:27:48,044 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 20:27:48,044 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 20:28:45,722 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:28:45,729 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:28:45,801 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 20:28:45,801 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 20:29:33,728 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/login
2025-07-25 20:29:34,015 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.287s
2025-07-25 20:29:35,022 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/register
2025-07-25 20:29:35,025 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/register 状态码: 200 耗时: 0.002s
2025-07-25 20:29:36,079 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me
2025-07-25 20:29:36,355 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me 状态码: 200 耗时: 0.276s
2025-07-25 20:29:37,370 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/refresh
2025-07-25 20:29:37,372 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/refresh 状态码: 200 耗时: 0.002s
2025-07-25 20:29:38,384 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?page=1&limit=10
2025-07-25 20:29:38,387 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?page=1&limit=10 状态码: 200 耗时: 0.004s
2025-07-25 20:29:39,400 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/
2025-07-25 20:29:40,919 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 1/3): 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 20:29:42,177 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 2/3): 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 20:29:43,447 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 3/3): 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 20:29:43,447 [ERROR] app.services.tvbox_decryptor:86 - 解密配置URL失败: 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 20:29:43,453 [ERROR] app.services.interface_service:155 - 解析接口失败: 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 20:29:43,455 [INFO] app.services.interface_service:64 - 接口源创建成功: 测试接口
2025-07-25 20:29:43,456 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/ 状态码: 200 耗时: 4.056s
2025-07-25 20:29:44,472 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1
2025-07-25 20:29:44,476 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.005s
2025-07-25 20:29:45,495 [INFO] root:149 - 请求开始: PUT http://localhost:8001/api/v1/interfaces/1
2025-07-25 20:29:45,498 [INFO] root:158 - 请求完成: PUT http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.002s
2025-07-25 20:29:46,507 [INFO] root:149 - 请求开始: DELETE http://localhost:8001/api/v1/interfaces/1
2025-07-25 20:29:46,517 [INFO] app.services.interface_service:287 - 接口源删除成功: 测试接口
2025-07-25 20:29:46,518 [INFO] root:158 - 请求完成: DELETE http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.011s
2025-07-25 20:29:47,535 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/test
2025-07-25 20:29:47,537 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/test 状态码: 200 耗时: 0.002s
2025-07-25 20:29:48,549 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/refresh
2025-07-25 20:29:48,551 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/refresh 状态码: 200 耗时: 0.001s
2025-07-25 20:29:49,564 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions/
2025-07-25 20:29:49,851 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions/ 状态码: 200 耗时: 0.287s
2025-07-25 20:29:50,860 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/subscriptions/
2025-07-25 20:29:50,863 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/subscriptions/ 状态码: 200 耗时: 0.003s
2025-07-25 20:29:51,874 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions/1
2025-07-25 20:29:52,146 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions/1 状态码: 200 耗时: 0.273s
2025-07-25 20:29:53,165 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions/1/config
2025-07-25 20:29:53,167 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions/1/config 状态码: 200 耗时: 0.002s
2025-07-25 20:29:54,185 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/users/?page=1&limit=10
2025-07-25 20:29:54,460 [ERROR] app.api.v1.users:121 - 获取用户列表异常: 'User' object has no attribute 'nickname'
2025-07-25 20:29:54,461 [ERROR] app.core.database:75 - 数据库会话错误: 500: 获取用户列表失败
2025-07-25 20:29:54,461 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/users/?page=1&limit=10 状态码: 500 耗时: 0.275s
2025-07-25 20:29:55,480 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/users/
2025-07-25 20:29:55,751 [ERROR] app.api.v1.users:189 - 创建用户异常: 'UserService' object has no attribute 'get_user_by_username'
2025-07-25 20:29:55,752 [ERROR] app.core.database:75 - 数据库会话错误: 500: 创建用户失败
2025-07-25 20:29:55,753 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/users/ 状态码: 500 耗时: 0.273s
2025-07-25 20:29:56,758 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats
2025-07-25 20:29:57,037 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats 状态码: 200 耗时: 0.278s
2025-07-25 20:29:58,053 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/settings
2025-07-25 20:29:58,332 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/settings 状态码: 200 耗时: 0.279s
2025-07-25 20:29:59,349 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/decrypt/url
2025-07-25 20:29:59,350 [INFO] app.api.v1.decrypt:55 - 开始解密URL: https://example.com/config.json
2025-07-25 20:30:00,388 [ERROR] app.core.tvbox_decryptor:51 - 解密URL失败: 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 20:30:00,388 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/decrypt/url 状态码: 200 耗时: 1.039s
2025-07-25 20:30:01,396 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/decrypt/content
2025-07-25 20:30:01,397 [INFO] app.api.v1.decrypt:96 - 开始解密内容，方法: base64
2025-07-25 20:30:01,397 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/decrypt/content 状态码: 200 耗时: 0.001s
2025-07-25 20:30:02,418 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/decrypt/methods
2025-07-25 20:30:02,418 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/decrypt/methods 状态码: 200 耗时: 0.000s
2025-07-25 20:30:07,473 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 20:30:11,726 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:30:11,731 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:30:11,774 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 20:30:11,775 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 20:32:43,653 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:32:43,661 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:32:43,715 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 20:32:43,716 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 20:33:15,756 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:33:15,761 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:33:15,812 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 20:33:15,812 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 20:33:34,840 [INFO] root:149 - 请求开始: GET http://localhost:8001/docs
2025-07-25 20:33:34,841 [INFO] root:158 - 请求完成: GET http://localhost:8001/docs 状态码: 200 耗时: 0.001s
2025-07-25 20:34:32,152 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/auth/login
2025-07-25 20:34:32,152 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.000s
2025-07-25 20:34:32,155 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/login
2025-07-25 20:34:32,477 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.322s
2025-07-25 20:34:33,487 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/auth/register
2025-07-25 20:34:33,488 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/auth/register 状态码: 200 耗时: 0.000s
2025-07-25 20:34:33,489 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/register
2025-07-25 20:34:33,502 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/register 状态码: 200 耗时: 0.013s
2025-07-25 20:34:34,533 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/auth/me
2025-07-25 20:34:34,533 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/auth/me 状态码: 200 耗时: 0.000s
2025-07-25 20:34:34,536 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me
2025-07-25 20:34:34,807 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me 状态码: 200 耗时: 0.271s
2025-07-25 20:34:35,828 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/auth/refresh
2025-07-25 20:34:35,829 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/auth/refresh 状态码: 200 耗时: 0.001s
2025-07-25 20:34:35,830 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/refresh
2025-07-25 20:34:35,834 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/refresh 状态码: 200 耗时: 0.004s
2025-07-25 20:34:36,844 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/?page=1&limit=10
2025-07-25 20:34:36,845 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/?page=1&limit=10 状态码: 200 耗时: 0.001s
2025-07-25 20:34:36,847 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?page=1&limit=10
2025-07-25 20:34:36,858 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?page=1&limit=10 状态码: 200 耗时: 0.010s
2025-07-25 20:34:37,874 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/
2025-07-25 20:34:37,875 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/ 状态码: 200 耗时: 0.001s
2025-07-25 20:34:37,876 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/
2025-07-25 20:34:39,013 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 1/3): 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 20:34:40,319 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 2/3): 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 20:34:41,629 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 3/3): 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 20:34:41,630 [ERROR] app.services.tvbox_decryptor:86 - 解密配置URL失败: 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 20:34:41,637 [ERROR] app.services.interface_service:155 - 解析接口失败: 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 20:34:41,641 [INFO] app.services.interface_service:64 - 接口源创建成功: 测试接口
2025-07-25 20:34:41,642 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/ 状态码: 200 耗时: 3.765s
2025-07-25 20:34:42,661 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/1
2025-07-25 20:34:42,661 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.000s
2025-07-25 20:34:42,663 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1
2025-07-25 20:34:42,666 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.002s
2025-07-25 20:34:43,677 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/1
2025-07-25 20:34:43,678 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.001s
2025-07-25 20:34:43,680 [INFO] root:149 - 请求开始: PUT http://localhost:8001/api/v1/interfaces/1
2025-07-25 20:34:43,685 [INFO] root:158 - 请求完成: PUT http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.004s
2025-07-25 20:34:44,693 [INFO] root:149 - 请求开始: DELETE http://localhost:8001/api/v1/interfaces/1
2025-07-25 20:34:44,704 [INFO] app.services.interface_service:287 - 接口源删除成功: 测试接口
2025-07-25 20:34:44,705 [INFO] root:158 - 请求完成: DELETE http://localhost:8001/api/v1/interfaces/1 状态码: 200 耗时: 0.013s
2025-07-25 20:34:45,720 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/1/test
2025-07-25 20:34:45,721 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/1/test 状态码: 200 耗时: 0.001s
2025-07-25 20:34:45,723 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/test
2025-07-25 20:34:45,726 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/test 状态码: 200 耗时: 0.002s
2025-07-25 20:34:46,731 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/1/refresh
2025-07-25 20:34:46,731 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/1/refresh 状态码: 200 耗时: 0.000s
2025-07-25 20:34:46,733 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/refresh
2025-07-25 20:34:46,734 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/refresh 状态码: 200 耗时: 0.001s
2025-07-25 20:34:47,745 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/subscriptions/
2025-07-25 20:34:47,746 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/subscriptions/ 状态码: 200 耗时: 0.001s
2025-07-25 20:34:47,748 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions/
2025-07-25 20:34:48,017 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions/ 状态码: 200 耗时: 0.270s
2025-07-25 20:34:49,037 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/subscriptions/
2025-07-25 20:34:49,038 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/subscriptions/ 状态码: 200 耗时: 0.001s
2025-07-25 20:34:49,039 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/subscriptions/
2025-07-25 20:34:49,042 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/subscriptions/ 状态码: 200 耗时: 0.003s
2025-07-25 20:34:50,051 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/subscriptions/1
2025-07-25 20:34:50,052 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/subscriptions/1 状态码: 200 耗时: 0.001s
2025-07-25 20:34:50,053 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions/1
2025-07-25 20:34:50,323 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions/1 状态码: 200 耗时: 0.270s
2025-07-25 20:34:51,330 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions/1/config
2025-07-25 20:34:51,332 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions/1/config 状态码: 200 耗时: 0.002s
2025-07-25 20:34:52,348 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/users/?page=1&limit=10
2025-07-25 20:34:52,349 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/users/?page=1&limit=10 状态码: 200 耗时: 0.001s
2025-07-25 20:34:52,350 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/users/?page=1&limit=10
2025-07-25 20:34:52,352 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/users/?page=1&limit=10 状态码: 200 耗时: 0.002s
2025-07-25 20:34:53,359 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/users/
2025-07-25 20:34:53,360 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/users/ 状态码: 200 耗时: 0.001s
2025-07-25 20:34:53,362 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/users/
2025-07-25 20:34:53,369 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/users/ 状态码: 200 耗时: 0.006s
2025-07-25 20:34:54,385 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/system/stats
2025-07-25 20:34:54,387 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/system/stats 状态码: 200 耗时: 0.000s
2025-07-25 20:34:54,388 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats
2025-07-25 20:34:54,663 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats 状态码: 200 耗时: 0.276s
2025-07-25 20:34:55,679 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/system/settings
2025-07-25 20:34:55,680 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/system/settings 状态码: 200 耗时: 0.001s
2025-07-25 20:34:55,682 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/settings
2025-07-25 20:34:55,953 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/settings 状态码: 200 耗时: 0.271s
2025-07-25 20:34:56,958 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/decrypt/url
2025-07-25 20:34:56,959 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/decrypt/url 状态码: 200 耗时: 0.001s
2025-07-25 20:34:56,961 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/decrypt/url
2025-07-25 20:34:56,963 [INFO] app.api.v1.decrypt:55 - 开始解密URL: https://example.com/config.json
2025-07-25 20:34:58,455 [ERROR] app.core.tvbox_decryptor:51 - 解密URL失败: 404 Client Error: Not Found for url: https://example.com/config.json
2025-07-25 20:34:58,456 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/decrypt/url 状态码: 200 耗时: 1.495s
2025-07-25 20:34:59,467 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/decrypt/content
2025-07-25 20:34:59,468 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/decrypt/content 状态码: 200 耗时: 0.001s
2025-07-25 20:34:59,471 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/decrypt/content
2025-07-25 20:34:59,473 [INFO] app.api.v1.decrypt:96 - 开始解密内容，方法: base64
2025-07-25 20:34:59,473 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/decrypt/content 状态码: 200 耗时: 0.004s
2025-07-25 20:35:00,482 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/decrypt/methods
2025-07-25 20:35:00,482 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/decrypt/methods 状态码: 200 耗时: 0.001s
2025-07-25 20:39:13,479 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 20:39:18,360 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:39:18,368 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:39:18,442 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 20:39:18,443 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 20:39:22,193 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:39:22,201 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:39:22,266 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 20:39:22,267 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 20:40:08,606 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 20:40:12,748 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:40:12,754 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:40:12,800 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 20:40:12,801 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 20:42:36,535 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 20:42:40,606 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:42:40,616 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:42:40,668 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 20:42:40,668 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 20:42:49,155 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:42:49,163 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:42:49,220 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 20:42:49,221 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 20:43:20,616 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:43:20,622 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:43:20,679 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 20:43:20,680 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 20:44:01,571 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:44:01,577 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:44:01,628 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 20:44:01,629 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 20:44:20,863 [INFO] root:149 - 请求开始: GET http://localhost:8001/docs
2025-07-25 20:44:20,865 [INFO] root:158 - 请求完成: GET http://localhost:8001/docs 状态码: 200 耗时: 0.002s
2025-07-25 20:44:21,071 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 20:44:24,700 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 20:44:24,705 [INFO] root:48 - 数据库表创建完成
2025-07-25 20:44:24,754 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 20:44:24,755 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 20:52:16,563 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/?page=1&limit=10
2025-07-25 20:52:16,564 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/?page=1&limit=10 状态码: 200 耗时: 0.001s
2025-07-25 20:52:16,566 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?page=1&limit=10
2025-07-25 20:52:16,580 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?page=1&limit=10 状态码: 200 耗时: 0.014s
2025-07-25 21:20:02,691 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/auth/login
2025-07-25 21:20:02,693 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.001s
2025-07-25 21:20:02,696 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/login
2025-07-25 21:20:03,036 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.340s
2025-07-25 21:20:04,211 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/system/stats?_t=1753449604104
2025-07-25 21:20:04,212 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/system/stats?_t=1753449604104 状态码: 200 耗时: 0.001s
2025-07-25 21:20:04,215 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats?_t=1753449604104
2025-07-25 21:20:04,223 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats?_t=1753449604104 状态码: 200 耗时: 0.008s
2025-07-25 21:20:30,321 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753449630315
2025-07-25 21:20:30,321 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753449630315 状态码: 200 耗时: 0.001s
2025-07-25 21:20:30,325 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/categories/list?_t=1753449630315
2025-07-25 21:20:30,325 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/categories/list?_t=1753449630315 状态码: 200 耗时: 0.000s
2025-07-25 21:20:30,328 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753449630315
2025-07-25 21:20:30,329 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753449630315 状态码: 307 耗时: 0.001s
2025-07-25 21:20:30,331 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753449630315
2025-07-25 21:20:30,337 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753449630315 状态码: 200 耗时: 0.005s
2025-07-25 21:20:30,356 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753449630315
2025-07-25 21:20:30,357 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753449630315 状态码: 200 耗时: 0.001s
2025-07-25 21:20:30,360 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753449630315
2025-07-25 21:20:30,365 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753449630315 状态码: 200 耗时: 0.005s
2025-07-25 21:20:30,390 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753449630384
2025-07-25 21:20:30,390 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753449630384 状态码: 200 耗时: 0.000s
2025-07-25 21:20:30,394 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753449630384
2025-07-25 21:20:30,395 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753449630384 状态码: 307 耗时: 0.001s
2025-07-25 21:20:30,397 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753449630384
2025-07-25 21:20:30,397 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753449630384 状态码: 200 耗时: 0.000s
2025-07-25 21:20:30,400 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753449630384
2025-07-25 21:20:30,402 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753449630384 状态码: 200 耗时: 0.003s
2025-07-25 21:20:32,763 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/categories/list?_t=1753449632749
2025-07-25 21:20:32,764 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/categories/list?_t=1753449632749 状态码: 200 耗时: 0.001s
2025-07-25 21:20:32,769 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753449632749
2025-07-25 21:20:32,776 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753449632749 状态码: 200 耗时: 0.007s
2025-07-25 21:20:37,234 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753449637227
2025-07-25 21:20:37,235 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/categories/list?_t=1753449637227
2025-07-25 21:20:37,236 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753449637227 状态码: 200 耗时: 0.001s
2025-07-25 21:20:37,236 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/categories/list?_t=1753449637227 状态码: 200 耗时: 0.001s
2025-07-25 21:20:37,239 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753449637227
2025-07-25 21:20:37,241 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753449637227 状态码: 307 耗时: 0.002s
2025-07-25 21:20:37,242 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753449637227
2025-07-25 21:20:37,247 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753449637227 状态码: 200 耗时: 0.006s
2025-07-25 21:20:37,269 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753449637227
2025-07-25 21:20:37,269 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753449637227 状态码: 200 耗时: 0.000s
2025-07-25 21:20:37,275 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753449637227
2025-07-25 21:20:37,294 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753449637227 状态码: 200 耗时: 0.018s
2025-07-25 21:20:37,326 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753449637320
2025-07-25 21:20:37,328 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753449637320 状态码: 200 耗时: 0.002s
2025-07-25 21:20:37,335 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753449637320
2025-07-25 21:20:37,336 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753449637320 状态码: 307 耗时: 0.001s
2025-07-25 21:20:37,362 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753449637320
2025-07-25 21:20:37,363 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753449637320 状态码: 200 耗时: 0.001s
2025-07-25 21:20:37,365 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753449637320
2025-07-25 21:20:37,370 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753449637320 状态码: 200 耗时: 0.004s
2025-07-25 21:20:49,279 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 21:20:53,830 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 21:20:53,836 [INFO] root:48 - 数据库表创建完成
2025-07-25 21:20:53,888 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 21:20:53,888 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 21:33:42,156 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 21:33:42,162 [INFO] root:48 - 数据库表创建完成
2025-07-25 21:33:42,211 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 21:33:42,212 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 21:34:13,497 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 21:34:13,502 [INFO] root:48 - 数据库表创建完成
2025-07-25 21:34:13,556 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 21:34:13,556 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 21:34:36,207 [INFO] root:149 - 请求开始: GET http://localhost:8001/docs
2025-07-25 21:34:36,208 [INFO] root:158 - 请求完成: GET http://localhost:8001/docs 状态码: 200 耗时: 0.001s
2025-07-25 21:34:38,218 [INFO] root:149 - 请求开始: GET http://localhost:8001/openapi.json
2025-07-25 21:34:38,293 [INFO] root:158 - 请求完成: GET http://localhost:8001/openapi.json 状态码: 200 耗时: 0.075s
2025-07-25 21:35:53,238 [INFO] root:149 - 请求开始: POST http://localhost:8001/v1/auth/login
2025-07-25 21:35:53,239 [INFO] root:158 - 请求完成: POST http://localhost:8001/v1/auth/login 状态码: 404 耗时: 0.001s
2025-07-25 21:41:28,970 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/login
2025-07-25 21:41:29,292 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.322s
2025-07-25 21:41:30,072 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats?_t=1753450889972
2025-07-25 21:41:30,077 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats?_t=1753450889972 状态码: 200 耗时: 0.005s
2025-07-25 21:42:09,981 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753450929975
2025-07-25 21:42:09,985 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753450929975 状态码: 200 耗时: 0.004s
2025-07-25 21:42:12,341 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753450932333
2025-07-25 21:42:12,348 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753450932333 状态码: 200 耗时: 0.007s
2025-07-25 21:42:20,809 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats
2025-07-25 21:42:20,809 [ERROR] app.core.database:75 - 数据库会话错误: 401: 缺少认证信息
2025-07-25 21:42:20,811 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats 状态码: 401 耗时: 0.002s
2025-07-25 21:42:20,828 [INFO] root:149 - 请求开始: GET http://localhost:8001/favicon.ico
2025-07-25 21:42:20,828 [INFO] root:158 - 请求完成: GET http://localhost:8001/favicon.ico 状态码: 404 耗时: 0.001s
2025-07-25 21:43:14,539 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753450994534
2025-07-25 21:43:14,546 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753450994534 状态码: 200 耗时: 0.007s
2025-07-25 21:43:15,068 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats?_t=1753450994987
2025-07-25 21:43:15,073 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats?_t=1753450994987 状态码: 200 耗时: 0.006s
2025-07-25 21:43:24,043 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753451004039
2025-07-25 21:43:24,050 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753451004039 状态码: 200 耗时: 0.007s
2025-07-25 21:43:24,400 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats?_t=1753451004368
2025-07-25 21:43:24,404 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats?_t=1753451004368 状态码: 200 耗时: 0.004s
2025-07-25 21:43:54,564 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753451034557
2025-07-25 21:43:54,569 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753451034557 状态码: 200 耗时: 0.006s
2025-07-25 21:43:55,059 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats?_t=1753451034958
2025-07-25 21:43:55,064 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats?_t=1753451034958 状态码: 200 耗时: 0.005s
2025-07-25 21:45:21,831 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/auth/login
2025-07-25 21:45:22,128 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/auth/login 状态码: 200 耗时: 0.297s
2025-07-25 21:45:22,861 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats?_t=1753451122820
2025-07-25 21:45:22,866 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats?_t=1753451122820 状态码: 200 耗时: 0.005s
2025-07-25 21:47:07,701 [INFO] root:149 - 请求开始: GET http://localhost:8001/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753451227687
2025-07-25 21:47:07,702 [INFO] root:149 - 请求开始: GET http://localhost:8001/v1/interfaces/categories/list?_t=1753451227687
2025-07-25 21:47:07,703 [INFO] root:158 - 请求完成: GET http://localhost:8001/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753451227687 状态码: 404 耗时: 0.002s
2025-07-25 21:47:07,705 [INFO] root:158 - 请求完成: GET http://localhost:8001/v1/interfaces/categories/list?_t=1753451227687 状态码: 404 耗时: 0.003s
2025-07-25 21:47:19,527 [INFO] root:149 - 请求开始: GET http://localhost:8001/v1/interfaces/categories/list?_t=1753451239515
2025-07-25 21:47:19,528 [INFO] root:158 - 请求完成: GET http://localhost:8001/v1/interfaces/categories/list?_t=1753451239515 状态码: 404 耗时: 0.001s
2025-07-25 21:47:21,886 [INFO] root:149 - 请求开始: GET http://localhost:8001/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753451241880
2025-07-25 21:47:21,887 [INFO] root:149 - 请求开始: GET http://localhost:8001/v1/interfaces/categories/list?_t=1753451241880
2025-07-25 21:47:21,887 [INFO] root:158 - 请求完成: GET http://localhost:8001/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753451241880 状态码: 404 耗时: 0.001s
2025-07-25 21:47:21,888 [INFO] root:158 - 请求完成: GET http://localhost:8001/v1/interfaces/categories/list?_t=1753451241880 状态码: 404 耗时: 0.001s
2025-07-25 21:47:27,137 [INFO] root:149 - 请求开始: GET http://localhost:8001/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753451247131
2025-07-25 21:47:27,138 [INFO] root:149 - 请求开始: GET http://localhost:8001/v1/interfaces/categories/list?_t=1753451247131
2025-07-25 21:47:27,138 [INFO] root:158 - 请求完成: GET http://localhost:8001/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753451247131 状态码: 404 耗时: 0.002s
2025-07-25 21:47:27,139 [INFO] root:158 - 请求完成: GET http://localhost:8001/v1/interfaces/categories/list?_t=1753451247131 状态码: 404 耗时: 0.002s
2025-07-25 21:47:27,896 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats?_t=1753451247856
2025-07-25 21:47:27,899 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats?_t=1753451247856 状态码: 200 耗时: 0.003s
2025-07-25 21:47:39,348 [INFO] root:149 - 请求开始: GET http://localhost:8001/v1/interfaces/categories/list?_t=1753451259030
2025-07-25 21:47:39,349 [INFO] root:158 - 请求完成: GET http://localhost:8001/v1/interfaces/categories/list?_t=1753451259030 状态码: 404 耗时: 0.001s
2025-07-25 21:47:41,991 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats?_t=1753451261969
2025-07-25 21:47:41,995 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats?_t=1753451261969 状态码: 200 耗时: 0.004s
2025-07-25 21:47:43,525 [INFO] root:149 - 请求开始: GET http://localhost:8001/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753451263207
2025-07-25 21:47:43,526 [INFO] root:149 - 请求开始: GET http://localhost:8001/v1/interfaces/categories/list?_t=1753451263207
2025-07-25 21:47:43,526 [INFO] root:158 - 请求完成: GET http://localhost:8001/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753451263207 状态码: 404 耗时: 0.001s
2025-07-25 21:47:43,527 [INFO] root:158 - 请求完成: GET http://localhost:8001/v1/interfaces/categories/list?_t=1753451263207 状态码: 404 耗时: 0.001s
2025-07-25 21:47:50,749 [INFO] root:149 - 请求开始: GET http://localhost:8001/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753451270743
2025-07-25 21:47:50,749 [INFO] root:158 - 请求完成: GET http://localhost:8001/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753451270743 状态码: 404 耗时: 0.000s
2025-07-25 21:47:51,059 [INFO] root:149 - 请求开始: GET http://localhost:8001/v1/interfaces/categories/list?_t=1753451270743
2025-07-25 21:47:51,060 [INFO] root:158 - 请求完成: GET http://localhost:8001/v1/interfaces/categories/list?_t=1753451270743 状态码: 404 耗时: 0.001s
2025-07-25 21:51:43,952 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753451503945
2025-07-25 21:51:43,953 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories?_t=1753451503945
2025-07-25 21:51:43,955 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753451503945 状态码: 307 耗时: 0.002s
2025-07-25 21:51:43,958 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories?_t=1753451503945 状态码: 422 耗时: 0.005s
2025-07-25 21:51:44,299 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753451503945
2025-07-25 21:51:44,312 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753451503945 状态码: 200 耗时: 0.013s
2025-07-25 21:51:44,344 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753451504338
2025-07-25 21:51:44,345 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753451504338 状态码: 307 耗时: 0.001s
2025-07-25 21:51:44,348 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753451504338
2025-07-25 21:51:44,352 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753451504338 状态码: 200 耗时: 0.004s
2025-07-25 21:52:22,245 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753451542239
2025-07-25 21:52:22,246 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753451542239 状态码: 307 耗时: 0.001s
2025-07-25 21:52:22,248 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753451542239
2025-07-25 21:52:22,252 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753451542239 状态码: 200 耗时: 0.004s
2025-07-25 21:52:22,275 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753451542239
2025-07-25 21:52:22,281 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753451542239 状态码: 200 耗时: 0.006s
2025-07-25 21:52:22,422 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753451542306
2025-07-25 21:52:22,423 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753451542306 状态码: 307 耗时: 0.001s
2025-07-25 21:52:22,426 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753451542306
2025-07-25 21:52:22,431 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753451542306 状态码: 200 耗时: 0.006s
2025-07-25 21:52:37,367 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753451557362
2025-07-25 21:52:37,373 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753451557362 状态码: 200 耗时: 0.006s
2025-07-25 21:52:37,704 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats?_t=1753451557627
2025-07-25 21:52:37,710 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats?_t=1753451557627 状态码: 200 耗时: 0.006s
2025-07-25 21:53:38,408 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats?_t=1753451618369
2025-07-25 21:53:38,412 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats?_t=1753451618369 状态码: 200 耗时: 0.004s
2025-07-25 21:54:13,613 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats?_t=1753451653581
2025-07-25 21:54:13,619 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats?_t=1753451653581 状态码: 200 耗时: 0.006s
2025-07-25 21:56:19,353 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats?_t=1753451779274
2025-07-25 21:56:19,360 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats?_t=1753451779274 状态码: 200 耗时: 0.008s
2025-07-25 21:56:40,508 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats?_t=1753451800480
2025-07-25 21:56:40,513 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats?_t=1753451800480 状态码: 200 耗时: 0.005s
2025-07-25 21:57:06,697 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats?_t=1753451826670
2025-07-25 21:57:06,701 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats?_t=1753451826670 状态码: 200 耗时: 0.005s
2025-07-25 21:57:47,199 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753451867191
2025-07-25 21:57:47,201 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753451867191
2025-07-25 21:57:47,201 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753451867191 状态码: 307 耗时: 0.001s
2025-07-25 21:57:47,209 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753451867191 状态码: 200 耗时: 0.008s
2025-07-25 21:57:47,529 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753451867191
2025-07-25 21:57:47,533 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753451867191 状态码: 200 耗时: 0.003s
2025-07-25 21:57:47,557 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753451867553
2025-07-25 21:57:47,558 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753451867553 状态码: 307 耗时: 0.002s
2025-07-25 21:57:47,560 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753451867553
2025-07-25 21:57:47,563 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753451867553 状态码: 200 耗时: 0.003s
2025-07-25 21:59:39,265 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats?_t=1753451978936
2025-07-25 21:59:39,268 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats?_t=1753451978936 状态码: 200 耗时: 0.004s
2025-07-25 21:59:40,197 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753451980189
2025-07-25 21:59:40,198 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753451980189 状态码: 307 耗时: 0.001s
2025-07-25 21:59:40,513 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753451980189
2025-07-25 21:59:40,517 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753451980189 状态码: 200 耗时: 0.004s
2025-07-25 21:59:40,556 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753451980189
2025-07-25 21:59:40,561 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753451980189 状态码: 200 耗时: 0.004s
2025-07-25 21:59:40,884 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753451980577
2025-07-25 21:59:40,885 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753451980577 状态码: 307 耗时: 0.001s
2025-07-25 21:59:40,888 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753451980577
2025-07-25 21:59:40,893 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753451980577 状态码: 200 耗时: 0.005s
2025-07-25 21:59:45,000 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats?_t=1753451984982
2025-07-25 21:59:45,004 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats?_t=1753451984982 状态码: 200 耗时: 0.004s
2025-07-25 22:03:22,396 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753452202383
2025-07-25 22:03:22,407 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753452202383 状态码: 200 耗时: 0.011s
2025-07-25 22:03:22,619 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 22:03:28,072 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 22:03:28,078 [INFO] root:48 - 数据库表创建完成
2025-07-25 22:03:28,136 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 22:03:28,136 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 22:03:28,145 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats?_t=1753452203727
2025-07-25 22:03:28,159 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats?_t=1753452203727 状态码: 200 耗时: 0.015s
2025-07-25 22:09:05,094 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 22:09:10,755 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 22:09:10,766 [INFO] root:48 - 数据库表创建完成
2025-07-25 22:09:10,840 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 22:09:10,841 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 22:16:53,805 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats?_t=1753453013764
2025-07-25 22:16:53,812 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats?_t=1753453013764 状态码: 200 耗时: 0.007s
2025-07-25 22:21:25,926 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions?page=1&limit=20&name=&status=&_t=1753453285916
2025-07-25 22:21:25,927 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions?page=1&limit=20&name=&status=&_t=1753453285916 状态码: 307 耗时: 0.001s
2025-07-25 22:21:26,243 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions/?page=1&limit=20&name=&status=&_t=1753453285916
2025-07-25 22:21:26,244 [ERROR] app.api.v1.subscriptions:74 - 获取订阅列表异常: 401: 缺少认证信息
2025-07-25 22:21:26,246 [ERROR] app.core.database:75 - 数据库会话错误: 500: 获取订阅列表失败
2025-07-25 22:21:26,247 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions/?page=1&limit=20&name=&status=&_t=1753453285916 状态码: 500 耗时: 0.004s
2025-07-25 22:23:17,561 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753453397552
2025-07-25 22:23:17,575 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753453397552 状态码: 200 耗时: 0.014s
2025-07-25 22:23:18,080 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/configs?page=1&limit=20&name=&type=&status=&_t=1753453398064
2025-07-25 22:23:18,083 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/configs?page=1&limit=20&name=&type=&status=&_t=1753453398064 状态码: 404 耗时: 0.002s
2025-07-25 22:26:25,099 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753453585087
2025-07-25 22:26:25,102 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753453585087
2025-07-25 22:26:25,102 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753453585087 状态码: 307 耗时: 0.002s
2025-07-25 22:26:25,111 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753453585087 状态码: 200 耗时: 0.010s
2025-07-25 22:26:25,447 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753453585087
2025-07-25 22:26:25,460 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753453585087 状态码: 200 耗时: 0.013s
2025-07-25 22:26:25,493 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753453585488
2025-07-25 22:26:25,494 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753453585488 状态码: 307 耗时: 0.001s
2025-07-25 22:26:25,504 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753453585488
2025-07-25 22:26:25,510 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753453585488 状态码: 200 耗时: 0.007s
2025-07-25 22:26:29,650 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats?_t=1753453589563
2025-07-25 22:26:29,656 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats?_t=1753453589563 状态码: 200 耗时: 0.006s
2025-07-25 22:26:32,299 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753453592292
2025-07-25 22:26:32,300 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753453592292
2025-07-25 22:26:32,301 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753453592292 状态码: 307 耗时: 0.002s
2025-07-25 22:26:32,307 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753453592292 状态码: 200 耗时: 0.007s
2025-07-25 22:26:32,328 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753453592292
2025-07-25 22:26:32,333 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753453592292 状态码: 200 耗时: 0.005s
2025-07-25 22:26:32,659 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753453592348
2025-07-25 22:26:32,660 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753453592348 状态码: 307 耗时: 0.001s
2025-07-25 22:26:32,662 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753453592348
2025-07-25 22:26:32,665 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753453592348 状态码: 200 耗时: 0.003s
2025-07-25 22:26:35,697 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753453595691
2025-07-25 22:26:35,704 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753453595691 状态码: 200 耗时: 0.007s
2025-07-25 22:26:37,891 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753453597568
2025-07-25 22:26:37,892 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753453597568
2025-07-25 22:26:37,894 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753453597568 状态码: 307 耗时: 0.003s
2025-07-25 22:26:37,898 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753453597568 状态码: 200 耗时: 0.006s
2025-07-25 22:26:38,200 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753453597568
2025-07-25 22:26:38,205 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753453597568 状态码: 200 耗时: 0.005s
2025-07-25 22:26:38,226 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753453598221
2025-07-25 22:26:38,227 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753453598221 状态码: 307 耗时: 0.001s
2025-07-25 22:26:38,230 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753453598221
2025-07-25 22:26:38,234 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753453598221 状态码: 200 耗时: 0.004s
2025-07-25 22:26:43,805 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/configs?page=1&limit=20&name=&type=&status=&_t=1753453603489
2025-07-25 22:26:43,806 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/configs?page=1&limit=20&name=&type=&status=&_t=1753453603489 状态码: 404 耗时: 0.001s
2025-07-25 22:26:44,099 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753453604088
2025-07-25 22:26:44,101 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753453604088 状态码: 307 耗时: 0.002s
2025-07-25 22:26:44,129 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753453604088
2025-07-25 22:26:44,136 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753453604088 状态码: 200 耗时: 0.007s
2025-07-25 22:26:44,413 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753453604088
2025-07-25 22:26:44,416 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753453604088 状态码: 200 耗时: 0.003s
2025-07-25 22:26:44,474 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753453604161
2025-07-25 22:26:44,475 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753453604161 状态码: 307 耗时: 0.000s
2025-07-25 22:26:44,477 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753453604161
2025-07-25 22:26:44,481 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753453604161 状态码: 200 耗时: 0.004s
2025-07-25 22:27:14,414 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753453634409
2025-07-25 22:27:14,423 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753453634409 状态码: 200 耗时: 0.009s
2025-07-25 22:27:14,779 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753453634771
2025-07-25 22:27:14,780 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753453634771 状态码: 307 耗时: 0.001s
2025-07-25 22:27:14,782 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753453634771
2025-07-25 22:27:14,789 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753453634771 状态码: 200 耗时: 0.007s
2025-07-25 22:27:15,098 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753453634771
2025-07-25 22:27:15,106 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753453634771 状态码: 200 耗时: 0.008s
2025-07-25 22:27:15,142 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753453635135
2025-07-25 22:27:15,144 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753453635135 状态码: 307 耗时: 0.002s
2025-07-25 22:27:15,147 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753453635135
2025-07-25 22:27:15,153 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753453635135 状态码: 200 耗时: 0.005s
2025-07-25 22:28:13,471 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753453693463
2025-07-25 22:28:13,472 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753453693463 状态码: 307 耗时: 0.001s
2025-07-25 22:28:13,476 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753453693463
2025-07-25 22:28:13,483 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753453693463 状态码: 200 耗时: 0.006s
2025-07-25 22:28:13,526 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753453693463
2025-07-25 22:28:13,531 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753453693463 状态码: 200 耗时: 0.005s
2025-07-25 22:28:32,575 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753453712513
2025-07-25 22:28:32,577 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753453712513
2025-07-25 22:28:32,579 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753453712513 状态码: 307 耗时: 0.004s
2025-07-25 22:28:32,589 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753453712513 状态码: 200 耗时: 0.011s
2025-07-25 22:28:33,188 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753453712513
2025-07-25 22:28:33,192 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753453712513 状态码: 200 耗时: 0.005s
2025-07-25 22:30:47,116 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753453847107
2025-07-25 22:30:47,117 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753453847107 状态码: 307 耗时: 0.001s
2025-07-25 22:30:47,159 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753453847107
2025-07-25 22:30:47,164 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753453847107 状态码: 200 耗时: 0.005s
2025-07-25 22:30:47,471 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753453847107
2025-07-25 22:30:47,477 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753453847107 状态码: 200 耗时: 0.006s
2025-07-25 22:31:30,262 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753453890253
2025-07-25 22:31:30,263 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753453890253
2025-07-25 22:31:30,263 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753453890253 状态码: 307 耗时: 0.001s
2025-07-25 22:31:30,270 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753453890253 状态码: 200 耗时: 0.007s
2025-07-25 22:31:30,307 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753453890253
2025-07-25 22:31:30,314 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753453890253 状态码: 200 耗时: 0.007s
2025-07-25 22:32:37,469 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats?_t=1753453957390
2025-07-25 22:32:37,474 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats?_t=1753453957390 状态码: 200 耗时: 0.005s
2025-07-25 22:32:38,917 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753453958908
2025-07-25 22:32:38,917 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753453958908 状态码: 307 耗时: 0.000s
2025-07-25 22:32:38,919 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753453958908
2025-07-25 22:32:38,923 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753453958908 状态码: 200 耗时: 0.004s
2025-07-25 22:32:39,250 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753453958908
2025-07-25 22:32:39,255 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753453958908 状态码: 200 耗时: 0.004s
2025-07-25 22:32:42,294 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753453962287
2025-07-25 22:32:42,299 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753453962287 状态码: 200 耗时: 0.004s
2025-07-25 22:34:24,282 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/decrypt
2025-07-25 22:34:24,286 [ERROR] app.api.v1.interfaces:525 - 解密接口URL异常: name 'interface_service' is not defined
2025-07-25 22:34:24,287 [ERROR] app.core.database:75 - 数据库会话错误: 2 validation errors for DecryptResponse
content
  Field required [type=missing, input_value={'success': False, 'error...ervice' is not defined"}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
method
  Field required [type=missing, input_value={'success': False, 'error...ervice' is not defined"}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-07-25 22:34:24,287 [ERROR] root:111 - 未处理的异常: 2 validation errors for DecryptResponse
content
  Field required [type=missing, input_value={'success': False, 'error...ervice' is not defined"}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
method
  Field required [type=missing, input_value={'success': False, 'error...ervice' is not defined"}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
  + Exception Group Traceback (most recent call last):
  |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\_utils.py", line 76, in collapse_excgroups
  |     yield
  |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 186, in __call__
  |     async with anyio.create_task_group() as task_group:
  |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_backends\_asyncio.py", line 767, in __aexit__
  |     raise BaseExceptionGroup(
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    |     await self.app(scope, receive, _send)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 185, in __call__
    |     with collapse_excgroups():
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 155, in __exit__
    |     self.gen.throw(typ, value, traceback)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    |     raise exc
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 187, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "G:\项目区\tvbox1\tvbox-manager-pro\backend\app\main.py", line 152, in log_requests
    |     response = await call_next(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 163, in call_next
    |     raise app_exc
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 149, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\trustedhost.py", line 36, in __call__
    |     await self.app(scope, receive, send)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    |     await self.simple_response(scope, receive, send, request_headers=headers)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    |     await self.app(scope, receive, send)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    |     await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    |     raise exc
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    |     await app(scope, receive, sender)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    |     await route.handle(scope, receive, send)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    |     await self.app(scope, receive, send)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    |     await wrap_app_handling_exceptions(app, request)(scope, receive, send)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    |     raise exc
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    |     await app(scope, receive, sender)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    |     response = await f(request)
    |                ^^^^^^^^^^^^^^^^
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    |     raw_response = await run_endpoint_function(
    |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    |     return await dependant.call(**values)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "G:\项目区\tvbox1\tvbox-manager-pro\backend\app\api\v1\interfaces.py", line 526, in decrypt_interface_url
    |     return DecryptResponse(
    |            ^^^^^^^^^^^^^^^^
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pydantic\main.py", line 214, in __init__
    |     validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    | pydantic_core._pydantic_core.ValidationError: 2 validation errors for DecryptResponse
    | content
    |   Field required [type=missing, input_value={'success': False, 'error...ervice' is not defined"}, input_type=dict]
    |     For further information visit https://errors.pydantic.dev/2.10/v/missing
    | method
    |   Field required [type=missing, input_value={'success': False, 'error...ervice' is not defined"}, input_type=dict]
    |     For further information visit https://errors.pydantic.dev/2.10/v/missing
    +------------------------------------

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 185, in __call__
    with collapse_excgroups():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 155, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 187, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "G:\项目区\tvbox1\tvbox-manager-pro\backend\app\main.py", line 152, in log_requests
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 163, in call_next
    raise app_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 149, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\trustedhost.py", line 36, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "G:\项目区\tvbox1\tvbox-manager-pro\backend\app\api\v1\interfaces.py", line 526, in decrypt_interface_url
    return DecryptResponse(
           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pydantic\main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pydantic_core._pydantic_core.ValidationError: 2 validation errors for DecryptResponse
content
  Field required [type=missing, input_value={'success': False, 'error...ervice' is not defined"}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
method
  Field required [type=missing, input_value={'success': False, 'error...ervice' is not defined"}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-07-25 22:34:46,976 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces
2025-07-25 22:34:46,977 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces 状态码: 307 耗时: 0.002s
2025-07-25 22:34:47,290 [INFO] root:149 - 请求开始: OPTIONS http://localhost:8001/api/v1/interfaces/
2025-07-25 22:34:47,290 [INFO] root:158 - 请求完成: OPTIONS http://localhost:8001/api/v1/interfaces/ 状态码: 200 耗时: 0.000s
2025-07-25 22:34:47,292 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/
2025-07-25 22:34:47,806 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 1/3): HTTPConnectionPool(host='www.xn--4bra.xn--vuq861b.com', port=80): Max retries exceeded with url: /tv (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFB4090F50>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-25 22:34:48,849 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 2/3): HTTPConnectionPool(host='www.xn--4bra.xn--vuq861b.com', port=80): Max retries exceeded with url: /tv (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFB4080B90>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-25 22:34:49,893 [WARNING] app.services.tvbox_decryptor:119 - 获取URL内容失败 (尝试 3/3): HTTPConnectionPool(host='www.xn--4bra.xn--vuq861b.com', port=80): Max retries exceeded with url: /tv (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFB407AAD0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-25 22:34:49,893 [ERROR] app.services.tvbox_decryptor:86 - 解密配置URL失败: HTTPConnectionPool(host='www.xn--4bra.xn--vuq861b.com', port=80): Max retries exceeded with url: /tv (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFB407AAD0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-25 22:34:49,900 [ERROR] app.services.interface_service:155 - 解析接口失败: HTTPConnectionPool(host='www.xn--4bra.xn--vuq861b.com', port=80): Max retries exceeded with url: /tv (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DFB407AAD0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-25 22:34:49,903 [INFO] app.services.interface_service:64 - 接口源创建成功: 123
2025-07-25 22:34:49,904 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/ 状态码: 200 耗时: 2.612s
2025-07-25 22:34:50,804 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753454090481
2025-07-25 22:34:50,806 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753454090481
2025-07-25 22:34:50,807 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753454090481 状态码: 307 耗时: 0.003s
2025-07-25 22:34:50,810 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753454090481
2025-07-25 22:34:50,816 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753454090481 状态码: 200 耗时: 0.010s
2025-07-25 22:34:50,819 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753454090481 状态码: 200 耗时: 0.009s
2025-07-25 22:34:54,827 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753454094824
2025-07-25 22:34:54,832 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753454094824 状态码: 200 耗时: 0.004s
2025-07-25 22:35:02,501 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753454102181
2025-07-25 22:35:02,504 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753454102181
2025-07-25 22:35:02,504 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753454102181 状态码: 307 耗时: 0.002s
2025-07-25 22:35:02,506 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753454102181
2025-07-25 22:35:02,511 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753454102181 状态码: 200 耗时: 0.005s
2025-07-25 22:35:02,512 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753454102181 状态码: 200 耗时: 0.009s
2025-07-25 22:35:03,851 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-25 22:35:03,853 [ERROR] app.api.v1.interfaces:474 - 解析接口源异常: name 'interface_service' is not defined
2025-07-25 22:35:03,853 [ERROR] app.core.database:75 - 数据库会话错误: 500: 解析接口源失败
2025-07-25 22:35:03,855 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 500 耗时: 0.004s
2025-07-25 22:37:16,983 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753454236975
2025-07-25 22:37:16,993 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753454236975 状态码: 200 耗时: 0.010s
2025-07-25 22:37:17,516 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/decrypt/methods?_t=1753454237496
2025-07-25 22:37:17,519 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/decrypt/history?page=1&limit=20&_t=1753454237496
2025-07-25 22:37:17,519 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/decrypt/methods?_t=1753454237496 状态码: 200 耗时: 0.003s
2025-07-25 22:37:17,520 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/decrypt/history?page=1&limit=20&_t=1753454237496 状态码: 404 耗时: 0.003s
2025-07-25 22:40:04,072 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/decrypt/methods?_t=1753454404050
2025-07-25 22:40:04,076 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/decrypt/history?page=1&limit=20&_t=1753454404050
2025-07-25 22:40:04,076 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/decrypt/methods?_t=1753454404050 状态码: 200 耗时: 0.004s
2025-07-25 22:40:04,079 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/decrypt/history?page=1&limit=20&_t=1753454404050 状态码: 404 耗时: 0.003s
2025-07-25 22:40:04,273 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 22:40:09,587 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 22:40:09,594 [INFO] root:48 - 数据库表创建完成
2025-07-25 22:40:09,663 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 22:40:09,663 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 22:43:19,029 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753454599011
2025-07-25 22:43:19,031 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753454599011 状态码: 307 耗时: 0.002s
2025-07-25 22:43:19,032 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753454599011
2025-07-25 22:43:19,054 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753454599011 状态码: 200 耗时: 0.022s
2025-07-25 22:43:19,365 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753454599011
2025-07-25 22:43:19,379 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753454599011 状态码: 200 耗时: 0.015s
2025-07-25 22:43:22,159 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753454602155
2025-07-25 22:43:22,162 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753454602155 状态码: 200 耗时: 0.004s
2025-07-25 22:43:31,053 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753454611044
2025-07-25 22:43:31,054 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753454611044 状态码: 307 耗时: 0.001s
2025-07-25 22:43:31,056 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753454611044
2025-07-25 22:43:31,061 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753454611044 状态码: 200 耗时: 0.005s
2025-07-25 22:43:31,083 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753454611044
2025-07-25 22:43:31,091 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753454611044 状态码: 200 耗时: 0.008s
2025-07-25 22:43:33,141 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-25 22:43:33,143 [ERROR] app.api.v1.interfaces:474 - 解析接口源异常: name 'interface_service' is not defined
2025-07-25 22:43:33,143 [ERROR] app.core.database:75 - 数据库会话错误: 500: 解析接口源失败
2025-07-25 22:43:33,144 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 500 耗时: 0.003s
2025-07-25 22:43:39,301 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753454619296
2025-07-25 22:43:39,306 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753454619296 状态码: 200 耗时: 0.005s
2025-07-25 22:43:41,176 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753454620868
2025-07-25 22:43:41,176 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753454620868 状态码: 307 耗时: 0.000s
2025-07-25 22:43:41,177 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753454620868
2025-07-25 22:43:41,182 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753454620868 状态码: 200 耗时: 0.005s
2025-07-25 22:43:41,490 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753454620868
2025-07-25 22:43:41,493 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753454620868 状态码: 200 耗时: 0.003s
2025-07-25 22:43:46,389 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753454626382
2025-07-25 22:43:46,393 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753454626382 状态码: 200 耗时: 0.004s
2025-07-25 22:43:53,912 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753454633589
2025-07-25 22:43:53,914 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753454633589
2025-07-25 22:43:53,915 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753454633589 状态码: 307 耗时: 0.003s
2025-07-25 22:43:53,922 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753454633589
2025-07-25 22:43:53,932 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753454633589 状态码: 200 耗时: 0.018s
2025-07-25 22:43:53,935 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753454633589 状态码: 200 耗时: 0.013s
2025-07-25 22:45:20,126 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-25 22:45:20,127 [ERROR] app.api.v1.interfaces:474 - 解析接口源异常: name 'interface_service' is not defined
2025-07-25 22:45:20,128 [ERROR] app.core.database:75 - 数据库会话错误: 500: 解析接口源失败
2025-07-25 22:45:20,128 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 500 耗时: 0.002s
2025-07-25 22:46:49,520 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 22:46:49,526 [INFO] root:48 - 数据库表创建完成
2025-07-25 22:46:49,579 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 22:46:49,580 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 22:48:46,007 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/health
2025-07-25 22:48:46,009 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/health 状态码: 200 耗时: 0.002s
2025-07-25 22:49:02,888 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/decrypt/methods
2025-07-25 22:49:02,889 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/decrypt/methods 状态码: 200 耗时: 0.002s
2025-07-25 22:49:45,230 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/decrypt/url
2025-07-25 22:49:45,233 [INFO] app.api.v1.decrypt:55 - 开始解密URL: http://www.xn--sss604efuw.com/tv
2025-07-25 22:49:45,355 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/decrypt/url 状态码: 200 耗时: 0.125s
2025-07-25 22:56:05,170 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 22:56:22,682 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 22:56:22,695 [INFO] root:48 - 数据库表创建完成
2025-07-25 22:56:22,775 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 22:56:22,775 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 22:56:26,622 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/decrypt/url
2025-07-25 22:56:26,623 [INFO] app.api.v1.decrypt:55 - 开始解密URL: http://www.xn--sss604efuw.com/tv
2025-07-25 22:56:26,785 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/decrypt/url 状态码: 200 耗时: 0.163s
2025-07-25 23:01:16,960 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 23:01:21,089 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 23:01:21,095 [INFO] root:48 - 数据库表创建完成
2025-07-25 23:01:21,144 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 23:01:21,144 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 23:02:38,586 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 23:02:43,924 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 23:02:43,930 [INFO] root:48 - 数据库表创建完成
2025-07-25 23:02:43,983 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 23:02:43,983 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 23:05:41,419 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753455941397
2025-07-25 23:05:41,442 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753455941397 状态码: 200 耗时: 0.022s
2025-07-25 23:05:42,140 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/decrypt/methods?_t=1753455942117
2025-07-25 23:05:42,140 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/decrypt/history?page=1&limit=20&_t=1753455942117
2025-07-25 23:05:42,141 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/decrypt/methods?_t=1753455942117 状态码: 200 耗时: 0.001s
2025-07-25 23:05:42,143 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/decrypt/history?page=1&limit=20&_t=1753455942117 状态码: 404 耗时: 0.003s
2025-07-25 23:06:52,263 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753456012245
2025-07-25 23:06:52,281 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753456012245 状态码: 200 耗时: 0.018s
2025-07-25 23:06:53,531 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/decrypt/methods?_t=1753456013506
2025-07-25 23:06:53,534 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/decrypt/history?page=1&limit=20&_t=1753456013506
2025-07-25 23:06:53,535 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/decrypt/methods?_t=1753456013506 状态码: 200 耗时: 0.004s
2025-07-25 23:06:53,540 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/decrypt/history?page=1&limit=20&_t=1753456013506 状态码: 404 耗时: 0.006s
2025-07-25 23:07:32,899 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/decrypt/url
2025-07-25 23:07:32,902 [INFO] app.api.v1.decrypt:55 - 开始解密URL: http://www.xn--sss604efuw.com/tv
2025-07-25 23:07:33,028 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/decrypt/url 状态码: 200 耗时: 0.129s
2025-07-25 23:12:39,997 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 23:12:44,321 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 23:12:44,326 [INFO] root:48 - 数据库表创建完成
2025-07-25 23:12:44,377 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 23:12:44,378 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 23:17:48,183 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 23:17:48,189 [INFO] root:48 - 数据库表创建完成
2025-07-25 23:17:48,244 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 23:17:48,245 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 23:18:47,338 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 23:18:47,343 [INFO] root:48 - 数据库表创建完成
2025-07-25 23:18:47,394 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 23:18:47,394 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 23:21:20,557 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753456880540
2025-07-25 23:21:20,575 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753456880540 状态码: 200 耗时: 0.017s
2025-07-25 23:21:21,354 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/decrypt/methods?_t=1753456881342
2025-07-25 23:21:21,356 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/decrypt/history?page=1&limit=20&_t=1753456881342
2025-07-25 23:21:21,356 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/decrypt/methods?_t=1753456881342 状态码: 200 耗时: 0.002s
2025-07-25 23:21:21,357 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/decrypt/history?page=1&limit=20&_t=1753456881342 状态码: 404 耗时: 0.002s
2025-07-25 23:22:46,554 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753456966542
2025-07-25 23:22:46,562 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753456966542 状态码: 200 耗时: 0.008s
2025-07-25 23:22:47,746 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/decrypt/methods?_t=1753456967735
2025-07-25 23:22:47,747 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/decrypt/history?page=1&limit=20&_t=1753456967735
2025-07-25 23:22:47,749 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/decrypt/methods?_t=1753456967735 状态码: 200 耗时: 0.002s
2025-07-25 23:22:47,751 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/decrypt/history?page=1&limit=20&_t=1753456967735 状态码: 404 耗时: 0.004s
2025-07-25 23:23:46,541 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753457026534
2025-07-25 23:23:46,547 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753457026534 状态码: 200 耗时: 0.006s
2025-07-25 23:23:47,084 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats?_t=1753457026996
2025-07-25 23:23:47,087 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats?_t=1753457026996 状态码: 200 耗时: 0.003s
2025-07-25 23:23:58,886 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753457038875
2025-07-25 23:23:58,894 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753457038875 状态码: 200 耗时: 0.008s
2025-07-25 23:23:59,243 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/decrypt/methods?_t=1753457039222
2025-07-25 23:23:59,244 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/decrypt/history?page=1&limit=20&_t=1753457039222
2025-07-25 23:23:59,244 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/decrypt/methods?_t=1753457039222 状态码: 200 耗时: 0.001s
2025-07-25 23:23:59,245 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/decrypt/history?page=1&limit=20&_t=1753457039222 状态码: 404 耗时: 0.001s
2025-07-25 23:25:15,113 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/decrypt/url
2025-07-25 23:25:15,116 [INFO] app.api.v1.decrypt:55 - 开始解密URL: http://www.xn--sss604efuw.com/tv
2025-07-25 23:25:15,265 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/decrypt/url 状态码: 200 耗时: 0.152s
2025-07-25 23:28:09,963 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/decrypt/url
2025-07-25 23:28:09,964 [INFO] app.api.v1.decrypt:55 - 开始解密URL: http://www.xn--sss604efuw.com/tv
2025-07-25 23:28:10,123 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/decrypt/url 状态码: 200 耗时: 0.160s
2025-07-25 23:28:17,148 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753457297142
2025-07-25 23:28:17,149 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753457297142
2025-07-25 23:28:17,151 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753457297142 状态码: 307 耗时: 0.002s
2025-07-25 23:28:17,156 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753457297142 状态码: 200 耗时: 0.007s
2025-07-25 23:28:17,478 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753457297142
2025-07-25 23:28:17,498 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753457297142 状态码: 200 耗时: 0.020s
2025-07-25 23:28:19,384 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-25 23:28:19,385 [ERROR] app.api.v1.interfaces:474 - 解析接口源异常: name 'interface_service' is not defined
2025-07-25 23:28:19,386 [ERROR] app.core.database:75 - 数据库会话错误: 500: 解析接口源失败
2025-07-25 23:28:19,386 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 500 耗时: 0.002s
2025-07-25 23:28:20,412 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-25 23:28:20,414 [ERROR] app.api.v1.interfaces:474 - 解析接口源异常: name 'interface_service' is not defined
2025-07-25 23:28:20,414 [ERROR] app.core.database:75 - 数据库会话错误: 500: 解析接口源失败
2025-07-25 23:28:20,415 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 500 耗时: 0.003s
2025-07-25 23:28:20,628 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-25 23:28:20,629 [ERROR] app.api.v1.interfaces:474 - 解析接口源异常: name 'interface_service' is not defined
2025-07-25 23:28:20,630 [ERROR] app.core.database:75 - 数据库会话错误: 500: 解析接口源失败
2025-07-25 23:28:20,630 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 500 耗时: 0.002s
2025-07-25 23:28:20,793 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-25 23:28:20,794 [ERROR] app.api.v1.interfaces:474 - 解析接口源异常: name 'interface_service' is not defined
2025-07-25 23:28:20,795 [ERROR] app.core.database:75 - 数据库会话错误: 500: 解析接口源失败
2025-07-25 23:28:20,795 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 500 耗时: 0.002s
2025-07-25 23:28:21,301 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-25 23:28:21,302 [ERROR] app.api.v1.interfaces:474 - 解析接口源异常: name 'interface_service' is not defined
2025-07-25 23:28:21,303 [ERROR] app.core.database:75 - 数据库会话错误: 500: 解析接口源失败
2025-07-25 23:28:21,303 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 500 耗时: 0.002s
2025-07-25 23:28:21,503 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-25 23:28:21,505 [ERROR] app.api.v1.interfaces:474 - 解析接口源异常: name 'interface_service' is not defined
2025-07-25 23:28:21,505 [ERROR] app.core.database:75 - 数据库会话错误: 500: 解析接口源失败
2025-07-25 23:28:21,506 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 500 耗时: 0.002s
2025-07-25 23:28:21,683 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-25 23:28:21,685 [ERROR] app.api.v1.interfaces:474 - 解析接口源异常: name 'interface_service' is not defined
2025-07-25 23:28:21,685 [ERROR] app.core.database:75 - 数据库会话错误: 500: 解析接口源失败
2025-07-25 23:28:21,686 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 500 耗时: 0.003s
2025-07-25 23:28:25,155 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753457305151
2025-07-25 23:28:25,159 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753457305151 状态码: 200 耗时: 0.004s
2025-07-25 23:28:31,611 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-25 23:28:31,612 [ERROR] app.api.v1.interfaces:474 - 解析接口源异常: name 'interface_service' is not defined
2025-07-25 23:28:31,612 [ERROR] app.core.database:75 - 数据库会话错误: 500: 解析接口源失败
2025-07-25 23:28:31,613 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 500 耗时: 0.002s
2025-07-25 23:28:32,391 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/subscribe
2025-07-25 23:28:32,396 [ERROR] app.api.v1.interfaces:502 - 订阅接口源异常: name 'interface_service' is not defined
2025-07-25 23:28:32,397 [ERROR] app.core.database:75 - 数据库会话错误: 500: 订阅接口源失败
2025-07-25 23:28:32,397 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/subscribe 状态码: 500 耗时: 0.006s
2025-07-25 23:28:34,390 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753457314383
2025-07-25 23:28:34,390 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753457314383
2025-07-25 23:28:34,390 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753457314383 状态码: 307 耗时: 0.001s
2025-07-25 23:28:34,394 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753457314383 状态码: 200 耗时: 0.004s
2025-07-25 23:28:34,411 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753457314383
2025-07-25 23:28:34,415 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753457314383 状态码: 200 耗时: 0.004s
2025-07-25 23:28:37,772 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/configs?page=1&limit=20&name=&type=&status=&_t=1753457317767
2025-07-25 23:28:37,773 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/configs?page=1&limit=20&name=&type=&status=&_t=1753457317767 状态码: 404 耗时: 0.001s
2025-07-25 23:28:40,442 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions?page=1&limit=20&name=&status=&_t=1753457320437
2025-07-25 23:28:40,442 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions?page=1&limit=20&name=&status=&_t=1753457320437 状态码: 307 耗时: 0.000s
2025-07-25 23:28:40,770 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/subscriptions/?page=1&limit=20&name=&status=&_t=1753457320437
2025-07-25 23:28:40,771 [ERROR] app.api.v1.subscriptions:74 - 获取订阅列表异常: 401: 缺少认证信息
2025-07-25 23:28:40,771 [ERROR] app.core.database:75 - 数据库会话错误: 500: 获取订阅列表失败
2025-07-25 23:28:40,772 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/subscriptions/?page=1&limit=20&name=&status=&_t=1753457320437 状态码: 500 耗时: 0.003s
2025-07-25 23:28:41,438 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/configs?page=1&limit=20&name=&type=&status=&_t=1753457321431
2025-07-25 23:28:41,440 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/configs?page=1&limit=20&name=&type=&status=&_t=1753457321431 状态码: 404 耗时: 0.002s
2025-07-25 23:28:55,584 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753457335578
2025-07-25 23:28:55,585 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753457335578
2025-07-25 23:28:55,585 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753457335578 状态码: 307 耗时: 0.001s
2025-07-25 23:28:55,589 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753457335578 状态码: 200 耗时: 0.004s
2025-07-25 23:28:55,604 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753457335578
2025-07-25 23:28:55,609 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753457335578 状态码: 200 耗时: 0.004s
2025-07-25 23:29:00,472 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753457340467
2025-07-25 23:29:00,473 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753457340467 状态码: 307 耗时: 0.001s
2025-07-25 23:29:00,494 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753457340467
2025-07-25 23:29:00,501 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753457340467 状态码: 200 耗时: 0.007s
2025-07-25 23:29:00,785 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753457340467
2025-07-25 23:29:00,789 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753457340467 状态码: 200 耗时: 0.006s
2025-07-25 23:29:02,093 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/subscribe
2025-07-25 23:29:02,096 [ERROR] app.api.v1.interfaces:502 - 订阅接口源异常: name 'interface_service' is not defined
2025-07-25 23:29:02,096 [ERROR] app.core.database:75 - 数据库会话错误: 500: 订阅接口源失败
2025-07-25 23:29:02,096 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/subscribe 状态码: 500 耗时: 0.003s
2025-07-25 23:29:03,242 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/1?_t=1753457343239
2025-07-25 23:29:03,246 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/1?_t=1753457343239 状态码: 200 耗时: 0.004s
2025-07-25 23:29:09,407 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753457349087
2025-07-25 23:29:09,408 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753457349087
2025-07-25 23:29:09,408 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753457349087 状态码: 307 耗时: 0.001s
2025-07-25 23:29:09,417 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753457349087 状态码: 200 耗时: 0.009s
2025-07-25 23:29:09,736 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753457349087
2025-07-25 23:29:09,741 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753457349087 状态码: 200 耗时: 0.005s
2025-07-25 23:29:18,131 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753457358113
2025-07-25 23:29:18,142 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753457358113 状态码: 200 耗时: 0.011s
2025-07-25 23:29:18,620 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753457358593
2025-07-25 23:29:18,624 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753457358593 状态码: 307 耗时: 0.004s
2025-07-25 23:29:18,625 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753457358593
2025-07-25 23:29:18,635 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753457358593 状态码: 200 耗时: 0.009s
2025-07-25 23:29:18,671 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753457358593
2025-07-25 23:29:18,681 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753457358593 状态码: 200 耗时: 0.010s
2025-07-25 23:29:18,788 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 23:29:26,135 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 23:29:26,143 [INFO] root:48 - 数据库表创建完成
2025-07-25 23:29:26,209 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 23:29:26,210 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 23:29:26,222 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753457363296
2025-07-25 23:29:26,243 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753457363296 状态码: 200 耗时: 0.020s
2025-07-25 23:29:26,508 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753457366496
2025-07-25 23:29:26,511 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753457366496 状态码: 307 耗时: 0.003s
2025-07-25 23:29:26,512 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753457366496
2025-07-25 23:29:26,520 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753457366496 状态码: 200 耗时: 0.008s
2025-07-25 23:29:26,847 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753457366496
2025-07-25 23:29:26,868 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753457366496 状态码: 200 耗时: 0.021s
2025-07-25 23:29:28,647 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/system/stats?_t=1753457368565
2025-07-25 23:29:28,654 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/system/stats?_t=1753457368565 状态码: 200 耗时: 0.007s
2025-07-25 23:29:30,042 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753457370033
2025-07-25 23:29:30,043 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753457370033
2025-07-25 23:29:30,044 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753457370033 状态码: 307 耗时: 0.002s
2025-07-25 23:29:30,049 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753457370033 状态码: 200 耗时: 0.007s
2025-07-25 23:29:30,069 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753457370033
2025-07-25 23:29:30,075 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753457370033 状态码: 200 耗时: 0.006s
2025-07-25 23:29:33,626 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753457373622
2025-07-25 23:29:33,628 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753457373622 状态码: 307 耗时: 0.001s
2025-07-25 23:29:33,629 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753457373622
2025-07-25 23:29:33,634 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753457373622 状态码: 200 耗时: 0.005s
2025-07-25 23:29:33,654 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753457373622
2025-07-25 23:29:33,656 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753457373622 状态码: 200 耗时: 0.002s
2025-07-25 23:29:36,551 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753457376239
2025-07-25 23:29:36,551 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753457376239
2025-07-25 23:29:36,552 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753457376239 状态码: 307 耗时: 0.001s
2025-07-25 23:29:36,557 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753457376239
2025-07-25 23:29:36,558 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753457376239 状态码: 200 耗时: 0.007s
2025-07-25 23:29:36,562 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753457376239 状态码: 200 耗时: 0.005s
2025-07-25 23:29:38,034 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-25 23:29:38,035 [ERROR] app.api.v1.interfaces:474 - 解析接口源异常: name 'interface_service' is not defined
2025-07-25 23:29:38,035 [ERROR] app.core.database:75 - 数据库会话错误: 500: 解析接口源失败
2025-07-25 23:29:38,036 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 500 耗时: 0.002s
2025-07-25 23:29:43,693 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753457383687
2025-07-25 23:29:43,694 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753457383687
2025-07-25 23:29:43,694 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753457383687 状态码: 307 耗时: 0.001s
2025-07-25 23:29:43,702 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753457383687 状态码: 200 耗时: 0.008s
2025-07-25 23:29:43,722 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753457383687
2025-07-25 23:29:43,725 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753457383687 状态码: 200 耗时: 0.003s
2025-07-25 23:31:02,374 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/auth/me?_t=1753457462369
2025-07-25 23:31:02,380 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/auth/me?_t=1753457462369 状态码: 200 耗时: 0.005s
2025-07-25 23:31:02,629 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753457462621
2025-07-25 23:31:02,630 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753457462621
2025-07-25 23:31:02,630 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces?skip=0&limit=20&name=&category=&status=&_t=1753457462621 状态码: 307 耗时: 0.002s
2025-07-25 23:31:02,637 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/categories/list?_t=1753457462621 状态码: 200 耗时: 0.007s
2025-07-25 23:31:02,978 [INFO] root:149 - 请求开始: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753457462621
2025-07-25 23:31:02,982 [INFO] root:158 - 请求完成: GET http://localhost:8001/api/v1/interfaces/?skip=0&limit=20&name=&category=&status=&_t=1753457462621 状态码: 200 耗时: 0.005s
2025-07-25 23:31:36,976 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-25 23:31:36,977 [ERROR] app.api.v1.interfaces:474 - 解析接口源异常: name 'interface_service' is not defined
2025-07-25 23:31:36,979 [ERROR] app.core.database:75 - 数据库会话错误: 500: 解析接口源失败
2025-07-25 23:31:36,979 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 500 耗时: 0.003s
2025-07-25 23:34:21,072 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 23:34:24,351 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 23:34:24,358 [INFO] root:48 - 数据库表创建完成
2025-07-25 23:34:24,403 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 23:34:24,403 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 23:35:39,630 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 23:35:42,867 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 23:35:42,873 [INFO] root:48 - 数据库表创建完成
2025-07-25 23:35:42,921 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 23:35:42,921 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 23:38:25,776 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-25 23:38:25,778 [ERROR] app.api.v1.interfaces:474 - 解析接口源异常: name 'interface_service' is not defined
2025-07-25 23:38:25,780 [ERROR] app.core.database:75 - 数据库会话错误: 500: 解析接口源失败
2025-07-25 23:38:25,781 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 500 耗时: 0.004s
2025-07-25 23:38:25,920 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 23:38:30,093 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 23:38:30,098 [INFO] root:48 - 数据库表创建完成
2025-07-25 23:38:30,145 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 23:38:30,146 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 23:38:53,023 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 23:38:53,030 [INFO] root:48 - 数据库表创建完成
2025-07-25 23:38:53,092 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 23:38:53,092 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 23:39:17,916 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-25 23:39:17,922 [ERROR] app.api.v1.interfaces:474 - 解析接口源异常: name 'interface_service' is not defined
2025-07-25 23:39:17,923 [ERROR] app.core.database:75 - 数据库会话错误: 500: 解析接口源失败
2025-07-25 23:39:17,924 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 500 耗时: 0.007s
2025-07-25 23:43:43,195 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-25 23:43:43,197 [ERROR] app.api.v1.interfaces:474 - 解析接口源异常: name 'interface_service' is not defined
2025-07-25 23:43:43,197 [ERROR] app.core.database:75 - 数据库会话错误: 500: 解析接口源失败
2025-07-25 23:43:43,198 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 500 耗时: 0.004s
2025-07-25 23:43:43,410 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 23:43:46,796 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 23:43:46,802 [INFO] root:48 - 数据库表创建完成
2025-07-25 23:43:46,847 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 23:43:46,847 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 23:44:21,200 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-25 23:44:21,204 [ERROR] app.api.v1.interfaces:474 - 解析接口源异常: name 'interface_service' is not defined
2025-07-25 23:44:21,204 [ERROR] app.core.database:75 - 数据库会话错误: 500: 解析接口源失败
2025-07-25 23:44:21,205 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 500 耗时: 0.006s
2025-07-25 23:44:21,408 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 23:44:24,910 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 23:44:24,916 [INFO] root:48 - 数据库表创建完成
2025-07-25 23:44:24,970 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 23:44:24,970 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 23:45:01,274 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 23:45:01,280 [INFO] root:48 - 数据库表创建完成
2025-07-25 23:45:01,339 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 23:45:01,339 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 23:47:31,392 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 23:47:31,398 [INFO] root:48 - 数据库表创建完成
2025-07-25 23:47:31,451 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 23:47:31,453 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 23:48:07,366 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-25 23:48:07,370 [ERROR] app.api.v1.interfaces:474 - 解析接口源异常: name 'interface_service' is not defined
2025-07-25 23:48:07,370 [ERROR] app.core.database:75 - 数据库会话错误: 500: 解析接口源失败
2025-07-25 23:48:07,371 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 500 耗时: 0.004s
2025-07-25 23:49:18,670 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 23:49:18,676 [INFO] root:48 - 数据库表创建完成
2025-07-25 23:49:18,726 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 23:49:18,726 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 23:50:28,068 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 23:50:28,074 [INFO] root:48 - 数据库表创建完成
2025-07-25 23:50:28,118 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 23:50:28,118 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 23:51:13,698 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-25 23:51:13,701 [ERROR] app.api.v1.interfaces:474 - 解析接口源异常: name 'interface_service' is not defined
2025-07-25 23:51:13,701 [ERROR] app.core.database:75 - 数据库会话错误: 500: 解析接口源失败
2025-07-25 23:51:13,703 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 500 耗时: 0.003s
2025-07-25 23:52:16,750 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 23:52:16,755 [INFO] root:48 - 数据库表创建完成
2025-07-25 23:52:16,802 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 23:52:16,802 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 23:53:26,874 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 23:53:26,882 [INFO] root:48 - 数据库表创建完成
2025-07-25 23:53:26,928 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 23:53:26,929 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 23:53:26,931 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 23:53:47,567 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 23:53:47,573 [INFO] root:48 - 数据库表创建完成
2025-07-25 23:53:47,621 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 23:53:47,621 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 23:53:47,622 [INFO] root:60 - TVBox Manager Pro 关闭中...
2025-07-25 23:54:45,262 [INFO] root:44 - TVBox Manager Pro 启动中...
2025-07-25 23:54:45,269 [INFO] root:48 - 数据库表创建完成
2025-07-25 23:54:45,329 [INFO] app.services.user_service:36 - 默认管理员用户已存在
2025-07-25 23:54:45,329 [INFO] root:55 - TVBox Manager Pro 启动完成
2025-07-25 23:55:25,341 [INFO] root:149 - 请求开始: POST http://localhost:8001/api/v1/interfaces/1/parse
2025-07-25 23:55:25,375 [ERROR] app.api.v1.interfaces:474 - 解析接口源异常: name 'interface_service' is not defined
2025-07-25 23:55:25,377 [ERROR] app.core.database:75 - 数据库会话错误: 500: 解析接口源失败
2025-07-25 23:55:25,395 [INFO] root:158 - 请求完成: POST http://localhost:8001/api/v1/interfaces/1/parse 状态码: 500 耗时: 0.054s
