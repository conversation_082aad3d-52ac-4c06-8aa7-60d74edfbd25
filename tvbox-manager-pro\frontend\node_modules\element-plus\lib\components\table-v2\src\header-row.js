'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var common = require('./common.js');
var runtime = require('../../../utils/vue/props/runtime.js');

const tableV2HeaderRowProps = runtime.buildProps({
  class: String,
  columns: common.columns,
  columnsStyles: {
    type: runtime.definePropType(Object),
    required: true
  },
  headerIndex: Number,
  style: { type: runtime.definePropType(Object) }
});

exports.tableV2HeaderRowProps = tableV2HeaderRowProps;
//# sourceMappingURL=header-row.js.map
