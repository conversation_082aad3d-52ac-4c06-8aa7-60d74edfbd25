#!/usr/bin/env python3
"""
检查数据库表结构
"""
import sqlite3
import os

def check_tables():
    """检查数据库表"""
    db_path = "data/tvbox.db"
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print("数据库中的表:")
        for table in tables:
            print(f"  - {table[0]}")
            
        # 检查接口相关的表
        for table in tables:
            table_name = table[0]
            if 'interface' in table_name.lower():
                print(f"\n表 {table_name} 的结构:")
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                for col in columns:
                    print(f"  {col[1]} ({col[2]})")
                    
    except Exception as e:
        print(f"检查失败: {str(e)}")
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    check_tables()
