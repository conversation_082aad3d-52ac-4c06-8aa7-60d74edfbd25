#!/usr/bin/env python3
"""
添加 localized_config 字段到 interface_sources 表
"""
import sqlite3
import sys
import os
from pathlib import Path

def add_localized_config_field():
    """添加 localized_config 字段"""
    db_path = "data/tvbox.db"
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查字段是否已存在
        cursor.execute("PRAGMA table_info(interface_sources)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'localized_config' in columns:
            print("localized_config 字段已存在")
            return True
        
        # 添加字段
        print("添加 localized_config 字段...")
        cursor.execute("""
            ALTER TABLE interface_sources 
            ADD COLUMN localized_config TEXT
        """)
        
        conn.commit()
        print("✅ 成功添加 localized_config 字段")
        
        # 验证字段添加成功
        cursor.execute("PRAGMA table_info(interface_sources)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'localized_config' in columns:
            print("✅ 字段验证成功")
            return True
        else:
            print("❌ 字段验证失败")
            return False
            
    except Exception as e:
        print(f"❌ 添加字段失败: {str(e)}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    success = add_localized_config_field()
    sys.exit(0 if success else 1)
