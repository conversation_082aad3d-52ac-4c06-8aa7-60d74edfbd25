<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="16dp">

    <com.fongmi.android.tv.ui.custom.CustomRecyclerView
        android:id="@+id/recycler"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        app:maxHeight="264dp"
        tools:itemCount="5"
        tools:listitem="@layout/adapter_site" />

    <LinearLayout
        android:id="@+id/action"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginStart="16dp"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/search"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/selector_text"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:nextFocusUp="@id/search"
            android:nextFocusDown="@id/change"
            android:padding="8dp"
            android:src="@drawable/ic_site_search"
            app:tint="@color/text" />

        <ImageView
            android:id="@+id/change"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginTop="16dp"
            android:background="@drawable/selector_text"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:nextFocusUp="@id/search"
            android:nextFocusDown="@id/select"
            android:padding="8dp"
            android:src="@drawable/ic_site_change"
            app:tint="@color/text" />

        <ImageView
            android:id="@+id/select"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginTop="16dp"
            android:background="@drawable/selector_text"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:nextFocusUp="@id/change"
            android:nextFocusDown="@id/cancel"
            android:padding="8dp"
            android:src="@drawable/ic_site_select" />

        <ImageView
            android:id="@+id/cancel"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginTop="16dp"
            android:background="@drawable/selector_text"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:nextFocusUp="@id/select"
            android:nextFocusDown="@id/mode"
            android:padding="8dp"
            android:src="@drawable/ic_site_cancel" />

        <ImageView
            android:id="@+id/mode"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginTop="16dp"
            android:background="@drawable/selector_text"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:nextFocusUp="@id/cancel"
            android:nextFocusDown="@id/mode"
            android:padding="8dp"
            tools:src="@drawable/ic_site_list" />

    </LinearLayout>
</LinearLayout>